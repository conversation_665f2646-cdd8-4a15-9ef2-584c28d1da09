# Chat Functionality Test Plan

## Current Status Analysis

### Dependencies Status
- ✅ sockjs-client@1.6.1 - INSTALLED
- ❌ stompjs@^2.3.3 - MISSING
- ❌ @types/sockjs-client@^1.5.1 - MISSING  
- ❌ @types/stompjs@^2.3.4 - MISSING
- ❌ file-saver@^2.0.5 - MISSING
- ❌ xlsx@^0.18.5 - MISSING

### Frontend Components Found
1. **WebSocket Services**:
   - `Frontend/src/app/all-modules/chat/services/websocket.service.ts`
   - `Frontend/src/app/all-modules/chat/services/simple-websocket.service.ts`
   - `Frontend/src/app/sharing/websocket.service.ts`

2. **Chat Components**:
   - `Frontend/src/app/all-modules/chat/chat.component.ts`
   - `Frontend/src/app/all-modules/chat/chat-main.component.ts`
   - `Frontend/src/app/all-modules/chat/components/chat-list/chat-list.component.ts`
   - `Frontend/src/app/all-modules/chat/components/chat-room/chat-room.component.ts`
   - `Frontend/src/app/all-modules/chat/components/notification-list/notification-list.component.ts`

3. **Chat Services**:
   - `Frontend/src/app/all-modules/chat/services/chat.service.ts`

4. **Routing**:
   - `Frontend/src/app/all-modules/chat/chat-routing.module.ts`

### Issues Identified

#### 1. Missing Dependencies
- npm install is failing with "must provide string spec" error
- Multiple critical dependencies are missing

#### 2. Multiple WebSocket Service Implementations
- Three different WebSocket services exist
- Potential conflicts between implementations
- Different connection URLs and protocols

#### 3. Backend Integration
- Frontend expects backend endpoints at `/api/chat/*`
- WebSocket endpoint expected at `/ws`
- Backend implementation not verified

## Test Plan

### Phase 1: Fix Dependencies
1. ✅ Manually add dependencies to package.json
2. ❌ Install missing packages (npm issue)
3. 🔄 Alternative installation methods

### Phase 2: Component Testing
1. Test WebSocket connection
2. Test message sending/receiving
3. Test user status updates
4. Test notifications
5. Test chat rooms
6. Test file uploads

### Phase 3: Integration Testing
1. Frontend-Backend communication
2. Real-time messaging
3. User authentication
4. Error handling

### Phase 4: UI/UX Testing
1. Chat interface responsiveness
2. Message display
3. User list functionality
4. Notification system

## Immediate Actions Required

1. **Fix npm installation issue**
2. **Install missing dependencies**
3. **Consolidate WebSocket services**
4. **Verify backend endpoints**
5. **Test basic chat functionality**

## Expected Endpoints (Frontend Assumptions)

### REST API Endpoints
- `GET /api/chat/messages/{roomId}` - Get chat history
- `POST /api/chat/send` - Send message
- `GET /api/chat/recent` - Get recent chats
- `GET /api/users/{userId}` - Get user info
- `GET /api/chat/users/online` - Get online users
- `PUT /api/chat/users/status` - Update user status
- `GET /api/notifications` - Get notifications
- `PUT /api/notifications/{id}/read` - Mark notification as read
- `PUT /api/notifications/read-all` - Mark all notifications as read
- `DELETE /api/notifications/{id}` - Delete notification
- `POST /api/chat/rooms` - Create chat room
- `GET /api/chat/rooms` - Get chat rooms
- `POST /api/chat/upload` - Upload file

### WebSocket Endpoints
- `/ws` - WebSocket connection endpoint
- `/app/chat.sendMessage` - Send message via WebSocket
- `/app/user.updateStatus` - Update user status via WebSocket
- `/topic/chat/{roomId}` - Subscribe to chat room
- `/queue/notifications/{userId}` - User-specific notifications
- `/topic/user-status` - User status updates
- `/topic/announcements` - System announcements

## Next Steps

1. Resolve npm installation issues
2. Install missing dependencies using alternative methods
3. Create comprehensive test suite
4. Fix any compilation errors
5. Test chat functionality end-to-end
