(window.webpackJsonp=window.webpackJsonp||[]).push([[28],{Zqwr:function(e,t,a){"use strict";a.r(t),a.d(t,"UsersModule",function(){return S});var i=a("ofXK"),c=a("tyNb"),d=a("fXoL");let s=(()=>{let e=class{constructor(e){this.ngZone=e,window.onresize=e=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){}onResize(e){this.innerHeight=e.target.innerHeight+"px"}};return e.\u0275fac=function(t){return new(t||e)(d.Ub(d.G))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-users"]],decls:1,vars:0,template:function(e,t){1&e&&d.Vb(0,"router-outlet")},directives:[c.g],styles:[""]}),e})();var r=a("IhMt"),o=a("3Pt+"),n=a("XNiG"),b=a("njyG"),l=a("5eHb");function u(e,t){if(1&e){const e=d.bc();d.ac(0,"tr"),d.ac(1,"td"),d.ac(2,"h2",77),d.ac(3,"a",78),d.Vb(4,"img",79),d.Zb(),d.ac(5,"a",80),d.Lc(6),d.ac(7,"span"),d.Lc(8),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(9,"td"),d.Lc(10),d.Zb(),d.ac(11,"td"),d.Lc(12),d.Zb(),d.ac(13,"td"),d.Lc(14,"1 Jan 2013"),d.Zb(),d.ac(15,"td"),d.ac(16,"span",81),d.Lc(17),d.Zb(),d.Zb(),d.ac(18,"td",32),d.ac(19,"div",82),d.ac(20,"a",83),d.ac(21,"i",84),d.Lc(22,"more_vert"),d.Zb(),d.Zb(),d.ac(23,"div",85),d.ac(24,"a",86),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().edit(a.id)}),d.Vb(25,"i",87),d.Lc(26," Edit"),d.Zb(),d.ac(27,"a",88),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().tempId=a.id}),d.Vb(28,"i",89),d.Lc(29," Delete"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()}if(2&e){const e=t.$implicit,a=t.index;d.Ib(4),d.rc("src","assets/img/profiles/avatar-",a+1,".jpg",d.Fc),d.Ib(2),d.Nc("",e.name," "),d.Ib(2),d.Mc(e.designation),d.Ib(2),d.Mc(e.email),d.Ib(2),d.Mc(e.company),d.Ib(5),d.Mc(e.role)}}function Z(e,t){1&e&&(d.ac(0,"tr"),d.ac(1,"td",90),d.ac(2,"h5",91),d.Lc(3,"No data found"),d.Zb(),d.Zb(),d.Zb())}function p(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *User name is required"),d.Zb())}function m(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,p,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.addUsers.get("addUserName").invalid&&e.addUsers.get("addUserName").touched)}}function h(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *Email is required"),d.Zb())}function g(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,h,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.addUsers.get("addEmail").invalid&&e.addUsers.get("addEmail").touched)}}function v(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *Role is required"),d.Zb())}function f(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,v,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.addUsers.get("addRole").invalid&&e.addUsers.get("addRole").touched)}}function L(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *Company is required"),d.Zb())}function U(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,L,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.addUsers.get("addCompany").invalid&&e.addUsers.get("addCompany").touched)}}function y(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *User name is required"),d.Zb())}function I(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,y,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.editUsers.get("editUsersName").invalid&&e.editUsers.get("editUsersName").touched)}}function V(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *Email is required"),d.Zb())}function w(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,V,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.editUsers.get("editEmail").invalid&&e.editUsers.get("editEmail").touched)}}function C(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *Role is required"),d.Zb())}function E(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,C,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.editUsers.get("editRole").invalid&&e.editUsers.get("editRole").touched)}}function N(e,t){1&e&&(d.ac(0,"small",46),d.Lc(1," *Company is required"),d.Zb())}function R(e,t){if(1&e&&(d.ac(0,"div"),d.Jc(1,N,2,0,"small",92),d.Zb()),2&e){const e=d.jc();d.Ib(1),d.pc("ngIf",e.editUsers.get("editCompany").invalid&&e.editUsers.get("editCompany").touched)}}const x=[{path:"",component:s,children:[{path:"user-main",component:(()=>{class e{constructor(e,t,a){this.allModuleService=e,this.formBuilder=t,this.toastr=a,this.dtOptions={},this.url="users",this.allUsers=[],this.rows=[],this.srch=[],this.dtTrigger=new n.a}ngOnInit(){$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),this.getUsers(),this.addUsers=this.formBuilder.group({addUserName:["",[o.w.required]],addEmail:["",[o.w.required]],addRole:["",[o.w.required]],addCompany:["",[o.w.required]]}),this.editUsers=this.formBuilder.group({editUsersName:["",[o.w.required]],editEmail:["",[o.w.required]],editRole:["",[o.w.required]],editCompany:["",[o.w.required]]}),this.dtOptions={pageLength:10,dom:"lrtip"}}ngAfterViewInit(){setTimeout(()=>{this.dtTrigger.next()},1e3)}rerender(){$("#datatable").DataTable().clear(),this.dtElement.dtInstance.then(e=>{e.destroy()}),this.allUsers=[],this.getUsers(),setTimeout(()=>{this.dtTrigger.next()},1e3)}getUsers(){this.allModuleService.get(this.url).subscribe(e=>{this.allUsers=e,this.rows=this.allUsers,this.srch=[...this.rows]})}addUsersSubmit(){this.addUsers.valid?(this.allModuleService.add({name:this.addUsers.value.addUserName,designation:"Web Designer",email:this.addUsers.value.addEmail,role:this.addUsers.value.addRole,company:this.addUsers.value.addCompany},this.url).subscribe(e=>{$("#datatable").DataTable().clear(),this.dtElement.dtInstance.then(e=>{e.destroy()}),this.dtTrigger.next()}),this.getUsers(),$("#add_user").modal("hide"),this.addUsers.reset(),this.toastr.success("Users is added","Success")):this.toastr.warning("Mandatory fields required","")}editUsersSubmit(){this.editUsers.valid?(this.allModuleService.update({name:this.editUsers.value.editUsersName,designation:"Android Developer",email:this.editUsers.value.editEmail,company:this.editUsers.value.editCompany,role:this.editUsers.value.editRole,id:this.editId},this.url).subscribe(e=>{$("#datatable").DataTable().clear(),this.dtElement.dtInstance.then(e=>{e.destroy()}),this.dtTrigger.next()}),this.getUsers(),$("#edit_user").modal("hide"),this.toastr.success("Users is edited","Success")):this.toastr.warning("Mandatory fields required","")}edit(e){this.editId=e;const t=this.allUsers.findIndex(t=>t.id===e);let a=this.allUsers[t];this.editUsers.setValue({editUsersName:a.name,editEmail:a.email,editRole:a.role,editCompany:a.company})}deleteUsers(){this.allModuleService.delete(this.tempId,this.url).subscribe(e=>{$("#datatable").DataTable().clear(),this.dtElement.dtInstance.then(e=>{e.destroy()}),this.dtTrigger.next()}),this.getUsers(),$("#delete_user").modal("hide"),this.toastr.success("Users is deleted","Success")}searchName(e){this.rows.splice(0,this.rows.length);let t=this.srch.filter(function(t){return e=e.toLowerCase(),-1!==t.name.toLowerCase().indexOf(e)||!e});this.rows.push(...t)}searchStatus(e){this.rows.splice(0,this.rows.length);let t=this.srch.filter(function(t){return e=e.toLowerCase(),-1!==t.company.toLowerCase().indexOf(e)||!e});this.rows.push(...t)}searchRole(e){this.rows.splice(0,this.rows.length);let t=this.srch.filter(function(t){return e=e.toLowerCase(),-1!==t.role.toLowerCase().indexOf(e)||!e});this.rows.push(...t)}ngOnDestroy(){this.dtTrigger.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(r.a),d.Ub(o.d),d.Ub(l.b))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-user-main"]],viewQuery:function(e,t){if(1&e&&d.Rc(b.a,1),2&e){let e;d.yc(e=d.ic())&&(t.dtElement=e.first)}},decls:422,vars:30,consts:[[1,"page-wrapper"],[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["data-toggle","modal","data-target","#add_user",1,"btn","add-btn"],[1,"fa","fa-plus"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input","focusout"],[1,"focus-label"],[1,"form-group","form-focus","select-focus"],[1,"select","form-control",3,"input"],["value",""],["value","Global Technologies"],["value","Delta Infotech"],["value","Web Developer"],["value","Web Designer"],["value","Android Developer"],["value","Ios Developer"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"table-responsive"],["datatable","","id","datatable",1,"table","table-striped","custom-table","datatable",3,"dtOptions","dtTrigger"],[1,"text-right"],[4,"ngFor","ngForOf"],[4,"ngIf"],["id","add_user","role","dialog",1,"modal","custom-modal","fade"],["role","document",1,"modal-dialog","modal-dialog-centered","modal-lg"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["type","button","data-dismiss","modal","aria-label","Close",1,"close"],["aria-hidden","true"],[1,"modal-body"],[3,"formGroup","ngSubmit"],[1,"col-sm-6"],[1,"form-group"],[1,"text-danger"],["type","text",1,"form-control"],["type","text","formControlName","addUserName",1,"form-control"],["type","email","formControlName","addEmail",1,"form-control"],["type","password",1,"form-control"],["formControlName","addRole",1,"select","form-control"],["formControlName","addCompany",1,"select","form-control"],["type","text",1,"form-control","floating"],[1,"table-responsive","m-t-15"],[1,"table","table-striped","custom-table"],[1,"text-center"],["checked","","type","checkbox"],[1,"submit-section"],[1,"btn","btn-primary","submit-btn"],["id","edit_user","role","dialog",1,"modal","custom-modal","fade"],["value","John","type","text",1,"form-control"],["value","Doe","type","text",1,"form-control"],["type","text","formControlName","editUsersName",1,"form-control"],["type","email","formControlName","editEmail",1,"form-control"],["value","9876543210","type","text",1,"form-control"],["formControlName","editRole",1,"select","form-control"],["selected",""],["formControlName","editCompany",1,"select","form-control"],["type","text","value","FT-0001",1,"form-control","floating"],["id","delete_user","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"table-avatar"],["routerLink","/employees/employeeprofile",1,"avatar"],["alt","",3,"src"],["routerLink","/employees/employeeprofile"],[1,"badge","bg-inverse-danger"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],["data-toggle","modal","data-target","#edit_user",1,"dropdown-item",3,"click"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_user",1,"dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],["class","text-danger",4,"ngIf"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"div",4),d.ac(5,"h3",5),d.Lc(6,"Users"),d.Zb(),d.ac(7,"ul",6),d.ac(8,"li",7),d.ac(9,"a",8),d.Lc(10,"Dashboard"),d.Zb(),d.Zb(),d.ac(11,"li",9),d.Lc(12,"Users"),d.Zb(),d.Zb(),d.Zb(),d.ac(13,"div",10),d.ac(14,"a",11),d.Vb(15,"i",12),d.Lc(16," Add User"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(17,"div",13),d.ac(18,"div",14),d.ac(19,"div",15),d.ac(20,"input",16),d.hc("input",function(e){return t.searchName(e.target.value)})("focusout",function(){return t.rerender()}),d.Zb(),d.ac(21,"label",17),d.Lc(22,"Name"),d.Zb(),d.Zb(),d.Zb(),d.ac(23,"div",14),d.ac(24,"div",18),d.ac(25,"select",19),d.hc("input",function(e){return t.searchStatus(e.target.value)}),d.ac(26,"option",20),d.Lc(27,"Select Company"),d.Zb(),d.ac(28,"option",21),d.Lc(29,"Global Technologies"),d.Zb(),d.ac(30,"option",22),d.Lc(31,"Delta Infotech"),d.Zb(),d.Zb(),d.ac(32,"label",17),d.Lc(33,"Company"),d.Zb(),d.Zb(),d.Zb(),d.ac(34,"div",14),d.ac(35,"div",18),d.ac(36,"select",19),d.hc("input",function(e){return t.searchRole(e.target.value)}),d.ac(37,"option",20),d.Lc(38,"Select Roll"),d.Zb(),d.ac(39,"option",23),d.Lc(40,"Web Developer"),d.Zb(),d.ac(41,"option",24),d.Lc(42,"Web Designer"),d.Zb(),d.ac(43,"option",25),d.Lc(44,"Android Developer"),d.Zb(),d.ac(45,"option",26),d.Lc(46,"Ios Developer"),d.Zb(),d.Zb(),d.ac(47,"label",17),d.Lc(48,"Role"),d.Zb(),d.Zb(),d.Zb(),d.ac(49,"div",14),d.ac(50,"a",27),d.Lc(51," Search "),d.Zb(),d.Zb(),d.Zb(),d.ac(52,"div",28),d.ac(53,"div",29),d.ac(54,"div",30),d.ac(55,"table",31),d.ac(56,"thead"),d.ac(57,"tr"),d.ac(58,"th"),d.Lc(59,"Name"),d.Zb(),d.ac(60,"th"),d.Lc(61,"Email"),d.Zb(),d.ac(62,"th"),d.Lc(63,"Company"),d.Zb(),d.ac(64,"th"),d.Lc(65,"Created Date"),d.Zb(),d.ac(66,"th"),d.Lc(67,"Role"),d.Zb(),d.ac(68,"th",32),d.Lc(69,"Action"),d.Zb(),d.Zb(),d.Zb(),d.ac(70,"tbody"),d.Jc(71,u,30,6,"tr",33),d.Jc(72,Z,4,0,"tr",34),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(73,"div",35),d.ac(74,"div",36),d.ac(75,"div",37),d.ac(76,"div",38),d.ac(77,"h5",39),d.Lc(78,"Add User"),d.Zb(),d.ac(79,"button",40),d.ac(80,"span",41),d.Lc(81,"\xd7"),d.Zb(),d.Zb(),d.Zb(),d.ac(82,"div",42),d.ac(83,"form",43),d.hc("ngSubmit",function(){return t.addUsersSubmit()}),d.ac(84,"div",28),d.ac(85,"div",44),d.ac(86,"div",45),d.ac(87,"label"),d.Lc(88,"First Name "),d.ac(89,"span",46),d.Lc(90,"*"),d.Zb(),d.Zb(),d.Vb(91,"input",47),d.Zb(),d.Zb(),d.ac(92,"div",44),d.ac(93,"div",45),d.ac(94,"label"),d.Lc(95,"Last Name"),d.Zb(),d.Vb(96,"input",47),d.Zb(),d.Zb(),d.ac(97,"div",44),d.ac(98,"div",45),d.ac(99,"label"),d.Lc(100,"Username "),d.ac(101,"span",46),d.Lc(102,"*"),d.Zb(),d.Zb(),d.Vb(103,"input",48),d.Jc(104,m,2,1,"div",34),d.Zb(),d.Zb(),d.ac(105,"div",44),d.ac(106,"div",45),d.ac(107,"label"),d.Lc(108,"Email "),d.ac(109,"span",46),d.Lc(110,"*"),d.Zb(),d.Zb(),d.Vb(111,"input",49),d.Jc(112,g,2,1,"div",34),d.Zb(),d.Zb(),d.ac(113,"div",44),d.ac(114,"div",45),d.ac(115,"label"),d.Lc(116,"Password"),d.Zb(),d.Vb(117,"input",50),d.Zb(),d.Zb(),d.ac(118,"div",44),d.ac(119,"div",45),d.ac(120,"label"),d.Lc(121,"Confirm Password"),d.Zb(),d.Vb(122,"input",50),d.Zb(),d.Zb(),d.ac(123,"div",44),d.ac(124,"div",45),d.ac(125,"label"),d.Lc(126,"Phone "),d.Zb(),d.Vb(127,"input",47),d.Zb(),d.Zb(),d.ac(128,"div",44),d.ac(129,"div",45),d.ac(130,"label"),d.Lc(131,"Role"),d.Zb(),d.ac(132,"select",51),d.ac(133,"option"),d.Lc(134,"Admin"),d.Zb(),d.ac(135,"option"),d.Lc(136,"Client"),d.Zb(),d.ac(137,"option"),d.Lc(138,"Employee"),d.Zb(),d.Zb(),d.Jc(139,f,2,1,"div",34),d.Zb(),d.Zb(),d.ac(140,"div",44),d.ac(141,"div",45),d.ac(142,"label"),d.Lc(143,"Company"),d.Zb(),d.ac(144,"select",52),d.ac(145,"option"),d.Lc(146,"Global Technologies"),d.Zb(),d.ac(147,"option"),d.Lc(148,"Delta Infotech"),d.Zb(),d.Zb(),d.Jc(149,U,2,1,"div",34),d.Zb(),d.Zb(),d.ac(150,"div",44),d.ac(151,"div",45),d.ac(152,"label"),d.Lc(153,"Employee ID "),d.ac(154,"span",46),d.Lc(155,"*"),d.Zb(),d.Zb(),d.Vb(156,"input",53),d.Zb(),d.Zb(),d.Zb(),d.ac(157,"div",54),d.ac(158,"table",55),d.ac(159,"thead"),d.ac(160,"tr"),d.ac(161,"th"),d.Lc(162,"Module Permission"),d.Zb(),d.ac(163,"th",56),d.Lc(164,"Read"),d.Zb(),d.ac(165,"th",56),d.Lc(166,"Write"),d.Zb(),d.ac(167,"th",56),d.Lc(168,"Create"),d.Zb(),d.ac(169,"th",56),d.Lc(170,"Delete"),d.Zb(),d.ac(171,"th",56),d.Lc(172,"Import"),d.Zb(),d.ac(173,"th",56),d.Lc(174,"Export"),d.Zb(),d.Zb(),d.Zb(),d.ac(175,"tbody"),d.ac(176,"tr"),d.ac(177,"td"),d.Lc(178,"Employee"),d.Zb(),d.ac(179,"td",56),d.Vb(180,"input",57),d.Zb(),d.ac(181,"td",56),d.Vb(182,"input",57),d.Zb(),d.ac(183,"td",56),d.Vb(184,"input",57),d.Zb(),d.ac(185,"td",56),d.Vb(186,"input",57),d.Zb(),d.ac(187,"td",56),d.Vb(188,"input",57),d.Zb(),d.ac(189,"td",56),d.Vb(190,"input",57),d.Zb(),d.Zb(),d.ac(191,"tr"),d.ac(192,"td"),d.Lc(193,"Holidays"),d.Zb(),d.ac(194,"td",56),d.Vb(195,"input",57),d.Zb(),d.ac(196,"td",56),d.Vb(197,"input",57),d.Zb(),d.ac(198,"td",56),d.Vb(199,"input",57),d.Zb(),d.ac(200,"td",56),d.Vb(201,"input",57),d.Zb(),d.ac(202,"td",56),d.Vb(203,"input",57),d.Zb(),d.ac(204,"td",56),d.Vb(205,"input",57),d.Zb(),d.Zb(),d.ac(206,"tr"),d.ac(207,"td"),d.Lc(208,"Leaves"),d.Zb(),d.ac(209,"td",56),d.Vb(210,"input",57),d.Zb(),d.ac(211,"td",56),d.Vb(212,"input",57),d.Zb(),d.ac(213,"td",56),d.Vb(214,"input",57),d.Zb(),d.ac(215,"td",56),d.Vb(216,"input",57),d.Zb(),d.ac(217,"td",56),d.Vb(218,"input",57),d.Zb(),d.ac(219,"td",56),d.Vb(220,"input",57),d.Zb(),d.Zb(),d.ac(221,"tr"),d.ac(222,"td"),d.Lc(223,"Events"),d.Zb(),d.ac(224,"td",56),d.Vb(225,"input",57),d.Zb(),d.ac(226,"td",56),d.Vb(227,"input",57),d.Zb(),d.ac(228,"td",56),d.Vb(229,"input",57),d.Zb(),d.ac(230,"td",56),d.Vb(231,"input",57),d.Zb(),d.ac(232,"td",56),d.Vb(233,"input",57),d.Zb(),d.ac(234,"td",56),d.Vb(235,"input",57),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(236,"div",58),d.ac(237,"button",59),d.Lc(238,"Submit"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(239,"div",60),d.ac(240,"div",36),d.ac(241,"div",37),d.ac(242,"div",38),d.ac(243,"h5",39),d.Lc(244,"Edit User"),d.Zb(),d.ac(245,"button",40),d.ac(246,"span",41),d.Lc(247,"\xd7"),d.Zb(),d.Zb(),d.Zb(),d.ac(248,"div",42),d.ac(249,"form",43),d.hc("ngSubmit",function(){return t.editUsersSubmit()}),d.ac(250,"div",28),d.ac(251,"div",44),d.ac(252,"div",45),d.ac(253,"label"),d.Lc(254,"First Name "),d.ac(255,"span",46),d.Lc(256,"*"),d.Zb(),d.Zb(),d.Vb(257,"input",61),d.Zb(),d.Zb(),d.ac(258,"div",44),d.ac(259,"div",45),d.ac(260,"label"),d.Lc(261,"Last Name"),d.Zb(),d.Vb(262,"input",62),d.Zb(),d.Zb(),d.ac(263,"div",44),d.ac(264,"div",45),d.ac(265,"label"),d.Lc(266,"Username "),d.ac(267,"span",46),d.Lc(268,"*"),d.Zb(),d.Zb(),d.Vb(269,"input",63),d.Jc(270,I,2,1,"div",34),d.Zb(),d.Zb(),d.ac(271,"div",44),d.ac(272,"div",45),d.ac(273,"label"),d.Lc(274,"Email "),d.ac(275,"span",46),d.Lc(276,"*"),d.Zb(),d.Zb(),d.Vb(277,"input",64),d.Jc(278,w,2,1,"div",34),d.Zb(),d.Zb(),d.ac(279,"div",44),d.ac(280,"div",45),d.ac(281,"label"),d.Lc(282,"Password"),d.Zb(),d.Vb(283,"input",50),d.Zb(),d.Zb(),d.ac(284,"div",44),d.ac(285,"div",45),d.ac(286,"label"),d.Lc(287,"Confirm Password"),d.Zb(),d.Vb(288,"input",50),d.Zb(),d.Zb(),d.ac(289,"div",44),d.ac(290,"div",45),d.ac(291,"label"),d.Lc(292,"Phone "),d.Zb(),d.Vb(293,"input",65),d.Zb(),d.Zb(),d.ac(294,"div",44),d.ac(295,"div",45),d.ac(296,"label"),d.Lc(297,"Role"),d.Zb(),d.ac(298,"select",66),d.ac(299,"option"),d.Lc(300,"Admin"),d.Zb(),d.ac(301,"option"),d.Lc(302,"Client"),d.Zb(),d.ac(303,"option",67),d.Lc(304,"Employee"),d.Zb(),d.Zb(),d.Jc(305,E,2,1,"div",34),d.Zb(),d.Zb(),d.ac(306,"div",44),d.ac(307,"div",45),d.ac(308,"label"),d.Lc(309,"Company"),d.Zb(),d.ac(310,"select",68),d.ac(311,"option"),d.Lc(312,"Global Technologies"),d.Zb(),d.ac(313,"option"),d.Lc(314,"Delta Infotech"),d.Zb(),d.Zb(),d.Jc(315,R,2,1,"div",34),d.Zb(),d.Zb(),d.ac(316,"div",44),d.ac(317,"div",45),d.ac(318,"label"),d.Lc(319,"Employee ID "),d.ac(320,"span",46),d.Lc(321,"*"),d.Zb(),d.Zb(),d.Vb(322,"input",69),d.Zb(),d.Zb(),d.Zb(),d.ac(323,"div",54),d.ac(324,"table",55),d.ac(325,"thead"),d.ac(326,"tr"),d.ac(327,"th"),d.Lc(328,"Module Permission"),d.Zb(),d.ac(329,"th",56),d.Lc(330,"Read"),d.Zb(),d.ac(331,"th",56),d.Lc(332,"Write"),d.Zb(),d.ac(333,"th",56),d.Lc(334,"Create"),d.Zb(),d.ac(335,"th",56),d.Lc(336,"Delete"),d.Zb(),d.ac(337,"th",56),d.Lc(338,"Import"),d.Zb(),d.ac(339,"th",56),d.Lc(340,"Export"),d.Zb(),d.Zb(),d.Zb(),d.ac(341,"tbody"),d.ac(342,"tr"),d.ac(343,"td"),d.Lc(344,"Employee"),d.Zb(),d.ac(345,"td",56),d.Vb(346,"input",57),d.Zb(),d.ac(347,"td",56),d.Vb(348,"input",57),d.Zb(),d.ac(349,"td",56),d.Vb(350,"input",57),d.Zb(),d.ac(351,"td",56),d.Vb(352,"input",57),d.Zb(),d.ac(353,"td",56),d.Vb(354,"input",57),d.Zb(),d.ac(355,"td",56),d.Vb(356,"input",57),d.Zb(),d.Zb(),d.ac(357,"tr"),d.ac(358,"td"),d.Lc(359,"Holidays"),d.Zb(),d.ac(360,"td",56),d.Vb(361,"input",57),d.Zb(),d.ac(362,"td",56),d.Vb(363,"input",57),d.Zb(),d.ac(364,"td",56),d.Vb(365,"input",57),d.Zb(),d.ac(366,"td",56),d.Vb(367,"input",57),d.Zb(),d.ac(368,"td",56),d.Vb(369,"input",57),d.Zb(),d.ac(370,"td",56),d.Vb(371,"input",57),d.Zb(),d.Zb(),d.ac(372,"tr"),d.ac(373,"td"),d.Lc(374,"Leaves"),d.Zb(),d.ac(375,"td",56),d.Vb(376,"input",57),d.Zb(),d.ac(377,"td",56),d.Vb(378,"input",57),d.Zb(),d.ac(379,"td",56),d.Vb(380,"input",57),d.Zb(),d.ac(381,"td",56),d.Vb(382,"input",57),d.Zb(),d.ac(383,"td",56),d.Vb(384,"input",57),d.Zb(),d.ac(385,"td",56),d.Vb(386,"input",57),d.Zb(),d.Zb(),d.ac(387,"tr"),d.ac(388,"td"),d.Lc(389,"Events"),d.Zb(),d.ac(390,"td",56),d.Vb(391,"input",57),d.Zb(),d.ac(392,"td",56),d.Vb(393,"input",57),d.Zb(),d.ac(394,"td",56),d.Vb(395,"input",57),d.Zb(),d.ac(396,"td",56),d.Vb(397,"input",57),d.Zb(),d.ac(398,"td",56),d.Vb(399,"input",57),d.Zb(),d.ac(400,"td",56),d.Vb(401,"input",57),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(402,"div",58),d.ac(403,"button",59),d.Lc(404,"Save"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(405,"div",70),d.ac(406,"div",71),d.ac(407,"div",37),d.ac(408,"div",42),d.ac(409,"div",72),d.ac(410,"h3"),d.Lc(411,"Delete User"),d.Zb(),d.ac(412,"p"),d.Lc(413,"Are you sure want to delete?"),d.Zb(),d.Zb(),d.ac(414,"div",73),d.ac(415,"div",28),d.ac(416,"div",74),d.ac(417,"a",75),d.hc("click",function(){return t.deleteUsers()}),d.Lc(418,"Delete"),d.Zb(),d.Zb(),d.ac(419,"div",74),d.ac(420,"a",76),d.Lc(421,"Cancel"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(55),d.pc("dtOptions",t.dtOptions)("dtTrigger",t.dtTrigger),d.Ib(16),d.pc("ngForOf",t.allUsers),d.Ib(1),d.pc("ngIf",0===t.allUsers.length),d.Ib(11),d.pc("formGroup",t.addUsers),d.Ib(20),d.Mb("invalid",t.addUsers.get("addUserName").invalid&&t.addUsers.get("addUserName").touched),d.Ib(1),d.pc("ngIf",t.addUsers.get("addUserName").invalid&&t.addUsers.get("addUserName").touched),d.Ib(7),d.Mb("invalid",t.addUsers.get("addEmail").invalid&&t.addUsers.get("addEmail").touched),d.Ib(1),d.pc("ngIf",t.addUsers.get("addEmail").invalid&&t.addUsers.get("addEmail").touched),d.Ib(20),d.Mb("invalid",t.addUsers.get("addRole").invalid&&t.addUsers.get("addRole").touched),d.Ib(7),d.pc("ngIf",t.addUsers.get("addRole").invalid&&t.addUsers.get("addRole").touched),d.Ib(5),d.Mb("invalid",t.addUsers.get("addCompany").invalid&&t.addUsers.get("addCompany").touched),d.Ib(5),d.pc("ngIf",t.addUsers.get("addCompany").invalid&&t.addUsers.get("addCompany").touched),d.Ib(100),d.pc("formGroup",t.editUsers),d.Ib(20),d.Mb("invalid",t.editUsers.get("editUsersName").invalid&&t.editUsers.get("editUsersName").touched),d.Ib(1),d.pc("ngIf",t.editUsers.get("editUsersName").invalid&&t.editUsers.get("editUsersName").touched),d.Ib(7),d.Mb("invalid",t.editUsers.get("editEmail").invalid&&t.editUsers.get("editEmail").touched),d.Ib(1),d.pc("ngIf",t.editUsers.get("editEmail").invalid&&t.editUsers.get("editEmail").touched),d.Ib(20),d.Mb("invalid",t.editUsers.get("editRole").invalid&&t.editUsers.get("editRole").touched),d.Ib(7),d.pc("ngIf",t.editUsers.get("editRole").invalid&&t.editUsers.get("editRole").touched),d.Ib(5),d.Mb("invalid",t.editUsers.get("editCompany").invalid&&t.editUsers.get("editCompany").touched),d.Ib(5),d.pc("ngIf",t.editUsers.get("editCompany").invalid&&t.editUsers.get("editCompany").touched))},directives:[c.e,o.s,o.y,b.a,i.l,i.m,o.x,o.p,o.h,o.b,o.o,o.f,o.v],styles:[""]}),e})()}]}];let D=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=d.Sb({type:e}),e.\u0275inj=d.Rb({imports:[[c.f.forChild(x)],c.f]}),e})(),S=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=d.Sb({type:e}),e.\u0275inj=d.Rb({imports:[[i.c,D,b.b,o.j,o.u]]}),e})()}}]);