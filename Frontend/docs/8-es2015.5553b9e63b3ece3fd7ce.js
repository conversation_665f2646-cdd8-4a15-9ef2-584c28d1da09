(window.webpackJsonp=window.webpackJsonp||[]).push([[8],{"3E0/":function(t,e,r){"use strict";r.d(e,"a",function(){return s});var i=r("D0XW"),n=r("7o/Q"),o=r("WMd4");function s(t,e=i.a){var r;const n=(r=t)instanceof Date&&!isNaN(+r)?+t-e.now():Math.abs(t);return t=>t.lift(new c(n,e))}class c{constructor(t,e){this.delay=t,this.scheduler=e}call(t,e){return e.subscribe(new a(t,this.delay,this.scheduler))}}class a extends n.a{constructor(t,e,r){super(t),this.delay=e,this.scheduler=r,this.queue=[],this.active=!1,this.errored=!1}static dispatch(t){const e=t.source,r=e.queue,i=t.scheduler,n=t.destination;for(;r.length>0&&r[0].time-i.now()<=0;)r.shift().notification.observe(n);if(r.length>0){const e=Math.max(0,r[0].time-i.now());this.schedule(t,e)}else this.unsubscribe(),e.active=!1}_schedule(t){this.active=!0,this.destination.add(t.schedule(a.dispatch,this.delay,{source:this,destination:this.destination,scheduler:t}))}scheduleNotification(t){if(!0===this.errored)return;const e=this.scheduler,r=new u(e.now()+this.delay,t);this.queue.push(r),!1===this.active&&this._schedule(e)}_next(t){this.scheduleNotification(o.a.createNext(t))}_error(t){this.errored=!0,this.queue=[],this.destination.error(t),this.unsubscribe()}_complete(){this.scheduleNotification(o.a.createComplete()),this.unsubscribe()}}class u{constructor(t,e){this.time=t,this.notification=e}}},AuF9:function(t,e,r){"use strict";r.d(e,"a",function(){return c});var i=r("un/a"),n=r("AytR"),o=r("fXoL"),s=r("tk/3");let c=(()=>{class t{constructor(t){this.http=t,this.baseUrl=n.a.baseUrl}getEmployees(){return this.http.get(`${this.baseUrl}/hrCrEmp/empList`)}getEmpListView(t,e){return this.http.get(t,{params:e}).pipe(Object(i.a)(3))}sendGetRequest(t,e){return console.log("@sendGetRequest"),this.http.get(t,{params:e}).pipe(Object(i.a)(3))}createEmploy(t){return this.http.post(`${this.baseUrl}/hrCrEmp/create`,t)}updateEmploy(t){return this.http.put(`${this.baseUrl}/hrCrEmp/edit`,t)}getEmployeeById(t){return this.http.get(`${this.baseUrl}/hrCrEmp/getData/${t}`)}findEmployeeById(t){return this.http.get(`${this.baseUrl}/hrCrEmp/find/${t}`)}getEmployeeByLoginCode(t){return this.http.get(`${this.baseUrl}/hrCrEmp/findByLoginCode/${t}`)}uploadProfileImage(t,e){return this.http.post(`${this.baseUrl}/multimedia/profile/${t}`,e)}getAlkpSearchByKeyword(t){return this.http.get(`${this.baseUrl}/alkp/search/${t}`)}saveEmployeeAssignemntData(t){return this.http.post(`${this.baseUrl}/hrCrEmpAssgnmnt/create`,t)}updateEmployeeAssignment(t){return this.http.put(`${this.baseUrl}/hrCrEmpAssgnmnt/edit`,t)}getLastAssignmentByHrCrEmpId(t){return this.http.get(`${this.baseUrl}/hrCrEmpAssgnmnt/getByHrCrEmp/${t}`)}getEmployeeAssignmentByHrCrEmpId(t){return this.http.get(`${this.baseUrl}/hrCrEmpAssgnmnt/getByHrCrEmpId/${t}`)}saveOrUpdateBankAndPayroll(t){return this.http.post(`${this.baseUrl}/hrCrEmpAssgnmnt/saveBankAndPayroll`,t)}getDesignations(){return this.http.get(`${this.baseUrl}/designation/getAll`)}getALLDivisions(t){return this.http.get(`${this.baseUrl}/address/division/${t}`)}fetchAllDivision(){return this.http.get(`${this.baseUrl}/address/division/getAll`)}getDistrictByDivId(t){return this.http.get(`${this.baseUrl}/address/division/${t}`)}getAllDistrict(t,e){return console.log("@getAllDistrict"),this.http.get(t,{params:e}).pipe(Object(i.a)(3))}getAllUpazila(t,e){return console.log("@getAllUpazila"),this.http.get(t,{params:e}).pipe(Object(i.a)(3))}getAllUnions(t,e){return console.log("@sendGetRequest"),this.http.get(t,{params:e}).pipe(Object(i.a)(3))}saveHrCrEmpEdu(t){return this.http.post(`${this.baseUrl}/hrCrEmpEdu/create`,t)}findhrCrEmpEduByEmpId(t){return this.http.get(`${this.baseUrl}/hrCrEmpEdu/find/${t}`)}findhrCrEmpEduById(t){return this.http.get(`${this.baseUrl}/hrCrEmpEdu/get/${t}`)}edithrCrEmpEducation(t){return this.http.put(`${this.baseUrl}/hrCrEmpEdu/edit`,t)}deleteHrCrEmpEducation(t){return this.http.delete(`${this.baseUrl}/hrCrEmpEdu/delete/${t}`)}getAllRawAttendanceData(){return this.http.get(`${this.baseUrl}/attn/findAllBySrcType`)}getAllRawAttendanceData2(t,e){return this.http.get(t,{params:e}).pipe(Object(i.a)(3))}createAttnViaHr(t){return this.http.post(`${this.baseUrl}/AttnViaHr/save`,t)}getAllViaHrAttnData(){return this.http.get(`${this.baseUrl}/AttnViaHr/findAllBySrcType`)}getAllViaHrAttnData2(t,e){return this.http.get(t,{params:e}).pipe(Object(i.a)(3))}getSearchAttn(t,e){return this.http.get(t,{params:e}).pipe(Object(i.a)(3))}createLeave(t){return this.http.post(`${this.baseUrl}/leaveTrnse/save`,t)}}return t.\u0275fac=function(e){return new(e||t)(o.ec(s.c))},t.\u0275prov=o.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},WMd4:function(t,e,r){"use strict";r.d(e,"a",function(){return s});var i=r("EY2u"),n=r("LRne"),o=r("z6cu");let s=(()=>{class t{constructor(t,e,r){this.kind=t,this.value=e,this.error=r,this.hasValue="N"===t}observe(t){switch(this.kind){case"N":return t.next&&t.next(this.value);case"E":return t.error&&t.error(this.error);case"C":return t.complete&&t.complete()}}do(t,e,r){switch(this.kind){case"N":return t&&t(this.value);case"E":return e&&e(this.error);case"C":return r&&r()}}accept(t,e,r){return t&&"function"==typeof t.next?this.observe(t):this.do(t,e,r)}toObservable(){switch(this.kind){case"N":return Object(n.a)(this.value);case"E":return Object(o.a)(this.error);case"C":return Object(i.b)()}throw new Error("unexpected notification kind value")}static createNext(e){return void 0!==e?new t("N",e):t.undefinedValueNotification}static createError(e){return new t("E",void 0,e)}static createComplete(){return t.completeNotification}}return t.completeNotification=new t("C"),t.undefinedValueNotification=new t("N",void 0),t})()},X3zk:function(t,e,r){"use strict";r.r(e),r.d(e,"LoginModule",function(){return z});var i=r("ofXK"),n=r("3Pt+"),o=r("tyNb"),s=r("fXoL");let c=(()=>{class t{constructor(){}ngOnInit(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.Ob({type:t,selectors:[["app-forgot"]],decls:26,vars:0,consts:[[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/logo2.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"account-title"],[1,"account-subtitle"],[1,"form-group"],["type","text",1,"form-control"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn"],[1,"account-footer"],["routerLink","/login/login"]],template:function(t,e){1&t&&(s.ac(0,"div",0),s.ac(1,"a",1),s.Lc(2,"Apply Job"),s.Zb(),s.ac(3,"div",2),s.ac(4,"div",3),s.ac(5,"a",4),s.Vb(6,"img",5),s.Zb(),s.Zb(),s.ac(7,"div",6),s.ac(8,"div",7),s.ac(9,"h3",8),s.Lc(10,"Forgot Password?"),s.Zb(),s.ac(11,"p",9),s.Lc(12,"Enter your email to get a password reset link"),s.Zb(),s.ac(13,"form"),s.ac(14,"div",10),s.ac(15,"label"),s.Lc(16,"Email Address"),s.Zb(),s.Vb(17,"input",11),s.Zb(),s.ac(18,"div",12),s.ac(19,"button",13),s.Lc(20,"Reset Password"),s.Zb(),s.Zb(),s.ac(21,"div",14),s.ac(22,"p"),s.Lc(23,"Remember your password? "),s.ac(24,"a",15),s.Lc(25,"Login"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb())},directives:[o.e,n.x,n.p,n.q],styles:[""]}),t})(),a=(()=>{class t{constructor(){}ngOnInit(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.Ob({type:t,selectors:[["app-lockscreen"]],decls:27,vars:0,consts:[[1,"main-wrapper"],[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/logo2.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"lock-user"],["alt","","src","assets/img/profiles/avatar-2.jpg",1,"rounded-circle"],["action","dashboard"],[1,"form-group"],["type","password",1,"form-control"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn"],[1,"account-footer"],["routerLink","/login/register"]],template:function(t,e){1&t&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"a",2),s.Lc(3,"Apply Job"),s.Zb(),s.ac(4,"div",3),s.ac(5,"div",4),s.ac(6,"a",5),s.Vb(7,"img",6),s.Zb(),s.Zb(),s.ac(8,"div",7),s.ac(9,"div",8),s.ac(10,"div",9),s.Vb(11,"img",10),s.ac(12,"h4"),s.Lc(13,"John Doe"),s.Zb(),s.Zb(),s.ac(14,"form",11),s.ac(15,"div",12),s.ac(16,"label"),s.Lc(17,"Password"),s.Zb(),s.Vb(18,"input",13),s.Zb(),s.ac(19,"div",14),s.ac(20,"button",15),s.Lc(21,"Enter"),s.Zb(),s.Zb(),s.ac(22,"div",16),s.ac(23,"p"),s.Lc(24,"Sign in as a different user? "),s.ac(25,"a",17),s.Lc(26,"Login"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb())},directives:[o.e,n.x,n.p,n.q],styles:[""]}),t})();var u=r("mrSG"),l=r("AuF9"),d=r("d//k"),b=r("3E0/"),p=r("5eHb");function h(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Username is required"),s.Zb())}function g(t,e){if(1&t&&(s.ac(0,"div",30),s.Jc(1,h,2,0,"div",31),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.username.errors.required)}}function m(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Password is required"),s.Zb())}function f(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Password must be at least 4 characters"),s.Zb())}function v(t,e){if(1&t&&(s.ac(0,"div",30),s.Jc(1,m,2,0,"div",31),s.Jc(2,f,2,0,"div",31),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.password.errors.required),s.Ib(1),s.pc("ngIf",t.f.password.errors.minlength)}}function Z(t,e){1&t&&s.Vb(0,"span",32)}const y=function(t){return{"is-invalid":t}};let w=(()=>{class t{constructor(t,e,r,i,n){this.formBuilder=t,this.toastr=e,this.loginService=r,this.router=i,this.hrcremp=n,this.loading=!1,this.submitted=!1}ngOnInit(){this.initializeForm(),this.initButtonRippleEffect(),this.loginService.isLoggedIn&&this.router.navigate(["dashboard"])}get f(){return this.loginForm.controls}initializeForm(){this.loginForm=this.formBuilder.group({username:["",n.w.required],password:["",[n.w.minLength(4),n.w.required]]})}onSubmit(){return Object(u.a)(this,void 0,void 0,function*(){if(this.submitted=!0,this.loading=!0,this.loginForm.invalid)return;let t=Object.assign(this.loginForm.value);console.log("Above Generate Token"),this.loginService.generateToken(t).pipe(Object(b.a)(1300)).subscribe(t=>{console.log("Inside Generate Token"),this.loginService.loginUser(t.token),this.loginService.getCurrentUser().subscribe(t=>{console.log("Inside Current User"),console.log(t),this.loginService.setUser(t);let e=this.loginService.getLoginUserRole();e.includes("ROLE_USER")||e.includes("ROLE_ADMIN")||e.includes("ROLE_SUPER_ADMIN")?(this.loading=!1,this.toastr.success("You are now authenticated","Success",{positionClass:"toast-custom"}),this.router.navigate(["dashboard"]),this.loginService.loginStatusSubject.next(!0)):(this.loading=!1,this.loginService.logout())})},t=>{this.loading=!1,console.log("Error !"),console.log(t),this.toastr.error(""+t.error.message)})})}initButtonRippleEffect(){document.querySelectorAll(".btn-ripple").forEach(t=>{t.addEventListener("click",function(t){!function(t){const e=t.currentTarget;let r=t.clientX-t.target.getBoundingClientRect().left,i=t.clientY-t.target.getBoundingClientRect().top,n=document.createElement("span");n.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 0.8s linear infinite;",n.style.left=`${r}px`,n.style.top=`${i}px`,e.appendChild(n),setTimeout(()=>{n.remove()},800)}(t)})})}}return t.\u0275fac=function(e){return new(e||t)(s.Ub(n.d),s.Ub(p.b),s.Ub(d.a),s.Ub(o.c),s.Ub(l.a))},t.\u0275cmp=s.Ob({type:t,selectors:[["app-login"]],decls:46,vars:11,consts:[[1,"account-content"],[1,"center-screen"],[1,"container"],[1,"row"],[1,"col-12"],[2,"height","0px"],[1,"row","login-box-ct"],[1,"col-sm-12","col-md-6","col-xl-6","login-box-title"],[1,"account-box"],[1,"account-wrapper"],[1,"account-logo"],["href","javascript:"],["src","assets/img/one_direction_logo.png","alt","Dreamguy's Technologies"],[1,"company-title",2,"color","aliceblue"],[1,"account-title",2,"color","aliceblue"],[1,"account-subtitle",2,"color","aliceblue"],[1,"col-6","login-box-form"],[1,"account-title"],[1,"account-subtitle"],[3,"formGroup","ngSubmit"],[1,"form-group"],["formControlName","username","type","text",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"col"],[1,"col-auto"],["formControlName","password","formControlName","password","type","password",1,"form-control",3,"ngClass"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn","btn-ripple",3,"disabled"],["class","spinner-border spinner-border-sm mr-1",4,"ngIf"],[1,"account-footer"],[1,"invalid-feedback"],[4,"ngIf"],[1,"spinner-border","spinner-border-sm","mr-1"]],template:function(t,e){1&t&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"div",4),s.Vb(5,"div",5),s.Zb(),s.ac(6,"div",4),s.ac(7,"div",6),s.ac(8,"div",7),s.ac(9,"div",8),s.ac(10,"div",9),s.ac(11,"div",10),s.ac(12,"a",11),s.Vb(13,"img",12),s.Zb(),s.Zb(),s.ac(14,"h3",13),s.Lc(15,"One Direction Company"),s.Zb(),s.ac(16,"h4",14),s.Lc(17,"( Smart HRMS )"),s.Zb(),s.ac(18,"p",15),s.Lc(19,"Manage Your Resource Smartly"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",16),s.ac(21,"div",8),s.ac(22,"div",9),s.ac(23,"h3",17),s.Lc(24,"Login"),s.Zb(),s.ac(25,"p",18),s.Lc(26,"Access your account"),s.Zb(),s.ac(27,"form",19),s.hc("ngSubmit",function(){return e.onSubmit()}),s.ac(28,"div",20),s.ac(29,"label"),s.Lc(30,"Username"),s.Zb(),s.Vb(31,"input",21),s.Jc(32,g,2,1,"div",22),s.Zb(),s.ac(33,"div",20),s.ac(34,"div",3),s.ac(35,"div",23),s.ac(36,"label"),s.Lc(37,"Password"),s.Zb(),s.Zb(),s.Vb(38,"div",24),s.Zb(),s.Vb(39,"input",25),s.Jc(40,v,3,2,"div",22),s.Zb(),s.ac(41,"div",26),s.ac(42,"button",27),s.Jc(43,Z,1,0,"span",28),s.Lc(44," Login "),s.Zb(),s.Zb(),s.Vb(45,"div",29),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&t&&(s.Ib(27),s.pc("formGroup",e.loginForm),s.Ib(4),s.pc("ngClass",s.tc(7,y,e.submitted&&e.f.username.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.username.errors),s.Ib(7),s.pc("ngClass",s.tc(9,y,e.submitted&&e.f.password.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.password.errors),s.Ib(2),s.pc("disabled",e.loading),s.Ib(1),s.pc("ngIf",e.loading))},directives:[n.x,n.p,n.h,n.b,n.o,n.f,i.k,i.m],styles:[".center-screen[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh}body[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom right,hsla(0,0%,100%,.3) 30%,rgba(102,126,234,.5) 50%,rgba(118,75,162,.3)),url(/src/assets/img/ROIKBN.jpg)}div.main-wrapper[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom right,hsla(0,0%,100%,.3) 30%,rgba(102,126,234,.5) 50%,rgba(118,75,162,.3)),url(ROIKBN.118a4b073dacdb8abebb.jpg)}div.account-content[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom right,hsla(0,0%,100%,.3) 30%,rgba(176,188,240,.****************) 50%,rgba(43,0,73,.4)),url(ROIKBN.118a4b073dacdb8abebb.jpg)}.account-box[_ngcontent-%COMP%]{background-color:#fff;border:2px solid #ededed;border-radius:4px;box-shadow:0 1px 1px 0 rgb(0 0 0/20%);margin:0 auto;overflow:hidden;width:350px;height:100%}company-title[_ngcontent-%COMP%]{font-size:26px;font-weight:500;margin-bottom:5px;text-align:center}.account-title[_ngcontent-%COMP%]{font-size:20px}.login-box-ct[_ngcontent-%COMP%]   .login-box-title[_ngcontent-%COMP%]{padding-right:0}.login-box-ct[_ngcontent-%COMP%]   .login-box-form[_ngcontent-%COMP%]{padding-left:0}.login-box-ct[_ngcontent-%COMP%]   .login-box-title[_ngcontent-%COMP%]   .account-box[_ngcontent-%COMP%]{margin-right:0}.login-box-ct[_ngcontent-%COMP%]   .login-box-form[_ngcontent-%COMP%]   .account-box[_ngcontent-%COMP%]{margin-left:0}.login-box-title[_ngcontent-%COMP%]   .account-box[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#fff 30%,rgba(0,0,0,.5) 50%),url(login-bg-img-1.4d62662e11e36d35684e.png);background-position:50%;background-repeat:no-repeat;background-size:cover}.btn-ripple[_ngcontent-%COMP%]{position:relative;overflow:hidden}@-webkit-keyframes animate{0%{width:0;height:0;opacity:.5}to{width:700px;height:700px;opacity:0}}@keyframes animate{0%{width:0;height:0;opacity:.5}to{width:700px;height:700px;opacity:0}}"]}),t})(),I=(()=>{class t{constructor(){}ngOnInit(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.Ob({type:t,selectors:[["app-otp"]],decls:28,vars:0,consts:[[1,"main-wrapper"],[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/logo2.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"account-title"],[1,"account-subtitle"],["action","dashboard"],[1,"otp-wrap"],["type","text","placeholder","0","maxlength","1",1,"otp-input"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn"],[1,"account-footer"]],template:function(t,e){1&t&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"a",2),s.Lc(3,"Apply Job"),s.Zb(),s.ac(4,"div",3),s.ac(5,"div",4),s.ac(6,"a",5),s.Vb(7,"img",6),s.Zb(),s.Zb(),s.ac(8,"div",7),s.ac(9,"div",8),s.ac(10,"h3",9),s.Lc(11,"OTP"),s.Zb(),s.ac(12,"p",10),s.Lc(13,"Verification your account"),s.Zb(),s.ac(14,"form",11),s.ac(15,"div",12),s.Vb(16,"input",13),s.Vb(17,"input",13),s.Vb(18,"input",13),s.Vb(19,"input",13),s.Zb(),s.ac(20,"div",14),s.ac(21,"button",15),s.Lc(22,"Enter"),s.Zb(),s.Zb(),s.ac(23,"div",16),s.ac(24,"p"),s.Lc(25,"Not yet received? "),s.ac(26,"a"),s.Lc(27,"Resend OTP"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb())},directives:[o.e,n.x,n.p,n.q],styles:[""]}),t})();var L=r("SxV6"),C=r("ur0Y");function E(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Username is required"),s.Zb())}function x(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,E,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.username.errors.required)}}function O(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Email is required"),s.Zb())}function A(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Email must be a valid email address"),s.Zb())}function U(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,O,2,0,"div",27),s.Jc(2,A,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.email.errors.required),s.Ib(1),s.pc("ngIf",t.f.email.errors.email)}}function k(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"First Name is required"),s.Zb())}function P(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,k,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.firstName.errors.required)}}function N(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Last Name is required"),s.Zb())}function j(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,N,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.lastName.errors.required)}}function S(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Phone is required"),s.Zb())}function $(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,S,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.phone.errors.required)}}function q(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Password is required"),s.Zb())}function R(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Password must be at least 4 characters"),s.Zb())}function V(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,q,2,0,"div",27),s.Jc(2,R,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.password.errors.required),s.Ib(1),s.pc("ngIf",t.f.password.errors.minlength)}}function M(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Confirm Password is required"),s.Zb())}function _(t,e){1&t&&(s.ac(0,"div"),s.Lc(1,"Passwords must match"),s.Zb())}function J(t,e){if(1&t&&(s.ac(0,"div",26),s.Jc(1,M,2,0,"div",27),s.Jc(2,_,2,0,"div",27),s.Zb()),2&t){const t=s.jc();s.Ib(1),s.pc("ngIf",t.f.confirmPassword.errors.required),s.Ib(1),s.pc("ngIf",t.f.confirmPassword.errors.mustMatch)}}function B(t,e){1&t&&s.Vb(0,"span",28)}const D=function(t){return{"is-invalid":t}},T=[{path:"",component:w},{path:"forgot",component:c},{path:"register",component:(()=>{class t{constructor(t,e,r,i,n){this.formBuilder=t,this.loginService=e,this.route=r,this.router=i,this.toastr=n,this.loading=!1,this.submitted=!1}ngOnInit(){this.id=this.route.snapshot.params.id,this.isAddMode=!this.id,this.initializeForm()}initializeForm(){const t={validators:Object(C.a)("password","confirmPassword")};this.form=this.formBuilder.group({username:["",n.w.required],firstName:["",n.w.required],lastName:["",n.w.required],phone:["",n.w.required],email:["",[n.w.required,n.w.email]],password:["",[n.w.minLength(4),n.w.required]],confirmPassword:["",n.w.required]},t),this.isAddMode||this.loginService.getById(this.id).pipe(Object(L.a)()).subscribe(t=>this.form.patchValue(t))}get f(){return this.form.controls}onSubmit(){this.submitted=!0,console.log("OK"+this.isAddMode),this.form.invalid||(this.loading=!0,this.isAddMode&&this.createUser())}createUser(){this.loginService.register(this.form.value).pipe(Object(L.a)()).subscribe(()=>{this.toastr.success("Successfully Registered"),this.router.navigate(["../"],{relativeTo:this.route})},t=>{this.toastr.error(t.error.message)}).add(()=>this.loading=!1)}}return t.\u0275fac=function(e){return new(e||t)(s.Ub(n.d),s.Ub(d.a),s.Ub(o.a),s.Ub(o.c),s.Ub(p.b))},t.\u0275cmp=s.Ob({type:t,selectors:[["app-register"]],decls:59,vars:31,consts:[[1,"main-wrapper"],[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/w_logo.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"account-title"],[1,"account-subtitle"],[3,"formGroup","ngSubmit"],[1,"form-group"],["formControlName","username","type","text",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],["formControlName","email","type","email",1,"form-control",3,"ngClass"],["formControlName","firstName","type","text",1,"form-control",3,"ngClass"],["formControlName","lastName","type","text",1,"form-control",3,"ngClass"],["formControlName","phone","type","number",1,"form-control",3,"ngClass"],["type","password","formControlName","password",1,"form-control",3,"ngClass"],["type","password","formControlName","confirmPassword",1,"form-control",3,"ngClass"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn",3,"disabled"],["class","spinner-border spinner-border-sm mr-1",4,"ngIf"],[1,"account-footer"],["routerLink","/login/login"],[1,"invalid-feedback"],[4,"ngIf"],[1,"spinner-border","spinner-border-sm","mr-1"]],template:function(t,e){1&t&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"a",2),s.Lc(3,"Apply Job"),s.Zb(),s.ac(4,"div",3),s.ac(5,"div",4),s.ac(6,"a",5),s.Vb(7,"img",6),s.Zb(),s.Zb(),s.ac(8,"div",7),s.ac(9,"div",8),s.ac(10,"h3",9),s.Lc(11,"Register"),s.Zb(),s.ac(12,"p",10),s.Lc(13,"Access to our dashboard"),s.Zb(),s.ac(14,"form",11),s.hc("ngSubmit",function(){return e.onSubmit()}),s.ac(15,"div",12),s.ac(16,"label"),s.Lc(17,"Username"),s.Zb(),s.Vb(18,"input",13),s.Jc(19,x,2,1,"div",14),s.Zb(),s.ac(20,"div",12),s.ac(21,"label"),s.Lc(22,"Email"),s.Zb(),s.Vb(23,"input",15),s.Jc(24,U,3,2,"div",14),s.Zb(),s.ac(25,"div",12),s.ac(26,"label"),s.Lc(27,"First Name"),s.Zb(),s.Vb(28,"input",16),s.Jc(29,P,2,1,"div",14),s.Zb(),s.ac(30,"div",12),s.ac(31,"label"),s.Lc(32,"Last Name"),s.Zb(),s.Vb(33,"input",17),s.Jc(34,j,2,1,"div",14),s.Zb(),s.ac(35,"div",12),s.ac(36,"label"),s.Lc(37,"Phone"),s.Zb(),s.Vb(38,"input",18),s.Jc(39,$,2,1,"div",14),s.Zb(),s.ac(40,"div",12),s.ac(41,"label"),s.Lc(42,"Password"),s.Zb(),s.Vb(43,"input",19),s.Jc(44,V,3,2,"div",14),s.Zb(),s.ac(45,"div",12),s.ac(46,"label"),s.Lc(47,"Repeat Password"),s.Zb(),s.Vb(48,"input",20),s.Jc(49,J,3,2,"div",14),s.Zb(),s.ac(50,"div",21),s.ac(51,"button",22),s.Jc(52,B,1,0,"span",23),s.Lc(53," Register "),s.Zb(),s.Zb(),s.ac(54,"div",24),s.ac(55,"p"),s.Lc(56,"Already have an account? "),s.ac(57,"a",25),s.Lc(58,"Login"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&t&&(s.Ib(14),s.pc("formGroup",e.form),s.Ib(4),s.pc("ngClass",s.tc(17,D,e.submitted&&e.f.username.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.username.errors),s.Ib(4),s.pc("ngClass",s.tc(19,D,e.submitted&&e.f.email.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.email.errors),s.Ib(4),s.pc("ngClass",s.tc(21,D,e.submitted&&e.f.firstName.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.firstName.errors),s.Ib(4),s.pc("ngClass",s.tc(23,D,e.submitted&&e.f.lastName.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.lastName.errors),s.Ib(4),s.pc("ngClass",s.tc(25,D,e.submitted&&e.f.phone.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.phone.errors),s.Ib(4),s.pc("ngClass",s.tc(27,D,e.submitted&&e.f.password.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.password.errors),s.Ib(4),s.pc("ngClass",s.tc(29,D,e.submitted&&e.f.confirmPassword.errors)),s.Ib(1),s.pc("ngIf",e.submitted&&e.f.confirmPassword.errors),s.Ib(2),s.pc("disabled",e.loading),s.Ib(1),s.pc("ngIf",e.loading))},directives:[o.e,n.x,n.p,n.h,n.b,n.o,n.f,i.k,i.m,n.t],styles:[""]}),t})()},{path:"otp",component:I},{path:"lockscreen",component:a}];let F=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.Sb({type:t}),t.\u0275inj=s.Rb({imports:[[o.f.forChild(T)],o.f]}),t})();var H=r("tk/3");let z=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.Sb({type:t}),t.\u0275inj=s.Rb({imports:[[i.c,F,n.j,n.u,H.d]]}),t})()},mrSG:function(t,e,r){"use strict";function i(t,e,r,i){var n,o=arguments.length,s=o<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,i);else for(var c=t.length-1;c>=0;c--)(n=t[c])&&(s=(o<3?n(s):o>3?n(e,r,s):n(e,r))||s);return o>3&&s&&Object.defineProperty(e,r,s),s}function n(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function o(t,e,r,i){return new(r||(r=Promise))(function(n,o){function s(t){try{a(i.next(t))}catch(e){o(e)}}function c(t){try{a(i.throw(t))}catch(e){o(e)}}function a(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r(function(t){t(e)})).then(s,c)}a((i=i.apply(t,e||[])).next())})}r.d(e,"b",function(){return i}),r.d(e,"c",function(){return n}),r.d(e,"a",function(){return o})},"un/a":function(t,e,r){"use strict";r.d(e,"a",function(){return n});var i=r("7o/Q");function n(t=-1){return e=>e.lift(new o(t,e))}class o{constructor(t,e){this.count=t,this.source=e}call(t,e){return e.subscribe(new s(t,this.count,this.source))}}class s extends i.a{constructor(t,e,r){super(t),this.count=e,this.source=r}error(t){if(!this.isStopped){const{source:e,count:r}=this;if(0===r)return super.error(t);r>-1&&(this.count=r-1),e.subscribe(this._unsubscribeAndRecycle())}}}}}]);