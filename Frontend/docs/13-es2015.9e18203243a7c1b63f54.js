(window.webpackJsonp=window.webpackJsonp||[]).push([[13],{"/uUt":function(e,t,i){"use strict";i.d(t,"a",function(){return a});var n=i("7o/Q");function a(e,t){return i=>i.lift(new o(e,t))}class o{constructor(e,t){this.compare=e,this.keySelector=t}call(e,t){return t.subscribe(new r(e,this.compare,this.keySelector))}}class r extends n.a{constructor(e,t,i){super(e),this.keySelector=i,this.hasKey=!1,"function"==typeof t&&(this.compare=t)}compare(e,t){return e===t}_next(e){let t;try{const{keySelector:i}=this;t=i?i(e):e}catch(n){return this.destination.error(n)}let i=!1;if(this.hasKey)try{const{compare:e}=this;i=e(this.key,t)}catch(n){return this.destination.error(n)}else this.hasKey=!0;i||(this.key=t,this.destination.next(e))}}},"1G5W":function(e,t,i){"use strict";i.d(t,"a",function(){return o});var n=i("l7GE"),a=i("ZUHj");function o(e){return t=>t.lift(new r(e))}class r{constructor(e){this.notifier=e}call(e,t){const i=new s(e),n=Object(a.a)(i,this.notifier);return n&&!i.seenValue?(i.add(n),t.subscribe(i)):i}}class s extends n.a{constructor(e){super(e),this.seenValue=!1}notifyNext(e,t,i,n,a){this.seenValue=!0,this.complete()}notifyComplete(){}}},"3UWI":function(e,t,i){"use strict";i.d(t,"a",function(){return c});var n=i("D0XW"),a=i("l7GE"),o=i("ZUHj");class r{constructor(e){this.durationSelector=e}call(e,t){return t.subscribe(new s(e,this.durationSelector))}}class s extends a.a{constructor(e,t){super(e),this.durationSelector=t,this.hasValue=!1}_next(e){if(this.value=e,this.hasValue=!0,!this.throttled){let i;try{const{durationSelector:t}=this;i=t(e)}catch(t){return this.destination.error(t)}const n=Object(o.a)(this,i);!n||n.closed?this.clearThrottle():this.add(this.throttled=n)}}clearThrottle(){const{value:e,hasValue:t,throttled:i}=this;i&&(this.remove(i),this.throttled=null,i.unsubscribe()),t&&(this.value=null,this.hasValue=!1,this.destination.next(e))}notifyNext(e,t,i,n){this.clearThrottle()}notifyComplete(){this.clearThrottle()}}var l=i("PqYM");function c(e,t=n.a){return i=()=>Object(l.a)(e,t),function(e){return e.lift(new r(i))};var i}},"6Qlo":function(e,t,i){"use strict";i.d(t,"a",function(){return s});var n=i("d//k"),a=i("AytR"),o=i("fXoL"),r=i("tk/3");let s=(()=>{class e{constructor(e,t){this.http=e,this.loginService=t,this.baseUrl=a.a.baseUrl}getDataFromJson(e){return this.http.get(`assets/json/${e}.json`)}sendGetRequestForMenusAuth(){return console.log("sendGetRequestForMenusAuth"),this.http.get(`${this.baseUrl}/menusAuth`)}}return e.\u0275fac=function(t){return new(t||e)(o.ec(r.c),o.ec(n.a))},e.\u0275prov=o.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},HYfV:function(e,t,i){"use strict";i.r(t),i.d(t,"AllModulesModule",function(){return lt});var n=i("ofXK"),a=i("3Pt+"),o=i("tk/3"),r=i("tyNb"),s=i("3owW"),l=i("fXoL"),c=i("AytR"),p=i("IhMt"),d=i("cRb6"),m=i("6Qlo"),u=i("XNiG"),h=i("xgIS"),f=i("VRyK"),b=i("3UWI"),g=i("1G5W"),y=i("/uUt"),v=i("7o/Q");function w(e){return t=>t.lift(new D(e))}class D{constructor(e){this.value=e}call(e,t){return t.subscribe(new x(e,this.value))}}class x extends v.a{constructor(e,t){super(e),this.value=t}_next(e){this.destination.next(this.value)}}function C(e){return getComputedStyle(e)}function _(e,t){for(var i in t){var n=t[i];"number"==typeof n&&(n+="px"),e.style[i]=n}return e}function T(e){var t=document.createElement("div");return t.className=e,t}var L="undefined"!=typeof Element&&(Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector);function S(e,t){if(!L)throw new Error("No element matching method supported");return L.call(e,t)}function M(e){e.remove?e.remove():e.parentNode&&e.parentNode.removeChild(e)}function R(e,t){return Array.prototype.filter.call(e.children,function(e){return S(e,t)})}var k=function(e){return"ps__thumb-"+e},A=function(e){return"ps__rail-"+e},I=function(e){return"ps--active-"+e},j=function(e){return"ps--scrolling-"+e},W={x:null,y:null};function P(e,t){var i=e.element.classList,n=j(t);i.contains(n)?clearTimeout(W[t]):i.add(n)}function U(e,t){W[t]=setTimeout(function(){return e.isAlive&&e.element.classList.remove(j(t))},e.settings.scrollingThreshold)}var O=function(e){this.element=e,this.handlers={}},E={isEmpty:{configurable:!0}};O.prototype.bind=function(e,t){void 0===this.handlers[e]&&(this.handlers[e]=[]),this.handlers[e].push(t),this.element.addEventListener(e,t,!1)},O.prototype.unbind=function(e,t){var i=this;this.handlers[e]=this.handlers[e].filter(function(n){return!(!t||n===t)||(i.element.removeEventListener(e,n,!1),!1)})},O.prototype.unbindAll=function(){for(var e in this.handlers)this.unbind(e)},E.isEmpty.get=function(){var e=this;return Object.keys(this.handlers).every(function(t){return 0===e.handlers[t].length})},Object.defineProperties(O.prototype,E);var N=function(){this.eventElements=[]};function F(e){if("function"==typeof window.CustomEvent)return new CustomEvent(e);var t=document.createEvent("CustomEvent");return t.initCustomEvent(e,!1,!1,void 0),t}function q(e,t,i,n,a){var o;if(void 0===n&&(n=!0),void 0===a&&(a=!1),"top"===t)o=["contentHeight","containerHeight","scrollTop","y","up","down"];else{if("left"!==t)throw new Error("A proper axis should be provided");o=["contentWidth","containerWidth","scrollLeft","x","left","right"]}!function(e,t,i,n,a){var o=i[0],r=i[1],s=i[2],l=i[3],c=i[4],p=i[5];void 0===n&&(n=!0),void 0===a&&(a=!1);var d=e.element;e.reach[l]=null,d[s]<1&&(e.reach[l]="start"),d[s]>e[o]-e[r]-1&&(e.reach[l]="end"),t&&(d.dispatchEvent(F("ps-scroll-"+l)),t<0?d.dispatchEvent(F("ps-scroll-"+c)):t>0&&d.dispatchEvent(F("ps-scroll-"+p)),n&&function(e,t){P(e,t),U(e,t)}(e,l)),e.reach[l]&&(t||a)&&d.dispatchEvent(F("ps-"+l+"-reach-"+e.reach[l]))}(e,i,o,n,a)}function Z(e){return parseInt(e,10)||0}N.prototype.eventElement=function(e){var t=this.eventElements.filter(function(t){return t.element===e})[0];return t||(t=new O(e),this.eventElements.push(t)),t},N.prototype.bind=function(e,t,i){this.eventElement(e).bind(t,i)},N.prototype.unbind=function(e,t,i){var n=this.eventElement(e);n.unbind(t,i),n.isEmpty&&this.eventElements.splice(this.eventElements.indexOf(n),1)},N.prototype.unbindAll=function(){this.eventElements.forEach(function(e){return e.unbindAll()}),this.eventElements=[]},N.prototype.once=function(e,t,i){var n=this.eventElement(e),a=function(e){n.unbind(t,a),i(e)};n.bind(t,a)};var J={isWebKit:"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style,supportsTouch:"undefined"!=typeof window&&("ontouchstart"in window||"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>0||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:"undefined"!=typeof navigator&&navigator.msMaxTouchPoints,isChrome:"undefined"!=typeof navigator&&/Chrome/i.test(navigator&&navigator.userAgent)};function B(e){var t=e.element,i=Math.floor(t.scrollTop),n=t.getBoundingClientRect();e.containerWidth=Math.ceil(n.width),e.containerHeight=Math.ceil(n.height),e.contentWidth=t.scrollWidth,e.contentHeight=t.scrollHeight,t.contains(e.scrollbarXRail)||(R(t,A("x")).forEach(function(e){return M(e)}),t.appendChild(e.scrollbarXRail)),t.contains(e.scrollbarYRail)||(R(t,A("y")).forEach(function(e){return M(e)}),t.appendChild(e.scrollbarYRail)),!e.settings.suppressScrollX&&e.containerWidth+e.settings.scrollXMarginOffset<e.contentWidth?(e.scrollbarXActive=!0,e.railXWidth=e.containerWidth-e.railXMarginWidth,e.railXRatio=e.containerWidth/e.railXWidth,e.scrollbarXWidth=Y(e,Z(e.railXWidth*e.containerWidth/e.contentWidth)),e.scrollbarXLeft=Z((e.negativeScrollAdjustment+t.scrollLeft)*(e.railXWidth-e.scrollbarXWidth)/(e.contentWidth-e.containerWidth))):e.scrollbarXActive=!1,!e.settings.suppressScrollY&&e.containerHeight+e.settings.scrollYMarginOffset<e.contentHeight?(e.scrollbarYActive=!0,e.railYHeight=e.containerHeight-e.railYMarginHeight,e.railYRatio=e.containerHeight/e.railYHeight,e.scrollbarYHeight=Y(e,Z(e.railYHeight*e.containerHeight/e.contentHeight)),e.scrollbarYTop=Z(i*(e.railYHeight-e.scrollbarYHeight)/(e.contentHeight-e.containerHeight))):e.scrollbarYActive=!1,e.scrollbarXLeft>=e.railXWidth-e.scrollbarXWidth&&(e.scrollbarXLeft=e.railXWidth-e.scrollbarXWidth),e.scrollbarYTop>=e.railYHeight-e.scrollbarYHeight&&(e.scrollbarYTop=e.railYHeight-e.scrollbarYHeight),function(e,t){var i={width:t.railXWidth},n=Math.floor(e.scrollTop);i.left=t.isRtl?t.negativeScrollAdjustment+e.scrollLeft+t.containerWidth-t.contentWidth:e.scrollLeft,t.isScrollbarXUsingBottom?i.bottom=t.scrollbarXBottom-n:i.top=t.scrollbarXTop+n,_(t.scrollbarXRail,i);var a={top:n,height:t.railYHeight};t.isScrollbarYUsingRight?a.right=t.isRtl?t.contentWidth-(t.negativeScrollAdjustment+e.scrollLeft)-t.scrollbarYRight-t.scrollbarYOuterWidth-9:t.scrollbarYRight-e.scrollLeft:a.left=t.isRtl?t.negativeScrollAdjustment+e.scrollLeft+2*t.containerWidth-t.contentWidth-t.scrollbarYLeft-t.scrollbarYOuterWidth:t.scrollbarYLeft+e.scrollLeft,_(t.scrollbarYRail,a),_(t.scrollbarX,{left:t.scrollbarXLeft,width:t.scrollbarXWidth-t.railBorderXWidth}),_(t.scrollbarY,{top:t.scrollbarYTop,height:t.scrollbarYHeight-t.railBorderYWidth})}(t,e),e.scrollbarXActive?t.classList.add(I("x")):(t.classList.remove(I("x")),e.scrollbarXWidth=0,e.scrollbarXLeft=0,t.scrollLeft=!0===e.isRtl?e.contentWidth:0),e.scrollbarYActive?t.classList.add(I("y")):(t.classList.remove(I("y")),e.scrollbarYHeight=0,e.scrollbarYTop=0,t.scrollTop=0)}function Y(e,t){return e.settings.minScrollbarLength&&(t=Math.max(t,e.settings.minScrollbarLength)),e.settings.maxScrollbarLength&&(t=Math.min(t,e.settings.maxScrollbarLength)),t}function X(e,t){var i=t[0],n=t[1],a=t[2],o=t[3],r=t[4],s=t[5],l=t[6],c=t[7],p=t[8],d=e.element,m=null,u=null,h=null;function f(t){t.touches&&t.touches[0]&&(t[a]=t.touches[0].pageY),d[l]=m+h*(t[a]-u),P(e,c),B(e),t.stopPropagation(),t.preventDefault()}function b(){U(e,c),e[p].classList.remove("ps--clicking"),e.event.unbind(e.ownerDocument,"mousemove",f)}function g(t,r){m=d[l],r&&t.touches&&(t[a]=t.touches[0].pageY),u=t[a],h=(e[n]-e[i])/(e[o]-e[s]),r?e.event.bind(e.ownerDocument,"touchmove",f):(e.event.bind(e.ownerDocument,"mousemove",f),e.event.once(e.ownerDocument,"mouseup",b),t.preventDefault()),e[p].classList.add("ps--clicking"),t.stopPropagation()}e.event.bind(e[r],"mousedown",function(e){g(e)}),e.event.bind(e[r],"touchstart",function(e){g(e,!0)})}var H={"click-rail":function(e){e.event.bind(e.scrollbarY,"mousedown",function(e){return e.stopPropagation()}),e.event.bind(e.scrollbarYRail,"mousedown",function(t){var i=t.pageY-window.pageYOffset-e.scrollbarYRail.getBoundingClientRect().top;e.element.scrollTop+=(i>e.scrollbarYTop?1:-1)*e.containerHeight,B(e),t.stopPropagation()}),e.event.bind(e.scrollbarX,"mousedown",function(e){return e.stopPropagation()}),e.event.bind(e.scrollbarXRail,"mousedown",function(t){var i=t.pageX-window.pageXOffset-e.scrollbarXRail.getBoundingClientRect().left;e.element.scrollLeft+=(i>e.scrollbarXLeft?1:-1)*e.containerWidth,B(e),t.stopPropagation()})},"drag-thumb":function(e){X(e,["containerWidth","contentWidth","pageX","railXWidth","scrollbarX","scrollbarXWidth","scrollLeft","x","scrollbarXRail"]),X(e,["containerHeight","contentHeight","pageY","railYHeight","scrollbarY","scrollbarYHeight","scrollTop","y","scrollbarYRail"])},keyboard:function(e){var t=e.element;e.event.bind(e.ownerDocument,"keydown",function(i){if(!(i.isDefaultPrevented&&i.isDefaultPrevented()||i.defaultPrevented)&&(S(t,":hover")||S(e.scrollbarX,":focus")||S(e.scrollbarY,":focus"))){var n,a=document.activeElement?document.activeElement:e.ownerDocument.activeElement;if(a){if("IFRAME"===a.tagName)a=a.contentDocument.activeElement;else for(;a.shadowRoot;)a=a.shadowRoot.activeElement;if(S(n=a,"input,[contenteditable]")||S(n,"select,[contenteditable]")||S(n,"textarea,[contenteditable]")||S(n,"button,[contenteditable]"))return}var o=0,r=0;switch(i.which){case 37:o=i.metaKey?-e.contentWidth:i.altKey?-e.containerWidth:-30;break;case 38:r=i.metaKey?e.contentHeight:i.altKey?e.containerHeight:30;break;case 39:o=i.metaKey?e.contentWidth:i.altKey?e.containerWidth:30;break;case 40:r=i.metaKey?-e.contentHeight:i.altKey?-e.containerHeight:-30;break;case 32:r=i.shiftKey?e.containerHeight:-e.containerHeight;break;case 33:r=e.containerHeight;break;case 34:r=-e.containerHeight;break;case 36:r=e.contentHeight;break;case 35:r=-e.contentHeight;break;default:return}e.settings.suppressScrollX&&0!==o||e.settings.suppressScrollY&&0!==r||(t.scrollTop-=r,t.scrollLeft+=o,B(e),function(i,n){var a=Math.floor(t.scrollTop);if(0===i){if(!e.scrollbarYActive)return!1;if(0===a&&n>0||a>=e.contentHeight-e.containerHeight&&n<0)return!e.settings.wheelPropagation}var o=t.scrollLeft;if(0===n){if(!e.scrollbarXActive)return!1;if(0===o&&i<0||o>=e.contentWidth-e.containerWidth&&i>0)return!e.settings.wheelPropagation}return!0}(o,r)&&i.preventDefault())}})},wheel:function(e){var t=e.element;function i(i){var n=function(e){var t=e.deltaX,i=-1*e.deltaY;return void 0!==t&&void 0!==i||(t=-1*e.wheelDeltaX/6,i=e.wheelDeltaY/6),e.deltaMode&&1===e.deltaMode&&(t*=10,i*=10),t!=t&&i!=i&&(t=0,i=e.wheelDelta),e.shiftKey?[-i,-t]:[t,i]}(i),a=n[0],o=n[1];if(!function(e,i,n){if(!J.isWebKit&&t.querySelector("select:focus"))return!0;if(!t.contains(e))return!1;for(var a=e;a&&a!==t;){if(a.classList.contains("ps__child--consume"))return!0;var o=C(a);if(n&&o.overflowY.match(/(scroll|auto)/)){var r=a.scrollHeight-a.clientHeight;if(r>0&&(a.scrollTop>0&&n<0||a.scrollTop<r&&n>0))return!0}if(i&&o.overflowX.match(/(scroll|auto)/)){var s=a.scrollWidth-a.clientWidth;if(s>0&&(a.scrollLeft>0&&i<0||a.scrollLeft<s&&i>0))return!0}a=a.parentNode}return!1}(i.target,a,o)){var r=!1;e.settings.useBothWheelAxes?e.scrollbarYActive&&!e.scrollbarXActive?(o?t.scrollTop-=o*e.settings.wheelSpeed:t.scrollTop+=a*e.settings.wheelSpeed,r=!0):e.scrollbarXActive&&!e.scrollbarYActive&&(a?t.scrollLeft+=a*e.settings.wheelSpeed:t.scrollLeft-=o*e.settings.wheelSpeed,r=!0):(t.scrollTop-=o*e.settings.wheelSpeed,t.scrollLeft+=a*e.settings.wheelSpeed),B(e),(r=r||function(i,n){var a=Math.floor(t.scrollTop),o=0===t.scrollTop,r=a+t.offsetHeight===t.scrollHeight,s=0===t.scrollLeft,l=t.scrollLeft+t.offsetWidth===t.scrollWidth;return!(Math.abs(n)>Math.abs(i)?o||r:s||l)||!e.settings.wheelPropagation}(a,o))&&!i.ctrlKey&&(i.stopPropagation(),i.preventDefault())}}void 0!==window.onwheel?e.event.bind(t,"wheel",i):void 0!==window.onmousewheel&&e.event.bind(t,"mousewheel",i)},touch:function(e){if(J.supportsTouch||J.supportsIePointer){var t=e.element,i={},n=0,a={},o=null;J.supportsTouch?(e.event.bind(t,"touchstart",c),e.event.bind(t,"touchmove",p),e.event.bind(t,"touchend",d)):J.supportsIePointer&&(window.PointerEvent?(e.event.bind(t,"pointerdown",c),e.event.bind(t,"pointermove",p),e.event.bind(t,"pointerup",d)):window.MSPointerEvent&&(e.event.bind(t,"MSPointerDown",c),e.event.bind(t,"MSPointerMove",p),e.event.bind(t,"MSPointerUp",d)))}function r(i,n){t.scrollTop-=n,t.scrollLeft-=i,B(e)}function s(e){return e.targetTouches?e.targetTouches[0]:e}function l(e){return!(e.pointerType&&"pen"===e.pointerType&&0===e.buttons||(!e.targetTouches||1!==e.targetTouches.length)&&(!e.pointerType||"mouse"===e.pointerType||e.pointerType===e.MSPOINTER_TYPE_MOUSE))}function c(e){if(l(e)){var t=s(e);i.pageX=t.pageX,i.pageY=t.pageY,n=(new Date).getTime(),null!==o&&clearInterval(o)}}function p(o){if(l(o)){var c=s(o),p={pageX:c.pageX,pageY:c.pageY},d=p.pageX-i.pageX,m=p.pageY-i.pageY;if(function(e,i,n){if(!t.contains(e))return!1;for(var a=e;a&&a!==t;){if(a.classList.contains("ps__child--consume"))return!0;var o=C(a);if(n&&o.overflowY.match(/(scroll|auto)/)){var r=a.scrollHeight-a.clientHeight;if(r>0&&(a.scrollTop>0&&n<0||a.scrollTop<r&&n>0))return!0}if(i&&o.overflowX.match(/(scroll|auto)/)){var s=a.scrollWidth-a.clientWidth;if(s>0&&(a.scrollLeft>0&&i<0||a.scrollLeft<s&&i>0))return!0}a=a.parentNode}return!1}(o.target,d,m))return;r(d,m),i=p;var u=(new Date).getTime(),h=u-n;h>0&&(a.x=d/h,a.y=m/h,n=u),function(i,n){var a=Math.floor(t.scrollTop),o=t.scrollLeft,r=Math.abs(i),s=Math.abs(n);if(s>r){if(n<0&&a===e.contentHeight-e.containerHeight||n>0&&0===a)return 0===window.scrollY&&n>0&&J.isChrome}else if(r>s&&(i<0&&o===e.contentWidth-e.containerWidth||i>0&&0===o))return!0;return!0}(d,m)&&o.preventDefault()}}function d(){e.settings.swipeEasing&&(clearInterval(o),o=setInterval(function(){e.isInitialized?clearInterval(o):a.x||a.y?Math.abs(a.x)<.01&&Math.abs(a.y)<.01?clearInterval(o):(r(30*a.x,30*a.y),a.x*=.8,a.y*=.8):clearInterval(o)},10))}}},V=function(e,t){var i=this;if(void 0===t&&(t={}),"string"==typeof e&&(e=document.querySelector(e)),!e||!e.nodeName)throw new Error("no element is specified to initialize PerfectScrollbar");for(var n in this.element=e,e.classList.add("ps"),this.settings={handlers:["click-rail","drag-thumb","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollingThreshold:1e3,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!0,wheelSpeed:1},t)this.settings[n]=t[n];this.containerWidth=null,this.containerHeight=null,this.contentWidth=null,this.contentHeight=null;var a,o,r=function(){return e.classList.add("ps--focus")},s=function(){return e.classList.remove("ps--focus")};this.isRtl="rtl"===C(e).direction,!0===this.isRtl&&e.classList.add("ps__rtl"),this.isNegativeScroll=(o=e.scrollLeft,e.scrollLeft=-1,a=e.scrollLeft<0,e.scrollLeft=o,a),this.negativeScrollAdjustment=this.isNegativeScroll?e.scrollWidth-e.clientWidth:0,this.event=new N,this.ownerDocument=e.ownerDocument||document,this.scrollbarXRail=T(A("x")),e.appendChild(this.scrollbarXRail),this.scrollbarX=T(k("x")),this.scrollbarXRail.appendChild(this.scrollbarX),this.scrollbarX.setAttribute("tabindex",0),this.event.bind(this.scrollbarX,"focus",r),this.event.bind(this.scrollbarX,"blur",s),this.scrollbarXActive=null,this.scrollbarXWidth=null,this.scrollbarXLeft=null;var l=C(this.scrollbarXRail);this.scrollbarXBottom=parseInt(l.bottom,10),isNaN(this.scrollbarXBottom)?(this.isScrollbarXUsingBottom=!1,this.scrollbarXTop=Z(l.top)):this.isScrollbarXUsingBottom=!0,this.railBorderXWidth=Z(l.borderLeftWidth)+Z(l.borderRightWidth),_(this.scrollbarXRail,{display:"block"}),this.railXMarginWidth=Z(l.marginLeft)+Z(l.marginRight),_(this.scrollbarXRail,{display:""}),this.railXWidth=null,this.railXRatio=null,this.scrollbarYRail=T(A("y")),e.appendChild(this.scrollbarYRail),this.scrollbarY=T(k("y")),this.scrollbarYRail.appendChild(this.scrollbarY),this.scrollbarY.setAttribute("tabindex",0),this.event.bind(this.scrollbarY,"focus",r),this.event.bind(this.scrollbarY,"blur",s),this.scrollbarYActive=null,this.scrollbarYHeight=null,this.scrollbarYTop=null;var c=C(this.scrollbarYRail);this.scrollbarYRight=parseInt(c.right,10),isNaN(this.scrollbarYRight)?(this.isScrollbarYUsingRight=!1,this.scrollbarYLeft=Z(c.left)):this.isScrollbarYUsingRight=!0,this.scrollbarYOuterWidth=this.isRtl?function(e){var t=C(e);return Z(t.width)+Z(t.paddingLeft)+Z(t.paddingRight)+Z(t.borderLeftWidth)+Z(t.borderRightWidth)}(this.scrollbarY):null,this.railBorderYWidth=Z(c.borderTopWidth)+Z(c.borderBottomWidth),_(this.scrollbarYRail,{display:"block"}),this.railYMarginHeight=Z(c.marginTop)+Z(c.marginBottom),_(this.scrollbarYRail,{display:""}),this.railYHeight=null,this.railYRatio=null,this.reach={x:e.scrollLeft<=0?"start":e.scrollLeft>=this.contentWidth-this.containerWidth?"end":null,y:e.scrollTop<=0?"start":e.scrollTop>=this.contentHeight-this.containerHeight?"end":null},this.isAlive=!0,this.settings.handlers.forEach(function(e){return H[e](i)}),this.lastScrollTop=Math.floor(e.scrollTop),this.lastScrollLeft=e.scrollLeft,this.event.bind(this.element,"scroll",function(e){return i.onScroll(e)}),B(this)};V.prototype.update=function(){this.isAlive&&(this.negativeScrollAdjustment=this.isNegativeScroll?this.element.scrollWidth-this.element.clientWidth:0,_(this.scrollbarXRail,{display:"block"}),_(this.scrollbarYRail,{display:"block"}),this.railXMarginWidth=Z(C(this.scrollbarXRail).marginLeft)+Z(C(this.scrollbarXRail).marginRight),this.railYMarginHeight=Z(C(this.scrollbarYRail).marginTop)+Z(C(this.scrollbarYRail).marginBottom),_(this.scrollbarXRail,{display:"none"}),_(this.scrollbarYRail,{display:"none"}),B(this),q(this,"top",0,!1,!0),q(this,"left",0,!1,!0),_(this.scrollbarXRail,{display:""}),_(this.scrollbarYRail,{display:""}))},V.prototype.onScroll=function(e){this.isAlive&&(B(this),q(this,"top",this.element.scrollTop-this.lastScrollTop),q(this,"left",this.element.scrollLeft-this.lastScrollLeft),this.lastScrollTop=Math.floor(this.element.scrollTop),this.lastScrollLeft=this.element.scrollLeft)},V.prototype.destroy=function(){this.isAlive&&(this.event.unbindAll(),M(this.scrollbarX),M(this.scrollbarY),M(this.scrollbarXRail),M(this.scrollbarYRail),this.removePsClasses(),this.element=null,this.scrollbarX=null,this.scrollbarY=null,this.scrollbarXRail=null,this.scrollbarYRail=null,this.isAlive=!1)},V.prototype.removePsClasses=function(){this.element.className=this.element.className.split(" ").filter(function(e){return!e.match(/^ps([-_].+|)$/)}).join(" ")};var G=V,z=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var i=-1;return e.some(function(e,n){return e[0]===t&&(i=n,!0)}),i}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var i=e(this.__entries__,t),n=this.__entries__[i];return n&&n[1]},t.prototype.set=function(t,i){var n=e(this.__entries__,t);~n?this.__entries__[n][1]=i:this.__entries__.push([t,i])},t.prototype.delete=function(t){var i=this.__entries__,n=e(i,t);~n&&i.splice(n,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var i=0,n=this.__entries__;i<n.length;i++){var a=n[i];e.call(t,a[1],a[0])}},t}()}(),K="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,Q="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),ee="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(Q):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)},te=["top","right","bottom","left","width","height","size","weight"],ie="undefined"!=typeof MutationObserver,ne=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var i=!1,n=!1,a=0;function o(){i&&(i=!1,e()),n&&s()}function r(){ee(o)}function s(){var e=Date.now();if(i){if(e-a<2)return;n=!0}else i=!0,n=!1,setTimeout(r,20);a=e}return s}(this.refresh.bind(this))}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,i=t.indexOf(e);~i&&t.splice(i,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter(function(e){return e.gatherActive(),e.hasActive()});return e.forEach(function(e){return e.broadcastActive()}),e.length>0},e.prototype.connect_=function(){K&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),ie?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){K&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,i=void 0===t?"":t;te.some(function(e){return!!~i.indexOf(e)})&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),ae=function(e,t){for(var i=0,n=Object.keys(t);i<n.length;i++){var a=n[i];Object.defineProperty(e,a,{value:t[a],enumerable:!1,writable:!1,configurable:!0})}return e},oe=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||Q},re=pe(0,0,0,0);function se(e){return parseFloat(e)||0}function le(e){for(var t=[],i=1;i<arguments.length;i++)t[i-1]=arguments[i];return t.reduce(function(t,i){return t+se(e["border-"+i+"-width"])},0)}var ce="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof oe(e).SVGGraphicsElement}:function(e){return e instanceof oe(e).SVGElement&&"function"==typeof e.getBBox};function pe(e,t,i,n){return{x:e,y:t,width:i,height:n}}var de=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=pe(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e,t=(e=this.target,K?ce(e)?function(e){var t=e.getBBox();return pe(0,0,t.width,t.height)}(e):function(e){var t=e.clientWidth,i=e.clientHeight;if(!t&&!i)return re;var n=oe(e).getComputedStyle(e),a=function(e){for(var t={},i=0,n=["top","right","bottom","left"];i<n.length;i++){var a=n[i];t[a]=se(e["padding-"+a])}return t}(n),o=a.left+a.right,r=a.top+a.bottom,s=se(n.width),l=se(n.height);if("border-box"===n.boxSizing&&(Math.round(s+o)!==t&&(s-=le(n,"left","right")+o),Math.round(l+r)!==i&&(l-=le(n,"top","bottom")+r)),!function(e){return e===oe(e).document.documentElement}(e)){var c=Math.round(s+o)-t,p=Math.round(l+r)-i;1!==Math.abs(c)&&(s-=c),1!==Math.abs(p)&&(l-=p)}return pe(a.left,a.top,s,l)}(e):re);return this.contentRect_=t,t.width!==this.broadcastWidth||t.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),me=function(e,t){var i,n,a,o,r,s,l,c=(n=(i=t).x,a=i.y,o=i.width,r=i.height,s="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,l=Object.create(s.prototype),ae(l,{x:n,y:a,width:o,height:r,top:a,right:n+o,bottom:r+a,left:n}),l);ae(this,{target:e,contentRect:c})},ue=function(){function e(e,t,i){if(this.activeObservations_=[],this.observations_=new z,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=i}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof oe(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new de(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof oe(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&e.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map(function(e){return new me(e.target,e.broadcastRect())});this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),he="undefined"!=typeof WeakMap?new WeakMap:new z,fe=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var i=ne.getInstance(),n=new ue(t,i,this);he.set(this,n)};["observe","unobserve","disconnect"].forEach(function(e){fe.prototype[e]=function(){var t;return(t=he.get(this))[e].apply(t,arguments)}});var be=void 0!==Q.ResizeObserver?Q.ResizeObserver:fe;function ge(e,t){if(1&e&&(l.ac(0,"div",3),l.Vb(1,"div",4),l.Vb(2,"div",5),l.Vb(3,"div",6),l.Vb(4,"div",7),l.Zb()),2&e){var i=l.jc();l.Mb("ps-at-top",i.states.top)("ps-at-left",i.states.left)("ps-at-right",i.states.right)("ps-at-bottom",i.states.bottom),l.Ib(1),l.Mb("ps-indicator-show",i.indicatorY&&i.interaction),l.Ib(1),l.Mb("ps-indicator-show",i.indicatorX&&i.interaction),l.Ib(1),l.Mb("ps-indicator-show",i.indicatorX&&i.interaction),l.Ib(1),l.Mb("ps-indicator-show",i.indicatorY&&i.interaction)}}var ye=["*"],ve=new l.v("PERFECT_SCROLLBAR_CONFIG"),we=function(){return function(e,t,i,n){this.x=e,this.y=t,this.w=i,this.h=n}}(),De=function(){return function(e,t){this.x=e,this.y=t}}(),xe=["psScrollY","psScrollX","psScrollUp","psScrollDown","psScrollLeft","psScrollRight","psYReachEnd","psYReachStart","psXReachEnd","psXReachStart"],Ce=function(){function e(e){void 0===e&&(e={}),this.assign(e)}return e.prototype.assign=function(e){for(var t in void 0===e&&(e={}),e)this[t]=e[t]},e}(),_e=function(){function e(e,t,i,n,a){this.zone=e,this.differs=t,this.elementRef=i,this.platformId=n,this.defaults=a,this.instance=null,this.ro=null,this.timeout=null,this.animation=null,this.configDiff=null,this.ngDestroy=new u.a,this.disabled=!1,this.psScrollY=new l.q,this.psScrollX=new l.q,this.psScrollUp=new l.q,this.psScrollDown=new l.q,this.psScrollLeft=new l.q,this.psScrollRight=new l.q,this.psYReachEnd=new l.q,this.psYReachStart=new l.q,this.psXReachEnd=new l.q,this.psXReachStart=new l.q}return e.prototype.ngOnInit=function(){var e=this;if(!this.disabled&&Object(n.v)(this.platformId)){var t=new Ce(this.defaults);t.assign(this.config),this.zone.runOutsideAngular(function(){e.instance=new G(e.elementRef.nativeElement,t)}),this.configDiff||(this.configDiff=this.differs.find(this.config||{}).create(),this.configDiff.diff(this.config||{})),this.zone.runOutsideAngular(function(){e.ro=new be(function(){e.update()}),e.elementRef.nativeElement.children[0]&&e.ro.observe(e.elementRef.nativeElement.children[0]),e.ro.observe(e.elementRef.nativeElement)}),this.zone.runOutsideAngular(function(){xe.forEach(function(t){var i=t.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()});Object(h.a)(e.elementRef.nativeElement,i).pipe(Object(b.a)(20),Object(g.a)(e.ngDestroy)).subscribe(function(i){e[t].emit(i)})})})}},e.prototype.ngOnDestroy=function(){var e=this;Object(n.v)(this.platformId)&&(this.ngDestroy.next(),this.ngDestroy.complete(),this.ro&&this.ro.disconnect(),this.timeout&&"undefined"!=typeof window&&window.clearTimeout(this.timeout),this.zone.runOutsideAngular(function(){e.instance&&e.instance.destroy()}),this.instance=null)},e.prototype.ngDoCheck=function(){!this.disabled&&this.configDiff&&Object(n.v)(this.platformId)&&this.configDiff.diff(this.config||{})&&(this.ngOnDestroy(),this.ngOnInit())},e.prototype.ngOnChanges=function(e){e.disabled&&!e.disabled.isFirstChange()&&Object(n.v)(this.platformId)&&e.disabled.currentValue!==e.disabled.previousValue&&(!0===e.disabled.currentValue?this.ngOnDestroy():!1===e.disabled.currentValue&&this.ngOnInit())},e.prototype.ps=function(){return this.instance},e.prototype.update=function(){var e=this;"undefined"!=typeof window&&(this.timeout&&window.clearTimeout(this.timeout),this.timeout=window.setTimeout(function(){if(!e.disabled&&e.configDiff)try{e.zone.runOutsideAngular(function(){e.instance&&e.instance.update()})}catch(t){}},0))},e.prototype.geometry=function(e){return void 0===e&&(e="scroll"),new we(this.elementRef.nativeElement[e+"Left"],this.elementRef.nativeElement[e+"Top"],this.elementRef.nativeElement[e+"Width"],this.elementRef.nativeElement[e+"Height"])},e.prototype.position=function(e){return void 0===e&&(e=!1),!e&&this.instance?new De(this.instance.reach.x||0,this.instance.reach.y||0):new De(this.elementRef.nativeElement.scrollLeft,this.elementRef.nativeElement.scrollTop)},e.prototype.scrollable=function(e){void 0===e&&(e="any");var t=this.elementRef.nativeElement;return"any"===e?t.classList.contains("ps--active-x")||t.classList.contains("ps--active-y"):"both"===e?t.classList.contains("ps--active-x")&&t.classList.contains("ps--active-y"):t.classList.contains("ps--active-"+e)},e.prototype.scrollTo=function(e,t,i){this.disabled||(null==t&&null==i?this.animateScrolling("scrollTop",e,i):(null!=e&&this.animateScrolling("scrollLeft",e,i),null!=t&&this.animateScrolling("scrollTop",t,i)))},e.prototype.scrollToX=function(e,t){this.animateScrolling("scrollLeft",e,t)},e.prototype.scrollToY=function(e,t){this.animateScrolling("scrollTop",e,t)},e.prototype.scrollToTop=function(e,t){this.animateScrolling("scrollTop",e||0,t)},e.prototype.scrollToLeft=function(e,t){this.animateScrolling("scrollLeft",e||0,t)},e.prototype.scrollToRight=function(e,t){this.animateScrolling("scrollLeft",this.elementRef.nativeElement.scrollWidth-this.elementRef.nativeElement.clientWidth-(e||0),t)},e.prototype.scrollToBottom=function(e,t){this.animateScrolling("scrollTop",this.elementRef.nativeElement.scrollHeight-this.elementRef.nativeElement.clientHeight-(e||0),t)},e.prototype.scrollToElement=function(e,t,i){var n=this.elementRef.nativeElement.querySelector(e);if(n){var a=n.getBoundingClientRect(),o=this.elementRef.nativeElement.getBoundingClientRect();this.elementRef.nativeElement.classList.contains("ps--active-x")&&this.animateScrolling("scrollLeft",a.left-o.left+this.elementRef.nativeElement.scrollLeft+(t||0),i),this.elementRef.nativeElement.classList.contains("ps--active-y")&&this.animateScrolling("scrollTop",a.top-o.top+this.elementRef.nativeElement.scrollTop+(t||0),i)}},e.prototype.animateScrolling=function(e,t,i){var n=this;if(this.animation&&(window.cancelAnimationFrame(this.animation),this.animation=null),i&&"undefined"!=typeof window){if(t!==this.elementRef.nativeElement[e]){var a=0,o=0,r=performance.now(),s=this.elementRef.nativeElement[e],l=(s-t)/2,c=function(p){o+=Math.PI/(i/(p-r)),a=Math.round(t+l+l*Math.cos(o)),n.elementRef.nativeElement[e]===s&&(o>=Math.PI?n.animateScrolling(e,t,0):(n.elementRef.nativeElement[e]=a,s=n.elementRef.nativeElement[e],r=p,n.animation=window.requestAnimationFrame(c)))};window.requestAnimationFrame(c)}}else this.elementRef.nativeElement[e]=t},e.\u0275fac=function(t){return new(t||e)(l.Ub(l.G),l.Ub(l.z),l.Ub(l.o),l.Ub(l.J),l.Ub(ve,8))},e.\u0275dir=l.Pb({type:e,selectors:[["","perfectScrollbar",""]],inputs:{disabled:"disabled",config:["perfectScrollbar","config"]},outputs:{psScrollY:"psScrollY",psScrollX:"psScrollX",psScrollUp:"psScrollUp",psScrollDown:"psScrollDown",psScrollLeft:"psScrollLeft",psScrollRight:"psScrollRight",psYReachEnd:"psYReachEnd",psYReachStart:"psYReachStart",psXReachEnd:"psXReachEnd",psXReachStart:"psXReachStart"},exportAs:["ngxPerfectScrollbar"],features:[l.Gb]}),e}(),Te=function(){function e(e,t,i){this.zone=e,this.cdRef=t,this.platformId=i,this.states={},this.indicatorX=!1,this.indicatorY=!1,this.interaction=!1,this.scrollPositionX=0,this.scrollPositionY=0,this.scrollDirectionX=0,this.scrollDirectionY=0,this.usePropagationX=!1,this.usePropagationY=!1,this.allowPropagationX=!1,this.allowPropagationY=!1,this.stateTimeout=null,this.ngDestroy=new u.a,this.stateUpdate=new u.a,this.disabled=!1,this.usePSClass=!0,this.autoPropagation=!1,this.scrollIndicators=!1,this.psScrollY=new l.q,this.psScrollX=new l.q,this.psScrollUp=new l.q,this.psScrollDown=new l.q,this.psScrollLeft=new l.q,this.psScrollRight=new l.q,this.psYReachEnd=new l.q,this.psYReachStart=new l.q,this.psXReachEnd=new l.q,this.psXReachStart=new l.q}return e.prototype.ngOnInit=function(){var e=this;Object(n.v)(this.platformId)&&(this.stateUpdate.pipe(Object(g.a)(this.ngDestroy),Object(y.a)(function(t,i){return t===i&&!e.stateTimeout})).subscribe(function(t){e.stateTimeout&&"undefined"!=typeof window&&(window.clearTimeout(e.stateTimeout),e.stateTimeout=null),"x"===t||"y"===t?(e.interaction=!1,"x"===t?(e.indicatorX=!1,e.states.left=!1,e.states.right=!1,e.autoPropagation&&e.usePropagationX&&(e.allowPropagationX=!1)):"y"===t&&(e.indicatorY=!1,e.states.top=!1,e.states.bottom=!1,e.autoPropagation&&e.usePropagationY&&(e.allowPropagationY=!1))):("left"===t||"right"===t?(e.states.left=!1,e.states.right=!1,e.states[t]=!0,e.autoPropagation&&e.usePropagationX&&(e.indicatorX=!0)):"top"!==t&&"bottom"!==t||(e.states.top=!1,e.states.bottom=!1,e.states[t]=!0,e.autoPropagation&&e.usePropagationY&&(e.indicatorY=!0)),e.autoPropagation&&"undefined"!=typeof window&&(e.stateTimeout=window.setTimeout(function(){e.indicatorX=!1,e.indicatorY=!1,e.stateTimeout=null,e.interaction&&(e.states.left||e.states.right)&&(e.allowPropagationX=!0),e.interaction&&(e.states.top||e.states.bottom)&&(e.allowPropagationY=!0),e.cdRef.markForCheck()},500))),e.cdRef.markForCheck(),e.cdRef.detectChanges()}),this.zone.runOutsideAngular(function(){if(e.directiveRef){var t=e.directiveRef.elementRef.nativeElement;Object(h.a)(t,"wheel").pipe(Object(g.a)(e.ngDestroy)).subscribe(function(t){!e.disabled&&e.autoPropagation&&e.checkPropagation(t,t.deltaX,t.deltaY)}),Object(h.a)(t,"touchmove").pipe(Object(g.a)(e.ngDestroy)).subscribe(function(t){if(!e.disabled&&e.autoPropagation){var i=t.touches[0].clientX,n=t.touches[0].clientY;e.checkPropagation(t,i-e.scrollPositionX,n-e.scrollPositionY),e.scrollPositionX=i,e.scrollPositionY=n}}),Object(f.a)(Object(h.a)(t,"ps-scroll-x").pipe(w("x")),Object(h.a)(t,"ps-scroll-y").pipe(w("y")),Object(h.a)(t,"ps-x-reach-end").pipe(w("right")),Object(h.a)(t,"ps-y-reach-end").pipe(w("bottom")),Object(h.a)(t,"ps-x-reach-start").pipe(w("left")),Object(h.a)(t,"ps-y-reach-start").pipe(w("top"))).pipe(Object(g.a)(e.ngDestroy)).subscribe(function(t){e.disabled||!e.autoPropagation&&!e.scrollIndicators||e.stateUpdate.next(t)})}}),window.setTimeout(function(){xe.forEach(function(t){e.directiveRef&&(e.directiveRef[t]=e[t])})},0))},e.prototype.ngOnDestroy=function(){Object(n.v)(this.platformId)&&(this.ngDestroy.next(),this.ngDestroy.unsubscribe(),this.stateTimeout&&"undefined"!=typeof window&&window.clearTimeout(this.stateTimeout))},e.prototype.ngDoCheck=function(){if(Object(n.v)(this.platformId)&&!this.disabled&&this.autoPropagation&&this.directiveRef){var e=this.directiveRef.elementRef.nativeElement;this.usePropagationX=e.classList.contains("ps--active-x"),this.usePropagationY=e.classList.contains("ps--active-y")}},e.prototype.checkPropagation=function(e,t,i){this.interaction=!0;var n=t<0?-1:1,a=i<0?-1:1;(this.usePropagationX&&this.usePropagationY||this.usePropagationX&&(!this.allowPropagationX||this.scrollDirectionX!==n)||this.usePropagationY&&(!this.allowPropagationY||this.scrollDirectionY!==a))&&(e.preventDefault(),e.stopPropagation()),t&&(this.scrollDirectionX=n),i&&(this.scrollDirectionY=a),this.stateUpdate.next("interaction"),this.cdRef.detectChanges()},e.\u0275fac=function(t){return new(t||e)(l.Ub(l.G),l.Ub(l.i),l.Ub(l.J))},e.\u0275cmp=l.Ob({type:e,selectors:[["perfect-scrollbar"]],viewQuery:function(e,t){var i;1&e&&l.Rc(_e,3),2&e&&l.yc(i=l.ic())&&(t.directiveRef=i.first)},hostVars:4,hostBindings:function(e,t){2&e&&l.Mb("ps-show-limits",t.autoPropagation)("ps-show-active",t.scrollIndicators)},inputs:{disabled:"disabled",usePSClass:"usePSClass",autoPropagation:"autoPropagation",scrollIndicators:"scrollIndicators",config:"config"},outputs:{psScrollY:"psScrollY",psScrollX:"psScrollX",psScrollUp:"psScrollUp",psScrollDown:"psScrollDown",psScrollLeft:"psScrollLeft",psScrollRight:"psScrollRight",psYReachEnd:"psYReachEnd",psYReachStart:"psYReachStart",psXReachEnd:"psXReachEnd",psXReachStart:"psXReachStart"},exportAs:["ngxPerfectScrollbar"],ngContentSelectors:ye,decls:4,vars:5,consts:[[2,"position","static",3,"perfectScrollbar","disabled"],[1,"ps-content"],["class","ps-overlay",3,"ps-at-top","ps-at-left","ps-at-right","ps-at-bottom",4,"ngIf"],[1,"ps-overlay"],[1,"ps-indicator-top"],[1,"ps-indicator-left"],[1,"ps-indicator-right"],[1,"ps-indicator-bottom"]],template:function(e,t){1&e&&(l.oc(),l.ac(0,"div",0),l.ac(1,"div",1),l.nc(2),l.Zb(),l.Jc(3,ge,5,16,"div",2),l.Zb()),2&e&&(l.Mb("ps",t.usePSClass),l.pc("perfectScrollbar",t.config)("disabled",t.disabled),l.Ib(3),l.pc("ngIf",t.scrollIndicators))},directives:[_e,n.m],styles:["/*\n  TODO: Remove important flags after this bug if fixed:\n  https://github.com/angular/flex-layout/issues/381\n*/\n\nperfect-scrollbar {\n  position: relative;\n\n  display: block;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  max-width: 100%;\n  max-height: 100%;\n}\n\nperfect-scrollbar[hidden] {\n  display: none;\n}\n\nperfect-scrollbar[fxflex] {\n  display: flex;\n  flex-direction: column;\n  height: auto;\n  min-width: 0;\n  min-height: 0;\n\n  -webkit-box-direction: column;\n  -webkit-box-orient: column;\n}\n\nperfect-scrollbar[fxflex] > .ps {\n  -ms-flex: 1 1 auto;\n\n  flex: 1 1 auto;\n  width: auto;\n  height: auto;\n  min-width: 0;\n  min-height: 0;\n\n  -webkit-box-flex: 1;\n}\n\nperfect-scrollbar[fxlayout] > .ps,\nperfect-scrollbar[fxlayout] > .ps > .ps-content {\n  display: flex;\n\n  -ms-flex: 1 1 auto;\n\n  flex: 1 1 auto;\n  flex-direction: inherit;\n  align-items: inherit;\n  align-content: inherit;\n  justify-content: inherit;\n  width: 100%;\n  height: 100%;\n\n  -webkit-box-align: inherit;\n  -webkit-box-direction: inherit;\n  -webkit-box-flex: 1;\n  -webkit-box-orient: inherit;\n  -webkit-box-pack: inherit;\n}\n\nperfect-scrollbar[fxlayout='row'] > .ps,\nperfect-scrollbar[fxlayout='row'] > .ps > .ps-content, {\n  flex-direction: row !important;\n\n  -webkit-box-direction: row !important;\n  -webkit-box-orient: row !important;\n}\n\nperfect-scrollbar[fxlayout='column'] > .ps,\nperfect-scrollbar[fxlayout='column'] > .ps > .ps-content {\n  flex-direction: column !important;\n\n  -webkit-box-direction: column !important;\n  -webkit-box-orient: column !important;\n}\n\nperfect-scrollbar > .ps {\n  position: static;\n\n  display: block;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\nperfect-scrollbar > .ps textarea {\n  -ms-overflow-style: scrollbar;\n}\n\nperfect-scrollbar > .ps > .ps-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n\n  display: block;\n  overflow: hidden;\n\n  pointer-events: none;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-top,\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-left,\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-right,\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-bottom {\n  position: absolute;\n\n  opacity: 0;\n\n  transition: opacity 300ms ease-in-out;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-top,\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-bottom {\n  left: 0;\n\n  min-width: 100%;\n  min-height: 24px;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-left,\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-right {\n  top: 0;\n\n  min-width: 24px;\n  min-height: 100%;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-top {\n  top: 0;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-left {\n  left: 0;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-right {\n  right: 0;\n}\n\nperfect-scrollbar > .ps > .ps-overlay .ps-indicator-bottom {\n  bottom: 0;\n}\n\nperfect-scrollbar > .ps.ps--active-y > .ps__rail-y {\n  top: 0 !important;\n  right: 0 !important;\n  left: auto !important;\n\n  width: 10px;\n\n  cursor: default;\n\n  transition:\n    width 200ms linear,\n    opacity 200ms linear,\n    background-color 200ms linear;\n}\n\nperfect-scrollbar > .ps.ps--active-y > .ps__rail-y:hover,\nperfect-scrollbar > .ps.ps--active-y > .ps__rail-y.ps--clicking {\n  width: 15px;\n}\n\nperfect-scrollbar > .ps.ps--active-x > .ps__rail-x {\n  top: auto !important;\n  bottom: 0 !important;\n  left: 0 !important;\n\n  height: 10px;\n\n  cursor: default;\n\n  transition:\n    height 200ms linear,\n    opacity 200ms linear,\n    background-color 200ms linear;\n}\n\nperfect-scrollbar > .ps.ps--active-x > .ps__rail-x:hover,\nperfect-scrollbar > .ps.ps--active-x > .ps__rail-x.ps--clicking {\n  height: 15px;\n}\n\nperfect-scrollbar > .ps.ps--active-x.ps--active-y > .ps__rail-y {\n  margin: 0 0 10px;\n}\n\nperfect-scrollbar > .ps.ps--active-x.ps--active-y > .ps__rail-x {\n  margin: 0 10px 0 0;\n}\n\nperfect-scrollbar > .ps.ps--scrolling-y > .ps__rail-y,\nperfect-scrollbar > .ps.ps--scrolling-x > .ps__rail-x {\n  opacity: 0.9;\n\n  background-color: #eee;\n}\n\nperfect-scrollbar.ps-show-always > .ps.ps--active-y > .ps__rail-y,\nperfect-scrollbar.ps-show-always > .ps.ps--active-x > .ps__rail-x {\n  opacity: 0.6;\n}\n\nperfect-scrollbar.ps-show-active > .ps.ps--active-y > .ps-overlay:not(.ps-at-top) .ps-indicator-top {\n  opacity: 1;\n\n  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active > .ps.ps--active-y > .ps-overlay:not(.ps-at-bottom) .ps-indicator-bottom {\n  opacity: 1;\n\n  background: linear-gradient(to top, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active > .ps.ps--active-x > .ps-overlay:not(.ps-at-left) .ps-indicator-left {\n  opacity: 1;\n\n  background: linear-gradient(to right, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active > .ps.ps--active-x > .ps-overlay:not(.ps-at-right) .ps-indicator-right {\n  opacity: 1;\n\n  background: linear-gradient(to left, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-y > .ps-overlay.ps-at-top .ps-indicator-top {\n  background: linear-gradient(to bottom, rgba(170, 170, 170, 0.5) 0%, rgba(170, 170, 170, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-y > .ps-overlay.ps-at-bottom .ps-indicator-bottom {\n  background: linear-gradient(to top, rgba(170, 170, 170, 0.5) 0%, rgba(170, 170, 170, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-x > .ps-overlay.ps-at-left .ps-indicator-left {\n  background: linear-gradient(to right, rgba(170, 170, 170, 0.5) 0%, rgba(170, 170, 170, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-x > .ps-overlay.ps-at-right .ps-indicator-right {\n  background: linear-gradient(to left, rgba(170, 170, 170, 0.5) 0%, rgba(170, 170, 170, 0) 100%);\n}\n\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-y > .ps-overlay.ps-at-top .ps-indicator-top.ps-indicator-show,\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-y > .ps-overlay.ps-at-bottom .ps-indicator-bottom.ps-indicator-show,\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-x > .ps-overlay.ps-at-left .ps-indicator-left.ps-indicator-show,\nperfect-scrollbar.ps-show-active.ps-show-limits > .ps.ps--active-x > .ps-overlay.ps-at-right .ps-indicator-right.ps-indicator-show {\n  opacity: 1;\n}\n","/*\n * Container style\n */\n.ps {\n  overflow: hidden !important;\n  overflow-anchor: none;\n  -ms-overflow-style: none;\n  touch-action: auto;\n  -ms-touch-action: auto;\n}\n\n/*\n * Scrollbar rail styles\n */\n.ps__rail-x {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  height: 15px;\n  /* there must be 'bottom' or 'top' for ps__rail-x */\n  bottom: 0px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-y {\n  display: none;\n  opacity: 0;\n  transition: background-color .2s linear, opacity .2s linear;\n  -webkit-transition: background-color .2s linear, opacity .2s linear;\n  width: 15px;\n  /* there must be 'right' or 'left' for ps__rail-y */\n  right: 0;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps--active-x > .ps__rail-x,\n.ps--active-y > .ps__rail-y {\n  display: block;\n  background-color: transparent;\n}\n\n.ps:hover > .ps__rail-x,\n.ps:hover > .ps__rail-y,\n.ps--focus > .ps__rail-x,\n.ps--focus > .ps__rail-y,\n.ps--scrolling-x > .ps__rail-x,\n.ps--scrolling-y > .ps__rail-y {\n  opacity: 0.6;\n}\n\n.ps .ps__rail-x:hover,\n.ps .ps__rail-y:hover,\n.ps .ps__rail-x:focus,\n.ps .ps__rail-y:focus,\n.ps .ps__rail-x.ps--clicking,\n.ps .ps__rail-y.ps--clicking {\n  background-color: #eee;\n  opacity: 0.9;\n}\n\n/*\n * Scrollbar thumb styles\n */\n.ps__thumb-x {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, height .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, height .2s ease-in-out;\n  height: 6px;\n  /* there must be 'bottom' for ps__thumb-x */\n  bottom: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__thumb-y {\n  background-color: #aaa;\n  border-radius: 6px;\n  transition: background-color .2s linear, width .2s ease-in-out;\n  -webkit-transition: background-color .2s linear, width .2s ease-in-out;\n  width: 6px;\n  /* there must be 'right' for ps__thumb-y */\n  right: 2px;\n  /* please don't change 'position' */\n  position: absolute;\n}\n\n.ps__rail-x:hover > .ps__thumb-x,\n.ps__rail-x:focus > .ps__thumb-x,\n.ps__rail-x.ps--clicking .ps__thumb-x {\n  background-color: #999;\n  height: 11px;\n}\n\n.ps__rail-y:hover > .ps__thumb-y,\n.ps__rail-y:focus > .ps__thumb-y,\n.ps__rail-y.ps--clicking .ps__thumb-y {\n  background-color: #999;\n  width: 11px;\n}\n\n/* MS supports */\n@supports (-ms-overflow-style: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .ps {\n    overflow: auto !important;\n  }\n}\n"],encapsulation:2}),e}(),Le=function(){function e(){}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=l.Sb({type:e}),e.\u0275inj=l.Rb({imports:[[n.c],n.c]}),e}();const Se=function(e){return{active:e}},Me=function(e){return{subdrop:e}},Re=function(e){return{display:e}};function ke(e,t){if(1&e&&(l.ac(0,"ul"),l.ac(1,"li",5),l.ac(2,"span"),l.Lc(3,"Main"),l.Zb(),l.Zb(),l.ac(4,"li",6),l.ac(5,"a",7),l.Vb(6,"i",8),l.ac(7,"span"),l.Lc(8,"Dashboard"),l.Zb(),l.Vb(9,"span",9),l.Zb(),l.ac(10,"ul",10),l.ac(11,"li",11),l.ac(12,"a",7),l.Lc(13,"Employee Dashboard"),l.Zb(),l.Zb(),l.ac(14,"li",12),l.ac(15,"a",7),l.Lc(16,"Admin Dashboard"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(17,"li",5),l.ac(18,"span"),l.Lc(19,"Employees"),l.Zb(),l.Zb(),l.ac(20,"li",13),l.ac(21,"a",14),l.Vb(22,"i",15),l.ac(23,"span"),l.Lc(24," Employees"),l.Zb(),l.Vb(25,"span",9),l.Zb(),l.ac(26,"ul",10),l.ac(27,"li",16),l.ac(28,"a",7),l.Lc(29,"All Employees"),l.Zb(),l.Zb(),l.ac(30,"li",17),l.ac(31,"a",7),l.Lc(32,"Attendance "),l.Zb(),l.Zb(),l.ac(33,"li",18),l.ac(34,"a",7),l.Lc(35,"In Out Transaction "),l.Zb(),l.Zb(),l.ac(36,"li",19),l.ac(37,"a",7),l.Lc(38,"Shift"),l.Zb(),l.Zb(),l.ac(39,"li",20),l.ac(40,"a",7),l.Lc(41,"Assign Shift"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(42,"li",21),l.ac(43,"a",22),l.Vb(44,"i",23),l.ac(45,"span"),l.Lc(46,"Self Service"),l.Zb(),l.Vb(47,"span",9),l.Zb(),l.ac(48,"ul",24),l.ac(49,"li",25),l.ac(50,"a",26),l.ac(51,"span"),l.Lc(52,"Sim"),l.Zb(),l.Vb(53,"span",9),l.Zb(),l.ac(54,"ul",24),l.ac(55,"li"),l.ac(56,"a",27),l.ac(57,"span"),l.Lc(58,"Requisition"),l.Zb(),l.Zb(),l.Zb(),l.ac(59,"li"),l.ac(60,"a",28),l.ac(61,"span"),l.Lc(62,"Management"),l.Zb(),l.Zb(),l.Zb(),l.ac(63,"li"),l.ac(64,"a",29),l.ac(65,"span"),l.Lc(66,"Bill upload"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(67,"li"),l.ac(68,"a",30),l.ac(69,"span"),l.Lc(70,"Apply OnTour"),l.Zb(),l.Zb(),l.Zb(),l.ac(71,"li"),l.ac(72,"a",31),l.Lc(73,"Leave Application"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(74,"li",32),l.ac(75,"a",7),l.Vb(76,"i",33),l.ac(77,"span"),l.Lc(78," Payroll "),l.Zb(),l.Vb(79,"span",9),l.Zb(),l.ac(80,"ul",10),l.ac(81,"li",34),l.ac(82,"a",7),l.Lc(83," Payroll Element "),l.Zb(),l.Zb(),l.ac(84,"li",35),l.ac(85,"a",7),l.Lc(86," Salary Process "),l.Zb(),l.Zb(),l.ac(87,"li",36),l.ac(88,"a",7),l.Lc(89," Salary & Payslip "),l.Zb(),l.Zb(),l.ac(90,"li",37),l.ac(91,"a",7),l.Lc(92," Salary Sheet [ Mgnt ] "),l.Zb(),l.Zb(),l.ac(93,"li",38),l.ac(94,"a",7),l.Lc(95," Salary Sheet [ Bank ] "),l.Zb(),l.Zb(),l.ac(96,"li",39),l.ac(97,"a",7),l.Lc(98," Off Day Bill "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(99,"li",40),l.ac(100,"a",7),l.Vb(101,"i",41),l.ac(102,"span"),l.Lc(103," Approval "),l.Zb(),l.Vb(104,"span",9),l.Zb(),l.ac(105,"ul",10),l.ac(106,"li",42),l.ac(107,"a",7),l.Lc(108," Approval Process "),l.Zb(),l.Zb(),l.ac(109,"li",43),l.ac(110,"a",7),l.Lc(111," Approval Step "),l.Zb(),l.Zb(),l.ac(112,"li",44),l.ac(113,"a",7),l.Lc(114," Approval Step Approver "),l.Zb(),l.Zb(),l.ac(115,"li",45),l.ac(116,"a",7),l.Lc(117," Approval Step Action "),l.Zb(),l.Zb(),l.ac(118,"li",46),l.ac(119,"a",7),l.Lc(120," Approval Process Tnx History "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(121,"li",47),l.ac(122,"a",7),l.Vb(123,"i",48),l.ac(124,"span"),l.Lc(125," i-Recruitment "),l.Zb(),l.Vb(126,"span",9),l.Zb(),l.ac(127,"ul",10),l.ac(128,"li",49),l.ac(129,"a",7),l.Lc(130," Vacancy "),l.Zb(),l.Zb(),l.ac(131,"li",50),l.ac(132,"a",7),l.Lc(133," Applicant "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(134,"li",51),l.ac(135,"a",7),l.Vb(136,"i",52),l.ac(137,"span"),l.Lc(138," Reports "),l.Zb(),l.Vb(139,"span",9),l.Zb(),l.ac(140,"ul",10),l.ac(141,"li",53),l.ac(142,"a",7),l.Lc(143," Payslip Report "),l.Zb(),l.Zb(),l.ac(144,"li",54),l.ac(145,"a",7),l.Lc(146," Attendance Report "),l.Zb(),l.Zb(),l.ac(147,"li",55),l.ac(148,"a",7),l.Lc(149," Attendance Report (Hr Admin) "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(150,"li",56),l.ac(151,"a",57),l.Vb(152,"i",58),l.ac(153,"span"),l.Lc(154,"Base Setup"),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e){const e=l.jc();l.Ib(4),l.pc("ngClass",l.tc(48,Se,"dashboard"===e.urlComplete.mainUrl)),l.Ib(1),l.pc("ngClass",l.tc(50,Me,"dashboard"===e.urlComplete.mainUrl)),l.Ib(5),l.pc("ngStyle",l.tc(52,Re,"dashboard"===e.urlComplete.mainUrl?"block":"none")),l.Ib(2),l.pc("ngClass",l.tc(54,Se,"employee"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(56,Se,"admin"===e.urlComplete.subUrl)),l.Ib(5),l.pc("ngClass",l.tc(58,Se,"employees"===e.urlComplete.mainUrl&&"employeepage"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"holidays"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"adminleaves"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"employeeleaves"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"leavesettings"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendanceadmin"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendancerowadmin"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendanceemployee"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"departments"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"designation"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"timesheet"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"overtime"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"employeelist"===e.urlComplete.subUrl||"scheduling"===e.urlComplete.mainUrl||"shift"===e.urlComplete.mainUrl||"scheduling"===e.urlComplete.mainUrl||"assign-shift"===e.urlComplete.mainUrl)),l.Ib(1),l.pc("ngClass",l.tc(60,Me,"employees"===e.urlComplete.mainUrl&&"employeepage"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"holidays"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"adminleaves"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"employeeleaves"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"leavesettings"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendanceadmin"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendancerowadmin"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendanceemployee"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"departments"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"designation"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"timesheet"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"overtime"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"employeelist"===e.urlComplete.subUrl||"scheduling"===e.urlComplete.mainUrl||"shift"===e.urlComplete.mainUrl||"assign-shift"===e.urlComplete.mainUrl)),l.Ib(5),l.pc("ngStyle",l.tc(62,Re,"employees"===e.urlComplete.mainUrl&&"employeepage"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"holidays"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"adminleaves"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"employeeleaves"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"leavesettings"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendanceadmin"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendancerowadmin"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"attendanceemployee"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"departments"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"designation"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"timesheet"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"overtime"===e.urlComplete.subUrl||"employees"===e.urlComplete.mainUrl&&"employeelist"===e.urlComplete.subUrl||"scheduling"===e.urlComplete.mainUrl||"shift"===e.urlComplete.mainUrl||"assign-shift"===e.urlComplete.mainUrl?"block":"none")),l.Ib(2),l.pc("ngClass",l.tc(64,Se,"employeepage"===e.urlComplete.subUrl||"employeelist"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(66,Se,"attendanceemployee"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(68,Se,"attendancerowadmin"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(70,Se,"shift-list"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(72,Se,"shift-assign"===e.urlComplete.subUrl)),l.Ib(2),l.pc("ngClass",l.tc(74,Se,"sefl-service"===e.urlComplete.mainUrl||"sim"===e.urlComplete.mainUrl)),l.Ib(14),l.pc("ngClass",l.tc(76,Se,"requisition/list"===e.urlComplete.fullUrl)),l.Ib(4),l.pc("ngClass",l.tc(78,Se,"management/list"===e.urlComplete.fullUrl)),l.Ib(4),l.pc("ngClass",l.tc(80,Se,"billUpload/list"===e.urlComplete.fullUrl)),l.Ib(4),l.pc("ngClass",l.tc(82,Se,"onTour"===e.urlComplete.subUrl)),l.Ib(4),l.pc("ngClass",l.tc(84,Se,"employeeleaves"===e.urlComplete.subUrl)),l.Ib(2),l.pc("ngClass",l.tc(86,Se,"payroll"===e.urlComplete.mainUrl)),l.Ib(1),l.pc("ngClass",l.tc(88,Me,"payroll"===e.urlComplete.mainUrl)),l.Ib(5),l.pc("ngStyle",l.tc(90,Re,"payroll"===e.urlComplete.mainUrl?"block":"none")),l.Ib(2),l.pc("ngClass",l.tc(92,Se,"element-value/list"===e.urlComplete.fullUrl)),l.Ib(3),l.pc("ngClass",l.tc(94,Se,"salary-process-list"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(96,Se,"employee-salary"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(98,Se,"salary-sheet"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(100,Se,"salary-sheet-bank"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(102,Se,"salary-sheet-bank"===e.urlComplete.subUrl)),l.Ib(2),l.pc("ngClass",l.tc(104,Se,"approval"===e.urlComplete.mainUrl)),l.Ib(1),l.pc("ngClass",l.tc(106,Me,"approval"===e.urlComplete.mainUrl)),l.Ib(5),l.pc("ngStyle",l.tc(108,Re,"approval"===e.urlComplete.mainUrl?"block":"none")),l.Ib(2),l.pc("ngClass",l.tc(110,Se,"approval-process"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(112,Se,"approval-step"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(114,Se,"approval-step-approver"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(116,Se,"approval-step-action"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(118,Se,"approval-process-tnx-history"===e.urlComplete.subUrl)),l.Ib(2),l.pc("ngClass",l.tc(120,Se,"irecruitment"===e.urlComplete.mainUrl)),l.Ib(1),l.pc("ngClass",l.tc(122,Me,"irecruitment"===e.urlComplete.mainUrl)),l.Ib(5),l.pc("ngStyle",l.tc(124,Re,"irecruitment"===e.urlComplete.mainUrl?"block":"none")),l.Ib(2),l.pc("ngClass",l.tc(126,Se,"vacancy/list"===e.urlComplete.fullUrl)),l.Ib(3),l.pc("ngClass",l.tc(128,Se,"applicant/list"===e.urlComplete.fullUrl)),l.Ib(2),l.pc("ngClass",l.tc(130,Se,"reports"===e.urlComplete.mainUrl||"employeereports"===e.urlComplete.mainUrl||"payslipreports"===e.urlComplete.mainUrl||"projectreports"===e.urlComplete.mainUrl||"paymentreports"===e.urlComplete.mainUrl||"taskreports"===e.urlComplete.mainUrl||"userreports"===e.urlComplete.mainUrl||"attendancereports"===e.urlComplete.mainUrl||"leavereports"===e.urlComplete.mainUrl||"dailyreports"===e.urlComplete.mainUrl)),l.Ib(1),l.pc("ngClass",l.tc(132,Me,"reports"===e.urlComplete.mainUrl||"employeereports"===e.urlComplete.mainUrl||"payslipreports"===e.urlComplete.mainUrl||"projectreports"===e.urlComplete.mainUrl||"paymentreports"===e.urlComplete.mainUrl||"taskreports"===e.urlComplete.mainUrl||"userreports"===e.urlComplete.mainUrl||"attendancereports"===e.urlComplete.mainUrl||"leavereports"===e.urlComplete.mainUrl||"dailyreports"===e.urlComplete.mainUrl)),l.Ib(5),l.pc("ngStyle",l.tc(134,Re,"reports"===e.urlComplete.mainUrl||"employeereports"===e.urlComplete.mainUrl||"payslipreports"===e.urlComplete.mainUrl||"projectreports"===e.urlComplete.mainUrl||"paymentreports"===e.urlComplete.mainUrl||"taskreports"===e.urlComplete.mainUrl||"userreports"===e.urlComplete.mainUrl||"attendancereports"===e.urlComplete.mainUrl||"leavereports"===e.urlComplete.mainUrl||"dailyreports"===e.urlComplete.mainUrl?"block":"none")),l.Ib(2),l.pc("ngClass",l.tc(136,Se,"payslip-reports"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(138,Se,"attendance-reports"===e.urlComplete.subUrl)),l.Ib(3),l.pc("ngClass",l.tc(140,Se,"attendance-reports-hr-admin"===e.urlComplete.subUrl)),l.Ib(2),l.pc("ngClass",l.tc(142,Se,"settings"===e.urlComplete.subUrl))}}let Ae=(()=>{class e{constructor(e,t,i,n){this.router=e,this.allModulesService=t,this.headerService=i,this.employeeDashboardService=n,this.baseUrl=c.a.baseUrl,this.menuData=[],this.urlComplete={mainUrl:"",subUrl:"",childUrl:"",fullUrl:""},this.sidebarMenus={default:!0,chat:!1,settings:!1},this.members={},this.groups={},this.router.events.subscribe(e=>{if(e instanceof r.b){$(".main-wrapper").removeClass("slide-nav"),$(".sidebar-overlay").removeClass("opened");const t=e.url.split("/");this.urlComplete.mainUrl=t[1],this.urlComplete.subUrl=t[2],this.urlComplete.childUrl=t[3],""===t[1]&&(this.urlComplete.mainUrl="dashboard",this.urlComplete.subUrl="admin"),"chat"===t[2]||"calls"===t[2]?(this.sidebarMenus.chat=!0,this.sidebarMenus.default=!1):(this.sidebarMenus.chat=!1,this.sidebarMenus.default=!0),this.urlComplete.fullUrl=t[1],t.length>3&&(this.urlComplete.fullUrl=t[2]+"/"+t[3]),console.log(this.urlComplete.fullUrl)}}),this.groups=Object.assign({},this.allModulesService.groups),this.members=Object.assign({},this.allModulesService.members)}ngOnInit(){this._getMenuesAuth(),$(document).on("click","#sidebar-menu a",function(e){e.stopImmediatePropagation(),$(this).parent().hasClass("submenu")&&e.preventDefault(),$(this).hasClass("subdrop")?$(this).hasClass("subdrop")&&($(this).removeClass("subdrop"),$(this).next("ul").slideUp(350)):($("ul",$(this).parents("ul:first")).slideUp(350),$("a",$(this).parents("ul:first")).removeClass("subdrop"),$(this).next("ul").slideDown(350),$(this).addClass("subdrop"))})}_getMenuesAuth(){this.employeeDashboardService.sendGetRequestForMenusAuth().subscribe(e=>{this.menuData=e,console.log("From Sidebar :: "+e),console.log(this.menuData);for(const t in this.menuData){const e=this.menuData[t];console.log(e),1!=e&&$("#"+t).css({display:"none"})}})}setActive(e){this.allModulesService.members.active=e}}return e.\u0275fac=function(t){return new(t||e)(l.Ub(r.c),l.Ub(p.a),l.Ub(m.a),l.Ub(d.a))},e.\u0275cmp=l.Ob({type:e,selectors:[["app-sidebar"]],decls:5,vars:2,consts:[["id","sidebar",1,"sidebar"],[1,"sidebar-inner","slimscroll"],[3,"scrollIndicators"],["id","sidebar-menu",1,"sidebar-menu"],[4,"ngIf"],[1,"menu-title"],["id","DB_L1_100",1,"submenu",3,"ngClass"],["href","javascript:",3,"ngClass"],[1,"la","la-dashboard"],[1,"menu-arrow"],[2,"display","none",3,"ngStyle"],["id","DB_L2_100","routerLink","/dashboard/employee"],["id","DB_L2_200","routerLink","/dashboard/admin"],["id","EMP_L1_200",1,"submenu",3,"ngClass"],["href","javascript:",1,"noti-dot",3,"ngClass"],[1,"la","la-user"],["id","EMP_L2_100","routerLink","/employees/employeepage"],["id","EMP_L2_200","routerLink","/employees/attendanceemployee"],["id","EMP_L2_300","routerLink","/employees/attendancerowadmin"],["id","EMP_L2_400","routerLink","/shift/shift-list"],["id","EMP_L2_500","routerLink","/shift/shift-assign"],["id","SLS_L1_300",1,"submenu",3,"ngClass"],["id","selfService","href","javascript:"],[1,"la","la-share-alt"],[2,"display","none"],[1,"submenu"],["href","javascript:"],["routerLink","/sim/requisition/list","href","javascript:",3,"ngClass"],["routerLink","/sim/management/list","href","javascript:",3,"ngClass"],["routerLink","sim/billUpload/list","href","javascript:",3,"ngClass"],["routerLink","/sefl-service/onTour","href","javascript:",3,"ngClass"],["routerLink","/sefl-service/employeeleaves","href","javascript:",3,"ngClass"],["id","PRL_L1_400",1,"submenu",3,"ngClass"],[1,"la","la-money"],["id","PRL_L2_100","routerLink","/payroll/element-value/list"],["id","PRL_L2_200","routerLink","/payroll/salary-process-list"],["id","PRL_L2_300","routerLink","/payroll/employee-salary"],["id","PRL_L2_400","routerLink","/payroll/employee-salary"],["id","PRL_L2_500","routerLink","/payroll/employee-salary"],["id","PRL_L2_600","routerLink","/payroll/off-day-bill"],["id","APRVL_L1_500",1,"submenu",3,"ngClass"],[1,"la","la-check-circle"],["id","APRVL_L2_100","routerLink","/approval/approval-process"],["id","APRVL_L2_200","routerLink","/approval/approval-step"],["id","APRVL_L2_300","routerLink","/approval/approval-step-approver"],["id","APRVL_L2_400","routerLink","/approval/approval-step-action"],["id","APRVL_L2_500","routerLink","/approval/approval-process-tnx-history"],["id","IR_L1_600",1,"submenu",3,"ngClass"],[1,"las","la-user-circle"],["id","IR_L2_100","routerLink","/irecruitment/vacancy/list"],["id","IR_L2_200","routerLink","/irecruitment/applicant/list"],["id","RPT_L1_700",1,"submenu",3,"ngClass"],[1,"la","la-pie-chart"],["id","RPT_L2_100","routerLink","/payslipreports/payslip-reports"],["id","RPT_L2_200","routerLink","/attendancereports/attendance-reports"],["routerLink","/attendancereports/attendance-reports-hr-admin"],["id","BS_L1_800",3,"ngClass"],["routerLink","/settings/company-settings"],[1,"la","la-cog"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"perfect-scrollbar",2),l.ac(3,"div",3),l.Jc(4,ke,155,144,"ul",4),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(2),l.pc("scrollIndicators",!1),l.Ib(2),l.pc("ngIf",t.sidebarMenus.default))},directives:[Te,n.m,n.k,n.n,r.d,r.e],styles:[".submenu-slidedown[_ngcontent-%COMP%]{display:block;transition:display 2s ease-out}.submenu-slideup[_ngcontent-%COMP%]{display:none}"]}),e})();var Ie=i("d//k");function je(e,t){if(1&e&&(l.ac(0,"li",47),l.ac(1,"a",37),l.ac(2,"div",48),l.ac(3,"span",49),l.Vb(4,"img",50),l.Zb(),l.ac(5,"div",51),l.ac(6,"p",52),l.ac(7,"span",53),l.Lc(8),l.Zb(),l.Lc(9),l.ac(10,"span",53),l.Lc(11),l.Zb(),l.Zb(),l.ac(12,"p",54),l.ac(13,"span",55),l.Lc(14),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e){const e=t.$implicit,i=t.index;l.Ib(4),l.rc("src","assets/img/profiles/avatar-",i+1,".jpg",l.Fc),l.Ib(4),l.Mc(e.author),l.Ib(1),l.Nc(" ",e.function," "),l.Ib(2),l.Mc(e.message),l.Ib(3),l.Mc(e.time)}}function We(e,t){if(1&e){const e=l.bc();l.ac(0,"a",64),l.hc("click",function(){return l.Cc(e),l.jc(2).logout()}),l.Lc(1,"Logout"),l.Zb()}}function Pe(e,t){if(1&e&&(l.ac(0,"li",56),l.ac(1,"a",26),l.ac(2,"span",57),l.Vb(3,"img",58),l.Vb(4,"span",59),l.Zb(),l.ac(5,"span"),l.Lc(6),l.Zb(),l.Zb(),l.ac(7,"div",60),l.ac(8,"a",61),l.Lc(9,"My Profile"),l.Zb(),l.ac(10,"a",62),l.Lc(11,"Settings"),l.Zb(),l.Jc(12,We,2,0,"a",63),l.Zb(),l.Zb()),2&e){const e=l.jc();l.Ib(3),l.pc("src",e.profileImageUrl,l.Fc),l.Ib(3),l.Nc(" ",e.user.firstName,""),l.Ib(2),l.rc("routerLink","/employees/employeeprofile/",e.user.id,""),l.Ib(4),l.pc("ngIf",e.isLoggedIn)}}const Ue=function(){return["/dashboard"]};let Oe=(()=>{class e{constructor(e,t,i){this.headerService=e,this.router=t,this.login=i,this.baseUrl=c.a.baseUrl,this.isLoggedIn=!1,this.jsonData={notification:[],message:[]}}ngOnInit(){this.isLoggedIn=this.login.isLoggedIn(),this.user=this.login.getUser(),this.profileImageUrl=this.baseUrl+this.user.pic_,this.login.loginStatusSubject.asObservable().subscribe(e=>{this.isLoggedIn=this.login.isLoggedIn()}),this.notifications=[{message:"Patient appointment booking",author:"John Doe",function:"added new task",time:"4 mins ago"},{message:"Patient appointment booking",author:"John Doe",function:"added new task",time:"1 hour ago"},{message:"Patient appointment booking",author:"John Doe",function:"added new task",time:"4 mins ago"},{message:"Patient appointment booking",author:"John Doe",function:"added new task",time:"1 hour ago"},{message:"Patient appointment booking",author:"John Doe",function:"added new task",time:"4 mins ago"},{message:"Patient appointment booking",author:"John Doe",function:"added new task",time:"1 hour ago"}],this.messagesData=[{message:"Lorem ipsum dolor sit amet, consectetur adipiscing",author:"Mike Litorus",time:"4 mins ago"},{message:"Lorem ipsum dolor sit amet, consectetur adipiscing",author:"Mike Litorus",time:"1 hour ago"},{message:"Lorem ipsum dolor sit amet, consectetur adipiscing",author:"Mike Litorus",time:"4 mins ago"}]}logout(){this.login.logout()}getDatas(e){this.headerService.getDataFromJson(e).subscribe(t=>{this.jsonData[e]=t})}clearData(e){this.jsonData[e]=[]}onSubmit(){this.router.navigate(["/pages/search"])}toggleCompanyLogo(){console.log("toggleCompanyLogo"),"1"==$("#companyLogo").attr("data-flag")?($("#companyLogo").attr("data-flag","0"),$("#companyLogo").attr("src","assets/img/one_direction_logo.png")):($("#companyLogo").attr("data-flag","1"),$("#companyLogo").attr("src","assets/img/White_w_logo-2.png"))}}return e.\u0275fac=function(t){return new(t||e)(l.Ub(m.a),l.Ub(r.c),l.Ub(Ie.a))},e.\u0275cmp=l.Ob({type:e,selectors:[["app-header"]],decls:73,vars:4,consts:[[1,"header"],[1,"header-left"],[1,"logo",3,"routerLink"],["id","companyLogo","data-flag","1","src","assets/img/White_w_logo-2.png","width","80","height","36","alt",""],["id","toggle_btn",3,"click"],[1,"bar-icon"],[1,"page-title-box"],["id","mobile_btn","href","javascript:",1,"mobile_btn"],[1,"fa","fa-bars"],[1,"nav","user-menu"],[1,"nav-item"],[1,"top-nav-search"],[1,"responsive-search"],[1,"fa","fa-search"],[3,"ngSubmit"],["type","text","placeholder","Search here",1,"form-control"],["type","button",1,"btn"],[1,"nav-item","dropdown","has-arrow","flag-nav"],["href","javascript:","data-toggle","dropdown",1,"nav-link","dropdown-toggle"],[1,"dropdown-menu","dropdown-menu-right"],[1,"dropdown-item"],["src","assets/img/flags/us.png","alt","","height","16"],["src","assets/img/flags/fr.png","alt","","height","16"],["src","assets/img/flags/es.png","alt","","height","16"],["src","assets/img/flags/de.png","alt","","height","16"],[1,"nav-item","dropdown"],["href","javascript:","data-toggle","dropdown",1,"dropdown-toggle","nav-link"],[1,"fa","fa-bell-o"],[1,"badge","badge-pill"],[1,"dropdown-menu","notifications"],[1,"topnav-dropdown-header"],[1,"notification-title"],["href","javascript:",1,"clear-noti",3,"click"],[1,"noti-content"],[1,"notification-list"],["class","notification-message",4,"ngFor","ngForOf"],[1,"topnav-dropdown-footer"],["routerLink","/activities/activities-main"],["routerLink","/settings/chat","href","javascript:","data-toggle","dropdown",1,"dropdown-toggle","nav-link"],[1,"fa","fa-comment-o"],["class","nav-item dropdown has-arrow main-drop",4,"ngIf"],[1,"dropdown","mobile-user-menu"],["data-toggle","dropdown","aria-expanded","false",1,"nav-link","dropdown-toggle"],[1,"fa","fa-ellipsis-v"],["routerLink","/employees/employeeprofile",1,"dropdown-item"],["href","javascript:",1,"dropdown-item"],["routerLink","/login",1,"dropdown-item"],[1,"notification-message"],[1,"media"],[1,"avatar"],["alt","",3,"src"],[1,"media-body"],[1,"noti-details"],[1,"noti-title"],[1,"noti-time"],[1,"notification-time"],[1,"nav-item","dropdown","has-arrow","main-drop"],[1,"user-img"],["onerror","this.src='assets/img/user-icon/u-sq-pic.jpg';","alt","",3,"src"],[1,"status","online"],[1,"dropdown-menu"],[1,"dropdown-item",3,"routerLink"],["routerLink","/settings/company-settings",1,"dropdown-item"],["class","dropdown-item","href","#",3,"click",4,"ngIf"],["href","#",1,"dropdown-item",3,"click"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"a",2),l.Vb(3,"img",3),l.Zb(),l.Zb(),l.ac(4,"a",4),l.hc("click",function(){return t.toggleCompanyLogo()}),l.ac(5,"span",5),l.Vb(6,"span"),l.Vb(7,"span"),l.Vb(8,"span"),l.Zb(),l.Zb(),l.ac(9,"div",6),l.ac(10,"h3"),l.Lc(11,"One Direction Company Limited"),l.Zb(),l.Zb(),l.ac(12,"a",7),l.Vb(13,"i",8),l.Zb(),l.ac(14,"ul",9),l.ac(15,"li",10),l.ac(16,"div",11),l.ac(17,"a",12),l.Vb(18,"i",13),l.Zb(),l.ac(19,"form",14),l.hc("ngSubmit",function(){return t.onSubmit()}),l.Vb(20,"input",15),l.ac(21,"button",16),l.Vb(22,"i",13),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(23,"li",17),l.ac(24,"a",18),l.ac(25,"span"),l.Lc(26,"English"),l.Zb(),l.Zb(),l.ac(27,"div",19),l.ac(28,"a",20),l.Vb(29,"img",21),l.Lc(30," English "),l.Zb(),l.ac(31,"a",20),l.Vb(32,"img",22),l.Lc(33," French "),l.Zb(),l.ac(34,"a",20),l.Vb(35,"img",23),l.Lc(36," Spanish "),l.Zb(),l.ac(37,"a",20),l.Vb(38,"img",24),l.Lc(39," German "),l.Zb(),l.Zb(),l.Zb(),l.ac(40,"li",25),l.ac(41,"a",26),l.Vb(42,"i",27),l.ac(43,"span",28),l.Lc(44,"3"),l.Zb(),l.Zb(),l.ac(45,"div",29),l.ac(46,"div",30),l.ac(47,"span",31),l.Lc(48,"Notifications"),l.Zb(),l.ac(49,"a",32),l.hc("click",function(){return t.clearData("notification")}),l.Lc(50," Clear All "),l.Zb(),l.Zb(),l.ac(51,"div",33),l.ac(52,"ul",34),l.Jc(53,je,15,5,"li",35),l.Zb(),l.Zb(),l.ac(54,"div",36),l.ac(55,"a",37),l.Lc(56,"View all Notifications"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(57,"li",25),l.ac(58,"a",38),l.Vb(59,"i",39),l.ac(60,"span",28),l.Lc(61,"0"),l.Zb(),l.Zb(),l.Zb(),l.Jc(62,Pe,13,4,"li",40),l.Zb(),l.ac(63,"div",41),l.ac(64,"a",42),l.Vb(65,"i",43),l.Zb(),l.ac(66,"div",19),l.ac(67,"a",44),l.Lc(68,"My Profile"),l.Zb(),l.ac(69,"a",45),l.Lc(70,"Settings"),l.Zb(),l.ac(71,"a",46),l.Lc(72,"Logout"),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(2),l.pc("routerLink",l.sc(3,Ue)),l.Ib(51),l.pc("ngForOf",t.notifications),l.Ib(9),l.pc("ngIf",t.user))},directives:[r.e,a.x,a.p,a.q,n.l,n.m],styles:[".header[_ngcontent-%COMP%]{background:linear-gradient(90deg,#18255f 0,#612f94)}"]}),e})();const Ee=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=l.Ob({type:e,selectors:[["app-all-modules"]],decls:3,vars:0,template:function(e,t){1&e&&(l.Vb(0,"app-sidebar"),l.Vb(1,"app-header"),l.Vb(2,"router-outlet"))},directives:[Ae,Oe,r.g],styles:[""]}),e})(),children:[{path:"dashboard",loadChildren:()=>i.e(24).then(i.bind(null,"C3Jh")).then(e=>e.DashboardModule)},{path:"employees",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(2),i.e(3),i.e(7),i.e(26)]).then(i.bind(null,"YMQh")).then(e=>e.EmployeesModule),canActivate:[s.a]},{path:"payroll",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(2),i.e(3),i.e(16)]).then(i.bind(null,"DRj9")).then(e=>e.PayrollModule)},{path:"reports",loadChildren:()=>Promise.all([i.e(0),i.e(21)]).then(i.bind(null,"Vc9h")).then(e=>e.ReportsModule)},{path:"assets",loadChildren:()=>Promise.all([i.e(0),i.e(20)]).then(i.bind(null,"Z1Zw")).then(e=>e.AssetsModule)},{path:"users",loadChildren:()=>i.e(28).then(i.bind(null,"Zqwr")).then(e=>e.UsersModule)},{path:"settings",loadChildren:()=>Promise.all([i.e(2),i.e(3),i.e(4),i.e(14)]).then(i.bind(null,"03ai")).then(e=>e.SettingsModule)},{path:"shift",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(17)]).then(i.bind(null,"MqyI")).then(e=>e.ShiftModule)},{path:"employeereports",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(25)]).then(i.bind(null,"CmBZ")).then(e=>e.EmployeereportsModule)},{path:"attendancereports",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(2),i.e(3),i.e(7),i.e(23)]).then(i.bind(null,"Nyzf")).then(e=>e.AttendancereportsModule)},{path:"leavereports",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(27)]).then(i.bind(null,"KQSM")).then(e=>e.LeavereportsModule)},{path:"approval",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(2),i.e(3),i.e(4),i.e(22)]).then(i.bind(null,"oV/K")).then(e=>e.ApprovalModule)},{path:"sim",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(2),i.e(3),i.e(18)]).then(i.bind(null,"S+So")).then(e=>e.SimModule)},{path:"sefl-service",loadChildren:()=>Promise.all([i.e(0),i.e(1),i.e(2),i.e(3),i.e(4),i.e(19)]).then(i.bind(null,"7K3g")).then(e=>e.SelfServiceModule)},{path:"irecruitment",loadChildren:()=>Promise.all([i.e(1),i.e(2),i.e(15)]).then(i.bind(null,"F+LP")).then(e=>e.IRecruitmentModule)}]}];let Ne=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=l.Sb({type:e}),e.\u0275inj=l.Rb({imports:[[r.f.forChild(Ee)],r.f]}),e})();var Fe,qe=i("2Vo4"),Ze=i("HDdC"),Je=i("Cfvw"),Be=i("LRne"),Ye=i("SxV6"),Xe=i("bOdf"),He={100:{code:100,text:"Continue",description:'"The initial part of a request has been received and has not yet been rejected by the server."',spec_title:"RFC7231#6.2.1",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.2.1"},101:{code:101,text:"Switching Protocols",description:'"The server understands and is willing to comply with the client\'s request, via the Upgrade header field, for a change in the application protocol being used on this connection."',spec_title:"RFC7231#6.2.2",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.2.2"},200:{code:200,text:"OK",description:'"The request has succeeded."',spec_title:"RFC7231#6.3.1",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.1"},201:{code:201,text:"Created",description:'"The request has been fulfilled and has resulted in one or more new resources being created."',spec_title:"RFC7231#6.3.2",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.2"},202:{code:202,text:"Accepted",description:'"The request has been accepted for processing, but the processing has not been completed."',spec_title:"RFC7231#6.3.3",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.3"},203:{code:203,text:"Non-Authoritative Information",description:'"The request was successful but the enclosed payload has been modified from that of the origin server\'s 200 (OK) response by a transforming proxy."',spec_title:"RFC7231#6.3.4",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.4"},204:{code:204,text:"No Content",description:'"The server has successfully fulfilled the request and that there is no additional content to send in the response payload body."',spec_title:"RFC7231#6.3.5",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.5"},205:{code:205,text:"Reset Content",description:'"The server has fulfilled the request and desires that the user agent reset the "document view", which caused the request to be sent, to its original state as received from the origin server."',spec_title:"RFC7231#6.3.6",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.6"},206:{code:206,text:"Partial Content",description:'"The server is successfully fulfilling a range request for the target resource by transferring one or more parts of the selected representation that correspond to the satisfiable ranges found in the requests\'s Range header field."',spec_title:"RFC7233#4.1",spec_href:"http://tools.ietf.org/html/rfc7233#section-4.1"},300:{code:300,text:"Multiple Choices",description:'"The target resource has more than one representation, each with its own more specific identifier, and information about the alternatives is being provided so that the user (or user agent) can select a preferred representation by redirecting its request to one or more of those identifiers."',spec_title:"RFC7231#6.4.1",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.4.1"},301:{code:301,text:"Moved Permanently",description:'"The target resource has been assigned a new permanent URI and any future references to this resource ought to use one of the enclosed URIs."',spec_title:"RFC7231#6.4.2",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.4.2"},302:{code:302,text:"Found",description:'"The target resource resides temporarily under a different URI."',spec_title:"RFC7231#6.4.3",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.4.3"},303:{code:303,text:"See Other",description:'"The server is redirecting the user agent to a different resource, as indicated by a URI in the Location header field, that is intended to provide an indirect response to the original request."',spec_title:"RFC7231#6.4.4",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.4.4"},304:{code:304,text:"Not Modified",description:'"A conditional GET request has been received and would have resulted in a 200 (OK) response if it were not for the fact that the condition has evaluated to false."',spec_title:"RFC7232#4.1",spec_href:"http://tools.ietf.org/html/rfc7232#section-4.1"},305:{code:305,text:"Use Proxy",description:"*deprecated*",spec_title:"RFC7231#6.4.5",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.4.5"},307:{code:307,text:"Temporary Redirect",description:'"The target resource resides temporarily under a different URI and the user agent MUST NOT change the request method if it performs an automatic redirection to that URI."',spec_title:"RFC7231#6.4.7",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.4.7"},400:{code:400,text:"Bad Request",description:'"The server cannot or will not process the request because the received syntax is invalid, nonsensical, or exceeds some limitation on what the server is willing to process."',spec_title:"RFC7231#6.5.1",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.1"},401:{code:401,text:"Unauthorized",description:'"The request has not been applied because it lacks valid authentication credentials for the target resource."',spec_title:"RFC7235#6.3.1",spec_href:"http://tools.ietf.org/html/rfc7235#section-3.1"},402:{code:402,text:"Payment Required",description:"*reserved*",spec_title:"RFC7231#6.5.2",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.2"},403:{code:403,text:"Forbidden",description:'"The server understood the request but refuses to authorize it."',spec_title:"RFC7231#6.5.3",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.3"},404:{code:404,text:"Not Found",description:'"The origin server did not find a current representation for the target resource or is not willing to disclose that one exists."',spec_title:"RFC7231#6.5.4",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.4"},405:{code:405,text:"Method Not Allowed",description:'"The method specified in the request-line is known by the origin server but not supported by the target resource."',spec_title:"RFC7231#6.5.5",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.5"},406:{code:406,text:"Not Acceptable",description:'"The target resource does not have a current representation that would be acceptable to the user agent, according to the proactive negotiation header fields received in the request, and the server is unwilling to supply a default representation."',spec_title:"RFC7231#6.5.6",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.6"},407:{code:407,text:"Proxy Authentication Required",description:'"The client needs to authenticate itself in order to use a proxy."',spec_title:"RFC7231#6.3.2",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.3.2"},408:{code:408,text:"Request Timeout",description:'"The server did not receive a complete request message within the time that it was prepared to wait."',spec_title:"RFC7231#6.5.7",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.7"},409:{code:409,text:"Conflict",description:'"The request could not be completed due to a conflict with the current state of the resource."',spec_title:"RFC7231#6.5.8",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.8"},410:{code:410,text:"Gone",description:'"Access to the target resource is no longer available at the origin server and that this condition is likely to be permanent."',spec_title:"RFC7231#6.5.9",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.9"},411:{code:411,text:"Length Required",description:'"The server refuses to accept the request without a defined Content-Length."',spec_title:"RFC7231#6.5.10",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.10"},412:{code:412,text:"Precondition Failed",description:'"One or more preconditions given in the request header fields evaluated to false when tested on the server."',spec_title:"RFC7232#4.2",spec_href:"http://tools.ietf.org/html/rfc7232#section-4.2"},413:{code:413,text:"Payload Too Large",description:'"The server is refusing to process a request because the request payload is larger than the server is willing or able to process."',spec_title:"RFC7231#6.5.11",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.11"},414:{code:414,text:"URI Too Long",description:'"The server is refusing to service the request because the request-target is longer than the server is willing to interpret."',spec_title:"RFC7231#6.5.12",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.12"},415:{code:415,text:"Unsupported Media Type",description:'"The origin server is refusing to service the request because the payload is in a format not supported by the target resource for this method."',spec_title:"RFC7231#6.5.13",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.13"},416:{code:416,text:"Range Not Satisfiable",description:'"None of the ranges in the request\'s Range header field overlap the current extent of the selected resource or that the set of ranges requested has been rejected due to invalid ranges or an excessive request of small or overlapping ranges."',spec_title:"RFC7233#4.4",spec_href:"http://tools.ietf.org/html/rfc7233#section-4.4"},417:{code:417,text:"Expectation Failed",description:'"The expectation given in the request\'s Expect header field could not be met by at least one of the inbound servers."',spec_title:"RFC7231#6.5.14",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.14"},418:{code:418,text:"I'm a teapot",description:'"1988 April Fools Joke. Returned by tea pots requested to brew coffee."',spec_title:"RFC 2324",spec_href:"https://tools.ietf.org/html/rfc2324"},426:{code:426,text:"Upgrade Required",description:'"The server refuses to perform the request using the current protocol but might be willing to do so after the client upgrades to a different protocol."',spec_title:"RFC7231#6.5.15",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.5.15"},500:{code:500,text:"Internal Server Error",description:'"The server encountered an unexpected condition that prevented it from fulfilling the request."',spec_title:"RFC7231#6.6.1",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.6.1"},501:{code:501,text:"Not Implemented",description:'"The server does not support the functionality required to fulfill the request."',spec_title:"RFC7231#6.6.2",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.6.2"},502:{code:502,text:"Bad Gateway",description:'"The server, while acting as a gateway or proxy, received an invalid response from an inbound server it accessed while attempting to fulfill the request."',spec_title:"RFC7231#6.6.3",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.6.3"},503:{code:503,text:"Service Unavailable",description:'"The server is currently unable to handle the request due to a temporary overload or scheduled maintenance, which will likely be alleviated after some delay."',spec_title:"RFC7231#6.6.4",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.6.4"},504:{code:504,text:"Gateway Time-out",description:'"The server, while acting as a gateway or proxy, did not receive a timely response from an upstream server it needed to access in order to complete the request."',spec_title:"RFC7231#6.6.5",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.6.5"},505:{code:505,text:"HTTP Version Not Supported",description:'"The server does not support, or refuses to support, the protocol version that was used in the request message."',spec_title:"RFC7231#6.6.6",spec_href:"http://tools.ietf.org/html/rfc7231#section-6.6.6"},102:{code:102,text:"Processing",description:'"An interim response to inform the client that the server has accepted the complete request, but has not yet completed it."',spec_title:"RFC5218#10.1",spec_href:"http://tools.ietf.org/html/rfc2518#section-10.1"},207:{code:207,text:"Multi-Status",description:'"Status for multiple independent operations."',spec_title:"RFC5218#10.2",spec_href:"http://tools.ietf.org/html/rfc2518#section-10.2"},226:{code:226,text:"IM Used",description:'"The server has fulfilled a GET request for the resource, and the response is a representation of the result of one or more instance-manipulations applied to the current instance."',spec_title:"RFC3229#10.4.1",spec_href:"http://tools.ietf.org/html/rfc3229#section-10.4.1"},308:{code:308,text:"Permanent Redirect",description:'"The target resource has been assigned a new permanent URI and any future references to this resource SHOULD use one of the returned URIs. [...] This status code is similar to 301 Moved Permanently (Section 7.3.2 of rfc7231), except that it does not allow rewriting the request method from POST to GET."',spec_title:"RFC7238",spec_href:"http://tools.ietf.org/html/rfc7238"},422:{code:422,text:"Unprocessable Entity",description:'"The server understands the content type of the request entity (hence a 415(Unsupported Media Type) status code is inappropriate), and the syntax of the request entity is correct (thus a 400 (Bad Request) status code is inappropriate) but was unable to process the contained instructions."',spec_title:"RFC5218#10.3",spec_href:"http://tools.ietf.org/html/rfc2518#section-10.3"},423:{code:423,text:"Locked",description:'"The source or destination resource of a method is locked."',spec_title:"RFC5218#10.4",spec_href:"http://tools.ietf.org/html/rfc2518#section-10.4"},424:{code:424,text:"Failed Dependency",description:'"The method could not be performed on the resource because the requested action depended on another action and that action failed."',spec_title:"RFC5218#10.5",spec_href:"http://tools.ietf.org/html/rfc2518#section-10.5"},428:{code:428,text:"Precondition Required",description:'"The origin server requires the request to be conditional."',spec_title:"RFC6585#3",spec_href:"http://tools.ietf.org/html/rfc6585#section-3"},429:{code:429,text:"Too Many Requests",description:'"The user has sent too many requests in a given amount of time ("rate limiting")."',spec_title:"RFC6585#4",spec_href:"http://tools.ietf.org/html/rfc6585#section-4"},431:{code:431,text:"Request Header Fields Too Large",description:'"The server is unwilling to process the request because its header fields are too large."',spec_title:"RFC6585#5",spec_href:"http://tools.ietf.org/html/rfc6585#section-5"},451:{code:451,text:"Unavailable For Legal Reasons",description:'"The server is denying access to the resource in response to a legal demand."',spec_title:"draft-ietf-httpbis-legally-restricted-status",spec_href:"http://tools.ietf.org/html/draft-ietf-httpbis-legally-restricted-status"},506:{code:506,text:"Variant Also Negotiates",description:'"The server has an internal configuration error: the chosen variant resource is configured to engage in transparent content negotiation itself, and is therefore not a proper end point in the negotiation process."',spec_title:"RFC2295#8.1",spec_href:"http://tools.ietf.org/html/rfc2295#section-8.1"},507:{code:507,text:"Insufficient Storage",description:'The method could not be performed on the resource because the server is unable to store the representation needed to successfully complete the request."',spec_title:"RFC5218#10.6",spec_href:"http://tools.ietf.org/html/rfc2518#section-10.6"},511:{code:511,text:"Network Authentication Required",description:'"The client needs to authenticate to gain network access."',spec_title:"RFC6585#6",spec_href:"http://tools.ietf.org/html/rfc6585#section-6"}},Ve=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},Ge=function(){},$e=function(){},ze=function(){function e(e){void 0===e&&(e={}),Object.assign(this,{caseSensitiveSearch:!1,dataEncapsulation:!1,delay:500,delete404:!1,passThruUnknownUrl:!1,post204:!0,post409:!1,put204:!0,put404:!1,apiBase:void 0,host:void 0,rootPath:void 0},e)}return(e=function(e,t,i,n){var a,o=arguments.length,r=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,i,n);else for(var s=e.length-1;s>=0;s--)(a=e[s])&&(r=(o<3?a(r):o>3?a(t,i,r):a(t,i))||r);return o>3&&r&&Object.defineProperty(t,i,r),r}([Ve("design:paramtypes",[$e])],e)).\u0275fac=function(t){return new(t||e)(l.ec($e))},e.\u0275prov=l.Qb({token:e,factory:function(t){return e.\u0275fac(t)}}),e}(),Ke=function(){function e(e,t){void 0===t&&(t={}),this.inMemDbService=e,this.config=new ze,this.requestInfoUtils=this.getRequestInfoUtils();var i=this.getLocation("/");this.config.host=i.host,this.config.rootPath=i.path,Object.assign(this.config,t)}return Object.defineProperty(e.prototype,"dbReady",{get:function(){return this.dbReadySubject||(this.dbReadySubject=new qe.a(!1),this.resetDb()),this.dbReadySubject.asObservable().pipe(Object(Ye.a)(function(e){return e}))},enumerable:!0,configurable:!0}),e.prototype.handleRequest=function(e){var t=this;return this.dbReady.pipe(Object(Xe.a)(function(){return t.handleRequest_(e)}))},e.prototype.handleRequest_=function(e){var t,i=this,n=e.urlWithParams?e.urlWithParams:e.url,a=this.bind("parseRequestUrl"),o=a&&a(n,this.requestInfoUtils)||this.parseRequestUrl(n),r=o.collectionName,s=this.db[r],l={req:e,apiBase:o.apiBase,collection:s,collectionName:r,headers:this.createHeaders({"Content-Type":"application/json"}),id:this.parseId(s,r,o.id),method:this.getRequestMethod(e),query:o.query,resourceUrl:o.resourceUrl,url:n,utils:this.requestInfoUtils};if(/commands\/?$/i.test(l.apiBase))return this.commands(l);var c=this.bind(l.method);if(c){var p=c(l);if(p)return p}return this.db[r]?this.createResponse$(function(){return i.collectionHandler(l)}):this.config.passThruUnknownUrl?this.getPassThruBackend().handle(e):(t=this.createErrorResponseOptions(n,404,"Collection '"+r+"' not found"),this.createResponse$(function(){return t}))},e.prototype.addDelay=function(e){var t,i,n=this.config.delay;return 0===n?e:(t=e,i=n||500,new Ze.a(function(e){var n=!1,a=!1,o=t.subscribe(function(t){a=!0,setTimeout(function(){e.next(t),n&&e.complete()},i)},function(t){return setTimeout(function(){return e.error(t)},i)},function(){n=!0,a||e.complete()});return function(){return o.unsubscribe()}}))},e.prototype.applyQuery=function(e,t){var i=[],n=this.config.caseSensitiveSearch?void 0:"i";t.forEach(function(e,t){e.forEach(function(e){return i.push({name:t,rx:new RegExp(decodeURI(e),n)})})});var a=i.length;return a?e.filter(function(e){for(var t=!0,n=a;t&&n;){var o=i[n-=1];t=o.rx.test(e[o.name])}return t}):e},e.prototype.bind=function(e){var t=this.inMemDbService[e];return t?t.bind(this.inMemDbService):void 0},e.prototype.bodify=function(e){return this.config.dataEncapsulation?{data:e}:e},e.prototype.clone=function(e){return JSON.parse(JSON.stringify(e))},e.prototype.collectionHandler=function(e){var t;switch(e.method){case"get":t=this.get(e);break;case"post":t=this.post(e);break;case"put":t=this.put(e);break;case"delete":t=this.delete(e);break;default:t=this.createErrorResponseOptions(e.url,405,"Method not allowed")}var i=this.bind("responseInterceptor");return i?i(t,e):t},e.prototype.commands=function(e){var t=this,i=e.collectionName.toLowerCase(),n=e.method,a={url:e.url};switch(i){case"resetdb":return a.status=204,this.resetDb(e).pipe(Object(Xe.a)(function(){return t.createResponse$(function(){return a},!1)}));case"config":if("get"===n)a.status=200,a.body=this.clone(this.config);else{var o=this.getJsonBody(e.req);Object.assign(this.config,o),this.passThruBackend=void 0,a.status=204}break;default:a=this.createErrorResponseOptions(e.url,500,'Unknown command "'+i+'"')}return this.createResponse$(function(){return a},!1)},e.prototype.createErrorResponseOptions=function(e,t,i){return{body:{error:""+i},url:e,headers:this.createHeaders({"Content-Type":"application/json"}),status:t}},e.prototype.createResponse$=function(e,t){void 0===t&&(t=!0);var i=this.createResponseOptions$(e),n=this.createResponse$fromResponseOptions$(i);return t?this.addDelay(n):n},e.prototype.createResponseOptions$=function(e){var t=this;return new Ze.a(function(i){var n;try{n=e()}catch(o){n=t.createErrorResponseOptions("",500,""+(o.message||o))}var a=n.status;try{n.statusText=function(e){return He[e].text||"Unknown Status"}(a)}catch(r){}return function(e){return e>=200&&e<300}(a)?(i.next(n),i.complete()):i.error(n),function(){}})},e.prototype.delete=function(e){var t=e.collection,i=e.headers,n=e.id;return null==n?this.createErrorResponseOptions(e.url,404,'Missing "'+e.collectionName+'" id'):{headers:i,status:this.removeById(t,n)||!this.config.delete404?204:404}},e.prototype.findById=function(e,t){return e.find(function(e){return e.id===t})},e.prototype.genId=function(e,t){var i=this.bind("genId");if(i){var n=i(e,t);if(null!=n)return n}return this.genIdDefault(e,t)},e.prototype.genIdDefault=function(e,t){if(!this.isCollectionIdNumeric(e,t))throw new Error("Collection '"+t+"' id type is non-numeric or unknown. Can only generate numeric ids.");var i=0;return e.reduce(function(e,t){i=Math.max(i,"number"==typeof t.id?t.id:i)},void 0),i+1},e.prototype.get=function(e){var t=e.collection,i=e.collectionName,n=e.headers,a=e.id,o=e.query,r=e.url,s=t;return null!=a&&""!==a?s=this.findById(t,a):o&&(s=this.applyQuery(t,o)),s?{body:this.bodify(this.clone(s)),headers:n,status:200}:this.createErrorResponseOptions(r,404,"'"+i+"' with id='"+a+"' not found")},e.prototype.getLocation=function(e){if(!e.startsWith("http")){var t="undefined"==typeof document?void 0:document,i=t?t.location.protocol+"//"+t.location.host:"http://fake";e=e.startsWith("/")?i+e:i+"/"+e}return function(e){for(var t=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e),i={source:"",protocol:"",authority:"",userInfo:"",user:"",password:"",host:"",port:"",relative:"",path:"",directory:"",file:"",query:"",anchor:""},n=Object.keys(i),a=n.length;a--;)i[n[a]]=t[a]||"";return i}(e)},e.prototype.getPassThruBackend=function(){return this.passThruBackend?this.passThruBackend:this.passThruBackend=this.createPassThruBackend()},e.prototype.getRequestInfoUtils=function(){var e=this;return{createResponse$:this.createResponse$.bind(this),findById:this.findById.bind(this),isCollectionIdNumeric:this.isCollectionIdNumeric.bind(this),getConfig:function(){return e.config},getDb:function(){return e.db},getJsonBody:this.getJsonBody.bind(this),getLocation:this.getLocation.bind(this),getPassThruBackend:this.getPassThruBackend.bind(this),parseRequestUrl:this.parseRequestUrl.bind(this)}},e.prototype.indexOf=function(e,t){return e.findIndex(function(e){return e.id===t})},e.prototype.parseId=function(e,t,i){if(!this.isCollectionIdNumeric(e,t))return i;var n=parseFloat(i);return isNaN(n)?i:n},e.prototype.isCollectionIdNumeric=function(e,t){return!(!e||!e[0])&&"number"==typeof e[0].id},e.prototype.parseRequestUrl=function(e){try{var t=this.getLocation(e),i=this.config.rootPath.length,n="";t.host!==this.config.host&&(i=1,n=t.protocol+"//"+t.host+"/");var a=t.path.substring(i).split("/"),o=0,r=void 0;null==this.config.apiBase?r=a[o++]:o=(r=this.config.apiBase.trim().replace(/\/$/,""))?r.split("/").length:0;var s=a[o++];return{apiBase:r+="/",collectionName:s=s&&s.split(".")[0],id:a[o++],query:this.createQueryMap(t.query),resourceUrl:n+r+s+"/"}}catch(l){throw new Error("unable to parse url '"+e+"'; original error: "+l.message)}},e.prototype.post=function(e){var t=e.collection,i=e.collectionName,n=e.headers,a=e.id,o=e.resourceUrl,r=e.url,s=this.clone(this.getJsonBody(e.req));if(null==s.id)try{s.id=a||this.genId(t,i)}catch(d){var l=d.message||"";return/id type is non-numeric/.test(l)?this.createErrorResponseOptions(r,422,l):(console.error(d),this.createErrorResponseOptions(r,500,"Failed to generate new id for '"+i+"'"))}if(a&&a!==s.id)return this.createErrorResponseOptions(r,400,"Request id does not match item.id");var c=this.indexOf(t,a=s.id),p=this.bodify(s);return-1===c?(t.push(s),n.set("Location",o+"/"+a),{headers:n,body:p,status:201}):this.config.post409?this.createErrorResponseOptions(r,409,"'"+i+"' item with id='"+a+" exists and may not be updated with POST; use PUT instead."):(t[c]=s,this.config.post204?{headers:n,status:204}:{headers:n,body:p,status:200})},e.prototype.put=function(e){var t=e.collection,i=e.collectionName,n=e.headers,a=e.id,o=e.url,r=this.clone(this.getJsonBody(e.req));if(null==r.id)return this.createErrorResponseOptions(o,404,"Missing '"+i+"' id");if(a&&a!==r.id)return this.createErrorResponseOptions(o,400,"Request for '"+i+"' id does not match item.id");var s=this.indexOf(t,a=r.id),l=this.bodify(r);return s>-1?(t[s]=r,this.config.put204?{headers:n,status:204}:{headers:n,body:l,status:200}):this.config.put404?this.createErrorResponseOptions(o,404,"'"+i+"' item with id='"+a+" not found and may not be created with PUT; use POST instead."):(t.push(r),{headers:n,body:l,status:201})},e.prototype.removeById=function(e,t){var i=this.indexOf(e,t);return i>-1&&(e.splice(i,1),!0)},e.prototype.resetDb=function(e){var t=this;this.dbReadySubject.next(!1);var i=this.inMemDbService.createDb(e);return(i instanceof Ze.a?i:"function"==typeof i.then?Object(Je.a)(i):Object(Be.a)(i)).pipe(Object(Ye.a)()).subscribe(function(e){t.db=e,t.dbReadySubject.next(!0)}),this.dbReady},e}(),Qe=i("lJxs"),et=(Fe=function(e,t){return(Fe=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])})(e,t)},function(e,t){function i(){this.constructor=e}Fe(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),tt=function(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)},it=function(e,t){return function(i,n){t(i,n,e)}},nt=function(e){function t(t,i,n){var a=e.call(this,t,i)||this;return a.xhrFactory=n,a}return et(t,e),t.prototype.handle=function(e){try{return this.handleRequest(e)}catch(i){var t=this.createErrorResponseOptions(e.url,500,""+(i.message||i));return this.createResponse$(function(){return t})}},t.prototype.getJsonBody=function(e){return e.body},t.prototype.getRequestMethod=function(e){return(e.method||"get").toLowerCase()},t.prototype.createHeaders=function(e){return new o.e(e)},t.prototype.createQueryMap=function(e){var t=new Map;if(e){var i=new o.f({fromString:e});i.keys().forEach(function(e){return t.set(e,i.getAll(e))})}return t},t.prototype.createResponse$fromResponseOptions$=function(e){return e.pipe(Object(Qe.a)(function(e){return new o.g(e)}))},t.prototype.createPassThruBackend=function(){try{return new o.h(this.xhrFactory)}catch(e){throw e.message="Cannot create passThru404 backend; "+(e.message||""),e}},(t=function(e,t,i,n){var a,o=arguments.length,r=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,i,n);else for(var s=e.length-1;s>=0;s--)(a=e[s])&&(r=(o<3?a(r):o>3?a(t,i,r):a(t,i))||r);return o>3&&r&&Object.defineProperty(t,i,r),r}([it(1,Object(l.s)(ze)),it(1,Object(l.H)()),tt("design:paramtypes",[Ge,$e,o.i])],t)).\u0275fac=function(e){return new(e||t)(l.ec(Ge),l.ec(ze,8),l.ec(o.i))},t.\u0275prov=l.Qb({token:t,factory:function(e){return t.\u0275fac(e)}}),t}(Ke);function at(e,t,i){return new nt(e,t,i)}!function(){function e(){}var t;t=e,e.forRoot=function(e,i){return{ngModule:t,providers:[{provide:Ge,useClass:e},{provide:ze,useValue:i},{provide:o.b,useFactory:at,deps:[Ge,ze,o.i]}]}},e.forFeature=function(e,i){return t.forRoot(e,i)},e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=l.Sb({type:e}),e.\u0275inj=l.Rb({})}();var ot=function(){function e(){}var t;return t=e,e.forRoot=function(e,i){return{ngModule:t,providers:[{provide:Ge,useClass:e},{provide:ze,useValue:i},{provide:o.b,useFactory:at,deps:[Ge,ze,o.i]}]}},e.forFeature=function(e,i){return t.forRoot(e,i)},e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=l.Sb({type:e}),e.\u0275inj=l.Rb({}),e}();class rt{createDb(){return{invoice:[{id:1,number:"#INV-0001",client:"Barry Cuda",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",invoice_date:"11-03-2019",due_date:"20-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:2,number:"#INV-0002 ",client:"Shooshi",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",invoice_date:"11-03-2019",due_date:"20-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:3,number:"#INV-0003",client:"Kenneth",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",invoice_date:"10-03-2019",due_date:"20-04-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:4,number:"#INV-0004",client:"Barry Cuda",project:"Office Management",email:"<EMAIL>",tax:"VAT",client_address:"Florida",billing_address:"Washington",invoice_date:"11-03-2019",due_date:"20-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:5,number:"#INV-0005",client:"Denver",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Vegas",invoice_date:"10-03-2019",due_date:"21-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:6,number:"#INV-0006",client:"John",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",invoice_date:"11-04-2019",due_date:"20-06-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"}],contacts:[{name:"John Doe",role:"Web Developer",type:"company",number:"9834553448",email:"<EMAIL>",id:121},{name:"Richard Miles",role:"React Developer",type:"client",number:"9834573448",email:"<EMAIL>",id:122},{name:"John Smith",role:"Angular Developer",type:"staff",number:"9834593448",email:"<EMAIL>",id:124},{name:"Mike Litorus",role:"Web Developer",type:"company",number:"9834053448",email:"<EMAIL>",id:125},{name:"Wilmer Deluna",role:"Front End Developer",type:"client",number:"9835553448",email:"<EMAIL>",id:126},{name:"Jeffrey Warden",role:"Back End Developer",type:"company",number:"6834553448",email:"<EMAIL>",id:127},{name:"Loren Gatlin",role:"Web Developer",type:"staff",number:"9834552348",email:"<EMAIL>",id:128},{name:"Lesley Grauer",role:"Android Developer",type:"company",number:"9834233448",email:"<EMAIL>",id:129}],clients:[{name:"Barry Cuda",role:"CEO",company:"Global Technologies",image:"avatar-19",clientId:"CLT-0008",email:"<EMAIL>",phone:"**********",status:"Active",id:1},{name:"Tressa Wexler",role:"Manager",company:"Delta Infotech",image:"avatar-29",clientId:"CLT-0003",email:"<EMAIL>",phone:"9876543211",status:"Inactive",id:2},{name:"Ruby Bartlett ",role:"CEO",company:"Cream Inc",image:"avatar-07",clientId:"CLT-0002",email:"<EMAIL>",phone:"9876543212",status:"Inactive",id:3},{name:"misty Tison",role:"CEO",company:"Wellware Company",image:"avatar-06",clientId:"CLT-0001",email:"<EMAIL>",phone:"9876543213",status:"Inactive",id:4},{name:"Daniel Deacon",role:"CEO",company:"Mustang Technologies",image:"avatar-14",clientId:"CLT-0006",email:"<EMAIL>",phone:"9876543214",status:"Active",id:5},{name:"Walter  Weaver",role:"CEO",company:"International Software",image:"avatar-18",clientId:"CLT-0007",email:"<EMAIL>",phone:"9876543215",status:"Active",id:6},{name:"Amanda Warren",role:"CEO",company:"Mercury Software Inc",image:"avatar-28",clientId:"CLT-0005",email:"<EMAIL>",phone:"9876543216",status:"Active",id:7},{name:"Bretty Carlson",role:"CEO",company:"Carlson Technologies",image:"avatar-13",clientId:"CLT-0004",email:"<EMAIL>",phone:"9876543217",status:"Inactive",id:8},{name:"Barry Cuda",role:"CEO",company:"Global Technologies",image:"avatar-19",clientId:"CLT-0008",email:"<EMAIL>",phone:"**********",status:"Active",id:9},{name:"Walter  Weaver",role:"CEO",company:"International Software",image:"avatar-18",clientId:"CLT-0007",email:"<EMAIL>",phone:"9876543215",status:"Active",id:10}],projects:[{name:"Office Management",description:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",endDate:"17-04-2019",startDate:"17-04-2019",priority:"High",projectleader:"Aravind",teamMember:"Prakash",projectId:"PRO-001",id:1},{name:"Hospital Administration",description:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",endDate:"17-04-2019",startDate:"17-04-2019",priority:"High",projectleader:"Ashok",teamMember:"Aravind",projectId:"PRO-001",id:2},{name:"Project Management",description:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",endDate:"17-08-2019",startDate:"17-07-2019",priority:"High",projectleader:"vijay",teamMember:"prakash",projectId:"PRO-001",id:3},{name:"Video Calling App",description:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",endDate:"17-04-2019",startDate:"17-03-2019",priority:"High",projectleader:"Ashok",teamMember:"Aravind",projectId:"PRO-001",id:4},{name:"Project Management",description:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",endDate:"17-08-2019",startDate:"17-07-2019",priority:"High",projectleader:"vijay",teamMember:"prakash",projectId:"PRO-001",id:5},{name:"Office Management",description:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",endDate:"17-04-2019",startDate:"17-04-2019",priority:"High",projectleader:"Aravind",teamMember:"Prakash",projectId:"PRO-001",id:6}],leaders:[{name:"Wilmer deluna",id:1},{name:"John Doe",id:2},{name:"Wilmer deluna",id:2},{name:"Richard Miles",id:2},{name:"Mike Litorus",id:2}],employeepage:[{firstname:"John Doe",designation:"Web Developer",id:1},{firstname:"Richard Miles",designation:"Web Developer",id:2},{firstname:"John Smith",designation:"Web Developer",id:3},{firstname:"Mike Litorus",designation:"Web Developer",id:4},{firstname:"Wilmer Deluna",designation:"Team Leader",id:5},{firstname:"Jeffrey Warden",designation:"Web Developer",id:6},{firstname:"Bernardo Galaviz",designation:"Web Developer",id:7},{firstname:"Lesley Grauer",designation:"Team Leader",id:8},{firstname:"Jeffery Lalor",designation:"Team Leader",id:9},{firstname:"Loren Gatlin",designation:"Android  Developer",id:10},{firstname:"Tarah Shropshire",designation:"Android  Developer",id:11},{firstname:"Catherine Manseau",designation:"Web  Developer",id:12}],employeelist:[{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:1},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Front end Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:2},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"UI/Ux Designer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-05-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:3},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:4},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:5},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:6},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:7},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:8},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:9},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:10},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:11},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:12},{firstname:"Catherine Manseau",lastname:"Manseau",username:"Manseau",password:"123445",confirmpassword:"123456",department:"software",designation:"Web  Developer",phone:"9842354254",email:"<EMAIL>",mobile:"**********",joindate:"18-04-2013",role:"Web Developer",employeeId:"FT-0001",company:"FT-0001",id:13}],holidays:[{id:1,title:"New Year",holidaydate:"01-01-2020",day:"sun day"},{id:2,title:"Diwali",holidaydate:"28-02-2020",day:"Thursday "},{id:3,title:"Christmas",holidaydate:"28-02-2020",day:"Friday"},{id:4,title:"Ramzon",holidaydate:"17-02-2020",day:"sun day"},{id:5,title:"Bakrid",holidaydate:"15-09-2020",day:"Saturday"}],adminleaves:[{id:1,employeeName:"John Doe",designation:"web developer",leaveType:"Casual Leave",from:"08-09-2019",to:"11-09-2019",noofDays:"2 days",remainleaves:"12",reason:"Going to Hospital",status:"New"},{id:2,employeeName:"John Smith",designation:"web developer",leaveType:"LOP",from:"08-09-2019",to:"11-09-2019",noofDays:"2 days",remainleaves:"4",reason:"Personnal",status:"Approved"},{id:3,employeeName:"Mike Litorus",designation:"Android developer",leaveType:"Paternity Leave",from:"13-02-2019",to:"17-02-2019",noofDays:"5 days",remainleaves:"10",reason:"Personnal",status:"Declined"},{id:4,employeeName:"Mike Litorus",designation:"web developer",leaveType:"Paternity Leave",from:"13-05-2019",to:"17-05-2019",noofDays:"5 days",remainleaves:"6",reason:"Medical leave",status:"Declined"},{id:5,employeeName:"Catherine Manseau",designation:"web designer",leaveType:"Casual Leave",from:"13-04-2019",to:"17-06-2019",noofDays:"5 days",remainleaves:"7",reason:"Going to Hospital",status:"Approved"},{id:6,employeeName:"Mike Litorus",designation:"web developer",leaveType:"Paternity Leave",from:"13-05-2019",to:"17-05-2019",noofDays:"5 days",remainleaves:"6",reason:"Medical leave",status:"Declined"},{id:7,employeeName:"John Smith",designation:"web developer",leaveType:"LOP",from:"08-09-2019",to:"11-09-2019",noofDays:"2 days",remainleaves:"4",reason:"Personnal",status:"Approved"}],leads:[{leadName:"Wilmer Deluna",email:"<EMAIL>",phone:"**********",project:"Hospital Administration",status:"Working",created:"10 hours ago",id:1},{leadName:"Lesley Grauer",email:"<EMAIL>",phone:"**********",project:"Video Calling App",status:"Working",created:"5 Mar 2019",id:2},{leadName:"Jeffery Lalor",email:"<EMAIL>",phone:"**********",project:"Office Management",status:"Working",created:"27 Feb 2019",id:3},{leadName:"Lesley Grauer",email:"<EMAIL>",phone:"**********",project:"Video Calling App",status:"Working",created:"5 Mar 2019",id:4},{leadName:"Jeffery Lalor",email:"<EMAIL>",phone:"**********",project:"Office Management",status:"Working",created:"27 Feb 2019",id:5},{leadName:"Wilmer Deluna",email:"<EMAIL>",phone:"**********",project:"Hospital Administration",status:"Working",created:"10 hours ago",id:6},{leadName:"Wilmer Deluna",email:"<EMAIL>",phone:"**********",project:"Hospital Administration",status:"Working",created:"10 hours ago",id:7},{leadName:"Lesley Grauer",email:"<EMAIL>",phone:"**********",project:"Video Calling App",status:"Working",created:"5 Mar 2019",id:8},{leadName:"Jeffery Lalor",email:"<EMAIL>",phone:"**********",project:"Office Management",status:"Working",created:"27 Feb 2019",id:9}],tickets:[{ticketId:"#TKT-001",ticketSubject:"Laptop Issue",assignedStaff:"John Smith",client:"Delta Infotech",priority:"Low",cc:"ashok",assigne:"prakash",addfollow:"tested",description:"tested",createdDate:"05-05-2020",lastReply:"06-05-2020",status:"Approved",id:1},{ticketId:"#TKT-002",ticketSubject:"Laptop Issue",assignedStaff:"Mark Hentry",client:"International software Inc",priority:"High",cc:"ashok",assigne:"prakash",addfollow:"tested",description:"tested",createdDate:"05-05-2020",lastReply:"06-05-2020",status:"Pending",id:2},{ticketId:"#TKT-003",ticketSubject:"Mouse Issue",assignedStaff:"Mikel deo",client:"International software Inc",priority:"High",cc:"ashok",assigne:"prakash",addfollow:"tested",description:"tested",createdDate:"05-05-2020",lastReply:"06-05-2020",status:"Pending",id:3},{ticketId:"#TKT-004",ticketSubject:"Monitor Issue",assignedStaff:"Richared Deo",client:"International software Inc",priority:"High",cc:"ashok",assigne:"prakash",addfollow:"tested",description:"tested",createdDate:"05-05-2020",lastReply:"06-05-2020",status:"Pending",id:4}],employeeleaves:[{id:1,employeeName:"John Doe",designation:"web developer",leaveType:"Casual Leave",from:"08-03-2019",to:"09-04-2019",noofDays:"2 days",remainleaves:"12",reason:"Going to Hospital",status:"New"},{id:2,employeeName:"John Smith",designation:"web developer",leaveType:"LOP",from:"24-02-2019",to:"25-02-2019",noofDays:"2 days",remainleaves:"4",reason:"Personnal",status:"Approved"},{id:3,employeeName:"Mike Litorus",designation:"Android developer",leaveType:"Paternity Leave",from:"13-02-2019",to:"17-02-2019",noofDays:"5 days",remainleaves:"10",reason:"Personnal",status:"Declined"},{id:4,employeeName:"Mike Litorus",designation:"web developer",leaveType:"Paternity Leave",from:"13-02-2019",to:"17-02-2019",noofDays:"5 days",remainleaves:"6",reason:"Medical leave",status:"Declined"},{id:5,employeeName:"Catherine Manseau",designation:"web designer",leaveType:"Casual Leave",from:"13-02-2019",to:"17-02-2019",noofDays:"5 days",remainleaves:"7",reason:"Going to Hospital",status:"Approved"},{id:6,employeeName:"Mike Litorus",designation:"web developer",leaveType:"Paternity Leave",from:"13-02-2019",to:"17-02-2019",noofDays:"5 days",remainleaves:"6",reason:"Medical leave",status:"Declined"},{id:7,employeeName:"John Smith",designation:"web developer",leaveType:"LOP",from:"13-02-2019",to:"17-02-2019",noofDays:"2 days",remainleaves:"4",reason:"Personnal",status:"Approved"}],departments:[{id:1,departmentName:"Web Development"},{id:2,departmentName:"Application Development"},{id:3,departmentName:"IT Management"},{id:4,departmentName:"Accounts Development"},{id:5,departmentName:"Support Management"},{id:6,departmentName:"Marketing"}],designation:[{id:1,designation:"Web Designer",departmentName:"Web Development"},{id:2,designation:"Web Developer",departmentName:"Web Development"},{id:3,designation:"Android Developer",departmentName:"Application Development"},{id:4,designation:"IOS Developer",departmentName:"Application Development"},{id:5,designation:"UI Designer",departmentName:"Application Development"},{id:6,designation:"IT Technician",departmentName:"Application Development"},{id:7,designation:"Product Manager",departmentName:"Application Development"},{id:8,designation:"SEO Analyst",departmentName:"Application Development"},{id:9,designation:"Front End Designer",departmentName:"Web Development"}],timesheet:[{id:1,employee:"Bernardo Galaviz",designation:"Web developer",date:"8 Mar 2019",deadline:"",project:"Video Calling App",assignedhours:"20",hrs:"12",description:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque"},{id:2,employee:" Catherine Manseau",designation:"Android developer",date:"9 Mar 2019",deadline:"",totalhrs:"",remainHrs:"",project:"Video Calling App",assignedhours:"20",hrs:"12",description:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque"},{id:3,employee:"Jeffry lalor Galaviz",designation:"Android developer",date:"10 Mar 2019",deadline:"",totalhrs:"",project:"Video Calling App",assignedhours:"20",hrs:"12",description:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque"},{id:4,employee:"Jeffry Warden",designation:"Web developer",date:"11 Mar 2019",deadline:"",totalhrs:"",remainHrs:"",project:"Video Calling App",assignedhours:"20",hrs:"12",description:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque"},{id:5,employee:"John doe Galaviz",designation:"Web developer",date:"13 Mar 2019",deadline:"",totalhrs:"",remainHrs:"",project:"Video Calling App",assignedhours:"20",hrs:"12",description:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque"}],overtime:[{id:1,name:"Bernardo Galaviz",otDate:"08-03-2019",otHrs:"04",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:2,name:"John Deo",otDate:"25-04-2019",otHrs:"07",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:3,name:"Russia david",otDate:"12-09-2019",otHrs:"09",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:4,name:"Mark hentry",otDate:"15-10-2019",otHrs:"02",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:5,name:"Ruchared hentry",otDate:"23-04-2019",otHrs:"04",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:6,name:"Mark rio",otDate:"11-07-2019",otHrs:"05",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:7,name:"John Galaviz",otDate:"25-08-2019",otHrs:"08",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:8,name:"Loren Gatlin",otDate:"05-01-2019",otHrs:"5",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:9,name:"Tarah Shropshire",otDate:"05-01-2019",otHrs:"5",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:10,name:"John Doe",otDate:"13-01-2019",otHrs:"5",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:11,name:"John Smith",otDate:"20-01-2019",otHrs:"5",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"},{id:12,name:"John Smith",otDate:"20-01-2019",otHrs:"5",otType:"Normal day OT 1.5x",status:"New",approvedBy:"Richard Miles",description:"Lorem ipsum dollar"}],expenses:[{item:"Dell Laptop",purchaseFrom:"Amazon",purchaseDate:"05-01-2019",purchasedBy:"Loren Gatlin",amount:"$1215",paidby:"Cash",id:1},{item:"Mac System",purchaseFrom:"Amazon",purchaseDate:"05-01-2019",purchasedBy:"Tarah Shropshire",amount:"$1215",paidby:"Cheque",id:2},{item:"Apple",purchaseFrom:"Amazon",purchaseDate:"05-01-2019",purchasedBy:"John Doe",amount:"$1215",paidby:"Cheque",id:3},{item:"HCL",purchaseFrom:"Amazon",purchaseDate:"01-01-2019",purchasedBy:"John Doe",amount:"$1215",paidby:"Cheque",id:4},{item:"HCL",purchaseFrom:"Flipkart",purchaseDate:"01-01-2019",purchasedBy:"Loren Gatlin",amount:"$1215",paidby:"Cheque",id:5},{item:"Sony",purchaseFrom:"Flipkart",purchaseDate:"20-01-2019",purchasedBy:"Loren Mac",amount:"$1215",paidby:"Cheque",id:6}],providentFund:[{employeeName:"Loren Mac",providentFundType:"Percentage of Basic Salary",employeeShare:"2%",organizationShare:"2%",id:1},{employeeName:"John Doe",providentFundType:"Percentage of Basic Salary",employeeShare:"4%",organizationShare:"5%",id:2},{employeeName:"Tarah Shropshire",providentFundType:"Percentage of Basic Salary",employeeShare:"9%",organizationShare:"5%",id:3},{employeeName:"John Doe",providentFundType:"Percentage of Basic Salary",employeeShare:"9%",organizationShare:"2%",id:4},{employeeName:"John michelin",providentFundType:"Percentage of Basic Salary",employeeShare:"4%",organizationShare:"2%",id:5},{employeeName:"Kennedy michelin",providentFundType:"Percentage of Basic Salary",employeeShare:"2%",organizationShare:"2%",id:6}],goallist:[{id:1,goalType:"Event Goal",subject:"Test Goal",targetAchivement:"Lorem ipsum dollar",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",status:"Active",progress:"Completed 73%"},{id:2,goalType:"Event Goal",subject:"Employee Goal",targetAchivement:"Lorem ipsum dollar",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",status:"Inactive",progress:"Completed 73%"},{id:3,goalType:"Event Goal",subject:"Invoice Goal",targetAchivement:"Lorem ipsum dollar",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",status:"Inactive",progress:"Completed 43%"},{id:4,goalType:"Event Goal",subject:"Project Goal",targetAchivement:"Lorem ipsum dollar",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",status:"Active",progress:"Completed 53%"}],goaltype:[{id:1,type:"Event goal",description:"Event goal\tLorem ipsum dollar",status:"active"},{id:2,type:"Project goal",description:"Lorem ipsum dollar",status:"Inactive"},{id:3,type:"Event goal",description:"Lorem ipsum dollar",status:"active"},{id:4,type:"Invoice goal",description:"Event goal tested",status:"active"},{id:5,type:"Project goal",description:"Lorem ipsum dollar",status:"Inactive"},{id:6,type:"Event goal",description:"Event goal\tLorem ipsum dollar",status:"active"}],trainingtype:[{id:1,type:"Event goal",description:"Event goal\tLorem ipsum dollar",status:"active"},{id:2,type:"Project goal",description:"Lorem ipsum dollar",status:"Inactive"},{id:3,type:"Event goal",description:"Lorem ipsum dollar",status:"active"},{id:4,type:"Invoice goal",description:"Event goal tested",status:"active"},{id:5,type:"Project goal",description:"Lorem ipsum dollar",status:"Inactive"},{id:6,type:"Project goal",description:"Lorem ipsum dollar",status:"Inactive"}],trainers:[{id:1,name:"John Doe",lname:"Doe",role:"developer",contactNumber:"**********",mail:"<EMAIL>",description:"Lorem ipsum dollar",status:"active"},{id:2,name:"Mike Litorus",lname:"Litorus",role:"developer",contactNumber:"9876543120",mail:"<EMAIL>",description:"Lorem ipsum dollar",status:"active"},{id:3,name:"Wilmer Deluna",lname:"Deluna",role:"developer",contactNumber:"**********",mail:"<EMAIL>",description:"Lorem ipsum dollar",status:"active"},{id:4,name:"John Smith",lname:"Smith",role:"developer",contactNumber:"**********",mail:"<EMAIL>",description:"Lorem ipsum dollar",status:"active"},{id:5,name:"Richard Milesh",lname:"Milesh",role:"developer",contactNumber:"**********",mail:"<EMAIL>",description:"Lorem ipsum dollar",status:"active"}],traininglist:[{id:1,trainingType:"Node Training",trainer:"John Doe",employee:"",timeDuration:"7 May 2019 - 10 May 2019",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",cost:"$450",status:"active"},{id:2,trainingType:"Git Training",trainer:"John Doe",employee:"",timeDuration:"7 May 2019 - 10 May 2019",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",cost:"$450",status:"active"},{id:3,trainingType:"Angular Training",trainer:"John Doe",employee:"",timeDuration:"7 May 2019 - 10 May 2019",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",cost:"$450",status:"active"},{id:4,trainingType:"Swift Training",trainer:"John Doe",employee:"",timeDuration:"7 May 2019 - 10 May 2019",startDate:"07-05-2019",endDate:"10-05-2019",description:"Lorem ipsum dollar",cost:"$450",status:"active"}],promotionmain:[{id:1,employee:"John Doe",department:"Web development",designation:"Web Design",promotionFrom:"Web developer",promotionTo:"Sr.Web developer",promotionDate:"28-09-2019"},{id:2,employee:"John Doe",department:"Web development",designation:"Web Design",promotionFrom:"Web developer",promotionTo:"Sr.Web developer",promotionDate:"28-09-2019"},{id:3,employee:"John Doe",department:"Web development",designation:"Web Design",promotionFrom:"Web developer",promotionTo:"Sr.Web developer",promotionDate:"28-09-2019"},{id:4,employee:"John Doe",department:"Web development",designation:"Web design",promotionFrom:"Web developer",promotionTo:"Sr.Web developer",promotionDate:"28-09-2019"}],resignationmain:[{id:1,employee:"John Doe",department:"Web development",reason:"tested",noticedDate:"28-02-2019",resignDate:"28-03-2019"},{id:2,employee:"Russia smith",department:"Web development",reason:"tested",noticedDate:"28-02-2019",resignDate:"28-03-2019"},{id:3,employee:"Richared deo",department:"Web development",reason:"tested",noticedDate:"28-02-2019",resignDate:"28-03-2019"},{id:4,employee:"Mark hentry",department:"Web development",reason:"tested",noticedDate:"28-02-2019",resignDate:"28-03-2019"}],terminationmain:[{id:1,employee:"Richared dio",department:"Web development",terminationType:"Misconduct",terminationDate:"28-10-2019",reason:"Lorem Ipsum Dollar",noticedDate:"28-03-2019"},{id:2,employee:"Mikel Rio",department:"Web development",terminationType:"Others",terminationDate:"28-02-2019",reason:"Lorem Ipsum Dollar",noticedDate:"28-03-2019"},{id:3,employee:"John smith",department:"Web development",terminationType:"Others",terminationDate:"18-05-2019",reason:"Lorem Ipsum Dollar",noticedDate:"28-03-2019"},{id:4,employee:"Russia hentry",department:"Web development",terminationType:"Misconduct",terminationDate:"28-08-2019",reason:"Lorem Ipsum Dollar",noticedDate:"28-03-2019"},{id:5,employee:"Jackson feioz",department:"Web development",terminationType:"Others",terminationDate:"08-09-2019",reason:"Lorem Ipsum Dollar",noticedDate:"28-03-2019"},{id:6,employee:"John Doe",department:"Web development",terminationType:"Misconduct",terminationDate:"27-10-2019",reason:"Lorem Ipsum Dollar",noticedDate:"28-03-2019"}],taxes:[{taxName:"VAT",taxPercentage:"14%",id:1},{taxName:"GST",taxPercentage:"30%",id:2},{taxName:"GST",taxPercentage:"30%",id:3},{taxName:"VAT",taxPercentage:"10%",id:4},{taxName:"GST",taxPercentage:"25%",id:5},{taxName:"VAT",taxPercentage:"20%",id:6}],policies:[{policyName:"Leave Policy",department:"All Departments",description:"Lorem ipsum dollar",createdDate:"19 Feb 2019",id:1},{policyName:"Permission Policy",department:"Marketing",description:"Lorem ipsum dollar",createdDate:"18 Feb 2019",id:2},{policyName:"Leave Policy",department:"All Departments",description:"Lorem ipsum dollar",createdDate:"19 Feb 2019",id:3},{policyName:"Permission Policy",department:"Marketing",description:"Lorem ipsum dollar",createdDate:"25 Feb 2019",id:4},{policyName:"Leave Policy",department:"All Departments",description:"Lorem ipsum dollar",createdDate:"18 Feb 2019",id:5},{policyName:"Permission Policy",department:"Marketing",description:"Lorem ipsum dollar",createdDate:"22 Feb 2019",id:6}],expenseReport:[{item:"Dell Laptop",purchaseFrom:"Amazon",purchaseDate:"12-01-2019",purchasedBy:"Loren Gatlin",amount:"$ 1210",paidBy:"Cash",id:1},{item:"Mac System",purchaseFrom:"Amazon",purchaseDate:"10-01-2019",purchasedBy:"Tarah Shropshire",amount:"$ 1215",paidBy:"Cheque",id:2},{item:"Dell Laptop",purchaseFrom:"Snap",purchaseDate:"24-01-2019",purchasedBy:"kenneth",amount:"$ 1205",paidBy:"Cash",id:3},{item:"Adobe System",purchaseFrom:"Orion",purchaseDate:"11-01-2019",purchasedBy:"John Doe",amount:"$ 134",paidBy:"Cheque",id:4},{item:"Adobe System",purchaseFrom:"Amazon",purchaseDate:"08-01-2019",purchasedBy:"John Michellin",amount:"$ 12",paidBy:"Cheque",id:5},{item:"Adobe System",purchaseFrom:"Flip",purchaseDate:"10-01-2019",purchasedBy:"Arnold",amount:"$ 121",paidBy:"Cheque",id:6}],appliedCandidates:[{name:"\tJohn Doe",email:"<EMAIL>",phone:"**********",applyDate:"9 Feb 2019",id:1},{name:"Arnold",email:"<EMAIL>",phone:"9872543210",applyDate:"25 Mar 2019",id:2},{name:"kenneth",email:"<EMAIL>",phone:"9876543230",applyDate:"13 Feb 2019",id:3},{name:"Sam",email:"<EMAIL>",phone:"9876543297",applyDate:"25 Jan 2019",id:4},{name:"Michellin",email:"<EMAIL>",phone:"9876524210",applyDate:"26 Feb 2019",id:5},{name:"john Mckensey",email:"<EMAIL>",phone:"9876543410",applyDate:"18 Jun 2019",id:6}],knowledgeBase:[{title:"Installation & Activation",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:1},{title:"Premium Members Features",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:2},{title:"API Usage & Guide lines",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:3},{title:"Getting Started",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:4},{title:"Lorem ipsum dolor",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:5},{title:"Lorem ipsum dolor",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:6},{title:"Lorem ipsum dolor",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:7},{title:"Lorem ipsum dolor",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:8},{title:"Lorem ipsum dolor",list1:"Sed ut perspiciatis unde omnis?",list2:"Sed ut perspiciatis unde omnis?",list3:"Sed ut perspiciatis unde omnis?",list4:"Sed ut perspiciatis unde omnis?",list5:"Sed ut perspiciatis unde omnis?",id:9}],assets:[{assetUser:"Richard Miles",assetName:"Dell Laptop",assetId:"#AST-0001",assetStatus:"Pending",purchaseDate:"05-01-2019",warrenty:"12 Months",warrentyEnd:"05-01-2020",Amount:"$1215",id:1},{assetUser:"John Doe",assetName:"Seagate Harddisk",assetId:"#AST-0002",assetStatus:"Pending",purchaseDate:"14-01-2019",warrenty:"12 Months",warrentyEnd:"14-07-2019",Amount:"$300",id:2},{assetUser:"John Smith",assetName:"Canon Portable Printer",assetId:"#AST-0003",assetStatus:"Pending",purchaseDate:"14-01-2019",warrenty:"12 Months",warrentyEnd:"14-08-2019",Amount:"$2500",id:3},{assetUser:"Mike Litorus",assetName:"Dell Laptop",assetId:"#AST-0004",assetStatus:"Pending",purchaseDate:"05-01-2019",warrenty:"12 Months",warrentyEnd:"05-01-2020",Amount:"$1215",id:4},{assetUser:"Wilmer Deluna",assetName:"Seagate Harddisk",assetId:"#AST-0005",assetStatus:"Pending",purchaseDate:"14-01-2019",warrenty:"12 Months",warrentyEnd:"14-01-2020",Amount:"$300",id:5},{assetUser:"Jeffrey Warden",assetName:"Canon Portable Printer",assetId:"#AST-0006",assetStatus:"Pending",purchaseDate:"14-01-2019",warrenty:"12 Months",warrentyEnd:"14-01-2020",Amount:"$2500",id:6},{assetUser:"Bernardo Galaviz",assetName:"Dell Laptop",assetId:"#AST-0007",assetStatus:"Pending",purchaseDate:"05-01-2019",warrenty:"12 Months",warrentyEnd:"05-02-2020",Amount:"$1215",id:7},{assetUser:"Lesley Grauer",assetName:"Seagate Harddisk",assetId:"#AST-0008",assetStatus:"Pending",purchaseDate:"14-01-2019",warrenty:"12 Months",warrentyEnd:"14-01-2020",Amount:"$300",id:8},{assetUser:"Jeffery Lalor",assetName:"Canon Portable Printer",assetId:"#AST-0009",assetStatus:"Pending",purchaseDate:"14-01-2019",warrenty:"12 Months",warrentyEnd:"14-01-2020",Amount:"$2500",id:9},{assetUser:"Loren Gatlin",assetName:"Dell Laptop",assetId:"#AST-0010",assetStatus:"Pending",purchaseDate:"05-01-2019",warrenty:"12 Months",warrentyEnd:"05-01-2020",Amount:"$1215",id:10}],users:[{name:"\tBarryCuda",designation:"Android Developer",email:"<EMAIL>",company:"Global Technologies",role:"Client",id:1},{name:"\tJohn Doe ",designation:"Web Designer",email:"<EMAIL>",company:"Dreamguy's Technologies",role:"Employee",id:2},{name:"Richard Miles",designation:"Admin",email:"<EMAIL>",company:"Dreamguy's Technologies",role:"Employee",id:3},{name:"\tJohn Smith",designation:"Android Developer",email:"<EMAIL>",company:"Dreamguy's Technologies",role:"Employee",id:4},{name:"\tMike Litorus",designation:"IOS Developer",email:"<EMAIL>",company:"Dreamguy's Technologies",role:"Employee",id:5},{name:"Wilmer Deluna",designation:"Team Leader",email:"<EMAIL>",company:"Dreamguy's Technologies",role:"Employee",id:6},{name:"\tBarryCuda",designation:"Team Leader",email:"<EMAIL>",company:"Global Technologies",role:"Client",id:7}],payments:[{invoiceId:"#INV-0001",client:"Global Technologies",paymenttype:"Paypal",paidDate:"8 Feb 2019",paidAmount:"$500",id:1},{invoiceId:"#INV-0002",client:"Delta Infotech",paymenttype:"Paypal",paidDate:"9 Jan 2019",paidAmount:"$420",id:2},{invoiceId:"#INV-0003",client:"Savior Inc",paymenttype:"Paypal",paidDate:"8 Ma2019",paidAmount:"$600",id:3},{invoiceId:"#INV-0004",client:"Nata ltd",paymenttype:"Paypal",paidDate:"10 Jul 2019",paidAmount:"$410",id:4},{invoiceId:"#INV-0005",client:"Paypal",paymenttype:"Paypal",paidDate:"10 Dec 2019",paidAmount:"$250",id:5},{invoiceId:"#INV-0006",client:"Tell Inc",paymenttype:"Paypal",paidDate:"10 Apr 2019",paidAmount:"$300",id:6}],manageJobs:[{jobTitle:"Web Developer",department:"Development",startDate:"03-03-2019",expireDate:"11-05-2019",id:1},{jobTitle:"Web Designer",department:"Designing",startDate:"05-04-2019",expireDate:"21-05-2019",id:2},{jobTitle:"Android Developer",department:"Android",startDate:"03-08-2019",expireDate:"15-05-2019",id:3},{jobTitle:"Web Designer",department:"Designing",startDate:"03-09-2019",expireDate:"11-05-2019",id:4},{jobTitle:"Web Developer",department:"Development",startDate:"03-10-2019",expireDate:"31-05-2019",id:5},{jobTitle:"Android Developer",department:"Android",startDate:"03-12-2019",expireDate:"31-10-2019",id:6}],estimates:[{id:1,number:"EST-0001",client:"Barry Cuda",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",estimate_date:"11-03-2019",expiry_date:"20-05-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Accepted"},{id:2,number:"EST-0002",client:"Barry Cuda",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",estimate_date:"11-03-2019",expiry_date:"20-05-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Accepted"},{id:3,number:"EST-0003",client:"Joshy",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Florida",estimate_date:"11-04-2019",expiry_date:"13-05-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Accepted"},{id:4,number:"EST-0004",client:"Denver",project:"Office Management",email:"<EMAIL>",tax:"VAT",client_address:"Texas",billing_address:"Washington",estimate_date:"11-04-2019",expiry_date:"13-05-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Accepted"},{id:5,number:"EST-0006",client:"Kenneth",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",estimate_date:"10-04-2019",expiry_date:"20-10-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Decline"}],leaveType:[{leaveType:"Casual Leave",leaveDays:"12 Days",id:1},{leaveType:"Medical Leave",leaveDays:"12 Days",id:2},{leaveType:"Loss of Pay",leaveDays:"10 Days",id:3},{leaveType:"Medical Leave",leaveDays:"1 Days",id:4},{leaveType:"Casual Leave",leaveDays:"15 Days",id:5},{leaveType:"Loss of Pay",leaveDays:"10 Days",id:6}],performanceindicator:[{id:1,designation:"Web Designer",experience:"Beginner",integrirty:"",Marketing:"",professionalism:"",managementskill:"",teamwork:"",adminstartion:"",criticalthinking:"",presentationskil:"",conflictmanagement:"",qualityofwork:"",attendance:"",effientcy:"",meetdeadline:"",department:"Designing",addedBy:"John Doe",createdBy:"28 Feb 2019",status:"Active"},{id:2,designation:"Ios developer",experience:"Beginner",department:"Ios",integrirty:"",Marketing:"",professionalism:"",managementskill:"",teamwork:"",adminstartion:"",criticalthinking:"",presentationskil:"",conflictmanagement:"",qualityofwork:"",attendance:"",effientcy:"",meetdeadline:"",addedBy:"Mike Litorus",createdBy:"28 Feb 2019",status:"Active"},{id:3,designation:"Web developer",experience:"Beginner",department:"Web design",integrirty:"",Marketing:"",professionalism:"",managementskill:"",teamwork:"",adminstartion:"",criticalthinking:"",presentationskil:"",conflictmanagement:"",qualityofwork:"",attendance:"",effientcy:"",meetdeadline:"",addedBy:"John Smith",createdBy:"28 Feb 2019",status:"InActive"},{id:4,designation:"Web Designer",experience:"Beginner",department:"Web development",integrirty:"",Marketing:"",professionalism:"",managementskill:"",teamwork:"",adminstartion:"",criticalthinking:"",presentationskil:"",conflictmanagement:"",qualityofwork:"",attendance:"",effientcy:"",meetdeadline:"",addedBy:"Jeffrey Warden",createdBy:"28 Feb 2019",status:"Active"}],performanceappraisal:[{id:1,employee:"John deo",designation:"Web designer",apparaisaldate:"02-05-2020",department:"Web design",status:"Active"},{id:2,employee:"Mixcle Rao",designation:"Ios developer",apparaisaldate:"02-05-2020",department:"Web design",status:"Active"},{id:3,employee:"John rio",designation:"Web developer",apparaisaldate:"02-05-2020",department:"Web design",status:"Active"},{id:4,employee:"effrey Warden",designation:"Web Designer",apparaisaldate:"02-05-2020",department:"Web development",status:"Active"}],roles:[{roleName:"Administrator",id:1},{roleName:"CEO",id:2},{roleName:"Manager",id:3},{roleName:"Team Leader",id:4},{roleName:"Accountant",id:5},{roleName:"Web Developer",id:6},{roleName:"Web Designer",id:7},{roleName:"HR",id:8},{roleName:"UI/UX Developer",id:9},{roleName:"SEO Analyst",id:10}],payrollAddition:[{name:"Leave balance amount",category:"Monthly remuneration",unitCost:"5",id:1},{name:"Arrears of salary",category:"Additional remuneration",unitCost:"8",id:2},{name:"Gratuity",category:"Monthly remuneration",unitCost:"10",id:3},{name:"Arrears of salary",category:"Additional remuneration",unitCost:"10",id:4},{name:"Gratuity",category:"Monthly remuneration",unitCost:"20",id:5},{name:"Leave balance amount",category:"Additional remuneration",unitCost:"25",id:6}],payrollOvertime:[{name:"Normal day OT 1.5x",rate:"5",id:1},{name:"Public holiday OT 3.0x",rate:"13",id:2},{name:"Rest day OT 2.0x",rate:"20",id:3},{name:"Public holiday OT 3.0x",rate:"8",id:4},{name:"Normal day OT 1.5x",rate:"10",id:5},{name:"Public holiday OT 3.0x",rate:"10",id:6}],payrollDeduction:[{name:"Absent amount",unitCost:"5",id:1},{name:"Advance",unitCost:"10",id:2},{name:"Unpaid leave",unitCost:"20",id:3},{name:"Advance",unitCost:"8",id:4},{name:"Absent amount",unitCost:"21",id:5},{name:"Unpaid leave",unitCost:"20",id:6}],invoiceReport:[{id:1,number:"#INV-0001",client:"Global Technologies",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",invoice_date:"11-03-2019",due_date:"20-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:2,number:"#INV-0002",client:"Delta Technologies",project:"Office Management",email:"<EMAIL>",tax:"VAT",client_address:"Combodia",billing_address:"Washington",invoice_date:"11-02-2019",due_date:"21-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Pending"},{id:3,number:"#INV-0003",client:"Aura Technologies",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Newyork",billing_address:"Vegas",invoice_date:"14-03-2019",due_date:"21-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:4,number:"#INV-0004",client:"Mine Technologies",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Washington",invoice_date:"11-05-2019",due_date:"23-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:5,number:"#INV-0005",client:"Global Technologies",project:"Office Management",email:"<EMAIL>",tax:"VAT",client_address:"Texas",billing_address:"Arizona",invoice_date:"14-03-2019",due_date:"20-09-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"},{id:6,number:"#INV-0006",client:"Senzer Technologies",project:"Office Management",email:"<EMAIL>",tax:"GST",client_address:"Texas",billing_address:"Florida",invoice_date:"11-03-2019",due_date:"23-03-2019",items:[{item:"Item",description:"Description",unit_cost:"10",qty:10,amount:100}],totalamount:100,discount:5,grandTotal:100,other_information:"Description",status:"Paid"}],employeeSalary:[{employee:"Bernardo Galaviz",employeeId:"FT-0007",email:"<EMAIL>",joinDate:"14-01-2019",role:"Web Developer",employeeRole:"Employee",status:"pending",salary:"76670",Basic:"55300",tDS:"7500",da:"11820",hra:"4300",pf:"4300",conveyance:"5400",leave:"4400",allowance:"2600",proTax:"3050",medAllowance:"6500",labourWelfare:"3900",othersAdd:"100",othersDed:"200",esi:"200",id:1},{employee:"Jeffery Lalor",employeeId:"FT-0009",email:"<EMAIL>",joinDate:"05-01-2019",role:"Team Leader",employeeRole:"Employee",status:"pending",salary:"63670",Basic:"45300",tDS:"7500",da:"10820",hra:"5500",pf:"5500",conveyance:"4800",leave:"4400",allowance:"3200",proTax:"3050",medAllowance:"4500",labourWelfare:"3900",othersAdd:"100",othersDed:"200",esi:"200",id:2},{employee:"Jeffrey Warden",employeeId:"FT-0006",email:"<EMAIL>",joinDate:"02-01-2019",role:"Web Designer",employeeRole:"Employee",status:"pending",salary:"63140",Basic:"53300",tDS:"7500",da:"9320",hra:"3400",pf:"5500",conveyance:"3800",leave:"4400",allowance:"2300",proTax:"3020",medAllowance:"3500",labourWelfare:"1900",othersAdd:"100",othersDed:"200",esi:"200",id:3},{employee:"John Doe",employeeId:"FT-0001",email:"<EMAIL>",joinDate:"07-01-2019",role:"Android Developer",employeeRole:"Employee",status:"pending",salary:"54840",Basic:"43300",tDS:"3500",da:"4320",hra:"3500",pf:"5400",conveyance:"2800",leave:"2500",allowance:"3300",proTax:"4020",medAllowance:"3200",labourWelfare:"1800",othersAdd:"100",othersDed:"200",esi:"200",id:4},{employee:"John Smith",employeeId:"FT-0003",email:"<EMAIL>",joinDate:"17-01-2019",role:"Frontend Developer",employeeRole:"Employee",status:"pending",salary:"69960",Basic:"55300",tDS:"3000",da:"5060",hra:"4000",pf:"5400",conveyance:"3000",leave:"2400",allowance:"3400",proTax:"4000",medAllowance:"2800",labourWelfare:"3200",othersAdd:"100",othersDed:"200",esi:"200",id:5},{employee:"Lesley Grauer",employeeId:"FT-0008",email:"<EMAIL>",joinDate:"20-01-2019",role:"Ios Developer",employeeRole:"Employee",status:"pending",salary:"50000",Basic:"39300",tDS:"1000",da:"5000",hra:"5000",pf:"5800",conveyance:"4000",leave:"4000",allowance:"5000",proTax:"3500",medAllowance:"1800",labourWelfare:"2800",othersAdd:"100",othersDed:"200",esi:"200",id:6},{employee:"Loren Gatlin",employeeId:"FT-0010",email:"<EMAIL>",joinDate:"22-01-2019",role:"Software Engineer",employeeRole:"Employee",status:"pending",salary:"34900",Basic:"18000",tDS:"1000",da:"5000",hra:"6000",pf:"5000",conveyance:"3500",leave:"3000",allowance:"4000",proTax:"3000",medAllowance:"2000",labourWelfare:"2400",othersAdd:"100",othersDed:"200",esi:"200",id:7},{employee:"Mike Litorus",employeeId:"FT-0004",email:"<EMAIL>",joinDate:"23-01-2019",role:"Web Developer",employeeRole:"Employee",status:"pending",salary:"28700",Basic:"15000",tdS:"100",da:"5000",hra:"4000",pf:"3400",conveyance:"2500",leave:"2500",allowance:"3000",proTax:"1000",medAllowance:"2000",labourWelfare:"2200",othersAdd:"100",othersDed:"200",esi:"200",id:8},{employee:"Richard Miles",employeeId:"FT-0002",email:"<EMAIL>",joinDate:"31-01-2019",role:"Ui/Ux Developer",employeeRole:"Employee",status:"pending",salary:"20450",Basic:"13000",tDS:"500",da:"3000",hra:"3000",pf:"3600",conveyance:"1500",leave:"2000",allowance:"3000",proTax:"850",medAllowance:"1000",labourWelfare:"1200",othersAdd:"100",othersDed:"200",esi:"200",id:9},{employee:"Tarah Shropshire",employeeId:"FT-0011",email:"<EMAIL>",joinDate:"11-01-2019",role:"Software Tester",employeeRole:"Employee",status:"pending",salary:"17250",Basic:"14000",tDS:"500",da:"1000",hra:"2000",pf:"3600",conveyance:"800",leave:"2000",allowance:"4000",proTax:"250",medAllowance:"500",labourWelfare:"800",othersAdd:"100",othersDed:"200",esi:"200",id:10}],taskboard:[{id:1,taskname:"John deo",taskpriority:"Medium",duedate:"02-05-2020",followers:"John deo",status:"Active"},{id:2,taskname:"John Mclaren",taskpriority:"Low",duedate:"02-10-2020",followers:"Richard Williams",status:"Active"},{id:3,taskname:"Kennedy",taskpriority:"High",duedate:"05-11-2020",followers:"Richard deo",status:"Active"},{id:4,taskname:"Barry cuda",taskpriority:"Medium",duedate:"02-05-2020",followers:"Williams",status:"Active"},{id:5,taskname:"Joshy",taskpriority:"High",duedate:"02-05-2020",followers:"Loren",status:"Active"},{id:6,taskname:"Hector",taskpriority:"Medium",duedate:"25-10-2020",followers:"Rihanna",status:"Active"}],pickListNames:[{id:1,name:"John deo"},{id:2,name:"John Mclaren"},{id:3,name:"Kennedy"},{id:4,name:"Barry cuda"}],customPolicy:[{id:1,name:"John deo",days:5},{id:2,name:"John Mclaren",days:6},{id:3,name:"Kennedy",days:8},{id:4,name:"Barry cuda",days:9}],categories:[{id:1,categoryname:"Hardware",subcategoryname:"Hardware Expenses"},{id:2,categoryname:"Material",subcategoryname:"Material Expenses"},{id:3,categoryname:"Vehicle",subcategoryname:"Company Vehicle Information"}],revenue:[{id:1,notes:"test",categoryname:"Project",subcategoryname:"Project Expenses",amount:"1000.00",revenuedate:"06 Jan 2020"},{id:2,notes:"test",categoryname:"Hardware",subcategoryname:"Hardware Expenses",amount:"1000.00",revenuedate:"06 Jan 2020"}],useralljobs:[{id:1,jobtitle:"Web Developer",department:"Development",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Full Time",status:"Open"},{id:2,jobtitle:"Web Designer",department:"Designing",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Part Time",status:"Closed"},{id:3,jobtitle:"Android Developer",department:"Android",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Internship",status:"Cancelled"}],offeredjobs:[{id:1,jobtitle:"Web Developer",department:"Development",jobtype:"Full Time"},{id:2,jobtitle:"Web Designer",department:"Designing",jobtype:"Part Time"},{id:3,jobtitle:"Android Developer",department:"Android",jobtype:"Internship"}],visitedjobs:[{id:1,jobtitle:"Web Developer",department:"Development",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Full Time",status:"Open"},{id:2,jobtitle:"Web Designer",department:"Designing",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Part Time",status:"Closed"},{id:3,jobtitle:"Android Developer",department:"Android",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Internship",status:"Cancelled"}],projectreports:[{id:1,projecttitle:"Hospital Administration",clientname:"Global Technologies",startdate:"9 Jan 2021",expiredate:"10 Apr 2021",status:"Active"},{id:2,projecttitle:"Office Management",clientname:"Delta Infotech",startdate:"10 Dec 2021",expiredate:"2 May 2021",status:"Pending"}],archivedjobs:[{id:1,jobtitle:"Web Developer",department:"Development",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Full Time",status:"Open"},{id:2,jobtitle:"Web Designer",department:"Designing",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Part Time",status:"Closed"},{id:3,jobtitle:"Android Developer",department:"Android",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Internship",status:"Cancelled"}],appliedjobs:[{id:1,jobtitle:"Web Developer",department:"Development",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Full Time",status:"Open"},{id:2,jobtitle:"Web Designer",department:"Designing",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Part Time",status:"Closed"},{id:3,jobtitle:"Android Developer",department:"Android",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Internship",status:"Cancelled"}],savedjobs:[{id:1,jobtitle:"Web Developer",department:"Development",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Full Time",status:"Open"},{id:2,jobtitle:"Web Designer",department:"Designing",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Part Time",status:"Closed"},{id:3,jobtitle:"Android Developer",department:"Android",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Internship",status:"Cancelled"}],interview:[{id:1,questions:"IS management has decided to rewrite a legacy customer relations system using fourth generation languages (4GLs). Which of the following risks is MOST often associated with system development using 4GLs?",option1:"design facilities",option2:"language subsets",option3:"Lack of portability",option4:"Inability to perform data",correctanswer:"A"},{id:2,questions:"Which of the following would be the BEST method for ensuring that critical fields in a master record have been updated properly?",option1:"design facilities",option2:"language subsets",option3:"Lack of portability",option4:"Inability to perform data",correctanswer:"A"}],candidate:[{id:1,name:"John Doe",mobilenumber:"**********",email:"<EMAIL>",createddate:"1 Jan 2013"},{id:2,name:"Richard Miles",mobilenumber:"**********",email:"<EMAIL>",createddate:"18 Mar 2014"},{id:3,name:"John Smith",mobilenumber:"**********",email:"<EMAIL>",createddate:"1 Apr 2014"}],expire:[{id:1,experience:"1-2",status:"Active"},{id:2,experience:"1-3",status:"Active"},{id:3,experience:"4-7",status:"Active"}],paymentreports:[{id:1,transactionid:"834521",date:"2nd Dec 2020",clientname:"Dreams",paymentmethod:"Online",invoice:"INV0001",amount:"$4,329,970.7"},{id:2,transactionid:"834521",date:"2nd Dec 2020",clientname:"Dreams",paymentmethod:"Online",invoice:"INV0001",amount:"$4,329,970.7"}],taskreports:[{id:1,taskname:"Hospital Administration",startdate:"26 Mar 2019",enddate:"26 Apr 2021",status:"Active"},{id:2,taskname:"Hospital Administration",startdate:"26 Mar 2019",enddate:"26 Apr 2021",status:"Active"}],userreports:[{id:1,name1:"Barry Cuda",name2:"Global Technologies",company:"Global Technologies",email:"<EMAIL>",role:"Client",designation:"CEO",status:"Active"},{id:2,name1:"Daniel Porter",name2:"Admin",company:"Focus Technologies",email:"<EMAIL>",role:"Admin",designation:"Admin Manager",status:"Active"}],attendancereports:[{id:1,date:"1 Jan 2020",clockin:"-",clockout:"-",workstatus:"-"},{id:2,date:"2 Jan 2020",clockin:"-",clockout:"-",workstatus:"-"},{id:3,date:"3 Jan 2020",clockin:"-",clockout:"-",workstatus:"-"},{id:4,date:"4 Jan 2020",clockin:"-",clockout:"Week Off",workstatus:"-"},{id:5,date:"5 Jan 2020",clockin:"-",clockout:"Week Off",workstatus:"-"},{id:6,date:"6 Jan 2020",clockin:"-",clockout:"-",workstatus:"-"}],leavereports:[{id:1,name1:"John Doe",name2:"#0001",date:"20 Dec 2020",department:"Design",leavetype:"Sick Leave",noofdays:"05",remainingleave:"08",totalleaves:"20",totalleavetaken:"12",leavecarryforward:"08"},{id:2,name1:"Richard Miles",name2:"#0002",date:"21 Dec 2020",department:"Web Developer",leavetype:"Parenting Leave",noofdays:"03",remainingleave:"08",totalleaves:"20",totalleavetaken:"12",leavecarryforward:"05"},{id:3,name1:"John Smith",name2:"#0003",date:"22 Dec 2020",department:"Android Developer",leavetype:"Emergency Leave",noofdays:"01",remainingleave:"09",totalleaves:"20",totalleavetaken:"17",leavecarryforward:"03"},{id:4,name1:"Mike Litorus",name2:"#0004",date:"23 Dec 2020",department:"IOS Developer",leavetype:"Sick Leave",noofdays:"15",remainingleave:"05",totalleaves:"20",totalleavetaken:"15",leavecarryforward:"05"},{id:5,name1:"John Doe",name2:"#0001",date:"24 Dec 2020",department:"Team Leader",leavetype:"Sick Leave",noofdays:"10",remainingleave:"07",totalleaves:"20",totalleavetaken:"18",leavecarryforward:"2"}],dailyreport:[{id:1,name1:"John Doe",name2:"#0001",date:"20 Dec 2020",department:"Design",status:"Week off"},{id:2,name1:"John Smith",name2:"#0003",date:"20 Dec 2020",department:"Android Developer",status:"Week off"},{id:3,name1:"Mike Litorus",name2:"#0004",date:"20 Dec 2020",department:"IOS Developer",status:"Week off"},{id:4,name1:"Richard Miles",name2:"#0002",date:"20 Dec 2020",department:"Web Developer",status:"Absent"},{id:5,name1:"Wilmer Deluna",name2:"#0005",date:"20 Dec 2020",department:"Team Leader",status:"Week off"}],candidatelist:[{id:1,name1:"John Doe",name2:"Web Designer",jobtitle:"Web Developer",department:"Development",status:"Offered"},{id:2,name1:"Richard Miles",name2:"Web Developer",jobtitle:"Web Designer",department:"Designing",status:"Offered"},{id:3,name1:"John Smith",name2:"Android Developer",jobtitle:"Android Developer",department:"Android",status:"Offered"}],offer:[{id:1,name1:"John Doe",name2:"Web Designer",jobtitle:"Web Developer",jobtype:"Temporary",pay:"$25000",annualip:"15%",longtermip:"No",status:"requested"},{id:2,name1:"Richard Miles",name2:"Web Developer",jobtitle:"Web Designer",jobtype:"Contract",pay:"$25000",annualip:"15%",longtermip:"No",status:"rejected"},{id:3,name1:"John Smith",name2:"Android Developer",jobtitle:"Android Developer",jobtype:"Salary",pay:"$25000",annualip:"15%",longtermip:"No",status:"Approved"}],scheduletiming:[{id:1,name1:"John Doe",name2:"Web Designer",jobtitle:"Web Developer",useravailable:"11-03-2020",useravailabletimings:"- 11:00 AM-12:00 PM",useravailable1:"11-03-2020",useravailabletimings1:"- 10:00 AM-11:00 AM",useravailable2:"01-01-1970",useravailabletimings2:"- 10:00 AM-11:00 AM"},{id:2,name1:"Richard Miles",name2:"Web Developer",jobtitle:"Web Designer",useravailable:"11-03-2020",useravailabletimings:"- 11:00 AM-12:00 PM",useravailable1:"11-03-2020",useravailabletimings1:"- 10:00 AM-11:00 AM",useravailable2:"01-01-1970",useravailabletimings2:"- 10:00 AM-11:00 AM"},{id:3,name1:"John Smith",name2:"Android Developer",jobtitle:"Android Developer",useravailable:"11-03-2020",useravailabletimings:"- 11:00 AM-12:00 PM",useravailable1:"11-03-2020",useravailabletimings1:"- 10:00 AM-11:00 AM",useravailable2:"01-01-1970",useravailabletimings2:"- 10:00 AM-11:00 AM"}],aptituteresult:[{id:1,name1:"John Doe",name2:"Web Designer",jobtitle:"Web Developer",department:"Development",categorywise:"html -",categorywisemark:"1",categorywise1:"Level1 -",categorywisemark1:"0",totalmark:"1",status:"Action pending"},{id:2,name1:"Richard Miles",name2:"Web Developer",jobtitle:"Web Designer",department:"Designing",categorywise:"html -",categorywisemark:"1",categorywise1:"Level1 -",categorywisemark1:"0",totalmark:"1",status:"Action pending"},{id:3,name1:"John Smith",name2:"Android Developer",jobtitle:"Android Developer",department:"Android",categorywise:"html -",categorywisemark:"1",categorywise1:"Level1 -",categorywisemark1:"0",totalmark:"1",status:"Action pending"}],manage:[{id:1,name1:"John Doe",name2:"Web Designer",jobtitle:"Web Developer",department:"Development",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Full Time",status:"Open",resume:"Download"},{id:2,name1:"Richard Miles",name2:"Web Developer",jobtitle:"Web Designer",department:"Designing",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Part Time",status:"Closed",resume:"Download"},{id:3,name1:"John Smith",name2:"Android Developer",jobtitle:"Android Developer",department:"Android",startdate:"3 Mar 2019",expiredate:"31 May 2019",jobtype:"Internship",status:"Cancelled",resume:"Download"}],payslip:[{id:1,name1:"Bernardo Galaviz",name2:"Web Developer",paidamount:"$200",paymentmonth:"Apr",paymentyear:"2019",actions:"PDF"},{id:2,name1:"Jeffrey Warden",name2:"Web Developer",paidamount:"$300",paymentmonth:"Dec",paymentyear:"2020",actions:"PDF"},{id:3,name1:"John Doe",name2:"Web Designer",paidamount:"$400",paymentmonth:"Jun",paymentyear:"2020",actions:"PDF"},{id:4,name1:"John Smith",name2:"Android Developer",paidamount:"$500",paymentmonth:"Feb",paymentyear:"2020",actions:"PDF"},{id:5,name1:"Mike Litorus",name2:"IOS Developer",paidamount:"$600",paymentmonth:"Jan",paymentyear:"2020",actions:"PDF"}],employeereport:[{id:1,name1:"John Doe",name2:"#0001",employeetype:"Employee",email:"<EMAIL>",department:"Designing",designation:"UI Design",joiningdate:"20 Aug 2020",dob:"03 Mar 1992",marritalstatus:"Married",gender:"Male",terminateddate:"-",relievingdate:"-",salary:"$20000",address:"1861 Bayonne Ave, Manchester Township, NJ, 08759",contactnumber:"7894561235",experience:"0 years 4 months and 9 days",status:"Active"},{id:2,name1:"Richard Miles",name2:"#0002",employeetype:"Employee",email:"<EMAIL>",department:"Android Developer",designation:"IT Support",joiningdate:"01 Jul 2020",dob:"05 Dec 1979",marritalstatus:"Married",gender:"Male",terminateddate:"-",relievingdate:"-",salary:"$20000",address:"1861 Bayonne Ave, Manchester Township, NJ, 08759",contactnumber:"7894561235",experience:"0 years 5 months and 24 days",status:"Active"},{id:3,name1:"John Smith",name2:"#0003",employeetype:"Employee",email:"<EMAIL>",department:"IOS Developer",designation:"Development Manager",joiningdate:"03 Sep 2020",dob:"16 Apr 1984",marritalstatus:"Married",gender:"Male",terminateddate:"-",relievingdate:"-",salary:"$27000",address:"1861 Bayonne Ave, Manchester Township, NJ, 08759",contactnumber:"7894561235",experience:"0 years 3 months and 21 days",status:"Active"},{id:4,name1:"Mike Litorus",name2:"#0004",employeetype:"Employee",email:"<EMAIL>",department:"Web Developer",designation:"IT Support",joiningdate:"15 Nov 2020",dob:"15 Jul 2005",marritalstatus:"Single",gender:"Male",terminateddate:"-",relievingdate:"-",salary:"$15000",address:"1861 Bayonne Ave, Manchester Township, NJ, 08759",contactnumber:"7894561235",experience:"0 years 1 months and 9 days",status:"Active"},{id:5,name1:"Wilmer Deluna",name2:"#005",employeetype:"Employee",email:"<EMAIL>",department:"Team Leader",designation:"Development Manager",joiningdate:"01 Dec 2020",dob:"21 Jun 1984",marritalstatus:"Married",gender:"Male",terminateddate:"-",relievingdate:"-",salary:"$25000",address:"1861 Bayonne Ave, Manchester Township, NJ, 08759",contactnumber:"7894561235",experience:"0 years 0 months and 24 days",status:"Active"}],budgetexpense:[{id:1,notes:"test",categoryname:"Hardware",subcategoryname:"Hardware Expenses",amount:"1000.00",revenuedate:"06 Jan 2020"},{id:2,notes:"test",categoryname:"Project",subcategoryname:"Project Expenses",amount:"1000.00",revenuedate:"06 Jan 2020"}],budget:[{id:1,budgettitle:"Tender",budgettype:"project",startdate:"01 Jan 2021",enddate:"31 Dec 2021",totalrevenue:"2500000",totalexpenses:"1500000",taxamount:"10",budgetamount:"999990"},{id:2,budgettitle:"Project",budgettype:"project",startdate:"01 Feb 2021",enddate:"31 Apr 2021",totalrevenue:"100000",totalexpenses:"50000",taxamount:"1000",budgetamount:"49000"}],shiftlist:[{id:1,shiftname:"10'0 clock Shift",minstarttime:"09.00:00 am",starttime:"10:00:00 am",maxstarttime:"10:30:00 am",minendtime:"06:00:00 pm",endtime:"06:30:00 pm",maxendtime:"07:00:00 pm",breaktime:"30 mins",status:"Active"},{id:2,shiftname:"10:30 shift",minstarttime:"10.00:00 am",starttime:"10:30:00 am",maxstarttime:"11:00:00 am",minendtime:"06:30:00 pm",endtime:"07:00:00 pm",maxendtime:"07:30:00 pm",breaktime:"45 mins",status:"Active"},{id:3,shiftname:"Daily Rout",minstarttime:"06.00:00 am",starttime:"06:30:00 am",maxstarttime:"07:00:00 am",minendtime:"03:00:00 pm",endtime:"03:30:00 pm",maxendtime:"04:00:00 pm",breaktime:"60 mins",status:"Active"},{id:4,shiftname:"New Shift",minstarttime:"06.11:00 am",starttime:"06:30:00 am",maxstarttime:"08:12:00 am",minendtime:"09:12:00 pm",endtime:"09:30:00 pm",maxendtime:"09:45:00 pm",breaktime:"45 mins",status:"Active"},{id:5,shiftname:"Recurring Shift",minstarttime:"08.30:00 am",starttime:"09:00:00 am",maxstarttime:"09:30:00 am",minendtime:"05:30:00 pm",endtime:"06:00:00 pm",maxendtime:"06:30:00 pm",breaktime:"60 mins",status:"Active"}],shiftscheduling:[{id:1,name1:"Brenardo Galaviz",name2:"Web Developer"},{id:2,name1:"Brenardo Galaviz",name2:"Web Developer"},{id:3,name1:"John Doe",name2:"Web Designer"},{id:4,name1:"John Smith",name2:"Android Developer"},{id:5,name1:"Mike Litorus",name2:"IOS Developer"},{id:6,name1:"Richard Miles",name2:"Web Developer"},{id:7,name1:"Wilmer Deluna",name2:"Team Leader"}]}}}const st={};let lt=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=l.Sb({type:e}),e.\u0275inj=l.Rb({providers:[p.a,{provide:ve,useValue:st},m.a],imports:[[n.c,a.j,o.d,ot.forRoot(rt),Le,Ne]]}),e})()},IhMt:function(e,t,i){"use strict";i.d(t,"a",function(){return c});var n=i("tk/3"),a=i("z6cu"),o=i("vkgz"),r=i("JIr8"),s=i("lJxs"),l=i("fXoL");let c=(()=>{class e{constructor(e){this.http=e,this.groups={active:"",total:["general","video responsive survey","500rs","warehouse"]},this.members={active:"Mike Litorus",total:[{name:"John Doe",count:3},{name:"Richard Miles",count:0},{name:"John Smith",count:7},{name:"Mike Litorus",count:9}]},this.headers=(new n.e).set("Content-Type","application/json").set("Accept","application/json"),this.httpOptions={headers:this.headers}}handleError(e){return Object(a.a)(e)}get(e){return this.apiurl=`api/${e}`,this.http.get(this.apiurl).pipe(Object(o.a)(),Object(r.a)(this.handleError))}add(e,t){return this.apiurl=`api/${t}`,e.id=null,this.http.post(this.apiurl,e,this.httpOptions).pipe(Object(o.a)(),Object(r.a)(this.handleError))}update(e,t){return this.apiurl=`api/${t}`,this.http.put(`${this.apiurl}/${e.id}`,e,this.httpOptions).pipe(Object(s.a)(()=>e),Object(r.a)(this.handleError))}delete(e,t){return this.apiurl=`api/${t}`,this.http.delete(`${this.apiurl}/${e}`,this.httpOptions).pipe(Object(r.a)(this.handleError))}}return e.\u0275fac=function(t){return new(t||e)(l.ec(n.c))},e.\u0275prov=l.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},PqYM:function(e,t,i){"use strict";i.d(t,"a",function(){return l});var n=i("HDdC"),a=i("D0XW"),o=i("DH7j");function r(e){return!Object(o.a)(e)&&e-parseFloat(e)+1>=0}var s=i("z+Ro");function l(e=0,t,i){let o=-1;return r(t)?o=Number(t)<1?1:Number(t):Object(s.a)(t)&&(i=t),Object(s.a)(i)||(i=a.a),new n.a(t=>{const n=r(e)?e:+e-i.now();return i.schedule(c,n,{index:0,period:o,subscriber:t})})}function c(e){const{index:t,period:i,subscriber:n}=e;if(n.next(t),!n.closed){if(-1===i)return n.complete();e.index=t+1,this.schedule(e,i)}}},cRb6:function(e,t,i){"use strict";i.d(t,"a",function(){return s});var n=i("6Qlo"),a=i("AytR"),o=i("fXoL"),r=i("tk/3");let s=(()=>{class e{constructor(e,t){this.http=e,this.headerService=t,this.baseUrl=a.a.baseUrl}getLastSevenDaysAttn(){return this.http.get(`${this.baseUrl}/attnProc/lastSevenDaysAttn`)}sendGetRequestForMenusAuth(){return console.log("sendGetRequestForMenusAuth"),this.http.get(`${this.baseUrl}/menusAuth`)}}return e.\u0275fac=function(t){return new(t||e)(o.ec(r.c),o.ec(n.a))},e.\u0275prov=o.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},xgIS:function(e,t,i){"use strict";i.d(t,"a",function(){return s});var n=i("HDdC"),a=i("DH7j"),o=i("n6bG"),r=i("lJxs");function s(e,t,i,c){return Object(o.a)(i)&&(c=i,i=void 0),c?s(e,t,i).pipe(Object(r.a)(e=>Object(a.a)(e)?c(...e):c(e))):new n.a(n=>{l(e,t,function(e){n.next(arguments.length>1?Array.prototype.slice.call(arguments):e)},n,i)})}function l(e,t,i,n,a){let o;if(function(e){return e&&"function"==typeof e.addEventListener&&"function"==typeof e.removeEventListener}(e)){const n=e;e.addEventListener(t,i,a),o=()=>n.removeEventListener(t,i,a)}else if(function(e){return e&&"function"==typeof e.on&&"function"==typeof e.off}(e)){const n=e;e.on(t,i),o=()=>n.off(t,i)}else if(function(e){return e&&"function"==typeof e.addListener&&"function"==typeof e.removeListener}(e)){const n=e;e.addListener(t,i),o=()=>n.removeListener(t,i)}else{if(!e||!e.length)throw new TypeError("Invalid event target");for(let o=0,r=e.length;o<r;o++)l(e[o],t,i,n,a)}n.add(o)}}}]);