(window.webpackJsonp=window.webpackJsonp||[]).push([[16],{DRj9:function(e,t,a){"use strict";a.r(t),a.d(t,"PayrollModule",function(){return ke});var i=a("ofXK"),c=a("tyNb"),o=a("fXoL");const n=function(e){return{height:e}};let r=(()=>{let e=class{constructor(e){this.ngZone=e,window.onresize=e=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){}onResize(e){this.innerHeight=e.target.innerHeight+"px"}};return e.\u0275fac=function(t){return new(t||e)(o.Ub(o.G))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.hc("resized",function(e){return t.onResize(e)}),o.Vb(1,"router-outlet"),o.Zb()),2&e&&o.pc("ngStyle",o.tc(1,n,t.innerHeight))},directives:[i.n,c.g],styles:[""]}),e})();var l=a("bNXq"),d=a("AytR"),s=a("3Pt+"),b=a("JqCM"),p=a("oW1M"),m=a("oOf3");function u(e,t){if(1&e&&(o.ac(0,"tr"),o.ac(1,"td"),o.Lc(2),o.Zb(),o.ac(3,"td",34),o.Lc(4),o.Zb(),o.ac(5,"td"),o.Lc(6),o.Zb(),o.ac(7,"td"),o.Lc(8),o.Zb(),o.ac(9,"td"),o.Lc(10),o.Zb(),o.ac(11,"td"),o.Lc(12),o.Zb(),o.ac(13,"td"),o.Lc(14),o.Zb(),o.ac(15,"td"),o.ac(16,"a",45),o.Lc(17),o.Zb(),o.Zb(),o.Zb()),2&e){const e=t.$implicit,a=t.index,i=o.jc();o.Mb("active",a==i.currentIndex),o.Ib(2),o.Mc((i.pngConfig.pageNum-1)*i.pngConfig.pageSize+(a+1)),o.Ib(2),o.Mc(e.id),o.Ib(2),o.Mc(e.empName),o.Ib(2),o.Mc(e.empCode),o.Ib(2),o.Mc(e.salaryDayMonthYear),o.Ib(2),o.Mc(e.prlElmntGross),o.Ib(2),o.Mc(e.netPayableAmount),o.Ib(2),o.rc("routerLink","/payroll/payslip/",e.id,""),o.Ib(1),o.Mc(e.paySlipNum)}}function g(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",46),o.ac(2,"h5",47),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function h(e,t){if(1&e&&(o.ac(0,"option",48),o.Lc(1),o.Zb()),2&e){const e=t.$implicit;o.pc("value",e),o.Ib(1),o.Nc(" ",e," ")}}const f=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let Z=(()=>{class e{constructor(e,t){this.payrollService=e,this.spinnerService=t,this.baseUrl=d.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.pngConfig={pageNum:1,pageSize:10,totalItem:50,pageSizes:[3,5,10,25,50,100,200,500,1e3],pngDiplayLastSeq:10,itemsPerPage:10,currentPage:1,totalItems:50}}ngOnInit(){this.myFromGroup=new s.g({pageSize:new s.e}),this.myFromGroup.get("pageSize").setValue(this.pngConfig.pageSize),this.bindFromFloatingLabel(),this.getListData()}ngAfterViewInit(){setTimeout(()=>{},1e3)}bindFromFloatingLabel(){var e=this;$(".floating").length>0&&$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),$(".filter-row").find("input, select, textarea").keyup(function(t){console.log(t.keyCode),13==t.keyCode&&e.getSearchData()})}searchByFromDate(e){let t=this.pipe.transform(e,"yyyy-MM-dd");this.srcFromDate=t,console.log(t),this.bindFromFloatingLabel()}searchByToDate(e){let t=this.pipe.transform(e,"yyyy-MM-dd");this.srcToDate=t,console.log(t),this.bindFromFloatingLabel()}searchByEmpName(e){console.log(e),this.srcEmpName=e}searchByEmpCode(e){console.log(e),this.srcEmpCode=e}searchBySearchButton(){console.log(this.srcFromDate),console.log(this.srcToDate),console.log(this.srcEmpName),console.log(this.srcEmpCode),this.getListData()}getSearchData(){this.getListData()}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.srcEmpCode&&(a.empCode=this.srcEmpCode),this.srcEmpName&&(a.empName=this.srcEmpName),this.srcFromDate&&this.srcToDate&&(a.fromDate=this.srcFromDate,a.toDate=this.srcToDate),a}getListData(){let e=this.baseUrl+"/empSalary",t={};t=this.getUserQueryParams(this.pngConfig.pageNum,this.pngConfig.pageSize),this.spinnerService.show(),this.payrollService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.pngConfig.totalItem=e.totalItems,this.pngConfig.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}setDisplayLastSequence(){this.pngConfig.pngDiplayLastSeq=(this.pngConfig.pageNum-1)*this.pngConfig.pageSize+this.pngConfig.pageSize,this.listData.length<this.pngConfig.pageSize&&(this.pngConfig.pngDiplayLastSeq=(this.pngConfig.pageNum-1)*this.pngConfig.pageSize+this.pngConfig.pageSize),this.pngConfig.totalItem<this.pngConfig.pngDiplayLastSeq&&(this.pngConfig.pngDiplayLastSeq=this.pngConfig.totalItem)}handlePageChange(e){this.pngConfig.pageNum=e,this.pngConfig.currentPage=this.pngConfig.pageNum,this.getListData()}handlePageSizeChange(e){this.pngConfig.pageSize=e.target.value,this.pngConfig.pageNum=1,this.pngConfig.itemsPerPage=this.pngConfig.pageSize,this.getListData()}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(l.a),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-employee-salary"]],decls:91,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-2","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-2","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"btn","btn-success","btn-block",3,"click"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"d-none"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"",3,"formGroup"],["formControlName","pageSize",1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Employee Salary"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Salary"),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",9),o.ac(13,"div",10),o.ac(14,"button",11),o.Lc(15,"Excel"),o.Zb(),o.ac(16,"button",11),o.Lc(17,"PDF"),o.Zb(),o.ac(18,"button",11),o.Vb(19,"i",12),o.Lc(20," Print"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(21,"div",13),o.ac(22,"div",14),o.ac(23,"div",15),o.ac(24,"div",16),o.ac(25,"div",17),o.ac(26,"input",18),o.hc("input",function(e){return t.searchByEmpName(e.target.value)}),o.Zb(),o.ac(27,"label",19),o.Lc(28,"Employee Name"),o.Zb(),o.Zb(),o.Zb(),o.ac(29,"div",20),o.ac(30,"div",17),o.ac(31,"input",18),o.hc("input",function(e){return t.searchByEmpCode(e.target.value)}),o.Zb(),o.ac(32,"label",19),o.Lc(33,"Employee Code"),o.Zb(),o.Zb(),o.Zb(),o.ac(34,"div",21),o.ac(35,"div",17),o.ac(36,"div",22),o.ac(37,"input",23),o.hc("bsValueChange",function(e){return t.searchByFromDate(e)}),o.Zb(),o.Zb(),o.ac(38,"label",19),o.Lc(39,"From"),o.Zb(),o.Zb(),o.Zb(),o.ac(40,"div",21),o.ac(41,"div",17),o.ac(42,"div",22),o.ac(43,"input",23),o.hc("bsValueChange",function(e){return t.searchByToDate(e)}),o.Zb(),o.Zb(),o.ac(44,"label",19),o.Lc(45,"To"),o.Zb(),o.Zb(),o.Zb(),o.ac(46,"div",21),o.ac(47,"a",24),o.hc("click",function(){return t.searchBySearchButton()}),o.Lc(48," Search "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(49,"div",25),o.ac(50,"div",26),o.ac(51,"div",27),o.ac(52,"div",28),o.ac(53,"div",29),o.ac(54,"div",30),o.ac(55,"div",31),o.ac(56,"span",32),o.Lc(57),o.Zb(),o.Zb(),o.Zb(),o.ac(58,"table",33),o.ac(59,"thead"),o.ac(60,"tr"),o.ac(61,"th"),o.Lc(62,"SL"),o.Zb(),o.ac(63,"th",34),o.Lc(64,"TB_ROW_ID"),o.Zb(),o.ac(65,"th"),o.Lc(66,"Employee"),o.Zb(),o.ac(67,"th"),o.Lc(68,"Employee Code"),o.Zb(),o.ac(69,"th"),o.Lc(70,"Salary Disburse Date"),o.Zb(),o.ac(71,"th"),o.Lc(72,"Gross Salary"),o.Zb(),o.ac(73,"th"),o.Lc(74,"Net Pay Amount"),o.Zb(),o.ac(75,"th"),o.Lc(76,"Payslip"),o.Zb(),o.Zb(),o.Zb(),o.ac(77,"tbody"),o.Jc(78,u,18,11,"tr",35),o.kc(79,"paginate"),o.Jc(80,g,4,0,"tr",36),o.Zb(),o.Zb(),o.ac(81,"div",37),o.ac(82,"div",38),o.Lc(83," Items per Page "),o.ac(84,"select",39),o.hc("change",function(e){return t.handlePageSizeChange(e)}),o.Jc(85,h,2,2,"option",40),o.Zb(),o.Zb(),o.ac(86,"div",41),o.ac(87,"pagination-controls",42),o.hc("pageChange",function(e){return t.handlePageChange(e)}),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(88,"ngx-spinner",43),o.ac(89,"p",44),o.Lc(90," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(37),o.pc("bsConfig",o.sc(13,f)),o.Ib(6),o.pc("bsConfig",o.sc(14,f)),o.Ib(14),o.Pc("Displaying ( ",(t.pngConfig.pageNum-1)*t.pngConfig.pageSize+1," to ",t.pngConfig.pngDiplayLastSeq," of ",t.pngConfig.totalItem," ) entries"),o.Ib(21),o.pc("ngForOf",o.mc(79,10,t.listData,t.pngConfig)),o.Ib(2),o.pc("ngIf",0===t.listData.length),o.Ib(2),o.pc("formGroup",t.myFromGroup),o.Ib(3),o.pc("ngForOf",t.pngConfig.pageSizes),o.Ib(3),o.pc("fullScreen",!1))},directives:[c.e,p.b,p.a,i.l,i.m,s.p,s.h,s.v,s.o,s.f,m.c,b.a,s.s,s.y],pipes:[m.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}.pgn-pageSizeOption[_ngcontent-%COMP%]{padding:3px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})(),v=(()=>{class e{constructor(e,t,a){this.payrollService=e,this.spinnerService=t,this.route=a,this.baseUrl=d.a.baseUrl,this.payslipData={}}ngOnInit(){this.loadPayslipData()}loadPayslipData(){this.payslipId=this.route.snapshot.params.id;let e=this.baseUrl+"/getEmpPayslip/"+this.payslipId;this.spinnerService.show(),this.payrollService.sendGetRequest(e,{}).subscribe(e=>{this.payslipData=e,this.spinnerService.hide()},e=>{console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(l.a),o.Ub(b.c),o.Ub(c.a))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-salary-view"]],decls:141,vars:36,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group-sm"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-body"],[1,"payslip-title"],[1,"col-sm-6","m-b-20"],[1,"list-unstyled","mb-0"],[1,"invoice-details"],[1,"text-uppercase"],[1,"list-unstyled"],[1,"col-lg-12","m-b-20"],[1,"mb-0"],[1,"col-sm-6"],[1,"m-b-10"],[1,"table","table-bordered"],[1,"float-right"],[1,"col-sm-12"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Payslip"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payslip"),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",9),o.ac(13,"div",10),o.ac(14,"button",11),o.Lc(15,"CSV"),o.Zb(),o.ac(16,"button",11),o.Lc(17,"PDF"),o.Zb(),o.ac(18,"button",11),o.Vb(19,"i",12),o.Lc(20," Print"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(21,"div",13),o.ac(22,"div",14),o.ac(23,"div",15),o.ac(24,"div",16),o.ac(25,"h4",17),o.Lc(26),o.Zb(),o.ac(27,"div",13),o.ac(28,"div",18),o.ac(29,"ul",19),o.ac(30,"li"),o.Lc(31),o.Zb(),o.ac(32,"li"),o.Lc(33),o.Zb(),o.ac(34,"li"),o.Lc(35),o.Zb(),o.Zb(),o.Zb(),o.ac(36,"div",18),o.ac(37,"div",20),o.ac(38,"h3",21),o.Lc(39),o.Zb(),o.ac(40,"ul",22),o.ac(41,"li"),o.Lc(42,"Salary Month: "),o.ac(43,"span"),o.Lc(44),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(45,"div",13),o.ac(46,"div",23),o.ac(47,"ul",22),o.ac(48,"li"),o.ac(49,"h5",24),o.ac(50,"strong"),o.Lc(51),o.Zb(),o.Zb(),o.Zb(),o.ac(52,"li"),o.ac(53,"span"),o.Lc(54),o.Zb(),o.Zb(),o.ac(55,"li"),o.Lc(56),o.Zb(),o.ac(57,"li"),o.Lc(58),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(59,"div",13),o.ac(60,"div",25),o.ac(61,"div"),o.ac(62,"h4",26),o.ac(63,"strong"),o.Lc(64,"Earnings"),o.Zb(),o.Zb(),o.ac(65,"table",27),o.ac(66,"tbody"),o.ac(67,"tr"),o.ac(68,"td"),o.ac(69,"strong"),o.Lc(70,"Basic Pay"),o.Zb(),o.ac(71,"span",28),o.Lc(72),o.kc(73,"number"),o.Zb(),o.Zb(),o.Zb(),o.ac(74,"tr"),o.ac(75,"td"),o.ac(76,"strong"),o.Lc(77,"House Rent Allowance"),o.Zb(),o.ac(78,"span",28),o.Lc(79),o.kc(80,"number"),o.Zb(),o.Zb(),o.Zb(),o.ac(81,"tr"),o.ac(82,"td"),o.ac(83,"strong"),o.Lc(84,"Conveyance"),o.Zb(),o.ac(85,"span",28),o.Lc(86,"0.00"),o.Zb(),o.Zb(),o.Zb(),o.ac(87,"tr"),o.ac(88,"td"),o.ac(89,"strong"),o.Lc(90,"Other Allowance"),o.Zb(),o.ac(91,"span",28),o.Lc(92,"0.00"),o.Zb(),o.Zb(),o.Zb(),o.ac(93,"tr"),o.ac(94,"td"),o.ac(95,"strong"),o.Lc(96,"Total Earnings"),o.Zb(),o.ac(97,"span",28),o.ac(98,"strong"),o.Lc(99),o.kc(100,"number"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(101,"div",25),o.ac(102,"div"),o.ac(103,"h4",26),o.ac(104,"strong"),o.Lc(105,"Deductions"),o.Zb(),o.Zb(),o.ac(106,"table",27),o.ac(107,"tbody"),o.ac(108,"tr"),o.ac(109,"td"),o.ac(110,"strong"),o.Lc(111,"AIT"),o.Zb(),o.ac(112,"span",28),o.Lc(113,"0.00"),o.Zb(),o.Zb(),o.Zb(),o.ac(114,"tr"),o.ac(115,"td"),o.ac(116,"strong"),o.Lc(117,"Food Deduction"),o.Zb(),o.ac(118,"span",28),o.Lc(119,"0.00"),o.Zb(),o.Zb(),o.Zb(),o.ac(120,"tr"),o.ac(121,"td"),o.ac(122,"strong"),o.Lc(123,"Absent Deduction"),o.Zb(),o.ac(124,"span",28),o.Lc(125),o.kc(126,"number"),o.Zb(),o.Zb(),o.Zb(),o.ac(127,"tr"),o.ac(128,"td"),o.ac(129,"strong"),o.Lc(130,"Total Deductions"),o.Zb(),o.ac(131,"span",28),o.ac(132,"strong"),o.Lc(133),o.kc(134,"number"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(135,"div",29),o.ac(136,"p"),o.ac(137,"strong"),o.Lc(138),o.kc(139,"number"),o.Zb(),o.Lc(140),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&e&&(o.Ib(26),o.Mc(t.payslipData.payslipNarration),o.Ib(5),o.Mc(t.payslipData.orgName),o.Ib(2),o.Mc(t.payslipData.orgAddress),o.Ib(2),o.Mc(t.payslipData.orgAddressLine2),o.Ib(4),o.Mc(t.payslipData.payslipNum),o.Ib(5),o.Oc("",t.payslipData.monthFullName,", ",t.payslipData.salaryYear,""),o.Ib(7),o.Mc(t.payslipData.empName),o.Ib(3),o.Mc(t.payslipData.empDesignation),o.Ib(2),o.Nc("Employee Code: ",t.payslipData.empCode,""),o.Ib(2),o.Nc("Joining Date: ",t.payslipData.joiningDate,""),o.Ib(14),o.Mc(o.mc(73,18,t.payslipData.basicSalary,"1.2-2")),o.Ib(7),o.Mc(o.mc(80,21,t.payslipData.houseRentAlwAmt,"1.2-2")),o.Ib(20),o.Mc(o.mc(100,24,t.payslipData.totalEarnings,"1.2-2")),o.Ib(26),o.Mc(o.mc(126,27,t.payslipData.absentDeduction,"1.2-2")),o.Ib(8),o.Mc(o.mc(134,30,t.payslipData.totalDeductions,"1.2-2")),o.Ib(5),o.Nc("Net Pay: ",o.mc(139,33,t.payslipData.netPayable,"1.2-2"),""),o.Ib(2),o.Nc(" ( ",t.payslipData.netPayableStr," )"))},directives:[c.e],pipes:[i.f],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();var y=a("IhMt"),L=a("5eHb");function D(e,t){if(1&e){const e=o.bc();o.ac(0,"tr"),o.ac(1,"th"),o.Lc(2),o.Zb(),o.ac(3,"td"),o.Lc(4),o.Zb(),o.ac(5,"td"),o.Lc(6),o.Zb(),o.ac(7,"td",25),o.ac(8,"div",113),o.ac(9,"a",114),o.ac(10,"i",115),o.Lc(11,"more_vert"),o.Zb(),o.Zb(),o.ac(12,"div",116),o.ac(13,"a",117),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().editAdd(a.id)}),o.Vb(14,"i",118),o.Lc(15," Edit"),o.Zb(),o.ac(16,"a",119),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().tempAddId=a.id}),o.Vb(17,"i",120),o.Lc(18," Delete"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()}if(2&e){const e=t.$implicit;o.Ib(2),o.Mc(e.name),o.Ib(2),o.Mc(e.category),o.Ib(2),o.Mc(e.unitCost)}}function P(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",121),o.ac(2,"h5",122),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function S(e,t){if(1&e){const e=o.bc();o.ac(0,"tr"),o.ac(1,"th"),o.Lc(2),o.Zb(),o.ac(3,"td"),o.Lc(4),o.Zb(),o.ac(5,"td",25),o.ac(6,"div",113),o.ac(7,"a",114),o.ac(8,"i",115),o.Lc(9,"more_vert"),o.Zb(),o.Zb(),o.ac(10,"div",116),o.ac(11,"a",123),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().editOver(a.id)}),o.Vb(12,"i",118),o.Lc(13," Edit"),o.Zb(),o.ac(14,"a",124),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().tempOverId=a.id}),o.Vb(15,"i",120),o.Lc(16," Delete"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()}if(2&e){const e=t.$implicit;o.Ib(2),o.Mc(e.name),o.Ib(2),o.Mc(e.rate)}}function C(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",121),o.ac(2,"h5",122),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function F(e,t){if(1&e){const e=o.bc();o.ac(0,"tr"),o.ac(1,"th"),o.Lc(2),o.Zb(),o.ac(3,"td"),o.Lc(4),o.Zb(),o.ac(5,"td",25),o.ac(6,"div",113),o.ac(7,"a",114),o.ac(8,"i",115),o.Lc(9,"more_vert"),o.Zb(),o.Zb(),o.ac(10,"div",116),o.ac(11,"a",125),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().editDeduct(a.id)}),o.Vb(12,"i",118),o.Lc(13," Edit"),o.Zb(),o.ac(14,"a",126),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().tempDeductId=a.id}),o.Vb(15,"i",120),o.Lc(16," Delete"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()}if(2&e){const e=t.$implicit;o.Ib(2),o.Mc(e.name),o.Ib(2),o.Mc(e.unitCost)}}function I(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",121),o.ac(2,"h5",122),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function O(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Name is required"),o.Zb())}function M(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,O,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addPayrollForm.get("addPayrollName").invalid&&e.addPayrollForm.get("addPayrollName").touched)}}function x(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Category is required"),o.Zb())}function _(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,x,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addPayrollForm.get("addPayrollCategory").invalid&&e.addPayrollForm.get("addPayrollCategory").touched)}}function N(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Category is required"),o.Zb())}function w(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,N,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addPayrollForm.get("addPayrollUnit").invalid&&e.addPayrollForm.get("addPayrollUnit").touched)}}function E(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Name is required"),o.Zb())}function k(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,E,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editPayrollForm.get("editPayrollName").invalid&&e.editPayrollForm.get("editPayrollName").touched)}}function A(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Category is required"),o.Zb())}function U(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,A,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editPayrollForm.get("editPayrollCategory").invalid&&e.editPayrollForm.get("editPayrollCategory").touched)}}function V(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Unit is required"),o.Zb())}function R(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,V,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editPayrollForm.get("editPayrollUnit").invalid&&e.editPayrollForm.get("editPayrollUnit").touched)}}function T(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Name is required"),o.Zb())}function B(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,T,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addOverForm.get("addOverName").invalid&&e.addOverForm.get("addOverName").touched)}}function q(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Rate is required"),o.Zb())}function z(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,q,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addOverForm.get("addOverRate").invalid&&e.addOverForm.get("addOverRate").touched)}}function G(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Name is required"),o.Zb())}function J(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,G,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editOverForm.get("editOverName").invalid&&e.editOverForm.get("editOverName").touched)}}function j(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Rate is required"),o.Zb())}function H(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,j,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editOverForm.get("editOverRate").invalid&&e.editOverForm.get("editOverRate").touched)}}function W(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Name is required"),o.Zb())}function Y(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,W,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addDeductForm.get("addDeductName").invalid&&e.addDeductForm.get("addDeductName").touched)}}function Q(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Unit is required"),o.Zb())}function X(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,Q,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.addDeductForm.get("addDeductUnit").invalid&&e.addDeductForm.get("addDeductUnit").touched)}}function K(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Name is required"),o.Zb())}function ee(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,K,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editDeductForm.get("editDeductName").invalid&&e.editDeductForm.get("editDeductName").touched)}}function te(e,t){1&e&&(o.ac(0,"small",42),o.Lc(1," *Unit is required"),o.Zb())}function ae(e,t){if(1&e&&(o.ac(0,"div"),o.Jc(1,te,2,0,"small",127),o.Zb()),2&e){const e=o.jc();o.Ib(1),o.pc("ngIf",e.editDeductForm.get("editDeductunit").invalid&&e.editDeductForm.get("editDeductunit").touched)}}let ie=(()=>{class e{constructor(e,t,a){this.allModuleService=e,this.formBuilder=t,this.toastr=a,this.urlAdd="payrollAddition",this.urlOver="payrollOvertime",this.urlDeduct="payrollDeduction",this.allAddPayroll=[],this.allOverPayroll=[],this.allDeductPayroll=[]}ngOnInit(){this.getAddPayroll(),this.getOverpayroll(),this.getDeductPayroll(),this.addPayrollForm=this.formBuilder.group({addPayrollName:["",[s.w.required]],addPayrollCategory:["",[s.w.required]],addPayrollUnit:["",[s.w.required]]}),this.editPayrollForm=this.formBuilder.group({editPayrollName:["",[s.w.required]],editPayrollCategory:["",[s.w.required]],editPayrollUnit:["",[s.w.required]]}),this.addOverForm=this.formBuilder.group({addOverName:["",[s.w.required]],addOverRate:["",[s.w.required]]}),this.editOverForm=this.formBuilder.group({editOverName:["",[s.w.required]],editOverRate:["",[s.w.required]]}),this.addDeductForm=this.formBuilder.group({addDeductName:["",[s.w.required]],addDeductUnit:["",[s.w.required]]}),this.editDeductForm=this.formBuilder.group({editDeductName:["",[s.w.required]],editDeductunit:["",[s.w.required]]})}getAddPayroll(){this.allModuleService.get(this.urlAdd).subscribe(e=>{this.allAddPayroll=e,$("#datatable1").DataTable().clear()})}getOverpayroll(){this.allModuleService.get(this.urlOver).subscribe(e=>{this.allOverPayroll=e,$("#datatable2").DataTable().clear()})}getDeductPayroll(){this.allModuleService.get(this.urlDeduct).subscribe(e=>{this.allDeductPayroll=e,$("#datatable3").DataTable().clear()})}addPayroll(){this.addPayrollForm.valid&&(this.allModuleService.add({name:this.addPayrollForm.value.addPayrollName,category:this.addPayrollForm.value.addPayrollCategory,unitCost:this.addPayrollForm.value.addPayrollUnit},this.urlAdd).subscribe(e=>{}),this.getAddPayroll(),$("#add_addition").modal("hide"),this.addPayrollForm.reset(),this.toastr.success("Payroll added","Success"))}editPayroll(){this.allModuleService.update({name:this.editPayrollForm.value.editPayrollName,category:this.editPayrollForm.value.editPayrollCategory,unitCost:this.editPayrollForm.value.editPayrollUnit,id:this.editAddId},this.urlAdd).subscribe(e=>{}),this.getAddPayroll(),$("#edit_addition").modal("hide"),this.toastr.success("Payroll edited","Success")}editAdd(e){this.editAddId=e;const t=this.allAddPayroll.findIndex(t=>t.id===e);let a=this.allAddPayroll[t];this.editPayrollForm.setValue({editPayrollName:a.name,editPayrollCategory:a.category,editPayrollUnit:a.unitCost})}deletePayroll(){this.allModuleService.delete(this.tempAddId,this.urlAdd).subscribe(e=>{this.getAddPayroll(),$("#delete_addition").modal("hide")}),this.toastr.success("Payroll deleted","Success")}addOver(){this.addOverForm.valid&&(this.allModuleService.add({name:this.addOverForm.value.addOverName,rate:this.addOverForm.value.addOverRate},this.urlOver).subscribe(e=>{}),this.getOverpayroll(),$("#add_overtime").modal("hide"),this.addOverForm.reset(),this.toastr.success("Overtime added","Success"))}editOverSubmit(){this.allModuleService.update({name:this.editOverForm.value.editOverName,rate:this.editOverForm.value.editOverRate,id:this.editOverId},this.urlOver).subscribe(e=>{}),this.getOverpayroll(),$("#edit_overtime").modal("hide"),this.toastr.success("Overtime edited","Success")}editOver(e){this.editOverId=e;const t=this.allOverPayroll.findIndex(t=>t.id===e);let a=this.allOverPayroll[t];this.editOverForm.setValue({editOverName:a.name,editOverRate:a.rate})}deleteOver(){this.allModuleService.delete(this.tempOverId,this.urlOver).subscribe(e=>{this.getOverpayroll(),$("#delete_overtime").modal("hide")}),this.toastr.success("Overtime deleted","Success")}addDeducts(){this.addDeductForm.valid&&(this.allModuleService.add({name:this.addDeductForm.value.addDeductName,unitCost:this.addDeductForm.value.addDeductUnit},this.urlDeduct).subscribe(e=>{}),this.getDeductPayroll(),$("#add_deduction").modal("hide"),this.addDeductForm.reset(),this.toastr.success("Deduction added","Success"))}editDeductSubmit(){this.allModuleService.update({name:this.editDeductForm.value.editDeductName,unitCost:this.editDeductForm.value.editDeductunit,id:this.editDeductId},this.urlDeduct).subscribe(e=>{}),this.getDeductPayroll(),$("#edit_deduction").modal("hide"),this.toastr.success("Deducts edited","Success")}editDeduct(e){this.editDeductId=e;const t=this.allDeductPayroll.findIndex(t=>t.id===e);let a=this.allDeductPayroll[t];this.editDeductForm.setValue({editDeductName:a.name,editDeductunit:a.unitCost})}deleteDeduct(){this.allModuleService.delete(this.tempDeductId,this.urlDeduct).subscribe(e=>{this.getDeductPayroll(),$("#delete_deduction").modal("hide")}),this.toastr.success("Deduction deleted","Success")}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(y.a),o.Ub(s.d),o.Ub(L.b))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll-items"]],decls:502,vars:54,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"page-menu"],[1,"row"],[1,"col-sm-12"],[1,"nav","nav-tabs","nav-tabs-bottom"],[1,"nav-item"],["data-toggle","tab","href","#tab_additions",1,"nav-link","active"],["data-toggle","tab","href","#tab_overtime",1,"nav-link"],["data-toggle","tab","href","#tab_deductions",1,"nav-link"],[1,"tab-content"],["id","tab_additions",1,"tab-pane","show","active"],[1,"text-right","mb-4","clearfix"],["type","button","data-toggle","modal","data-target","#add_addition",1,"btn","btn-primary","add-btn"],[1,"fa","fa-plus"],[1,"payroll-table","card"],[1,"table-responsive"],[1,"table","table-hover","table-radius"],[1,"text-right"],[4,"ngFor","ngForOf"],[4,"ngIf"],["id","tab_overtime",1,"tab-pane"],["type","button","data-toggle","modal","data-target","#add_overtime",1,"btn","btn-primary","add-btn"],["id","tab_deductions",1,"tab-pane"],["type","button","data-toggle","modal","data-target","#add_deduction",1,"btn","btn-primary","add-btn"],["id","add_addition","role","dialog",1,"modal","custom-modal","fade"],["role","document",1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["type","button","data-dismiss","modal","aria-label","Close",1,"close"],["aria-hidden","true"],[1,"modal-body"],[3,"formGroup","ngSubmit"],[1,"form-group"],[1,"text-danger"],["type","text","formControlName","addPayrollName",1,"form-control"],["formControlName","addPayrollCategory",1,"select","form-control"],[1,"d-block"],[1,"status-toggle"],["type","checkbox","id","unit_calculation",1,"check"],["for","unit_calculation",1,"checktoggle"],[1,"input-group"],[1,"input-group-prepend"],[1,"input-group-text"],["type","text","formControlName","addPayrollUnit",1,"form-control"],[1,"input-group-append"],[1,"form-check","form-check-inline"],["type","radio","name","addition_assignee","id","addition_no_emp","value","option1","checked","",1,"form-check-input"],["for","addition_no_emp",1,"form-check-label"],["type","radio","name","addition_assignee","id","addition_all_emp","value","option2",1,"form-check-input"],["for","addition_all_emp",1,"form-check-label"],["type","radio","name","addition_assignee","id","addition_single_emp","value","option3",1,"form-check-input"],["for","addition_single_emp",1,"form-check-label"],[1,"select","form-control"],[1,"submit-section"],[1,"btn","btn-primary","submit-btn"],["id","edit_addition","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","editPayrollName",1,"form-control"],["formControlName","editPayrollCategory",1,"select","form-control"],["type","checkbox","id","edit_unit_calculation",1,"check"],["for","edit_unit_calculation",1,"checktoggle"],["type","text","formControlName","editPayrollUnit",1,"form-control"],["type","radio","name","edit_addition_assignee","id","edit_addition_no_emp","value","option1","checked","",1,"form-check-input"],["for","edit_addition_no_emp",1,"form-check-label"],["type","radio","name","edit_addition_assignee","id","edit_addition_all_emp","value","option2",1,"form-check-input"],["for","edit_addition_all_emp",1,"form-check-label"],["type","radio","name","edit_addition_assignee","id","edit_addition_single_emp","value","option3",1,"form-check-input"],["for","edit_addition_single_emp",1,"form-check-label"],["id","delete_addition","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],["id","add_overtime","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","addOverName",1,"form-control"],["type","text","formControlName","addOverRate",1,"form-control"],["id","edit_overtime","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","editOverName",1,"form-control"],["type","text","formControlName","editOverRate",1,"form-control"],["id","delete_overtime","role","dialog",1,"modal","custom-modal","fade"],["id","add_deduction","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","addDeductName",1,"form-control"],["type","checkbox","id","unit_calculation_deduction",1,"check"],["for","unit_calculation_deduction",1,"checktoggle"],["type","text","formControlName","addDeductUnit",1,"form-control"],["type","radio","name","deduction_assignee","id","deduction_no_emp","value","option1","checked","",1,"form-check-input"],["for","deduction_no_emp",1,"form-check-label"],["type","radio","name","deduction_assignee","id","deduction_all_emp","value","option2",1,"form-check-input"],["for","deduction_all_emp",1,"form-check-label"],["type","radio","name","deduction_assignee","id","deduction_single_emp","value","option3",1,"form-check-input"],["for","deduction_single_emp",1,"form-check-label"],["id","edit_deduction","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","editDeductName",1,"form-control"],["type","checkbox","id","edit_unit_calculation_deduction",1,"check"],["for","edit_unit_calculation_deduction",1,"checktoggle"],["type","text","formControlName","editDeductunit",1,"form-control"],["type","radio","name","edit_deduction_assignee","id","edit_deduction_no_emp","value","option1","checked","",1,"form-check-input"],["for","edit_deduction_no_emp",1,"form-check-label"],["type","radio","name","edit_deduction_assignee","id","edit_deduction_all_emp","value","option2",1,"form-check-input"],["for","edit_deduction_all_emp",1,"form-check-label"],["type","radio","name","edit_deduction_assignee","id","edit_deduction_single_emp","value","option3",1,"form-check-input"],["for","edit_deduction_single_emp",1,"form-check-label"],["id","delete_deduction","role","dialog",1,"modal","custom-modal","fade"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],["data-toggle","modal","data-target","#edit_addition",1,"dropdown-item",3,"click"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_addition",1,"dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],["data-toggle","modal","data-target","#edit_overtime",1,"dropdown-item",3,"click"],["data-toggle","modal","data-target","#delete_overtime",1,"dropdown-item",3,"click"],["data-toggle","modal","data-target","#edit_deduction",1,"dropdown-item",3,"click"],["data-toggle","modal","data-target","#delete_deduction",1,"dropdown-item",3,"click"],["class","text-danger",4,"ngIf"]],template:function(e,t){if(1&e){o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Payroll Items"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payroll Items"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",9),o.ac(13,"div",10),o.ac(14,"div",11),o.ac(15,"ul",12),o.ac(16,"li",13),o.ac(17,"a",14),o.Lc(18,"Additions"),o.Zb(),o.Zb(),o.ac(19,"li",13),o.ac(20,"a",15),o.Lc(21,"Overtime"),o.Zb(),o.Zb(),o.ac(22,"li",13),o.ac(23,"a",16),o.Lc(24,"Deductions"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(25,"div",17),o.ac(26,"div",18),o.ac(27,"div",19),o.ac(28,"button",20),o.Vb(29,"i",21),o.Lc(30," Add Addition"),o.Zb(),o.Zb(),o.ac(31,"div",22),o.ac(32,"div",23),o.ac(33,"table",24),o.ac(34,"thead"),o.ac(35,"tr"),o.ac(36,"th"),o.Lc(37,"Name"),o.Zb(),o.ac(38,"th"),o.Lc(39,"Category"),o.Zb(),o.ac(40,"th"),o.Lc(41,"Default/Unit Amount"),o.Zb(),o.ac(42,"th",25),o.Lc(43,"Action"),o.Zb(),o.Zb(),o.Zb(),o.ac(44,"tbody"),o.Jc(45,D,19,3,"tr",26),o.Jc(46,P,4,0,"tr",27),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(47,"div",28),o.ac(48,"div",19),o.ac(49,"button",29),o.Vb(50,"i",21),o.Lc(51," Add Overtime"),o.Zb(),o.Zb(),o.ac(52,"div",22),o.ac(53,"div",23),o.ac(54,"table",24),o.ac(55,"thead"),o.ac(56,"tr"),o.ac(57,"th"),o.Lc(58,"Name"),o.Zb(),o.ac(59,"th"),o.Lc(60,"Rate"),o.Zb(),o.ac(61,"th",25),o.Lc(62,"Action"),o.Zb(),o.Zb(),o.Zb(),o.ac(63,"tbody"),o.Jc(64,S,17,2,"tr",26),o.Jc(65,C,4,0,"tr",27),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(66,"div",30),o.ac(67,"div",19),o.ac(68,"button",31),o.Vb(69,"i",21),o.Lc(70," Add Deduction"),o.Zb(),o.Zb(),o.ac(71,"div",22),o.ac(72,"div",23),o.ac(73,"table",24),o.ac(74,"thead"),o.ac(75,"tr"),o.ac(76,"th"),o.Lc(77,"Name"),o.Zb(),o.ac(78,"th"),o.Lc(79,"Default/Unit Amount"),o.Zb(),o.ac(80,"th",25),o.Lc(81,"Action"),o.Zb(),o.Zb(),o.Zb(),o.ac(82,"tbody"),o.Jc(83,F,17,2,"tr",26),o.Jc(84,I,4,0,"tr",27),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(85,"div",32),o.ac(86,"div",33),o.ac(87,"div",34),o.ac(88,"div",35),o.ac(89,"h5",36),o.Lc(90,"Add Addition"),o.Zb(),o.ac(91,"button",37),o.ac(92,"span",38),o.Lc(93,"\xd7"),o.Zb(),o.Zb(),o.Zb(),o.ac(94,"div",39),o.ac(95,"form",40),o.hc("ngSubmit",function(){return t.addPayroll()}),o.ac(96,"div",41),o.ac(97,"label"),o.Lc(98,"Name "),o.ac(99,"span",42),o.Lc(100,"*"),o.Zb(),o.Zb(),o.Vb(101,"input",43),o.Jc(102,M,2,1,"div",27),o.Zb(),o.ac(103,"div",41),o.ac(104,"label"),o.Lc(105,"Category "),o.ac(106,"span",42),o.Lc(107,"*"),o.Zb(),o.Zb(),o.ac(108,"select",44),o.ac(109,"option"),o.Lc(110,"Select a category"),o.Zb(),o.ac(111,"option"),o.Lc(112,"Monthly remuneration"),o.Zb(),o.ac(113,"option"),o.Lc(114,"Additional remuneration"),o.Zb(),o.Zb(),o.Jc(115,_,2,1,"div",27),o.Zb(),o.ac(116,"div",41),o.ac(117,"label",45),o.Lc(118,"Unit calculation"),o.Zb(),o.ac(119,"div",46),o.Vb(120,"input",47),o.ac(121,"label",48),o.Lc(122,"checkbox"),o.Zb(),o.Zb(),o.Zb(),o.ac(123,"div",41),o.ac(124,"label"),o.Lc(125,"Unit Amount"),o.Zb(),o.ac(126,"div",49),o.ac(127,"div",50),o.ac(128,"span",51),o.Lc(129,"$"),o.Zb(),o.Zb(),o.Vb(130,"input",52),o.Jc(131,w,2,1,"div",27),o.ac(132,"div",53),o.ac(133,"span",51),o.Lc(134,".00"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(135,"div",41),o.ac(136,"label",45),o.Lc(137,"Assignee"),o.Zb(),o.ac(138,"div",54),o.Vb(139,"input",55),o.ac(140,"label",56),o.Lc(141," No assignee "),o.Zb(),o.Zb(),o.ac(142,"div",54),o.Vb(143,"input",57),o.ac(144,"label",58),o.Lc(145," All employees "),o.Zb(),o.Zb(),o.ac(146,"div",54),o.Vb(147,"input",59),o.ac(148,"label",60),o.Lc(149," Select Employee "),o.Zb(),o.Zb(),o.ac(150,"div",41),o.ac(151,"select",61),o.ac(152,"option"),o.Lc(153,"-"),o.Zb(),o.ac(154,"option"),o.Lc(155,"Select All"),o.Zb(),o.ac(156,"option"),o.Lc(157,"John Doe"),o.Zb(),o.ac(158,"option"),o.Lc(159,"Richard Miles"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(160,"div",62),o.ac(161,"button",63),o.Lc(162,"Submit"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(163,"div",64),o.ac(164,"div",33),o.ac(165,"div",34),o.ac(166,"div",35),o.ac(167,"h5",36),o.Lc(168,"Edit Addition"),o.Zb(),o.ac(169,"button",37),o.ac(170,"span",38),o.Lc(171,"\xd7"),o.Zb(),o.Zb(),o.Zb(),o.ac(172,"div",39),o.ac(173,"form",40),o.hc("ngSubmit",function(){return t.editPayroll()}),o.ac(174,"div",41),o.ac(175,"label"),o.Lc(176,"Name "),o.ac(177,"span",42),o.Lc(178,"*"),o.Zb(),o.Zb(),o.Vb(179,"input",65),o.Jc(180,k,2,1,"div",27),o.Zb(),o.ac(181,"div",41),o.ac(182,"label"),o.Lc(183,"Category "),o.ac(184,"span",42),o.Lc(185,"*"),o.Zb(),o.Zb(),o.ac(186,"select",66),o.ac(187,"option"),o.Lc(188,"Select a category"),o.Zb(),o.ac(189,"option"),o.Lc(190,"Monthly remuneration"),o.Zb(),o.ac(191,"option"),o.Lc(192,"Additional remuneration"),o.Zb(),o.Zb(),o.Jc(193,U,2,1,"div",27),o.Zb(),o.ac(194,"div",41),o.ac(195,"label",45),o.Lc(196,"Unit calculation"),o.Zb(),o.ac(197,"div",46),o.Vb(198,"input",67),o.ac(199,"label",68),o.Lc(200,"checkbox"),o.Zb(),o.Zb(),o.Zb(),o.ac(201,"div",41),o.ac(202,"label"),o.Lc(203,"Unit Amount"),o.Zb(),o.ac(204,"div",49),o.ac(205,"div",50),o.ac(206,"span",51),o.Lc(207,"$"),o.Zb(),o.Zb(),o.Vb(208,"input",69),o.Jc(209,R,2,1,"div",27),o.ac(210,"div",53),o.ac(211,"span",51),o.Lc(212,".00"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(213,"div",41),o.ac(214,"label",45),o.Lc(215,"Assignee"),o.Zb(),o.ac(216,"div",54),o.Vb(217,"input",70),o.ac(218,"label",71),o.Lc(219," No assignee "),o.Zb(),o.Zb(),o.ac(220,"div",54),o.Vb(221,"input",72),o.ac(222,"label",73),o.Lc(223," All employees "),o.Zb(),o.Zb(),o.ac(224,"div",54),o.Vb(225,"input",74),o.ac(226,"label",75),o.Lc(227," Select Employee "),o.Zb(),o.Zb(),o.ac(228,"div",41),o.ac(229,"select",61),o.ac(230,"option"),o.Lc(231,"-"),o.Zb(),o.ac(232,"option"),o.Lc(233,"Select All"),o.Zb(),o.ac(234,"option"),o.Lc(235,"John Doe"),o.Zb(),o.ac(236,"option"),o.Lc(237,"Richard Miles"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(238,"div",62),o.ac(239,"button",63),o.Lc(240,"Save"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(241,"div",76),o.ac(242,"div",77),o.ac(243,"div",34),o.ac(244,"div",39),o.ac(245,"div",78),o.ac(246,"h3"),o.Lc(247,"Delete Addition"),o.Zb(),o.ac(248,"p"),o.Lc(249,"Are you sure want to delete?"),o.Zb(),o.Zb(),o.ac(250,"div",79),o.ac(251,"div",10),o.ac(252,"div",80),o.ac(253,"a",81),o.hc("click",function(){return t.deletePayroll()}),o.Lc(254,"Delete"),o.Zb(),o.Zb(),o.ac(255,"div",80),o.ac(256,"a",82),o.Lc(257,"Cancel"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(258,"div",83),o.ac(259,"div",33),o.ac(260,"div",34),o.ac(261,"div",35),o.ac(262,"h5",36),o.Lc(263,"Add Overtime"),o.Zb(),o.ac(264,"button",37),o.ac(265,"span",38),o.Lc(266,"\xd7"),o.Zb(),o.Zb(),o.Zb(),o.ac(267,"div",39),o.ac(268,"form",40),o.hc("ngSubmit",function(){return t.addOver()}),o.ac(269,"div",41),o.ac(270,"label"),o.Lc(271,"Name "),o.ac(272,"span",42),o.Lc(273,"*"),o.Zb(),o.Zb(),o.Vb(274,"input",84),o.Jc(275,B,2,1,"div",27),o.Zb(),o.ac(276,"div",41),o.ac(277,"label"),o.Lc(278,"Rate Type "),o.ac(279,"span",42),o.Lc(280,"*"),o.Zb(),o.Zb(),o.ac(281,"select",61),o.ac(282,"option"),o.Lc(283,"-"),o.Zb(),o.ac(284,"option"),o.Lc(285,"Daily Rate"),o.Zb(),o.ac(286,"option"),o.Lc(287,"Hourly Rate"),o.Zb(),o.Zb(),o.Zb(),o.ac(288,"div",41),o.ac(289,"label"),o.Lc(290,"Rate "),o.ac(291,"span",42),o.Lc(292,"*"),o.Zb(),o.Zb(),o.Vb(293,"input",85),o.Jc(294,z,2,1,"div",27),o.Zb(),o.ac(295,"div",62),o.ac(296,"button",63),o.Lc(297,"Submit"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(298,"div",86),o.ac(299,"div",33),o.ac(300,"div",34),o.ac(301,"div",35),o.ac(302,"h5",36),o.Lc(303,"Edit Overtime"),o.Zb(),o.ac(304,"button",37),o.ac(305,"span",38),o.Lc(306,"\xd7"),o.Zb(),o.Zb(),o.Zb(),o.ac(307,"div",39),o.ac(308,"form",40),o.hc("ngSubmit",function(){return t.editOverSubmit()}),o.ac(309,"div",41),o.ac(310,"label"),o.Lc(311,"Name "),o.ac(312,"span",42),o.Lc(313,"*"),o.Zb(),o.Zb(),o.Vb(314,"input",87),o.Jc(315,J,2,1,"div",27),o.Zb(),o.ac(316,"div",41),o.ac(317,"label"),o.Lc(318,"Rate Type "),o.ac(319,"span",42),o.Lc(320,"*"),o.Zb(),o.Zb(),o.ac(321,"select",61),o.ac(322,"option"),o.Lc(323,"-"),o.Zb(),o.ac(324,"option"),o.Lc(325,"Daily Rate"),o.Zb(),o.ac(326,"option"),o.Lc(327,"Hourly Rate"),o.Zb(),o.Zb(),o.Zb(),o.ac(328,"div",41),o.ac(329,"label"),o.Lc(330,"Rate "),o.ac(331,"span",42),o.Lc(332,"*"),o.Zb(),o.Zb(),o.Vb(333,"input",88),o.Jc(334,H,2,1,"div",27),o.Zb(),o.ac(335,"div",62),o.ac(336,"button",63),o.Lc(337,"Save"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(338,"div",89),o.ac(339,"div",77),o.ac(340,"div",34),o.ac(341,"div",39),o.ac(342,"div",78),o.ac(343,"h3"),o.Lc(344,"Delete Overtime"),o.Zb(),o.ac(345,"p"),o.Lc(346,"Are you sure want to delete?"),o.Zb(),o.Zb(),o.ac(347,"div",79),o.ac(348,"div",10),o.ac(349,"div",80),o.ac(350,"a",81),o.hc("click",function(){return t.deleteOver()}),o.Lc(351,"Delete"),o.Zb(),o.Zb(),o.ac(352,"div",80),o.ac(353,"a",82),o.Lc(354,"Cancel"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(355,"div",90),o.ac(356,"div",33),o.ac(357,"div",34),o.ac(358,"div",35),o.ac(359,"h5",36),o.Lc(360,"Add Deduction"),o.Zb(),o.ac(361,"button",37),o.ac(362,"span",38),o.Lc(363,"\xd7"),o.Zb(),o.Zb(),o.Zb(),o.ac(364,"div",39),o.ac(365,"form",40),o.hc("ngSubmit",function(){return t.addDeducts()}),o.ac(366,"div",41),o.ac(367,"label"),o.Lc(368,"Name "),o.ac(369,"span",42),o.Lc(370,"*"),o.Zb(),o.Zb(),o.Vb(371,"input",91),o.Jc(372,Y,2,1,"div",27),o.Zb(),o.ac(373,"div",41),o.ac(374,"label",45),o.Lc(375,"Unit calculation"),o.Zb(),o.ac(376,"div",46),o.Vb(377,"input",92),o.ac(378,"label",93),o.Lc(379,"checkbox"),o.Zb(),o.Zb(),o.Zb(),o.ac(380,"div",41),o.ac(381,"label"),o.Lc(382,"Unit Amount"),o.Zb(),o.ac(383,"div",49),o.ac(384,"div",50),o.ac(385,"span",51),o.Lc(386,"$"),o.Zb(),o.Zb(),o.Vb(387,"input",94),o.Jc(388,X,2,1,"div",27),o.ac(389,"div",53),o.ac(390,"span",51),o.Lc(391,".00"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(392,"div",41),o.ac(393,"label",45),o.Lc(394,"Assignee"),o.Zb(),o.ac(395,"div",54),o.Vb(396,"input",95),o.ac(397,"label",96),o.Lc(398," No assignee "),o.Zb(),o.Zb(),o.ac(399,"div",54),o.Vb(400,"input",97),o.ac(401,"label",98),o.Lc(402," All employees "),o.Zb(),o.Zb(),o.ac(403,"div",54),o.Vb(404,"input",99),o.ac(405,"label",100),o.Lc(406," Select Employee "),o.Zb(),o.Zb(),o.ac(407,"div",41),o.ac(408,"select",61),o.ac(409,"option"),o.Lc(410,"-"),o.Zb(),o.ac(411,"option"),o.Lc(412,"Select All"),o.Zb(),o.ac(413,"option"),o.Lc(414,"John Doe"),o.Zb(),o.ac(415,"option"),o.Lc(416,"Richard Miles"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(417,"div",62),o.ac(418,"button",63),o.Lc(419,"Submit"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(420,"div",101),o.ac(421,"div",33),o.ac(422,"div",34),o.ac(423,"div",35),o.ac(424,"h5",36),o.Lc(425,"Edit Deduction"),o.Zb(),o.ac(426,"button",37),o.ac(427,"span",38),o.Lc(428,"\xd7"),o.Zb(),o.Zb(),o.Zb(),o.ac(429,"div",39),o.ac(430,"form",40),o.hc("ngSubmit",function(){return t.editDeductSubmit()}),o.ac(431,"div",41),o.ac(432,"label"),o.Lc(433,"Name "),o.ac(434,"span",42),o.Lc(435,"*"),o.Zb(),o.Zb(),o.Vb(436,"input",102),o.Jc(437,ee,2,1,"div",27),o.Zb(),o.ac(438,"div",41),o.ac(439,"label",45),o.Lc(440,"Unit calculation"),o.Zb(),o.ac(441,"div",46),o.Vb(442,"input",103),o.ac(443,"label",104),o.Lc(444,"checkbox"),o.Zb(),o.Zb(),o.Zb(),o.ac(445,"div",41),o.ac(446,"label"),o.Lc(447,"Unit Amount"),o.Zb(),o.ac(448,"div",49),o.ac(449,"div",50),o.ac(450,"span",51),o.Lc(451,"$"),o.Zb(),o.Zb(),o.Vb(452,"input",105),o.Jc(453,ae,2,1,"div",27),o.ac(454,"div",53),o.ac(455,"span",51),o.Lc(456,".00"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(457,"div",41),o.ac(458,"label",45),o.Lc(459,"Assignee"),o.Zb(),o.ac(460,"div",54),o.Vb(461,"input",106),o.ac(462,"label",107),o.Lc(463," No assignee "),o.Zb(),o.Zb(),o.ac(464,"div",54),o.Vb(465,"input",108),o.ac(466,"label",109),o.Lc(467," All employees "),o.Zb(),o.Zb(),o.ac(468,"div",54),o.Vb(469,"input",110),o.ac(470,"label",111),o.Lc(471," Select Employee "),o.Zb(),o.Zb(),o.ac(472,"div",41),o.ac(473,"select",61),o.ac(474,"option"),o.Lc(475,"-"),o.Zb(),o.ac(476,"option"),o.Lc(477,"Select All"),o.Zb(),o.ac(478,"option"),o.Lc(479,"John Doe"),o.Zb(),o.ac(480,"option"),o.Lc(481,"Richard Miles"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(482,"div",62),o.ac(483,"button",63),o.Lc(484,"Save"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(485,"div",112),o.ac(486,"div",77),o.ac(487,"div",34),o.ac(488,"div",39),o.ac(489,"div",78),o.ac(490,"h3");o.Lc(491,"Delete Deduction"),o.Zb(),o.ac(492,"p"),o.Lc(493,"Are you sure want to delete?"),o.Zb(),o.Zb(),o.ac(494,"div",79),o.ac(495,"div",10),o.ac(496,"div",80),o.ac(497,"a",81),o.hc("click",function(){return t.deleteDeduct()}),o.Lc(498,"Delete"),o.Zb(),o.Zb(),o.ac(499,"div",80),o.ac(500,"a",82),o.Lc(501,"Cancel"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()}2&e&&(o.Ib(45),o.pc("ngForOf",t.allAddPayroll),o.Ib(1),o.pc("ngIf",0===t.allAddPayroll.length),o.Ib(18),o.pc("ngForOf",t.allOverPayroll),o.Ib(1),o.pc("ngIf",0===t.allOverPayroll.length),o.Ib(18),o.pc("ngForOf",t.allDeductPayroll),o.Ib(1),o.pc("ngIf",0===t.allDeductPayroll.length),o.Ib(11),o.pc("formGroup",t.addPayrollForm),o.Ib(6),o.Mb("invalid",t.addPayrollForm.get("addPayrollName").invalid&&t.addPayrollForm.get("addPayrollName").touched),o.Ib(1),o.pc("ngIf",t.addPayrollForm.get("addPayrollName").invalid&&t.addPayrollForm.get("addPayrollName").touched),o.Ib(6),o.Mb("invalid",t.addPayrollForm.get("addPayrollCategory").invalid&&t.addPayrollForm.get("addPayrollCategory").touched),o.Ib(7),o.pc("ngIf",t.addPayrollForm.get("addPayrollCategory").invalid&&t.addPayrollForm.get("addPayrollCategory").touched),o.Ib(15),o.Mb("invalid",t.addPayrollForm.get("addPayrollUnit").invalid&&t.addPayrollForm.get("addPayrollUnit").touched),o.Ib(1),o.pc("ngIf",t.addPayrollForm.get("addPayrollUnit").invalid&&t.addPayrollForm.get("addPayrollUnit").touched),o.Ib(42),o.pc("formGroup",t.editPayrollForm),o.Ib(6),o.Mb("invalid",t.editPayrollForm.get("editPayrollName").invalid&&t.editPayrollForm.get("editPayrollName").touched),o.Ib(1),o.pc("ngIf",t.editPayrollForm.get("editPayrollName").invalid&&t.editPayrollForm.get("editPayrollName").touched),o.Ib(6),o.Mb("invalid",t.editPayrollForm.get("editPayrollCategory").invalid&&t.editPayrollForm.get("editPayrollCategory").touched),o.Ib(7),o.pc("ngIf",t.editPayrollForm.get("editPayrollCategory").invalid&&t.editPayrollForm.get("editPayrollCategory").touched),o.Ib(15),o.Mb("invalid",t.editPayrollForm.get("editPayrollUnit").invalid&&t.editPayrollForm.get("editPayrollUnit").touched),o.Ib(1),o.pc("ngIf",t.editPayrollForm.get("editPayrollUnit").invalid&&t.editPayrollForm.get("editPayrollUnit").touched),o.Ib(59),o.pc("formGroup",t.addOverForm),o.Ib(6),o.Mb("invalid",t.addOverForm.get("addOverName").invalid&&t.addOverForm.get("addOverName").touched),o.Ib(1),o.pc("ngIf",t.addOverForm.get("addOverName").invalid&&t.addOverForm.get("addOverName").touched),o.Ib(18),o.Mb("invalid",t.addOverForm.get("addOverRate").invalid&&t.addOverForm.get("addOverRate").touched),o.Ib(1),o.pc("ngIf",t.addOverForm.get("addOverRate").invalid&&t.addOverForm.get("addOverRate").touched),o.Ib(14),o.pc("formGroup",t.editOverForm),o.Ib(6),o.Mb("invalid",t.editOverForm.get("editOverName").invalid&&t.editOverForm.get("editOverName").touched),o.Ib(1),o.pc("ngIf",t.editOverForm.get("editOverName").invalid&&t.editOverForm.get("editOverName").touched),o.Ib(18),o.Mb("invalid",t.editOverForm.get("editOverRate").invalid&&t.editOverForm.get("editOverRate").touched),o.Ib(1),o.pc("ngIf",t.editOverForm.get("editOverRate").invalid&&t.editOverForm.get("editOverRate").touched),o.Ib(31),o.pc("formGroup",t.addDeductForm),o.Ib(6),o.Mb("invalid",t.addDeductForm.get("addDeductName").invalid&&t.addDeductForm.get("addDeductName").touched),o.Ib(1),o.pc("ngIf",t.addDeductForm.get("addDeductName").invalid&&t.addDeductForm.get("addDeductName").touched),o.Ib(15),o.Mb("invalid",t.addDeductForm.get("addDeductUnit").invalid&&t.addDeductForm.get("addDeductUnit").touched),o.Ib(1),o.pc("ngIf",t.addDeductForm.get("addDeductUnit").invalid&&t.addDeductForm.get("addDeductUnit").touched),o.Ib(42),o.pc("formGroup",t.editDeductForm),o.Ib(6),o.Mb("invalid",t.editDeductForm.get("editDeductName").invalid&&t.editDeductForm.get("editDeductName").touched),o.Ib(1),o.pc("ngIf",t.editDeductForm.get("editDeductName").invalid&&t.editDeductForm.get("editDeductName").touched),o.Ib(15),o.Mb("invalid",t.editDeductForm.get("editDeductunit").invalid&&t.editDeductForm.get("editDeductunit").touched),o.Ib(1),o.pc("ngIf",t.editDeductForm.get("editDeductunit").invalid&&t.editDeductForm.get("editDeductunit").touched))},directives:[c.e,i.l,i.m,s.x,s.p,s.h,s.b,s.o,s.f,s.v,s.s,s.y],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),ce=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.datePipe=t,this.payrollService=a,this.route=i,this.router=c,this.spinnerService=o,this.baseUrl=d.a.baseUrl}ngOnInit(){this.basicForm=this.formBuilder.group({year:[""],month:[""],totalPayableDay:[""],totalDisburseDay:[""],fromDate:[""],toDate:[""]}),this.initializeForm(),this.loadData(),document.querySelectorAll(".btn-ripple").forEach(e=>{e.addEventListener("click",function(e){!function(e){const t=e.currentTarget;let a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left=`${a}px`,c.style.top=`${i}px`,t.appendChild(c),setTimeout(()=>{c.remove()},1e3)}(e)})})}initializeForm(){this.setFormDefaultValues()}basicFormSubmit(){let e=this.baseUrl+"/salaryProcess/start",t={abc:"OK"};t=this.basicForm.value,t.procFromDate=t.fromDate?this.datePipe.transform(t.fromDate,"yyyy-MM-dd").toString().slice(0,10):null,t.procToDate=t.toDate?this.datePipe.transform(t.toDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.payrollService.sendPostRequest(e,t).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/payroll/salary-process-list"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide()})}loadData(){}daysInThisMonth(e){return new Date(e.getFullYear(),e.getMonth()+1,0).getDate()}getMonthFirstDate(e){return new Date(e.getFullYear(),e.getMonth(),1)}getMonthLastDate(e){return e=new Date,new Date(e.getFullYear(),e.getMonth()+1,0)}onChangeYear(e){console.log(e)}onChangeMonth(e){console.log(e),console.log(parseInt(e));let t=this.basicForm.get("year").value,a=parseInt(e);var i=new Date(t,a-1,1);let c=this.daysInThisMonth(i);this.basicForm.controls.totalPayableDay.setValue(c),this.basicForm.controls.totalDisburseDay.setValue(c),this.basicForm.controls.fromDate.setValue(this.getMonthFirstDate(i)),this.basicForm.controls.toDate.setValue(this.getMonthLastDate(i))}setFormDefaultValues(){var e=(new Date).getFullYear();this.basicForm.patchValue({year:e}),this.basicForm.controls.year.setValue(e)}resetFormValues(){this.basicForm=this.formBuilder.group({year:[""],month:[""],totalPayableDay:[""],totalDisburseDay:[""],fromDate:[""],toDate:[""]}),this.setFormDefaultValues()}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(s.d),o.Ub(i.e),o.Ub(l.a),o.Ub(c.a),o.Ub(c.c),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll-salary-process"]],decls:105,vars:2,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/salary-process-list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-title","mb-0"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","year",1,"select","form-control",3,"change"],["value",""],["value","2021","selected",""],["value","2022"],["value","2023"],["value","2024"],["value","2025"],["formControlName","month",1,"select","form-control",3,"change"],["value","","selected",""],["value","01"],["value","02"],["value","03"],["value","04"],["value","05"],["value","06"],["value","07"],["value","08"],["value","09"],["value","10"],["value","11"],["value","12"],["type","text","formControlName","totalPayableDay",1,"form-control"],["type","text","formControlName","totalDisburseDay",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","fromDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","toDate","bsDatepicker","",1,"form-control"],[1,"text-right"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-arrow-circle-o-right"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Salary Process"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Salary Process"),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",9),o.ac(13,"a",10),o.Vb(14,"i",11),o.Lc(15," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",12),o.ac(17,"div",13),o.ac(18,"div",14),o.ac(19,"div",15),o.ac(20,"h4",16),o.Lc(21,"Process Parameters"),o.Zb(),o.Zb(),o.ac(22,"div",17),o.ac(23,"form",18),o.hc("ngSubmit",function(){return t.basicFormSubmit()}),o.ac(24,"div",19),o.ac(25,"label",20),o.Lc(26,"Year"),o.Zb(),o.ac(27,"div",21),o.ac(28,"select",22),o.hc("change",function(e){return t.onChangeYear(e.target.value)}),o.ac(29,"option",23),o.Lc(30,"Select Year"),o.Zb(),o.ac(31,"option",24),o.Lc(32,"2021"),o.Zb(),o.ac(33,"option",25),o.Lc(34,"2022"),o.Zb(),o.ac(35,"option",26),o.Lc(36,"2023"),o.Zb(),o.ac(37,"option",27),o.Lc(38,"2024"),o.Zb(),o.ac(39,"option",28),o.Lc(40,"2025"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(41,"div",19),o.ac(42,"label",20),o.Lc(43,"Month"),o.Zb(),o.ac(44,"div",21),o.ac(45,"select",29),o.hc("change",function(e){return t.onChangeMonth(e.target.value)}),o.ac(46,"option",30),o.Lc(47,"Select Month"),o.Zb(),o.ac(48,"option",31),o.Lc(49,"January"),o.Zb(),o.ac(50,"option",32),o.Lc(51,"February"),o.Zb(),o.ac(52,"option",33),o.Lc(53,"March"),o.Zb(),o.ac(54,"option",34),o.Lc(55,"April"),o.Zb(),o.ac(56,"option",35),o.Lc(57,"May"),o.Zb(),o.ac(58,"option",36),o.Lc(59,"Jun"),o.Zb(),o.ac(60,"option",37),o.Lc(61,"July"),o.Zb(),o.ac(62,"option",38),o.Lc(63,"August"),o.Zb(),o.ac(64,"option",39),o.Lc(65,"September"),o.Zb(),o.ac(66,"option",40),o.Lc(67,"October"),o.Zb(),o.ac(68,"option",41),o.Lc(69,"November"),o.Zb(),o.ac(70,"option",42),o.Lc(71,"December"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(72,"div",19),o.ac(73,"label",20),o.Lc(74,"Total Payable Day"),o.Zb(),o.ac(75,"div",21),o.Vb(76,"input",43),o.Zb(),o.Zb(),o.ac(77,"div",19),o.ac(78,"label",20),o.Lc(79,"Total Disburse Day"),o.Zb(),o.ac(80,"div",21),o.Vb(81,"input",44),o.Zb(),o.Zb(),o.ac(82,"div",19),o.ac(83,"label",20),o.Lc(84,"Process From Date"),o.Zb(),o.ac(85,"div",21),o.ac(86,"div",45),o.Vb(87,"input",46),o.Zb(),o.Zb(),o.Zb(),o.ac(88,"div",19),o.ac(89,"label",20),o.Lc(90,"Process To Date"),o.Zb(),o.ac(91,"div",21),o.ac(92,"div",45),o.Vb(93,"input",47),o.Zb(),o.Zb(),o.Zb(),o.ac(94,"div",48),o.ac(95,"button",49),o.hc("click",function(){return t.resetFormValues()}),o.Vb(96,"i",50),o.Lc(97," Reset "),o.Zb(),o.Lc(98," \xa0 \xa0 \xa0 "),o.ac(99,"button",51),o.Vb(100,"i",52),o.Lc(101," Start Process "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(102,"ngx-spinner",53),o.ac(103,"p",54),o.Lc(104," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(23),o.pc("formGroup",t.basicForm),o.Ib(79),o.pc("fullScreen",!1))},directives:[c.e,s.x,s.p,s.h,s.v,s.o,s.f,s.s,s.y,s.b,p.b,p.a,b.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}.btn-ripple[_ngcontent-%COMP%]{position:relative;overflow:hidden}.btn-ripple[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:absolute;background:#fff;transform:translate(-50%,-50%);pointer-events:none;border-radius:50%;-webkit-animation:animate 1s linear infinite;animation:animate 1s linear infinite}@-webkit-keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}@keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}"]}),e})();var oe=a("PqYM");function ne(e,t){if(1&e&&(o.ac(0,"tr"),o.ac(1,"td"),o.Lc(2),o.Zb(),o.ac(3,"td",35),o.Lc(4),o.Zb(),o.ac(5,"td"),o.Lc(6),o.Zb(),o.ac(7,"td"),o.Lc(8),o.Zb(),o.ac(9,"td"),o.Lc(10),o.Zb(),o.ac(11,"td"),o.Lc(12),o.Zb(),o.ac(13,"td"),o.Lc(14),o.Zb(),o.ac(15,"td"),o.Lc(16),o.Zb(),o.ac(17,"td"),o.Vb(18,"i",46),o.Zb(),o.ac(19,"td"),o.Lc(20),o.Zb(),o.ac(21,"td"),o.Lc(22),o.Zb(),o.ac(23,"td"),o.Lc(24),o.Zb(),o.ac(25,"td"),o.ac(26,"a",47),o.Lc(27,"View"),o.Zb(),o.Zb(),o.Zb()),2&e){const e=t.$implicit,a=t.index,i=o.jc();o.Mb("active",a==i.currentIndex),o.Ib(2),o.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),o.Ib(2),o.Mc(e.id),o.Ib(2),o.Mc(e.year),o.Ib(2),o.Mc(e.month),o.Ib(2),o.Mc(e.totalPayableDay),o.Ib(2),o.Mc(e.totalDisburseDay),o.Ib(2),o.Mc(e.procFromDate),o.Ib(2),o.Mc(e.procToDate),o.Ib(4),o.Mc(e.jobStartTime),o.Ib(2),o.Mc(e.jobEndTime),o.Ib(2),o.Mc(e.jobRunUser),o.Ib(2),o.rc("routerLink","/payroll/payslip/",e.id,"")}}function re(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",48),o.ac(2,"h5",49),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function le(e,t){if(1&e&&(o.ac(0,"option",50),o.Lc(1),o.Zb()),2&e){const e=t.$implicit;o.pc("value",e),o.Ib(1),o.Nc(" ",e," ")}}const de=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let se=(()=>{class e{constructor(e,t){this.payrollService=e,this.spinnerService=t,this.baseUrl=d.a.baseUrl,this.everyFiveSeconds=Object(oe.a)(0,11e3),this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:10,totalItem:50,pageSizes:[3,5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:10,currentPage:1,totalItems:50}}ngOnInit(){this.myFromGroup=new s.g({pageSize:new s.e}),this.myFromGroup.get("pageSize").setValue(this.configPgn.pageSize),this.bindFromFloatingLabel(),this.getListData(),this.subscription=this.everyFiveSeconds.subscribe(()=>{this.getListData()})}ngAfterViewInit(){setTimeout(()=>{},1e3)}bindFromFloatingLabel(){var e=this;$(".floating").length>0&&$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),$(".filter-row").find("input, select, textarea").keyup(function(t){console.log(t.keyCode),13==t.keyCode&&e.getSearchData()})}searchByFromDate(e){let t=this.pipe.transform(e,"yyyy-MM-dd");this.srcFromDate=t,console.log(t),this.bindFromFloatingLabel()}searchByToDate(e){let t=this.pipe.transform(e,"yyyy-MM-dd");this.srcToDate=t,console.log(t),this.bindFromFloatingLabel()}searchByEmpCode(e){console.log(e),this.srcEmpCode=e}searchBySearchButton(){console.log(this.srcFromDate),console.log(this.srcToDate),console.log(this.srcEmpCode),this.getListData()}getSearchData(){this.getListData()}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.srcEmpCode&&(a.empCode=this.srcEmpCode),this.srcFromDate&&this.srcToDate&&(a.fromDate=this.srcFromDate,a.toDate=this.srcToDate),a}getListData(){let e=this.baseUrl+"/api/salaryProcessJP/getList",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.payrollService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}ngOnDestroy(){this.subscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(l.a),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-salary-process-job-list"]],decls:94,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-2","col-12"],[1,"btn","btn-success","btn-block",3,"click"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/payroll/salary-process/",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"d-none"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"",3,"formGroup"],["formControlName","pageSize",1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],[1,"fa","fa-spinner","fa-pulse"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Salary Process"),o.Zb(),o.Vb(6,"ul",5),o.Zb(),o.ac(7,"div",6),o.ac(8,"div",7),o.ac(9,"button",8),o.Lc(10,"Excel"),o.Zb(),o.ac(11,"button",8),o.Lc(12,"PDF"),o.Zb(),o.ac(13,"button",8),o.Vb(14,"i",9),o.Lc(15," Print"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",10),o.ac(17,"div",11),o.ac(18,"div",12),o.ac(19,"div",13),o.ac(20,"div",14),o.ac(21,"input",15),o.hc("input",function(e){return t.searchByEmpCode(e.target.value)}),o.Zb(),o.ac(22,"label",16),o.Lc(23,"Employee Code"),o.Zb(),o.Zb(),o.Zb(),o.ac(24,"div",17),o.ac(25,"div",14),o.ac(26,"div",18),o.ac(27,"input",19),o.hc("bsValueChange",function(e){return t.searchByFromDate(e)}),o.Zb(),o.Zb(),o.ac(28,"label",16),o.Lc(29,"From"),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",17),o.ac(31,"div",14),o.ac(32,"div",18),o.ac(33,"input",19),o.hc("bsValueChange",function(e){return t.searchByToDate(e)}),o.Zb(),o.Zb(),o.ac(34,"label",16),o.Lc(35,"To"),o.Zb(),o.Zb(),o.Zb(),o.ac(36,"div",20),o.ac(37,"a",21),o.hc("click",function(){return t.searchBySearchButton()}),o.Lc(38," Search "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(39,"div",22),o.ac(40,"div",23),o.ac(41,"div",24),o.ac(42,"div",25),o.ac(43,"div",26),o.ac(44,"a",27),o.Vb(45,"i",28),o.Lc(46," New \xa0\xa0\xa0"),o.Zb(),o.Zb(),o.Zb(),o.ac(47,"div",29),o.ac(48,"div",30),o.ac(49,"div",31),o.ac(50,"div",32),o.ac(51,"span",33),o.Lc(52),o.Zb(),o.Zb(),o.Zb(),o.ac(53,"table",34),o.ac(54,"thead"),o.ac(55,"tr"),o.ac(56,"th"),o.Lc(57,"SL"),o.Zb(),o.ac(58,"th",35),o.Lc(59,"TB_ROW_ID"),o.Zb(),o.ac(60,"th"),o.Lc(61,"Year"),o.Zb(),o.ac(62,"th"),o.Lc(63,"Month"),o.Zb(),o.ac(64,"th"),o.Lc(65,"Total Payable Day"),o.Zb(),o.ac(66,"th"),o.Lc(67,"Total Disburse Day"),o.Zb(),o.ac(68,"th"),o.Lc(69,"Proc From Date"),o.Zb(),o.ac(70,"th"),o.Lc(71,"Proc To Date"),o.Zb(),o.ac(72,"th"),o.Lc(73,"Status"),o.Zb(),o.ac(74,"th"),o.Lc(75,"Job Start Time"),o.Zb(),o.ac(76,"th"),o.Lc(77,"Job End Time"),o.Zb(),o.ac(78,"th"),o.Lc(79,"Job Run User"),o.Zb(),o.Zb(),o.Zb(),o.ac(80,"tbody"),o.Jc(81,ne,28,14,"tr",36),o.kc(82,"paginate"),o.Jc(83,re,4,0,"tr",37),o.Zb(),o.Zb(),o.ac(84,"div",38),o.ac(85,"div",39),o.Lc(86," Items per Page "),o.ac(87,"select",40),o.hc("change",function(e){return t.handlePageSizeChange(e)}),o.Jc(88,le,2,2,"option",41),o.Zb(),o.Zb(),o.ac(89,"div",42),o.ac(90,"pagination-controls",43),o.hc("pageChange",function(e){return t.handlePageChange(e)}),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(91,"ngx-spinner",44),o.ac(92,"p",45),o.Lc(93," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(27),o.pc("bsConfig",o.sc(13,de)),o.Ib(6),o.pc("bsConfig",o.sc(14,de)),o.Ib(19),o.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),o.Ib(29),o.pc("ngForOf",o.mc(82,10,t.listData,t.configPgn)),o.Ib(2),o.pc("ngIf",0===t.listData.length),o.Ib(2),o.pc("formGroup",t.myFromGroup),o.Ib(3),o.pc("ngForOf",t.configPgn.pageSizes),o.Ib(3),o.pc("fullScreen",!1))},directives:[p.b,p.a,c.e,i.l,i.m,s.p,s.h,s.v,s.o,s.f,m.c,b.a,s.s,s.y],pipes:[m.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}.page-header[_ngcontent-%COMP%]{margin-bottom:1.1rem}"]}),e})();function be(e,t){if(1&e){const e=o.bc();o.ac(0,"tr"),o.ac(1,"td"),o.Lc(2),o.Zb(),o.ac(3,"td",34),o.Lc(4),o.Zb(),o.ac(5,"td"),o.Lc(6),o.Zb(),o.ac(7,"td"),o.Lc(8),o.Zb(),o.ac(9,"td"),o.Lc(10),o.Zb(),o.ac(11,"td"),o.Lc(12),o.Zb(),o.ac(13,"td"),o.Lc(14),o.Zb(),o.ac(15,"td"),o.Lc(16),o.Zb(),o.ac(17,"td"),o.Lc(18),o.Zb(),o.ac(19,"td"),o.Lc(20),o.Zb(),o.ac(21,"td"),o.ac(22,"a",54),o.Vb(23,"i",55),o.Lc(24,"View"),o.Zb(),o.Lc(25," \xa0 "),o.ac(26,"a",56),o.Vb(27,"i",57),o.Zb(),o.Lc(28,"\xa0\xa0 "),o.ac(29,"a",58),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().tempId=a.id}),o.Vb(30,"i",59),o.Zb(),o.Zb(),o.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=o.jc();o.Mb("active",a==i.currentIndex),o.Ib(2),o.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),o.Ib(2),o.Mc(e.id),o.Ib(2),o.Mc(e.empTitle),o.Ib(2),o.Mc(i.titleDecode[e.elementTitle]),o.Ib(2),o.Mc(e.elementAmount),o.Ib(2),o.Mc(e.activeStartDate),o.Ib(2),o.Mc(e.activeEndDate),o.Ib(2),o.Mc(e.isActive),o.Ib(2),o.Mc(e.creationDateTime),o.Ib(2),o.Mc(e.creationUser),o.Ib(2),o.rc("routerLink","/payroll/element-value/show/",e.id,""),o.Ib(4),o.rc("routerLink","/payroll/element-value/edit/",e.id,"")}}function pe(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",60),o.ac(2,"h5",61),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function me(e,t){if(1&e&&(o.ac(0,"option",62),o.Lc(1),o.Zb()),2&e){const e=t.$implicit;o.pc("value",e),o.Ib(1),o.Nc(" ",e," ")}}const ue=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let ge=(()=>{class e{constructor(e,t,a,c,o){this.payrollService=e,this.spinnerService=t,this.route=a,this.router=c,this.toastr=o,this.baseUrl=d.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.titleDecode={HRA:"House Rent Allowance",MDL_ALW:"Medical Allowance",OT_ALW:"Overtime Allowance",LTA:"Leave Travel Allowance",EA:"Entertainment Allowance",UCA_ALW:"Uniform Allowance / Corporate Attire",FAMILY_ALW:"Family Allowance",EDA:"Education Allowance",PRJ_ALW:"Project Allowance",HOSTEL_ALW:"Hostel Allowance",CHILD_EDU_ALW:"Children's education Allowance",CHILD_HOSTEL_ALW:"Children's hostel Allowance",PROF_ALW:"Professional pursuit/research Allowance"},this.configPgn={pageNum:1,pageSize:10,totalItem:50,pageSizes:[3,5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:10,currentPage:1,totalItems:50}}ngOnInit(){this.myFromGroup=new s.g({pageSize:new s.e}),this.myFromGroup.get("pageSize").setValue(this.configPgn.pageSize),this.bindFromFloatingLabel(),this.getListData()}ngAfterViewInit(){setTimeout(()=>{},1e3)}bindFromFloatingLabel(){var e=this;$(".floating").length>0&&$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),$(".filter-row").find("input, select, textarea").keyup(function(t){console.log(t.keyCode),13==t.keyCode&&e.getSearchData()})}searchByFromDate(e){let t=this.pipe.transform(e,"yyyy-MM-dd");this.srcFromDate=t,console.log(t),this.bindFromFloatingLabel()}searchByToDate(e){let t=this.pipe.transform(e,"yyyy-MM-dd");this.srcToDate=t,console.log(t),this.bindFromFloatingLabel()}searchByEmpCode(e){console.log(e),this.srcEmpCode=e}searchBySearchButton(){console.log(this.srcFromDate),console.log(this.srcToDate),console.log(this.srcEmpCode),this.getListData()}getSearchData(){this.getListData()}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.srcEmpCode&&(a.empCode=this.srcEmpCode),this.srcFromDate&&this.srcToDate&&(a.fromDate=this.srcFromDate,a.toDate=this.srcToDate),a}getListData(){let e=this.baseUrl+"/api/payrollElValue/getList",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.payrollService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/api/payrollElValue/delete/"+e;console.log(t),this.spinnerService.show(),this.payrollService.sendDeleteRequest(t,{rEntityName:"PayrollElementValue",rActiveOperation:"delete"}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getListData()},e=>{console.log(e),this.spinnerService.hide()})}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(l.a),o.Ub(b.c),o.Ub(c.a),o.Ub(c.c),o.Ub(L.b))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll-element-value-list"]],decls:109,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"btn","btn-success","btn-block",3,"click"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/payroll/element-value/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"d-none"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"",3,"formGroup"],["formControlName","pageSize",1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"fa","fa-eye","m-r-5"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Payroll Element"),o.Zb(),o.Vb(6,"ul",5),o.Zb(),o.ac(7,"div",6),o.ac(8,"div",7),o.ac(9,"button",8),o.Lc(10,"Excel"),o.Zb(),o.ac(11,"button",8),o.Lc(12,"PDF"),o.Zb(),o.ac(13,"button",8),o.Vb(14,"i",9),o.Lc(15," Print"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",10),o.ac(17,"div",11),o.ac(18,"div",12),o.ac(19,"div",13),o.ac(20,"div",14),o.ac(21,"input",15),o.hc("input",function(e){return t.searchByEmpCode(e.target.value)}),o.Zb(),o.ac(22,"label",16),o.Lc(23,"Employee Code"),o.Zb(),o.Zb(),o.Zb(),o.ac(24,"div",17),o.ac(25,"div",14),o.ac(26,"div",18),o.ac(27,"input",19),o.hc("bsValueChange",function(e){return t.searchByFromDate(e)}),o.Zb(),o.Zb(),o.ac(28,"label",16),o.Lc(29,"From"),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",17),o.ac(31,"div",14),o.ac(32,"div",18),o.ac(33,"input",19),o.hc("bsValueChange",function(e){return t.searchByToDate(e)}),o.Zb(),o.Zb(),o.ac(34,"label",16),o.Lc(35,"To"),o.Zb(),o.Zb(),o.Zb(),o.ac(36,"div",17),o.ac(37,"a",20),o.hc("click",function(){return t.searchBySearchButton()}),o.Lc(38," Search "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(39,"div",21),o.ac(40,"div",22),o.ac(41,"div",23),o.ac(42,"div",24),o.ac(43,"div",25),o.ac(44,"a",26),o.Vb(45,"i",27),o.Lc(46," New \xa0\xa0\xa0"),o.Zb(),o.Zb(),o.Zb(),o.ac(47,"div",28),o.ac(48,"div",29),o.ac(49,"div",30),o.ac(50,"div",31),o.ac(51,"span",32),o.Lc(52),o.Zb(),o.Zb(),o.Zb(),o.ac(53,"table",33),o.ac(54,"thead"),o.ac(55,"tr"),o.ac(56,"th"),o.Lc(57,"SL"),o.Zb(),o.ac(58,"th",34),o.Lc(59,"TB_ROW_ID"),o.Zb(),o.ac(60,"th"),o.Lc(61,"Employee"),o.Zb(),o.ac(62,"th"),o.Lc(63,"Element Title"),o.Zb(),o.ac(64,"th"),o.Lc(65,"Amount"),o.Zb(),o.ac(66,"th"),o.Lc(67,"Start Date"),o.Zb(),o.ac(68,"th"),o.Lc(69,"End Date"),o.Zb(),o.ac(70,"th"),o.Lc(71,"Status"),o.Zb(),o.ac(72,"th"),o.Lc(73,"Creation Time"),o.Zb(),o.ac(74,"th"),o.Lc(75,"Creation User"),o.Zb(),o.ac(76,"th"),o.Lc(77,"Action"),o.Zb(),o.Zb(),o.Zb(),o.ac(78,"tbody"),o.Jc(79,be,31,14,"tr",35),o.kc(80,"paginate"),o.Jc(81,pe,4,0,"tr",36),o.Zb(),o.Zb(),o.ac(82,"div",37),o.ac(83,"div",38),o.Lc(84," Items per Page "),o.ac(85,"select",39),o.hc("change",function(e){return t.handlePageSizeChange(e)}),o.Jc(86,me,2,2,"option",40),o.Zb(),o.Zb(),o.ac(87,"div",41),o.ac(88,"pagination-controls",42),o.hc("pageChange",function(e){return t.handlePageChange(e)}),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(89,"ngx-spinner",43),o.ac(90,"p",44),o.Lc(91," Processing... "),o.Zb(),o.Zb(),o.ac(92,"div",45),o.ac(93,"div",46),o.ac(94,"div",47),o.ac(95,"div",48),o.ac(96,"div",49),o.ac(97,"h3"),o.Lc(98,"Delete Item"),o.Zb(),o.ac(99,"p"),o.Lc(100,"Are you sure want to delete?"),o.Zb(),o.Zb(),o.ac(101,"div",50),o.ac(102,"div",21),o.ac(103,"div",51),o.ac(104,"a",52),o.hc("click",function(){return t.deleteEnityData(t.tempId)}),o.Lc(105,"Delete"),o.Zb(),o.Zb(),o.ac(106,"div",51),o.ac(107,"a",53),o.Lc(108,"Cancel"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&e&&(o.Ib(27),o.pc("bsConfig",o.sc(13,ue)),o.Ib(6),o.pc("bsConfig",o.sc(14,ue)),o.Ib(19),o.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),o.Ib(27),o.pc("ngForOf",o.mc(80,10,t.listData,t.configPgn)),o.Ib(2),o.pc("ngIf",0===t.listData.length),o.Ib(2),o.pc("formGroup",t.myFromGroup),o.Ib(3),o.pc("ngForOf",t.configPgn.pageSizes),o.Ib(3),o.pc("fullScreen",!1))},directives:[p.b,p.a,c.e,i.l,i.m,s.p,s.h,s.v,s.o,s.f,m.c,b.a,s.s,s.y],pipes:[m.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}.content[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{margin-bottom:1.075rem}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})(),he=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.datePipe=t,this.payrollService=a,this.route=i,this.router=c,this.spinnerService=o,this.baseUrl=d.a.baseUrl}ngOnInit(){this.initializeForm(),this.setFormDefaultValues(),this.initButtonsRippleEffect(),this.loadData()}initializeForm(){this.myForm=this.formBuilder.group({empTitle:[""],elementTitle:[""],elementAmount:[""],activeStartDate:[""],activeEndDate:[""],isActive:[""]})}setFormDefaultValues(){(new Date).getFullYear()}resetFormValues(){this.myForm.reset(),this.setFormDefaultValues()}initButtonsRippleEffect(){document.querySelectorAll(".btn-ripple").forEach(e=>{e.addEventListener("click",function(e){!function(e){const t=e.currentTarget;let a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left=`${a}px`,c.style.top=`${i}px`,t.appendChild(c),setTimeout(()=>{c.remove()},1e3)}(e)})})}myFormSubmit(){let e=this.baseUrl+"/api/payrollElValue/create",t={};t=this.myForm.value,t.rActiveOperation="Create",t.activeStartDate=t.activeStartDate?this.datePipe.transform(t.activeStartDate,"yyyy-MM-dd").toString().slice(0,10):null,t.activeEndDate=t.activeEndDate?this.datePipe.transform(t.activeEndDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.payrollService.sendPostRequest(e,t).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/payroll/element-value/list"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide()})}loadData(){}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(s.d),o.Ub(i.e),o.Ub(l.a),o.Ub(c.a),o.Ub(c.c),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll-element-value-create"]],decls:93,vars:2,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/element-value/list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","empTitle",1,"form-control"],["formControlName","elementTitle",1,"form-control"],["value",""],["value","HRA"],["value","MDL_ALW"],["value","OT_ALW"],["value","LTA"],["value","EA"],["value","UCA_ALW"],["value","FAMILY_ALW"],["value","EDA"],["value","PRJ_ALW"],["value","HOSTEL_ALW"],["value","CHILD_EDU_ALW"],["value","CHILD_HOSTEL_ALW"],["value","PROF_ALW"],["type","text","formControlName","elementAmount",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","activeStartDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","activeEndDate","bsDatepicker","",1,"form-control"],[1,"text-right"],["routerLink","/payroll/element-value/list",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Payroll Element"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payroll Element"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Create"),o.Zb(),o.Zb(),o.Zb(),o.ac(14,"div",9),o.ac(15,"a",10),o.Vb(16,"i",11),o.Lc(17," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(18,"div",12),o.ac(19,"div",13),o.ac(20,"div",14),o.ac(21,"div",15),o.ac(22,"form",16),o.hc("ngSubmit",function(){return t.myFormSubmit()}),o.ac(23,"div",17),o.ac(24,"label",18),o.Lc(25,"Employee"),o.Zb(),o.ac(26,"div",19),o.Vb(27,"input",20),o.Zb(),o.Zb(),o.ac(28,"div",17),o.ac(29,"label",18),o.Lc(30,"Element Title"),o.Zb(),o.ac(31,"div",19),o.ac(32,"select",21),o.ac(33,"option",22),o.Lc(34,"Select"),o.Zb(),o.ac(35,"option",23),o.Lc(36,"House Rent Allowance"),o.Zb(),o.ac(37,"option",24),o.Lc(38,"Medical Allowance"),o.Zb(),o.ac(39,"option",25),o.Lc(40,"Overtime Allowance"),o.Zb(),o.ac(41,"option",26),o.Lc(42,"Leave Travel Allowance"),o.Zb(),o.ac(43,"option",27),o.Lc(44,"Entertainment Allowance"),o.Zb(),o.ac(45,"option",28),o.Lc(46,"Uniform Allowance / Corporate Attire"),o.Zb(),o.ac(47,"option",29),o.Lc(48,"Family Allowance"),o.Zb(),o.ac(49,"option",30),o.Lc(50,"Education Allowance"),o.Zb(),o.ac(51,"option",31),o.Lc(52,"Project Allowance"),o.Zb(),o.ac(53,"option",32),o.Lc(54,"Hostel Allowance"),o.Zb(),o.ac(55,"option",33),o.Lc(56,"Children's education Allowance"),o.Zb(),o.ac(57,"option",34),o.Lc(58,"Children's hostel Allowance"),o.Zb(),o.ac(59,"option",35),o.Lc(60,"Professional pursuit/research Allowance"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(61,"div",17),o.ac(62,"label",18),o.Lc(63,"Amount"),o.Zb(),o.ac(64,"div",19),o.Vb(65,"input",36),o.Zb(),o.Zb(),o.ac(66,"div",17),o.ac(67,"label",18),o.Lc(68,"Start Date"),o.Zb(),o.ac(69,"div",19),o.ac(70,"div",37),o.Vb(71,"input",38),o.Zb(),o.Zb(),o.Zb(),o.ac(72,"div",17),o.ac(73,"label",18),o.Lc(74,"End Date"),o.Zb(),o.ac(75,"div",19),o.ac(76,"div",37),o.Vb(77,"input",39),o.Zb(),o.Zb(),o.Zb(),o.ac(78,"div",40),o.ac(79,"a",41),o.Vb(80,"i",11),o.Lc(81," Cancel"),o.Zb(),o.Lc(82," \xa0 \xa0 "),o.ac(83,"button",42),o.hc("click",function(){return t.resetFormValues()}),o.Vb(84,"i",43),o.Lc(85," Reset "),o.Zb(),o.Lc(86," \xa0 \xa0 "),o.ac(87,"button",44),o.Vb(88,"i",45),o.Lc(89," Save \xa0\xa0\xa0 "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(90,"ngx-spinner",46),o.ac(91,"p",47),o.Lc(92," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(22),o.pc("formGroup",t.myForm),o.Ib(68),o.pc("fullScreen",!1))},directives:[c.e,s.x,s.p,s.h,s.b,s.o,s.f,s.v,s.s,s.y,p.b,p.a,b.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})();const fe=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let Ze=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.datePipe=t,this.payrollService=a,this.route=i,this.router=c,this.spinnerService=o,this.baseUrl=d.a.baseUrl,this.myFormData={}}ngOnInit(){this.initializeForm(),this.setFormDefaultValues(),this.initButtonsRippleEffect(),this.getFormData()}ngOnDestroy(){}initializeForm(){this.myForm=this.formBuilder.group({id:[""],empTitle:[""],elementTitle:[""],elementAmount:[""],activeStartDate:[""],activeEndDate:[""],isActive:[""]})}setFormDefaultValues(){}resetFormValues(){this.myForm.reset(),this.setFormDefaultValues()}initButtonsRippleEffect(){document.querySelectorAll(".btn-ripple").forEach(e=>{e.addEventListener("click",function(e){!function(e){const t=e.currentTarget;let a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left=`${a}px`,c.style.top=`${i}px`,t.appendChild(c),setTimeout(()=>{c.remove()},1e3)}(e)})})}convertToISODateString(e){let t=e.split("-");return t[2]+"-"+t[1]+t[0]}getFormData(){let e=this.baseUrl+"/api/payrollElValue/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.payrollService.sendGetRequest(e,{rEntityName:"PayrollElementValue",rActiveOpetation:"read"}).subscribe(e=>{this.myFormData=e,this.myFormData.activeStartDate=this.myFormData.activeStartDate?this.datePipe.transform(this.myFormData.activeStartDate,"dd-MM-yyyy"):null,this.myFormData.activeEndDate=this.myFormData.activeEndDate?this.datePipe.transform(this.myFormData.activeEndDate,"dd-MM-yyyy"):null,this.spinnerService.hide(),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){let e=this.baseUrl+"/api/payrollElValue/update/"+this.myForm.value.id;console.log(e);let t={};t=this.myForm.value,t.rEntityName="PayrollElementValue",t.rActiveOperation="update",t.activeStartDate=t.activeStartDate?this.datePipe.transform(this.convertToISODateString(t.activeStartDate),"yyyy-MM-dd"):null,t.activeEndDate=t.activeEndDate?this.datePipe.transform(this.convertToISODateString(t.activeEndDate),"yyyy-MM-dd"):null,this.spinnerService.show(),this.payrollService.sendPutRequest(e,t).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/payroll/element-value/list"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide()})}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(s.d),o.Ub(i.e),o.Ub(l.a),o.Ub(c.a),o.Ub(c.c),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll-element-value-edit"]],decls:94,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/element-value/list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","id","readonly","","disabled","",1,"form-control"],["type","text","formControlName","empTitle",1,"form-control"],["formControlName","elementTitle",1,"form-control"],["value",""],["value","HRA"],["value","MDL_ALW"],["value","OT_ALW"],["value","LTA"],["value","EA"],["value","UCA_ALW"],["value","FAMILY_ALW"],["value","EDA"],["value","PRJ_ALW"],["value","HOSTEL_ALW"],["value","CHILD_EDU_ALW"],["value","CHILD_HOSTEL_ALW"],["value","PROF_ALW"],["type","text","formControlName","elementAmount",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","activeStartDate","bsDatepicker","",1,"form-control","datetimepicker",3,"bsConfig"],["type","text","formControlName","activeEndDate","bsDatepicker","",1,"form-control",3,"bsConfig"],[1,"text-right"],["routerLink","/payroll/element-value/list",1,"btn","btn-warning","btn-ripple"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Payroll Element"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payroll Element"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Edit"),o.Zb(),o.Zb(),o.Zb(),o.ac(14,"div",9),o.ac(15,"a",10),o.Vb(16,"i",11),o.Lc(17," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(18,"div",12),o.ac(19,"div",13),o.ac(20,"div",14),o.ac(21,"div",15),o.ac(22,"form",16),o.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),o.ac(23,"div",17),o.ac(24,"label",18),o.Lc(25,"ID"),o.Zb(),o.ac(26,"div",19),o.Vb(27,"input",20),o.Zb(),o.Zb(),o.ac(28,"div",17),o.ac(29,"label",18),o.Lc(30,"Employee"),o.Zb(),o.ac(31,"div",19),o.Vb(32,"input",21),o.Zb(),o.Zb(),o.ac(33,"div",17),o.ac(34,"label",18),o.Lc(35,"Element Title"),o.Zb(),o.ac(36,"div",19),o.ac(37,"select",22),o.ac(38,"option",23),o.Lc(39,"Select"),o.Zb(),o.ac(40,"option",24),o.Lc(41,"House Rent Allowance"),o.Zb(),o.ac(42,"option",25),o.Lc(43,"Medical Allowance"),o.Zb(),o.ac(44,"option",26),o.Lc(45,"Overtime Allowance"),o.Zb(),o.ac(46,"option",27),o.Lc(47,"Leave Travel Allowance"),o.Zb(),o.ac(48,"option",28),o.Lc(49,"Entertainment Allowance"),o.Zb(),o.ac(50,"option",29),o.Lc(51,"Uniform Allowance / Corporate Attire"),o.Zb(),o.ac(52,"option",30),o.Lc(53,"Family Allowance"),o.Zb(),o.ac(54,"option",31),o.Lc(55,"Education Allowance"),o.Zb(),o.ac(56,"option",32),o.Lc(57,"Project Allowance"),o.Zb(),o.ac(58,"option",33),o.Lc(59,"Hostel Allowance"),o.Zb(),o.ac(60,"option",34),o.Lc(61,"Children's education Allowance"),o.Zb(),o.ac(62,"option",35),o.Lc(63,"Children's hostel Allowance"),o.Zb(),o.ac(64,"option",36),o.Lc(65,"Professional pursuit/research Allowance"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(66,"div",17),o.ac(67,"label",18),o.Lc(68,"Amount"),o.Zb(),o.ac(69,"div",19),o.Vb(70,"input",37),o.Zb(),o.Zb(),o.ac(71,"div",17),o.ac(72,"label",18),o.Lc(73,"Start Date"),o.Zb(),o.ac(74,"div",19),o.ac(75,"div",38),o.Vb(76,"input",39),o.Zb(),o.Zb(),o.Zb(),o.ac(77,"div",17),o.ac(78,"label",18),o.Lc(79,"End Date"),o.Zb(),o.ac(80,"div",19),o.ac(81,"div",38),o.Vb(82,"input",40),o.Zb(),o.Zb(),o.Zb(),o.ac(83,"div",41),o.ac(84,"a",42),o.Vb(85,"i",11),o.Lc(86," Cancel"),o.Zb(),o.Lc(87," \xa0 \xa0 \xa0 "),o.ac(88,"button",43),o.Vb(89,"i",44),o.Lc(90," Save \xa0\xa0\xa0 "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(91,"ngx-spinner",45),o.ac(92,"p",46),o.Lc(93," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(22),o.pc("formGroup",t.myForm),o.Ib(54),o.pc("bsConfig",o.sc(4,fe)),o.Ib(6),o.pc("bsConfig",o.sc(5,fe)),o.Ib(9),o.pc("fullScreen",!1))},directives:[c.e,s.x,s.p,s.h,s.b,s.o,s.f,s.v,s.s,s.y,p.b,p.a,b.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})(),ve=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.datePipe=t,this.payrollService=a,this.route=i,this.router=c,this.spinnerService=o,this.baseUrl=d.a.baseUrl,this.myFormData={},this.titleDecode={HRA:"House Rent Allowance",MDL_ALW:"Medical Allowance",OT_ALW:"Overtime Allowance",LTA:"Leave Travel Allowance",EA:"Entertainment Allowance",UCA_ALW:"Uniform Allowance / Corporate Attire",FAMILY_ALW:"Family Allowance",EDA:"Education Allowance",PRJ_ALW:"Project Allowance",HOSTEL_ALW:"Hostel Allowance",CHILD_EDU_ALW:"Children's education Allowance",CHILD_HOSTEL_ALW:"Children's hostel Allowance",PROF_ALW:"Professional pursuit/research Allowance"}}ngOnInit(){this.initializeForm(),this.setFormDefaultValues(),this.initButtonsRippleEffect(),this.getFormData()}ngOnDestroy(){}initializeForm(){this.myForm=this.formBuilder.group({empTitle:[""],elementTitle:[""],elementAmount:[""],activeStartDate:[""],activeEndDate:[""],isActive:[""]})}setFormDefaultValues(){}resetFormValues(){this.myForm.reset(),this.setFormDefaultValues()}initButtonsRippleEffect(){document.querySelectorAll(".btn-ripple").forEach(e=>{e.addEventListener("click",function(e){!function(e){const t=e.currentTarget;let a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left=`${a}px`,c.style.top=`${i}px`,t.appendChild(c),setTimeout(()=>{c.remove()},1e3)}(e)})})}getFormData(){let e=this.baseUrl+"/api/payrollElValue/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.payrollService.sendGetRequest(e,{rEntityName:"PayrollElementValue",rActiveOpetation:"read"}).subscribe(e=>{this.myFormData=e,this.spinnerService.hide(),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){let e=this.baseUrl+"/api/payrollElValue/create",t={};t=this.myForm.value,t.rActiveOperation="Create",t.activeStartDate=t.activeStartDate?this.datePipe.transform(t.activeStartDate,"yyyy-MM-dd").toString().slice(0,10):null,t.activeEndDate=t.activeEndDate?this.datePipe.transform(t.activeEndDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.payrollService.sendPostRequest(e,t).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/payroll/element-value/list"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide()})}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(s.d),o.Ub(i.e),o.Ub(l.a),o.Ub(c.a),o.Ub(c.c),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-payroll-element-value-show"]],decls:107,vars:12,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/element-value/list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["id","formERP","novalidate","",3,"formGroup","ngSubmit"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"row","fieldsetBorder","logBox"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/payroll/element-value/list",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Payroll Element"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payroll Element"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Show"),o.Zb(),o.Zb(),o.Zb(),o.ac(14,"div",9),o.ac(15,"a",10),o.Vb(16,"i",11),o.Lc(17," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(18,"div",12),o.ac(19,"div",13),o.ac(20,"div",14),o.ac(21,"div",15),o.ac(22,"form",16),o.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),o.ac(23,"fieldset",17),o.Vb(24,"legend"),o.ac(25,"div",18),o.ac(26,"div",19),o.ac(27,"label",20),o.Lc(28,"Employee"),o.Zb(),o.ac(29,"div",21),o.ac(30,"span"),o.Lc(31,": \xa0"),o.Zb(),o.ac(32,"span"),o.Lc(33),o.Zb(),o.Zb(),o.Zb(),o.ac(34,"div",19),o.ac(35,"label",20),o.Lc(36,"Element Title"),o.Zb(),o.ac(37,"div",21),o.ac(38,"span"),o.Lc(39,": \xa0"),o.Zb(),o.ac(40,"span"),o.Lc(41),o.Zb(),o.Zb(),o.Zb(),o.ac(42,"div",19),o.ac(43,"label",20),o.Lc(44,"Amount"),o.Zb(),o.ac(45,"div",21),o.ac(46,"span"),o.Lc(47,": \xa0"),o.Zb(),o.ac(48,"span"),o.Lc(49),o.Zb(),o.Zb(),o.Zb(),o.ac(50,"div",19),o.ac(51,"label",20),o.Lc(52,"Start Date"),o.Zb(),o.ac(53,"div",21),o.ac(54,"span"),o.Lc(55,": \xa0"),o.Zb(),o.ac(56,"span"),o.Lc(57),o.Zb(),o.Zb(),o.Zb(),o.ac(58,"div",19),o.ac(59,"label",20),o.Lc(60,"End Date"),o.Zb(),o.ac(61,"div",21),o.ac(62,"span"),o.Lc(63,": \xa0"),o.Zb(),o.ac(64,"span"),o.Lc(65),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(66,"fieldset",22),o.ac(67,"legend"),o.Lc(68,"System Log Information"),o.Zb(),o.ac(69,"div",23),o.ac(70,"label",24),o.Lc(71,"ID"),o.Zb(),o.ac(72,"div",25),o.ac(73,"span"),o.Lc(74),o.Zb(),o.Zb(),o.Zb(),o.ac(75,"div",23),o.ac(76,"label",24),o.Lc(77,"Creation Time"),o.Zb(),o.ac(78,"div",25),o.ac(79,"span"),o.Lc(80),o.Zb(),o.Zb(),o.Zb(),o.ac(81,"div",23),o.ac(82,"label",24),o.Lc(83,"Creation User"),o.Zb(),o.ac(84,"div",25),o.ac(85,"span"),o.Lc(86),o.Zb(),o.Zb(),o.Zb(),o.ac(87,"div",23),o.ac(88,"label",24),o.Lc(89,"Last Update Time"),o.Zb(),o.ac(90,"div",25),o.ac(91,"span"),o.Lc(92),o.Zb(),o.Zb(),o.Zb(),o.ac(93,"div",23),o.ac(94,"label",24),o.Lc(95,"Last Update User"),o.Zb(),o.ac(96,"div",25),o.ac(97,"span"),o.Lc(98),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(99,"div",26),o.ac(100,"a",27),o.Vb(101,"i",11),o.Lc(102," Close"),o.Zb(),o.Lc(103," \xa0 "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(104,"ngx-spinner",28),o.ac(105,"p",29),o.Lc(106," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(22),o.pc("formGroup",t.myForm),o.Ib(11),o.Mc(t.myFormData.empTitle),o.Ib(8),o.Mc(t.titleDecode[t.myFormData.elementTitle]),o.Ib(8),o.Mc(t.myFormData.elementAmount),o.Ib(8),o.Mc(t.myFormData.activeStartDate),o.Ib(8),o.Mc(t.myFormData.activeEndDate),o.Ib(9),o.Mc(t.myFormData.id),o.Ib(6),o.Mc(t.myFormData.creationDateTime),o.Ib(6),o.Mc(t.myFormData.creationUser),o.Ib(6),o.Mc(t.myFormData.lastUpdateDateTime),o.Ib(6),o.Mc(t.myFormData.lastUpdateUser),o.Ib(6),o.pc("fullScreen",!1))},directives:[c.e,s.x,s.p,s.h,b.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}#formERP[_ngcontent-%COMP%]   fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}#formERP[_ngcontent-%COMP%]   fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}#formERP[_ngcontent-%COMP%]   fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]{font-size:13px}"]}),e})();var ye=a("un/a"),Le=a("tk/3");let De=(()=>{class e{constructor(e){this.http=e}sendGetSelfRequest(e,t){return console.log("@sendGetSelfRequest"),this.http.get(e,{params:t}).pipe(Object(ye.a)(3))}sendPostRequest(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}sendPutRequest(e,t){return console.log("@sendPostRequest"),this.http.put(e,t)}sendGetRequest(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(ye.a)(3))}sendDeleteRequest(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}}return e.\u0275fac=function(t){return new(t||e)(o.ec(Le.c))},e.\u0275prov=o.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();function Pe(e,t){if(1&e){const e=o.bc();o.ac(0,"tr"),o.ac(1,"td"),o.Lc(2),o.Zb(),o.ac(3,"td"),o.Lc(4),o.Zb(),o.ac(5,"td"),o.Lc(6),o.kc(7,"date"),o.Zb(),o.ac(8,"td"),o.Lc(9),o.kc(10,"date"),o.Zb(),o.ac(11,"td"),o.Lc(12),o.kc(13,"date"),o.Zb(),o.ac(14,"td"),o.Lc(15),o.Zb(),o.ac(16,"td"),o.ac(17,"span",56),o.Lc(18),o.Zb(),o.Zb(),o.ac(19,"td"),o.ac(20,"a",57),o.Lc(21,"View"),o.Zb(),o.Lc(22," \xa0 "),o.ac(23,"a",58),o.Vb(24,"i",59),o.Zb(),o.Lc(25,"\xa0\xa0 "),o.ac(26,"a",60),o.hc("click",function(){o.Cc(e);const a=t.$implicit;return o.jc().tempId=a.id}),o.Vb(27,"i",61),o.Zb(),o.Zb(),o.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=o.jc();o.Mb("active",a==i.currentIndex),o.Ib(2),o.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),o.Ib(2),o.Mc(e.hrCrEmp.displayName),o.Ib(2),o.Mc(o.mc(7,11,e.createDate,"yyyy-MM-dd")),o.Ib(3),o.Mc(o.mc(10,14,e.startDate,"yyyy-MM-dd")),o.Ib(3),o.Mc(o.mc(13,17,e.endDate,"yyyy-MM-dd")),o.Ib(3),o.Mc(e.offDays),o.Ib(3),o.Mc(e.offDayBillApprovalStatus),o.Ib(2),o.rc("routerLink","./show/",e.id,""),o.Ib(3),o.rc("routerLink","./edit/",e.id,"")}}function Se(e,t){1&e&&(o.ac(0,"tr"),o.ac(1,"td",62),o.ac(2,"h5",63),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}function Ce(e,t){if(1&e&&(o.ac(0,"option",64),o.Lc(1),o.Zb()),2&e){const e=t.$implicit;o.pc("value",e),o.Ib(1),o.Nc(" ",e," ")}}const Fe=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let Ie=(()=>{class e{constructor(e,t,a){this.offDayBillService=e,this.spinnerService=t,this.toastr=a,this.baseUrl=d.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.getListData()}getListData(){let e=this.baseUrl+"/offDayBill/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.offDayBillService.sendGetSelfRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),console.log(this.listData),this.spinnerService.hide()},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/offDayBill/delete/"+e;console.log(t),this.spinnerService.show(),this.offDayBillService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getListData()},e=>{console.log(e),this.spinnerService.hide()})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(De),o.Ub(b.c),o.Ub(L.b))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-off-day-bills"]],decls:112,vars:14,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","./create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"badge","badge-success"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Off Day Bill"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payroll"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Off Day Bill"),o.Zb(),o.ac(14,"li",8),o.Lc(15,"List"),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",9),o.ac(17,"div",10),o.ac(18,"button",11),o.Lc(19,"Excel"),o.Zb(),o.ac(20,"button",11),o.Lc(21,"PDF"),o.Zb(),o.ac(22,"button",11),o.Vb(23,"i",12),o.Lc(24," Print"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(25,"div",13),o.ac(26,"div",14),o.ac(27,"div",15),o.ac(28,"div",16),o.ac(29,"div",17),o.Vb(30,"input",18),o.ac(31,"label",19),o.Lc(32,"Employee Code"),o.Zb(),o.Zb(),o.Zb(),o.ac(33,"div",20),o.ac(34,"div",17),o.ac(35,"div",21),o.Vb(36,"input",22),o.Zb(),o.ac(37,"label",19),o.Lc(38,"From"),o.Zb(),o.Zb(),o.Zb(),o.ac(39,"div",20),o.ac(40,"div",17),o.ac(41,"div",21),o.Vb(42,"input",22),o.Zb(),o.ac(43,"label",19),o.Lc(44,"To"),o.Zb(),o.Zb(),o.Zb(),o.ac(45,"div",20),o.ac(46,"a",23),o.Lc(47," Search "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(48,"div",24),o.ac(49,"div",25),o.ac(50,"div",26),o.ac(51,"div",27),o.ac(52,"div",28),o.ac(53,"a",29),o.Vb(54,"i",30),o.Lc(55," New \xa0\xa0\xa0"),o.Zb(),o.Zb(),o.Zb(),o.ac(56,"div",31),o.ac(57,"div",32),o.ac(58,"div",33),o.ac(59,"div",34),o.ac(60,"span",35),o.Lc(61),o.Zb(),o.Zb(),o.Zb(),o.ac(62,"table",36),o.ac(63,"thead"),o.ac(64,"tr"),o.ac(65,"th"),o.Lc(66,"SL"),o.Zb(),o.ac(67,"th"),o.Lc(68,"Employee"),o.Zb(),o.ac(69,"th"),o.Lc(70,"Apply Date"),o.Zb(),o.ac(71,"th"),o.Lc(72,"From Date"),o.Zb(),o.ac(73,"th"),o.Lc(74,"To Date"),o.Zb(),o.ac(75,"th"),o.Lc(76,"Total Days"),o.Zb(),o.ac(77,"th"),o.Lc(78,"Approval Status"),o.Zb(),o.ac(79,"th"),o.Lc(80,"Action"),o.Zb(),o.Zb(),o.Zb(),o.ac(81,"tbody"),o.Jc(82,Pe,28,20,"tr",37),o.kc(83,"paginate"),o.Jc(84,Se,4,0,"tr",38),o.Zb(),o.Zb(),o.ac(85,"div",39),o.ac(86,"div",40),o.Lc(87," Items per Page "),o.ac(88,"select",41),o.hc("change",function(e){return t.handlePageSizeChange(e)}),o.Jc(89,Ce,2,2,"option",42),o.Zb(),o.Zb(),o.ac(90,"div",43),o.ac(91,"pagination-controls",44),o.hc("pageChange",function(e){return t.handlePageChange(e)}),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(92,"ngx-spinner",45),o.ac(93,"p",46),o.Lc(94," Processing... "),o.Zb(),o.Zb(),o.ac(95,"div",47),o.ac(96,"div",48),o.ac(97,"div",49),o.ac(98,"div",50),o.ac(99,"div",51),o.ac(100,"h3"),o.Lc(101,"Delete Item"),o.Zb(),o.ac(102,"p"),o.Lc(103,"Are you sure want to delete?"),o.Zb(),o.Zb(),o.ac(104,"div",52),o.ac(105,"div",24),o.ac(106,"div",53),o.ac(107,"a",54),o.hc("click",function(){return t.deleteEnityData(t.tempId)}),o.Lc(108,"Delete"),o.Zb(),o.Zb(),o.ac(109,"div",53),o.ac(110,"a",55),o.Lc(111,"Cancel"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&e&&(o.Ib(36),o.pc("bsConfig",o.sc(12,Fe)),o.Ib(6),o.pc("bsConfig",o.sc(13,Fe)),o.Ib(19),o.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),o.Ib(21),o.pc("ngForOf",o.mc(83,9,t.listData,t.configPgn)),o.Ib(2),o.pc("ngIf",0===t.listData.length),o.Ib(5),o.pc("ngForOf",t.configPgn.pageSizes),o.Ib(3),o.pc("fullScreen",!1))},directives:[c.e,p.b,p.a,i.l,i.m,m.c,b.a,s.s,s.y],pipes:[m.b,i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();var Oe=a("xrk7"),Me=a("ZOsW");const xe=[{path:"",component:r,children:[{path:"employee-salary",component:Z},{path:"payslip/:id",component:v},{path:"payroll-items",component:ie},{path:"salary-process",component:ce},{path:"salary-process-list",component:se},{path:"element-value/list",component:ge},{path:"element-value/create",component:he},{path:"element-value/edit/:id",component:Ze},{path:"element-value/show/:id",component:ve},{path:"off-day-bill",component:Ie},{path:"off-day-bill/create",component:(()=>{class e{constructor(e,t,a,i,c,o,n){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.offDayBillService=c,this.toastr=o,this.commonService=n,this.baseUrl=d.a.baseUrl,this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm()}initializeForm(){this.myForm=this.formBuilder.group({hrCrEmp:[{},s.w.required],contactNo:[""],hrCrEmpResponsible:[{},s.w.required],startDate:[""],endDate:[""],remarks:[""]})}resetFormValues(){this.myForm.reset()}myFormSubmit(){if((this.myForm.value.endDate.getTime()-this.myForm.value.startDate.getTime())/864e5+1<1)return void this.toastr.error("End Date must be equal or greater");if(this.myForm.invalid)return;let e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),t=this.baseUrl+"/offDayBill/save",a={};a=e,a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.offDayBillService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.router.navigate(["/payroll/off-day-bill"],{relativeTo:this.route})},e=>{console.log(e)})}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.listData=this.configDDL.append?this.configDDL.listData.concat(e.objectList):e.objectList,this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(s.d),o.Ub(i.e),o.Ub(c.a),o.Ub(c.c),o.Ub(De),o.Ub(L.b),o.Ub(Oe.a))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-off-day-bill-create"]],decls:69,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/off-day-bill",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/payroll/off-day-bill",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Off Day Bill"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Off Day Bill"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Payroll"),o.Zb(),o.ac(14,"li",8),o.Lc(15,"Create"),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",9),o.ac(17,"a",10),o.Vb(18,"i",11),o.Lc(19," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(20,"div",12),o.ac(21,"div",13),o.ac(22,"div",14),o.ac(23,"div",15),o.ac(24,"form",16),o.hc("ngSubmit",function(){return t.myFormSubmit()}),o.ac(25,"div",17),o.ac(26,"label",18),o.Lc(27,"Employee "),o.Zb(),o.ac(28,"div",19),o.ac(29,"ng-select",20),o.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",17),o.ac(31,"label",18),o.Lc(32,"Contact No"),o.Zb(),o.ac(33,"div",19),o.Vb(34,"input",21),o.Zb(),o.Zb(),o.ac(35,"div",17),o.ac(36,"label",18),o.Lc(37,"Responsible Employee "),o.Zb(),o.ac(38,"div",19),o.ac(39,"ng-select",22),o.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),o.Zb(),o.Zb(),o.Zb(),o.ac(40,"div",17),o.ac(41,"label",18),o.Lc(42,"Start Date"),o.Zb(),o.ac(43,"div",19),o.ac(44,"div",23),o.Vb(45,"input",24),o.Zb(),o.Zb(),o.Zb(),o.ac(46,"div",17),o.ac(47,"label",18),o.Lc(48,"End Date"),o.Zb(),o.ac(49,"div",19),o.ac(50,"div",23),o.Vb(51,"input",25),o.Zb(),o.Zb(),o.Zb(),o.ac(52,"div",17),o.ac(53,"label",18),o.Lc(54,"Remarks"),o.Zb(),o.ac(55,"div",19),o.Vb(56,"textarea",26),o.Zb(),o.Zb(),o.ac(57,"div",27),o.ac(58,"a",28),o.Vb(59,"i",11),o.Lc(60," Cancel"),o.Zb(),o.Lc(61," \xa0 \xa0 "),o.ac(62,"button",29),o.hc("click",function(){return t.resetFormValues()}),o.Vb(63,"i",30),o.Lc(64," Reset "),o.Zb(),o.Lc(65," \xa0 \xa0 "),o.ac(66,"button",31),o.Vb(67,"i",32),o.Lc(68," Save \xa0\xa0\xa0 "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&e&&(o.Ib(24),o.pc("formGroup",t.myForm),o.Ib(5),o.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),o.Ib(10),o.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[c.e,s.x,s.p,s.h,Me.a,s.o,s.f,s.t,s.b,p.b,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})()},{path:"off-day-bill/show/:id",component:(()=>{class e{constructor(e,t,a){this.route=e,this.spinnerService=t,this.offDayBillService=a,this.baseUrl=d.a.baseUrl,this.myData={}}ngOnInit(){this.getFormData()}getFormData(){let e=this.baseUrl+"/offDayBill/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.offDayBillService.sendGetRequest(e,{}).subscribe(e=>{this.myData=e,console.log(this.myData),this.spinnerService.hide()},e=>{console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(c.a),o.Ub(b.c),o.Ub(De))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-off-day-bill-show"]],decls:142,vars:23,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/off-day-bill",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"row","fieldsetBorder","logBox"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/payroll/off-day-bill",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Off Day Bill"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Payroll"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Off Day Bill"),o.Zb(),o.ac(14,"li",8),o.Lc(15,"Show"),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",9),o.ac(17,"a",10),o.Vb(18,"i",11),o.Lc(19," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(20,"div",12),o.ac(21,"div",13),o.ac(22,"div",14),o.ac(23,"div",15),o.ac(24,"fieldset",16),o.Vb(25,"legend"),o.ac(26,"div",17),o.ac(27,"div",18),o.ac(28,"label",19),o.Lc(29,"Employee"),o.Zb(),o.ac(30,"div",20),o.ac(31,"span"),o.Lc(32,": \xa0"),o.Zb(),o.ac(33,"span"),o.Lc(34),o.Zb(),o.Zb(),o.Zb(),o.ac(35,"div",18),o.ac(36,"label",19),o.Lc(37,"Responsible Employee"),o.Zb(),o.ac(38,"div",20),o.ac(39,"span"),o.Lc(40,": \xa0"),o.Zb(),o.ac(41,"span"),o.Lc(42),o.Zb(),o.Zb(),o.Zb(),o.ac(43,"div",18),o.ac(44,"label",19),o.Lc(45,"Contact Number(Given)"),o.Zb(),o.ac(46,"div",20),o.ac(47,"span"),o.Lc(48,": \xa0"),o.Zb(),o.ac(49,"span"),o.Lc(50),o.Zb(),o.Zb(),o.Zb(),o.ac(51,"div",18),o.ac(52,"label",19),o.Lc(53,"Contact Number"),o.Zb(),o.ac(54,"div",20),o.ac(55,"span"),o.Lc(56,": \xa0"),o.Zb(),o.ac(57,"span"),o.Lc(58),o.Zb(),o.Zb(),o.Zb(),o.ac(59,"div",18),o.ac(60,"label",19),o.Lc(61,"Start Date"),o.Zb(),o.ac(62,"div",20),o.ac(63,"span"),o.Lc(64,": \xa0"),o.Zb(),o.ac(65,"span"),o.Lc(66),o.kc(67,"date"),o.Zb(),o.Zb(),o.Zb(),o.ac(68,"div",18),o.ac(69,"label",19),o.Lc(70,"End Date"),o.Zb(),o.ac(71,"div",20),o.ac(72,"span"),o.Lc(73,": \xa0"),o.Zb(),o.ac(74,"span"),o.Lc(75),o.kc(76,"date"),o.Zb(),o.Zb(),o.Zb(),o.ac(77,"div",18),o.ac(78,"label",19),o.Lc(79,"Total Days"),o.Zb(),o.ac(80,"div",20),o.ac(81,"span"),o.Lc(82,": \xa0"),o.Zb(),o.ac(83,"span"),o.Lc(84),o.Zb(),o.Zb(),o.Zb(),o.ac(85,"div",18),o.ac(86,"label",19),o.Lc(87,"Remarks"),o.Zb(),o.ac(88,"div",20),o.ac(89,"span"),o.Lc(90,": \xa0"),o.Zb(),o.ac(91,"span"),o.Lc(92),o.Zb(),o.Zb(),o.Zb(),o.ac(93,"div",18),o.ac(94,"label",19),o.Lc(95,"Approval Status"),o.Zb(),o.ac(96,"div",20),o.ac(97,"span"),o.Lc(98,": \xa0"),o.Zb(),o.ac(99,"span"),o.Lc(100),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(101,"fieldset",21),o.ac(102,"legend"),o.Lc(103,"System Log Information"),o.Zb(),o.ac(104,"div",22),o.ac(105,"label",23),o.Lc(106,"ID"),o.Zb(),o.ac(107,"div",24),o.ac(108,"span"),o.Lc(109),o.Zb(),o.Zb(),o.Zb(),o.ac(110,"div",22),o.ac(111,"label",23),o.Lc(112,"Creation Time"),o.Zb(),o.ac(113,"div",24),o.ac(114,"span"),o.Lc(115),o.Zb(),o.Zb(),o.Zb(),o.ac(116,"div",22),o.ac(117,"label",23),o.Lc(118,"Creation User"),o.Zb(),o.ac(119,"div",24),o.ac(120,"span"),o.Lc(121),o.Zb(),o.Zb(),o.Zb(),o.ac(122,"div",22),o.ac(123,"label",23),o.Lc(124,"Last Update Time"),o.Zb(),o.ac(125,"div",24),o.ac(126,"span"),o.Lc(127),o.Zb(),o.Zb(),o.Zb(),o.ac(128,"div",22),o.ac(129,"label",23),o.Lc(130,"Last Update User"),o.Zb(),o.ac(131,"div",24),o.ac(132,"span"),o.Lc(133),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(134,"div",25),o.ac(135,"a",26),o.Vb(136,"i",11),o.Lc(137," Close"),o.Zb(),o.Lc(138," \xa0 "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(139,"ngx-spinner",27),o.ac(140,"p",28),o.Lc(141," Processing... "),o.Zb(),o.Zb()),2&e&&(o.Ib(34),o.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),o.Ib(8),o.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),o.Ib(8),o.Mc(t.myData.contactNo),o.Ib(8),o.Mc(t.myData.hrCrEmp.mobCode),o.Ib(8),o.Mc(o.mc(67,17,t.myData.startDate,"yyyy-MM-dd")),o.Ib(9),o.Mc(o.mc(76,20,t.myData.endDate,"yyyy-MM-dd")),o.Ib(9),o.Mc(t.myData.offDays),o.Ib(8),o.Mc(t.myData.remarks),o.Ib(8),o.Mc(t.myData.offDayBillApprovalStatus),o.Ib(9),o.Mc(t.myData.id),o.Ib(6),o.Mc(t.myData.createDate),o.Ib(6),o.Mc(t.myData.createdByHrCrEmp.user.username),o.Ib(6),o.Mc(t.myData.updateDateTime),o.Ib(6),o.Mc(t.myData.updatedByHrCrEmp.user.username),o.Ib(6),o.pc("fullScreen",!1))},directives:[c.e,b.a],pipes:[i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),e})()},{path:"off-day-bill/edit/:id",component:(()=>{class e{constructor(e,t,a,i,c,o,n,r){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.offDayBillService=c,this.toastr=o,this.commonService=n,this.spinnerService=r,this.baseUrl=d.a.baseUrl,this.myFormData={},this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",s.w.required],contactNo:[""],hrCrEmpResponsible:["",s.w.required],startDate:[""],endDate:[""],remarks:[""]})}getFormData(){let e=this.baseUrl+"/offDayBill/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.offDayBillService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide(),this.configDDL.listData=[{ddlCode:e.hrCrEmp.id,ddlDescription:e.hrCrEmp.loginCode+"-"+e.hrCrEmp.displayName}],this.myFormData.hrCrEmp=e.hrCrEmp.id,this.configDDL.listData2=[{ddlCode:e.hrCrEmpResponsible.id,ddlDescription:e.hrCrEmpResponsible.loginCode+"-"+e.hrCrEmpResponsible.displayName}],this.myFormData.hrCrEmpResponsible=e.hrCrEmpResponsible.id,this.myFormData.startDate=this.datePipe.transform(e.startDate,"MM-dd-yyyy").toString().slice(0,10),this.myFormData.endDate=this.datePipe.transform(e.endDate,"MM-dd-yyyy").toString().slice(0,10),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){if(this.myForm.invalid)return;let e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),t=this.baseUrl+"/offDayBill/edit",a={};a=e,a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.offDayBillService.sendPutRequest(t,a).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/payroll/off-day-bill"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide(),this.toastr.error(e.error.message)})}resetFormValues(){this.getFormData()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?(this.configDDL.listData=this.configDDL.listData.concat(e.objectList),this.configDDL.listData2=this.configDDL.listData2.concat(e.objectList)):(this.configDDL.listData=e.objectList,this.configDDL.listData2=e.objectList),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}}return e.\u0275fac=function(t){return new(t||e)(o.Ub(s.d),o.Ub(i.e),o.Ub(c.a),o.Ub(c.c),o.Ub(De),o.Ub(L.b),o.Ub(Oe.a),o.Ub(b.c))},e.\u0275cmp=o.Ob({type:e,selectors:[["app-off-day-bill-edit"]],decls:69,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/off-day-bill",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/payroll/off-day-bill",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Off Day Bill"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Home"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Off Day Bill"),o.Zb(),o.ac(12,"li",8),o.Lc(13,"Payroll"),o.Zb(),o.ac(14,"li",8),o.Lc(15,"edit"),o.Zb(),o.Zb(),o.Zb(),o.ac(16,"div",9),o.ac(17,"a",10),o.Vb(18,"i",11),o.Lc(19," Back To List"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(20,"div",12),o.ac(21,"div",13),o.ac(22,"div",14),o.ac(23,"div",15),o.ac(24,"form",16),o.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),o.ac(25,"div",17),o.ac(26,"label",18),o.Lc(27,"Employee "),o.Zb(),o.ac(28,"div",19),o.ac(29,"ng-select",20),o.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",17),o.ac(31,"label",18),o.Lc(32,"Contact No"),o.Zb(),o.ac(33,"div",19),o.Vb(34,"input",21),o.Zb(),o.Zb(),o.ac(35,"div",17),o.ac(36,"label",18),o.Lc(37,"Responsible Employee "),o.Zb(),o.ac(38,"div",19),o.ac(39,"ng-select",22),o.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),o.Zb(),o.Zb(),o.Zb(),o.ac(40,"div",17),o.ac(41,"label",18),o.Lc(42,"Start Date"),o.Zb(),o.ac(43,"div",19),o.ac(44,"div",23),o.Vb(45,"input",24),o.Zb(),o.Zb(),o.Zb(),o.ac(46,"div",17),o.ac(47,"label",18),o.Lc(48,"End Date"),o.Zb(),o.ac(49,"div",19),o.ac(50,"div",23),o.Vb(51,"input",25),o.Zb(),o.Zb(),o.Zb(),o.ac(52,"div",17),o.ac(53,"label",18),o.Lc(54,"Remarks"),o.Zb(),o.ac(55,"div",19),o.Vb(56,"textarea",26),o.Zb(),o.Zb(),o.ac(57,"div",27),o.ac(58,"a",28),o.Vb(59,"i",11),o.Lc(60," Cancel"),o.Zb(),o.Lc(61," \xa0 \xa0 "),o.ac(62,"button",29),o.hc("click",function(){return t.resetFormValues()}),o.Vb(63,"i",30),o.Lc(64," Reset "),o.Zb(),o.Lc(65," \xa0 \xa0 "),o.ac(66,"button",31),o.Vb(67,"i",32),o.Lc(68," Save \xa0\xa0\xa0 "),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&e&&(o.Ib(24),o.pc("formGroup",t.myForm),o.Ib(5),o.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),o.Ib(10),o.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[c.e,s.x,s.p,s.h,Me.a,s.o,s.f,s.t,s.b,p.b,p.a],styles:[""]}),e})()}]}];let _e=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=o.Sb({type:e}),e.\u0275inj=o.Rb({imports:[[c.f.forChild(xe)],c.f]}),e})();var Ne=a("njyG"),we=a("0jEk"),Ee=a("iHf9");let ke=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=o.Sb({type:e}),e.\u0275inj=o.Rb({imports:[[i.c,_e,Ne.b,p.c.forRoot(),we.a,s.u,Ee.b,m.a,b.b,Me.b]]}),e})()},bNXq:function(e,t,a){"use strict";a.d(t,"a",function(){return r});var i=a("un/a"),c=a("rmPI"),o=a("fXoL"),n=a("tk/3");let r=(()=>{class e{constructor(e){this.http=e}sendGetRequest(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(i.a)(3))}sendPostRequest(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}sendPutRequest(e,t){return console.log("@sendPutRequest"),this.http.put(e,t)}sendDeleteRequest(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}getPayrollElementAssignmentByEmpId(e){return this.http.get(`${c.a}/empPayrollAssignment/get/${e}`)}}return e.\u0275fac=function(t){return new(t||e)(o.ec(n.c))},e.\u0275prov=o.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},rmPI:function(e,t,a){"use strict";t.a="http://localhost:9090/hrms_api"}}]);