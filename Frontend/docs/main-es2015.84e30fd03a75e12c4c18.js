(window.webpackJsonp=window.webpackJsonp||[]).push([[9],{0:function(t,e,n){t.exports=n("zUnb")},"2QA8":function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=(()=>"function"==typeof Symbol?Symbol("rxSubscriber"):"@@rxSubscriber_"+Math.random())()},"2Vo4":function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("XNiG"),s=n("9ppp");class i extends r.a{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){const e=super._subscribe(t);return e&&!e.closed&&t.next(this._value),e}getValue(){if(this.hasError)throw this.thrownError;if(this.closed)throw new s.a;return this._value}next(t){super.next(this._value=t)}}},"2fFW":function(t,e,n){"use strict";n.d(e,"a",function(){return s});let r=!1;const s={Promise:void 0,set useDeprecatedSynchronousErrorHandling(t){if(t){const t=new Error;console.warn("DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \n"+t.stack)}else r&&console.log("RxJS: Back to a better error behavior. Thank you. <3");r=t},get useDeprecatedSynchronousErrorHandling(){return r}}},"3owW":function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("d//k"),s=n("fXoL"),i=n("tyNb");let o=(()=>{class t{constructor(t,e){this.login=t,this.router=e}canActivate(t,e){let n=this.login.getLoginUserRole();return this.login.isLoggedIn()&&null!=n&&(n.includes("ROLE_USER")||n.includes("ROLE_ADMIN")||n.includes("ROLE_SUPER_ADMIN"))?(console.log("UURRLL"+this.router.url),!0):(this.router.navigate(["login"]),!1)}}return t.\u0275fac=function(e){return new(e||t)(s.ec(r.a),s.ec(i.c))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},"4I5i":function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=(()=>{function t(){return Error.call(this),this.message="argument out of range",this.name="ArgumentOutOfRangeError",this}return t.prototype=Object.create(Error.prototype),t})()},"5+tZ":function(t,e,n){"use strict";n.d(e,"a",function(){return c});var r=n("ZUHj"),s=n("l7GE"),i=n("51Dv"),o=n("lJxs"),a=n("Cfvw");function c(t,e,n=Number.POSITIVE_INFINITY){return"function"==typeof e?r=>r.pipe(c((n,r)=>Object(a.a)(t(n,r)).pipe(Object(o.a)((t,s)=>e(n,t,r,s))),n)):("number"==typeof e&&(n=e),e=>e.lift(new l(t,n)))}class l{constructor(t,e=Number.POSITIVE_INFINITY){this.project=t,this.concurrent=e}call(t,e){return e.subscribe(new u(t,this.project,this.concurrent))}}class u extends s.a{constructor(t,e,n=Number.POSITIVE_INFINITY){super(t),this.project=e,this.concurrent=n,this.hasCompleted=!1,this.buffer=[],this.active=0,this.index=0}_next(t){this.active<this.concurrent?this._tryNext(t):this.buffer.push(t)}_tryNext(t){let e;const n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this.active++,this._innerSub(e,t,n)}_innerSub(t,e,n){const s=new i.a(this,e,n),o=this.destination;o.add(s);const a=Object(r.a)(this,t,void 0,void 0,s);a!==s&&o.add(a)}_complete(){this.hasCompleted=!0,0===this.active&&0===this.buffer.length&&this.destination.complete(),this.unsubscribe()}notifyNext(t,e,n,r,s){this.destination.next(e)}notifyComplete(t){const e=this.buffer;this.remove(t),this.active--,e.length>0?this._next(e.shift()):0===this.active&&this.hasCompleted&&this.destination.complete()}}},"51Dv":function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");class s extends r.a{constructor(t,e,n){super(),this.parent=t,this.outerValue=e,this.outerIndex=n,this.index=0}_next(t){this.parent.notifyNext(this.outerValue,t,this.outerIndex,this.index++,this)}_error(t){this.parent.notifyError(t,this),this.unsubscribe()}_complete(){this.parent.notifyComplete(this),this.unsubscribe()}}},"5eHb":function(t,e,n){"use strict";function r(t,e,n,r){var s,i=arguments.length,o=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(o=(i<3?s(o):i>3?s(e,n,o):s(e,n))||o);return i>3&&o&&Object.defineProperty(e,n,o),o}function s(t,e){return function(n,r){e(n,r,t)}}function i(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}n.d(e,"a",function(){return M}),n.d(e,"b",function(){return D});var o=n("fXoL"),a=n("R0Ic"),c=n("XNiG"),l=n("jhN1"),u=n("ofXK");const h=["toast-component",""];function d(t,e){if(1&t){const t=o.bc();o.ac(0,"button",5),o.hc("click",function(){return o.Cc(t),o.jc().remove()}),o.ac(1,"span",6),o.Lc(2,"\xd7"),o.Zb(),o.Zb()}}function f(t,e){if(1&t&&(o.Yb(0),o.Lc(1),o.Xb()),2&t){const t=o.jc(2);o.Ib(1),o.Nc("[",t.duplicatesCount+1,"]")}}function p(t,e){if(1&t&&(o.ac(0,"div"),o.Lc(1),o.Jc(2,f,2,1,"ng-container",4),o.Zb()),2&t){const t=o.jc();o.Kb(t.options.titleClass),o.Jb("aria-label",t.title),o.Ib(1),o.Nc(" ",t.title," "),o.Ib(1),o.pc("ngIf",t.duplicatesCount)}}function m(t,e){if(1&t&&o.Vb(0,"div",7),2&t){const t=o.jc();o.Kb(t.options.messageClass),o.pc("innerHTML",t.message,o.Dc)}}function g(t,e){if(1&t&&(o.ac(0,"div",8),o.Lc(1),o.Zb()),2&t){const t=o.jc();o.Kb(t.options.messageClass),o.Jb("aria-label",t.message),o.Ib(1),o.Nc(" ",t.message," ")}}function y(t,e){if(1&t&&(o.ac(0,"div"),o.Vb(1,"div",9),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.Hc("width",t.width+"%")}}function b(t,e){if(1&t){const t=o.bc();o.ac(0,"button",5),o.hc("click",function(){return o.Cc(t),o.jc().remove()}),o.ac(1,"span",6),o.Lc(2,"\xd7"),o.Zb(),o.Zb()}}function _(t,e){if(1&t&&(o.Yb(0),o.Lc(1),o.Xb()),2&t){const t=o.jc(2);o.Ib(1),o.Nc("[",t.duplicatesCount+1,"]")}}function v(t,e){if(1&t&&(o.ac(0,"div"),o.Lc(1),o.Jc(2,_,2,1,"ng-container",4),o.Zb()),2&t){const t=o.jc();o.Kb(t.options.titleClass),o.Jb("aria-label",t.title),o.Ib(1),o.Nc(" ",t.title," "),o.Ib(1),o.pc("ngIf",t.duplicatesCount)}}function w(t,e){if(1&t&&o.Vb(0,"div",7),2&t){const t=o.jc();o.Kb(t.options.messageClass),o.pc("innerHTML",t.message,o.Dc)}}function S(t,e){if(1&t&&(o.ac(0,"div",8),o.Lc(1),o.Zb()),2&t){const t=o.jc();o.Kb(t.options.messageClass),o.Jb("aria-label",t.message),o.Ib(1),o.Nc(" ",t.message," ")}}function C(t,e){if(1&t&&(o.ac(0,"div"),o.Vb(1,"div",9),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.Hc("width",t.width+"%")}}class E{constructor(t,e,n,r,s,i){this.toastId=t,this.config=e,this.message=n,this.title=r,this.toastType=s,this.toastRef=i,this._onTap=new c.a,this._onAction=new c.a,this.toastRef.afterClosed().subscribe(()=>{this._onAction.complete(),this._onTap.complete()})}triggerTap(){this._onTap.next(),this.config.tapToDismiss&&this._onTap.complete()}onTap(){return this._onTap.asObservable()}triggerAction(t){this._onAction.next(t)}onAction(){return this._onAction.asObservable()}}const O={maxOpened:0,autoDismiss:!1,newestOnTop:!0,preventDuplicates:!1,countDuplicates:!1,resetTimeoutOnDuplicate:!1,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},closeButton:!1,disableTimeOut:!1,timeOut:5e3,extendedTimeOut:1e3,enableHtml:!1,progressBar:!1,toastClass:"ngx-toastr",positionClass:"toast-top-right",titleClass:"toast-title",messageClass:"toast-message",easing:"ease-in",easeTime:300,tapToDismiss:!0,onActivateTick:!1,progressAnimation:"decreasing"},T=new o.v("ToastConfig");class x{constructor(t,e){this.component=t,this.injector=e}attach(t,e){return this._attachedHost=t,t.attach(this,e)}detach(){const t=this._attachedHost;if(t)return this._attachedHost=void 0,t.detach()}get isAttached(){return null!=this._attachedHost}setAttachedHost(t){this._attachedHost=t}}class k extends class{attach(t,e){return this._attachedPortal=t,this.attachComponentPortal(t,e)}detach(){this._attachedPortal&&this._attachedPortal.setAttachedHost(),this._attachedPortal=void 0,this._disposeFn&&(this._disposeFn(),this._disposeFn=void 0)}setDisposeFn(t){this._disposeFn=t}}{constructor(t,e,n){super(),this._hostDomElement=t,this._componentFactoryResolver=e,this._appRef=n}attachComponentPortal(t,e){const n=this._componentFactoryResolver.resolveComponentFactory(t.component);let r;return r=n.create(t.injector),this._appRef.attachView(r.hostView),this.setDisposeFn(()=>{this._appRef.detachView(r.hostView),r.destroy()}),e?this._hostDomElement.insertBefore(this._getComponentRootNode(r),this._hostDomElement.firstChild):this._hostDomElement.appendChild(this._getComponentRootNode(r)),r}_getComponentRootNode(t){return t.hostView.rootNodes[0]}}let j=(()=>{let t=class{constructor(t){this._document=t}ngOnDestroy(){this._containerElement&&this._containerElement.parentNode&&this._containerElement.parentNode.removeChild(this._containerElement)}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){const t=this._document.createElement("div");t.classList.add("overlay-container"),this._document.body.appendChild(t),this._containerElement=t}};return t.\u0275fac=function(e){return new(e||t)(o.ec(u.d))},t.\u0275prov=o.Qb({token:t,factory:function(e){return t.\u0275fac(e)},providedIn:"root"}),t.ngInjectableDef=Object(o.Qb)({factory:function(){return new t(Object(o.ec)(u.d))},token:t,providedIn:"root"}),t=r([s(0,Object(o.s)(u.d)),i("design:paramtypes",[Object])],t),t})();class I{constructor(t){this._portalHost=t}attach(t,e=!0){return this._portalHost.attach(t,e)}detach(){return this._portalHost.detach()}}let A=(()=>{let t=class{constructor(t,e,n,r){this._overlayContainer=t,this._componentFactoryResolver=e,this._appRef=n,this._document=r,this._paneElements=new Map}create(t,e){return this._createOverlayRef(this.getPaneElement(t,e))}getPaneElement(t="",e){return this._paneElements.get(e)||this._paneElements.set(e,{}),this._paneElements.get(e)[t]||(this._paneElements.get(e)[t]=this._createPaneElement(t,e)),this._paneElements.get(e)[t]}_createPaneElement(t,e){const n=this._document.createElement("div");return n.id="toast-container",n.classList.add(t),n.classList.add("toast-container"),e?e.getContainerElement().appendChild(n):this._overlayContainer.getContainerElement().appendChild(n),n}_createPortalHost(t){return new k(t,this._componentFactoryResolver,this._appRef)}_createOverlayRef(t){return new I(this._createPortalHost(t))}};return t.\u0275fac=function(e){return new(e||t)(o.ec(j),o.ec(o.l),o.ec(o.g),o.ec(u.d))},t.\u0275prov=o.Qb({token:t,factory:function(e){return t.\u0275fac(e)},providedIn:"root"}),t.ngInjectableDef=Object(o.Qb)({factory:function(){return new t(Object(o.ec)(j),Object(o.ec)(o.l),Object(o.ec)(o.g),Object(o.ec)(u.d))},token:t,providedIn:"root"}),t=r([s(3,Object(o.s)(u.d)),i("design:paramtypes",[j,o.l,o.g,Object])],t),t})();class P{constructor(t){this._overlayRef=t,this.duplicatesCount=0,this._afterClosed=new c.a,this._activate=new c.a,this._manualClose=new c.a,this._resetTimeout=new c.a,this._countDuplicate=new c.a}manualClose(){this._manualClose.next(),this._manualClose.complete()}manualClosed(){return this._manualClose.asObservable()}timeoutReset(){return this._resetTimeout.asObservable()}countDuplicate(){return this._countDuplicate.asObservable()}close(){this._overlayRef.detach(),this._afterClosed.next(),this._manualClose.next(),this._afterClosed.complete(),this._manualClose.complete(),this._activate.complete(),this._resetTimeout.complete(),this._countDuplicate.complete()}afterClosed(){return this._afterClosed.asObservable()}isInactive(){return this._activate.isStopped}activate(){this._activate.next(),this._activate.complete()}afterActivate(){return this._activate.asObservable()}onDuplicate(t,e){t&&this._resetTimeout.next(),e&&this._countDuplicate.next(++this.duplicatesCount)}}class R{constructor(t,e){this._toastPackage=t,this._parentInjector=e}get(t,e,n){return t===E?this._toastPackage:this._parentInjector.get(t,e,n)}}let D=(()=>{let t=class{constructor(t,e,n,r,s){this.overlay=e,this._injector=n,this.sanitizer=r,this.ngZone=s,this.currentlyActive=0,this.toasts=[],this.index=0,this.toastrConfig=Object.assign({},t.default,t.config),t.config.iconClasses&&(this.toastrConfig.iconClasses=Object.assign({},t.default.iconClasses,t.config.iconClasses))}show(t,e,n={},r=""){return this._preBuildNotification(r,t,e,this.applyConfig(n))}success(t,e,n={}){return this._preBuildNotification(this.toastrConfig.iconClasses.success||"",t,e,this.applyConfig(n))}error(t,e,n={}){return this._preBuildNotification(this.toastrConfig.iconClasses.error||"",t,e,this.applyConfig(n))}info(t,e,n={}){return this._preBuildNotification(this.toastrConfig.iconClasses.info||"",t,e,this.applyConfig(n))}warning(t,e,n={}){return this._preBuildNotification(this.toastrConfig.iconClasses.warning||"",t,e,this.applyConfig(n))}clear(t){for(const e of this.toasts)if(void 0!==t){if(e.toastId===t)return void e.toastRef.manualClose()}else e.toastRef.manualClose()}remove(t){const e=this._findToast(t);if(!e)return!1;if(e.activeToast.toastRef.close(),this.toasts.splice(e.index,1),this.currentlyActive=this.currentlyActive-1,!this.toastrConfig.maxOpened||!this.toasts.length)return!1;if(this.currentlyActive<this.toastrConfig.maxOpened&&this.toasts[this.currentlyActive]){const t=this.toasts[this.currentlyActive].toastRef;t.isInactive()||(this.currentlyActive=this.currentlyActive+1,t.activate())}return!0}findDuplicate(t,e,n){for(const r of this.toasts)if(r.message===t)return r.toastRef.onDuplicate(e,n),r;return null}applyConfig(t={}){return Object.assign({},this.toastrConfig,t)}_findToast(t){for(let e=0;e<this.toasts.length;e++)if(this.toasts[e].toastId===t)return{index:e,activeToast:this.toasts[e]};return null}_preBuildNotification(t,e,n,r){return r.onActivateTick?this.ngZone.run(()=>this._buildNotification(t,e,n,r)):this._buildNotification(t,e,n,r)}_buildNotification(t,e,n,r){if(!r.toastComponent)throw new Error("toastComponent required");const s=this.findDuplicate(e,this.toastrConfig.resetTimeoutOnDuplicate&&r.timeOut>0,this.toastrConfig.countDuplicates);if(e&&this.toastrConfig.preventDuplicates&&null!==s)return s;this.previousToastMessage=e;let i=!1;this.toastrConfig.maxOpened&&this.currentlyActive>=this.toastrConfig.maxOpened&&(i=!0,this.toastrConfig.autoDismiss&&this.clear(this.toasts[0].toastId));const a=this.overlay.create(r.positionClass,this.overlayContainer);this.index=this.index+1;let c=e;e&&r.enableHtml&&(c=this.sanitizer.sanitize(o.Q.HTML,e));const l=new P(a),u=new E(this.index,r,c,n,t,l),h=new R(u,this._injector),d=new x(r.toastComponent,h),f=a.attach(d,this.toastrConfig.newestOnTop);l.componentInstance=f._component;const p={toastId:this.index,message:e||"",toastRef:l,onShown:l.afterActivate(),onHidden:l.afterClosed(),onTap:u.onTap(),onAction:u.onAction(),portal:f};return i||(this.currentlyActive=this.currentlyActive+1,setTimeout(()=>{p.toastRef.activate()})),this.toasts.push(p),p}};return t.\u0275fac=function(e){return new(e||t)(o.ec(T),o.ec(A),o.ec(o.w),o.ec(l.b),o.ec(o.G))},t.\u0275prov=o.Qb({token:t,factory:function(e){return t.\u0275fac(e)},providedIn:"root"}),t.ngInjectableDef=Object(o.Qb)({factory:function(){return new t(Object(o.ec)(T),Object(o.ec)(A),Object(o.ec)(o.r),Object(o.ec)(l.b),Object(o.ec)(o.G))},token:t,providedIn:"root"}),t=r([s(0,Object(o.s)(T)),i("design:paramtypes",[Object,A,o.w,l.b,o.G])],t),t})(),N=(()=>{let t=class{constructor(t,e,n){this.toastrService=t,this.toastPackage=e,this.ngZone=n,this.width=-1,this.toastClasses="",this.state={value:"inactive",params:{easeTime:this.toastPackage.config.easeTime,easing:"ease-in"}},this.message=e.message,this.title=e.title,this.options=e.config,this.originalTimeout=e.config.timeOut,this.toastClasses=`${e.toastType} ${e.config.toastClass}`,this.sub=e.toastRef.afterActivate().subscribe(()=>{this.activateToast()}),this.sub1=e.toastRef.manualClosed().subscribe(()=>{this.remove()}),this.sub2=e.toastRef.timeoutReset().subscribe(()=>{this.resetTimeout()}),this.sub3=e.toastRef.countDuplicate().subscribe(t=>{this.duplicatesCount=t})}get displayStyle(){if("inactive"===this.state.value)return"none"}ngOnDestroy(){this.sub.unsubscribe(),this.sub1.unsubscribe(),this.sub2.unsubscribe(),this.sub3.unsubscribe(),clearInterval(this.intervalId),clearTimeout(this.timeout)}activateToast(){this.state=Object.assign({},this.state,{value:"active"}),!0!==this.options.disableTimeOut&&"timeOut"!==this.options.disableTimeOut&&this.options.timeOut&&(this.outsideTimeout(()=>this.remove(),this.options.timeOut),this.hideTime=(new Date).getTime()+this.options.timeOut,this.options.progressBar&&this.outsideInterval(()=>this.updateProgress(),10))}updateProgress(){if(0===this.width||100===this.width||!this.options.timeOut)return;const t=(new Date).getTime();this.width=(this.hideTime-t)/this.options.timeOut*100,"increasing"===this.options.progressAnimation&&(this.width=100-this.width),this.width<=0&&(this.width=0),this.width>=100&&(this.width=100)}resetTimeout(){clearTimeout(this.timeout),clearInterval(this.intervalId),this.state=Object.assign({},this.state,{value:"active"}),this.outsideTimeout(()=>this.remove(),this.originalTimeout),this.options.timeOut=this.originalTimeout,this.hideTime=(new Date).getTime()+(this.options.timeOut||0),this.width=-1,this.options.progressBar&&this.outsideInterval(()=>this.updateProgress(),10)}remove(){"removed"!==this.state.value&&(clearTimeout(this.timeout),this.state=Object.assign({},this.state,{value:"removed"}),this.outsideTimeout(()=>this.toastrService.remove(this.toastPackage.toastId),+this.toastPackage.config.easeTime))}tapToast(){"removed"!==this.state.value&&(this.toastPackage.triggerTap(),this.options.tapToDismiss&&this.remove())}stickAround(){"removed"!==this.state.value&&(clearTimeout(this.timeout),this.options.timeOut=0,this.hideTime=0,clearInterval(this.intervalId),this.width=0)}delayedHideToast(){!0!==this.options.disableTimeOut&&"extendedTimeOut"!==this.options.disableTimeOut&&0!==this.options.extendedTimeOut&&"removed"!==this.state.value&&(this.outsideTimeout(()=>this.remove(),this.options.extendedTimeOut),this.options.timeOut=this.options.extendedTimeOut,this.hideTime=(new Date).getTime()+(this.options.timeOut||0),this.width=-1,this.options.progressBar&&this.outsideInterval(()=>this.updateProgress(),10))}outsideTimeout(t,e){this.ngZone?this.ngZone.runOutsideAngular(()=>this.timeout=setTimeout(()=>this.runInsideAngular(t),e)):this.timeout=setTimeout(()=>t(),e)}outsideInterval(t,e){this.ngZone?this.ngZone.runOutsideAngular(()=>this.intervalId=setInterval(()=>this.runInsideAngular(t),e)):this.intervalId=setInterval(()=>t(),e)}runInsideAngular(t){this.ngZone?this.ngZone.run(()=>t()):t()}};return t.\u0275fac=function(e){return new(e||t)(o.Ub(D),o.Ub(E),o.Ub(o.G))},t.\u0275cmp=o.Ob({type:t,selectors:[["","toast-component",""]],hostVars:5,hostBindings:function(t,e){1&t&&o.hc("click",function(){return e.tapToast()})("mouseenter",function(){return e.stickAround()})("mouseleave",function(){return e.delayedHideToast()}),2&t&&(o.Ic("@flyInOut",e.state),o.Kb(e.toastClasses),o.Hc("display",e.displayStyle))},attrs:h,decls:5,vars:5,consts:[["class","toast-close-button","aria-label","Close",3,"click",4,"ngIf"],[3,"class",4,"ngIf"],["role","alertdialog","aria-live","polite",3,"class","innerHTML",4,"ngIf"],["role","alertdialog","aria-live","polite",3,"class",4,"ngIf"],[4,"ngIf"],["aria-label","Close",1,"toast-close-button",3,"click"],["aria-hidden","true"],["role","alertdialog","aria-live","polite",3,"innerHTML"],["role","alertdialog","aria-live","polite"],[1,"toast-progress"]],template:function(t,e){1&t&&(o.Jc(0,d,3,0,"button",0),o.Jc(1,p,3,5,"div",1),o.Jc(2,m,1,3,"div",2),o.Jc(3,g,2,4,"div",3),o.Jc(4,y,2,2,"div",4)),2&t&&(o.pc("ngIf",e.options.closeButton),o.Ib(1),o.pc("ngIf",e.title),o.Ib(1),o.pc("ngIf",e.message&&e.options.enableHtml),o.Ib(1),o.pc("ngIf",e.message&&!e.options.enableHtml),o.Ib(1),o.pc("ngIf",e.options.progressBar))},directives:[u.m],encapsulation:2,data:{animation:[Object(a.j)("flyInOut",[Object(a.g)("inactive",Object(a.h)({opacity:0})),Object(a.g)("active",Object(a.h)({opacity:1})),Object(a.g)("removed",Object(a.h)({opacity:0})),Object(a.i)("inactive => active",Object(a.e)("{{ easeTime }}ms {{ easing }}")),Object(a.i)("active => removed",Object(a.e)("{{ easeTime }}ms {{ easing }}"))])]}}),t=r([i("design:paramtypes",[D,E,o.G])],t),t})();var F;const L=Object.assign({},O,{toastComponent:N});let M=(()=>{let t=F=class{static forRoot(t={}){return{ngModule:F,providers:[{provide:T,useValue:{default:L,config:t}}]}}};return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=o.Sb({type:t}),t.\u0275inj=o.Rb({imports:[[u.c]]}),t})();Object.assign({},O,{toastComponent:(()=>{let t=class{constructor(t,e,n){this.toastrService=t,this.toastPackage=e,this.appRef=n,this.width=-1,this.toastClasses="",this.state="inactive",this.message=e.message,this.title=e.title,this.options=e.config,this.originalTimeout=e.config.timeOut,this.toastClasses=`${e.toastType} ${e.config.toastClass}`,this.sub=e.toastRef.afterActivate().subscribe(()=>{this.activateToast()}),this.sub1=e.toastRef.manualClosed().subscribe(()=>{this.remove()}),this.sub2=e.toastRef.timeoutReset().subscribe(()=>{this.resetTimeout()}),this.sub3=e.toastRef.countDuplicate().subscribe(t=>{this.duplicatesCount=t})}get displayStyle(){if("inactive"===this.state)return"none"}ngOnDestroy(){this.sub.unsubscribe(),this.sub1.unsubscribe(),this.sub2.unsubscribe(),this.sub3.unsubscribe(),clearInterval(this.intervalId),clearTimeout(this.timeout)}activateToast(){this.state="active",!0!==this.options.disableTimeOut&&"timeOut"!==this.options.disableTimeOut&&this.options.timeOut&&(this.timeout=setTimeout(()=>{this.remove()},this.options.timeOut),this.hideTime=(new Date).getTime()+this.options.timeOut,this.options.progressBar&&(this.intervalId=setInterval(()=>this.updateProgress(),10))),this.options.onActivateTick&&this.appRef.tick()}updateProgress(){if(0===this.width||100===this.width||!this.options.timeOut)return;const t=(new Date).getTime();this.width=(this.hideTime-t)/this.options.timeOut*100,"increasing"===this.options.progressAnimation&&(this.width=100-this.width),this.width<=0&&(this.width=0),this.width>=100&&(this.width=100)}resetTimeout(){clearTimeout(this.timeout),clearInterval(this.intervalId),this.state="active",this.options.timeOut=this.originalTimeout,this.timeout=setTimeout(()=>this.remove(),this.originalTimeout),this.hideTime=(new Date).getTime()+(this.originalTimeout||0),this.width=-1,this.options.progressBar&&(this.intervalId=setInterval(()=>this.updateProgress(),10))}remove(){"removed"!==this.state&&(clearTimeout(this.timeout),this.state="removed",this.timeout=setTimeout(()=>this.toastrService.remove(this.toastPackage.toastId)))}tapToast(){"removed"!==this.state&&(this.toastPackage.triggerTap(),this.options.tapToDismiss&&this.remove())}stickAround(){"removed"!==this.state&&(clearTimeout(this.timeout),this.options.timeOut=0,this.hideTime=0,clearInterval(this.intervalId),this.width=0)}delayedHideToast(){!0!==this.options.disableTimeOut&&"extendedTimeOut"!==this.options.disableTimeOut&&0!==this.options.extendedTimeOut&&"removed"!==this.state&&(this.timeout=setTimeout(()=>this.remove(),this.options.extendedTimeOut),this.options.timeOut=this.options.extendedTimeOut,this.hideTime=(new Date).getTime()+(this.options.timeOut||0),this.width=-1,this.options.progressBar&&(this.intervalId=setInterval(()=>this.updateProgress(),10)))}};return t.\u0275fac=function(e){return new(e||t)(o.Ub(D),o.Ub(E),o.Ub(o.g))},t.\u0275cmp=o.Ob({type:t,selectors:[["","toast-component",""]],hostVars:4,hostBindings:function(t,e){1&t&&o.hc("click",function(){return e.tapToast()})("mouseenter",function(){return e.stickAround()})("mouseleave",function(){return e.delayedHideToast()}),2&t&&(o.Kb(e.toastClasses),o.Hc("display",e.displayStyle))},attrs:h,decls:5,vars:5,consts:[["class","toast-close-button","aria-label","Close",3,"click",4,"ngIf"],[3,"class",4,"ngIf"],["role","alert","aria-live","polite",3,"class","innerHTML",4,"ngIf"],["role","alert","aria-live","polite",3,"class",4,"ngIf"],[4,"ngIf"],["aria-label","Close",1,"toast-close-button",3,"click"],["aria-hidden","true"],["role","alert","aria-live","polite",3,"innerHTML"],["role","alert","aria-live","polite"],[1,"toast-progress"]],template:function(t,e){1&t&&(o.Jc(0,b,3,0,"button",0),o.Jc(1,v,3,5,"div",1),o.Jc(2,w,1,3,"div",2),o.Jc(3,S,2,4,"div",3),o.Jc(4,C,2,2,"div",4)),2&t&&(o.pc("ngIf",e.options.closeButton),o.Ib(1),o.pc("ngIf",e.title),o.Ib(1),o.pc("ngIf",e.message&&e.options.enableHtml),o.Ib(1),o.pc("ngIf",e.message&&!e.options.enableHtml),o.Ib(1),o.pc("ngIf",e.options.progressBar))},directives:[u.m],encapsulation:2}),t=r([i("design:paramtypes",[D,E,o.g])],t),t})()})},"7o/Q":function(t,e,n){"use strict";n.d(e,"a",function(){return l});var r=n("n6bG"),s=n("gRHU"),i=n("quSY"),o=n("2QA8"),a=n("2fFW"),c=n("NJ4a");class l extends i.a{constructor(t,e,n){switch(super(),this.syncErrorValue=null,this.syncErrorThrown=!1,this.syncErrorThrowable=!1,this.isStopped=!1,arguments.length){case 0:this.destination=s.a;break;case 1:if(!t){this.destination=s.a;break}if("object"==typeof t){t instanceof l?(this.syncErrorThrowable=t.syncErrorThrowable,this.destination=t,t.add(this)):(this.syncErrorThrowable=!0,this.destination=new u(this,t));break}default:this.syncErrorThrowable=!0,this.destination=new u(this,t,e,n)}}[o.a](){return this}static create(t,e,n){const r=new l(t,e,n);return r.syncErrorThrowable=!1,r}next(t){this.isStopped||this._next(t)}error(t){this.isStopped||(this.isStopped=!0,this._error(t))}complete(){this.isStopped||(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe())}_next(t){this.destination.next(t)}_error(t){this.destination.error(t),this.unsubscribe()}_complete(){this.destination.complete(),this.unsubscribe()}_unsubscribeAndRecycle(){const{_parentOrParents:t}=this;return this._parentOrParents=null,this.unsubscribe(),this.closed=!1,this.isStopped=!1,this._parentOrParents=t,this}}class u extends l{constructor(t,e,n,i){let o;super(),this._parentSubscriber=t;let a=this;Object(r.a)(e)?o=e:e&&(o=e.next,n=e.error,i=e.complete,e!==s.a&&(a=Object.create(e),Object(r.a)(a.unsubscribe)&&this.add(a.unsubscribe.bind(a)),a.unsubscribe=this.unsubscribe.bind(this))),this._context=a,this._next=o,this._error=n,this._complete=i}next(t){if(!this.isStopped&&this._next){const{_parentSubscriber:e}=this;a.a.useDeprecatedSynchronousErrorHandling&&e.syncErrorThrowable?this.__tryOrSetError(e,this._next,t)&&this.unsubscribe():this.__tryOrUnsub(this._next,t)}}error(t){if(!this.isStopped){const{_parentSubscriber:e}=this,{useDeprecatedSynchronousErrorHandling:n}=a.a;if(this._error)n&&e.syncErrorThrowable?(this.__tryOrSetError(e,this._error,t),this.unsubscribe()):(this.__tryOrUnsub(this._error,t),this.unsubscribe());else if(e.syncErrorThrowable)n?(e.syncErrorValue=t,e.syncErrorThrown=!0):Object(c.a)(t),this.unsubscribe();else{if(this.unsubscribe(),n)throw t;Object(c.a)(t)}}}complete(){if(!this.isStopped){const{_parentSubscriber:t}=this;if(this._complete){const e=()=>this._complete.call(this._context);a.a.useDeprecatedSynchronousErrorHandling&&t.syncErrorThrowable?(this.__tryOrSetError(t,e),this.unsubscribe()):(this.__tryOrUnsub(e),this.unsubscribe())}else this.unsubscribe()}}__tryOrUnsub(t,e){try{t.call(this._context,e)}catch(n){if(this.unsubscribe(),a.a.useDeprecatedSynchronousErrorHandling)throw n;Object(c.a)(n)}}__tryOrSetError(t,e,n){if(!a.a.useDeprecatedSynchronousErrorHandling)throw new Error("bad call");try{e.call(this._context,n)}catch(r){return a.a.useDeprecatedSynchronousErrorHandling?(t.syncErrorValue=r,t.syncErrorThrown=!0,!0):(Object(c.a)(r),!0)}return!1}_unsubscribe(){const{_parentSubscriber:t}=this;this._context=null,this._parentSubscriber=null,t.unsubscribe()}}},"9ppp":function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=(()=>{function t(){return Error.call(this),this.message="object unsubscribed",this.name="ObjectUnsubscribedError",this}return t.prototype=Object.create(Error.prototype),t})()},AytR:function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r={production:!0,baseUrl:"http://192.168.61.66:9090/hrms_api",contextPath:"hrms/"}},BFxc:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("7o/Q"),s=n("4I5i"),i=n("EY2u");function o(t){return function(e){return 0===t?Object(i.b)():e.lift(new a(t))}}class a{constructor(t){if(this.total=t,this.total<0)throw new s.a}call(t,e){return e.subscribe(new c(t,this.total))}}class c extends r.a{constructor(t,e){super(t),this.total=e,this.ring=new Array,this.count=0}_next(t){const e=this.ring,n=this.total,r=this.count++;e.length<n?e.push(t):e[r%n]=t}_complete(){const t=this.destination;let e=this.count;if(e>0){const n=this.count>=this.total?this.total:this.count,r=this.ring;for(let s=0;s<n;s++){const s=e++%n;t.next(r[s])}}t.complete()}}},Cfvw:function(t,e,n){"use strict";n.d(e,"a",function(){return h});var r=n("HDdC"),s=n("SeVD"),i=n("quSY"),o=n("kJWO"),a=n("jZKg"),c=n("Lhse"),l=n("c2HN"),u=n("I55L");function h(t,e){return e?function(t,e){if(null!=t){if(function(t){return t&&"function"==typeof t[o.a]}(t))return function(t,e){return new r.a(n=>{const r=new i.a;return r.add(e.schedule(()=>{const s=t[o.a]();r.add(s.subscribe({next(t){r.add(e.schedule(()=>n.next(t)))},error(t){r.add(e.schedule(()=>n.error(t)))},complete(){r.add(e.schedule(()=>n.complete()))}}))})),r})}(t,e);if(Object(l.a)(t))return function(t,e){return new r.a(n=>{const r=new i.a;return r.add(e.schedule(()=>t.then(t=>{r.add(e.schedule(()=>{n.next(t),r.add(e.schedule(()=>n.complete()))}))},t=>{r.add(e.schedule(()=>n.error(t)))}))),r})}(t,e);if(Object(u.a)(t))return Object(a.a)(t,e);if(function(t){return t&&"function"==typeof t[c.a]}(t)||"string"==typeof t)return function(t,e){if(!t)throw new Error("Iterable cannot be null");return new r.a(n=>{const r=new i.a;let s;return r.add(()=>{s&&"function"==typeof s.return&&s.return()}),r.add(e.schedule(()=>{s=t[c.a](),r.add(e.schedule(function(){if(n.closed)return;let t,e;try{const n=s.next();t=n.value,e=n.done}catch(r){return void n.error(r)}e?n.complete():(n.next(t),this.schedule())}))})),r})}(t,e)}throw new TypeError((null!==t&&typeof t||t)+" is not observable")}(t,e):t instanceof r.a?t:new r.a(Object(s.a)(t))}},DH7j:function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=(()=>Array.isArray||(t=>t&&"number"==typeof t.length))()},EQ5u:function(t,e,n){"use strict";n.d(e,"a",function(){return a}),n.d(e,"b",function(){return c});var r=n("XNiG"),s=n("HDdC"),i=(n("7o/Q"),n("quSY")),o=n("x+ZX");class a extends s.a{constructor(t,e){super(),this.source=t,this.subjectFactory=e,this._refCount=0,this._isComplete=!1}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){const t=this._subject;return t&&!t.isStopped||(this._subject=this.subjectFactory()),this._subject}connect(){let t=this._connection;return t||(this._isComplete=!1,t=this._connection=new i.a,t.add(this.source.subscribe(new l(this.getSubject(),this))),t.closed&&(this._connection=null,t=i.a.EMPTY)),t}refCount(){return Object(o.a)()(this)}}const c=(()=>{const t=a.prototype;return{operator:{value:null},_refCount:{value:0,writable:!0},_subject:{value:null,writable:!0},_connection:{value:null,writable:!0},_subscribe:{value:t._subscribe},_isComplete:{value:t._isComplete,writable:!0},getSubject:{value:t.getSubject},connect:{value:t.connect},refCount:{value:t.refCount}}})();class l extends r.b{constructor(t,e){super(t),this.connectable=e}_error(t){this._unsubscribe(),super._error(t)}_complete(){this.connectable._isComplete=!0,this._unsubscribe(),super._complete()}_unsubscribe(){const t=this.connectable;if(t){this.connectable=null;const e=t._connection;t._refCount=0,t._subject=null,t._connection=null,e&&e.unsubscribe()}}}},EY2u:function(t,e,n){"use strict";n.d(e,"a",function(){return s}),n.d(e,"b",function(){return i});var r=n("HDdC");const s=new r.a(t=>t.complete());function i(t){return t?function(t){return new r.a(e=>t.schedule(()=>e.complete()))}(t):s}},GyhO:function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("LRne"),s=n("bHdf");function i(...t){return Object(s.a)(1)(Object(r.a)(...t))}},HDdC:function(t,e,n){"use strict";n.d(e,"a",function(){return l});var r=n("7o/Q"),s=n("2QA8"),i=n("gRHU"),o=n("kJWO"),a=n("mCNh"),c=n("2fFW");let l=(()=>{class t{constructor(t){this._isScalar=!1,t&&(this._subscribe=t)}lift(e){const n=new t;return n.source=this,n.operator=e,n}subscribe(t,e,n){const{operator:o}=this,a=function(t,e,n){if(t){if(t instanceof r.a)return t;if(t[s.a])return t[s.a]()}return t||e||n?new r.a(t,e,n):new r.a(i.a)}(t,e,n);if(a.add(o?o.call(a,this.source):this.source||c.a.useDeprecatedSynchronousErrorHandling&&!a.syncErrorThrowable?this._subscribe(a):this._trySubscribe(a)),c.a.useDeprecatedSynchronousErrorHandling&&a.syncErrorThrowable&&(a.syncErrorThrowable=!1,a.syncErrorThrown))throw a.syncErrorValue;return a}_trySubscribe(t){try{return this._subscribe(t)}catch(e){c.a.useDeprecatedSynchronousErrorHandling&&(t.syncErrorThrown=!0,t.syncErrorValue=e),function(t){for(;t;){const{closed:e,destination:n,isStopped:s}=t;if(e||s)return!1;t=n&&n instanceof r.a?n:null}return!0}(t)?t.error(e):console.warn(e)}}forEach(t,e){return new(e=u(e))((e,n)=>{let r;r=this.subscribe(e=>{try{t(e)}catch(s){n(s),r&&r.unsubscribe()}},n,e)})}_subscribe(t){const{source:e}=this;return e&&e.subscribe(t)}[o.a](){return this}pipe(...t){return 0===t.length?this:Object(a.b)(t)(this)}toPromise(t){return new(t=u(t))((t,e)=>{let n;this.subscribe(t=>n=t,t=>e(t),()=>t(n))})}}return t.create=e=>new t(e),t})();function u(t){if(t||(t=c.a.Promise||Promise),!t)throw new Error("no Promise impl found");return t}},I55L:function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=t=>t&&"number"==typeof t.length&&"function"!=typeof t},IzEk:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("7o/Q"),s=n("4I5i"),i=n("EY2u");function o(t){return e=>0===t?Object(i.b)():e.lift(new a(t))}class a{constructor(t){if(this.total=t,this.total<0)throw new s.a}call(t,e){return e.subscribe(new c(t,this.total))}}class c extends r.a{constructor(t,e){super(t),this.total=e,this.count=0}_next(t){const e=this.total,n=++this.count;n<=e&&(this.destination.next(t),n===e&&(this.destination.complete(),this.unsubscribe()))}}},JIr8:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("l7GE"),s=n("51Dv"),i=n("ZUHj");function o(t){return function(e){const n=new a(t),r=e.lift(n);return n.caught=r}}class a{constructor(t){this.selector=t}call(t,e){return e.subscribe(new c(t,this.selector,this.caught))}}class c extends r.a{constructor(t,e,n){super(t),this.selector=e,this.caught=n}error(t){if(!this.isStopped){let n;try{n=this.selector(t,this.caught)}catch(e){return void super.error(e)}this._unsubscribeAndRecycle();const r=new s.a(this,void 0,void 0);this.add(r);const o=Object(i.a)(this,n,void 0,void 0,r);o!==r&&this.add(o)}}}},JX91:function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("GyhO"),s=n("z+Ro");function i(...t){const e=t[t.length-1];return Object(s.a)(e)?(t.pop(),n=>Object(r.a)(t,n,e)):e=>Object(r.a)(t,e)}},Kqap:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");function s(t,e){let n=!1;return arguments.length>=2&&(n=!0),function(r){return r.lift(new i(t,e,n))}}class i{constructor(t,e,n=!1){this.accumulator=t,this.seed=e,this.hasSeed=n}call(t,e){return e.subscribe(new o(t,this.accumulator,this.seed,this.hasSeed))}}class o extends r.a{constructor(t,e,n,r){super(t),this.accumulator=e,this._seed=n,this.hasSeed=r,this.index=0}get seed(){return this._seed}set seed(t){this.hasSeed=!0,this._seed=t}_next(t){if(this.hasSeed)return this._tryNext(t);this.seed=t,this.destination.next(t)}_tryNext(t){const e=this.index++;let n;try{n=this.accumulator(this.seed,t,e)}catch(r){this.destination.error(r)}this.seed=n,this.destination.next(n)}}},KqfI:function(t,e,n){"use strict";function r(){}n.d(e,"a",function(){return r})},LRne:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("z+Ro"),s=n("yCtX"),i=n("jZKg");function o(...t){let e=t[t.length-1];return Object(r.a)(e)?(t.pop(),Object(i.a)(t,e)):Object(s.a)(t)}},Lhse:function(t,e,n){"use strict";function r(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}n.d(e,"a",function(){return s});const s=r()},NJ4a:function(t,e,n){"use strict";function r(t){setTimeout(()=>{throw t},0)}n.d(e,"a",function(){return r})},R0Ic:function(t,e,n){"use strict";n.d(e,"a",function(){return i}),n.d(e,"b",function(){return r}),n.d(e,"c",function(){return s}),n.d(e,"d",function(){return f}),n.d(e,"e",function(){return a}),n.d(e,"f",function(){return c}),n.d(e,"g",function(){return u}),n.d(e,"h",function(){return l}),n.d(e,"i",function(){return h}),n.d(e,"j",function(){return o}),n.d(e,"k",function(){return p}),n.d(e,"l",function(){return m});class r{}class s{}const i="*";function o(t,e){return{type:7,name:t,definitions:e,options:{}}}function a(t,e=null){return{type:4,styles:e,timings:t}}function c(t,e=null){return{type:2,steps:t,options:e}}function l(t){return{type:6,styles:t,offset:null}}function u(t,e,n){return{type:0,name:t,styles:e,options:n}}function h(t,e,n=null){return{type:1,expr:t,animation:e,options:n}}function d(t){Promise.resolve(null).then(t)}class f{constructor(t=0,e=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=t+e}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._onStartFns.push(t)}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){d(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){const e="start"==t?this._onStartFns:this._onDoneFns;e.forEach(t=>t()),e.length=0}}class p{constructor(t){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=t;let e=0,n=0,r=0;const s=this.players.length;0==s?d(()=>this._onFinish()):this.players.forEach(t=>{t.onDone(()=>{++e==s&&this._onFinish()}),t.onDestroy(()=>{++n==s&&this._onDestroy()}),t.onStart(()=>{++r==s&&this._onStart()})}),this.totalTime=this.players.reduce((t,e)=>Math.max(t,e.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){const e=t*this.totalTime;this.players.forEach(t=>{const n=t.totalTime?Math.min(1,e/t.totalTime):1;t.setPosition(n)})}getPosition(){const t=this.players.reduce((t,e)=>null===t||e.totalTime>t.totalTime?e:t,null);return null!=t?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){const e="start"==t?this._onStartFns:this._onDoneFns;e.forEach(t=>t()),e.length=0}}const m="!"},SeVD:function(t,e,n){"use strict";n.d(e,"a",function(){return u});var r=n("ngJS"),s=n("NJ4a"),i=n("Lhse"),o=n("kJWO"),a=n("I55L"),c=n("c2HN"),l=n("XoHu");const u=t=>{if(t&&"function"==typeof t[o.a])return u=t,t=>{const e=u[o.a]();if("function"!=typeof e.subscribe)throw new TypeError("Provided object does not correctly implement Symbol.observable");return e.subscribe(t)};if(Object(a.a)(t))return Object(r.a)(t);if(Object(c.a)(t))return n=t,t=>(n.then(e=>{t.closed||(t.next(e),t.complete())},e=>t.error(e)).then(null,s.a),t);if(t&&"function"==typeof t[i.a])return e=t,t=>{const n=e[i.a]();for(;;){const e=n.next();if(e.done){t.complete();break}if(t.next(e.value),t.closed)break}return"function"==typeof n.return&&t.add(()=>{n.return&&n.return()}),t};{const e=Object(l.a)(t)?"an invalid object":`'${t}'`;throw new TypeError(`You provided ${e} where a stream was expected. You can provide an Observable, Promise, Array, or Iterable.`)}var e,n,u}},SpAZ:function(t,e,n){"use strict";function r(t){return t}n.d(e,"a",function(){return r})},SxV6:function(t,e,n){"use strict";n.d(e,"a",function(){return l});var r=n("sVev"),s=n("pLZG"),i=n("IzEk"),o=n("xbPD"),a=n("XDbj"),c=n("SpAZ");function l(t,e){const n=arguments.length>=2;return l=>l.pipe(t?Object(s.a)((e,n)=>t(e,n,l)):c.a,Object(i.a)(1),n?Object(o.a)(e):Object(a.a)(()=>new r.a))}},VRyK:function(t,e,n){"use strict";n.d(e,"a",function(){return a});var r=n("HDdC"),s=n("z+Ro"),i=n("bHdf"),o=n("yCtX");function a(...t){let e=Number.POSITIVE_INFINITY,n=null,a=t[t.length-1];return Object(s.a)(a)?(n=t.pop(),t.length>1&&"number"==typeof t[t.length-1]&&(e=t.pop())):"number"==typeof a&&(e=t.pop()),null===n&&1===t.length&&t[0]instanceof r.a?t[0]:Object(i.a)(e)(Object(o.a)(t,n))}},XDbj:function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("sVev"),s=n("7o/Q");function i(t=c){return e=>e.lift(new o(t))}class o{constructor(t){this.errorFactory=t}call(t,e){return e.subscribe(new a(t,this.errorFactory))}}class a extends s.a{constructor(t,e){super(t),this.errorFactory=e,this.hasValue=!1}_next(t){this.hasValue=!0,this.destination.next(t)}_complete(){if(this.hasValue)return this.destination.complete();{let e;try{e=this.errorFactory()}catch(t){e=t}this.destination.error(e)}}}function c(){return new r.a}},XNiG:function(t,e,n){"use strict";n.d(e,"b",function(){return l}),n.d(e,"a",function(){return u});var r=n("HDdC"),s=n("7o/Q"),i=n("quSY"),o=n("9ppp");class a extends i.a{constructor(t,e){super(),this.subject=t,this.subscriber=e,this.closed=!1}unsubscribe(){if(this.closed)return;this.closed=!0;const t=this.subject,e=t.observers;if(this.subject=null,!e||0===e.length||t.isStopped||t.closed)return;const n=e.indexOf(this.subscriber);-1!==n&&e.splice(n,1)}}var c=n("2QA8");class l extends s.a{constructor(t){super(t),this.destination=t}}let u=(()=>{class t extends r.a{constructor(){super(),this.observers=[],this.closed=!1,this.isStopped=!1,this.hasError=!1,this.thrownError=null}[c.a](){return new l(this)}lift(t){const e=new h(this,this);return e.operator=t,e}next(t){if(this.closed)throw new o.a;if(!this.isStopped){const{observers:e}=this,n=e.length,r=e.slice();for(let s=0;s<n;s++)r[s].next(t)}}error(t){if(this.closed)throw new o.a;this.hasError=!0,this.thrownError=t,this.isStopped=!0;const{observers:e}=this,n=e.length,r=e.slice();for(let s=0;s<n;s++)r[s].error(t);this.observers.length=0}complete(){if(this.closed)throw new o.a;this.isStopped=!0;const{observers:t}=this,e=t.length,n=t.slice();for(let r=0;r<e;r++)n[r].complete();this.observers.length=0}unsubscribe(){this.isStopped=!0,this.closed=!0,this.observers=null}_trySubscribe(t){if(this.closed)throw new o.a;return super._trySubscribe(t)}_subscribe(t){if(this.closed)throw new o.a;return this.hasError?(t.error(this.thrownError),i.a.EMPTY):this.isStopped?(t.complete(),i.a.EMPTY):(this.observers.push(t),new a(this,t))}asObservable(){const t=new r.a;return t.source=this,t}}return t.create=(t,e)=>new h(t,e),t})();class h extends u{constructor(t,e){super(),this.destination=t,this.source=e}next(t){const{destination:e}=this;e&&e.next&&e.next(t)}error(t){const{destination:e}=this;e&&e.error&&this.destination.error(t)}complete(){const{destination:t}=this;t&&t.complete&&this.destination.complete()}_subscribe(t){const{source:e}=this;return e?this.source.subscribe(t):i.a.EMPTY}}},XoHu:function(t,e,n){"use strict";function r(t){return null!==t&&"object"==typeof t}n.d(e,"a",function(){return r})},ZUHj:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("51Dv"),s=n("SeVD"),i=n("HDdC");function o(t,e,n,o,a=new r.a(t,n,o)){if(!a.closed)return e instanceof i.a?e.subscribe(a):Object(s.a)(e)(a)}},bHdf:function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("5+tZ"),s=n("SpAZ");function i(t=Number.POSITIVE_INFINITY){return Object(r.a)(s.a,t)}},bOdf:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("5+tZ");function s(t,e){return Object(r.a)(t,e,1)}},c2HN:function(t,e,n){"use strict";function r(t){return!!t&&"function"!=typeof t.subscribe&&"function"==typeof t.then}n.d(e,"a",function(){return r})},"d//k":function(t,e,n){"use strict";n.d(e,"a",function(){return c});var r=n("XNiG"),s=n("AytR"),i=n("fXoL"),o=n("tk/3"),a=n("5eHb");let c=(()=>{class t{constructor(t,e){this.http=t,this.toastr=e,this.baseUrl=s.a.baseUrl,this.loginStatusSubject=new r.a}getCurrentUser(){return this.http.get(`${this.baseUrl}/currentUser`)}generateToken(t){return this.http.post(`${this.baseUrl}/generateToken`,t)}loginUser(t){return localStorage.setItem("token",t),!0}isLoggedIn(){let t=localStorage.getItem("token");return null!=t&&""!=t&&null!=t}logout(){return localStorage.removeItem("token"),localStorage.removeItem("user"),localStorage.removeItem("activeTabName"),this.clearTimeout&&clearTimeout(this.clearTimeout),this.toastr.warning("Goodbye","logout"),!0}getToken(){return localStorage.getItem("token")}setUser(t){localStorage.setItem("user",JSON.stringify(t))}getUser(){let t=localStorage.getItem("user");return null!=t?JSON.parse(t):(this.logout(),null)}getLoginUserRole(){let t="",e=this.getUser();return e&&e.authorities.forEach(e=>{t=t+e.authority+","}),console.log("userAuthorities"+t),t}register(t){return this.http.post(`${this.baseUrl}/user/register`,t)}getById(t){return this.http.get(`${this.baseUrl}/user/get/${t}`)}sendPostRequest(t,e){return this.http.post(t,e)}sendPutRequest(t,e){return this.http.put(t,e)}}return t.\u0275fac=function(e){return new(e||t)(i.ec(o.c),i.ec(a.b))},t.\u0275prov=i.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},eIep:function(t,e,n){"use strict";n.d(e,"a",function(){return c});var r=n("l7GE"),s=n("51Dv"),i=n("ZUHj"),o=n("lJxs"),a=n("Cfvw");function c(t,e){return"function"==typeof e?n=>n.pipe(c((n,r)=>Object(a.a)(t(n,r)).pipe(Object(o.a)((t,s)=>e(n,t,r,s))))):e=>e.lift(new l(t))}class l{constructor(t){this.project=t}call(t,e){return e.subscribe(new u(t,this.project))}}class u extends r.a{constructor(t,e){super(t),this.project=e,this.index=0}_next(t){let e;const n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this._innerSub(e,t,n)}_innerSub(t,e,n){const r=this.innerSubscription;r&&r.unsubscribe();const o=new s.a(this,e,n),a=this.destination;a.add(o),this.innerSubscription=Object(i.a)(this,t,void 0,void 0,o),this.innerSubscription!==o&&a.add(this.innerSubscription)}_complete(){const{innerSubscription:t}=this;t&&!t.closed||super._complete(),this.unsubscribe()}_unsubscribe(){this.innerSubscription=null}notifyComplete(t){this.destination.remove(t),this.innerSubscription=null,this.isStopped&&super._complete()}notifyNext(t,e,n,r,s){this.destination.next(e)}}},fXoL:function(t,e,n){"use strict";n.d(e,"a",function(){return En}),n.d(e,"b",function(){return Lf}),n.d(e,"c",function(){return Pf}),n.d(e,"d",function(){return If}),n.d(e,"e",function(){return Af}),n.d(e,"f",function(){return Ip}),n.d(e,"g",function(){return vp}),n.d(e,"h",function(){return L}),n.d(e,"i",function(){return xh}),n.d(e,"j",function(){return Zf}),n.d(e,"k",function(){return wf}),n.d(e,"l",function(){return Ju}),n.d(e,"m",function(){return Hf}),n.d(e,"n",function(){return vf}),n.d(e,"o",function(){return eh}),n.d(e,"p",function(){return bs}),n.d(e,"q",function(){return Td}),n.d(e,"r",function(){return Co}),n.d(e,"s",function(){return tr}),n.d(e,"t",function(){return P}),n.d(e,"u",function(){return Ra}),n.d(e,"v",function(){return Cn}),n.d(e,"w",function(){return Uo}),n.d(e,"x",function(){return Cf}),n.d(e,"y",function(){return vh}),n.d(e,"z",function(){return Sh}),n.d(e,"A",function(){return Uf}),n.d(e,"B",function(){return kf}),n.d(e,"C",function(){return Mh}),n.d(e,"D",function(){return Sp}),n.d(e,"E",function(){return Lh}),n.d(e,"F",function(){return mp}),n.d(e,"G",function(){return Jf}),n.d(e,"H",function(){return er}),n.d(e,"I",function(){return Ef}),n.d(e,"J",function(){return Ff}),n.d(e,"K",function(){return Nf}),n.d(e,"L",function(){return Sf}),n.d(e,"M",function(){return sh}),n.d(e,"N",function(){return rh}),n.d(e,"O",function(){return xs}),n.d(e,"P",function(){return oh}),n.d(e,"Q",function(){return is}),n.d(e,"R",function(){return rr}),n.d(e,"S",function(){return Op}),n.d(e,"T",function(){return Rh}),n.d(e,"U",function(){return sp}),n.d(e,"V",function(){return xn}),n.d(e,"W",function(){return ah}),n.d(e,"X",function(){return Hh}),n.d(e,"Y",function(){return M}),n.d(e,"Z",function(){return gp}),n.d(e,"ab",function(){return fp}),n.d(e,"bb",function(){return m}),n.d(e,"cb",function(){return dp}),n.d(e,"db",function(){return kp}),n.d(e,"eb",function(){return ap}),n.d(e,"fb",function(){return Mf}),n.d(e,"gb",function(){return Oo}),n.d(e,"hb",function(){return Fl}),n.d(e,"ib",function(){return rs}),n.d(e,"jb",function(){return Ur}),n.d(e,"kb",function(){return xr}),n.d(e,"lb",function(){return jr}),n.d(e,"mb",function(){return Rr}),n.d(e,"nb",function(){return Ar}),n.d(e,"ob",function(){return Ir}),n.d(e,"pb",function(){return Pr}),n.d(e,"qb",function(){return Pl}),n.d(e,"rb",function(){return xp}),n.d(e,"sb",function(){return Rl}),n.d(e,"tb",function(){return Dl}),n.d(e,"ub",function(){return kr}),n.d(e,"vb",function(){return z}),n.d(e,"wb",function(){return Xo}),n.d(e,"xb",function(){return Za}),n.d(e,"yb",function(){return Qa}),n.d(e,"zb",function(){return Ga}),n.d(e,"Ab",function(){return Al}),n.d(e,"Bb",function(){return Yh}),n.d(e,"Cb",function(){return jt}),n.d(e,"Db",function(){return d}),n.d(e,"Eb",function(){return Tr}),n.d(e,"Fb",function(){return Vo}),n.d(e,"Gb",function(){return Ct}),n.d(e,"Hb",function(){return Zu}),n.d(e,"Ib",function(){return Si}),n.d(e,"Jb",function(){return aa}),n.d(e,"Kb",function(){return jc}),n.d(e,"Lb",function(){return Xc}),n.d(e,"Mb",function(){return Tc}),n.d(e,"Nb",function(){return Hd}),n.d(e,"Ob",function(){return et}),n.d(e,"Pb",function(){return lt}),n.d(e,"Qb",function(){return C}),n.d(e,"Rb",function(){return E}),n.d(e,"Sb",function(){return ot}),n.d(e,"Tb",function(){return ut}),n.d(e,"Ub",function(){return Na}),n.d(e,"Vb",function(){return $a}),n.d(e,"Wb",function(){return Ba}),n.d(e,"Xb",function(){return za}),n.d(e,"Yb",function(){return Va}),n.d(e,"Zb",function(){return Ha}),n.d(e,"ac",function(){return Ua}),n.d(e,"bc",function(){return qa}),n.d(e,"cc",function(){return dn}),n.d(e,"dc",function(){return Tl}),n.d(e,"ec",function(){return Kn}),n.d(e,"fc",function(){return pn}),n.d(e,"gc",function(){return Qd}),n.d(e,"hc",function(){return Wa}),n.d(e,"ic",function(){return $d}),n.d(e,"jc",function(){return tc}),n.d(e,"kc",function(){return bd}),n.d(e,"lc",function(){return _d}),n.d(e,"mc",function(){return vd}),n.d(e,"nc",function(){return rc}),n.d(e,"oc",function(){return nc}),n.d(e,"pc",function(){return La}),n.d(e,"qc",function(){return sc}),n.d(e,"rc",function(){return ic}),n.d(e,"sc",function(){return nd}),n.d(e,"tc",function(){return rd}),n.d(e,"uc",function(){return sd}),n.d(e,"vc",function(){return id}),n.d(e,"wc",function(){return od}),n.d(e,"xc",function(){return ad}),n.d(e,"yc",function(){return Md}),n.d(e,"zc",function(){return xa}),n.d(e,"Ac",function(){return Es}),n.d(e,"Bc",function(){return Cs}),n.d(e,"Cc",function(){return Jt}),n.d(e,"Dc",function(){return os}),n.d(e,"Ec",function(){return ls}),n.d(e,"Fc",function(){return cs}),n.d(e,"Gc",function(){return at}),n.d(e,"Hc",function(){return Oc}),n.d(e,"Ic",function(){return xl}),n.d(e,"Jc",function(){return Ta}),n.d(e,"Kc",function(){return qd}),n.d(e,"Lc",function(){return Vc}),n.d(e,"Mc",function(){return zc}),n.d(e,"Nc",function(){return Bc}),n.d(e,"Oc",function(){return qc}),n.d(e,"Pc",function(){return Qc}),n.d(e,"Qc",function(){return Gc}),n.d(e,"Rc",function(){return Ud});var r=n("XNiG"),s=n("quSY"),i=n("HDdC"),o=n("VRyK"),a=n("EQ5u"),c=n("x+ZX");function l(){return new r.a}function u(t){for(let e in t)if(t[e]===u)return e;throw Error("Could not find renamed property on target object.")}function h(t,e){for(const n in e)e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n])}function d(t){if("string"==typeof t)return t;if(Array.isArray(t))return"["+t.map(d).join(", ")+"]";if(null==t)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;const e=t.toString();if(null==e)return""+e;const n=e.indexOf("\n");return-1===n?e:e.substring(0,n)}function f(t,e){return null==t||""===t?null===e?"":e:null==e||""===e?t:t+" "+e}const p=u({__forward_ref__:u});function m(t){return t.__forward_ref__=m,t.toString=function(){return d(this())},t}function g(t){return y(t)?t():t}function y(t){return"function"==typeof t&&t.hasOwnProperty(p)&&t.__forward_ref__===m}class b extends Error{constructor(t,e){super(function(t,e){return`${t?`NG0${t}: `:""}${e}`}(t,e)),this.code=t}}function _(t){return"string"==typeof t?t:null==t?"":String(t)}function v(t){return"function"==typeof t?t.name||t.toString():"object"==typeof t&&null!=t&&"function"==typeof t.type?t.type.name||t.type.toString():_(t)}function w(t,e){const n=e?` in ${e}`:"";throw new b("201",`No provider for ${v(t)} found${n}`)}function S(t,e,n,r){throw new Error(`ASSERTION ERROR: ${t}`+(null==r?"":` [Expected=> ${n} ${r} ${e} <=Actual]`))}function C(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function E(t){return{providers:t.providers||[],imports:t.imports||[]}}function O(t){return T(t,k)||T(t,I)}function T(t,e){return t.hasOwnProperty(e)?t[e]:null}function x(t){return t&&(t.hasOwnProperty(j)||t.hasOwnProperty(A))?t[j]:null}const k=u({"\u0275prov":u}),j=u({"\u0275inj":u}),I=u({ngInjectableDef:u}),A=u({ngInjectorDef:u});var P=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}({});let R;function D(t){const e=R;return R=t,e}function N(t,e,n){const r=O(t);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&P.Optional?null:void 0!==e?e:void w(d(t),"Injector")}function F(t){return{toString:t}.toString()}var L=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}({}),M=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}({});const U="undefined"!=typeof globalThis&&globalThis,H="undefined"!=typeof window&&window,$="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self,V="undefined"!=typeof global&&global,z=U||V||H||$,B={},q=[],Q=[],G=u({"\u0275cmp":u}),Z=u({"\u0275dir":u}),W=u({"\u0275pipe":u}),K=u({"\u0275mod":u}),J=u({"\u0275loc":u}),Y=u({"\u0275fac":u}),X=u({__NG_ELEMENT_ID__:u});let tt=0;function et(t){return F(()=>{const e={},n={type:t.type,providersResolver:null,decls:t.decls,vars:t.vars,factory:null,template:t.template||null,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputs:null,outputs:null,exportAs:t.exportAs||null,onPush:t.changeDetection===L.OnPush,directiveDefs:null,pipeDefs:null,selectors:t.selectors||Q,viewQuery:t.viewQuery||null,features:t.features||null,data:t.data||{},encapsulation:t.encapsulation||M.Emulated,id:"c",styles:t.styles||Q,_:null,setInput:null,schemas:t.schemas||null,tView:null},r=t.directives,s=t.features,i=t.pipes;return n.id+=tt++,n.inputs=ct(t.inputs,e),n.outputs=ct(t.outputs),s&&s.forEach(t=>t(n)),n.directiveDefs=r?()=>("function"==typeof r?r():r).map(rt):null,n.pipeDefs=i?()=>("function"==typeof i?i():i).map(st):null,n})}function nt(t,e,n){const r=t.\u0275cmp;r.directiveDefs=()=>e.map(rt),r.pipeDefs=()=>n.map(st)}function rt(t){return ht(t)||dt(t)}function st(t){return ft(t)}const it={};function ot(t){const e={type:t.type,bootstrap:t.bootstrap||Q,declarations:t.declarations||Q,imports:t.imports||Q,exports:t.exports||Q,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null};return null!=t.id&&F(()=>{it[t.id]=t.type}),e}function at(t,e){return F(()=>{const n=pt(t,!0);n.declarations=e.declarations||Q,n.imports=e.imports||Q,n.exports=e.exports||Q})}function ct(t,e){if(null==t)return B;const n={};for(const r in t)if(t.hasOwnProperty(r)){let s=t[r],i=s;Array.isArray(s)&&(i=s[1],s=s[0]),n[s]=r,e&&(e[s]=i)}return n}const lt=et;function ut(t){return{type:t.type,name:t.name,factory:null,pure:!1!==t.pure,onDestroy:t.type.prototype.ngOnDestroy||null}}function ht(t){return t[G]||null}function dt(t){return t[Z]||null}function ft(t){return t[W]||null}function pt(t,e){const n=t[K]||null;if(!n&&!0===e)throw new Error(`Type ${d(t)} does not have '\u0275mod' property.`);return n}function mt(t){return Array.isArray(t)&&"object"==typeof t[1]}function gt(t){return Array.isArray(t)&&!0===t[1]}function yt(t){return 0!=(8&t.flags)}function bt(t){return 2==(2&t.flags)}function _t(t){return 1==(1&t.flags)}function vt(t){return null!==t.template}function wt(t,e){return t.hasOwnProperty(Y)?t[Y]:null}class St{constructor(t,e,n){this.previousValue=t,this.currentValue=e,this.firstChange=n}isFirstChange(){return this.firstChange}}function Ct(){return Et}function Et(t){return t.type.prototype.ngOnChanges&&(t.setInput=Tt),Ot}function Ot(){const t=xt(this),e=null==t?void 0:t.current;if(e){const n=t.previous;if(n===B)t.previous=e;else for(let t in e)n[t]=e[t];t.current=null,this.ngOnChanges(e)}}function Tt(t,e,n,r){const s=xt(t)||function(t,e){return t.__ngSimpleChanges__=e}(t,{previous:B,current:null}),i=s.current||(s.current={}),o=s.previous,a=this.declaredInputs[n],c=o[a];i[a]=new St(c&&c.currentValue,e,o===B),t[r]=e}function xt(t){return t.__ngSimpleChanges__||null}let kt;function jt(t){kt=t}function It(){return void 0!==kt?kt:"undefined"!=typeof document?document:void 0}function At(t){return!!t.listen}Ct.ngInherit=!0;const Pt={createRenderer:(t,e)=>It()};function Rt(t){for(;Array.isArray(t);)t=t[0];return t}function Dt(t,e){return Rt(e[t])}function Nt(t,e){return Rt(e[t.index])}function Ft(t,e){return t.data[e]}function Lt(t,e){return t[e]}function Mt(t,e){const n=e[t];return mt(n)?n:n[0]}function Ut(t){const e=function(t){return t.__ngContext__||null}(t);return e?Array.isArray(e)?e:e.lView:null}function Ht(t){return 4==(4&t[2])}function $t(t){return 128==(128&t[2])}function Vt(t,e){return null==e?null:t[e]}function zt(t){t[18]=0}function Bt(t,e){t[5]+=e;let n=t,r=t[3];for(;null!==r&&(1===e&&1===n[5]||-1===e&&0===n[5]);)r[5]+=e,n=r,r=r[3]}const qt={lFrame:we(null),bindingsEnabled:!0,isInCheckNoChangesMode:!1};function Qt(){return qt.bindingsEnabled}function Gt(){qt.bindingsEnabled=!0}function Zt(){qt.bindingsEnabled=!1}function Wt(){return qt.lFrame.lView}function Kt(){return qt.lFrame.tView}function Jt(t){qt.lFrame.contextLView=t}function Yt(){let t=Xt();for(;null!==t&&64===t.type;)t=t.parent;return t}function Xt(){return qt.lFrame.currentTNode}function te(){const t=qt.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function ee(t,e){const n=qt.lFrame;n.currentTNode=t,n.isParent=e}function ne(){return qt.lFrame.isParent}function re(){qt.lFrame.isParent=!1}function se(){return qt.isInCheckNoChangesMode}function ie(t){qt.isInCheckNoChangesMode=t}function oe(){const t=qt.lFrame;let e=t.bindingRootIndex;return-1===e&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function ae(){return qt.lFrame.bindingIndex}function ce(t){return qt.lFrame.bindingIndex=t}function le(){return qt.lFrame.bindingIndex++}function ue(t){const e=qt.lFrame,n=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,n}function he(t){qt.lFrame.inI18n=t}function de(t,e){const n=qt.lFrame;n.bindingIndex=n.bindingRootIndex=t,fe(e)}function fe(t){qt.lFrame.currentDirectiveIndex=t}function pe(t){const e=qt.lFrame.currentDirectiveIndex;return-1===e?null:t[e]}function me(){return qt.lFrame.currentQueryIndex}function ge(t){qt.lFrame.currentQueryIndex=t}function ye(t){const e=t[1];return 2===e.type?e.declTNode:1===e.type?t[6]:null}function be(t,e,n){if(n&P.SkipSelf){let r=e,s=t;for(;r=r.parent,!(null!==r||n&P.Host||(r=ye(s),null===r)||(s=s[15],10&r.type)););if(null===r)return!1;e=r,t=s}const r=qt.lFrame=ve();return r.currentTNode=e,r.lView=t,!0}function _e(t){const e=ve(),n=t[1];qt.lFrame=e,e.currentTNode=n.firstChild,e.lView=t,e.tView=n,e.contextLView=t,e.bindingIndex=n.bindingStartIndex,e.inI18n=!1}function ve(){const t=qt.lFrame,e=null===t?null:t.child;return null===e?we(t):e}function we(t){const e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return null!==t&&(t.child=e),e}function Se(){const t=qt.lFrame;return qt.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}const Ce=Se;function Ee(){const t=Se();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function Oe(){return qt.lFrame.selectedIndex}function Te(t){qt.lFrame.selectedIndex=t}function xe(){const t=qt.lFrame;return Ft(t.tView,t.selectedIndex)}function ke(){qt.lFrame.currentNamespace="http://www.w3.org/2000/svg"}function je(){qt.lFrame.currentNamespace="http://www.w3.org/1998/MathML/"}function Ie(){qt.lFrame.currentNamespace=null}function Ae(t,e){for(let n=e.directiveStart,r=e.directiveEnd;n<r;n++){const e=t.data[n].type.prototype,{ngAfterContentInit:r,ngAfterContentChecked:s,ngAfterViewInit:i,ngAfterViewChecked:o,ngOnDestroy:a}=e;r&&(t.contentHooks||(t.contentHooks=[])).push(-n,r),s&&((t.contentHooks||(t.contentHooks=[])).push(n,s),(t.contentCheckHooks||(t.contentCheckHooks=[])).push(n,s)),i&&(t.viewHooks||(t.viewHooks=[])).push(-n,i),o&&((t.viewHooks||(t.viewHooks=[])).push(n,o),(t.viewCheckHooks||(t.viewCheckHooks=[])).push(n,o)),null!=a&&(t.destroyHooks||(t.destroyHooks=[])).push(n,a)}}function Pe(t,e,n){Ne(t,e,3,n)}function Re(t,e,n,r){(3&t[2])===n&&Ne(t,e,n,r)}function De(t,e){let n=t[2];(3&n)===e&&(n&=2047,n+=1,t[2]=n)}function Ne(t,e,n,r){const s=null!=r?r:-1,i=e.length-1;let o=0;for(let a=void 0!==r?65535&t[18]:0;a<i;a++)if("number"==typeof e[a+1]){if(o=e[a],null!=r&&o>=r)break}else e[a]<0&&(t[18]+=65536),(o<s||-1==s)&&(Fe(t,n,e,a),t[18]=(**********&t[18])+a+2),a++}function Fe(t,e,n,r){const s=n[r]<0,i=n[r+1],o=t[s?-n[r]:n[r]];s?t[2]>>11<t[18]>>16&&(3&t[2])===e&&(t[2]+=2048,i.call(o)):i.call(o)}class Le{constructor(t,e,n){this.factory=t,this.resolving=!1,this.canSeeViewProviders=e,this.injectImpl=n}}function Me(t,e,n){const r=At(t);let s=0;for(;s<n.length;){const i=n[s];if("number"==typeof i){if(0!==i)break;s++;const o=n[s++],a=n[s++],c=n[s++];r?t.setAttribute(e,a,c,o):e.setAttributeNS(o,a,c)}else{const o=i,a=n[++s];He(o)?r&&t.setProperty(e,o,a):r?t.setAttribute(e,o,a):e.setAttribute(o,a),s++}}return s}function Ue(t){return 3===t||4===t||6===t}function He(t){return 64===t.charCodeAt(0)}function $e(t,e){if(null===e||0===e.length);else if(null===t||0===t.length)t=e.slice();else{let n=-1;for(let r=0;r<e.length;r++){const s=e[r];"number"==typeof s?n=s:0===n||Ve(t,n,s,null,-1===n||2===n?e[++r]:null)}}return t}function Ve(t,e,n,r,s){let i=0,o=t.length;if(-1===e)o=-1;else for(;i<t.length;){const n=t[i++];if("number"==typeof n){if(n===e){o=-1;break}if(n>e){o=i-1;break}}}for(;i<t.length;){const e=t[i];if("number"==typeof e)break;if(e===n){if(null===r)return void(null!==s&&(t[i+1]=s));if(r===t[i+1])return void(t[i+2]=s)}i++,null!==r&&i++,null!==s&&i++}-1!==o&&(t.splice(o,0,e),i=o+1),t.splice(i++,0,n),null!==r&&t.splice(i++,0,r),null!==s&&t.splice(i++,0,s)}function ze(t){return-1!==t}function Be(t){return 32767&t}function qe(t,e){let n=t>>16,r=e;for(;n>0;)r=r[15],n--;return r}let Qe=!0;function Ge(t){const e=Qe;return Qe=t,e}let Ze=0;function We(t,e){const n=Je(t,e);if(-1!==n)return n;const r=e[1];r.firstCreatePass&&(t.injectorIndex=e.length,Ke(r.data,t),Ke(e,null),Ke(r.blueprint,null));const s=Ye(t,e),i=t.injectorIndex;if(ze(s)){const t=Be(s),n=qe(s,e),r=n[1].data;for(let s=0;s<8;s++)e[i+s]=n[t+s]|r[t+s]}return e[i+8]=s,i}function Ke(t,e){t.push(0,0,0,0,0,0,0,0,e)}function Je(t,e){return-1===t.injectorIndex||t.parent&&t.parent.injectorIndex===t.injectorIndex||null===e[t.injectorIndex+8]?-1:t.injectorIndex}function Ye(t,e){if(t.parent&&-1!==t.parent.injectorIndex)return t.parent.injectorIndex;let n=0,r=null,s=e;for(;null!==s;){const t=s[1],e=t.type;if(r=2===e?t.declTNode:1===e?s[6]:null,null===r)return-1;if(n++,s=s[15],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return-1}function Xe(t,e,n){!function(t,e,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(X)&&(r=n[X]),null==r&&(r=n[X]=Ze++);const s=255&r;e.data[t+(s>>5)]|=1<<s}(t,e,n)}function tn(t,e,n){if(n&P.Optional)return t;w(e,"NodeInjector")}function en(t,e,n,r){if(n&P.Optional&&void 0===r&&(r=null),0==(n&(P.Self|P.Host))){const s=t[9],i=D(void 0);try{return s?s.get(e,r,n&P.Optional):N(e,r,n&P.Optional)}finally{D(i)}}return tn(r,e,n)}function nn(t,e,n,r=P.Default,s){if(null!==t){const i=function(t){if("string"==typeof t)return t.charCodeAt(0)||0;const e=t.hasOwnProperty(X)?t[X]:void 0;return"number"==typeof e?e>=0?255&e:sn:e}(n);if("function"==typeof i){if(!be(e,t,r))return r&P.Host?tn(s,n,r):en(e,n,r,s);try{const t=i();if(null!=t||r&P.Optional)return t;w(n)}finally{Ce()}}else if("number"==typeof i){let s=null,o=Je(t,e),a=-1,c=r&P.Host?e[16][6]:null;for((-1===o||r&P.SkipSelf)&&(a=-1===o?Ye(t,e):e[o+8],-1!==a&&un(r,!1)?(s=e[1],o=Be(a),e=qe(a,e)):o=-1);-1!==o;){const t=e[1];if(ln(i,o,t.data)){const t=on(o,e,n,s,r,c);if(t!==rn)return t}a=e[o+8],-1!==a&&un(r,e[1].data[o+8]===c)&&ln(i,o,e)?(s=t,o=Be(a),e=qe(a,e)):o=-1}}}return en(e,n,r,s)}const rn={};function sn(){return new hn(Yt(),Wt())}function on(t,e,n,r,s,i){const o=e[1],a=o.data[t+8],c=an(a,o,n,null==r?bt(a)&&Qe:r!=o&&0!=(3&a.type),s&P.Host&&i===a);return null!==c?cn(e,o,c,a):rn}function an(t,e,n,r,s){const i=t.providerIndexes,o=e.data,a=1048575&i,c=t.directiveStart,l=i>>20,u=s?a+l:t.directiveEnd;for(let h=r?a:a+l;h<u;h++){const t=o[h];if(h<c&&n===t||h>=c&&t.type===n)return h}if(s){const t=o[c];if(t&&vt(t)&&t.type===n)return c}return null}function cn(t,e,n,r){let s=t[n];const i=e.data;if(s instanceof Le){const o=s;o.resolving&&function(t,e){throw new b("200",`Circular dependency in DI detected for ${t}`)}(v(i[n]));const a=Ge(o.canSeeViewProviders);o.resolving=!0;const c=o.injectImpl?D(o.injectImpl):null;be(t,r,P.Default);try{s=t[n]=o.factory(void 0,i,t,r),e.firstCreatePass&&n>=r.directiveStart&&function(t,e,n){const{ngOnChanges:r,ngOnInit:s,ngDoCheck:i}=e.type.prototype;if(r){const r=Et(e);(n.preOrderHooks||(n.preOrderHooks=[])).push(t,r),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(t,r)}s&&(n.preOrderHooks||(n.preOrderHooks=[])).push(0-t,s),i&&((n.preOrderHooks||(n.preOrderHooks=[])).push(t,i),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(t,i))}(n,i[n],e)}finally{null!==c&&D(c),Ge(a),o.resolving=!1,Ce()}}return s}function ln(t,e,n){return!!(n[e+(t>>5)]&1<<t)}function un(t,e){return!(t&P.Self||t&P.Host&&e)}class hn{constructor(t,e){this._tNode=t,this._lView=e}get(t,e){return nn(this._tNode,this._lView,t,void 0,e)}}function dn(t){return F(()=>{const e=t.prototype.constructor,n=e[Y]||fn(e),r=Object.prototype;let s=Object.getPrototypeOf(t.prototype).constructor;for(;s&&s!==r;){const t=s[Y]||fn(s);if(t&&t!==n)return t;s=Object.getPrototypeOf(s)}return t=>new t})}function fn(t){return y(t)?()=>{const e=fn(g(t));return e&&e()}:wt(t)}function pn(t){return function(t,e){if("class"===e)return t.classes;if("style"===e)return t.styles;const n=t.attrs;if(n){const t=n.length;let r=0;for(;r<t;){const s=n[r];if(Ue(s))break;if(0===s)r+=2;else if("number"==typeof s)for(r++;r<t&&"string"==typeof n[r];)r++;else{if(s===e)return n[r+1];r+=2}}}return null}(Yt(),t)}const mn="__annotations__",gn="__parameters__",yn="__prop__metadata__";function bn(t,e,n,r,s){return F(()=>{const i=_n(e);function o(...t){if(this instanceof o)return i.call(this,...t),this;const e=new o(...t);return function(n){return s&&s(n,...t),(n.hasOwnProperty(mn)?n[mn]:Object.defineProperty(n,mn,{value:[]})[mn]).push(e),r&&r(n),n}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=t,o.annotationCls=o,o})}function _n(t){return function(...e){if(t){const n=t(...e);for(const t in n)this[t]=n[t]}}}function vn(t,e,n){return F(()=>{const r=_n(e);function s(...t){if(this instanceof s)return r.apply(this,t),this;const e=new s(...t);return n.annotation=e,n;function n(t,n,r){const s=t.hasOwnProperty(gn)?t[gn]:Object.defineProperty(t,gn,{value:[]})[gn];for(;s.length<=r;)s.push(null);return(s[r]=s[r]||[]).push(e),t}}return n&&(s.prototype=Object.create(n.prototype)),s.prototype.ngMetadataName=t,s.annotationCls=s,s})}function wn(t,e,n,r){return F(()=>{const s=_n(e);function i(...t){if(this instanceof i)return s.apply(this,t),this;const e=new i(...t);return function(n,s){const i=n.constructor,o=i.hasOwnProperty(yn)?i[yn]:Object.defineProperty(i,yn,{value:{}})[yn];o[s]=o.hasOwnProperty(s)&&o[s]||[],o[s].unshift(e),r&&r(n,s,...t)}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=t,i.annotationCls=i,i})}const Sn=function(){return vn("Attribute",t=>({attributeName:t,__NG_ELEMENT_ID__:()=>pn(t)}))}();class Cn{constructor(t,e){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof e?this.__NG_ELEMENT_ID__=e:void 0!==e&&(this.\u0275prov=C({token:this,providedIn:e.providedIn||"root",factory:e.factory}))}toString(){return`InjectionToken ${this._desc}`}}const En=new Cn("AnalyzeForEntryComponents");var On=function(t){return t[t.Token=0]="Token",t[t.Attribute=1]="Attribute",t[t.ChangeDetectorRef=2]="ChangeDetectorRef",t[t.Invalid=3]="Invalid",t}({});function Tn(){const t=z.ng;if(!t||!t.\u0275compilerFacade)throw new Error("Angular JIT compilation failed: '@angular/compiler' not loaded!\n  - JIT compilation is discouraged for production use-cases! Consider AOT mode instead.\n  - Did you bootstrap using '@angular/platform-browser-dynamic' or '@angular/platform-server'?\n  - Alternatively provide the compiler with 'import \"@angular/compiler\";' before bootstrapping.");return t.\u0275compilerFacade}const xn=Function;function kn(t){return"function"==typeof t}function jn(t,e){void 0===e&&(e=t);for(let n=0;n<t.length;n++){let r=t[n];Array.isArray(r)?(e===t&&(e=t.slice(0,n)),jn(r,e)):e!==t&&e.push(r)}return e}function In(t,e){t.forEach(t=>Array.isArray(t)?In(t,e):e(t))}function An(t,e,n){e>=t.length?t.push(n):t.splice(e,0,n)}function Pn(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function Rn(t,e){const n=[];for(let r=0;r<t;r++)n.push(e);return n}function Dn(t,e,n){let r=Fn(t,e);return r>=0?t[1|r]=n:(r=~r,function(t,e,n,r){let s=t.length;if(s==e)t.push(n,r);else if(1===s)t.push(r,t[0]),t[0]=n;else{for(s--,t.push(t[s-1],t[s]);s>e;)t[s]=t[s-2],s--;t[e]=n,t[e+1]=r}}(t,r,e,n)),r}function Nn(t,e){const n=Fn(t,e);if(n>=0)return t[1|n]}function Fn(t,e){return function(t,e,n){let r=0,s=t.length>>1;for(;s!==r;){const n=r+(s-r>>1),i=t[n<<1];if(e===i)return n<<1;i>e?s=n:r=n+1}return~(s<<1)}(t,e)}const Ln=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*(arguments|[^()]+\(arguments\))\)/,Mn=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{/,Un=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(/,Hn=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(\)\s*{\s*super\(\.\.\.arguments\)/;class $n{constructor(t){this._reflect=t||z.Reflect}isReflectionEnabled(){return!0}factory(t){return(...e)=>new t(...e)}_zipTypesAndAnnotations(t,e){let n;n=Rn(void 0===t?e.length:t.length);for(let r=0;r<n.length;r++)n[r]=void 0===t?[]:t[r]&&t[r]!=Object?[t[r]]:[],e&&null!=e[r]&&(n[r]=n[r].concat(e[r]));return n}_ownParameters(t,e){if(n=t.toString(),Ln.test(n)||Hn.test(n)||Mn.test(n)&&!Un.test(n))return null;var n;if(t.parameters&&t.parameters!==e.parameters)return t.parameters;const r=t.ctorParameters;if(r&&r!==e.ctorParameters){const t="function"==typeof r?r():r,e=t.map(t=>t&&t.type),n=t.map(t=>t&&Vn(t.decorators));return this._zipTypesAndAnnotations(e,n)}const s=t.hasOwnProperty(gn)&&t[gn],i=this._reflect&&this._reflect.getOwnMetadata&&this._reflect.getOwnMetadata("design:paramtypes",t);return i||s?this._zipTypesAndAnnotations(i,s):Rn(t.length)}parameters(t){if(!kn(t))return[];const e=zn(t);let n=this._ownParameters(t,e);return n||e===Object||(n=this.parameters(e)),n||[]}_ownAnnotations(t,e){if(t.annotations&&t.annotations!==e.annotations){let e=t.annotations;return"function"==typeof e&&e.annotations&&(e=e.annotations),e}return t.decorators&&t.decorators!==e.decorators?Vn(t.decorators):t.hasOwnProperty(mn)?t[mn]:null}annotations(t){if(!kn(t))return[];const e=zn(t),n=this._ownAnnotations(t,e)||[];return(e!==Object?this.annotations(e):[]).concat(n)}_ownPropMetadata(t,e){if(t.propMetadata&&t.propMetadata!==e.propMetadata){let e=t.propMetadata;return"function"==typeof e&&e.propMetadata&&(e=e.propMetadata),e}if(t.propDecorators&&t.propDecorators!==e.propDecorators){const e=t.propDecorators,n={};return Object.keys(e).forEach(t=>{n[t]=Vn(e[t])}),n}return t.hasOwnProperty(yn)?t[yn]:null}propMetadata(t){if(!kn(t))return{};const e=zn(t),n={};if(e!==Object){const t=this.propMetadata(e);Object.keys(t).forEach(e=>{n[e]=t[e]})}const r=this._ownPropMetadata(t,e);return r&&Object.keys(r).forEach(t=>{const e=[];n.hasOwnProperty(t)&&e.push(...n[t]),e.push(...r[t]),n[t]=e}),n}ownPropMetadata(t){return kn(t)&&this._ownPropMetadata(t,zn(t))||{}}hasLifecycleHook(t,e){return t instanceof xn&&e in t.prototype}guards(t){return{}}getter(t){return new Function("o","return o."+t+";")}setter(t){return new Function("o","v","return o."+t+" = v;")}method(t){return new Function("o","args",`if (!o.${t}) throw new Error('"${t}" is undefined');\n        return o.${t}.apply(o, args);`)}importUri(t){return"object"==typeof t&&t.filePath?t.filePath:`./${d(t)}`}resourceUri(t){return`./${d(t)}`}resolveIdentifier(t,e,n,r){return r}resolveEnum(t,e){return t[e]}}function Vn(t){return t?t.map(t=>new(0,t.type.annotationCls)(...t.args?t.args:[])):[]}function zn(t){const e=t.prototype?Object.getPrototypeOf(t.prototype):null;return(e?e.constructor:null)||Object}const Bn={},qn=/\n/gm,Qn=u({provide:String,useValue:u});let Gn;function Zn(t){const e=Gn;return Gn=t,e}function Wn(t,e=P.Default){if(void 0===Gn)throw new Error("inject() must be called from an injection context");return null===Gn?N(t,void 0,e):Gn.get(t,e&P.Optional?null:void 0,e)}function Kn(t,e=P.Default){return(R||Wn)(g(t),e)}function Jn(t){throw new Error("invalid")}function Yn(t){const e=[];for(let n=0;n<t.length;n++){const r=g(t[n]);if(Array.isArray(r)){if(0===r.length)throw new Error("Arguments array must have arguments.");let t,n=P.Default;for(let e=0;e<r.length;e++){const s=r[e],i=s.__NG_DI_FLAG__;"number"==typeof i?-1===i?t=s.token:n|=i:t=s}e.push(Kn(t,n))}else e.push(Kn(r))}return e}function Xn(t,e){return t.__NG_DI_FLAG__=e,t.prototype.__NG_DI_FLAG__=e,t}const tr=Xn(vn("Inject",t=>({token:t})),-1),er=Xn(vn("Optional"),8),nr=Xn(vn("Self"),2),rr=Xn(vn("SkipSelf"),4),sr=Xn(vn("Host"),1);let ir=null;function or(){return ir=ir||new $n}function ar(t){return cr(or().parameters(t))}function cr(t){const e=Tn();return t.map(t=>function(t,e){const n={token:null,host:!1,optional:!1,resolved:t.R3ResolvedDependencyType.Token,self:!1,skipSelf:!1};function r(e){n.resolved=t.R3ResolvedDependencyType.Token,n.token=e}if(Array.isArray(e)&&e.length>0)for(let s=0;s<e.length;s++){const i=e[s];if(void 0===i)continue;const o=Object.getPrototypeOf(i);if(i instanceof er||"Optional"===o.ngMetadataName)n.optional=!0;else if(i instanceof rr||"SkipSelf"===o.ngMetadataName)n.skipSelf=!0;else if(i instanceof nr||"Self"===o.ngMetadataName)n.self=!0;else if(i instanceof sr||"Host"===o.ngMetadataName)n.host=!0;else if(i instanceof tr)n.token=i.token;else if(i instanceof Sn){if(void 0===i.attributeName)throw new Error("Attribute name must be defined.");n.token=i.attributeName,n.resolved=t.R3ResolvedDependencyType.Attribute}else!0===i.__ChangeDetectorRef__?(n.token=i,n.resolved=t.R3ResolvedDependencyType.ChangeDetectorRef):r(i)}else void 0===e||Array.isArray(e)&&0===e.length?(n.token=void 0,n.resolved=On.Invalid):r(e);return n}(e,t))}let lr=new Map;const ur=new Set;function hr(t){return!!(t.templateUrl&&!t.hasOwnProperty("template")||t.styleUrls&&t.styleUrls.length)}let dr,fr;function pr(){if(void 0===dr&&(dr=null,z.trustedTypes))try{dr=z.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch(t){}return dr}function mr(t){var e;return(null===(e=pr())||void 0===e?void 0:e.createHTML(t))||t}function gr(){if(void 0===fr&&(fr=null,z.trustedTypes))try{fr=z.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch(t){}return fr}function yr(t){var e;return(null===(e=gr())||void 0===e?void 0:e.createHTML(t))||t}function br(t){var e;return(null===(e=gr())||void 0===e?void 0:e.createScript(t))||t}function _r(t){var e;return(null===(e=gr())||void 0===e?void 0:e.createScriptURL(t))||t}class vr{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see https://g.co/ng/security#xss)`}}class wr extends vr{getTypeName(){return"HTML"}}class Sr extends vr{getTypeName(){return"Style"}}class Cr extends vr{getTypeName(){return"Script"}}class Er extends vr{getTypeName(){return"URL"}}class Or extends vr{getTypeName(){return"ResourceURL"}}function Tr(t){return t instanceof vr?t.changingThisBreaksApplicationSecurity:t}function xr(t,e){const n=kr(t);if(null!=n&&n!==e){if("ResourceURL"===n&&"URL"===e)return!0;throw new Error(`Required a safe ${e}, got a ${n} (see https://g.co/ng/security#xss)`)}return n===e}function kr(t){return t instanceof vr&&t.getTypeName()||null}function jr(t){return new wr(t)}function Ir(t){return new Sr(t)}function Ar(t){return new Cr(t)}function Pr(t){return new Er(t)}function Rr(t){return new Or(t)}function Dr(t){const e=new Fr(t);return function(){try{return!!(new window.DOMParser).parseFromString(mr(""),"text/html")}catch(t){return!1}}()?new Nr(e):e}class Nr{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{const e=(new window.DOMParser).parseFromString(mr(t),"text/html").body;return null===e?this.inertDocumentHelper.getInertBodyElement(t):(e.removeChild(e.firstChild),e)}catch(e){return null}}}class Fr{constructor(t){if(this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert"),null==this.inertDocument.body){const t=this.inertDocument.createElement("html");this.inertDocument.appendChild(t);const e=this.inertDocument.createElement("body");t.appendChild(e)}}getInertBodyElement(t){const e=this.inertDocument.createElement("template");if("content"in e)return e.innerHTML=mr(t),e;const n=this.inertDocument.createElement("body");return n.innerHTML=mr(t),this.defaultDoc.documentMode&&this.stripCustomNsAttrs(n),n}stripCustomNsAttrs(t){const e=t.attributes;for(let r=e.length-1;0<r;r--){const n=e.item(r).name;"xmlns:ns1"!==n&&0!==n.indexOf("ns1:")||t.removeAttribute(n)}let n=t.firstChild;for(;n;)n.nodeType===Node.ELEMENT_NODE&&this.stripCustomNsAttrs(n),n=n.nextSibling}}const Lr=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^&:/?#]*(?:[/?#]|$))/gi,Mr=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\/]+=*$/i;function Ur(t){return(t=String(t)).match(Lr)||t.match(Mr)?t:"unsafe:"+t}function Hr(t){return(t=String(t)).split(",").map(t=>Ur(t.trim())).join(", ")}function $r(t){const e={};for(const n of t.split(","))e[n]=!0;return e}function Vr(...t){const e={};for(const n of t)for(const t in n)n.hasOwnProperty(t)&&(e[t]=!0);return e}const zr=$r("area,br,col,hr,img,wbr"),Br=$r("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),qr=$r("rp,rt"),Qr=Vr(qr,Br),Gr=Vr(zr,Vr(Br,$r("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Vr(qr,$r("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Qr),Zr=$r("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Wr=$r("srcset"),Kr=Vr(Zr,Wr,$r("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),$r("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext")),Jr=$r("script,style,template");class Yr{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let e=t.firstChild,n=!0;for(;e;)if(e.nodeType===Node.ELEMENT_NODE?n=this.startElement(e):e.nodeType===Node.TEXT_NODE?this.chars(e.nodeValue):this.sanitizedSomething=!0,n&&e.firstChild)e=e.firstChild;else for(;e;){e.nodeType===Node.ELEMENT_NODE&&this.endElement(e);let t=this.checkClobberedElement(e,e.nextSibling);if(t){e=t;break}e=this.checkClobberedElement(e,e.parentNode)}return this.buf.join("")}startElement(t){const e=t.nodeName.toLowerCase();if(!Gr.hasOwnProperty(e))return this.sanitizedSomething=!0,!Jr.hasOwnProperty(e);this.buf.push("<"),this.buf.push(e);const n=t.attributes;for(let r=0;r<n.length;r++){const t=n.item(r),e=t.name,s=e.toLowerCase();if(!Kr.hasOwnProperty(s)){this.sanitizedSomething=!0;continue}let i=t.value;Zr[s]&&(i=Ur(i)),Wr[s]&&(i=Hr(i)),this.buf.push(" ",e,'="',es(i),'"')}return this.buf.push(">"),!0}endElement(t){const e=t.nodeName.toLowerCase();Gr.hasOwnProperty(e)&&!zr.hasOwnProperty(e)&&(this.buf.push("</"),this.buf.push(e),this.buf.push(">"))}chars(t){this.buf.push(es(t))}checkClobberedElement(t,e){if(e&&(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)===Node.DOCUMENT_POSITION_CONTAINED_BY)throw new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`);return e}}const Xr=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ts=/([^\#-~ |!])/g;function es(t){return t.replace(/&/g,"&amp;").replace(Xr,function(t){return"&#"+(1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320)+65536)+";"}).replace(ts,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}let ns;function rs(t,e){let n=null;try{ns=ns||Dr(t);let r=e?String(e):"";n=ns.getInertBodyElement(r);let s=5,i=r;do{if(0===s)throw new Error("Failed to sanitize html because the input is unstable");s--,r=i,i=n.innerHTML,n=ns.getInertBodyElement(r)}while(r!==i);return mr((new Yr).sanitizeChildren(ss(n)||n))}finally{if(n){const t=ss(n)||n;for(;t.firstChild;)t.removeChild(t.firstChild)}}}function ss(t){return"content"in t&&function(t){return t.nodeType===Node.ELEMENT_NODE&&"TEMPLATE"===t.nodeName}(t)?t.content:null}var is=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}({});function os(t){const e=ps();return e?yr(e.sanitize(is.HTML,t)||""):xr(t,"HTML")?yr(Tr(t)):rs(It(),_(t))}function as(t){const e=ps();return e?e.sanitize(is.STYLE,t)||"":xr(t,"Style")?Tr(t):_(t)}function cs(t){const e=ps();return e?e.sanitize(is.URL,t)||"":xr(t,"URL")?Tr(t):Ur(_(t))}function ls(t){const e=ps();if(e)return _r(e.sanitize(is.RESOURCE_URL,t)||"");if(xr(t,"ResourceURL"))return _r(Tr(t));throw new Error("unsafe value used in a resource URL context (see https://g.co/ng/security#xss)")}function us(t){const e=ps();if(e)return br(e.sanitize(is.SCRIPT,t)||"");if(xr(t,"Script"))return br(Tr(t));throw new Error("unsafe value used in a script context")}function hs(t){return mr(t[0])}function ds(t){return function(t){var e;return(null===(e=pr())||void 0===e?void 0:e.createScriptURL(t))||t}(t[0])}function fs(t,e,n){return function(t,e){return"src"===e&&("embed"===t||"frame"===t||"iframe"===t||"media"===t||"script"===t)||"href"===e&&("base"===t||"link"===t)?ls:cs}(e,n)(t)}function ps(){const t=Wt();return t&&t[12]}function ms(t){return t.ngDebugContext}function gs(t){return t.ngOriginalError}function ys(t,...e){t.error(...e)}class bs{constructor(){this._console=console}handleError(t){const e=this._findOriginalError(t),n=this._findContext(t),r=function(t){return t.ngErrorLogger||ys}(t);r(this._console,"ERROR",t),e&&r(this._console,"ORIGINAL ERROR",e),n&&r(this._console,"ERROR CONTEXT",n)}_findContext(t){return t?ms(t)?ms(t):this._findContext(gs(t)):null}_findOriginalError(t){let e=gs(t);for(;e&&gs(e);)e=gs(e);return e}}const _s=/^>|^->|<!--|-->|--!>|<!-$/g,vs=/(<|>)/;function ws(t,e){t.__ngContext__=e}const Ss=(()=>("undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||setTimeout).bind(z))();function Cs(t){return{name:"window",target:t.ownerDocument.defaultView}}function Es(t){return{name:"document",target:t.ownerDocument}}function Os(t){return{name:"body",target:t.ownerDocument.body}}function Ts(t){return t instanceof Function?t():t}var xs=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}({});let ks;function js(t,e){return ks(t,e)}function Is(t){const e=t[3];return gt(e)?e[3]:e}function As(t){return Rs(t[13])}function Ps(t){return Rs(t[4])}function Rs(t){for(;null!==t&&!gt(t);)t=t[4];return t}function Ds(t,e,n,r,s){if(null!=r){let i,o=!1;gt(r)?i=r:mt(r)&&(o=!0,r=r[0]);const a=Rt(r);0===t&&null!==n?null==s?Qs(e,n,a):qs(e,n,a,s||null,!0):1===t&&null!==n?qs(e,n,a,s||null,!0):2===t?si(e,a,o):3===t&&e.destroyNode(a),null!=i&&function(t,e,n,r,s){const i=n[7];i!==Rt(n)&&Ds(e,t,r,i,s);for(let o=10;o<n.length;o++){const s=n[o];oi(s[1],s,t,e,r,i)}}(e,t,i,n,s)}}function Ns(t,e){return At(t)?t.createText(e):t.createTextNode(e)}function Fs(t,e,n){At(t)?t.setValue(e,n):e.textContent=n}function Ls(t,e){return t.createComment(function(t){return t.replace(_s,t=>t.replace(vs,"\u200b$1\u200b"))}(e))}function Ms(t,e,n){return At(t)?t.createElement(e,n):null===n?t.createElement(e):t.createElementNS(n,e)}function Us(t,e){const n=t[9],r=n.indexOf(e),s=e[3];1024&e[2]&&(e[2]&=-1025,Bt(s,-1)),n.splice(r,1)}function Hs(t,e){if(t.length<=10)return;const n=10+e,r=t[n];if(r){const i=r[17];null!==i&&i!==t&&Us(i,r),e>0&&(t[n-1][4]=r[4]);const o=Pn(t,10+e);oi(r[1],s=r,s[11],2,null,null),s[0]=null,s[6]=null;const a=o[19];null!==a&&a.detachView(o[1]),r[3]=null,r[4]=null,r[2]&=-129}var s;return r}function $s(t,e){if(!(256&e[2])){const n=e[11];At(n)&&n.destroyNode&&oi(t,e,n,3,null,null),function(t){let e=t[13];if(!e)return Vs(t[1],t);for(;e;){let n=null;if(mt(e))n=e[13];else{const t=e[10];t&&(n=t)}if(!n){for(;e&&!e[4]&&e!==t;)mt(e)&&Vs(e[1],e),e=e[3];null===e&&(e=t),mt(e)&&Vs(e[1],e),n=e&&e[4]}e=n}}(e)}}function Vs(t,e){if(!(256&e[2])){e[2]&=-129,e[2]|=256,function(t,e){let n;if(null!=t&&null!=(n=t.destroyHooks))for(let r=0;r<n.length;r+=2){const t=e[n[r]];if(!(t instanceof Le)){const e=n[r+1];if(Array.isArray(e))for(let n=0;n<e.length;n+=2)e[n+1].call(t[e[n]]);else e.call(t)}}}(t,e),function(t,e){const n=t.cleanup,r=e[7];let s=-1;if(null!==n)for(let i=0;i<n.length-1;i+=2)if("string"==typeof n[i]){const t=n[i+1],o="function"==typeof t?t(e):Rt(e[t]),a=r[s=n[i+2]],c=n[i+3];"boolean"==typeof c?o.removeEventListener(n[i],a,c):c>=0?r[s=c]():r[s=-c].unsubscribe(),i+=2}else{const t=r[s=n[i+1]];n[i].call(t)}if(null!==r){for(let t=s+1;t<r.length;t++)(0,r[t])();e[7]=null}}(t,e),1===e[1].type&&At(e[11])&&e[11].destroy();const n=e[17];if(null!==n&&gt(e[3])){n!==e[3]&&Us(n,e);const r=e[19];null!==r&&r.detachView(t)}}}function zs(t,e,n){return Bs(t,e.parent,n)}function Bs(t,e,n){let r=e;for(;null!==r&&40&r.type;)r=(e=r).parent;if(null===r)return n[0];if(2&r.flags){const e=t.data[r.directiveStart].encapsulation;if(e===M.None||e===M.Emulated)return null}return Nt(r,n)}function qs(t,e,n,r,s){At(t)?t.insertBefore(e,n,r,s):e.insertBefore(n,r,s)}function Qs(t,e,n){At(t)?t.appendChild(e,n):e.appendChild(n)}function Gs(t,e,n,r,s){null!==r?qs(t,e,n,r,s):Qs(t,e,n)}function Zs(t,e){return At(t)?t.parentNode(e):e.parentNode}function Ws(t,e,n){return Ys(t,e,n)}function Ks(t,e,n){return 40&t.type?Nt(t,n):null}let Js,Ys=Ks;function Xs(t,e){Ys=t,Js=e}function ti(t,e,n,r){const s=zs(t,r,e),i=e[11],o=Ws(r.parent||e[6],r,e);if(null!=s)if(Array.isArray(n))for(let a=0;a<n.length;a++)Gs(i,s,n[a],o,!1);else Gs(i,s,n,o,!1);void 0!==Js&&Js(i,r,e,n,s)}function ei(t,e){if(null!==e){const n=e.type;if(3&n)return Nt(e,t);if(4&n)return ri(-1,t[e.index]);if(8&n){const n=e.child;if(null!==n)return ei(t,n);{const n=t[e.index];return gt(n)?ri(-1,n):Rt(n)}}if(32&n)return js(e,t)()||Rt(t[e.index]);{const n=ni(t,e);return null!==n?Array.isArray(n)?n[0]:ei(Is(t[16]),n):ei(t,e.next)}}return null}function ni(t,e){return null!==e?t[16][6].projection[e.projection]:null}function ri(t,e){const n=10+t+1;if(n<e.length){const t=e[n],r=t[1].firstChild;if(null!==r)return ei(t,r)}return e[7]}function si(t,e,n){const r=Zs(t,e);r&&function(t,e,n,r){At(t)?t.removeChild(e,n,r):e.removeChild(n)}(t,r,e,n)}function ii(t,e,n,r,s,i,o){for(;null!=n;){const a=r[n.index],c=n.type;if(o&&0===e&&(a&&ws(Rt(a),r),n.flags|=4),64!=(64&n.flags))if(8&c)ii(t,e,n.child,r,s,i,!1),Ds(e,t,s,a,i);else if(32&c){const o=js(n,r);let c;for(;c=o();)Ds(e,t,s,c,i);Ds(e,t,s,a,i)}else 16&c?ai(t,e,r,n,s,i):Ds(e,t,s,a,i);n=o?n.projectionNext:n.next}}function oi(t,e,n,r,s,i){ii(n,r,t.firstChild,e,s,i,!1)}function ai(t,e,n,r,s,i){const o=n[16],a=o[6].projection[r.projection];if(Array.isArray(a))for(let c=0;c<a.length;c++)Ds(e,t,s,a[c],i);else ii(t,e,a,o[3],s,i,!0)}function ci(t,e,n){At(t)?t.setAttribute(e,"style",n):e.style.cssText=n}function li(t,e,n){At(t)?""===n?t.removeAttribute(e,"class"):t.setAttribute(e,"class",n):e.className=n}function ui(t,e,n){let r=t.length;for(;;){const s=t.indexOf(e,n);if(-1===s)return s;if(0===s||t.charCodeAt(s-1)<=32){const n=e.length;if(s+n===r||t.charCodeAt(s+n)<=32)return s}n=s+1}}function hi(t,e,n){let r=0;for(;r<t.length;){let s=t[r++];if(n&&"class"===s){if(s=t[r],-1!==ui(s.toLowerCase(),e,0))return!0}else if(1===s){for(;r<t.length&&"string"==typeof(s=t[r++]);)if(s.toLowerCase()===e)return!0;return!1}}return!1}function di(t){return 4===t.type&&"ng-template"!==t.value}function fi(t,e,n){return e===(4!==t.type||n?t.value:"ng-template")}function pi(t,e,n){let r=4;const s=t.attrs||[],i=function(t){for(let e=0;e<t.length;e++)if(Ue(t[e]))return e;return t.length}(s);let o=!1;for(let a=0;a<e.length;a++){const c=e[a];if("number"!=typeof c){if(!o)if(4&r){if(r=2|1&r,""!==c&&!fi(t,c,n)||""===c&&1===e.length){if(mi(r))return!1;o=!0}}else{const l=8&r?c:e[++a];if(8&r&&null!==t.attrs){if(!hi(t.attrs,l,n)){if(mi(r))return!1;o=!0}continue}const u=gi(8&r?"class":c,s,di(t),n);if(-1===u){if(mi(r))return!1;o=!0;continue}if(""!==l){let t;t=u>i?"":s[u+1].toLowerCase();const e=8&r?t:null;if(e&&-1!==ui(e,l,0)||2&r&&l!==t){if(mi(r))return!1;o=!0}}}}else{if(!o&&!mi(r)&&!mi(c))return!1;if(o&&mi(c))continue;o=!1,r=c|1&r}}return mi(r)||o}function mi(t){return 0==(1&t)}function gi(t,e,n,r){if(null===e)return-1;let s=0;if(r||!n){let n=!1;for(;s<e.length;){const r=e[s];if(r===t)return s;if(3===r||6===r)n=!0;else{if(1===r||2===r){let t=e[++s];for(;"string"==typeof t;)t=e[++s];continue}if(4===r)break;if(0===r){s+=4;continue}}s+=n?1:2}return-1}return function(t,e){let n=t.indexOf(4);if(n>-1)for(n++;n<t.length;){const r=t[n];if("number"==typeof r)return-1;if(r===e)return n;n++}return-1}(e,t)}function yi(t,e,n=!1){for(let r=0;r<e.length;r++)if(pi(t,e[r],n))return!0;return!1}function bi(t,e){t:for(let n=0;n<e.length;n++){const r=e[n];if(t.length===r.length){for(let e=0;e<t.length;e++)if(t[e]!==r[e])continue t;return!0}}return!1}function _i(t,e){return t?":not("+e.trim()+")":e}function vi(t){let e=t[0],n=1,r=2,s="",i=!1;for(;n<t.length;){let o=t[n];if("string"==typeof o)if(2&r){const e=t[++n];s+="["+o+(e.length>0?'="'+e+'"':"")+"]"}else 8&r?s+="."+o:4&r&&(s+=" "+o);else""===s||mi(o)||(e+=_i(i,s),s=""),r=o,i=i||!mi(r);n++}return""!==s&&(e+=_i(i,s)),e}const wi={};function Si(t){Ci(Kt(),Wt(),Oe()+t,se())}function Ci(t,e,n,r){if(!r)if(3==(3&e[2])){const r=t.preOrderCheckHooks;null!==r&&Pe(e,r,n)}else{const r=t.preOrderHooks;null!==r&&Re(e,r,0,n)}Te(n)}function Ei(t,e){return t<<17|e<<2}function Oi(t){return t>>17&32767}function Ti(t){return 2|t}function xi(t){return(131068&t)>>2}function ki(t,e){return-131069&t|e<<2}function ji(t){return 1|t}function Ii(t,e){const n=t.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const s=n[r],i=n[r+1];if(-1!==i){const n=t.data[i];ge(s),n.contentQueries(2,e[i],i)}}}function Ai(t,e,n,r,s,i,o,a,c,l){const u=e.blueprint.slice();return u[0]=s,u[2]=140|r,zt(u),u[3]=u[15]=t,u[8]=n,u[10]=o||t&&t[10],u[11]=a||t&&t[11],u[12]=c||t&&t[12]||null,u[9]=l||t&&t[9]||null,u[6]=i,u[16]=2==e.type?t[16]:u,u}function Pi(t,e,n,r,s){let i=t.data[e];if(null===i)i=Ri(t,e,n,r,s),qt.lFrame.inI18n&&(i.flags|=64);else if(64&i.type){i.type=n,i.value=r,i.attrs=s;const t=te();i.injectorIndex=null===t?-1:t.injectorIndex}return ee(i,!0),i}function Ri(t,e,n,r,s){const i=Xt(),o=ne(),a=t.data[e]=function(t,e,n,r,s,i){return{type:n,index:r,insertBeforeIndex:null,injectorIndex:e?e.injectorIndex:-1,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,propertyBindings:null,flags:0,providerIndexes:0,value:s,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tViews:null,next:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,o?i:i&&i.parent,n,e,r,s);return null===t.firstChild&&(t.firstChild=a),null!==i&&(o?null==i.child&&null!==a.parent&&(i.child=a):null===i.next&&(i.next=a)),a}function Di(t,e,n,r){if(0===n)return-1;const s=e.length;for(let i=0;i<n;i++)e.push(r),t.blueprint.push(r),t.data.push(null);return s}function Ni(t,e,n){_e(e);try{const r=t.viewQuery;null!==r&&po(1,r,n);const s=t.template;null!==s&&Mi(t,e,s,1,n),t.firstCreatePass&&(t.firstCreatePass=!1),t.staticContentQueries&&Ii(t,e),t.staticViewQueries&&po(2,t.viewQuery,n);const i=t.components;null!==i&&function(t,e){for(let n=0;n<e.length;n++)co(t,e[n])}(e,i)}catch(r){throw t.firstCreatePass&&(t.incompleteFirstPass=!0),r}finally{e[2]&=-5,Ee()}}function Fi(t,e,n,r){const s=e[2];if(256==(256&s))return;_e(e);const i=se();try{zt(e),ce(t.bindingStartIndex),null!==n&&Mi(t,e,n,2,r);const o=3==(3&s);if(!i)if(o){const n=t.preOrderCheckHooks;null!==n&&Pe(e,n,null)}else{const n=t.preOrderHooks;null!==n&&Re(e,n,0,null),De(e,0)}if(function(t){for(let e=As(t);null!==e;e=Ps(e)){if(!e[2])continue;const t=e[9];for(let e=0;e<t.length;e++){const n=t[e],r=n[3];0==(1024&n[2])&&Bt(r,1),n[2]|=1024}}}(e),function(t){for(let e=As(t);null!==e;e=Ps(e))for(let t=10;t<e.length;t++){const n=e[t],r=n[1];$t(n)&&Fi(r,n,r.template,n[8])}}(e),null!==t.contentQueries&&Ii(t,e),!i)if(o){const n=t.contentCheckHooks;null!==n&&Pe(e,n)}else{const n=t.contentHooks;null!==n&&Re(e,n,1),De(e,1)}!function(t,e){const n=t.hostBindingOpCodes;if(null!==n)try{for(let t=0;t<n.length;t++){const r=n[t];if(r<0)Te(~r);else{const s=r,i=n[++t],o=n[++t];de(i,s),o(2,e[s])}}}finally{Te(-1)}}(t,e);const a=t.components;null!==a&&function(t,e){for(let n=0;n<e.length;n++)oo(t,e[n])}(e,a);const c=t.viewQuery;if(null!==c&&po(2,c,r),!i)if(o){const n=t.viewCheckHooks;null!==n&&Pe(e,n)}else{const n=t.viewHooks;null!==n&&Re(e,n,2),De(e,2)}!0===t.firstUpdatePass&&(t.firstUpdatePass=!1),i||(e[2]&=-73),1024&e[2]&&(e[2]&=-1025,Bt(e[3],-1))}finally{Ee()}}function Li(t,e,n,r){const s=e[10],i=!se(),o=Ht(e);try{i&&!o&&s.begin&&s.begin(),o&&Ni(t,e,r),Fi(t,e,n,r)}finally{i&&!o&&s.end&&s.end()}}function Mi(t,e,n,r,s){const i=Oe();try{Te(-1),2&r&&e.length>20&&Ci(t,e,20,se()),n(r,s)}finally{Te(i)}}function Ui(t,e,n){if(yt(e)){const r=e.directiveEnd;for(let s=e.directiveStart;s<r;s++){const e=t.data[s];e.contentQueries&&e.contentQueries(1,n[s],s)}}}function Hi(t,e,n){Qt()&&(function(t,e,n,r){const s=n.directiveStart,i=n.directiveEnd;t.firstCreatePass||We(n,e),ws(r,e);const o=n.initialInputs;for(let a=s;a<i;a++){const r=t.data[a],i=vt(r);i&&to(e,n,r);const c=cn(e,t,a,n);ws(c,e),null!==o&&ro(0,a-s,c,r,0,o),i&&(Mt(n.index,e)[8]=c)}}(t,e,n,Nt(n,e)),128==(128&n.flags)&&function(t,e,n){const r=n.directiveStart,s=n.directiveEnd,i=n.index,o=qt.lFrame.currentDirectiveIndex;try{Te(i);for(let n=r;n<s;n++){const r=t.data[n],s=e[n];fe(n),null===r.hostBindings&&0===r.hostVars&&null===r.hostAttrs||Wi(r,s)}}finally{Te(-1),fe(o)}}(t,e,n))}function $i(t,e,n=Nt){const r=e.localNames;if(null!==r){let s=e.index+1;for(let i=0;i<r.length;i+=2){const o=r[i+1],a=-1===o?n(e,t):t[o];t[s++]=a}}}function Vi(t){const e=t.tView;return null===e||e.incompleteFirstPass?t.tView=zi(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts):e}function zi(t,e,n,r,s,i,o,a,c,l){const u=20+r,h=u+s,d=function(t,e){const n=[];for(let r=0;r<e;r++)n.push(r<t?null:wi);return n}(u,h),f="function"==typeof l?l():l;return d[1]={type:t,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:e,data:d.slice().fill(null,u),bindingStartIndex:u,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof o?o():o,firstChild:null,schemas:c,consts:f,incompleteFirstPass:!1}}function Bi(t,e,n,r){const s=go(e);null===n?s.push(r):(s.push(n),t.firstCreatePass&&yo(t).push(r,s.length-1))}function qi(t,e,n){for(let r in t)if(t.hasOwnProperty(r)){const s=t[r];(n=null===n?{}:n).hasOwnProperty(r)?n[r].push(e,s):n[r]=[e,s]}return n}function Qi(t,e,n,r,s,i,o,a){const c=Nt(e,n);let l,u=e.inputs;var h;!a&&null!=u&&(l=u[r])?(vo(t,n,l,r,s),bt(e)&&function(t,e){const n=Mt(e,t);16&n[2]||(n[2]|=64)}(n,e.index)):3&e.type&&(r="class"===(h=r)?"className":"for"===h?"htmlFor":"formaction"===h?"formAction":"innerHtml"===h?"innerHTML":"readonly"===h?"readOnly":"tabindex"===h?"tabIndex":h,s=null!=o?o(s,e.value||"",r):s,At(i)?i.setProperty(c,r,s):He(r)||(c.setProperty?c.setProperty(r,s):c[r]=s))}function Gi(t,e,n,r){let s=!1;if(Qt()){const i=function(t,e,n){const r=t.directiveRegistry;let s=null;if(r)for(let i=0;i<r.length;i++){const o=r[i];yi(n,o.selectors,!1)&&(s||(s=[]),Xe(We(n,e),t,o.type),vt(o)?(Ki(t,n),s.unshift(o)):s.push(o))}return s}(t,e,n),o=null===r?null:{"":-1};if(null!==i){s=!0,Yi(n,t.data.length,i.length);for(let t=0;t<i.length;t++){const e=i[t];e.providersResolver&&e.providersResolver(e)}let r=!1,a=!1,c=Di(t,e,i.length,null);for(let s=0;s<i.length;s++){const l=i[s];n.mergedAttrs=$e(n.mergedAttrs,l.hostAttrs),Xi(t,n,e,c,l),Ji(c,l,o),null!==l.contentQueries&&(n.flags|=8),null===l.hostBindings&&null===l.hostAttrs&&0===l.hostVars||(n.flags|=128);const u=l.type.prototype;!r&&(u.ngOnChanges||u.ngOnInit||u.ngDoCheck)&&((t.preOrderHooks||(t.preOrderHooks=[])).push(n.index),r=!0),a||!u.ngOnChanges&&!u.ngDoCheck||((t.preOrderCheckHooks||(t.preOrderCheckHooks=[])).push(n.index),a=!0),c++}!function(t,e){const n=e.directiveEnd,r=t.data,s=e.attrs,i=[];let o=null,a=null;for(let c=e.directiveStart;c<n;c++){const t=r[c],n=t.inputs,l=null===s||di(e)?null:so(n,s);i.push(l),o=qi(n,c,o),a=qi(t.outputs,c,a)}null!==o&&(o.hasOwnProperty("class")&&(e.flags|=16),o.hasOwnProperty("style")&&(e.flags|=32)),e.initialInputs=i,e.inputs=o,e.outputs=a}(t,n)}o&&function(t,e,n){if(e){const r=t.localNames=[];for(let t=0;t<e.length;t+=2){const s=n[e[t+1]];if(null==s)throw new b("301",`Export of name '${e[t+1]}' not found!`);r.push(e[t],s)}}}(n,r,o)}return n.mergedAttrs=$e(n.mergedAttrs,n.attrs),s}function Zi(t,e,n,r,s,i){const o=i.hostBindings;if(o){let n=t.hostBindingOpCodes;null===n&&(n=t.hostBindingOpCodes=[]);const i=~e.index;(function(t){let e=t.length;for(;e>0;){const n=t[--e];if("number"==typeof n&&n<0)return n}return 0})(n)!=i&&n.push(i),n.push(r,s,o)}}function Wi(t,e){null!==t.hostBindings&&t.hostBindings(1,e)}function Ki(t,e){e.flags|=2,(t.components||(t.components=[])).push(e.index)}function Ji(t,e,n){if(n){if(e.exportAs)for(let r=0;r<e.exportAs.length;r++)n[e.exportAs[r]]=t;vt(e)&&(n[""]=t)}}function Yi(t,e,n){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+n,t.providerIndexes=e}function Xi(t,e,n,r,s){t.data[r]=s;const i=s.factory||(s.factory=wt(s.type)),o=new Le(i,vt(s),null);t.blueprint[r]=o,n[r]=o,Zi(t,e,0,r,Di(t,n,s.hostVars,wi),s)}function to(t,e,n){const r=Nt(e,t),s=Vi(n),i=t[10],o=lo(t,Ai(t,s,null,n.onPush?64:16,r,e,i,i.createRenderer(r,n),null,null));t[e.index]=o}function eo(t,e,n,r,s,i){const o=Nt(t,e);no(e[11],o,i,t.value,n,r,s)}function no(t,e,n,r,s,i,o){if(null==i)At(t)?t.removeAttribute(e,s,n):e.removeAttribute(s);else{const a=null==o?_(i):o(i,r||"",s);At(t)?t.setAttribute(e,s,a,n):n?e.setAttributeNS(n,s,a):e.setAttribute(s,a)}}function ro(t,e,n,r,s,i){const o=i[e];if(null!==o){const t=r.setInput;for(let e=0;e<o.length;){const s=o[e++],i=o[e++],a=o[e++];null!==t?r.setInput(n,a,s,i):n[i]=a}}}function so(t,e){let n=null,r=0;for(;r<e.length;){const s=e[r];if(0!==s)if(5!==s){if("number"==typeof s)break;t.hasOwnProperty(s)&&(null===n&&(n=[]),n.push(s,t[s],e[r+1])),r+=2}else r+=2;else r+=4}return n}function io(t,e,n,r){return new Array(t,!0,!1,e,null,0,r,n,null,null)}function oo(t,e){const n=Mt(e,t);if($t(n)){const t=n[1];80&n[2]?Fi(t,n,t.template,n[8]):n[5]>0&&ao(n)}}function ao(t){for(let n=As(t);null!==n;n=Ps(n))for(let t=10;t<n.length;t++){const e=n[t];if(1024&e[2]){const t=e[1];Fi(t,e,t.template,e[8])}else e[5]>0&&ao(e)}const e=t[1].components;if(null!==e)for(let n=0;n<e.length;n++){const r=Mt(e[n],t);$t(r)&&r[5]>0&&ao(r)}}function co(t,e){const n=Mt(e,t),r=n[1];!function(t,e){for(let n=e.length;n<t.blueprint.length;n++)e.push(t.blueprint[n])}(r,n),Ni(r,n,n[8])}function lo(t,e){return t[13]?t[14][4]=e:t[13]=e,t[14]=e,e}function uo(t){for(;t;){t[2]|=64;const e=Is(t);if(0!=(512&t[2])&&!e)return t;t=e}return null}function ho(t,e,n){const r=e[10];r.begin&&r.begin();try{Fi(t,e,t.template,n)}catch(s){throw _o(e,s),s}finally{r.end&&r.end()}}function fo(t){!function(t){for(let e=0;e<t.components.length;e++){const n=t.components[e],r=Ut(n),s=r[1];Li(s,r,s.template,n)}}(t[8])}function po(t,e,n){ge(0),e(t,n)}const mo=(()=>Promise.resolve(null))();function go(t){return t[7]||(t[7]=[])}function yo(t){return t.cleanup||(t.cleanup=[])}function bo(t,e,n){return(null===t||vt(t))&&(n=function(t){for(;Array.isArray(t);){if("object"==typeof t[1])return t;t=t[0]}return null}(n[e.index])),n[11]}function _o(t,e){const n=t[9],r=n?n.get(bs,null):null;r&&r.handleError(e)}function vo(t,e,n,r,s){for(let i=0;i<n.length;){const o=n[i++],a=n[i++],c=e[o],l=t.data[o];null!==l.setInput?l.setInput(c,s,r,a):c[a]=s}}function wo(t,e,n){const r=Dt(e,t);Fs(t[11],r,n)}function So(t,e,n){let r=n?t.styles:null,s=n?t.classes:null,i=0;if(null!==e)for(let o=0;o<e.length;o++){const t=e[o];"number"==typeof t?i=t:1==i?s=f(s,t):2==i&&(r=f(r,t+": "+e[++o]+";"))}n?t.styles=r:t.stylesWithoutHost=r,n?t.classes=s:t.classesWithoutHost=s}const Co=new Cn("INJECTOR",-1);class Eo{get(t,e=Bn){if(e===Bn){const e=new Error(`NullInjectorError: No provider for ${d(t)}!`);throw e.name="NullInjectorError",e}return e}}const Oo=new Cn("Set Injector scope."),To={},xo={},ko=[];let jo;function Io(){return void 0===jo&&(jo=new Eo),jo}function Ao(t,e=null,n=null,r){return new Po(t,n,e||Io(),r)}class Po{constructor(t,e,n,r=null){this.parent=n,this.records=new Map,this.injectorDefTypes=new Set,this.onDestroy=new Set,this._destroyed=!1;const s=[];e&&In(e,n=>this.processProvider(n,t,e)),In([t],t=>this.processInjectorType(t,[],s)),this.records.set(Co,No(void 0,this));const i=this.records.get(Oo);this.scope=null!=i?i.value:null,this.source=r||("object"==typeof t?null:d(t))}get destroyed(){return this._destroyed}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{this.onDestroy.forEach(t=>t.ngOnDestroy())}finally{this.records.clear(),this.onDestroy.clear(),this.injectorDefTypes.clear()}}get(t,e=Bn,n=P.Default){this.assertNotDestroyed();const r=Zn(this);try{if(!(n&P.SkipSelf)){let e=this.records.get(t);if(void 0===e){const n=("function"==typeof(s=t)||"object"==typeof s&&s instanceof Cn)&&O(t);e=n&&this.injectableDefInScope(n)?No(Ro(t),To):null,this.records.set(t,e)}if(null!=e)return this.hydrate(t,e)}return(n&P.Self?Io():this.parent).get(t,e=n&P.Optional&&e===Bn?null:e)}catch(i){if("NullInjectorError"===i.name){if((i.ngTempTokenPath=i.ngTempTokenPath||[]).unshift(d(t)),r)throw i;return function(t,e,n,r){const s=t.ngTempTokenPath;throw e.__source&&s.unshift(e.__source),t.message=function(t,e,n,r=null){t=t&&"\n"===t.charAt(0)&&"\u0275"==t.charAt(1)?t.substr(2):t;let s=d(e);if(Array.isArray(e))s=e.map(d).join(" -> ");else if("object"==typeof e){let t=[];for(let n in e)if(e.hasOwnProperty(n)){let r=e[n];t.push(n+":"+("string"==typeof r?JSON.stringify(r):d(r)))}s=`{${t.join(", ")}}`}return`${n}${r?"("+r+")":""}[${s}]: ${t.replace(qn,"\n  ")}`}("\n"+t.message,s,n,r),t.ngTokenPath=s,t.ngTempTokenPath=null,t}(i,t,"R3InjectorError",this.source)}throw i}finally{Zn(r)}var s}_resolveInjectorDefTypes(){this.injectorDefTypes.forEach(t=>this.get(t))}toString(){const t=[];return this.records.forEach((e,n)=>t.push(d(n))),`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new Error("Injector has already been destroyed.")}processInjectorType(t,e,n){if(!(t=g(t)))return!1;let r=x(t);const s=null==r&&t.ngModule||void 0,i=void 0===s?t:s,o=-1!==n.indexOf(i);if(void 0!==s&&(r=x(s)),null==r)return!1;if(null!=r.imports&&!o){let t;n.push(i);try{In(r.imports,r=>{this.processInjectorType(r,e,n)&&(void 0===t&&(t=[]),t.push(r))})}finally{}if(void 0!==t)for(let e=0;e<t.length;e++){const{ngModule:n,providers:r}=t[e];In(r,t=>this.processProvider(t,n,r||ko))}}this.injectorDefTypes.add(i);const a=wt(i)||(()=>new i);this.records.set(i,No(a,To));const c=r.providers;if(null!=c&&!o){const e=t;In(c,t=>this.processProvider(t,e,c))}return void 0!==s&&void 0!==t.providers}processProvider(t,e,n){let r=Lo(t=g(t))?t:g(t&&t.provide);const s=function(t,e,n){return Fo(t)?No(void 0,t.useValue):No(Do(t),To)}(t);if(Lo(t)||!0!==t.multi)this.records.get(r);else{let e=this.records.get(r);e||(e=No(void 0,To,!0),e.factory=()=>Yn(e.multi),this.records.set(r,e)),r=t,e.multi.push(t)}this.records.set(r,s)}hydrate(t,e){var n;return e.value===To&&(e.value=xo,e.value=e.factory()),"object"==typeof e.value&&e.value&&null!==(n=e.value)&&"object"==typeof n&&"function"==typeof n.ngOnDestroy&&this.onDestroy.add(e.value),e.value}injectableDefInScope(t){return!!t.providedIn&&("string"==typeof t.providedIn?"any"===t.providedIn||t.providedIn===this.scope:this.injectorDefTypes.has(t.providedIn))}}function Ro(t){const e=O(t),n=null!==e?e.factory:wt(t);if(null!==n)return n;if(t instanceof Cn)throw new Error(`Token ${d(t)} is missing a \u0275prov definition.`);if(t instanceof Function)return function(t){const e=t.length;if(e>0){const n=Rn(e,"?");throw new Error(`Can't resolve all parameters for ${d(t)}: (${n.join(", ")}).`)}const n=function(t){const e=t&&(t[k]||t[I]);if(e){const n=function(t){if(t.hasOwnProperty("name"))return t.name;const e=(""+t).match(/^function\s*([^\s(]+)/);return null===e?"":e[1]}(t);return console.warn(`DEPRECATED: DI is instantiating a token "${n}" that inherits its @Injectable decorator but does not provide one itself.\nThis will become an error in a future version of Angular. Please add @Injectable() to the "${n}" class.`),e}return null}(t);return null!==n?()=>n.factory(t):()=>new t}(t);throw new Error("unreachable")}function Do(t,e,n){let r;if(Lo(t)){const e=g(t);return wt(e)||Ro(e)}if(Fo(t))r=()=>g(t.useValue);else if((s=t)&&s.useFactory)r=()=>t.useFactory(...Yn(t.deps||[]));else if(function(t){return!(!t||!t.useExisting)}(t))r=()=>Kn(g(t.useExisting));else{const e=g(t&&(t.useClass||t.provide));if(!function(t){return!!t.deps}(t))return wt(e)||Ro(e);r=()=>new e(...Yn(t.deps))}var s;return r}function No(t,e,n=!1){return{factory:t,value:e,multi:n?[]:void 0}}function Fo(t){return null!==t&&"object"==typeof t&&Qn in t}function Lo(t){return"function"==typeof t}const Mo=function(t,e,n){return function(t,e=null,n=null,r){const s=Ao(t,e,n,r);return s._resolveInjectorDefTypes(),s}({name:n},e,t,n)};let Uo=(()=>{class t{static create(t,e){return Array.isArray(t)?Mo(t,e,""):Mo(t.providers,t.parent,t.name||"")}}return t.THROW_IF_NOT_FOUND=Bn,t.NULL=new Eo,t.\u0275prov=C({token:t,providedIn:"any",factory:()=>Kn(Co)}),t.__NG_ELEMENT_ID__=-1,t})();function Ho(t,e){Ae(Ut(t)[1],Yt())}function $o(t){return Object.getPrototypeOf(t.prototype).constructor}function Vo(t){let e=$o(t.type),n=!0;const r=[t];for(;e;){let s;if(vt(t))s=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new Error("Directives cannot inherit Components");s=e.\u0275dir}if(s){if(n){r.push(s);const e=t;e.inputs=zo(t.inputs),e.declaredInputs=zo(t.declaredInputs),e.outputs=zo(t.outputs);const n=s.hostBindings;n&&Qo(t,n);const i=s.viewQuery,o=s.contentQueries;if(i&&Bo(t,i),o&&qo(t,o),h(t.inputs,s.inputs),h(t.declaredInputs,s.declaredInputs),h(t.outputs,s.outputs),vt(s)&&s.data.animation){const e=t.data;e.animation=(e.animation||[]).concat(s.data.animation)}}const e=s.features;if(e)for(let r=0;r<e.length;r++){const s=e[r];s&&s.ngInherit&&s(t),s===Vo&&(n=!1)}}e=Object.getPrototypeOf(e)}!function(t){let e=0,n=null;for(let r=t.length-1;r>=0;r--){const s=t[r];s.hostVars=e+=s.hostVars,s.hostAttrs=$e(s.hostAttrs,n=$e(n,s.hostAttrs))}}(r)}function zo(t){return t===B?{}:t===Q?[]:t}function Bo(t,e){const n=t.viewQuery;t.viewQuery=n?(t,r)=>{e(t,r),n(t,r)}:e}function qo(t,e){const n=t.contentQueries;t.contentQueries=n?(t,r,s)=>{e(t,r,s),n(t,r,s)}:e}function Qo(t,e){const n=t.hostBindings;t.hostBindings=n?(t,r)=>{e(t,r),n(t,r)}:e}const Go=["providersResolver"],Zo=["template","decls","consts","vars","onPush","ngContentSelectors","styles","encapsulation","schemas"];function Wo(t){let e,n=$o(t.type);e=vt(t)?n.\u0275cmp:n.\u0275dir;const r=t;for(const s of Go)r[s]=e[s];if(vt(e))for(const s of Zo)r[s]=e[s]}let Ko=null;function Jo(){if(!Ko){const t=z.Symbol;if(t&&t.iterator)Ko=t.iterator;else{const t=Object.getOwnPropertyNames(Map.prototype);for(let e=0;e<t.length;++e){const n=t[e];"entries"!==n&&"size"!==n&&Map.prototype[n]===Map.prototype.entries&&(Ko=n)}}}return Ko}class Yo{constructor(t){this.wrapped=t}static wrap(t){return new Yo(t)}static unwrap(t){return Yo.isWrapped(t)?t.wrapped:t}static isWrapped(t){return t instanceof Yo}}function Xo(t){return!!ta(t)&&(Array.isArray(t)||!(t instanceof Map)&&Jo()in t)}function ta(t){return null!==t&&("function"==typeof t||"object"==typeof t)}function ea(t,e,n){return t[e]=n}function na(t,e){return t[e]}function ra(t,e,n){return!Object.is(t[e],n)&&(t[e]=n,!0)}function sa(t,e,n,r){const s=ra(t,e,n);return ra(t,e+1,r)||s}function ia(t,e,n,r,s){const i=sa(t,e,n,r);return ra(t,e+2,s)||i}function oa(t,e,n,r,s,i){const o=sa(t,e,n,r);return sa(t,e+2,s,i)||o}function aa(t,e,n,r){const s=Wt();return ra(s,le(),e)&&(Kt(),eo(xe(),s,t,e,n,r)),aa}function ca(t,e){let n=!1,r=ae();for(let i=1;i<e.length;i+=2)n=ra(t,r++,e[i])||n;if(ce(r),!n)return wi;let s=e[0];for(let i=1;i<e.length;i+=2)s+=_(e[i])+e[i+1];return s}function la(t,e,n,r){return ra(t,le(),n)?e+_(n)+r:wi}function ua(t,e,n,r,s,i){const o=sa(t,ae(),n,s);return ue(2),o?e+_(n)+r+_(s)+i:wi}function ha(t,e,n,r,s,i,o,a){const c=ia(t,ae(),n,s,o);return ue(3),c?e+_(n)+r+_(s)+i+_(o)+a:wi}function da(t,e,n,r,s,i,o,a,c,l){const u=oa(t,ae(),n,s,o,c);return ue(4),u?e+_(n)+r+_(s)+i+_(o)+a+_(c)+l:wi}function fa(t,e,n,r,s,i,o,a,c,l,u,h){const d=ae();let f=oa(t,d,n,s,o,c);return f=ra(t,d+4,u)||f,ue(5),f?e+_(n)+r+_(s)+i+_(o)+a+_(c)+l+_(u)+h:wi}function pa(t,e,n,r,s,i,o,a,c,l,u,h,d,f){const p=ae();let m=oa(t,p,n,s,o,c);return m=sa(t,p+4,u,d)||m,ue(6),m?e+_(n)+r+_(s)+i+_(o)+a+_(c)+l+_(u)+h+_(d)+f:wi}function ma(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m){const g=ae();let y=oa(t,g,n,s,o,c);return y=ia(t,g+4,u,d,p)||y,ue(7),y?e+_(n)+r+_(s)+i+_(o)+a+_(c)+l+_(u)+h+_(d)+f+_(p)+m:wi}function ga(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y){const b=ae();let v=oa(t,b,n,s,o,c);return v=oa(t,b+4,u,d,p,g)||v,ue(8),v?e+_(n)+r+_(s)+i+_(o)+a+_(c)+l+_(u)+h+_(d)+f+_(p)+m+_(g)+y:wi}function ya(t,e,n,r,s,i){const o=Wt(),a=la(o,e,n,r);return a!==wi&&eo(xe(),o,t,a,s,i),ya}function ba(t,e,n,r,s,i,o,a){const c=Wt(),l=ua(c,e,n,r,s,i);return l!==wi&&eo(xe(),c,t,l,o,a),ba}function _a(t,e,n,r,s,i,o,a,c,l){const u=Wt(),h=ha(u,e,n,r,s,i,o,a);return h!==wi&&eo(xe(),u,t,h,c,l),_a}function va(t,e,n,r,s,i,o,a,c,l,u,h){const d=Wt(),f=da(d,e,n,r,s,i,o,a,c,l);return f!==wi&&eo(xe(),d,t,f,u,h),va}function wa(t,e,n,r,s,i,o,a,c,l,u,h,d,f){const p=Wt(),m=fa(p,e,n,r,s,i,o,a,c,l,u,h);return m!==wi&&eo(xe(),p,t,m,d,f),wa}function Sa(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m){const g=Wt(),y=pa(g,e,n,r,s,i,o,a,c,l,u,h,d,f);return y!==wi&&eo(xe(),g,t,y,p,m),Sa}function Ca(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y){const b=Wt(),_=ma(b,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m);return _!==wi&&eo(xe(),b,t,_,g,y),Ca}function Ea(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y,b,_){const v=Wt(),w=ga(v,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y);return w!==wi&&eo(xe(),v,t,w,b,_),Ea}function Oa(t,e,n,r){const s=Wt(),i=ca(s,e);return i!==wi&&eo(xe(),s,t,i,n,r),Oa}function Ta(t,e,n,r,s,i,o,a){const c=Wt(),l=Kt(),u=t+20,h=l.firstCreatePass?function(t,e,n,r,s,i,o,a,c){const l=e.consts,u=Pi(e,t,4,o||null,Vt(l,a));Gi(e,n,u,Vt(l,c)),Ae(e,u);const h=u.tViews=zi(2,u,r,s,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,l);return null!==e.queries&&(e.queries.template(e,u),h.queries=e.queries.embeddedTView(u)),u}(u,l,c,e,n,r,s,i,o):l.data[u];ee(h,!1);const d=c[11].createComment("");ti(l,c,d,h),ws(d,c),lo(c,c[u]=io(d,c,d,h)),_t(h)&&Hi(l,c,h),null!=o&&$i(c,h,a)}function xa(t){return Lt(qt.lFrame.contextLView,20+t)}const ka={"\u0275\u0275defineInjectable":C,"\u0275\u0275defineInjector":E,"\u0275\u0275inject":Kn,"\u0275\u0275invalidFactoryDep":Jn},ja=u({provide:String,useValue:u});function Ia(t){return void 0!==t.useClass}function Aa(t){return void 0!==t.useFactory}function Pa(t,e){const n=e||{providedIn:null},r={name:t.name,type:t,typeArgumentCount:0,providedIn:n.providedIn,userDeps:void 0};return(Ia(n)||Aa(n))&&void 0!==n.deps&&(r.userDeps=cr(n.deps)),Ia(n)?r.useClass=g(n.useClass):function(t){return ja in t}(n)?r.useValue=g(n.useValue):Aa(n)?r.useFactory=n.useFactory:function(t){return void 0!==t.useExisting}(n)&&(r.useExisting=g(n.useExisting)),r}const Ra=bn("Injectable",void 0,void 0,void 0,(t,e)=>Da(t,e)),Da=function(t,e){let n=null,r=null;t.hasOwnProperty(k)||Object.defineProperty(t,k,{get:()=>(null===n&&(n=Tn().compileInjectable(ka,`ng:///${t.name}/\u0275prov.js`,Pa(t,e))),n)}),t.hasOwnProperty(Y)||Object.defineProperty(t,Y,{get:()=>{if(null===r){const n=Pa(t,e),s=Tn();r=s.compileFactory(ka,`ng:///${t.name}/\u0275fac.js`,{name:n.name,type:n.type,typeArgumentCount:n.typeArgumentCount,deps:ar(t),injectFn:"inject",target:s.R3FactoryTarget.Injectable})}return r},configurable:!0})};function Na(t,e=P.Default){const n=Wt();return null===n?Kn(t,e):nn(Yt(),n,g(t),e)}function Fa(){throw new Error("invalid")}function La(t,e,n){const r=Wt();return ra(r,le(),e)&&Qi(Kt(),xe(),r,t,e,r[11],n,!1),La}function Ma(t,e,n,r,s){const i=s?"class":"style";vo(t,n,e.inputs[i],i,r)}function Ua(t,e,n,r){const s=Wt(),i=Kt(),o=20+t,a=s[11],c=s[o]=Ms(a,e,qt.lFrame.currentNamespace),l=i.firstCreatePass?function(t,e,n,r,s,i,o){const a=e.consts,c=Pi(e,t,2,s,Vt(a,i));return Gi(e,n,c,Vt(a,o)),null!==c.attrs&&So(c,c.attrs,!1),null!==c.mergedAttrs&&So(c,c.mergedAttrs,!0),null!==e.queries&&e.queries.elementStart(e,c),c}(o,i,s,0,e,n,r):i.data[o];ee(l,!0);const u=l.mergedAttrs;null!==u&&Me(a,c,u);const h=l.classes;null!==h&&li(a,c,h);const d=l.styles;null!==d&&ci(a,c,d),64!=(64&l.flags)&&ti(i,s,c,l),0===qt.lFrame.elementDepthCount&&ws(c,s),qt.lFrame.elementDepthCount++,_t(l)&&(Hi(i,s,l),Ui(i,l,s)),null!==r&&$i(s,l)}function Ha(){let t=Yt();ne()?re():(t=t.parent,ee(t,!1));const e=t;qt.lFrame.elementDepthCount--;const n=Kt();n.firstCreatePass&&(Ae(n,t),yt(t)&&n.queries.elementEnd(t)),null!=e.classesWithoutHost&&function(t){return 0!=(16&t.flags)}(e)&&Ma(n,e,Wt(),e.classesWithoutHost,!0),null!=e.stylesWithoutHost&&function(t){return 0!=(32&t.flags)}(e)&&Ma(n,e,Wt(),e.stylesWithoutHost,!1)}function $a(t,e,n,r){Ua(t,e,n,r),Ha()}function Va(t,e,n){const r=Wt(),s=Kt(),i=t+20,o=s.firstCreatePass?function(t,e,n,r,s){const i=e.consts,o=Vt(i,r),a=Pi(e,t,8,"ng-container",o);return null!==o&&So(a,o,!0),Gi(e,n,a,Vt(i,s)),null!==e.queries&&e.queries.elementStart(e,a),a}(i,s,r,e,n):s.data[i];ee(o,!0);const a=r[i]=r[11].createComment("");ti(s,r,a,o),ws(a,r),_t(o)&&(Hi(s,r,o),Ui(s,o,r)),null!=n&&$i(r,o)}function za(){let t=Yt();const e=Kt();ne()?re():(t=t.parent,ee(t,!1)),e.firstCreatePass&&(Ae(e,t),yt(t)&&e.queries.elementEnd(t))}function Ba(t,e,n){Va(t,e,n),za()}function qa(){return Wt()}function Qa(t){return!!t&&"function"==typeof t.then}function Ga(t){return!!t&&"function"==typeof t.subscribe}const Za=Ga;function Wa(t,e,n=!1,r){const s=Wt(),i=Kt(),o=Yt();return Ja(i,s,s[11],o,t,e,n,r),Wa}function Ka(t,e,n=!1,r){const s=Yt(),i=Wt(),o=Kt();return Ja(o,i,bo(pe(o.data),s,i),s,t,e,n,r),Ka}function Ja(t,e,n,r,s,i,o=!1,a){const c=_t(r),l=t.firstCreatePass&&yo(t),u=go(e);let h=!0;if(3&r.type){const d=Nt(r,e),f=a?a(d):B,p=f.target||d,m=u.length,g=a?t=>a(Rt(t[r.index])).target:r.index;if(At(n)){let o=null;if(!a&&c&&(o=function(t,e,n,r){const s=t.cleanup;if(null!=s)for(let i=0;i<s.length-1;i+=2){const t=s[i];if(t===n&&s[i+1]===r){const t=e[7],n=s[i+2];return t.length>n?t[n]:null}"string"==typeof t&&(i+=2)}return null}(t,e,s,r.index)),null!==o)(o.__ngLastListenerFn__||o).__ngNextListenerFn__=i,o.__ngLastListenerFn__=i,h=!1;else{i=Xa(r,e,i,!1);const t=n.listen(f.name||p,s,i);u.push(i,t),l&&l.push(s,g,m,m+1)}}else i=Xa(r,e,i,!0),p.addEventListener(s,i,o),u.push(i),l&&l.push(s,g,m,o)}else i=Xa(r,e,i,!1);const d=r.outputs;let f;if(h&&null!==d&&(f=d[s])){const t=f.length;if(t)for(let n=0;n<t;n+=2){const t=e[f[n]][f[n+1]].subscribe(i),o=u.length;u.push(i,t),l&&l.push(s,r.index,o,-(o+1))}}}function Ya(t,e,n){try{return!1!==e(n)}catch(r){return _o(t,r),!1}}function Xa(t,e,n,r){return function s(i){if(i===Function)return n;const o=2&t.flags?Mt(t.index,e):e;0==(32&e[2])&&uo(o);let a=Ya(e,n,i),c=s.__ngNextListenerFn__;for(;c;)a=Ya(e,c,i)&&a,c=c.__ngNextListenerFn__;return r&&!1===a&&(i.preventDefault(),i.returnValue=!1),a}}function tc(t=1){return function(t){return(qt.lFrame.contextLView=function(t,e){for(;t>0;)e=e[15],t--;return e}(t,qt.lFrame.contextLView))[8]}(t)}function ec(t,e){let n=null;const r=function(t){const e=t.attrs;if(null!=e){const t=e.indexOf(5);if(0==(1&t))return e[t+1]}return null}(t);for(let s=0;s<e.length;s++){const i=e[s];if("*"!==i){if(null===r?yi(t,i,!0):bi(r,i))return s}else n=s}return n}function nc(t){const e=Wt()[16][6];if(!e.projection){const n=e.projection=Rn(t?t.length:1,null),r=n.slice();let s=e.child;for(;null!==s;){const e=t?ec(s,t):0;null!==e&&(r[e]?r[e].projectionNext=s:n[e]=s,r[e]=s),s=s.next}}}function rc(t,e=0,n){const r=Wt(),s=Kt(),i=Pi(s,20+t,16,null,n||null);null===i.projection&&(i.projection=e),re(),64!=(64&i.flags)&&function(t,e,n){ai(e[11],0,e,n,zs(t,n,e),Ws(n.parent||e[6],n,e))}(s,r,i)}function sc(t,e,n){return ic(t,"",e,"",n),sc}function ic(t,e,n,r,s){const i=Wt(),o=la(i,e,n,r);return o!==wi&&Qi(Kt(),xe(),i,t,o,i[11],s,!1),ic}function oc(t,e,n,r,s,i,o){const a=Wt(),c=ua(a,e,n,r,s,i);return c!==wi&&Qi(Kt(),xe(),a,t,c,a[11],o,!1),oc}function ac(t,e,n,r,s,i,o,a,c){const l=Wt(),u=ha(l,e,n,r,s,i,o,a);return u!==wi&&Qi(Kt(),xe(),l,t,u,l[11],c,!1),ac}function cc(t,e,n,r,s,i,o,a,c,l,u){const h=Wt(),d=da(h,e,n,r,s,i,o,a,c,l);return d!==wi&&Qi(Kt(),xe(),h,t,d,h[11],u,!1),cc}function lc(t,e,n,r,s,i,o,a,c,l,u,h,d){const f=Wt(),p=fa(f,e,n,r,s,i,o,a,c,l,u,h);return p!==wi&&Qi(Kt(),xe(),f,t,p,f[11],d,!1),lc}function uc(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p){const m=Wt(),g=pa(m,e,n,r,s,i,o,a,c,l,u,h,d,f);return g!==wi&&Qi(Kt(),xe(),m,t,g,m[11],p,!1),uc}function hc(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g){const y=Wt(),b=ma(y,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m);return b!==wi&&Qi(Kt(),xe(),y,t,b,y[11],g,!1),hc}function dc(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y,b){const _=Wt(),v=ga(_,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y);return v!==wi&&Qi(Kt(),xe(),_,t,v,_[11],b,!1),dc}function fc(t,e,n){const r=Wt(),s=ca(r,e);return s!==wi&&Qi(Kt(),xe(),r,t,s,r[11],n,!1),fc}function pc(t,e,n,r,s){const i=t[n+1],o=null===e;let a=r?Oi(i):xi(i),c=!1;for(;0!==a&&(!1===c||o);){const n=t[a+1];mc(t[a],e)&&(c=!0,t[a+1]=r?ji(n):Ti(n)),a=r?Oi(n):xi(n)}c&&(t[n+1]=r?Ti(i):ji(i))}function mc(t,e){return null===t||null==e||(Array.isArray(t)?t[1]:t)===e||!(!Array.isArray(t)||"string"!=typeof e)&&Fn(t,e)>=0}const gc={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function yc(t){return t.substring(gc.key,gc.keyEnd)}function bc(t){return t.substring(gc.value,gc.valueEnd)}function _c(t,e){const n=gc.textEnd;return n===e?-1:(e=gc.keyEnd=function(t,e,n){for(;e<n&&t.charCodeAt(e)>32;)e++;return e}(t,gc.key=e,n),Sc(t,e,n))}function vc(t,e){const n=gc.textEnd;let r=gc.key=Sc(t,e,n);return n===r?-1:(r=gc.keyEnd=function(t,e,n){let r;for(;e<n&&(45===(r=t.charCodeAt(e))||95===r||(-33&r)>=65&&(-33&r)<=90||r>=48&&r<=57);)e++;return e}(t,r,n),r=Cc(t,r,n),r=gc.value=Sc(t,r,n),r=gc.valueEnd=function(t,e,n){let r=-1,s=-1,i=-1,o=e,a=o;for(;o<n;){const c=t.charCodeAt(o++);if(59===c)return a;34===c||39===c?a=o=Ec(t,c,o,n):e===o-4&&85===i&&82===s&&76===r&&40===c?a=o=Ec(t,41,o,n):c>32&&(a=o),i=s,s=r,r=-33&c}return a}(t,r,n),Cc(t,r,n))}function wc(t){gc.key=0,gc.keyEnd=0,gc.value=0,gc.valueEnd=0,gc.textEnd=t.length}function Sc(t,e,n){for(;e<n&&t.charCodeAt(e)<=32;)e++;return e}function Cc(t,e,n,r){return(e=Sc(t,e,n))<n&&e++,e}function Ec(t,e,n,r){let s=-1,i=n;for(;i<r;){const n=t.charCodeAt(i++);if(n==e&&92!==s)return i;s=92==n&&92===s?0:n}throw new Error}function Oc(t,e,n){return Ac(t,e,n,!1),Oc}function Tc(t,e){return Ac(t,e,null,!0),Tc}function xc(t){Pc(Lc,kc,t,!1)}function kc(t,e){for(let n=function(t){return wc(t),vc(t,Sc(t,0,gc.textEnd))}(e);n>=0;n=vc(e,n))Lc(t,yc(e),bc(e))}function jc(t){Pc(Dn,Ic,t,!0)}function Ic(t,e){for(let n=function(t){return wc(t),_c(t,Sc(t,0,gc.textEnd))}(e);n>=0;n=_c(e,n))Dn(t,yc(e),!0)}function Ac(t,e,n,r){const s=Wt(),i=Kt(),o=ue(2);i.firstUpdatePass&&Dc(i,t,o,r),e!==wi&&ra(s,o,e)&&Mc(i,i.data[Oe()],s,s[11],t,s[o+1]=function(t,e){return null==t||("string"==typeof e?t+=e:"object"==typeof t&&(t=d(Tr(t)))),t}(e,n),r,o)}function Pc(t,e,n,r){const s=Kt(),i=ue(2);s.firstUpdatePass&&Dc(s,null,i,r);const o=Wt();if(n!==wi&&ra(o,i,n)){const a=s.data[Oe()];if($c(a,r)&&!Rc(s,i)){let t=r?a.classesWithoutHost:a.stylesWithoutHost;null!==t&&(n=f(t,n||"")),Ma(s,a,o,n,r)}else!function(t,e,n,r,s,i,o,a){s===wi&&(s=q);let c=0,l=0,u=0<s.length?s[0]:null,h=0<i.length?i[0]:null;for(;null!==u||null!==h;){const d=c<s.length?s[c+1]:void 0,f=l<i.length?i[l+1]:void 0;let p,m=null;u===h?(c+=2,l+=2,d!==f&&(m=h,p=f)):null===h||null!==u&&u<h?(c+=2,m=u):(l+=2,m=h,p=f),null!==m&&Mc(t,e,n,r,m,p,o,a),u=c<s.length?s[c]:null,h=l<i.length?i[l]:null}}(s,a,o,o[11],o[i+1],o[i+1]=function(t,e,n){if(null==n||""===n)return q;const r=[],s=Tr(n);if(Array.isArray(s))for(let i=0;i<s.length;i++)t(r,s[i],!0);else if("object"==typeof s)for(const i in s)s.hasOwnProperty(i)&&t(r,i,s[i]);else"string"==typeof s&&e(r,s);return r}(t,e,n),r,i)}}function Rc(t,e){return e>=t.expandoStartIndex}function Dc(t,e,n,r){const s=t.data;if(null===s[n+1]){const i=s[Oe()],o=Rc(t,n);$c(i,r)&&null===e&&!o&&(e=!1),e=function(t,e,n,r){const s=pe(t);let i=r?e.residualClasses:e.residualStyles;if(null===s)0===(r?e.classBindings:e.styleBindings)&&(n=Fc(n=Nc(null,t,e,n,r),e.attrs,r),i=null);else{const o=e.directiveStylingLast;if(-1===o||t[o]!==s)if(n=Nc(s,t,e,n,r),null===i){let n=function(t,e,n){const r=n?e.classBindings:e.styleBindings;if(0!==xi(r))return t[Oi(r)]}(t,e,r);void 0!==n&&Array.isArray(n)&&(n=Nc(null,t,e,n[1],r),n=Fc(n,e.attrs,r),function(t,e,n,r){t[Oi(n?e.classBindings:e.styleBindings)]=r}(t,e,r,n))}else i=function(t,e,n){let r;const s=e.directiveEnd;for(let i=1+e.directiveStylingLast;i<s;i++)r=Fc(r,t[i].hostAttrs,n);return Fc(r,e.attrs,n)}(t,e,r)}return void 0!==i&&(r?e.residualClasses=i:e.residualStyles=i),n}(s,i,e,r),function(t,e,n,r,s,i){let o=i?e.classBindings:e.styleBindings,a=Oi(o),c=xi(o);t[r]=n;let l,u=!1;if(Array.isArray(n)){const t=n;l=t[1],(null===l||Fn(t,l)>0)&&(u=!0)}else l=n;if(s)if(0!==c){const e=Oi(t[a+1]);t[r+1]=Ei(e,a),0!==e&&(t[e+1]=ki(t[e+1],r)),t[a+1]=131071&t[a+1]|r<<17}else t[r+1]=Ei(a,0),0!==a&&(t[a+1]=ki(t[a+1],r)),a=r;else t[r+1]=Ei(c,0),0===a?a=r:t[c+1]=ki(t[c+1],r),c=r;u&&(t[r+1]=Ti(t[r+1])),pc(t,l,r,!0),pc(t,l,r,!1),function(t,e,n,r,s){const i=s?t.residualClasses:t.residualStyles;null!=i&&"string"==typeof e&&Fn(i,e)>=0&&(n[r+1]=ji(n[r+1]))}(e,l,t,r,i),o=Ei(a,c),i?e.classBindings=o:e.styleBindings=o}(s,i,e,n,o,r)}}function Nc(t,e,n,r,s){let i=null;const o=n.directiveEnd;let a=n.directiveStylingLast;for(-1===a?a=n.directiveStart:a++;a<o&&(i=e[a],r=Fc(r,i.hostAttrs,s),i!==t);)a++;return null!==t&&(n.directiveStylingLast=a),r}function Fc(t,e,n){const r=n?1:2;let s=-1;if(null!==e)for(let i=0;i<e.length;i++){const o=e[i];"number"==typeof o?s=o:s===r&&(Array.isArray(t)||(t=void 0===t?[]:["",t]),Dn(t,o,!!n||e[++i]))}return void 0===t?null:t}function Lc(t,e,n){Dn(t,e,Tr(n))}function Mc(t,e,n,r,s,i,o,a){if(!(3&e.type))return;const c=t.data,l=c[a+1];Hc(1==(1&l)?Uc(c,e,n,s,xi(l),o):void 0)||(Hc(i)||2==(2&l)&&(i=Uc(c,null,n,s,a,o)),function(t,e,n,r,s){const i=At(t);if(e)s?i?t.addClass(n,r):n.classList.add(r):i?t.removeClass(n,r):n.classList.remove(r);else{let e=-1===r.indexOf("-")?void 0:xs.DashCase;if(null==s)i?t.removeStyle(n,r,e):n.style.removeProperty(r);else{const o="string"==typeof s&&s.endsWith("!important");o&&(s=s.slice(0,-10),e|=xs.Important),i?t.setStyle(n,r,s,e):n.style.setProperty(r,s,o?"important":"")}}}(r,o,Dt(Oe(),n),s,i))}function Uc(t,e,n,r,s,i){const o=null===e;let a;for(;s>0;){const e=t[s],i=Array.isArray(e),c=i?e[1]:e,l=null===c;let u=n[s+1];u===wi&&(u=l?q:void 0);let h=l?Nn(u,r):c===r?u:void 0;if(i&&!Hc(h)&&(h=Nn(e,r)),Hc(h)&&(a=h,o))return a;const d=t[s+1];s=o?Oi(d):xi(d)}if(null!==e){let t=i?e.residualClasses:e.residualStyles;null!=t&&(a=Nn(t,r))}return a}function Hc(t){return void 0!==t}function $c(t,e){return 0!=(t.flags&(e?16:32))}function Vc(t,e=""){const n=Wt(),r=Kt(),s=t+20,i=r.firstCreatePass?Pi(r,s,1,e,null):r.data[s],o=n[s]=Ns(n[11],e);ti(r,n,o,i),ee(i,!1)}function zc(t){return Bc("",t,""),zc}function Bc(t,e,n){const r=Wt(),s=la(r,t,e,n);return s!==wi&&wo(r,Oe(),s),Bc}function qc(t,e,n,r,s){const i=Wt(),o=ua(i,t,e,n,r,s);return o!==wi&&wo(i,Oe(),o),qc}function Qc(t,e,n,r,s,i,o){const a=Wt(),c=ha(a,t,e,n,r,s,i,o);return c!==wi&&wo(a,Oe(),c),Qc}function Gc(t,e,n,r,s,i,o,a,c){const l=Wt(),u=da(l,t,e,n,r,s,i,o,a,c);return u!==wi&&wo(l,Oe(),u),Gc}function Zc(t,e,n,r,s,i,o,a,c,l,u){const h=Wt(),d=fa(h,t,e,n,r,s,i,o,a,c,l,u);return d!==wi&&wo(h,Oe(),d),Zc}function Wc(t,e,n,r,s,i,o,a,c,l,u,h,d){const f=Wt(),p=pa(f,t,e,n,r,s,i,o,a,c,l,u,h,d);return p!==wi&&wo(f,Oe(),p),Wc}function Kc(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p){const m=Wt(),g=ma(m,t,e,n,r,s,i,o,a,c,l,u,h,d,f,p);return g!==wi&&wo(m,Oe(),g),Kc}function Jc(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g){const y=Wt(),b=ga(y,t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g);return b!==wi&&wo(y,Oe(),b),Jc}function Yc(t){const e=Wt(),n=ca(e,t);return n!==wi&&wo(e,Oe(),n),Yc}function Xc(t,e,n){Pc(Dn,Ic,la(Wt(),t,e,n),!0)}function tl(t,e,n,r,s){Pc(Dn,Ic,ua(Wt(),t,e,n,r,s),!0)}function el(t,e,n,r,s,i,o){Pc(Dn,Ic,ha(Wt(),t,e,n,r,s,i,o),!0)}function nl(t,e,n,r,s,i,o,a,c){Pc(Dn,Ic,da(Wt(),t,e,n,r,s,i,o,a,c),!0)}function rl(t,e,n,r,s,i,o,a,c,l,u){Pc(Dn,Ic,fa(Wt(),t,e,n,r,s,i,o,a,c,l,u),!0)}function sl(t,e,n,r,s,i,o,a,c,l,u,h,d){Pc(Dn,Ic,pa(Wt(),t,e,n,r,s,i,o,a,c,l,u,h,d),!0)}function il(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p){Pc(Dn,Ic,ma(Wt(),t,e,n,r,s,i,o,a,c,l,u,h,d,f,p),!0)}function ol(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g){Pc(Dn,Ic,ga(Wt(),t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g),!0)}function al(t){Pc(Dn,Ic,ca(Wt(),t),!0)}function cl(t,e,n){xc(la(Wt(),t,e,n))}function ll(t,e,n,r,s){xc(ua(Wt(),t,e,n,r,s))}function ul(t,e,n,r,s,i,o){xc(ha(Wt(),t,e,n,r,s,i,o))}function hl(t,e,n,r,s,i,o,a,c){xc(da(Wt(),t,e,n,r,s,i,o,a,c))}function dl(t,e,n,r,s,i,o,a,c,l,u){xc(fa(Wt(),t,e,n,r,s,i,o,a,c,l,u))}function fl(t,e,n,r,s,i,o,a,c,l,u,h,d){xc(pa(Wt(),t,e,n,r,s,i,o,a,c,l,u,h,d))}function pl(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p){xc(ma(Wt(),t,e,n,r,s,i,o,a,c,l,u,h,d,f,p))}function ml(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g){xc(ga(Wt(),t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g))}function gl(t){xc(ca(Wt(),t))}function yl(t,e,n,r,s){return Ac(t,la(Wt(),e,n,r),s,!1),yl}function bl(t,e,n,r,s,i,o){return Ac(t,ua(Wt(),e,n,r,s,i),o,!1),bl}function _l(t,e,n,r,s,i,o,a,c){return Ac(t,ha(Wt(),e,n,r,s,i,o,a),c,!1),_l}function vl(t,e,n,r,s,i,o,a,c,l,u){return Ac(t,da(Wt(),e,n,r,s,i,o,a,c,l),u,!1),vl}function wl(t,e,n,r,s,i,o,a,c,l,u,h,d){return Ac(t,fa(Wt(),e,n,r,s,i,o,a,c,l,u,h),d,!1),wl}function Sl(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p){return Ac(t,pa(Wt(),e,n,r,s,i,o,a,c,l,u,h,d,f),p,!1),Sl}function Cl(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g){return Ac(t,ma(Wt(),e,n,r,s,i,o,a,c,l,u,h,d,f,p,m),g,!1),Cl}function El(t,e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y,b){return Ac(t,ga(Wt(),e,n,r,s,i,o,a,c,l,u,h,d,f,p,m,g,y),b,!1),El}function Ol(t,e,n){return Ac(t,ca(Wt(),e),n,!1),Ol}function Tl(t,e,n){const r=Wt();return ra(r,le(),e)&&Qi(Kt(),xe(),r,t,e,r[11],n,!0),Tl}function xl(t,e,n){const r=Wt();if(ra(r,le(),e)){const s=Kt(),i=xe();Qi(s,i,r,t,e,bo(pe(s.data),i,r),n,!0)}return xl}const kl=void 0;var jl=["en",[["a","p"],["AM","PM"],kl],[["AM","PM"],kl,kl],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],kl,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],kl,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",kl,"{1} 'at' {0}",kl],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function(t){let e=Math.floor(Math.abs(t)),n=t.toString().replace(/^[^.]*\.?/,"").length;return 1===e&&0===n?1:5}];let Il={};function Al(t,e,n){"string"!=typeof e&&(n=e,e=t[Fl.LocaleId]),e=e.toLowerCase().replace(/_/g,"-"),Il[e]=t,n&&(Il[e][Fl.ExtraData]=n)}function Pl(t){const e=function(t){return t.toLowerCase().replace(/_/g,"-")}(t);let n=Nl(e);if(n)return n;const r=e.split("-")[0];if(n=Nl(r),n)return n;if("en"===r)return jl;throw new Error(`Missing locale data for the locale "${t}".`)}function Rl(t){return Pl(t)[Fl.CurrencyCode]||null}function Dl(t){return Pl(t)[Fl.PluralCase]}function Nl(t){return t in Il||(Il[t]=z.ng&&z.ng.common&&z.ng.common.locales&&z.ng.common.locales[t]),Il[t]}var Fl=function(t){return t[t.LocaleId=0]="LocaleId",t[t.DayPeriodsFormat=1]="DayPeriodsFormat",t[t.DayPeriodsStandalone=2]="DayPeriodsStandalone",t[t.DaysFormat=3]="DaysFormat",t[t.DaysStandalone=4]="DaysStandalone",t[t.MonthsFormat=5]="MonthsFormat",t[t.MonthsStandalone=6]="MonthsStandalone",t[t.Eras=7]="Eras",t[t.FirstDayOfWeek=8]="FirstDayOfWeek",t[t.WeekendRange=9]="WeekendRange",t[t.DateFormat=10]="DateFormat",t[t.TimeFormat=11]="TimeFormat",t[t.DateTimeFormat=12]="DateTimeFormat",t[t.NumberSymbols=13]="NumberSymbols",t[t.NumberFormats=14]="NumberFormats",t[t.CurrencyCode=15]="CurrencyCode",t[t.CurrencySymbol=16]="CurrencySymbol",t[t.CurrencyName=17]="CurrencyName",t[t.Currencies=18]="Currencies",t[t.Directionality=19]="Directionality",t[t.PluralCase=20]="PluralCase",t[t.ExtraData=21]="ExtraData",t}({});const Ll=["zero","one","two","few","many"],Ml={marker:"element"},Ul={marker:"ICU"};var Hl=function(t){return t[t.SHIFT=2]="SHIFT",t[t.APPEND_EAGERLY=1]="APPEND_EAGERLY",t[t.COMMENT=2]="COMMENT",t}({});let $l="en-US";function Vl(t){var e;null==(e=t)&&S("Expected localeId to be defined",e,null,"!="),"string"==typeof t&&($l=t.toLowerCase().replace(/_/g,"-"))}function zl(t,e,n){const r=e.insertBeforeIndex,s=Array.isArray(r)?r[0]:r;return null===s?Ks(t,0,n):Rt(n[s])}function Bl(t,e,n,r,s){const i=e.insertBeforeIndex;if(Array.isArray(i)){let o=r,a=null;if(3&e.type||(a=o,o=s),null!==o&&0==(2&e.flags))for(let e=1;e<i.length;e++)qs(t,o,n[i[e]],a,!1)}}function ql(t,e){if(t.push(e),t.length>1)for(let n=t.length-2;n>=0;n--){const r=t[n];Ql(r)||Gl(r,e)&&null===Zl(r)&&Wl(r,e.index)}}function Ql(t){return!(64&t.type)}function Gl(t,e){return Ql(e)||t.index>e.index}function Zl(t){const e=t.insertBeforeIndex;return Array.isArray(e)?e[0]:e}function Wl(t,e){const n=t.insertBeforeIndex;Array.isArray(n)?n[0]=e:(Xs(zl,Bl),t.insertBeforeIndex=e)}function Kl(t,e){const n=t.data[e];return null===n||"string"==typeof n?null:n.hasOwnProperty("currentCaseLViewIndex")?n:n.value}function Jl(t,e,n){const r=Ri(t,n,64,null,null);return ql(e,r),r}function Yl(t,e){const n=e[t.currentCaseLViewIndex];return null===n?n:n<0?~n:n}let Xl=0,tu=0;function eu(t,e,n,r){const s=n[11];let i,o=null;for(let a=0;a<e.length;a++){const c=e[a];if("string"==typeof c){const t=e[++a];null===n[t]&&(n[t]=Ns(s,c))}else if("number"==typeof c)switch(1&c){case 0:const l=c>>>17;let u,h;if(null===o&&(o=l,i=Zs(s,r)),l===o?(u=r,h=i):(u=null,h=Rt(n[l])),null!==h){const e=(131070&c)>>>1;qs(s,h,n[e],u,!1);const r=Kl(t,e);if(null!==r&&"object"==typeof r){const e=Yl(r,n);null!==e&&eu(t,r.create[e],n,n[r.anchorIdx])}}break;case 1:const d=e[++a],f=e[++a];no(s,Dt(c>>>1,n),null,null,d,f,null);break;default:throw new Error(`Unable to determine the type of mutate operation for "${c}"`)}else switch(c){case Ul:const t=e[++a],r=e[++a];null===n[r]&&ws(n[r]=Ls(s,t),n);break;case Ml:const i=e[++a],o=e[++a];null===n[o]&&ws(n[o]=Ms(s,i,null),n)}}}function nu(t,e,n,r,s){for(let i=0;i<n.length;i++){const o=n[i],a=n[++i];if(o&s){let s="";for(let o=i+1;o<=i+a;o++){const i=n[o];if("string"==typeof i)s+=i;else if("number"==typeof i)if(i<0)s+=_(e[r-i]);else{const a=i>>>2;switch(3&i){case 1:const i=n[++o],c=n[++o],l=t.data[a];"string"==typeof l?no(e[11],e[a],null,l,i,s,c):Qi(t,l,e,i,s,e[11],c,!1);break;case 0:const u=e[a];null!==u&&Fs(e[11],u,s);break;case 2:su(t,Kl(t,a),e,s);break;case 3:ru(t,Kl(t,a),r,e)}}}}else{const s=n[i+1];if(s>0&&3==(3&s)){const n=Kl(t,s>>>2);e[n.currentCaseLViewIndex]<0&&ru(t,n,r,e)}}i+=a}}function ru(t,e,n,r){let s=r[e.currentCaseLViewIndex];if(null!==s){let i=Xl;s<0&&(s=r[e.currentCaseLViewIndex]=~s,i=-1),nu(t,r,e.update[s],n,i)}}function su(t,e,n,r){const s=function(t,e){let n=t.cases.indexOf(e);if(-1===n)switch(t.type){case 1:{const r=function(t,e){const n=Dl(e)(parseInt(t,10)),r=Ll[n];return void 0!==r?r:"other"}(e,$l);n=t.cases.indexOf(r),-1===n&&"other"!==r&&(n=t.cases.indexOf("other"));break}case 0:n=t.cases.indexOf("other")}return-1===n?null:n}(e,r);if(Yl(e,n)!==s&&(iu(t,e,n),n[e.currentCaseLViewIndex]=null===s?null:~s,null!==s)){const r=n[e.anchorIdx];r&&eu(t,e.create[s],n,r)}}function iu(t,e,n){let r=Yl(e,n);if(null!==r){const s=e.remove[r];for(let e=0;e<s.length;e++){const r=s[e];if(r>0){const t=Dt(r,n);null!==t&&si(n[11],t)}else iu(t,Kl(t,~r),n)}}}const ou=/\ufffd(\d+):?\d*\ufffd/gi,au=/({\s*\ufffd\d+:?\d*\ufffd\s*,\s*\S{6}\s*,[\s\S]*})/gi,cu=/\ufffd(\d+)\ufffd/,lu=/^\s*(\ufffd\d+:?\d*\ufffd)\s*,\s*(select|plural)\s*,/,uu=/\ufffd\/?\*(\d+:\d+)\ufffd/gi,hu=/\ufffd(\/?[#*]\d+):?\d*\ufffd/gi,du=/\uE500/g;function fu(t,e,n,r,s,i,o){const a=Di(t,r,1,null);let c=a<<Hl.SHIFT,l=te();e===l&&(l=null),null===l&&(c|=Hl.APPEND_EAGERLY),o&&(c|=Hl.COMMENT,void 0===ks&&(ks=function(){const t=[];let e,n,r=-1;function s(t,e){r=0;const s=Yl(t,e);n=null!==s?t.remove[s]:Q}function i(){if(r<n.length){const o=n[r++];return o>0?e[o]:(t.push(r,n),s(e[1].data[~o],e),i())}return 0===t.length?null:(n=t.pop(),r=t.pop(),i())}return function(n,r){for(e=r;t.length;)t.pop();return s(n.value,r),i}}())),s.push(c,null===i?"":i);const u=Ri(t,a,o?32:1,null===i?"":i,null);ql(n,u);const h=u.index;return ee(u,!1),null!==l&&e!==l&&function(t,e){let n=t.insertBeforeIndex;var r;null===n?(Xs(zl,Bl),n=t.insertBeforeIndex=[null,e]):("Expecting array here",(r=Array.isArray(n))!=!0&&S("Expecting array here",r,true,"=="),n.push(e))}(l,h),u}function pu(t,e,n,r,s,i,o){const a=o.match(ou),c=fu(t,e,n,i,r,a?null:o,!1);a&&mu(s,o,c.index)}function mu(t,e,n,r,s=null){const i=t.length,o=i+1;t.push(null,null);const a=i+2,c=e.split(ou);let l=0;for(let u=0;u<c.length;u++){const e=c[u];if(1&u){const n=parseInt(e,10);t.push(-1-n),l|=gu(n)}else""!==e&&t.push(e)}return t.push(n<<2|(r?1:0)),r&&t.push(r,s),t[i]=l,t[o]=t.length-a,l}function gu(t){return 1<<Math.min(t,31)}function yu(t){let e,n,r="",s=0,i=!1;for(;null!==(e=uu.exec(t));)i?e[0]===`\ufffd/*${n}\ufffd`&&(s=e.index,i=!1):(r+=t.substring(s,e.index+e[0].length),n=e[1],i=!0);return r+=t.substr(s),r}function bu(t,e,n,r,s,i){let o=0;const a={type:s.type,currentCaseLViewIndex:Di(t,e,1,null),anchorIdx:i,cases:[],create:[],remove:[],update:[]};!function(t,e,n){t.push(gu(e.mainBinding),2,-1-e.mainBinding,n<<2|2)}(n,s,i),function(t,e,n){const r=t.data[e];null===r?t.data[e]=n:r.value=n}(t,i,a);const c=s.values;for(let l=0;l<c.length;l++){const i=c[l],u=[];for(let t=0;t<i.length;t++){const e=i[t];if("string"!=typeof e){const n=u.push(e)-1;i[t]=`\x3c!--\ufffd${n}\ufffd--\x3e`}}o=wu(t,a,e,n,r,s.cases[l],i.join(""),u)|o}o&&function(t,e,n){t.push(e,1,n<<2|3)}(n,o,i)}function _u(t){const e=[],n=[];let r=1,s=0;const i=vu(t=t.replace(lu,function(t,e,n){return r="select"===n?0:1,s=parseInt(e.substr(1),10),""}));for(let o=0;o<i.length;){let t=i[o++].trim();1===r&&(t=t.replace(/\s*(?:=)?(\w+)\s*/,"$1")),t.length&&e.push(t);const s=vu(i[o++]);e.length>n.length&&n.push(s)}return{type:r,mainBinding:s,cases:e,values:n}}function vu(t){if(!t)return[];let e=0;const n=[],r=[],s=/[{}]/g;let i;for(s.lastIndex=0;i=s.exec(t);){const s=i.index;if("}"==i[0]){if(n.pop(),0==n.length){const n=t.substring(e,s);lu.test(n)?r.push(_u(n)):r.push(n),e=s+1}}else{if(0==n.length){const n=t.substring(e,s);r.push(n),e=s+1}n.push("{")}}const o=t.substring(e);return r.push(o),r}function wu(t,e,n,r,s,i,o,a){const c=[],l=[],u=[];e.cases.push(i),e.create.push(c),e.remove.push(l),e.update.push(u);const h=Dr(It()).getInertBodyElement(o),d=ss(h)||h;return d?Su(t,e,n,r,c,l,u,d,s,a,0):0}function Su(t,e,n,r,s,i,o,a,c,l,u){let h=0,d=a.firstChild;for(;d;){const a=Di(t,n,1,null);switch(d.nodeType){case Node.ELEMENT_NODE:const f=d,p=f.tagName.toLowerCase();if(Gr.hasOwnProperty(p)){Ou(s,Ml,p,c,a),t.data[a]=p;const m=f.attributes;for(let t=0;t<m.length;t++){const e=m.item(t),n=e.name.toLowerCase();e.value.match(ou)?Kr.hasOwnProperty(n)&&(Zr[n]?mu(o,e.value,a,e.name,Ur):Wr[n]?mu(o,e.value,a,e.name,Hr):mu(o,e.value,a,e.name)):Tu(s,a,e)}h=Su(t,e,n,r,s,i,o,d,a,l,u+1)|h,Cu(i,a,u)}break;case Node.TEXT_NODE:const m=d.textContent||"",g=m.match(ou);Ou(s,null,g?"":m,c,a),Cu(i,a,u),g&&(h=mu(o,m,a)|h);break;case Node.COMMENT_NODE:const y=cu.exec(d.textContent||"");if(y){const e=l[parseInt(y[1],10)];Ou(s,Ul,"",c,a),bu(t,n,r,c,e,a),Eu(i,a,u)}}d=d.nextSibling}return h}function Cu(t,e,n){0===n&&t.push(e)}function Eu(t,e,n){0===n&&(t.push(~e),t.push(e))}function Ou(t,e,n,r,s){null!==e&&t.push(e),t.push(n,s,0|r<<17|s<<1)}function Tu(t,e,n){t.push(e<<1|1,n.name,n.value)}const xu=/\[(\ufffd.+?\ufffd?)\]/,ku=/\[(\ufffd.+?\ufffd?)\]|(\ufffd\/?\*\d+:\d+\ufffd)/g,ju=/({\s*)(VAR_(PLURAL|SELECT)(_\d+)?)(\s*,)/g,Iu=/{([A-Z0-9_]+)}/g,Au=/\ufffdI18N_EXP_(ICU(_\d+)?)\ufffd/g,Pu=/\/\*/,Ru=/\d+\:(\d+)/;function Du(t,e,n=-1){const r=Kt(),s=Wt(),i=20+t,o=Vt(r.consts,e),a=te();r.firstCreatePass&&function(t,e,n,r,s,i){const o=te(),a=[],c=[],l=[[]],u=(h=s=function(t,e){if(function(t){return-1===t}(e))return yu(t);{const n=t.indexOf(`:${e}\ufffd`)+2+e.toString().length,r=t.search(new RegExp(`\ufffd\\/\\*\\d+:${e}\ufffd`));return yu(t.substring(n,r))}}(s,i),h.replace(du," ")).split(hu);var h;for(let d=0;d<u.length;d++){let r=u[d];if(0==(1&d)){const i=vu(r);for(let r=0;r<i.length;r++){let u=i[r];if(0==(1&r)){const e=u;""!==e&&pu(t,o,l[0],a,c,n,e)}else{const r=u;if("object"!=typeof r)throw new Error(`Unable to parse ICU expression in "${s}" message.`);bu(t,n,c,e,r,fu(t,o,l[0],n,a,"",!0).index)}}}else{const e=47===r.charCodeAt(0),n=(r.charCodeAt(e?1:0),20+Number.parseInt(r.substring(e?2:1)));if(e)l.shift(),ee(te(),!1);else{const e=Jl(t,l[0],n);l.unshift([]),ee(e,!0)}}}t.data[r]={create:a,update:c}}(r,null===a?0:a.index,s,i,o,n);const c=r.data[i],l=Bs(r,a===s[6]?null:a,s);!function(t,e,n,r){const s=t[11];for(let i=0;i<e.length;i++){const o=e[i++],a=e[i],c=(o&Hl.COMMENT)===Hl.COMMENT,l=(o&Hl.APPEND_EAGERLY)===Hl.APPEND_EAGERLY,u=o>>>Hl.SHIFT;let h=t[u];null===h&&(h=t[u]=c?s.createComment(a):Ns(s,a)),l&&null!==n&&qs(s,n,h,r,!1)}}(s,c.create,l,a&&8&a.type?s[a.index]:null),he(!0)}function Nu(){he(!1)}function Fu(t,e,n){Du(t,e,n),Nu()}function Lu(t,e){const n=Kt();!function(t,e,n){const r=Yt().index,s=[];if(t.firstCreatePass&&null===t.data[e]){for(let t=0;t<n.length;t+=2){const e=n[t],i=n[t+1];if(""!==i){if(au.test(i))throw new Error(`ICU expressions are not supported in attributes. Message: "${i}".`);mu(s,i,r,e)}}t.data[e]=s}}(n,t+20,Vt(n.consts,e))}function Mu(t){return ra(Wt(),le(),t)&&(Xl|=1<<Math.min(tu,31)),tu++,Mu}function Uu(t){!function(t,e,n){if(tu>0){const r=t.data[n];nu(t,e,Array.isArray(r)?r:r.update,ae()-tu-1,Xl)}Xl=0,tu=0}(Kt(),Wt(),t+20)}function Hu(t,e={}){return function(t,e={}){let n=t;if(xu.test(t)){const t={},e=[0];n=n.replace(ku,(n,r,s)=>{const i=r||s,o=t[i]||[];if(o.length||(i.split("|").forEach(t=>{const e=t.match(Ru),n=e?parseInt(e[1],10):0,r=Pu.test(t);o.push([n,r,t])}),t[i]=o),!o.length)throw new Error(`i18n postprocess: unmatched placeholder - ${i}`);const a=e[e.length-1];let c=0;for(let t=0;t<o.length;t++)if(o[t][0]===a){c=t;break}const[l,u,h]=o[c];return u?e.pop():a!==l&&e.push(l),o.splice(c,1),h})}return Object.keys(e).length?(n=n.replace(ju,(t,n,r,s,i,o)=>e.hasOwnProperty(r)?`${n}${e[r]}${o}`:t),n=n.replace(Iu,(t,n)=>e.hasOwnProperty(n)?e[n]:t),n=n.replace(Au,(t,n)=>{if(e.hasOwnProperty(n)){const r=e[n];if(!r.length)throw new Error(`i18n postprocess: unmatched ICU - ${t} with key: ${n}`);return r.shift()}return t}),n):n}(t,e)}function $u(t,e,n,r,s){if(t=g(t),Array.isArray(t))for(let i=0;i<t.length;i++)$u(t[i],e,n,r,s);else{const i=Kt(),o=Wt();let a=Lo(t)?t:g(t.provide),c=Do(t);const l=Yt(),u=1048575&l.providerIndexes,h=l.directiveStart,d=l.providerIndexes>>20;if(Lo(t)||!t.multi){const r=new Le(c,s,Na),f=Bu(a,e,s?u:u+d,h);-1===f?(Xe(We(l,o),i,a),Vu(i,t,e.length),e.push(a),l.directiveStart++,l.directiveEnd++,s&&(l.providerIndexes+=1048576),n.push(r),o.push(r)):(n[f]=r,o[f]=r)}else{const f=Bu(a,e,u+d,h),p=Bu(a,e,u,u+d),m=f>=0&&n[f],g=p>=0&&n[p];if(s&&!g||!s&&!m){Xe(We(l,o),i,a);const u=function(t,e,n,r,s){const i=new Le(t,n,Na);return i.multi=[],i.index=e,i.componentProviders=0,zu(i,s,r&&!n),i}(s?Qu:qu,n.length,s,r,c);!s&&g&&(n[p].providerFactory=u),Vu(i,t,e.length,0),e.push(a),l.directiveStart++,l.directiveEnd++,s&&(l.providerIndexes+=1048576),n.push(u),o.push(u)}else Vu(i,t,f>-1?f:p,zu(n[s?p:f],c,!s&&r));!s&&r&&g&&n[p].componentProviders++}}}function Vu(t,e,n,r){const s=Lo(e);if(s||e.useClass){const i=(e.useClass||e).prototype.ngOnDestroy;if(i){const o=t.destroyHooks||(t.destroyHooks=[]);if(!s&&e.multi){const t=o.indexOf(n);-1===t?o.push(n,[r,i]):o[t+1].push(r,i)}else o.push(n,i)}}}function zu(t,e,n){return n&&t.componentProviders++,t.multi.push(e)-1}function Bu(t,e,n,r){for(let s=n;s<r;s++)if(e[s]===t)return s;return-1}function qu(t,e,n,r){return Gu(this.multi,[])}function Qu(t,e,n,r){const s=this.multi;let i;if(this.providerFactory){const t=this.providerFactory.componentProviders,e=cn(n,n[1],this.providerFactory.index,r);i=e.slice(0,t),Gu(s,i);for(let n=t;n<e.length;n++)i.push(e[n])}else i=[],Gu(s,i);return i}function Gu(t,e){for(let n=0;n<t.length;n++)e.push((0,t[n])());return e}function Zu(t,e=[]){return n=>{n.providersResolver=(n,r)=>function(t,e,n){const r=Kt();if(r.firstCreatePass){const s=vt(t);$u(n,r.data,r.blueprint,s,!0),$u(e,r.data,r.blueprint,s,!1)}}(n,r?r(t):t,e)}}class Wu{}class Ku{resolveComponentFactory(t){throw function(t){const e=Error(`No component factory found for ${d(t)}. Did you add it to @NgModule.entryComponents?`);return e.ngComponent=t,e}(t)}}let Ju=(()=>{class t{}return t.NULL=new Ku,t})();function Yu(...t){}function Xu(t,e){return new eh(Nt(t,e))}const th=function(){return Xu(Yt(),Wt())};let eh=(()=>{class t{constructor(t){this.nativeElement=t}}return t.__NG_ELEMENT_ID__=th,t})();function nh(t){return t instanceof eh?t.nativeElement:t}class rh{}let sh=(()=>{class t{}return t.__NG_ELEMENT_ID__=()=>ih(),t})();const ih=function(){const t=Wt(),e=Mt(Yt().index,t);return function(t){return t[11]}(mt(e)?e:t)};let oh=(()=>{class t{}return t.\u0275prov=C({token:t,providedIn:"root",factory:()=>null}),t})();class ah{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const ch=new ah("11.2.6");class lh{constructor(){}supports(t){return Xo(t)}create(t){return new hh(t)}}const uh=(t,e)=>e;class hh{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||uh}forEachItem(t){let e;for(e=this._itHead;null!==e;e=e._next)t(e)}forEachOperation(t){let e=this._itHead,n=this._removalsHead,r=0,s=null;for(;e||n;){const i=!n||e&&e.currentIndex<mh(n,r,s)?e:n,o=mh(i,r,s),a=i.currentIndex;if(i===n)r--,n=n._nextRemoved;else if(e=e._next,null==i.previousIndex)r++;else{s||(s=[]);const t=o-r,e=a-r;if(t!=e){for(let n=0;n<t;n++){const r=n<s.length?s[n]:s[n]=0,i=r+n;e<=i&&i<t&&(s[n]=r+1)}s[i.previousIndex]=e-t}}o!==a&&t(i,o,a)}}forEachPreviousItem(t){let e;for(e=this._previousItHead;null!==e;e=e._nextPrevious)t(e)}forEachAddedItem(t){let e;for(e=this._additionsHead;null!==e;e=e._nextAdded)t(e)}forEachMovedItem(t){let e;for(e=this._movesHead;null!==e;e=e._nextMoved)t(e)}forEachRemovedItem(t){let e;for(e=this._removalsHead;null!==e;e=e._nextRemoved)t(e)}forEachIdentityChange(t){let e;for(e=this._identityChangesHead;null!==e;e=e._nextIdentityChange)t(e)}diff(t){if(null==t&&(t=[]),!Xo(t))throw new Error(`Error trying to diff '${d(t)}'. Only arrays and iterables are allowed`);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let e,n,r,s=this._itHead,i=!1;if(Array.isArray(t)){this.length=t.length;for(let e=0;e<this.length;e++)n=t[e],r=this._trackByFn(e,n),null!==s&&Object.is(s.trackById,r)?(i&&(s=this._verifyReinsertion(s,n,r,e)),Object.is(s.item,n)||this._addIdentityChange(s,n)):(s=this._mismatch(s,n,r,e),i=!0),s=s._next}else e=0,function(t,e){if(Array.isArray(t))for(let n=0;n<t.length;n++)e(t[n]);else{const n=t[Jo()]();let r;for(;!(r=n.next()).done;)e(r.value)}}(t,t=>{r=this._trackByFn(e,t),null!==s&&Object.is(s.trackById,r)?(i&&(s=this._verifyReinsertion(s,t,r,e)),Object.is(s.item,t)||this._addIdentityChange(s,t)):(s=this._mismatch(s,t,r,e),i=!0),s=s._next,e++}),this.length=e;return this._truncate(s),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,e,n,r){let s;return null===t?s=this._itTail:(s=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(n,null))?(Object.is(t.item,e)||this._addIdentityChange(t,e),this._reinsertAfter(t,s,r)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(n,r))?(Object.is(t.item,e)||this._addIdentityChange(t,e),this._moveAfter(t,s,r)):t=this._addAfter(new dh(e,n),s,r),t}_verifyReinsertion(t,e,n,r){let s=null===this._unlinkedRecords?null:this._unlinkedRecords.get(n,null);return null!==s?t=this._reinsertAfter(s,t._prev,r):t.currentIndex!=r&&(t.currentIndex=r,this._addToMoves(t,r)),t}_truncate(t){for(;null!==t;){const e=t._next;this._addToRemovals(this._unlink(t)),t=e}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,e,n){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const r=t._prevRemoved,s=t._nextRemoved;return null===r?this._removalsHead=s:r._nextRemoved=s,null===s?this._removalsTail=r:s._prevRemoved=r,this._insertAfter(t,e,n),this._addToMoves(t,n),t}_moveAfter(t,e,n){return this._unlink(t),this._insertAfter(t,e,n),this._addToMoves(t,n),t}_addAfter(t,e,n){return this._insertAfter(t,e,n),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,e,n){const r=null===e?this._itHead:e._next;return t._next=r,t._prev=e,null===r?this._itTail=t:r._prev=t,null===e?this._itHead=t:e._next=t,null===this._linkedRecords&&(this._linkedRecords=new ph),this._linkedRecords.put(t),t.currentIndex=n,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const e=t._prev,n=t._next;return null===e?this._itHead=n:e._next=n,null===n?this._itTail=e:n._prev=e,t}_addToMoves(t,e){return t.previousIndex===e||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new ph),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,e){return t.item=e,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class dh{constructor(t,e){this.item=t,this.trackById=e,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class fh{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,e){let n;for(n=this._head;null!==n;n=n._nextDup)if((null===e||e<=n.currentIndex)&&Object.is(n.trackById,t))return n;return null}remove(t){const e=t._prevDup,n=t._nextDup;return null===e?this._head=n:e._nextDup=n,null===n?this._tail=e:n._prevDup=e,null===this._head}}class ph{constructor(){this.map=new Map}put(t){const e=t.trackById;let n=this.map.get(e);n||(n=new fh,this.map.set(e,n)),n.add(t)}get(t,e){const n=this.map.get(t);return n?n.get(t,e):null}remove(t){const e=t.trackById;return this.map.get(e).remove(t)&&this.map.delete(e),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function mh(t,e,n){const r=t.previousIndex;if(null===r)return r;let s=0;return n&&r<n.length&&(s=n[r]),r+e+s}class gh{constructor(){}supports(t){return t instanceof Map||ta(t)}create(){return new yh}}class yh{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let e;for(e=this._mapHead;null!==e;e=e._next)t(e)}forEachPreviousItem(t){let e;for(e=this._previousMapHead;null!==e;e=e._nextPrevious)t(e)}forEachChangedItem(t){let e;for(e=this._changesHead;null!==e;e=e._nextChanged)t(e)}forEachAddedItem(t){let e;for(e=this._additionsHead;null!==e;e=e._nextAdded)t(e)}forEachRemovedItem(t){let e;for(e=this._removalsHead;null!==e;e=e._nextRemoved)t(e)}diff(t){if(t){if(!(t instanceof Map||ta(t)))throw new Error(`Error trying to diff '${d(t)}'. Only maps and objects are allowed`)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let e=this._mapHead;if(this._appendAfter=null,this._forEach(t,(t,n)=>{if(e&&e.key===n)this._maybeAddToChanges(e,t),this._appendAfter=e,e=e._next;else{const r=this._getOrCreateRecordForKey(n,t);e=this._insertBeforeOrAppend(e,r)}}),e){e._prev&&(e._prev._next=null),this._removalsHead=e;for(let t=e;null!==t;t=t._nextRemoved)t===this._mapHead&&(this._mapHead=null),this._records.delete(t.key),t._nextRemoved=t._next,t.previousValue=t.currentValue,t.currentValue=null,t._prev=null,t._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,e){if(t){const n=t._prev;return e._next=t,e._prev=n,t._prev=e,n&&(n._next=e),t===this._mapHead&&(this._mapHead=e),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=e,e._prev=this._appendAfter):this._mapHead=e,this._appendAfter=e,null}_getOrCreateRecordForKey(t,e){if(this._records.has(t)){const n=this._records.get(t);this._maybeAddToChanges(n,e);const r=n._prev,s=n._next;return r&&(r._next=s),s&&(s._prev=r),n._next=null,n._prev=null,n}const n=new bh(t);return this._records.set(t,n),n.currentValue=e,this._addToAdditions(n),n}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,e){Object.is(e,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=e,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,e){t instanceof Map?t.forEach(e):Object.keys(t).forEach(n=>e(t[n],n))}}class bh{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function _h(){return new vh([new lh])}let vh=(()=>{class t{constructor(t){this.factories=t}static create(e,n){if(null!=n){const t=n.factories.slice();e=e.concat(t)}return new t(e)}static extend(e){return{provide:t,useFactory:n=>t.create(e,n||_h()),deps:[[t,new rr,new er]]}}find(t){const e=this.factories.find(e=>e.supports(t));if(null!=e)return e;throw new Error(`Cannot find a differ supporting object '${t}' of type '${n=t,n.name||typeof n}'`);var n}}return t.\u0275prov=C({token:t,providedIn:"root",factory:_h}),t})();function wh(){return new Sh([new gh])}let Sh=(()=>{class t{constructor(t){this.factories=t}static create(e,n){if(n){const t=n.factories.slice();e=e.concat(t)}return new t(e)}static extend(e){return{provide:t,useFactory:n=>t.create(e,n||wh()),deps:[[t,new rr,new er]]}}find(t){const e=this.factories.find(e=>e.supports(t));if(e)return e;throw new Error(`Cannot find a differ supporting object '${t}'`)}}return t.\u0275prov=C({token:t,providedIn:"root",factory:wh}),t})();function Ch(t,e,n,r,s=!1){for(;null!==n;){const i=e[n.index];if(null!==i&&r.push(Rt(i)),gt(i))for(let t=10;t<i.length;t++){const e=i[t],n=e[1].firstChild;null!==n&&Ch(e[1],e,n,r)}const o=n.type;if(8&o)Ch(t,e,n.child,r);else if(32&o){const t=js(n,e);let s;for(;s=t();)r.push(s)}else if(16&o){const t=ni(e,n);if(Array.isArray(t))r.push(...t);else{const n=Is(e[16]);Ch(n[1],n,t,r,!0)}}n=s?n.projectionNext:n.next}return r}class Eh{constructor(t,e){this._lView=t,this._cdRefInjectingView=e,this._appRef=null,this._attachedToViewContainer=!1}get rootNodes(){const t=this._lView,e=t[1];return Ch(e,t,e.firstChild,[])}get context(){return this._lView[8]}get destroyed(){return 256==(256&this._lView[2])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[3];if(gt(t)){const e=t[8],n=e?e.indexOf(this):-1;n>-1&&(Hs(t,n),Pn(e,n))}this._attachedToViewContainer=!1}$s(this._lView[1],this._lView)}onDestroy(t){Bi(this._lView[1],this._lView,null,t)}markForCheck(){uo(this._cdRefInjectingView||this._lView)}detach(){this._lView[2]&=-129}reattach(){this._lView[2]|=128}detectChanges(){ho(this._lView[1],this._lView,this.context)}checkNoChanges(){!function(t,e,n){ie(!0);try{ho(t,e,n)}finally{ie(!1)}}(this._lView[1],this._lView,this.context)}attachToViewContainerRef(){if(this._appRef)throw new Error("This view is already attached directly to the ApplicationRef!");this._attachedToViewContainer=!0}detachFromAppRef(){var t;this._appRef=null,oi(this._lView[1],t=this._lView,t[11],2,null,null)}attachToAppRef(t){if(this._attachedToViewContainer)throw new Error("This view is already attached to a ViewContainer!");this._appRef=t}}class Oh extends Eh{constructor(t){super(t),this._view=t}detectChanges(){fo(this._view)}checkNoChanges(){!function(t){ie(!0);try{fo(t)}finally{ie(!1)}}(this._view)}get context(){return null}}const Th=kh;let xh=(()=>{class t{}return t.__NG_ELEMENT_ID__=Th,t.__ChangeDetectorRef__=!0,t})();function kh(t=!1){return function(t,e,n){if(!n&&bt(t)){const n=Mt(t.index,e);return new Eh(n,n)}return 47&t.type?new Eh(e[16],e):null}(Yt(),Wt(),t)}const jh=[new gh],Ih=new vh([new lh]),Ah=new Sh(jh),Ph=function(){return Fh(Yt(),Wt())};let Rh=(()=>{class t{}return t.__NG_ELEMENT_ID__=Ph,t})();const Dh=Rh,Nh=class extends Dh{constructor(t,e,n){super(),this._declarationLView=t,this._declarationTContainer=e,this.elementRef=n}createEmbeddedView(t){const e=this._declarationTContainer.tViews,n=Ai(this._declarationLView,e,t,16,null,e.declTNode,null,null,null,null);n[17]=this._declarationLView[this._declarationTContainer.index];const r=this._declarationLView[19];return null!==r&&(n[19]=r.createEmbeddedView(e)),Ni(e,n,t),new Eh(n)}};function Fh(t,e){return 4&t.type?new Nh(e,t,Xu(t,e)):null}class Lh{}class Mh{}const Uh=function(){return qh(Yt(),Wt())};let Hh=(()=>{class t{}return t.__NG_ELEMENT_ID__=Uh,t})();const $h=Hh,Vh=class extends $h{constructor(t,e,n){super(),this._lContainer=t,this._hostTNode=e,this._hostLView=n}get element(){return Xu(this._hostTNode,this._hostLView)}get injector(){return new hn(this._hostTNode,this._hostLView)}get parentInjector(){const t=Ye(this._hostTNode,this._hostLView);if(ze(t)){const e=qe(t,this._hostLView),n=Be(t);return new hn(e[1].data[n+8],e)}return new hn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const e=zh(this._lContainer);return null!==e&&e[t]||null}get length(){return this._lContainer.length-10}createEmbeddedView(t,e,n){const r=t.createEmbeddedView(e||{});return this.insert(r,n),r}createComponent(t,e,n,r,s){const i=n||this.parentInjector;if(!s&&null==t.ngModule&&i){const t=i.get(Lh,null);t&&(s=t)}const o=t.create(i,r,void 0,s);return this.insert(o.hostView,e),o}insert(t,e){const n=t._lView,r=n[1];if(gt(n[3])){const e=this.indexOf(t);if(-1!==e)this.detach(e);else{const e=n[3],r=new Vh(e,e[6],e[3]);r.detach(r.indexOf(t))}}const s=this._adjustIndex(e),i=this._lContainer;!function(t,e,n,r){const s=10+r,i=n.length;r>0&&(n[s-1][4]=e),r<i-10?(e[4]=n[s],An(n,10+r,e)):(n.push(e),e[4]=null),e[3]=n;const o=e[17];null!==o&&n!==o&&function(t,e){const n=t[9];e[16]!==e[3][3][16]&&(t[2]=!0),null===n?t[9]=[e]:n.push(e)}(o,e);const a=e[19];null!==a&&a.insertView(t),e[2]|=128}(r,n,i,s);const o=ri(s,i),a=n[11],c=Zs(a,i[7]);return null!==c&&function(t,e,n,r,s,i){r[0]=s,r[6]=e,oi(t,r,n,1,s,i)}(r,i[6],a,n,c,o),t.attachToViewContainerRef(),An(Bh(i),s,t),t}move(t,e){return this.insert(t,e)}indexOf(t){const e=zh(this._lContainer);return null!==e?e.indexOf(t):-1}remove(t){const e=this._adjustIndex(t,-1),n=Hs(this._lContainer,e);n&&(Pn(Bh(this._lContainer),e),$s(n[1],n))}detach(t){const e=this._adjustIndex(t,-1),n=Hs(this._lContainer,e);return n&&null!=Pn(Bh(this._lContainer),e)?new Eh(n):null}_adjustIndex(t,e=0){return null==t?this.length+e:t}};function zh(t){return t[8]}function Bh(t){return t[8]||(t[8]=[])}function qh(t,e){let n;const r=e[t.index];if(gt(r))n=r;else{let s;if(8&t.type)s=Rt(r);else{const n=e[11];s=n.createComment("");const r=Nt(t,e);qs(n,Zs(n,r),s,function(t,e){return At(t)?t.nextSibling(e):e.nextSibling}(n,r),!1)}e[t.index]=n=io(r,e,s,t),lo(e,n)}return new Vh(n,t,e)}const Qh={};class Gh extends Ju{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const e=ht(t);return new Kh(e,this.ngModule)}}function Zh(t){const e=[];for(let n in t)t.hasOwnProperty(n)&&e.push({propName:t[n],templateName:n});return e}const Wh=new Cn("SCHEDULER_TOKEN",{providedIn:"root",factory:()=>Ss});class Kh extends Wu{constructor(t,e){super(),this.componentDef=t,this.ngModule=e,this.componentType=t.type,this.selector=t.selectors.map(vi).join(","),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!e}get inputs(){return Zh(this.componentDef.inputs)}get outputs(){return Zh(this.componentDef.outputs)}create(t,e,n,r){const s=(r=r||this.ngModule)?function(t,e){return{get:(n,r,s)=>{const i=t.get(n,Qh,s);return i!==Qh||r===Qh?i:e.get(n,r,s)}}}(t,r.injector):t,i=s.get(rh,Pt),o=s.get(oh,null),a=i.createRenderer(null,this.componentDef),c=this.componentDef.selectors[0][0]||"div",l=n?function(t,e,n){if(At(t))return t.selectRootElement(e,n===M.ShadowDom);let r="string"==typeof e?t.querySelector(e):e;return r.textContent="",r}(a,n,this.componentDef.encapsulation):Ms(i.createRenderer(null,this.componentDef),c,function(t){const e=t.toLowerCase();return"svg"===e?"http://www.w3.org/2000/svg":"math"===e?"http://www.w3.org/1998/MathML/":null}(c)),u=this.componentDef.onPush?576:528,h={components:[],scheduler:Ss,clean:mo,playerHandler:null,flags:0},d=zi(0,null,null,1,0,null,null,null,null,null),f=Ai(null,d,h,u,null,null,i,a,o,s);let p,m;_e(f);try{const t=function(t,e,n,r,s,i){const o=n[1];n[20]=t;const a=Pi(o,20,2,"#host",null),c=a.mergedAttrs=e.hostAttrs;null!==c&&(So(a,c,!0),null!==t&&(Me(s,t,c),null!==a.classes&&li(s,t,a.classes),null!==a.styles&&ci(s,t,a.styles)));const l=r.createRenderer(t,e),u=Ai(n,Vi(e),null,e.onPush?64:16,n[20],a,r,l,null,null);return o.firstCreatePass&&(Xe(We(a,n),o,e.type),Ki(o,a),Yi(a,n.length,1)),lo(n,u),n[20]=u}(l,this.componentDef,f,i,a);if(l)if(n)Me(a,l,["ng-version",ch.full]);else{const{attrs:t,classes:e}=function(t){const e=[],n=[];let r=1,s=2;for(;r<t.length;){let i=t[r];if("string"==typeof i)2===s?""!==i&&e.push(i,t[++r]):8===s&&n.push(i);else{if(!mi(s))break;s=i}r++}return{attrs:e,classes:n}}(this.componentDef.selectors[0]);t&&Me(a,l,t),e&&e.length>0&&li(a,l,e.join(" "))}if(m=Ft(d,20),void 0!==e){const t=m.projection=[];for(let n=0;n<this.ngContentSelectors.length;n++){const r=e[n];t.push(null!=r?Array.from(r):null)}}p=function(t,e,n,r,s){const i=n[1],o=function(t,e,n){const r=Yt();t.firstCreatePass&&(n.providersResolver&&n.providersResolver(n),Xi(t,r,e,Di(t,e,1,null),n));const s=cn(e,t,r.directiveStart,r);ws(s,e);const i=Nt(r,e);return i&&ws(i,e),s}(i,n,e);if(r.components.push(o),t[8]=o,s&&s.forEach(t=>t(o,e)),e.contentQueries){const t=Yt();e.contentQueries(1,o,t.directiveStart)}const a=Yt();return!i.firstCreatePass||null===e.hostBindings&&null===e.hostAttrs||(Te(a.index),Zi(n[1],a,0,a.directiveStart,a.directiveEnd,e),Wi(e,o)),o}(t,this.componentDef,f,h,[Ho]),Ni(d,f,null)}finally{Ee()}return new Jh(this.componentType,p,Xu(m,f),f,m)}}class Jh extends class{}{constructor(t,e,n,r,s){super(),this.location=n,this._rootLView=r,this._tNode=s,this.instance=e,this.hostView=this.changeDetectorRef=new Oh(r),this.componentType=t}get injector(){return new hn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function Yh(t,e,n,r){return F(()=>{const s=t;null!==e&&(s.hasOwnProperty("decorators")&&void 0!==s.decorators?s.decorators.push(...e):s.decorators=e),null!==n&&(s.ctorParameters=n),null!==r&&(s.propDecorators=s.hasOwnProperty("propDecorators")&&void 0!==s.propDecorators?Object.assign(Object.assign({},s.propDecorators),r):r)})}const Xh=new Map;class td extends Lh{constructor(t,e){super(),this._parent=e,this._bootstrapComponents=[],this.injector=this,this.destroyCbs=[],this.componentFactoryResolver=new Gh(this);const n=pt(t),r=t[J]||null;r&&Vl(r),this._bootstrapComponents=Ts(n.bootstrap),this._r3Injector=Ao(t,e,[{provide:Lh,useValue:this},{provide:Ju,useValue:this.componentFactoryResolver}],d(t)),this._r3Injector._resolveInjectorDefTypes(),this.instance=this.get(t)}get(t,e=Uo.THROW_IF_NOT_FOUND,n=P.Default){return t===Uo||t===Lh||t===Co?this:this._r3Injector.get(t,e,n)}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class ed extends Mh{constructor(t){super(),this.moduleType=t,null!==pt(t)&&function(t){const e=new Set;!function t(n){const r=pt(n,!0),s=r.id;null!==s&&(function(t,e,n){if(e&&e!==n)throw new Error(`Duplicate module registered for ${t} - ${d(e)} vs ${d(e.name)}`)}(s,Xh.get(s),n),Xh.set(s,n));const i=Ts(r.imports);for(const o of i)e.has(o)||(e.add(o),t(o))}(t)}(t)}create(t){return new td(this.moduleType,t)}}function nd(t,e,n){const r=oe()+t,s=Wt();return s[r]===wi?ea(s,r,n?e.call(n):e()):na(s,r)}function rd(t,e,n,r){return fd(Wt(),oe(),t,e,n,r)}function sd(t,e,n,r,s){return pd(Wt(),oe(),t,e,n,r,s)}function id(t,e,n,r,s,i){return md(Wt(),oe(),t,e,n,r,s,i)}function od(t,e,n,r,s,i,o){return gd(Wt(),oe(),t,e,n,r,s,i,o)}function ad(t,e,n,r,s,i,o,a){const c=oe()+t,l=Wt(),u=oa(l,c,n,r,s,i);return ra(l,c+4,o)||u?ea(l,c+5,a?e.call(a,n,r,s,i,o):e(n,r,s,i,o)):na(l,c+5)}function cd(t,e,n,r,s,i,o,a,c){const l=oe()+t,u=Wt(),h=oa(u,l,n,r,s,i);return sa(u,l+4,o,a)||h?ea(u,l+6,c?e.call(c,n,r,s,i,o,a):e(n,r,s,i,o,a)):na(u,l+6)}function ld(t,e,n,r,s,i,o,a,c,l){const u=oe()+t,h=Wt();let d=oa(h,u,n,r,s,i);return ia(h,u+4,o,a,c)||d?ea(h,u+7,l?e.call(l,n,r,s,i,o,a,c):e(n,r,s,i,o,a,c)):na(h,u+7)}function ud(t,e,n,r,s,i,o,a,c,l,u){const h=oe()+t,d=Wt(),f=oa(d,h,n,r,s,i);return oa(d,h+4,o,a,c,l)||f?ea(d,h+8,u?e.call(u,n,r,s,i,o,a,c,l):e(n,r,s,i,o,a,c,l)):na(d,h+8)}function hd(t,e,n,r){return yd(Wt(),oe(),t,e,n,r)}function dd(t,e){const n=t[e];return n===wi?void 0:n}function fd(t,e,n,r,s,i){const o=e+n;return ra(t,o,s)?ea(t,o+1,i?r.call(i,s):r(s)):dd(t,o+1)}function pd(t,e,n,r,s,i,o){const a=e+n;return sa(t,a,s,i)?ea(t,a+2,o?r.call(o,s,i):r(s,i)):dd(t,a+2)}function md(t,e,n,r,s,i,o,a){const c=e+n;return ia(t,c,s,i,o)?ea(t,c+3,a?r.call(a,s,i,o):r(s,i,o)):dd(t,c+3)}function gd(t,e,n,r,s,i,o,a,c){const l=e+n;return oa(t,l,s,i,o,a)?ea(t,l+4,c?r.call(c,s,i,o,a):r(s,i,o,a)):dd(t,l+4)}function yd(t,e,n,r,s,i){let o=e+n,a=!1;for(let c=0;c<s.length;c++)ra(t,o++,s[c])&&(a=!0);return a?ea(t,o,r.apply(i,s)):dd(t,o)}function bd(t,e){const n=Kt();let r;const s=t+20;n.firstCreatePass?(r=function(t,e){if(e)for(let n=e.length-1;n>=0;n--){const r=e[n];if(t===r.name)return r}throw new b("302",`The pipe '${t}' could not be found!`)}(e,n.pipeRegistry),n.data[s]=r,r.onDestroy&&(n.destroyHooks||(n.destroyHooks=[])).push(s,r.onDestroy)):r=n.data[s];const i=r.factory||(r.factory=wt(r.type)),o=D(Na);try{const t=Ge(!1),e=i();return Ge(t),function(t,e,n,r){n>=t.data.length&&(t.data[n]=null,t.blueprint[n]=null),e[n]=r}(n,Wt(),s,e),e}finally{D(o)}}function _d(t,e,n){const r=t+20,s=Wt(),i=Lt(s,r);return Od(s,Ed(s,r)?fd(s,oe(),e,i.transform,n,i):i.transform(n))}function vd(t,e,n,r){const s=t+20,i=Wt(),o=Lt(i,s);return Od(i,Ed(i,s)?pd(i,oe(),e,o.transform,n,r,o):o.transform(n,r))}function wd(t,e,n,r,s){const i=t+20,o=Wt(),a=Lt(o,i);return Od(o,Ed(o,i)?md(o,oe(),e,a.transform,n,r,s,a):a.transform(n,r,s))}function Sd(t,e,n,r,s,i){const o=t+20,a=Wt(),c=Lt(a,o);return Od(a,Ed(a,o)?gd(a,oe(),e,c.transform,n,r,s,i,c):c.transform(n,r,s,i))}function Cd(t,e,n){const r=t+20,s=Wt(),i=Lt(s,r);return Od(s,Ed(s,r)?yd(s,oe(),e,i.transform,n,i):i.transform.apply(i,n))}function Ed(t,e){return t[1].data[e].pure}function Od(t,e){return Yo.isWrapped(e)&&(e=Yo.unwrap(e),t[ae()]=wi),e}const Td=class extends r.a{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,e,n){let r,i=t=>null,o=()=>null;t&&"object"==typeof t?(r=this.__isAsync?e=>{setTimeout(()=>t.next(e))}:e=>{t.next(e)},t.error&&(i=this.__isAsync?e=>{setTimeout(()=>t.error(e))}:e=>{t.error(e)}),t.complete&&(o=this.__isAsync?()=>{setTimeout(()=>t.complete())}:()=>{t.complete()})):(r=this.__isAsync?e=>{setTimeout(()=>t(e))}:e=>{t(e)},e&&(i=this.__isAsync?t=>{setTimeout(()=>e(t))}:t=>{e(t)}),n&&(o=this.__isAsync?()=>{setTimeout(()=>n())}:()=>{n()}));const a=super.subscribe(r,i,o);return t instanceof s.a&&t.add(a),a}};function xd(){return this._results[Jo()]()}class kd{constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const e=Jo(),n=kd.prototype;n[e]||(n[e]=xd)}get changes(){return this._changes||(this._changes=new Td)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,e){return this._results.reduce(t,e)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,e){this.dirty=!1;const n=jn(t);(this._changesDetected=!function(t,e,n){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++){let s=t[r],i=e[r];if(n&&(s=n(s),i=n(i)),i!==s)return!1}return!0}(this._results,n,e))&&(this._results=n,this.length=n.length,this.last=n[this.length-1],this.first=n[0])}notifyOnChanges(){!this._changes||!this._changesDetected&&this._emitDistinctChangesOnly||this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}class jd{constructor(t){this.queryList=t,this.matches=null}clone(){return new jd(this.queryList)}setDirty(){this.queryList.setDirty()}}class Id{constructor(t=[]){this.queries=t}createEmbeddedView(t){const e=t.queries;if(null!==e){const n=null!==t.contentQueries?t.contentQueries[0]:e.length,r=[];for(let t=0;t<n;t++){const n=e.getByIndex(t);r.push(this.queries[n.indexInDeclarationView].clone())}return new Id(r)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let e=0;e<this.queries.length;e++)null!==Bd(t,e).matches&&this.queries[e].setDirty()}}class Ad{constructor(t,e,n=null){this.predicate=t,this.flags=e,this.read=n}}class Pd{constructor(t=[]){this.queries=t}elementStart(t,e){for(let n=0;n<this.queries.length;n++)this.queries[n].elementStart(t,e)}elementEnd(t){for(let e=0;e<this.queries.length;e++)this.queries[e].elementEnd(t)}embeddedTView(t){let e=null;for(let n=0;n<this.length;n++){const r=null!==e?e.length:0,s=this.getByIndex(n).embeddedTView(t,r);s&&(s.indexInDeclarationView=n,null!==e?e.push(s):e=[s])}return null!==e?new Pd(e):null}template(t,e){for(let n=0;n<this.queries.length;n++)this.queries[n].template(t,e)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Rd{constructor(t,e=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=e}elementStart(t,e){this.isApplyingToNode(e)&&this.matchTNode(t,e)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,e){this.elementStart(t,e)}embeddedTView(t,e){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,e),new Rd(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const e=this._declarationNodeIndex;let n=t.parent;for(;null!==n&&8&n.type&&n.index!==e;)n=n.parent;return e===(null!==n?n.index:-1)}return this._appliesToNextNode}matchTNode(t,e){const n=this.metadata.predicate;if(Array.isArray(n))for(let r=0;r<n.length;r++){const s=n[r];this.matchTNodeWithReadOption(t,e,Dd(e,s)),this.matchTNodeWithReadOption(t,e,an(e,t,s,!1,!1))}else n===Rh?4&e.type&&this.matchTNodeWithReadOption(t,e,-1):this.matchTNodeWithReadOption(t,e,an(e,t,n,!1,!1))}matchTNodeWithReadOption(t,e,n){if(null!==n){const r=this.metadata.read;if(null!==r)if(r===eh||r===Hh||r===Rh&&4&e.type)this.addMatch(e.index,-2);else{const n=an(e,t,r,!1,!1);null!==n&&this.addMatch(e.index,n)}else this.addMatch(e.index,n)}}addMatch(t,e){null===this.matches?this.matches=[t,e]:this.matches.push(t,e)}}function Dd(t,e){const n=t.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===e)return n[r+1];return null}function Nd(t,e,n,r){return-1===n?function(t,e){return 11&t.type?Xu(t,e):4&t.type?Fh(t,e):null}(e,t):-2===n?function(t,e,n){return n===eh?Xu(e,t):n===Rh?Fh(e,t):n===Hh?qh(e,t):void 0}(t,e,r):cn(t,t[1],n,e)}function Fd(t,e,n,r){const s=e[19].queries[r];if(null===s.matches){const r=t.data,i=n.matches,o=[];for(let t=0;t<i.length;t+=2){const s=i[t];o.push(s<0?null:Nd(e,r[s],i[t+1],n.metadata.read))}s.matches=o}return s.matches}function Ld(t,e,n,r){const s=t.queries.getByIndex(n),i=s.matches;if(null!==i){const o=Fd(t,e,s,n);for(let t=0;t<i.length;t+=2){const n=i[t];if(n>0)r.push(o[t/2]);else{const s=i[t+1],o=e[-n];for(let t=10;t<o.length;t++){const e=o[t];e[17]===e[3]&&Ld(e[1],e,s,r)}if(null!==o[9]){const t=o[9];for(let e=0;e<t.length;e++){const n=t[e];Ld(n[1],n,s,r)}}}}}return r}function Md(t){const e=Wt(),n=Kt(),r=me();ge(r+1);const s=Bd(n,r);if(t.dirty&&Ht(e)===(2==(2&s.metadata.flags))){if(null===s.matches)t.reset([]);else{const i=s.crossesNgTemplate?Ld(n,e,r,[]):Fd(n,e,s,r);t.reset(i,nh),t.notifyOnChanges()}return!0}return!1}function Ud(t,e,n){const r=Kt();r.firstCreatePass&&(zd(r,new Ad(t,e,n),-1),2==(2&e)&&(r.staticViewQueries=!0)),Vd(r,Wt(),e)}function Hd(t,e,n,r){const s=Kt();if(s.firstCreatePass){const i=Yt();zd(s,new Ad(e,n,r),i.index),function(t,e){const n=t.contentQueries||(t.contentQueries=[]);e!==(n.length?n[n.length-1]:-1)&&n.push(t.queries.length-1,e)}(s,t),2==(2&n)&&(s.staticContentQueries=!0)}Vd(s,Wt(),n)}function $d(){return t=Wt(),e=me(),t[19].queries[e].queryList;var t,e}function Vd(t,e,n){const r=new kd(4==(4&n));Bi(t,e,r,r.destroy),null===e[19]&&(e[19]=new Id),e[19].queries.push(new jd(r))}function zd(t,e,n){null===t.queries&&(t.queries=new Pd),t.queries.track(new Rd(e,n))}function Bd(t,e){return t.queries.getByIndex(e)}function qd(t,e){return Fh(t,e)}function Qd(t=P.Default){const e=kh(!0);if(null!=e||t&P.Optional)return e;w("ChangeDetectorRef")}const Gd=(()=>({"\u0275\u0275attribute":aa,"\u0275\u0275attributeInterpolate1":ya,"\u0275\u0275attributeInterpolate2":ba,"\u0275\u0275attributeInterpolate3":_a,"\u0275\u0275attributeInterpolate4":va,"\u0275\u0275attributeInterpolate5":wa,"\u0275\u0275attributeInterpolate6":Sa,"\u0275\u0275attributeInterpolate7":Ca,"\u0275\u0275attributeInterpolate8":Ea,"\u0275\u0275attributeInterpolateV":Oa,"\u0275\u0275defineComponent":et,"\u0275\u0275defineDirective":lt,"\u0275\u0275defineInjectable":C,"\u0275\u0275defineInjector":E,"\u0275\u0275defineNgModule":ot,"\u0275\u0275definePipe":ut,"\u0275\u0275directiveInject":Na,"\u0275\u0275getInheritedFactory":dn,"\u0275\u0275inject":Kn,"\u0275\u0275injectAttribute":pn,"\u0275\u0275invalidFactory":Fa,"\u0275\u0275invalidFactoryDep":Jn,"\u0275\u0275injectPipeChangeDetectorRef":Qd,"\u0275\u0275templateRefExtractor":qd,"\u0275\u0275NgOnChangesFeature":Ct,"\u0275\u0275ProvidersFeature":Zu,"\u0275\u0275CopyDefinitionFeature":Wo,"\u0275\u0275InheritDefinitionFeature":Vo,"\u0275\u0275nextContext":tc,"\u0275\u0275namespaceHTML":Ie,"\u0275\u0275namespaceMathML":je,"\u0275\u0275namespaceSVG":ke,"\u0275\u0275enableBindings":Gt,"\u0275\u0275disableBindings":Zt,"\u0275\u0275elementStart":Ua,"\u0275\u0275elementEnd":Ha,"\u0275\u0275element":$a,"\u0275\u0275elementContainerStart":Va,"\u0275\u0275elementContainerEnd":za,"\u0275\u0275elementContainer":Ba,"\u0275\u0275pureFunction0":nd,"\u0275\u0275pureFunction1":rd,"\u0275\u0275pureFunction2":sd,"\u0275\u0275pureFunction3":id,"\u0275\u0275pureFunction4":od,"\u0275\u0275pureFunction5":ad,"\u0275\u0275pureFunction6":cd,"\u0275\u0275pureFunction7":ld,"\u0275\u0275pureFunction8":ud,"\u0275\u0275pureFunctionV":hd,"\u0275\u0275getCurrentView":qa,"\u0275\u0275restoreView":Jt,"\u0275\u0275listener":Wa,"\u0275\u0275projection":rc,"\u0275\u0275syntheticHostProperty":xl,"\u0275\u0275syntheticHostListener":Ka,"\u0275\u0275pipeBind1":_d,"\u0275\u0275pipeBind2":vd,"\u0275\u0275pipeBind3":wd,"\u0275\u0275pipeBind4":Sd,"\u0275\u0275pipeBindV":Cd,"\u0275\u0275projectionDef":nc,"\u0275\u0275hostProperty":Tl,"\u0275\u0275property":La,"\u0275\u0275propertyInterpolate":sc,"\u0275\u0275propertyInterpolate1":ic,"\u0275\u0275propertyInterpolate2":oc,"\u0275\u0275propertyInterpolate3":ac,"\u0275\u0275propertyInterpolate4":cc,"\u0275\u0275propertyInterpolate5":lc,"\u0275\u0275propertyInterpolate6":uc,"\u0275\u0275propertyInterpolate7":hc,"\u0275\u0275propertyInterpolate8":dc,"\u0275\u0275propertyInterpolateV":fc,"\u0275\u0275pipe":bd,"\u0275\u0275queryRefresh":Md,"\u0275\u0275viewQuery":Ud,"\u0275\u0275loadQuery":$d,"\u0275\u0275contentQuery":Hd,"\u0275\u0275reference":xa,"\u0275\u0275classMap":jc,"\u0275\u0275classMapInterpolate1":Xc,"\u0275\u0275classMapInterpolate2":tl,"\u0275\u0275classMapInterpolate3":el,"\u0275\u0275classMapInterpolate4":nl,"\u0275\u0275classMapInterpolate5":rl,"\u0275\u0275classMapInterpolate6":sl,"\u0275\u0275classMapInterpolate7":il,"\u0275\u0275classMapInterpolate8":ol,"\u0275\u0275classMapInterpolateV":al,"\u0275\u0275styleMap":xc,"\u0275\u0275styleMapInterpolate1":cl,"\u0275\u0275styleMapInterpolate2":ll,"\u0275\u0275styleMapInterpolate3":ul,"\u0275\u0275styleMapInterpolate4":hl,"\u0275\u0275styleMapInterpolate5":dl,"\u0275\u0275styleMapInterpolate6":fl,"\u0275\u0275styleMapInterpolate7":pl,"\u0275\u0275styleMapInterpolate8":ml,"\u0275\u0275styleMapInterpolateV":gl,"\u0275\u0275styleProp":Oc,"\u0275\u0275stylePropInterpolate1":yl,"\u0275\u0275stylePropInterpolate2":bl,"\u0275\u0275stylePropInterpolate3":_l,"\u0275\u0275stylePropInterpolate4":vl,"\u0275\u0275stylePropInterpolate5":wl,"\u0275\u0275stylePropInterpolate6":Sl,"\u0275\u0275stylePropInterpolate7":Cl,"\u0275\u0275stylePropInterpolate8":El,"\u0275\u0275stylePropInterpolateV":Ol,"\u0275\u0275classProp":Tc,"\u0275\u0275advance":Si,"\u0275\u0275template":Ta,"\u0275\u0275text":Vc,"\u0275\u0275textInterpolate":zc,"\u0275\u0275textInterpolate1":Bc,"\u0275\u0275textInterpolate2":qc,"\u0275\u0275textInterpolate3":Qc,"\u0275\u0275textInterpolate4":Gc,"\u0275\u0275textInterpolate5":Zc,"\u0275\u0275textInterpolate6":Wc,"\u0275\u0275textInterpolate7":Kc,"\u0275\u0275textInterpolate8":Jc,"\u0275\u0275textInterpolateV":Yc,"\u0275\u0275i18n":Fu,"\u0275\u0275i18nAttributes":Lu,"\u0275\u0275i18nExp":Mu,"\u0275\u0275i18nStart":Du,"\u0275\u0275i18nEnd":Nu,"\u0275\u0275i18nApply":Uu,"\u0275\u0275i18nPostprocess":Hu,"\u0275\u0275resolveWindow":Cs,"\u0275\u0275resolveDocument":Es,"\u0275\u0275resolveBody":Os,"\u0275\u0275setComponentScope":nt,"\u0275\u0275setNgModuleScope":at,"\u0275\u0275sanitizeHtml":os,"\u0275\u0275sanitizeStyle":as,"\u0275\u0275sanitizeResourceUrl":ls,"\u0275\u0275sanitizeScript":us,"\u0275\u0275sanitizeUrl":cs,"\u0275\u0275sanitizeUrlOrResourceUrl":fs,"\u0275\u0275trustConstantHtml":hs,"\u0275\u0275trustConstantResourceUrl":ds,forwardRef:m,resolveForwardRef:g}))();const Zd=[],Wd=[];let Kd=!1;function Jd(t){return Array.isArray(t)?t.every(Jd):!!g(t)}function Yd(t,e){const n=jn(e.declarations||Zd),r=tf(t);n.forEach(e=>{e.hasOwnProperty(G)?Xd(ht(e),r):e.hasOwnProperty(Z)||e.hasOwnProperty(W)||(e.ngSelectorScope=t)})}function Xd(t,e){t.directiveDefs=()=>Array.from(e.compilation.directives).map(t=>t.hasOwnProperty(G)?ht(t):dt(t)).filter(t=>!!t),t.pipeDefs=()=>Array.from(e.compilation.pipes).map(t=>ft(t)),t.schemas=e.schemas,t.tView=null}function tf(t){if(!nf(t))throw new Error(`${t.name} does not have a module def (\u0275mod property)`);const e=pt(t);if(null!==e.transitiveCompileScopes)return e.transitiveCompileScopes;const n={schemas:e.schemas||null,compilation:{directives:new Set,pipes:new Set},exported:{directives:new Set,pipes:new Set}};return Ts(e.imports).forEach(t=>{const e=t;if(!nf(e))throw new Error(`Importing ${e.name} which does not have a \u0275mod property`);const r=tf(e);r.exported.directives.forEach(t=>n.compilation.directives.add(t)),r.exported.pipes.forEach(t=>n.compilation.pipes.add(t))}),Ts(e.declarations).forEach(t=>{ft(t)?n.compilation.pipes.add(t):n.compilation.directives.add(t)}),Ts(e.exports).forEach(t=>{const e=t;if(nf(e)){const t=tf(e);t.exported.directives.forEach(t=>{n.compilation.directives.add(t),n.exported.directives.add(t)}),t.exported.pipes.forEach(t=>{n.compilation.pipes.add(t),n.exported.pipes.add(t)})}else ft(e)?n.exported.pipes.add(e):n.exported.directives.add(e)}),e.transitiveCompileScopes=n,n}function ef(t){return function(t){return void 0!==t.ngModule}(t)?t.ngModule:t}function nf(t){return!!pt(t)}let rf=0;function sf(t,e){let n=null;af(t,e||{}),Object.defineProperty(t,Z,{get:()=>{if(null===n){const r=of(t,e||{});n=Tn().compileDirective(Gd,r.sourceMapUrl,r.metadata)}return n},configurable:!1})}function of(t,e){const n=t&&t.name,r=`ng:///${n}/\u0275dir.js`,s=Tn(),i=lf(t,e);return i.typeSourceSpan=s.createParseSourceSpan("Directive",n,r),i.usesInheritance&&uf(t),{metadata:i,sourceMapUrl:r}}function af(t,e){let n=null;Object.defineProperty(t,Y,{get:()=>{if(null===n){const r=of(t,e),s=Tn();n=s.compileFactory(Gd,`ng:///${t.name}/\u0275fac.js`,Object.assign(Object.assign({},r.metadata),{injectFn:"directiveInject",target:s.R3FactoryTarget.Directive}))}return n},configurable:!1})}function cf(t){return Object.getPrototypeOf(t.prototype)===Object.prototype}function lf(t,e){const n=or(),r=n.ownPropMetadata(t);return{name:t.name,type:t,typeArgumentCount:0,selector:void 0!==e.selector?e.selector:null,deps:ar(t),host:e.host||B,propMetadata:r,inputs:e.inputs||Q,outputs:e.outputs||Q,queries:df(t,r,ff),lifecycle:{usesOnChanges:n.hasLifecycleHook(t,"ngOnChanges")},typeSourceSpan:null,usesInheritance:!cf(t),exportAs:(s=e.exportAs,void 0===s?null:gf(s)),providers:e.providers||null,viewQueries:df(t,r,pf)};var s}function uf(t){const e=Object.prototype;let n=Object.getPrototypeOf(t.prototype).constructor;for(;n&&n!==e;)dt(n)||ht(n)||!bf(n)||sf(n,null),n=Object.getPrototypeOf(n)}function hf(t,e){return{propertyName:t,predicate:(n=e.selector,"string"==typeof n?gf(n):g(n)),descendants:e.descendants,first:e.first,read:e.read?e.read:null,static:!!e.static,emitDistinctChangesOnly:!!e.emitDistinctChangesOnly};var n}function df(t,e,n){const r=[];for(const s in e)if(e.hasOwnProperty(s)){const i=e[s];i.forEach(e=>{if(n(e)){if(!e.selector)throw new Error(`Can't construct a query for the property "${s}" of "${v(t)}" since the query selector wasn't defined.`);if(i.some(mf))throw new Error("Cannot combine @Input decorators with query decorators");r.push(hf(s,e))}})}return r}function ff(t){const e=t.ngMetadataName;return"ContentChild"===e||"ContentChildren"===e}function pf(t){const e=t.ngMetadataName;return"ViewChild"===e||"ViewChildren"===e}function mf(t){return"Input"===t.ngMetadataName}function gf(t){return t.split(",").map(t=>t.trim())}const yf=["ngOnChanges","ngOnInit","ngOnDestroy","ngDoCheck","ngAfterViewInit","ngAfterViewChecked","ngAfterContentInit","ngAfterContentChecked"];function bf(t){const e=or();if(yf.some(n=>e.hasLifecycleHook(t,n)))return!0;const n=e.propMetadata(t);for(const r in n){const t=n[r];for(let e=0;e<t.length;e++){const n=t[e],r=n.ngMetadataName;if(mf(n)||ff(n)||pf(n)||"Output"===r||"HostBinding"===r||"HostListener"===r)return!0}}return!1}function _f(t,e){return{type:t,typeArgumentCount:0,name:t.name,deps:ar(t),pipeName:e.name,pure:void 0===e.pure||e.pure}}const vf=bn("Directive",(t={})=>t,void 0,void 0,(t,e)=>Tf(t,e)),wf=bn("Component",(t={})=>Object.assign({changeDetection:L.Default},t),vf,void 0,(t,e)=>Of(t,e)),Sf=bn("Pipe",t=>Object.assign({pure:!0},t),void 0,void 0,(t,e)=>xf(t,e)),Cf=wn("Input",t=>({bindingPropertyName:t})),Ef=wn("Output",t=>({bindingPropertyName:t})),Of=function(t,e){let n=null;!function(t,e){hr(e)&&(lr.set(t,e),ur.add(t))}(t,e),af(t,e),Object.defineProperty(t,G,{get:()=>{if(null===n){const r=Tn();if(hr(e)){const n=[`Component '${t.name}' is not resolved:`];throw e.templateUrl&&n.push(` - templateUrl: ${e.templateUrl}`),e.styleUrls&&e.styleUrls.length&&n.push(` - styleUrls: ${JSON.stringify(e.styleUrls)}`),n.push("Did you run and wait for 'resolveComponentResources()'?"),new Error(n.join("\n"))}const s=null;let i=e.preserveWhitespaces;void 0===i&&(i=null!==s&&void 0!==s.preserveWhitespaces&&s.preserveWhitespaces);let o=e.encapsulation;void 0===o&&(o=null!==s&&void 0!==s.defaultEncapsulation?s.defaultEncapsulation:M.Emulated);const a=e.templateUrl||`ng:///${t.name}/template.html`,c=Object.assign(Object.assign({},lf(t,e)),{typeSourceSpan:r.createParseSourceSpan("Component",t.name,a),template:e.template||"",preserveWhitespaces:i,styles:e.styles||Q,animations:e.animations,directives:[],changeDetection:e.changeDetection,pipes:new Map,encapsulation:o,interpolation:e.interpolation,viewProviders:e.viewProviders||null});rf++;try{c.usesInheritance&&uf(t),n=r.compileComponent(Gd,a,c)}finally{rf--}if(0===rf&&function(){if(!Kd){Kd=!0;try{for(let t=Wd.length-1;t>=0;t--){const{moduleType:e,ngModule:n}=Wd[t];n.declarations&&n.declarations.every(Jd)&&(Wd.splice(t,1),Yd(e,n))}}finally{Kd=!1}}}(),void 0!==t.ngSelectorScope){const e=tf(t.ngSelectorScope);Xd(n,e)}}return n},configurable:!1})},Tf=sf,xf=function(t,e){let n=null,r=null;Object.defineProperty(t,Y,{get:()=>{if(null===r){const n=_f(t,e),s=Tn();r=s.compileFactory(Gd,`ng:///${n.name}/\u0275fac.js`,Object.assign(Object.assign({},n),{injectFn:"directiveInject",target:s.R3FactoryTarget.Pipe}))}return r},configurable:!1}),Object.defineProperty(t,W,{get:()=>{if(null===n){const r=_f(t,e);n=Tn().compilePipe(Gd,`ng:///${r.name}/\u0275pipe.js`,r)}return n},configurable:!1})},kf=bn("NgModule",t=>t,void 0,void 0,(t,e)=>jf(t,e)),jf=function(t,e={}){!function(t,e,n=!1){const r=jn(e.declarations||Zd);let s=null;Object.defineProperty(t,K,{configurable:!0,get:()=>(null===s&&(s=Tn().compileNgModule(Gd,`ng:///${t.name}/\u0275mod.js`,{type:t,bootstrap:jn(e.bootstrap||Zd).map(g),declarations:r.map(g),imports:jn(e.imports||Zd).map(g).map(ef),exports:jn(e.exports||Zd).map(g).map(ef),schemas:e.schemas?jn(e.schemas):null,id:e.id||null}),s.schemas||(s.schemas=[])),s)});let i=null;Object.defineProperty(t,Y,{get:()=>{if(null===i){const e=Tn();i=e.compileFactory(Gd,`ng:///${t.name}/\u0275fac.js`,{name:t.name,type:t,deps:ar(t),injectFn:"inject",target:e.R3FactoryTarget.NgModule,typeArgumentCount:0})}return i},configurable:!1});let o=null;Object.defineProperty(t,j,{get:()=>{if(null===o){const n={name:t.name,type:t,providers:e.providers||Zd,imports:[(e.imports||Zd).map(g),(e.exports||Zd).map(g)]};o=Tn().compileInjector(Gd,`ng:///${t.name}/\u0275inj.js`,n)}return o},configurable:!1})}(t,e),function(t,e){Wd.push({moduleType:t,ngModule:e})}(t,e)},If=new Cn("Application Initializer");let Af=(()=>{class t{constructor(t){this.appInits=t,this.resolve=Yu,this.reject=Yu,this.initialized=!1,this.done=!1,this.donePromise=new Promise((t,e)=>{this.resolve=t,this.reject=e})}runInitializers(){if(this.initialized)return;const t=[],e=()=>{this.done=!0,this.resolve()};if(this.appInits)for(let n=0;n<this.appInits.length;n++){const e=this.appInits[n]();Qa(e)&&t.push(e)}Promise.all(t).then(()=>{e()}).catch(t=>{this.reject(t)}),0===t.length&&e(),this.initialized=!0}}return t.\u0275fac=function(e){return new(e||t)(Kn(If,8))},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();const Pf=new Cn("AppId"),Rf={provide:Pf,useFactory:function(){return`${Df()}${Df()}${Df()}`},deps:[]};function Df(){return String.fromCharCode(97+Math.floor(25*Math.random()))}const Nf=new Cn("Platform Initializer"),Ff=new Cn("Platform ID"),Lf=new Cn("appBootstrapListener");let Mf=(()=>{class t{log(t){console.log(t)}warn(t){console.warn(t)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();const Uf=new Cn("LocaleId"),Hf=new Cn("DefaultCurrencyCode");class $f{constructor(t,e){this.ngModuleFactory=t,this.componentFactories=e}}const Vf=function(t){return new ed(t)},zf=Vf,Bf=function(t){return Promise.resolve(Vf(t))},qf=function(t){const e=Vf(t),n=Ts(pt(t).declarations).reduce((t,e)=>{const n=ht(e);return n&&t.push(new Kh(n)),t},[]);return new $f(e,n)},Qf=qf,Gf=function(t){return Promise.resolve(qf(t))};let Zf=(()=>{class t{constructor(){this.compileModuleSync=zf,this.compileModuleAsync=Bf,this.compileModuleAndAllComponentsSync=Qf,this.compileModuleAndAllComponentsAsync=Gf}clearCache(){}clearCacheFor(t){}getModuleId(t){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();const Wf=(()=>Promise.resolve(0))();function Kf(t){"undefined"==typeof Zone?Wf.then(()=>{t&&t.apply(null,null)}):Zone.current.scheduleMicroTask("scheduleMicrotask",t)}class Jf{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:e=!1,shouldCoalesceRunChangeDetection:n=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Td(!1),this.onMicrotaskEmpty=new Td(!1),this.onStable=new Td(!1),this.onError=new Td(!1),"undefined"==typeof Zone)throw new Error("In this configuration Angular requires Zone.js");Zone.assertZonePatched(),this._nesting=0,this._outer=this._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(this._inner=this._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(this._inner=this._inner.fork(Zone.longStackTraceZoneSpec)),this.shouldCoalesceEventChangeDetection=!n&&e,this.shouldCoalesceRunChangeDetection=n,this.lastRequestAnimationFrameId=-1,this.nativeRequestAnimationFrame=function(){let t=z.requestAnimationFrame,e=z.cancelAnimationFrame;if("undefined"!=typeof Zone&&t&&e){const n=t[Zone.__symbol__("OriginalDelegate")];n&&(t=n);const r=e[Zone.__symbol__("OriginalDelegate")];r&&(e=r)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:e}}().nativeRequestAnimationFrame,function(t){const e=()=>{!function(t){-1===t.lastRequestAnimationFrameId&&(t.lastRequestAnimationFrameId=t.nativeRequestAnimationFrame.call(z,()=>{t.fakeTopEventTask||(t.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{t.lastRequestAnimationFrameId=-1,tp(t),Xf(t)},void 0,()=>{},()=>{})),t.fakeTopEventTask.invoke()}),tp(t))}(t)};t._inner=t._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,s,i,o,a)=>{try{return ep(t),n.invokeTask(s,i,o,a)}finally{(t.shouldCoalesceEventChangeDetection&&"eventTask"===i.type||t.shouldCoalesceRunChangeDetection)&&e(),np(t)}},onInvoke:(n,r,s,i,o,a,c)=>{try{return ep(t),n.invoke(s,i,o,a,c)}finally{t.shouldCoalesceRunChangeDetection&&e(),np(t)}},onHasTask:(e,n,r,s)=>{e.hasTask(r,s),n===r&&("microTask"==s.change?(t._hasPendingMicrotasks=s.microTask,tp(t),Xf(t)):"macroTask"==s.change&&(t.hasPendingMacrotasks=s.macroTask))},onHandleError:(e,n,r,s)=>(e.handleError(r,s),t.runOutsideAngular(()=>t.onError.emit(s)),!1)})}(this)}static isInAngularZone(){return!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!Jf.isInAngularZone())throw new Error("Expected to be in Angular Zone, but it is not!")}static assertNotInAngularZone(){if(Jf.isInAngularZone())throw new Error("Expected to not be in Angular Zone, but it is!")}run(t,e,n){return this._inner.run(t,e,n)}runTask(t,e,n,r){const s=this._inner,i=s.scheduleEventTask("NgZoneEvent: "+r,t,Yf,Yu,Yu);try{return s.runTask(i,e,n)}finally{s.cancelTask(i)}}runGuarded(t,e,n){return this._inner.runGuarded(t,e,n)}runOutsideAngular(t){return this._outer.run(t)}}const Yf={};function Xf(t){if(0==t._nesting&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function tp(t){t.hasPendingMicrotasks=!!(t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&-1!==t.lastRequestAnimationFrameId)}function ep(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function np(t){t._nesting--,Xf(t)}class rp{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Td,this.onMicrotaskEmpty=new Td,this.onStable=new Td,this.onError=new Td}run(t,e,n){return t.apply(e,n)}runGuarded(t,e,n){return t.apply(e,n)}runOutsideAngular(t){return t()}runTask(t,e,n,r){return t.apply(e,n)}}let sp=(()=>{class t{constructor(t){this._ngZone=t,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,this._watchAngularEvents(),t.run(()=>{this.taskTrackingZone="undefined"==typeof Zone?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Jf.assertNotInAngularZone(),Kf(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())Kf(()=>{for(;0!==this._callbacks.length;){let t=this._callbacks.pop();clearTimeout(t.timeoutId),t.doneCb(this._didWork)}this._didWork=!1});else{let t=this.getPendingTasks();this._callbacks=this._callbacks.filter(e=>!e.updateCb||!e.updateCb(t)||(clearTimeout(e.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(t=>({source:t.source,creationLocation:t.creationLocation,data:t.data})):[]}addCallback(t,e,n){let r=-1;e&&e>0&&(r=setTimeout(()=>{this._callbacks=this._callbacks.filter(t=>t.timeoutId!==r),t(this._didWork,this.getPendingTasks())},e)),this._callbacks.push({doneCb:t,timeoutId:r,updateCb:n})}whenStable(t,e,n){if(n&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/dist/task-tracking.js" loaded?');this.addCallback(t,e,n),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}findProviders(t,e,n){return[]}}return t.\u0275fac=function(e){return new(e||t)(Kn(Jf))},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})(),ip=(()=>{class t{constructor(){this._applications=new Map,lp.addToWindow(this)}registerApplication(t,e){this._applications.set(t,e)}unregisterApplication(t){this._applications.delete(t)}unregisterAllApplications(){this._applications.clear()}getTestability(t){return this._applications.get(t)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(t,e=!0){return lp.findTestabilityInTree(this,t,e)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();class op{addToWindow(t){}findTestabilityInTree(t,e,n){return null}}function ap(t){lp=t}let cp,lp=new op,up=!0,hp=!1;function dp(){return hp=!0,up}function fp(){if(hp)throw new Error("Cannot enable prod mode after platform setup.");up=!1}const pp=new Cn("AllowMultipleToken");class mp{constructor(t,e){this.name=t,this.token=e}}function gp(t,e,n=[]){const r=`Platform: ${e}`,s=new Cn(r);return(e=[])=>{let i=yp();if(!i||i.injector.get(pp,!1))if(t)t(n.concat(e).concat({provide:s,useValue:!0}));else{const t=n.concat(e).concat({provide:s,useValue:!0},{provide:Oo,useValue:"platform"});!function(t){if(cp&&!cp.destroyed&&!cp.injector.get(pp,!1))throw new Error("There can be only one platform. Destroy the previous one to create a new one.");cp=t.get(bp);const e=t.get(Nf,null);e&&e.forEach(t=>t())}(Uo.create({providers:t,name:r}))}return function(t){const e=yp();if(!e)throw new Error("No platform exists!");if(!e.injector.get(t,null))throw new Error("A platform with a different configuration has been created. Please destroy it first.");return e}(s)}}function yp(){return cp&&!cp.destroyed?cp:null}let bp=(()=>{class t{constructor(t){this._injector=t,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(t,e){const n=function(t,e){let n;return n="noop"===t?new rp:("zone.js"===t?void 0:t)||new Jf({enableLongStackTrace:dp(),shouldCoalesceEventChangeDetection:!!(null==e?void 0:e.ngZoneEventCoalescing),shouldCoalesceRunChangeDetection:!!(null==e?void 0:e.ngZoneRunCoalescing)}),n}(e?e.ngZone:void 0,{ngZoneEventCoalescing:e&&e.ngZoneEventCoalescing||!1,ngZoneRunCoalescing:e&&e.ngZoneRunCoalescing||!1}),r=[{provide:Jf,useValue:n}];return n.run(()=>{const e=Uo.create({providers:r,parent:this.injector,name:t.moduleType.name}),s=t.create(e),i=s.injector.get(bs,null);if(!i)throw new Error("No ErrorHandler. Is platform module (BrowserModule) included?");return n.runOutsideAngular(()=>{const t=n.onError.subscribe({next:t=>{i.handleError(t)}});s.onDestroy(()=>{wp(this._modules,s),t.unsubscribe()})}),function(t,e,n){try{const r=n();return Qa(r)?r.catch(n=>{throw e.runOutsideAngular(()=>t.handleError(n)),n}):r}catch(r){throw e.runOutsideAngular(()=>t.handleError(r)),r}}(i,n,()=>{const t=s.injector.get(Af);return t.runInitializers(),t.donePromise.then(()=>(Vl(s.injector.get(Uf,"en-US")||"en-US"),this._moduleDoBootstrap(s),s))})})}bootstrapModule(t,e=[]){const n=_p({},e);return function(t,e,n){const r=new ed(n);return Promise.resolve(r)}(0,0,t).then(t=>this.bootstrapModuleFactory(t,n))}_moduleDoBootstrap(t){const e=t.injector.get(vp);if(t._bootstrapComponents.length>0)t._bootstrapComponents.forEach(t=>e.bootstrap(t));else{if(!t.instance.ngDoBootstrap)throw new Error(`The module ${d(t.instance.constructor)} was bootstrapped, but it does not declare "@NgModule.bootstrap" components nor a "ngDoBootstrap" method. Please define one of these.`);t.instance.ngDoBootstrap(e)}this._modules.push(t)}onDestroy(t){this._destroyListeners.push(t)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new Error("The platform has already been destroyed!");this._modules.slice().forEach(t=>t.destroy()),this._destroyListeners.forEach(t=>t()),this._destroyed=!0}get destroyed(){return this._destroyed}}return t.\u0275fac=function(e){return new(e||t)(Kn(Uo))},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();function _p(t,e){return Array.isArray(e)?e.reduce(_p,t):Object.assign(Object.assign({},t),e)}let vp=(()=>{class t{constructor(t,e,n,r,s){this._zone=t,this._injector=e,this._exceptionHandler=n,this._componentFactoryResolver=r,this._initStatus=s,this._bootstrapListeners=[],this._views=[],this._runningTick=!1,this._stable=!0,this.componentTypes=[],this.components=[],this._onMicrotaskEmptySubscription=this._zone.onMicrotaskEmpty.subscribe({next:()=>{this._zone.run(()=>{this.tick()})}});const u=new i.a(t=>{this._stable=this._zone.isStable&&!this._zone.hasPendingMacrotasks&&!this._zone.hasPendingMicrotasks,this._zone.runOutsideAngular(()=>{t.next(this._stable),t.complete()})}),h=new i.a(t=>{let e;this._zone.runOutsideAngular(()=>{e=this._zone.onStable.subscribe(()=>{Jf.assertNotInAngularZone(),Kf(()=>{this._stable||this._zone.hasPendingMacrotasks||this._zone.hasPendingMicrotasks||(this._stable=!0,t.next(!0))})})});const n=this._zone.onUnstable.subscribe(()=>{Jf.assertInAngularZone(),this._stable&&(this._stable=!1,this._zone.runOutsideAngular(()=>{t.next(!1)}))});return()=>{e.unsubscribe(),n.unsubscribe()}});this.isStable=Object(o.a)(u,h.pipe(t=>{return Object(c.a)()((e=l,function(t){let n;n="function"==typeof e?e:function(){return e};const r=Object.create(t,a.b);return r.source=t,r.subjectFactory=n,r})(t));var e}))}bootstrap(t,e){if(!this._initStatus.done)throw new Error("Cannot bootstrap as there are still asynchronous initializers running. Bootstrap components in the `ngDoBootstrap` method of the root module.");let n;n=t instanceof Wu?t:this._componentFactoryResolver.resolveComponentFactory(t),this.componentTypes.push(n.componentType);const r=n.isBoundToModule?void 0:this._injector.get(Lh),s=n.create(Uo.NULL,[],e||n.selector,r),i=s.location.nativeElement,o=s.injector.get(sp,null),a=o&&s.injector.get(ip);return o&&a&&a.registerApplication(i,o),s.onDestroy(()=>{this.detachView(s.hostView),wp(this.components,s),a&&a.unregisterApplication(i)}),this._loadComponent(s),s}tick(){if(this._runningTick)throw new Error("ApplicationRef.tick is called recursively");try{this._runningTick=!0;for(let t of this._views)t.detectChanges()}catch(t){this._zone.runOutsideAngular(()=>this._exceptionHandler.handleError(t))}finally{this._runningTick=!1}}attachView(t){const e=t;this._views.push(e),e.attachToAppRef(this)}detachView(t){const e=t;wp(this._views,e),e.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t),this._injector.get(Lf,[]).concat(this._bootstrapListeners).forEach(e=>e(t))}ngOnDestroy(){this._views.slice().forEach(t=>t.destroy()),this._onMicrotaskEmptySubscription.unsubscribe()}get viewCount(){return this._views.length}}return t.\u0275fac=function(e){return new(e||t)(Kn(Jf),Kn(Uo),Kn(bs),Kn(Ju),Kn(Af))},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();function wp(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class Sp{}class Cp{}const Ep={factoryPathPrefix:"",factoryPathSuffix:".ngfactory"};let Op=(()=>{class t{constructor(t,e){this._compiler=t,this._config=e||Ep}load(t){return this.loadAndCompile(t)}loadAndCompile(t){let[e,r]=t.split("#");return void 0===r&&(r="default"),n("zn8P")(e).then(t=>t[r]).then(t=>Tp(t,e,r)).then(t=>this._compiler.compileModuleAsync(t))}loadFactory(t){let[e,r]=t.split("#"),s="NgFactory";return void 0===r&&(r="default",s=""),n("zn8P")(this._config.factoryPathPrefix+e+this._config.factoryPathSuffix).then(t=>t[r+s]).then(t=>Tp(t,e,r))}}return t.\u0275fac=function(e){return new(e||t)(Kn(Zf),Kn(Cp,8))},t.\u0275prov=C({token:t,factory:t.\u0275fac}),t})();function Tp(t,e,n){if(!t)throw new Error(`Cannot find '${n}' in '${e}'`);return t}const xp=function(t){return null},kp=gp(null,"core",[{provide:Ff,useValue:"unknown"},{provide:bp,deps:[Uo]},{provide:ip,deps:[]},{provide:Mf,deps:[]}]),jp=[{provide:vp,useClass:vp,deps:[Jf,Uo,bs,Ju,Af]},{provide:Wh,deps:[Jf],useFactory:function(t){let e=[];return t.onStable.subscribe(()=>{for(;e.length;)e.pop()()}),function(t){e.push(t)}}},{provide:Af,useClass:Af,deps:[[new er,If]]},{provide:Zf,useClass:Zf,deps:[]},Rf,{provide:vh,useFactory:function(){return Ih},deps:[]},{provide:Sh,useFactory:function(){return Ah},deps:[]},{provide:Uf,useFactory:function(t){return Vl(t=t||"undefined"!=typeof $localize&&$localize.locale||"en-US"),t},deps:[[new tr(Uf),new er,new rr]]},{provide:Hf,useValue:"USD"}];let Ip=(()=>{class t{constructor(t){}}return t.\u0275fac=function(e){return new(e||t)(Kn(vp))},t.\u0275mod=ot({type:t}),t.\u0275inj=E({providers:jp}),t})()},gRHU:function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("2fFW"),s=n("NJ4a");const i={closed:!0,next(t){},error(t){if(r.a.useDeprecatedSynchronousErrorHandling)throw t;Object(s.a)(t)},complete(){}}},jZKg:function(t,e,n){"use strict";n.d(e,"a",function(){return i});var r=n("HDdC"),s=n("quSY");function i(t,e){return new r.a(n=>{const r=new s.a;let i=0;return r.add(e.schedule(function(){i!==t.length?(n.next(t[i++]),n.closed||r.add(this.schedule())):n.complete()})),r})}},jhN1:function(t,e,n){"use strict";n.d(e,"a",function(){return L}),n.d(e,"b",function(){return A}),n.d(e,"c",function(){return N}),n.d(e,"d",function(){return w});var r=n("ofXK"),s=n("fXoL");class i extends r.w{constructor(){super()}supportsDOMEvents(){return!0}}class o extends i{static makeCurrent(){Object(r.A)(new o)}getProperty(t,e){return t[e]}log(t){window.console&&window.console.log&&window.console.log(t)}logGroup(t){window.console&&window.console.group&&window.console.group(t)}logGroupEnd(){window.console&&window.console.groupEnd&&window.console.groupEnd()}onAndCancel(t,e,n){return t.addEventListener(e,n,!1),()=>{t.removeEventListener(e,n,!1)}}dispatchEvent(t,e){t.dispatchEvent(e)}remove(t){return t.parentNode&&t.parentNode.removeChild(t),t}getValue(t){return t.value}createElement(t,e){return(e=e||this.getDefaultDocument()).createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,e){return"window"===e?window:"document"===e?t:"body"===e?t.body:null}getHistory(){return window.history}getLocation(){return window.location}getBaseHref(t){const e=c||(c=document.querySelector("base"),c)?c.getAttribute("href"):null;return null==e?null:(n=e,a||(a=document.createElement("a")),a.setAttribute("href",n),"/"===a.pathname.charAt(0)?a.pathname:"/"+a.pathname);var n}resetBaseElement(){c=null}getUserAgent(){return window.navigator.userAgent}performanceNow(){return window.performance&&window.performance.now?window.performance.now():(new Date).getTime()}supportsCookies(){return!0}getCookie(t){return Object(r.z)(document.cookie,t)}}let a,c=null;const l=new s.v("TRANSITION_ID"),u=[{provide:s.d,useFactory:function(t,e,n){return()=>{n.get(s.e).donePromise.then(()=>{const n=Object(r.y)();Array.prototype.slice.apply(e.querySelectorAll("style[ng-transition]")).filter(e=>e.getAttribute("ng-transition")===t).forEach(t=>n.remove(t))})}},deps:[l,r.d,s.w],multi:!0}];class h{static init(){Object(s.eb)(new h)}addToWindow(t){s.vb.getAngularTestability=(e,n=!0)=>{const r=t.findTestabilityInTree(e,n);if(null==r)throw new Error("Could not find testability for element.");return r},s.vb.getAllAngularTestabilities=()=>t.getAllTestabilities(),s.vb.getAllAngularRootElements=()=>t.getAllRootElements(),s.vb.frameworkStabilizers||(s.vb.frameworkStabilizers=[]),s.vb.frameworkStabilizers.push(t=>{const e=s.vb.getAllAngularTestabilities();let n=e.length,r=!1;const i=function(e){r=r||e,n--,0==n&&t(r)};e.forEach(function(t){t.whenStable(i)})})}findTestabilityInTree(t,e,n){if(null==e)return null;const s=t.getTestability(e);return null!=s?s:n?Object(r.y)().isShadowRoot(e)?this.findTestabilityInTree(t,e.host,!0):this.findTestabilityInTree(t,e.parentElement,!0):null}}const d=new s.v("EventManagerPlugins");let f=(()=>{class t{constructor(t,e){this._zone=e,this._eventNameToPlugin=new Map,t.forEach(t=>t.manager=this),this._plugins=t.slice().reverse()}addEventListener(t,e,n){return this._findPluginFor(e).addEventListener(t,e,n)}addGlobalEventListener(t,e,n){return this._findPluginFor(e).addGlobalEventListener(t,e,n)}getZone(){return this._zone}_findPluginFor(t){const e=this._eventNameToPlugin.get(t);if(e)return e;const n=this._plugins;for(let r=0;r<n.length;r++){const e=n[r];if(e.supports(t))return this._eventNameToPlugin.set(t,e),e}throw new Error(`No event manager plugin found for event ${t}`)}}return t.\u0275fac=function(e){return new(e||t)(s.ec(d),s.ec(s.G))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();class p{constructor(t){this._doc=t}addGlobalEventListener(t,e,n){const s=Object(r.y)().getGlobalEventTarget(this._doc,t);if(!s)throw new Error(`Unsupported event target ${s} for event ${e}`);return this.addEventListener(s,e,n)}}let m=(()=>{class t{constructor(){this._stylesSet=new Set}addStyles(t){const e=new Set;t.forEach(t=>{this._stylesSet.has(t)||(this._stylesSet.add(t),e.add(t))}),this.onStylesAdded(e)}onStylesAdded(t){}getAllStyles(){return Array.from(this._stylesSet)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})(),g=(()=>{class t extends m{constructor(t){super(),this._doc=t,this._hostNodes=new Set,this._styleNodes=new Set,this._hostNodes.add(t.head)}_addStylesToHost(t,e){t.forEach(t=>{const n=this._doc.createElement("style");n.textContent=t,this._styleNodes.add(e.appendChild(n))})}addHost(t){this._addStylesToHost(this._stylesSet,t),this._hostNodes.add(t)}removeHost(t){this._hostNodes.delete(t)}onStylesAdded(t){this._hostNodes.forEach(e=>this._addStylesToHost(t,e))}ngOnDestroy(){this._styleNodes.forEach(t=>Object(r.y)().remove(t))}}return t.\u0275fac=function(e){return new(e||t)(s.ec(r.d))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();const y={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},b=/%COMP%/g;function _(t,e,n){for(let r=0;r<e.length;r++){let s=e[r];Array.isArray(s)?_(t,s,n):(s=s.replace(b,t),n.push(s))}return n}function v(t){return e=>{if("__ngUnwrap__"===e)return t;!1===t(e)&&(e.preventDefault(),e.returnValue=!1)}}let w=(()=>{class t{constructor(t,e,n){this.eventManager=t,this.sharedStylesHost=e,this.appId=n,this.rendererByCompId=new Map,this.defaultRenderer=new S(t)}createRenderer(t,e){if(!t||!e)return this.defaultRenderer;switch(e.encapsulation){case s.Y.Emulated:{let n=this.rendererByCompId.get(e.id);return n||(n=new C(this.eventManager,this.sharedStylesHost,e,this.appId),this.rendererByCompId.set(e.id,n)),n.applyToHost(t),n}case 1:case s.Y.ShadowDom:return new E(this.eventManager,this.sharedStylesHost,t,e);default:if(!this.rendererByCompId.has(e.id)){const t=_(e.id,e.styles,[]);this.sharedStylesHost.addStyles(t),this.rendererByCompId.set(e.id,this.defaultRenderer)}return this.defaultRenderer}}begin(){}end(){}}return t.\u0275fac=function(e){return new(e||t)(s.ec(f),s.ec(g),s.ec(s.c))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();class S{constructor(t){this.eventManager=t,this.data=Object.create(null)}destroy(){}createElement(t,e){return e?document.createElementNS(y[e]||e,t):document.createElement(t)}createComment(t){return document.createComment(t)}createText(t){return document.createTextNode(t)}appendChild(t,e){t.appendChild(e)}insertBefore(t,e,n){t&&t.insertBefore(e,n)}removeChild(t,e){t&&t.removeChild(e)}selectRootElement(t,e){let n="string"==typeof t?document.querySelector(t):t;if(!n)throw new Error(`The selector "${t}" did not match any elements`);return e||(n.textContent=""),n}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,e,n,r){if(r){e=r+":"+e;const s=y[r];s?t.setAttributeNS(s,e,n):t.setAttribute(e,n)}else t.setAttribute(e,n)}removeAttribute(t,e,n){if(n){const r=y[n];r?t.removeAttributeNS(r,e):t.removeAttribute(`${n}:${e}`)}else t.removeAttribute(e)}addClass(t,e){t.classList.add(e)}removeClass(t,e){t.classList.remove(e)}setStyle(t,e,n,r){r&(s.O.DashCase|s.O.Important)?t.style.setProperty(e,n,r&s.O.Important?"important":""):t.style[e]=n}removeStyle(t,e,n){n&s.O.DashCase?t.style.removeProperty(e):t.style[e]=""}setProperty(t,e,n){t[e]=n}setValue(t,e){t.nodeValue=e}listen(t,e,n){return"string"==typeof t?this.eventManager.addGlobalEventListener(t,e,v(n)):this.eventManager.addEventListener(t,e,v(n))}}class C extends S{constructor(t,e,n,r){super(t),this.component=n;const s=_(r+"-"+n.id,n.styles,[]);e.addStyles(s),this.contentAttr="_ngcontent-%COMP%".replace(b,r+"-"+n.id),this.hostAttr="_nghost-%COMP%".replace(b,r+"-"+n.id)}applyToHost(t){super.setAttribute(t,this.hostAttr,"")}createElement(t,e){const n=super.createElement(t,e);return super.setAttribute(n,this.contentAttr,""),n}}class E extends S{constructor(t,e,n,r){super(t),this.sharedStylesHost=e,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const s=_(r.id,r.styles,[]);for(let i=0;i<s.length;i++){const t=document.createElement("style");t.textContent=s[i],this.shadowRoot.appendChild(t)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}appendChild(t,e){return super.appendChild(this.nodeOrShadowRoot(t),e)}insertBefore(t,e,n){return super.insertBefore(this.nodeOrShadowRoot(t),e,n)}removeChild(t,e){return super.removeChild(this.nodeOrShadowRoot(t),e)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}}let O=(()=>{class t extends p{constructor(t){super(t)}supports(t){return!0}addEventListener(t,e,n){return t.addEventListener(e,n,!1),()=>this.removeEventListener(t,e,n)}removeEventListener(t,e,n){return t.removeEventListener(e,n)}}return t.\u0275fac=function(e){return new(e||t)(s.ec(r.d))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();const T=["alt","control","meta","shift"],x={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},k={A:"1",B:"2",C:"3",D:"4",E:"5",F:"6",G:"7",H:"8",I:"9",J:"*",K:"+",M:"-",N:".",O:"/","`":"0","\x90":"NumLock"},j={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey};let I=(()=>{class t extends p{constructor(t){super(t)}supports(e){return null!=t.parseEventName(e)}addEventListener(e,n,s){const i=t.parseEventName(n),o=t.eventCallback(i.fullKey,s,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Object(r.y)().onAndCancel(e,i.domEventName,o))}static parseEventName(e){const n=e.toLowerCase().split("."),r=n.shift();if(0===n.length||"keydown"!==r&&"keyup"!==r)return null;const s=t._normalizeKey(n.pop());let i="";if(T.forEach(t=>{const e=n.indexOf(t);e>-1&&(n.splice(e,1),i+=t+".")}),i+=s,0!=n.length||0===s.length)return null;const o={};return o.domEventName=r,o.fullKey=i,o}static getEventFullKey(t){let e="",n=function(t){let e=t.key;if(null==e){if(e=t.keyIdentifier,null==e)return"Unidentified";e.startsWith("U+")&&(e=String.fromCharCode(parseInt(e.substring(2),16)),3===t.location&&k.hasOwnProperty(e)&&(e=k[e]))}return x[e]||e}(t);return n=n.toLowerCase()," "===n?n="space":"."===n&&(n="dot"),T.forEach(r=>{r!=n&&(0,j[r])(t)&&(e+=r+".")}),e+=n,e}static eventCallback(e,n,r){return s=>{t.getEventFullKey(s)===e&&r.runGuarded(()=>n(s))}}static _normalizeKey(t){switch(t){case"esc":return"escape";default:return t}}}return t.\u0275fac=function(e){return new(e||t)(s.ec(r.d))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})(),A=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=Object(s.Qb)({factory:function(){return Object(s.ec)(R)},token:t,providedIn:"root"}),t})();function P(t){return new R(t.get(r.d))}let R=(()=>{class t extends A{constructor(t){super(),this._doc=t}sanitize(t,e){if(null==e)return null;switch(t){case s.Q.NONE:return e;case s.Q.HTML:return Object(s.kb)(e,"HTML")?Object(s.Eb)(e):Object(s.ib)(this._doc,String(e)).toString();case s.Q.STYLE:return Object(s.kb)(e,"Style")?Object(s.Eb)(e):e;case s.Q.SCRIPT:if(Object(s.kb)(e,"Script"))return Object(s.Eb)(e);throw new Error("unsafe value used in a script context");case s.Q.URL:return Object(s.ub)(e),Object(s.kb)(e,"URL")?Object(s.Eb)(e):Object(s.jb)(String(e));case s.Q.RESOURCE_URL:if(Object(s.kb)(e,"ResourceURL"))return Object(s.Eb)(e);throw new Error("unsafe value used in a resource URL context (see https://g.co/ng/security#xss)");default:throw new Error(`Unexpected SecurityContext ${t} (see https://g.co/ng/security#xss)`)}}bypassSecurityTrustHtml(t){return Object(s.lb)(t)}bypassSecurityTrustStyle(t){return Object(s.ob)(t)}bypassSecurityTrustScript(t){return Object(s.nb)(t)}bypassSecurityTrustUrl(t){return Object(s.pb)(t)}bypassSecurityTrustResourceUrl(t){return Object(s.mb)(t)}}return t.\u0275fac=function(e){return new(e||t)(s.ec(r.d))},t.\u0275prov=Object(s.Qb)({factory:function(){return P(Object(s.ec)(s.r))},token:t,providedIn:"root"}),t})();const D=[{provide:s.J,useValue:r.x},{provide:s.K,useValue:function(){o.makeCurrent(),h.init()},multi:!0},{provide:r.d,useFactory:function(){return Object(s.Cb)(document),document},deps:[]}],N=Object(s.Z)(s.db,"browser",D),F=[[],{provide:s.gb,useValue:"root"},{provide:s.p,useFactory:function(){return new s.p},deps:[]},{provide:d,useClass:O,multi:!0,deps:[r.d,s.G,s.J]},{provide:d,useClass:I,multi:!0,deps:[r.d]},[],{provide:w,useClass:w,deps:[f,g,s.c]},{provide:s.N,useExisting:w},{provide:m,useExisting:g},{provide:g,useClass:g,deps:[r.d]},{provide:s.U,useClass:s.U,deps:[s.G]},{provide:f,useClass:f,deps:[d,s.G]},[]];let L=(()=>{class t{constructor(t){if(t)throw new Error("BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.")}static withServerTransition(e){return{ngModule:t,providers:[{provide:s.c,useValue:e.appId},{provide:l,useExisting:s.c},u]}}}return t.\u0275fac=function(e){return new(e||t)(s.ec(t,12))},t.\u0275mod=s.Sb({type:t}),t.\u0275inj=s.Rb({providers:F,imports:[r.c,s.f]}),t})();"undefined"!=typeof window&&window},kJWO:function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")()},l7GE:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");class s extends r.a{notifyNext(t,e,n,r,s){this.destination.next(e)}notifyError(t,e){this.destination.error(t)}notifyComplete(t){this.destination.complete()}}},lJxs:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");function s(t,e){return function(n){if("function"!=typeof t)throw new TypeError("argument is not a function. Are you looking for `mapTo()`?");return n.lift(new i(t,e))}}class i{constructor(t,e){this.project=t,this.thisArg=e}call(t,e){return e.subscribe(new o(t,this.project,this.thisArg))}}class o extends r.a{constructor(t,e,n){super(t),this.project=e,this.count=0,this.thisArg=n||this}_next(t){let e;try{e=this.project.call(this.thisArg,t,this.count++)}catch(n){return void this.destination.error(n)}this.destination.next(e)}}},mCNh:function(t,e,n){"use strict";n.d(e,"a",function(){return s}),n.d(e,"b",function(){return i});var r=n("KqfI");function s(...t){return i(t)}function i(t){return t?1===t.length?t[0]:function(e){return t.reduce((t,e)=>e(t),e)}:r.a}},n6bG:function(t,e,n){"use strict";function r(t){return"function"==typeof t}n.d(e,"a",function(){return r})},ngJS:function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=t=>e=>{for(let n=0,r=t.length;n<r&&!e.closed;n++)e.next(t[n]);e.complete()}},njyG:function(t,e,n){"use strict";n.d(e,"a",function(){return i}),n.d(e,"b",function(){return a});var r=n("fXoL"),s=function(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)},i=function(){function t(t){this.el=t,this.dtOptions={}}return t.prototype.ngOnInit=function(){var t=this;this.dtTrigger?this.dtTrigger.subscribe(function(){t.displayTable()}):this.displayTable()},t.prototype.ngOnDestroy=function(){this.dtTrigger&&this.dtTrigger.unsubscribe(),this.dt&&this.dt.destroy(!0)},t.prototype.displayTable=function(){var t=this;this.dtInstance=new Promise(function(e,n){Promise.resolve(t.dtOptions).then(function(n){setTimeout(function(){t.dt=$(t.el.nativeElement).DataTable(n),e(t.dt)})})})},(t=function(t,e,n,r){var s,i=arguments.length,o=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(o=(i<3?s(o):i>3?s(e,n,o):s(e,n))||o);return i>3&&o&&Object.defineProperty(e,n,o),o}([s("design:paramtypes",[r.o])],t)).\u0275fac=function(e){return new(e||t)(r.Ub(r.o))},t.\u0275dir=r.Pb({type:t,selectors:[["","datatable",""]],inputs:{dtOptions:"dtOptions",dtTrigger:"dtTrigger"}}),t}(),o=n("ofXK"),a=function(){function t(){}var e;return e=t,t.forRoot=function(){return{ngModule:e}},t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=r.Sb({type:t}),t.\u0275inj=r.Rb({imports:[[o.c]]}),t}()},ofXK:function(t,e,n){"use strict";n.d(e,"a",function(){return v}),n.d(e,"b",function(){return kt}),n.d(e,"c",function(){return At}),n.d(e,"d",function(){return c}),n.d(e,"e",function(){return jt}),n.d(e,"f",function(){return It}),n.d(e,"g",function(){return S}),n.d(e,"h",function(){return h}),n.d(e,"i",function(){return C}),n.d(e,"j",function(){return b}),n.d(e,"k",function(){return ht}),n.d(e,"l",function(){return ft}),n.d(e,"m",function(){return mt}),n.d(e,"n",function(){return wt}),n.d(e,"o",function(){return _t}),n.d(e,"p",function(){return vt}),n.d(e,"q",function(){return St}),n.d(e,"r",function(){return w}),n.d(e,"s",function(){return l}),n.d(e,"t",function(){return Dt}),n.d(e,"u",function(){return Q}),n.d(e,"v",function(){return Rt}),n.d(e,"w",function(){return a}),n.d(e,"x",function(){return Pt}),n.d(e,"y",function(){return i}),n.d(e,"z",function(){return ut}),n.d(e,"A",function(){return o});var r=n("fXoL");let s=null;function i(){return s}function o(t){s||(s=t)}class a{}const c=new r.v("DocumentToken");let l=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=Object(r.Qb)({factory:u,token:t,providedIn:"platform"}),t})();function u(){return Object(r.ec)(d)}const h=new r.v("Location Initialized");let d=(()=>{class t extends l{constructor(t){super(),this._doc=t,this._init()}_init(){this.location=i().getLocation(),this._history=i().getHistory()}getBaseHrefFromDOM(){return i().getBaseHref(this._doc)}onPopState(t){i().getGlobalEventTarget(this._doc,"window").addEventListener("popstate",t,!1)}onHashChange(t){i().getGlobalEventTarget(this._doc,"window").addEventListener("hashchange",t,!1)}get href(){return this.location.href}get protocol(){return this.location.protocol}get hostname(){return this.location.hostname}get port(){return this.location.port}get pathname(){return this.location.pathname}get search(){return this.location.search}get hash(){return this.location.hash}set pathname(t){this.location.pathname=t}pushState(t,e,n){f()?this._history.pushState(t,e,n):this.location.hash=n}replaceState(t,e,n){f()?this._history.replaceState(t,e,n):this.location.hash=n}forward(){this._history.forward()}back(){this._history.back()}getState(){return this._history.state}}return t.\u0275fac=function(e){return new(e||t)(r.ec(c))},t.\u0275prov=Object(r.Qb)({factory:p,token:t,providedIn:"platform"}),t})();function f(){return!!window.history.pushState}function p(){return new d(Object(r.ec)(c))}function m(t,e){if(0==t.length)return e;if(0==e.length)return t;let n=0;return t.endsWith("/")&&n++,e.startsWith("/")&&n++,2==n?t+e.substring(1):1==n?t+e:t+"/"+e}function g(t){const e=t.match(/#|\?|$/),n=e&&e.index||t.length;return t.slice(0,n-("/"===t[n-1]?1:0))+t.slice(n)}function y(t){return t&&"?"!==t[0]?"?"+t:t}let b=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=Object(r.Qb)({factory:_,token:t,providedIn:"root"}),t})();function _(t){const e=Object(r.ec)(c).location;return new w(Object(r.ec)(l),e&&e.origin||"")}const v=new r.v("appBaseHref");let w=(()=>{class t extends b{constructor(t,e){if(super(),this._platformLocation=t,null==e&&(e=this._platformLocation.getBaseHrefFromDOM()),null==e)throw new Error("No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.");this._baseHref=e}onPopState(t){this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t)}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return m(this._baseHref,t)}path(t=!1){const e=this._platformLocation.pathname+y(this._platformLocation.search),n=this._platformLocation.hash;return n&&t?`${e}${n}`:e}pushState(t,e,n,r){const s=this.prepareExternalUrl(n+y(r));this._platformLocation.pushState(t,e,s)}replaceState(t,e,n,r){const s=this.prepareExternalUrl(n+y(r));this._platformLocation.replaceState(t,e,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}}return t.\u0275fac=function(e){return new(e||t)(r.ec(l),r.ec(v,8))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})(),S=(()=>{class t extends b{constructor(t,e){super(),this._platformLocation=t,this._baseHref="",null!=e&&(this._baseHref=e)}onPopState(t){this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t)}getBaseHref(){return this._baseHref}path(t=!1){let e=this._platformLocation.hash;return null==e&&(e="#"),e.length>0?e.substring(1):e}prepareExternalUrl(t){const e=m(this._baseHref,t);return e.length>0?"#"+e:e}pushState(t,e,n,r){let s=this.prepareExternalUrl(n+y(r));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.pushState(t,e,s)}replaceState(t,e,n,r){let s=this.prepareExternalUrl(n+y(r));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(t,e,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}}return t.\u0275fac=function(e){return new(e||t)(r.ec(l),r.ec(v,8))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})(),C=(()=>{class t{constructor(t,e){this._subject=new r.q,this._urlChangeListeners=[],this._platformStrategy=t;const n=this._platformStrategy.getBaseHref();this._platformLocation=e,this._baseHref=g(O(n)),this._platformStrategy.onPopState(t=>{this._subject.emit({url:this.path(!0),pop:!0,state:t.state,type:t.type})})}path(t=!1){return this.normalize(this._platformStrategy.path(t))}getState(){return this._platformLocation.getState()}isCurrentPathEqualTo(t,e=""){return this.path()==this.normalize(t+y(e))}normalize(e){return t.stripTrailingSlash(function(t,e){return t&&e.startsWith(t)?e.substring(t.length):e}(this._baseHref,O(e)))}prepareExternalUrl(t){return t&&"/"!==t[0]&&(t="/"+t),this._platformStrategy.prepareExternalUrl(t)}go(t,e="",n=null){this._platformStrategy.pushState(n,"",t,e),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+y(e)),n)}replaceState(t,e="",n=null){this._platformStrategy.replaceState(n,"",t,e),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+y(e)),n)}forward(){this._platformStrategy.forward()}back(){this._platformStrategy.back()}onUrlChange(t){this._urlChangeListeners.push(t),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(t=>{this._notifyUrlChangeListeners(t.url,t.state)}))}_notifyUrlChangeListeners(t="",e){this._urlChangeListeners.forEach(n=>n(t,e))}subscribe(t,e,n){return this._subject.subscribe({next:t,error:e,complete:n})}}return t.\u0275fac=function(e){return new(e||t)(r.ec(b),r.ec(l))},t.normalizeQueryParams=y,t.joinWithSlash=m,t.stripTrailingSlash=g,t.\u0275prov=Object(r.Qb)({factory:E,token:t,providedIn:"root"}),t})();function E(){return new C(Object(r.ec)(b),Object(r.ec)(l))}function O(t){return t.replace(/\/index.html$/,"")}var T=function(t){return t[t.Decimal=0]="Decimal",t[t.Percent=1]="Percent",t[t.Currency=2]="Currency",t[t.Scientific=3]="Scientific",t}({}),x=function(t){return t[t.Zero=0]="Zero",t[t.One=1]="One",t[t.Two=2]="Two",t[t.Few=3]="Few",t[t.Many=4]="Many",t[t.Other=5]="Other",t}({}),k=function(t){return t[t.Format=0]="Format",t[t.Standalone=1]="Standalone",t}({}),j=function(t){return t[t.Narrow=0]="Narrow",t[t.Abbreviated=1]="Abbreviated",t[t.Wide=2]="Wide",t[t.Short=3]="Short",t}({}),I=function(t){return t[t.Short=0]="Short",t[t.Medium=1]="Medium",t[t.Long=2]="Long",t[t.Full=3]="Full",t}({}),A=function(t){return t[t.Decimal=0]="Decimal",t[t.Group=1]="Group",t[t.List=2]="List",t[t.PercentSign=3]="PercentSign",t[t.PlusSign=4]="PlusSign",t[t.MinusSign=5]="MinusSign",t[t.Exponential=6]="Exponential",t[t.SuperscriptingExponent=7]="SuperscriptingExponent",t[t.PerMille=8]="PerMille",t[t[1/0]=9]="Infinity",t[t.NaN=10]="NaN",t[t.TimeSeparator=11]="TimeSeparator",t[t.CurrencyDecimal=12]="CurrencyDecimal",t[t.CurrencyGroup=13]="CurrencyGroup",t}({});function P(t,e){return M(Object(r.qb)(t)[r.hb.DateFormat],e)}function R(t,e){return M(Object(r.qb)(t)[r.hb.TimeFormat],e)}function D(t,e){return M(Object(r.qb)(t)[r.hb.DateTimeFormat],e)}function N(t,e){const n=Object(r.qb)(t),s=n[r.hb.NumberSymbols][e];if(void 0===s){if(e===A.CurrencyDecimal)return n[r.hb.NumberSymbols][A.Decimal];if(e===A.CurrencyGroup)return n[r.hb.NumberSymbols][A.Group]}return s}const F=r.tb;function L(t){if(!t[r.hb.ExtraData])throw new Error(`Missing extra locale data for the locale "${t[r.hb.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function M(t,e){for(let n=e;n>-1;n--)if(void 0!==t[n])return t[n];throw new Error("Locale data API: locale data undefined")}function U(t){const[e,n]=t.split(":");return{hours:+e,minutes:+n}}const H=/^(\d{4})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,$={},V=/((?:[^GyYMLwWdEabBhHmsSzZO']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var z=function(t){return t[t.Short=0]="Short",t[t.ShortGMT=1]="ShortGMT",t[t.Long=2]="Long",t[t.Extended=3]="Extended",t}({}),B=function(t){return t[t.FullYear=0]="FullYear",t[t.Month=1]="Month",t[t.Date=2]="Date",t[t.Hours=3]="Hours",t[t.Minutes=4]="Minutes",t[t.Seconds=5]="Seconds",t[t.FractionalSeconds=6]="FractionalSeconds",t[t.Day=7]="Day",t}({}),q=function(t){return t[t.DayPeriods=0]="DayPeriods",t[t.Days=1]="Days",t[t.Months=2]="Months",t[t.Eras=3]="Eras",t}({});function Q(t,e,n,r){let s=function(t){if(it(t))return t;if("number"==typeof t&&!isNaN(t))return new Date(t);if("string"==typeof t){if(t=t.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(t)){const[e,n=1,r=1]=t.split("-").map(t=>+t);return G(e,n-1,r)}const e=parseFloat(t);if(!isNaN(t-e))return new Date(e);let n;if(n=t.match(H))return function(t){const e=new Date(0);let n=0,r=0;const s=t[8]?e.setUTCFullYear:e.setFullYear,i=t[8]?e.setUTCHours:e.setHours;t[9]&&(n=Number(t[9]+t[10]),r=Number(t[9]+t[11])),s.call(e,Number(t[1]),Number(t[2])-1,Number(t[3]));const o=Number(t[4]||0)-n,a=Number(t[5]||0)-r,c=Number(t[6]||0),l=Math.floor(1e3*parseFloat("0."+(t[7]||0)));return i.call(e,o,a,c,l),e}(n)}const e=new Date(t);if(!it(e))throw new Error(`Unable to convert "${t}" into a date`);return e}(t);e=Z(n,e)||e;let i,o=[];for(;e;){if(i=V.exec(e),!i){o.push(e);break}{o=o.concat(i.slice(1));const t=o.pop();if(!t)break;e=t}}let a=s.getTimezoneOffset();r&&(a=st(r,a),s=function(t,e,n){const r=t.getTimezoneOffset();return function(t,e){return(t=new Date(t.getTime())).setMinutes(t.getMinutes()+e),t}(t,-1*(st(e,r)-r))}(s,r));let c="";return o.forEach(t=>{const e=function(t){if(rt[t])return rt[t];let e;switch(t){case"G":case"GG":case"GGG":e=Y(q.Eras,j.Abbreviated);break;case"GGGG":e=Y(q.Eras,j.Wide);break;case"GGGGG":e=Y(q.Eras,j.Narrow);break;case"y":e=J(B.FullYear,1,0,!1,!0);break;case"yy":e=J(B.FullYear,2,0,!0,!0);break;case"yyy":e=J(B.FullYear,3,0,!1,!0);break;case"yyyy":e=J(B.FullYear,4,0,!1,!0);break;case"Y":e=nt(1);break;case"YY":e=nt(2,!0);break;case"YYY":e=nt(3);break;case"YYYY":e=nt(4);break;case"M":case"L":e=J(B.Month,1,1);break;case"MM":case"LL":e=J(B.Month,2,1);break;case"MMM":e=Y(q.Months,j.Abbreviated);break;case"MMMM":e=Y(q.Months,j.Wide);break;case"MMMMM":e=Y(q.Months,j.Narrow);break;case"LLL":e=Y(q.Months,j.Abbreviated,k.Standalone);break;case"LLLL":e=Y(q.Months,j.Wide,k.Standalone);break;case"LLLLL":e=Y(q.Months,j.Narrow,k.Standalone);break;case"w":e=et(1);break;case"ww":e=et(2);break;case"W":e=et(1,!0);break;case"d":e=J(B.Date,1);break;case"dd":e=J(B.Date,2);break;case"E":case"EE":case"EEE":e=Y(q.Days,j.Abbreviated);break;case"EEEE":e=Y(q.Days,j.Wide);break;case"EEEEE":e=Y(q.Days,j.Narrow);break;case"EEEEEE":e=Y(q.Days,j.Short);break;case"a":case"aa":case"aaa":e=Y(q.DayPeriods,j.Abbreviated);break;case"aaaa":e=Y(q.DayPeriods,j.Wide);break;case"aaaaa":e=Y(q.DayPeriods,j.Narrow);break;case"b":case"bb":case"bbb":e=Y(q.DayPeriods,j.Abbreviated,k.Standalone,!0);break;case"bbbb":e=Y(q.DayPeriods,j.Wide,k.Standalone,!0);break;case"bbbbb":e=Y(q.DayPeriods,j.Narrow,k.Standalone,!0);break;case"B":case"BB":case"BBB":e=Y(q.DayPeriods,j.Abbreviated,k.Format,!0);break;case"BBBB":e=Y(q.DayPeriods,j.Wide,k.Format,!0);break;case"BBBBB":e=Y(q.DayPeriods,j.Narrow,k.Format,!0);break;case"h":e=J(B.Hours,1,-12);break;case"hh":e=J(B.Hours,2,-12);break;case"H":e=J(B.Hours,1);break;case"HH":e=J(B.Hours,2);break;case"m":e=J(B.Minutes,1);break;case"mm":e=J(B.Minutes,2);break;case"s":e=J(B.Seconds,1);break;case"ss":e=J(B.Seconds,2);break;case"S":e=J(B.FractionalSeconds,1);break;case"SS":e=J(B.FractionalSeconds,2);break;case"SSS":e=J(B.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":e=X(z.Short);break;case"ZZZZZ":e=X(z.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":e=X(z.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":e=X(z.Long);break;default:return null}return rt[t]=e,e}(t);c+=e?e(s,n,a):"''"===t?"'":t.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function G(t,e,n){const r=new Date(0);return r.setFullYear(t,e,n),r.setHours(0,0,0),r}function Z(t,e){const n=function(t){return Object(r.qb)(t)[r.hb.LocaleId]}(t);if($[n]=$[n]||{},$[n][e])return $[n][e];let s="";switch(e){case"shortDate":s=P(t,I.Short);break;case"mediumDate":s=P(t,I.Medium);break;case"longDate":s=P(t,I.Long);break;case"fullDate":s=P(t,I.Full);break;case"shortTime":s=R(t,I.Short);break;case"mediumTime":s=R(t,I.Medium);break;case"longTime":s=R(t,I.Long);break;case"fullTime":s=R(t,I.Full);break;case"short":const e=Z(t,"shortTime"),n=Z(t,"shortDate");s=W(D(t,I.Short),[e,n]);break;case"medium":const r=Z(t,"mediumTime"),i=Z(t,"mediumDate");s=W(D(t,I.Medium),[r,i]);break;case"long":const o=Z(t,"longTime"),a=Z(t,"longDate");s=W(D(t,I.Long),[o,a]);break;case"full":const c=Z(t,"fullTime"),l=Z(t,"fullDate");s=W(D(t,I.Full),[c,l])}return s&&($[n][e]=s),s}function W(t,e){return e&&(t=t.replace(/\{([^}]+)}/g,function(t,n){return null!=e&&n in e?e[n]:t})),t}function K(t,e,n="-",r,s){let i="";(t<0||s&&t<=0)&&(s?t=1-t:(t=-t,i=n));let o=String(t);for(;o.length<e;)o="0"+o;return r&&(o=o.substr(o.length-e)),i+o}function J(t,e,n=0,r=!1,s=!1){return function(i,o){let a=function(t,e){switch(t){case B.FullYear:return e.getFullYear();case B.Month:return e.getMonth();case B.Date:return e.getDate();case B.Hours:return e.getHours();case B.Minutes:return e.getMinutes();case B.Seconds:return e.getSeconds();case B.FractionalSeconds:return e.getMilliseconds();case B.Day:return e.getDay();default:throw new Error(`Unknown DateType value "${t}".`)}}(t,i);if((n>0||a>-n)&&(a+=n),t===B.Hours)0===a&&-12===n&&(a=12);else if(t===B.FractionalSeconds)return c=e,K(a,3).substr(0,c);var c;const l=N(o,A.MinusSign);return K(a,e,l,r,s)}}function Y(t,e,n=k.Format,s=!1){return function(i,o){return function(t,e,n,s,i,o){switch(n){case q.Months:return function(t,e,n){const s=Object(r.qb)(t),i=M([s[r.hb.MonthsFormat],s[r.hb.MonthsStandalone]],e);return M(i,n)}(e,i,s)[t.getMonth()];case q.Days:return function(t,e,n){const s=Object(r.qb)(t),i=M([s[r.hb.DaysFormat],s[r.hb.DaysStandalone]],e);return M(i,n)}(e,i,s)[t.getDay()];case q.DayPeriods:const a=t.getHours(),c=t.getMinutes();if(o){const t=function(t){const e=Object(r.qb)(t);return L(e),(e[r.hb.ExtraData][2]||[]).map(t=>"string"==typeof t?U(t):[U(t[0]),U(t[1])])}(e),n=function(t,e,n){const s=Object(r.qb)(t);L(s);const i=M([s[r.hb.ExtraData][0],s[r.hb.ExtraData][1]],e)||[];return M(i,n)||[]}(e,i,s),o=t.findIndex(t=>{if(Array.isArray(t)){const[e,n]=t,r=a>=e.hours&&c>=e.minutes,s=a<n.hours||a===n.hours&&c<n.minutes;if(e.hours<n.hours){if(r&&s)return!0}else if(r||s)return!0}else if(t.hours===a&&t.minutes===c)return!0;return!1});if(-1!==o)return n[o]}return function(t,e,n){const s=Object(r.qb)(t),i=M([s[r.hb.DayPeriodsFormat],s[r.hb.DayPeriodsStandalone]],e);return M(i,n)}(e,i,s)[a<12?0:1];case q.Eras:return function(t,e){return M(Object(r.qb)(t)[r.hb.Eras],e)}(e,s)[t.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${n}`)}}(i,o,t,e,n,s)}}function X(t){return function(e,n,r){const s=-1*r,i=N(n,A.MinusSign),o=s>0?Math.floor(s/60):Math.ceil(s/60);switch(t){case z.Short:return(s>=0?"+":"")+K(o,2,i)+K(Math.abs(s%60),2,i);case z.ShortGMT:return"GMT"+(s>=0?"+":"")+K(o,1,i);case z.Long:return"GMT"+(s>=0?"+":"")+K(o,2,i)+":"+K(Math.abs(s%60),2,i);case z.Extended:return 0===r?"Z":(s>=0?"+":"")+K(o,2,i)+":"+K(Math.abs(s%60),2,i);default:throw new Error(`Unknown zone width "${t}"`)}}}function tt(t){return G(t.getFullYear(),t.getMonth(),t.getDate()+(4-t.getDay()))}function et(t,e=!1){return function(n,r){let s;if(e){const t=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,e=n.getDate();s=1+Math.floor((e+t)/7)}else{const t=tt(n),e=function(t){const e=G(t,0,1).getDay();return G(t,0,1+(e<=4?4:11)-e)}(t.getFullYear()),r=t.getTime()-e.getTime();s=1+Math.round(r/6048e5)}return K(s,t,N(r,A.MinusSign))}}function nt(t,e=!1){return function(n,r){return K(tt(n).getFullYear(),t,N(r,A.MinusSign),e)}}const rt={};function st(t,e){t=t.replace(/:/g,"");const n=Date.parse("Jan 01, 1970 00:00:00 "+t)/6e4;return isNaN(n)?e:n}function it(t){return t instanceof Date&&!isNaN(t.valueOf())}const ot=/^(\d+)?\.((\d+)(-(\d+))?)?$/;function at(t){const e=parseInt(t);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+t);return e}class ct{}let lt=(()=>{class t extends ct{constructor(t){super(),this.locale=t}getPluralCategory(t,e){switch(F(e||this.locale)(t)){case x.Zero:return"zero";case x.One:return"one";case x.Two:return"two";case x.Few:return"few";case x.Many:return"many";default:return"other"}}}return t.\u0275fac=function(e){return new(e||t)(r.ec(r.A))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})();function ut(t,e){e=encodeURIComponent(e);for(const n of t.split(";")){const t=n.indexOf("="),[r,s]=-1==t?[n,""]:[n.slice(0,t),n.slice(t+1)];if(r.trim()===e)return decodeURIComponent(s)}return null}let ht=(()=>{class t{constructor(t,e,n,r){this._iterableDiffers=t,this._keyValueDiffers=e,this._ngEl=n,this._renderer=r,this._iterableDiffer=null,this._keyValueDiffer=null,this._initialClasses=[],this._rawClass=null}set klass(t){this._removeClasses(this._initialClasses),this._initialClasses="string"==typeof t?t.split(/\s+/):[],this._applyClasses(this._initialClasses),this._applyClasses(this._rawClass)}set ngClass(t){this._removeClasses(this._rawClass),this._applyClasses(this._initialClasses),this._iterableDiffer=null,this._keyValueDiffer=null,this._rawClass="string"==typeof t?t.split(/\s+/):t,this._rawClass&&(Object(r.wb)(this._rawClass)?this._iterableDiffer=this._iterableDiffers.find(this._rawClass).create():this._keyValueDiffer=this._keyValueDiffers.find(this._rawClass).create())}ngDoCheck(){if(this._iterableDiffer){const t=this._iterableDiffer.diff(this._rawClass);t&&this._applyIterableChanges(t)}else if(this._keyValueDiffer){const t=this._keyValueDiffer.diff(this._rawClass);t&&this._applyKeyValueChanges(t)}}_applyKeyValueChanges(t){t.forEachAddedItem(t=>this._toggleClass(t.key,t.currentValue)),t.forEachChangedItem(t=>this._toggleClass(t.key,t.currentValue)),t.forEachRemovedItem(t=>{t.previousValue&&this._toggleClass(t.key,!1)})}_applyIterableChanges(t){t.forEachAddedItem(t=>{if("string"!=typeof t.item)throw new Error(`NgClass can only toggle CSS classes expressed as strings, got ${Object(r.Db)(t.item)}`);this._toggleClass(t.item,!0)}),t.forEachRemovedItem(t=>this._toggleClass(t.item,!1))}_applyClasses(t){t&&(Array.isArray(t)||t instanceof Set?t.forEach(t=>this._toggleClass(t,!0)):Object.keys(t).forEach(e=>this._toggleClass(e,!!t[e])))}_removeClasses(t){t&&(Array.isArray(t)||t instanceof Set?t.forEach(t=>this._toggleClass(t,!1)):Object.keys(t).forEach(t=>this._toggleClass(t,!1)))}_toggleClass(t,e){(t=t.trim())&&t.split(/\s+/g).forEach(t=>{e?this._renderer.addClass(this._ngEl.nativeElement,t):this._renderer.removeClass(this._ngEl.nativeElement,t)})}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.y),r.Ub(r.z),r.Ub(r.o),r.Ub(r.M))},t.\u0275dir=r.Pb({type:t,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"}}),t})();class dt{constructor(t,e,n,r){this.$implicit=t,this.ngForOf=e,this.index=n,this.count=r}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let ft=(()=>{class t{constructor(t,e,n){this._viewContainer=t,this._template=e,this._differs=n,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const n=this._ngForOf;if(!this._differ&&n)try{this._differ=this._differs.find(n).create(this.ngForTrackBy)}catch(e){throw new Error(`Cannot find a differ supporting object '${n}' of type '${t=n,t.name||typeof t}'. NgFor only supports binding to Iterables such as Arrays.`)}}var t;if(this._differ){const t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){const e=[];t.forEachOperation((t,n,r)=>{if(null==t.previousIndex){const n=this._viewContainer.createEmbeddedView(this._template,new dt(null,this._ngForOf,-1,-1),null===r?void 0:r),s=new pt(t,n);e.push(s)}else if(null==r)this._viewContainer.remove(null===n?void 0:n);else if(null!==n){const s=this._viewContainer.get(n);this._viewContainer.move(s,r);const i=new pt(t,s);e.push(i)}});for(let n=0;n<e.length;n++)this._perViewChange(e[n].view,e[n].record);for(let n=0,r=this._viewContainer.length;n<r;n++){const t=this._viewContainer.get(n);t.context.index=n,t.context.count=r,t.context.ngForOf=this._ngForOf}t.forEachIdentityChange(t=>{this._viewContainer.get(t.currentIndex).context.$implicit=t.item})}_perViewChange(t,e){t.context.$implicit=e.item}static ngTemplateContextGuard(t,e){return!0}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.X),r.Ub(r.T),r.Ub(r.y))},t.\u0275dir=r.Pb({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}}),t})();class pt{constructor(t,e){this.record=t,this.view=e}}let mt=(()=>{class t{constructor(t,e){this._viewContainer=t,this._context=new gt,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=e}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){yt("ngIfThen",t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){yt("ngIfElse",t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(t,e){return!0}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.X),r.Ub(r.T))},t.\u0275dir=r.Pb({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}}),t})();class gt{constructor(){this.$implicit=null,this.ngIf=null}}function yt(t,e){if(e&&!e.createEmbeddedView)throw new Error(`${t} must be a TemplateRef, but received '${Object(r.Db)(e)}'.`)}class bt{constructor(t,e){this._viewContainerRef=t,this._templateRef=e,this._created=!1}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(t){t&&!this._created?this.create():!t&&this._created&&this.destroy()}}let _t=(()=>{class t{constructor(){this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}set ngSwitch(t){this._ngSwitch=t,0===this._caseCount&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(t){this._defaultViews||(this._defaultViews=[]),this._defaultViews.push(t)}_matchCase(t){const e=t==this._ngSwitch;return this._lastCasesMatched=this._lastCasesMatched||e,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),e}_updateDefaultCases(t){if(this._defaultViews&&t!==this._defaultUsed){this._defaultUsed=t;for(let e=0;e<this._defaultViews.length;e++)this._defaultViews[e].enforceState(t)}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275dir=r.Pb({type:t,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"}}),t})(),vt=(()=>{class t{constructor(t,e,n){this.ngSwitch=n,n._addCase(),this._view=new bt(t,e)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.X),r.Ub(r.T),r.Ub(_t,1))},t.\u0275dir=r.Pb({type:t,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"}}),t})(),wt=(()=>{class t{constructor(t,e,n){this._ngEl=t,this._differs=e,this._renderer=n,this._ngStyle=null,this._differ=null}set ngStyle(t){this._ngStyle=t,!this._differ&&t&&(this._differ=this._differs.find(t).create())}ngDoCheck(){if(this._differ){const t=this._differ.diff(this._ngStyle);t&&this._applyChanges(t)}}_setStyle(t,e){const[n,r]=t.split(".");null!=(e=null!=e&&r?`${e}${r}`:e)?this._renderer.setStyle(this._ngEl.nativeElement,n,e):this._renderer.removeStyle(this._ngEl.nativeElement,n)}_applyChanges(t){t.forEachRemovedItem(t=>this._setStyle(t.key,null)),t.forEachAddedItem(t=>this._setStyle(t.key,t.currentValue)),t.forEachChangedItem(t=>this._setStyle(t.key,t.currentValue))}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.o),r.Ub(r.z),r.Ub(r.M))},t.\u0275dir=r.Pb({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}}),t})(),St=(()=>{class t{constructor(t){this._viewContainerRef=t,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null}ngOnChanges(t){if(this._shouldRecreateView(t)){const t=this._viewContainerRef;this._viewRef&&t.remove(t.indexOf(this._viewRef)),this._viewRef=this.ngTemplateOutlet?t.createEmbeddedView(this.ngTemplateOutlet,this.ngTemplateOutletContext):null}else this._viewRef&&this.ngTemplateOutletContext&&this._updateExistingContext(this.ngTemplateOutletContext)}_shouldRecreateView(t){const e=t.ngTemplateOutletContext;return!!t.ngTemplateOutlet||e&&this._hasContextShapeChanged(e)}_hasContextShapeChanged(t){const e=Object.keys(t.previousValue||{}),n=Object.keys(t.currentValue||{});if(e.length===n.length){for(let t of n)if(-1===e.indexOf(t))return!0;return!1}return!0}_updateExistingContext(t){for(let e of Object.keys(t))this._viewRef.context[e]=this.ngTemplateOutletContext[e]}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.X))},t.\u0275dir=r.Pb({type:t,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet"},features:[r.Gb]}),t})();function Ct(t,e){return Error(`InvalidPipeArgument: '${e}' for pipe '${Object(r.Db)(t)}'`)}class Et{createSubscription(t,e){return t.subscribe({next:e,error:t=>{throw t}})}dispose(t){t.unsubscribe()}onDestroy(t){t.unsubscribe()}}class Ot{createSubscription(t,e){return t.then(e,t=>{throw t})}dispose(t){}onDestroy(t){}}const Tt=new Ot,xt=new Et;let kt=(()=>{class t{constructor(t){this._ref=t,this._latestValue=null,this._subscription=null,this._obj=null,this._strategy=null}ngOnDestroy(){this._subscription&&this._dispose()}transform(t){return this._obj?t!==this._obj?(this._dispose(),this.transform(t)):this._latestValue:(t&&this._subscribe(t),this._latestValue)}_subscribe(t){this._obj=t,this._strategy=this._selectStrategy(t),this._subscription=this._strategy.createSubscription(t,e=>this._updateLatestValue(t,e))}_selectStrategy(e){if(Object(r.yb)(e))return Tt;if(Object(r.zb)(e))return xt;throw Ct(t,e)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(t,e){t===this._obj&&(this._latestValue=e,this._ref.markForCheck())}}return t.\u0275fac=function(e){return new(e||t)(r.gc())},t.\u0275pipe=r.Tb({name:"async",type:t,pure:!1}),t})(),jt=(()=>{class t{constructor(t){this.locale=t}transform(e,n="mediumDate",r,s){if(null==e||""===e||e!=e)return null;try{return Q(e,n,s||this.locale,r)}catch(i){throw Ct(t,i.message)}}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.A))},t.\u0275pipe=r.Tb({name:"date",type:t,pure:!0}),t})(),It=(()=>{class t{constructor(t){this._locale=t}transform(e,n,s){if(!function(t){return!(null==t||""===t||t!=t)}(e))return null;s=s||this._locale;try{return function(t,e,n){return function(t,e,n,r,s,i,o=!1){let a="",c=!1;if(isFinite(t)){let l=function(t){let e,n,r,s,i,o=Math.abs(t)+"",a=0;for((n=o.indexOf("."))>-1&&(o=o.replace(".","")),(r=o.search(/e/i))>0?(n<0&&(n=r),n+=+o.slice(r+1),o=o.substring(0,r)):n<0&&(n=o.length),r=0;"0"===o.charAt(r);r++);if(r===(i=o.length))e=[0],n=1;else{for(i--;"0"===o.charAt(i);)i--;for(n-=r,e=[],s=0;r<=i;r++,s++)e[s]=Number(o.charAt(r))}return n>22&&(e=e.splice(0,21),a=n-1,n=1),{digits:e,exponent:a,integerLen:n}}(t);o&&(l=function(t){if(0===t.digits[0])return t;const e=t.digits.length-t.integerLen;return t.exponent?t.exponent+=2:(0===e?t.digits.push(0,0):1===e&&t.digits.push(0),t.integerLen+=2),t}(l));let u=e.minInt,h=e.minFrac,d=e.maxFrac;if(i){const t=i.match(ot);if(null===t)throw new Error(`${i} is not a valid digit info`);const e=t[1],n=t[3],r=t[5];null!=e&&(u=at(e)),null!=n&&(h=at(n)),null!=r?d=at(r):null!=n&&h>d&&(d=h)}!function(t,e,n){if(e>n)throw new Error(`The minimum number of digits after fraction (${e}) is higher than the maximum (${n}).`);let r=t.digits,s=r.length-t.integerLen;const i=Math.min(Math.max(e,s),n);let o=i+t.integerLen,a=r[o];if(o>0){r.splice(Math.max(t.integerLen,o));for(let t=o;t<r.length;t++)r[t]=0}else{s=Math.max(0,s),t.integerLen=1,r.length=Math.max(1,o=i+1),r[0]=0;for(let t=1;t<o;t++)r[t]=0}if(a>=5)if(o-1<0){for(let e=0;e>o;e--)r.unshift(0),t.integerLen++;r.unshift(1),t.integerLen++}else r[o-1]++;for(;s<Math.max(0,i);s++)r.push(0);let c=0!==i;const l=e+t.integerLen,u=r.reduceRight(function(t,e,n,r){return r[n]=(e+=t)<10?e:e-10,c&&(0===r[n]&&n>=l?r.pop():c=!1),e>=10?1:0},0);u&&(r.unshift(u),t.integerLen++)}(l,h,d);let f=l.digits,p=l.integerLen;const m=l.exponent;let g=[];for(c=f.every(t=>!t);p<u;p++)f.unshift(0);for(;p<0;p++)f.unshift(0);p>0?g=f.splice(p,f.length):(g=f,f=[0]);const y=[];for(f.length>=e.lgSize&&y.unshift(f.splice(-e.lgSize,f.length).join(""));f.length>e.gSize;)y.unshift(f.splice(-e.gSize,f.length).join(""));f.length&&y.unshift(f.join("")),a=y.join(N(n,r)),g.length&&(a+=N(n,s)+g.join("")),m&&(a+=N(n,A.Exponential)+"+"+m)}else a=N(n,A.Infinity);return a=t<0&&!c?e.negPre+a+e.negSuf:e.posPre+a+e.posSuf,a}(t,function(t,e="-"){const n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=t.split(";"),s=r[0],i=r[1],o=-1!==s.indexOf(".")?s.split("."):[s.substring(0,s.lastIndexOf("0")+1),s.substring(s.lastIndexOf("0")+1)],a=o[0],c=o[1]||"";n.posPre=a.substr(0,a.indexOf("#"));for(let u=0;u<c.length;u++){const t=c.charAt(u);"0"===t?n.minFrac=n.maxFrac=u+1:"#"===t?n.maxFrac=u+1:n.posSuf+=t}const l=a.split(",");if(n.gSize=l[1]?l[1].length:0,n.lgSize=l[2]||l[1]?(l[2]||l[1]).length:0,i){const t=s.length-n.posPre.length-n.posSuf.length,e=i.indexOf("#");n.negPre=i.substr(0,e).replace(/'/g,""),n.negSuf=i.substr(e+t).replace(/'/g,"")}else n.negPre=e+n.posPre,n.negSuf=n.posSuf;return n}(function(t,e){return Object(r.qb)(t)[r.hb.NumberFormats][e]}(e,T.Decimal),N(e,A.MinusSign)),e,A.Group,A.Decimal,n)}(function(t){if("string"==typeof t&&!isNaN(Number(t)-parseFloat(t)))return Number(t);if("number"!=typeof t)throw new Error(`${t} is not a number`);return t}(e),s,n)}catch(i){throw Ct(t,i.message)}}}return t.\u0275fac=function(e){return new(e||t)(r.Ub(r.A))},t.\u0275pipe=r.Tb({name:"number",type:t,pure:!0}),t})(),At=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=r.Sb({type:t}),t.\u0275inj=r.Rb({providers:[{provide:ct,useClass:lt}]}),t})();const Pt="browser";function Rt(t){return t===Pt}let Dt=(()=>{class t{}return t.\u0275prov=Object(r.Qb)({token:t,providedIn:"root",factory:()=>new Nt(Object(r.ec)(c),window)}),t})();class Nt{constructor(t,e){this.document=t,this.window=e,this.offset=()=>[0,0]}setOffset(t){this.offset=Array.isArray(t)?()=>t:t}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(t){this.supportsScrolling()&&this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){var e;if(!this.supportsScrolling())return;const n=null!==(e=this.document.getElementById(t))&&void 0!==e?e:this.document.getElementsByName(t)[0];void 0!==n&&(this.scrollToElement(n),this.attemptFocus(n))}setHistoryScrollRestoration(t){if(this.supportScrollRestoration()){const e=this.window.history;e&&e.scrollRestoration&&(e.scrollRestoration=t)}}scrollToElement(t){const e=t.getBoundingClientRect(),n=e.left+this.window.pageXOffset,r=e.top+this.window.pageYOffset,s=this.offset();this.window.scrollTo(n-s[0],r-s[1])}attemptFocus(t){return t.focus(),this.document.activeElement===t}supportScrollRestoration(){try{if(!this.supportsScrolling())return!1;const t=Ft(this.window.history)||Ft(Object.getPrototypeOf(this.window.history));return!(!t||!t.writable&&!t.set)}catch(t){return!1}}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch(t){return!1}}}function Ft(t){return Object.getOwnPropertyDescriptor(t,"scrollRestoration")}},pLZG:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");function s(t,e){return function(n){return n.lift(new i(t,e))}}class i{constructor(t,e){this.predicate=t,this.thisArg=e}call(t,e){return e.subscribe(new o(t,this.predicate,this.thisArg))}}class o extends r.a{constructor(t,e,n){super(t),this.predicate=e,this.thisArg=n,this.count=0}_next(t){let e;try{e=this.predicate.call(this.thisArg,t,this.count++)}catch(n){return void this.destination.error(n)}e&&this.destination.next(t)}}},quSY:function(t,e,n){"use strict";n.d(e,"a",function(){return a});var r=n("DH7j"),s=n("XoHu"),i=n("n6bG");const o=(()=>{function t(t){return Error.call(this),this.message=t?`${t.length} errors occurred during unsubscription:\n${t.map((t,e)=>`${e+1}) ${t.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=t,this}return t.prototype=Object.create(Error.prototype),t})();let a=(()=>{class t{constructor(t){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,t&&(this._unsubscribe=t)}unsubscribe(){let e;if(this.closed)return;let{_parentOrParents:n,_unsubscribe:a,_subscriptions:l}=this;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,n instanceof t)n.remove(this);else if(null!==n)for(let t=0;t<n.length;++t)n[t].remove(this);if(Object(i.a)(a))try{a.call(this)}catch(u){e=u instanceof o?c(u.errors):[u]}if(Object(r.a)(l)){let t=-1,n=l.length;for(;++t<n;){const n=l[t];if(Object(s.a)(n))try{n.unsubscribe()}catch(u){e=e||[],u instanceof o?e=e.concat(c(u.errors)):e.push(u)}}}if(e)throw new o(e)}add(e){let n=e;if(!e)return t.EMPTY;switch(typeof e){case"function":n=new t(e);case"object":if(n===this||n.closed||"function"!=typeof n.unsubscribe)return n;if(this.closed)return n.unsubscribe(),n;if(!(n instanceof t)){const e=n;n=new t,n._subscriptions=[e]}break;default:throw new Error("unrecognized teardown "+e+" added to Subscription.")}let{_parentOrParents:r}=n;if(null===r)n._parentOrParents=this;else if(r instanceof t){if(r===this)return n;n._parentOrParents=[r,this]}else{if(-1!==r.indexOf(this))return n;r.push(this)}const s=this._subscriptions;return null===s?this._subscriptions=[n]:s.push(n),n}remove(t){const e=this._subscriptions;if(e){const n=e.indexOf(t);-1!==n&&e.splice(n,1)}}}var e;return t.EMPTY=((e=new t).closed=!0,e),t})();function c(t){return t.reduce((t,e)=>t.concat(e instanceof o?e.errors:e),[])}},sVev:function(t,e,n){"use strict";n.d(e,"a",function(){return r});const r=(()=>{function t(){return Error.call(this),this.message="no elements in sequence",this.name="EmptyError",this}return t.prototype=Object.create(Error.prototype),t})()},"tk/3":function(t,e,n){"use strict";n.d(e,"a",function(){return k}),n.d(e,"b",function(){return h}),n.d(e,"c",function(){return T}),n.d(e,"d",function(){return $}),n.d(e,"e",function(){return d}),n.d(e,"f",function(){return m}),n.d(e,"g",function(){return C}),n.d(e,"h",function(){return R}),n.d(e,"i",function(){return A});var r=n("fXoL"),s=n("LRne"),i=n("HDdC"),o=n("bOdf"),a=n("pLZG"),c=n("lJxs"),l=n("ofXK");class u{}class h{}class d{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?this.lazyInit="string"==typeof t?()=>{this.headers=new Map,t.split("\n").forEach(t=>{const e=t.indexOf(":");if(e>0){const n=t.slice(0,e),r=n.toLowerCase(),s=t.slice(e+1).trim();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(s):this.headers.set(r,[s])}})}:()=>{this.headers=new Map,Object.keys(t).forEach(e=>{let n=t[e];const r=e.toLowerCase();"string"==typeof n&&(n=[n]),n.length>0&&(this.headers.set(r,n),this.maybeSetNormalizedName(e,r))})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();const e=this.headers.get(t.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,e){return this.clone({name:t,value:e,op:"a"})}set(t,e){return this.clone({name:t,value:e,op:"s"})}delete(t,e){return this.clone({name:t,value:e,op:"d"})}maybeSetNormalizedName(t,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,t)}init(){this.lazyInit&&(this.lazyInit instanceof d?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(e=>{this.headers.set(e,t.headers.get(e)),this.normalizedNames.set(e,t.normalizedNames.get(e))})}clone(t){const e=new d;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof d?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([t]),e}applyUpdate(t){const e=t.name.toLowerCase();switch(t.op){case"a":case"s":let n=t.value;if("string"==typeof n&&(n=[n]),0===n.length)return;this.maybeSetNormalizedName(t.name,e);const r=("a"===t.op?this.headers.get(e):void 0)||[];r.push(...n),this.headers.set(e,r);break;case"d":const s=t.value;if(s){let t=this.headers.get(e);if(!t)return;t=t.filter(t=>-1===s.indexOf(t)),0===t.length?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,t)}else this.headers.delete(e),this.normalizedNames.delete(e)}}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>t(this.normalizedNames.get(e),this.headers.get(e)))}}class f{encodeKey(t){return p(t)}encodeValue(t){return p(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}}function p(t){return encodeURIComponent(t).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/gi,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%2B/gi,"+").replace(/%3D/gi,"=").replace(/%3F/gi,"?").replace(/%2F/gi,"/")}class m{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new f,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function(t,e){const n=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(t=>{const r=t.indexOf("="),[s,i]=-1==r?[e.decodeKey(t),""]:[e.decodeKey(t.slice(0,r)),e.decodeValue(t.slice(r+1))],o=n.get(s)||[];o.push(i),n.set(s,o)}),n}(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(e=>{const n=t.fromObject[e];this.map.set(e,Array.isArray(n)?n:[n])})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();const e=this.map.get(t);return e?e[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,e){return this.clone({param:t,value:e,op:"a"})}appendAll(t){const e=[];return Object.keys(t).forEach(n=>{const r=t[n];Array.isArray(r)?r.forEach(t=>{e.push({param:n,value:t,op:"a"})}):e.push({param:n,value:r,op:"a"})}),this.clone(e)}set(t,e){return this.clone({param:t,value:e,op:"s"})}delete(t,e){return this.clone({param:t,value:e,op:"d"})}toString(){return this.init(),this.keys().map(t=>{const e=this.encoder.encodeKey(t);return this.map.get(t).map(t=>e+"="+this.encoder.encodeValue(t)).join("&")}).filter(t=>""!==t).join("&")}clone(t){const e=new m({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(t),e}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":const e=("a"===t.op?this.map.get(t.param):void 0)||[];e.push(t.value),this.map.set(t.param,e);break;case"d":if(void 0===t.value){this.map.delete(t.param);break}{let e=this.map.get(t.param)||[];const n=e.indexOf(t.value);-1!==n&&e.splice(n,1),e.length>0?this.map.set(t.param,e):this.map.delete(t.param)}}}),this.cloneFrom=this.updates=null)}}function g(t){return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer}function y(t){return"undefined"!=typeof Blob&&t instanceof Blob}function b(t){return"undefined"!=typeof FormData&&t instanceof FormData}class _{constructor(t,e,n,r){let s;if(this.url=e,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase(),function(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||r?(this.body=void 0!==n?n:null,s=r):s=n,s&&(this.reportProgress=!!s.reportProgress,this.withCredentials=!!s.withCredentials,s.responseType&&(this.responseType=s.responseType),s.headers&&(this.headers=s.headers),s.params&&(this.params=s.params)),this.headers||(this.headers=new d),this.params){const t=this.params.toString();if(0===t.length)this.urlWithParams=e;else{const n=e.indexOf("?");this.urlWithParams=e+(-1===n?"?":n<e.length-1?"&":"")+t}}else this.params=new m,this.urlWithParams=e}serializeBody(){return null===this.body?null:g(this.body)||y(this.body)||b(this.body)||"string"==typeof this.body?this.body:this.body instanceof m?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||b(this.body)?null:y(this.body)?this.body.type||null:g(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof m?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||Array.isArray(this.body)?"application/json":null}clone(t={}){const e=t.method||this.method,n=t.url||this.url,r=t.responseType||this.responseType,s=void 0!==t.body?t.body:this.body,i=void 0!==t.withCredentials?t.withCredentials:this.withCredentials,o=void 0!==t.reportProgress?t.reportProgress:this.reportProgress;let a=t.headers||this.headers,c=t.params||this.params;return void 0!==t.setHeaders&&(a=Object.keys(t.setHeaders).reduce((e,n)=>e.set(n,t.setHeaders[n]),a)),t.setParams&&(c=Object.keys(t.setParams).reduce((e,n)=>e.set(n,t.setParams[n]),c)),new _(e,n,s,{params:c,headers:a,reportProgress:o,responseType:r,withCredentials:i})}}var v=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}({});class w{constructor(t,e=200,n="OK"){this.headers=t.headers||new d,this.status=void 0!==t.status?t.status:e,this.statusText=t.statusText||n,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}}class S extends w{constructor(t={}){super(t),this.type=v.ResponseHeader}clone(t={}){return new S({headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class C extends w{constructor(t={}){super(t),this.type=v.Response,this.body=void 0!==t.body?t.body:null}clone(t={}){return new C({body:void 0!==t.body?t.body:this.body,headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class E extends w{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${t.url||"(unknown url)"}`:`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}}function O(t,e){return{body:e,headers:t.headers,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials}}let T=(()=>{class t{constructor(t){this.handler=t}request(t,e,n={}){let r;if(t instanceof _)r=t;else{let s,i;s=n.headers instanceof d?n.headers:new d(n.headers),n.params&&(i=n.params instanceof m?n.params:new m({fromObject:n.params})),r=new _(t,e,void 0!==n.body?n.body:null,{headers:s,params:i,reportProgress:n.reportProgress,responseType:n.responseType||"json",withCredentials:n.withCredentials})}const i=Object(s.a)(r).pipe(Object(o.a)(t=>this.handler.handle(t)));if(t instanceof _||"events"===n.observe)return i;const l=i.pipe(Object(a.a)(t=>t instanceof C));switch(n.observe||"body"){case"body":switch(r.responseType){case"arraybuffer":return l.pipe(Object(c.a)(t=>{if(null!==t.body&&!(t.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return t.body}));case"blob":return l.pipe(Object(c.a)(t=>{if(null!==t.body&&!(t.body instanceof Blob))throw new Error("Response is not a Blob.");return t.body}));case"text":return l.pipe(Object(c.a)(t=>{if(null!==t.body&&"string"!=typeof t.body)throw new Error("Response is not a string.");return t.body}));case"json":default:return l.pipe(Object(c.a)(t=>t.body))}case"response":return l;default:throw new Error(`Unreachable: unhandled observe type ${n.observe}}`)}}delete(t,e={}){return this.request("DELETE",t,e)}get(t,e={}){return this.request("GET",t,e)}head(t,e={}){return this.request("HEAD",t,e)}jsonp(t,e){return this.request("JSONP",t,{params:(new m).append(e,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,e={}){return this.request("OPTIONS",t,e)}patch(t,e,n={}){return this.request("PATCH",t,O(n,e))}post(t,e,n={}){return this.request("POST",t,O(n,e))}put(t,e,n={}){return this.request("PUT",t,O(n,e))}}return t.\u0275fac=function(e){return new(e||t)(r.ec(u))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})();class x{constructor(t,e){this.next=t,this.interceptor=e}handle(t){return this.interceptor.intercept(t,this.next)}}const k=new r.v("HTTP_INTERCEPTORS");let j=(()=>{class t{intercept(t,e){return e.handle(t)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})();const I=/^\)\]\}',?\n/;class A{}let P=(()=>{class t{constructor(){}build(){return new XMLHttpRequest}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})(),R=(()=>{class t{constructor(t){this.xhrFactory=t}handle(t){if("JSONP"===t.method)throw new Error("Attempted to construct Jsonp request without HttpClientJsonpModule installed.");return new i.a(e=>{const n=this.xhrFactory.build();if(n.open(t.method,t.urlWithParams),t.withCredentials&&(n.withCredentials=!0),t.headers.forEach((t,e)=>n.setRequestHeader(t,e.join(","))),t.headers.has("Accept")||n.setRequestHeader("Accept","application/json, text/plain, */*"),!t.headers.has("Content-Type")){const e=t.detectContentTypeHeader();null!==e&&n.setRequestHeader("Content-Type",e)}if(t.responseType){const e=t.responseType.toLowerCase();n.responseType="json"!==e?e:"text"}const r=t.serializeBody();let s=null;const i=()=>{if(null!==s)return s;const e=1223===n.status?204:n.status,r=n.statusText||"OK",i=new d(n.getAllResponseHeaders()),o=function(t){return"responseURL"in t&&t.responseURL?t.responseURL:/^X-Request-URL:/m.test(t.getAllResponseHeaders())?t.getResponseHeader("X-Request-URL"):null}(n)||t.url;return s=new S({headers:i,status:e,statusText:r,url:o}),s},o=()=>{let{headers:r,status:s,statusText:o,url:a}=i(),c=null;204!==s&&(c=void 0===n.response?n.responseText:n.response),0===s&&(s=c?200:0);let l=s>=200&&s<300;if("json"===t.responseType&&"string"==typeof c){const t=c;c=c.replace(I,"");try{c=""!==c?JSON.parse(c):null}catch(u){c=t,l&&(l=!1,c={error:u,text:c})}}l?(e.next(new C({body:c,headers:r,status:s,statusText:o,url:a||void 0})),e.complete()):e.error(new E({error:c,headers:r,status:s,statusText:o,url:a||void 0}))},a=t=>{const{url:r}=i(),s=new E({error:t,status:n.status||0,statusText:n.statusText||"Unknown Error",url:r||void 0});e.error(s)};let c=!1;const l=r=>{c||(e.next(i()),c=!0);let s={type:v.DownloadProgress,loaded:r.loaded};r.lengthComputable&&(s.total=r.total),"text"===t.responseType&&n.responseText&&(s.partialText=n.responseText),e.next(s)},u=t=>{let n={type:v.UploadProgress,loaded:t.loaded};t.lengthComputable&&(n.total=t.total),e.next(n)};return n.addEventListener("load",o),n.addEventListener("error",a),n.addEventListener("timeout",a),n.addEventListener("abort",a),t.reportProgress&&(n.addEventListener("progress",l),null!==r&&n.upload&&n.upload.addEventListener("progress",u)),n.send(r),e.next({type:v.Sent}),()=>{n.removeEventListener("error",a),n.removeEventListener("abort",a),n.removeEventListener("load",o),n.removeEventListener("timeout",a),t.reportProgress&&(n.removeEventListener("progress",l),null!==r&&n.upload&&n.upload.removeEventListener("progress",u)),n.readyState!==n.DONE&&n.abort()}})}}return t.\u0275fac=function(e){return new(e||t)(r.ec(A))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})();const D=new r.v("XSRF_COOKIE_NAME"),N=new r.v("XSRF_HEADER_NAME");class F{}let L=(()=>{class t{constructor(t,e,n){this.doc=t,this.platform=e,this.cookieName=n,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Object(l.z)(t,this.cookieName),this.lastCookieString=t),this.lastToken}}return t.\u0275fac=function(e){return new(e||t)(r.ec(l.d),r.ec(r.J),r.ec(D))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})(),M=(()=>{class t{constructor(t,e){this.tokenService=t,this.headerName=e}intercept(t,e){const n=t.url.toLowerCase();if("GET"===t.method||"HEAD"===t.method||n.startsWith("http://")||n.startsWith("https://"))return e.handle(t);const r=this.tokenService.getToken();return null===r||t.headers.has(this.headerName)||(t=t.clone({headers:t.headers.set(this.headerName,r)})),e.handle(t)}}return t.\u0275fac=function(e){return new(e||t)(r.ec(F),r.ec(N))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})(),U=(()=>{class t{constructor(t,e){this.backend=t,this.injector=e,this.chain=null}handle(t){if(null===this.chain){const t=this.injector.get(k,[]);this.chain=t.reduceRight((t,e)=>new x(t,e),this.backend)}return this.chain.handle(t)}}return t.\u0275fac=function(e){return new(e||t)(r.ec(h),r.ec(r.w))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac}),t})(),H=(()=>{class t{static disable(){return{ngModule:t,providers:[{provide:M,useClass:j}]}}static withOptions(e={}){return{ngModule:t,providers:[e.cookieName?{provide:D,useValue:e.cookieName}:[],e.headerName?{provide:N,useValue:e.headerName}:[]]}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=r.Sb({type:t}),t.\u0275inj=r.Rb({providers:[M,{provide:k,useExisting:M,multi:!0},{provide:F,useClass:L},{provide:D,useValue:"XSRF-TOKEN"},{provide:N,useValue:"X-XSRF-TOKEN"}]}),t})(),$=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=r.Sb({type:t}),t.\u0275inj=r.Rb({providers:[T,{provide:u,useClass:U},R,{provide:h,useExisting:R},P,{provide:A,useExisting:P}],imports:[[H.withOptions({cookieName:"XSRF-TOKEN",headerName:"X-XSRF-TOKEN"})]]}),t})()},tyNb:function(t,e,n){"use strict";n.d(e,"a",function(){return Zt}),n.d(e,"b",function(){return Q}),n.d(e,"c",function(){return pn}),n.d(e,"d",function(){return mn}),n.d(e,"e",function(){return gn}),n.d(e,"f",function(){return kn}),n.d(e,"g",function(){return bn});var r=n("ofXK"),s=n("fXoL"),i=n("Cfvw"),o=n("LRne"),a=n("2Vo4"),c=n("z+Ro"),l=n("DH7j"),u=n("l7GE"),h=n("ZUHj"),d=n("yCtX");const f={};class p{constructor(t){this.resultSelector=t}call(t,e){return e.subscribe(new m(t,this.resultSelector))}}class m extends u.a{constructor(t,e){super(t),this.resultSelector=e,this.active=0,this.values=[],this.observables=[]}_next(t){this.values.push(f),this.observables.push(t)}_complete(){const t=this.observables,e=t.length;if(0===e)this.destination.complete();else{this.active=e,this.toRespond=e;for(let n=0;n<e;n++){const e=t[n];this.add(Object(h.a)(this,e,e,n))}}}notifyComplete(t){0==(this.active-=1)&&this.destination.complete()}notifyNext(t,e,n,r,s){const i=this.values,o=this.toRespond?i[n]===f?--this.toRespond:this.toRespond:0;i[n]=e,0===o&&(this.resultSelector?this._tryResultSelector(i):this.destination.next(i.slice()))}_tryResultSelector(t){let e;try{e=this.resultSelector.apply(this,t)}catch(n){return void this.destination.error(n)}this.destination.next(e)}}var g=n("HDdC"),y=n("sVev"),b=n("GyhO"),_=n("EY2u");function v(t){return new g.a(e=>{let n;try{n=t()}catch(r){return void e.error(r)}return(n?Object(i.a)(n):Object(_.b)()).subscribe(e)})}var w=n("EQ5u"),S=n("XNiG"),C=n("lJxs"),E=n("eIep"),O=n("IzEk"),T=n("JX91"),x=n("Kqap"),k=n("pLZG"),j=n("JIr8"),I=n("bOdf"),A=n("BFxc"),P=n("XDbj"),R=n("xbPD"),D=n("SpAZ"),N=n("SxV6"),F=n("5+tZ"),L=n("vkgz"),M=n("x+ZX"),U=n("7o/Q"),H=n("quSY");class ${constructor(t){this.callback=t}call(t,e){return e.subscribe(new V(t,this.callback))}}class V extends U.a{constructor(t,e){super(t),this.add(new H.a(e))}}var z=n("bHdf");class B{constructor(t,e){this.id=t,this.url=e}}class q extends B{constructor(t,e,n="imperative",r=null){super(t,e),this.navigationTrigger=n,this.restoredState=r}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class Q extends B{constructor(t,e,n){super(t,e),this.urlAfterRedirects=n}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class G extends B{constructor(t,e,n){super(t,e),this.reason=n}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class Z extends B{constructor(t,e,n){super(t,e),this.error=n}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class W extends B{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class K extends B{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class J extends B{constructor(t,e,n,r,s){super(t,e),this.urlAfterRedirects=n,this.state=r,this.shouldActivate=s}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class Y extends B{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class X extends B{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class tt{constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class et{constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class nt{constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class rt{constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class st{constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class it{constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class ot{constructor(t,e,n){this.routerEvent=t,this.position=e,this.anchor=n}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class at{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){const e=this.params[t];return Array.isArray(e)?e[0]:e}return null}getAll(t){if(this.has(t)){const e=this.params[t];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}}function ct(t){return new at(t)}function lt(t){const e=Error("NavigationCancelingError: "+t);return e.ngNavigationCancelingError=!0,e}function ut(t,e,n){const r=n.path.split("/");if(r.length>t.length)return null;if("full"===n.pathMatch&&(e.hasChildren()||r.length<t.length))return null;const s={};for(let i=0;i<r.length;i++){const e=r[i],n=t[i];if(e.startsWith(":"))s[e.substring(1)]=n;else if(e!==n.path)return null}return{consumed:t.slice(0,r.length),posParams:s}}function ht(t,e){const n=t?Object.keys(t):void 0,r=e?Object.keys(e):void 0;if(!n||!r||n.length!=r.length)return!1;let s;for(let i=0;i<n.length;i++)if(s=n[i],!dt(t[s],e[s]))return!1;return!0}function dt(t,e){if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return!1;const n=[...t].sort(),r=[...e].sort();return n.every((t,e)=>r[e]===t)}return t===e}function ft(t){return Array.prototype.concat.apply([],t)}function pt(t){return t.length>0?t[t.length-1]:null}function mt(t,e){for(const n in t)t.hasOwnProperty(n)&&e(t[n],n)}function gt(t){return Object(s.xb)(t)?t:Object(s.yb)(t)?Object(i.a)(Promise.resolve(t)):Object(o.a)(t)}function yt(t,e,n){return n?function(t,e){return ht(t,e)}(t.queryParams,e.queryParams)&&bt(t.root,e.root):function(t,e){return Object.keys(e).length<=Object.keys(t).length&&Object.keys(e).every(n=>dt(t[n],e[n]))}(t.queryParams,e.queryParams)&&_t(t.root,e.root)}function bt(t,e){if(!Et(t.segments,e.segments))return!1;if(t.numberOfChildren!==e.numberOfChildren)return!1;for(const n in e.children){if(!t.children[n])return!1;if(!bt(t.children[n],e.children[n]))return!1}return!0}function _t(t,e){return vt(t,e,e.segments)}function vt(t,e,n){if(t.segments.length>n.length)return!!Et(t.segments.slice(0,n.length),n)&&!e.hasChildren();if(t.segments.length===n.length){if(!Et(t.segments,n))return!1;for(const n in e.children){if(!t.children[n])return!1;if(!_t(t.children[n],e.children[n]))return!1}return!0}{const r=n.slice(0,t.segments.length),s=n.slice(t.segments.length);return!!Et(t.segments,r)&&!!t.children.primary&&vt(t.children.primary,e,s)}}class wt{constructor(t,e,n){this.root=t,this.queryParams=e,this.fragment=n}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=ct(this.queryParams)),this._queryParamMap}toString(){return xt.serialize(this)}}class St{constructor(t,e){this.segments=t,this.children=e,this.parent=null,mt(e,(t,e)=>t.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return kt(this)}}class Ct{constructor(t,e){this.path=t,this.parameters=e}get parameterMap(){return this._parameterMap||(this._parameterMap=ct(this.parameters)),this._parameterMap}toString(){return Nt(this)}}function Et(t,e){return t.length===e.length&&t.every((t,n)=>t.path===e[n].path)}class Ot{}class Tt{parse(t){const e=new Ht(t);return new wt(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(t){var e;return`/${jt(t.root,!0)}${function(t){const e=Object.keys(t).map(e=>{const n=t[e];return Array.isArray(n)?n.map(t=>`${At(e)}=${At(t)}`).join("&"):`${At(e)}=${At(n)}`});return e.length?`?${e.join("&")}`:""}(t.queryParams)}${"string"==typeof t.fragment?`#${e=t.fragment,encodeURI(e)}`:""}`}}const xt=new Tt;function kt(t){return t.segments.map(t=>Nt(t)).join("/")}function jt(t,e){if(!t.hasChildren())return kt(t);if(e){const e=t.children.primary?jt(t.children.primary,!1):"",n=[];return mt(t.children,(t,e)=>{"primary"!==e&&n.push(`${e}:${jt(t,!1)}`)}),n.length>0?`${e}(${n.join("//")})`:e}{const e=function(t,e){let n=[];return mt(t.children,(t,r)=>{"primary"===r&&(n=n.concat(e(t,r)))}),mt(t.children,(t,r)=>{"primary"!==r&&(n=n.concat(e(t,r)))}),n}(t,(e,n)=>"primary"===n?[jt(t.children.primary,!1)]:[`${n}:${jt(e,!1)}`]);return 1===Object.keys(t.children).length&&null!=t.children.primary?`${kt(t)}/${e[0]}`:`${kt(t)}/(${e.join("//")})`}}function It(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function At(t){return It(t).replace(/%3B/gi,";")}function Pt(t){return It(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Rt(t){return decodeURIComponent(t)}function Dt(t){return Rt(t.replace(/\+/g,"%20"))}function Nt(t){return`${Pt(t.path)}${e=t.parameters,Object.keys(e).map(t=>`;${Pt(t)}=${Pt(e[t])}`).join("")}`;var e}const Ft=/^[^\/()?;=#]+/;function Lt(t){const e=t.match(Ft);return e?e[0]:""}const Mt=/^[^=?&#]+/,Ut=/^[^?&#]+/;class Ht{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new St([],{}):new St([],this.parseChildren())}parseQueryParams(){const t={};if(this.consumeOptional("?"))do{this.parseQueryParam(t)}while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(t.length>0||Object.keys(e).length>0)&&(n.primary=new St(t,e)),n}parseSegment(){const t=Lt(this.remaining);if(""===t&&this.peekStartsWith(";"))throw new Error(`Empty path url segment cannot have parameters: '${this.remaining}'.`);return this.capture(t),new Ct(Rt(t),this.parseMatrixParams())}parseMatrixParams(){const t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){const e=Lt(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){const t=Lt(this.remaining);t&&(n=t,this.capture(n))}t[Rt(e)]=Rt(n)}parseQueryParam(t){const e=function(t){const e=t.match(Mt);return e?e[0]:""}(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){const t=function(t){const e=t.match(Ut);return e?e[0]:""}(this.remaining);t&&(n=t,this.capture(n))}const r=Dt(e),s=Dt(n);if(t.hasOwnProperty(r)){let e=t[r];Array.isArray(e)||(e=[e],t[r]=e),e.push(s)}else t[r]=s}parseParens(t){const e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const n=Lt(this.remaining),r=this.remaining[n.length];if("/"!==r&&")"!==r&&";"!==r)throw new Error(`Cannot parse url '${this.url}'`);let s;n.indexOf(":")>-1?(s=n.substr(0,n.indexOf(":")),this.capture(s),this.capture(":")):t&&(s="primary");const i=this.parseChildren();e[s]=1===Object.keys(i).length?i.primary:new St([],i),this.consumeOptional("//")}return e}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return!!this.peekStartsWith(t)&&(this.remaining=this.remaining.substring(t.length),!0)}capture(t){if(!this.consumeOptional(t))throw new Error(`Expected "${t}".`)}}class $t{constructor(t){this._root=t}get root(){return this._root.value}parent(t){const e=this.pathFromRoot(t);return e.length>1?e[e.length-2]:null}children(t){const e=Vt(t,this._root);return e?e.children.map(t=>t.value):[]}firstChild(t){const e=Vt(t,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(t){const e=zt(t,this._root);return e.length<2?[]:e[e.length-2].children.map(t=>t.value).filter(e=>e!==t)}pathFromRoot(t){return zt(t,this._root).map(t=>t.value)}}function Vt(t,e){if(t===e.value)return e;for(const n of e.children){const e=Vt(t,n);if(e)return e}return null}function zt(t,e){if(t===e.value)return[e];for(const n of e.children){const r=zt(t,n);if(r.length)return r.unshift(e),r}return[]}class Bt{constructor(t,e){this.value=t,this.children=e}toString(){return`TreeNode(${this.value})`}}function qt(t){const e={};return t&&t.children.forEach(t=>e[t.value.outlet]=t),e}class Qt extends $t{constructor(t,e){super(t),this.snapshot=e,Yt(this,t)}toString(){return this.snapshot.toString()}}function Gt(t,e){const n=function(t,e){const n=new Kt([],{},{},"",{},"primary",e,null,t.root,-1,{});return new Jt("",new Bt(n,[]))}(t,e),r=new a.a([new Ct("",{})]),s=new a.a({}),i=new a.a({}),o=new a.a({}),c=new a.a(""),l=new Zt(r,s,o,c,i,"primary",e,n.root);return l.snapshot=n.root,new Qt(new Bt(l,[]),n)}class Zt{constructor(t,e,n,r,s,i,o,a){this.url=t,this.params=e,this.queryParams=n,this.fragment=r,this.data=s,this.outlet=i,this.component=o,this._futureSnapshot=a}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe(Object(C.a)(t=>ct(t)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe(Object(C.a)(t=>ct(t)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function Wt(t,e="emptyOnly"){const n=t.pathFromRoot;let r=0;if("always"!==e)for(r=n.length-1;r>=1;){const t=n[r],e=n[r-1];if(t.routeConfig&&""===t.routeConfig.path)r--;else{if(e.component)break;r--}}return function(t){return t.reduce((t,e)=>({params:Object.assign(Object.assign({},t.params),e.params),data:Object.assign(Object.assign({},t.data),e.data),resolve:Object.assign(Object.assign({},t.resolve),e._resolvedData)}),{params:{},data:{},resolve:{}})}(n.slice(r))}class Kt{constructor(t,e,n,r,s,i,o,a,c,l,u){this.url=t,this.params=e,this.queryParams=n,this.fragment=r,this.data=s,this.outlet=i,this.component=o,this.routeConfig=a,this._urlSegment=c,this._lastPathIndex=l,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=ct(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=ct(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(t=>t.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class Jt extends $t{constructor(t,e){super(e),this.url=t,Yt(this,e)}toString(){return Xt(this._root)}}function Yt(t,e){e.value._routerState=t,e.children.forEach(e=>Yt(t,e))}function Xt(t){const e=t.children.length>0?` { ${t.children.map(Xt).join(", ")} } `:"";return`${t.value}${e}`}function te(t){if(t.snapshot){const e=t.snapshot,n=t._futureSnapshot;t.snapshot=n,ht(e.queryParams,n.queryParams)||t.queryParams.next(n.queryParams),e.fragment!==n.fragment&&t.fragment.next(n.fragment),ht(e.params,n.params)||t.params.next(n.params),function(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;++n)if(!ht(t[n],e[n]))return!1;return!0}(e.url,n.url)||t.url.next(n.url),ht(e.data,n.data)||t.data.next(n.data)}else t.snapshot=t._futureSnapshot,t.data.next(t._futureSnapshot.data)}function ee(t,e){var n,r;return ht(t.params,e.params)&&Et(n=t.url,r=e.url)&&n.every((t,e)=>ht(t.parameters,r[e].parameters))&&!(!t.parent!=!e.parent)&&(!t.parent||ee(t.parent,e.parent))}function ne(t,e,n){if(n&&t.shouldReuseRoute(e.value,n.value.snapshot)){const r=n.value;r._futureSnapshot=e.value;const s=function(t,e,n){return e.children.map(e=>{for(const r of n.children)if(t.shouldReuseRoute(e.value,r.value.snapshot))return ne(t,e,r);return ne(t,e)})}(t,e,n);return new Bt(r,s)}{const n=t.retrieve(e.value);if(n){const t=n.route;return re(e,t),t}{const n=new Zt(new a.a((r=e.value).url),new a.a(r.params),new a.a(r.queryParams),new a.a(r.fragment),new a.a(r.data),r.outlet,r.component,r),s=e.children.map(e=>ne(t,e));return new Bt(n,s)}}var r}function re(t,e){if(t.value.routeConfig!==e.value.routeConfig)throw new Error("Cannot reattach ActivatedRouteSnapshot created from a different route");if(t.children.length!==e.children.length)throw new Error("Cannot reattach ActivatedRouteSnapshot with a different number of children");e.value._futureSnapshot=t.value;for(let n=0;n<t.children.length;++n)re(t.children[n],e.children[n])}function se(t){return"object"==typeof t&&null!=t&&!t.outlets&&!t.segmentPath}function ie(t){return"object"==typeof t&&null!=t&&t.outlets}function oe(t,e,n,r,s){let i={};return r&&mt(r,(t,e)=>{i[e]=Array.isArray(t)?t.map(t=>`${t}`):`${t}`}),new wt(n.root===t?e:ae(n.root,t,e),i,s)}function ae(t,e,n){const r={};return mt(t.children,(t,s)=>{r[s]=t===e?n:ae(t,e,n)}),new St(t.segments,r)}class ce{constructor(t,e,n){if(this.isAbsolute=t,this.numberOfDoubleDots=e,this.commands=n,t&&n.length>0&&se(n[0]))throw new Error("Root segment cannot have matrix parameters");const r=n.find(ie);if(r&&r!==pt(n))throw new Error("{outlets:{}} has to be the last command")}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class le{constructor(t,e,n){this.segmentGroup=t,this.processChildren=e,this.index=n}}function ue(t,e,n){if(t||(t=new St([],{})),0===t.segments.length&&t.hasChildren())return he(t,e,n);const r=function(t,e,n){let r=0,s=e;const i={match:!1,pathIndex:0,commandIndex:0};for(;s<t.segments.length;){if(r>=n.length)return i;const e=t.segments[s],o=n[r];if(ie(o))break;const a=`${o}`,c=r<n.length-1?n[r+1]:null;if(s>0&&void 0===a)break;if(a&&c&&"object"==typeof c&&void 0===c.outlets){if(!me(a,c,e))return i;r+=2}else{if(!me(a,{},e))return i;r++}s++}return{match:!0,pathIndex:s,commandIndex:r}}(t,e,n),s=n.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){const e=new St(t.segments.slice(0,r.pathIndex),{});return e.children.primary=new St(t.segments.slice(r.pathIndex),t.children),he(e,0,s)}return r.match&&0===s.length?new St(t.segments,{}):r.match&&!t.hasChildren()?de(t,e,n):r.match?he(t,0,s):de(t,e,n)}function he(t,e,n){if(0===n.length)return new St(t.segments,{});{const r=function(t){return ie(t[0])?t[0].outlets:{primary:t}}(n),s={};return mt(r,(n,r)=>{"string"==typeof n&&(n=[n]),null!==n&&(s[r]=ue(t.children[r],e,n))}),mt(t.children,(t,e)=>{void 0===r[e]&&(s[e]=t)}),new St(t.segments,s)}}function de(t,e,n){const r=t.segments.slice(0,e);let s=0;for(;s<n.length;){const i=n[s];if(ie(i)){const t=fe(i.outlets);return new St(r,t)}if(0===s&&se(n[0])){r.push(new Ct(t.segments[e].path,pe(n[0]))),s++;continue}const o=ie(i)?i.outlets.primary:`${i}`,a=s<n.length-1?n[s+1]:null;o&&a&&se(a)?(r.push(new Ct(o,pe(a))),s+=2):(r.push(new Ct(o,{})),s++)}return new St(r,{})}function fe(t){const e={};return mt(t,(t,n)=>{"string"==typeof t&&(t=[t]),null!==t&&(e[n]=de(new St([],{}),0,t))}),e}function pe(t){const e={};return mt(t,(t,n)=>e[n]=`${t}`),e}function me(t,e,n){return t==n.path&&ht(e,n.parameters)}class ge{constructor(t,e,n,r){this.routeReuseStrategy=t,this.futureState=e,this.currState=n,this.forwardEvent=r}activate(t){const e=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,n,t),te(this.futureState.root),this.activateChildRoutes(e,n,t)}deactivateChildRoutes(t,e,n){const r=qt(e);t.children.forEach(t=>{const e=t.value.outlet;this.deactivateRoutes(t,r[e],n),delete r[e]}),mt(r,(t,e)=>{this.deactivateRouteAndItsChildren(t,n)})}deactivateRoutes(t,e,n){const r=t.value,s=e?e.value:null;if(r===s)if(r.component){const s=n.getContext(r.outlet);s&&this.deactivateChildRoutes(t,e,s.children)}else this.deactivateChildRoutes(t,e,n);else s&&this.deactivateRouteAndItsChildren(e,n)}deactivateRouteAndItsChildren(t,e){this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,e):this.deactivateRouteAndOutlet(t,e)}detachAndStoreRouteSubtree(t,e){const n=e.getContext(t.value.outlet);if(n&&n.outlet){const e=n.outlet.detach(),r=n.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:e,route:t,contexts:r})}}deactivateRouteAndOutlet(t,e){const n=e.getContext(t.value.outlet),r=n&&t.value.component?n.children:e,s=qt(t);for(const i of Object.keys(s))this.deactivateRouteAndItsChildren(s[i],r);n&&n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated())}activateChildRoutes(t,e,n){const r=qt(e);t.children.forEach(t=>{this.activateRoutes(t,r[t.value.outlet],n),this.forwardEvent(new it(t.value.snapshot))}),t.children.length&&this.forwardEvent(new rt(t.value.snapshot))}activateRoutes(t,e,n){const r=t.value,s=e?e.value:null;if(te(r),r===s)if(r.component){const s=n.getOrCreateContext(r.outlet);this.activateChildRoutes(t,e,s.children)}else this.activateChildRoutes(t,e,n);else if(r.component){const e=n.getOrCreateContext(r.outlet);if(this.routeReuseStrategy.shouldAttach(r.snapshot)){const t=this.routeReuseStrategy.retrieve(r.snapshot);this.routeReuseStrategy.store(r.snapshot,null),e.children.onOutletReAttached(t.contexts),e.attachRef=t.componentRef,e.route=t.route.value,e.outlet&&e.outlet.attach(t.componentRef,t.route.value),ye(t.route)}else{const n=function(t){for(let e=t.parent;e;e=e.parent){const t=e.routeConfig;if(t&&t._loadedConfig)return t._loadedConfig;if(t&&t.component)return null}return null}(r.snapshot),s=n?n.module.componentFactoryResolver:null;e.attachRef=null,e.route=r,e.resolver=s,e.outlet&&e.outlet.activateWith(r,s),this.activateChildRoutes(t,null,e.children)}}else this.activateChildRoutes(t,null,n)}}function ye(t){te(t.value),t.children.forEach(ye)}class be{constructor(t,e){this.routes=t,this.module=e}}function _e(t){return"function"==typeof t}function ve(t){return t instanceof wt}const we=Symbol("INITIAL_VALUE");function Se(){return Object(E.a)(t=>function(...t){let e=null,n=null;return Object(c.a)(t[t.length-1])&&(n=t.pop()),"function"==typeof t[t.length-1]&&(e=t.pop()),1===t.length&&Object(l.a)(t[0])&&(t=t[0]),Object(d.a)(t,n).lift(new p(e))}(t.map(t=>t.pipe(Object(O.a)(1),Object(T.a)(we)))).pipe(Object(x.a)((t,e)=>{let n=!1;return e.reduce((t,r,s)=>{if(t!==we)return t;if(r===we&&(n=!0),!n){if(!1===r)return r;if(s===e.length-1||ve(r))return r}return t},t)},we),Object(k.a)(t=>t!==we),Object(C.a)(t=>ve(t)?t:!0===t),Object(O.a)(1)))}let Ce=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.Ob({type:t,selectors:[["ng-component"]],decls:1,vars:0,template:function(t,e){1&t&&s.Vb(0,"router-outlet")},directives:function(){return[bn]},encapsulation:2}),t})();function Ee(t,e=""){for(let n=0;n<t.length;n++){const r=t[n];Oe(r,Te(e,r))}}function Oe(t,e){t.children&&Ee(t.children,e)}function Te(t,e){return e?t||e.path?t&&!e.path?`${t}/`:!t&&e.path?e.path:`${t}/${e.path}`:"":t}function xe(t){const e=t.children&&t.children.map(xe),n=e?Object.assign(Object.assign({},t),{children:e}):Object.assign({},t);return!n.component&&(e||n.loadChildren)&&n.outlet&&"primary"!==n.outlet&&(n.component=Ce),n}function ke(t){return t.outlet||"primary"}function je(t,e){const n=t.filter(t=>ke(t)===e);return n.push(...t.filter(t=>ke(t)!==e)),n}const Ie={matched:!1,consumedSegments:[],lastChild:0,parameters:{},positionalParamSegments:{}};function Ae(t,e,n){var r;if(""===e.path)return"full"===e.pathMatch&&(t.hasChildren()||n.length>0)?Object.assign({},Ie):{matched:!0,consumedSegments:[],lastChild:0,parameters:{},positionalParamSegments:{}};const s=(e.matcher||ut)(n,t,e);if(!s)return Object.assign({},Ie);const i={};mt(s.posParams,(t,e)=>{i[e]=t.path});const o=s.consumed.length>0?Object.assign(Object.assign({},i),s.consumed[s.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:s.consumed,lastChild:s.consumed.length,parameters:o,positionalParamSegments:null!==(r=s.posParams)&&void 0!==r?r:{}}}function Pe(t,e,n,r,s="corrected"){if(n.length>0&&function(t,e,n){return n.some(n=>Re(t,e,n)&&"primary"!==ke(n))}(t,n,r)){const s=new St(e,function(t,e,n,r){const s={};s.primary=r,r._sourceSegment=t,r._segmentIndexShift=e.length;for(const i of n)if(""===i.path&&"primary"!==ke(i)){const n=new St([],{});n._sourceSegment=t,n._segmentIndexShift=e.length,s[ke(i)]=n}return s}(t,e,r,new St(n,t.children)));return s._sourceSegment=t,s._segmentIndexShift=e.length,{segmentGroup:s,slicedSegments:[]}}if(0===n.length&&function(t,e,n){return n.some(n=>Re(t,e,n))}(t,n,r)){const i=new St(t.segments,function(t,e,n,r,s,i){const o={};for(const a of r)if(Re(t,n,a)&&!s[ke(a)]){const n=new St([],{});n._sourceSegment=t,n._segmentIndexShift="legacy"===i?t.segments.length:e.length,o[ke(a)]=n}return Object.assign(Object.assign({},s),o)}(t,e,n,r,t.children,s));return i._sourceSegment=t,i._segmentIndexShift=e.length,{segmentGroup:i,slicedSegments:n}}const i=new St(t.segments,t.children);return i._sourceSegment=t,i._segmentIndexShift=e.length,{segmentGroup:i,slicedSegments:n}}function Re(t,e,n){return(!(t.hasChildren()||e.length>0)||"full"!==n.pathMatch)&&""===n.path}function De(t,e,n,r){return!!(ke(t)===r||"primary"!==r&&Re(e,n,t))&&("**"===t.path||Ae(e,t,n).matched)}function Ne(t,e,n){return 0===e.length&&!t.children[n]}class Fe{constructor(t){this.segmentGroup=t||null}}class Le{constructor(t){this.urlTree=t}}function Me(t){return new g.a(e=>e.error(new Fe(t)))}function Ue(t){return new g.a(e=>e.error(new Le(t)))}function He(t){return new g.a(e=>e.error(new Error(`Only absolute redirects can have named outlets. redirectTo: '${t}'`)))}class $e{constructor(t,e,n,r,i){this.configLoader=e,this.urlSerializer=n,this.urlTree=r,this.config=i,this.allowRedirects=!0,this.ngModule=t.get(s.E)}apply(){const t=Pe(this.urlTree.root,[],[],this.config).segmentGroup,e=new St(t.segments,t.children);return this.expandSegmentGroup(this.ngModule,this.config,e,"primary").pipe(Object(C.a)(t=>this.createUrlTree(Ve(t),this.urlTree.queryParams,this.urlTree.fragment))).pipe(Object(j.a)(t=>{if(t instanceof Le)return this.allowRedirects=!1,this.match(t.urlTree);if(t instanceof Fe)throw this.noMatchError(t);throw t}))}match(t){return this.expandSegmentGroup(this.ngModule,this.config,t.root,"primary").pipe(Object(C.a)(e=>this.createUrlTree(Ve(e),t.queryParams,t.fragment))).pipe(Object(j.a)(t=>{if(t instanceof Fe)throw this.noMatchError(t);throw t}))}noMatchError(t){return new Error(`Cannot match any routes. URL Segment: '${t.segmentGroup}'`)}createUrlTree(t,e,n){const r=t.segments.length>0?new St([],{primary:t}):t;return new wt(r,e,n)}expandSegmentGroup(t,e,n,r){return 0===n.segments.length&&n.hasChildren()?this.expandChildren(t,e,n).pipe(Object(C.a)(t=>new St([],t))):this.expandSegment(t,n,e,n.segments,r,!0)}expandChildren(t,e,n){const r=[];for(const s of Object.keys(n.children))"primary"===s?r.unshift(s):r.push(s);return Object(i.a)(r).pipe(Object(I.a)(r=>{const s=n.children[r],i=je(e,r);return this.expandSegmentGroup(t,i,s,r).pipe(Object(C.a)(t=>({segment:t,outlet:r})))}),Object(x.a)((t,e)=>(t[e.outlet]=e.segment,t),{}),function(t,e){const n=arguments.length>=2;return r=>r.pipe(t?Object(k.a)((e,n)=>t(e,n,r)):D.a,Object(A.a)(1),n?Object(R.a)(e):Object(P.a)(()=>new y.a))}())}expandSegment(t,e,n,r,s,a){return Object(i.a)(n).pipe(Object(I.a)(i=>this.expandSegmentAgainstRoute(t,e,n,i,r,s,a).pipe(Object(j.a)(t=>{if(t instanceof Fe)return Object(o.a)(null);throw t}))),Object(N.a)(t=>!!t),Object(j.a)((t,n)=>{if(t instanceof y.a||"EmptyError"===t.name){if(Ne(e,r,s))return Object(o.a)(new St([],{}));throw new Fe(e)}throw t}))}expandSegmentAgainstRoute(t,e,n,r,s,i,o){return De(r,e,s,i)?void 0===r.redirectTo?this.matchSegmentAgainstRoute(t,e,r,s,i):o&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i):Me(e):Me(e)}expandSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i){return"**"===r.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(t,n,r,i):this.expandRegularSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i)}expandWildCardWithParamsAgainstRouteUsingRedirect(t,e,n,r){const s=this.applyRedirectCommands([],n.redirectTo,{});return n.redirectTo.startsWith("/")?Ue(s):this.lineralizeSegments(n,s).pipe(Object(F.a)(n=>{const s=new St(n,{});return this.expandSegment(t,s,e,n,r,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i){const{matched:o,consumedSegments:a,lastChild:c,positionalParamSegments:l}=Ae(e,r,s);if(!o)return Me(e);const u=this.applyRedirectCommands(a,r.redirectTo,l);return r.redirectTo.startsWith("/")?Ue(u):this.lineralizeSegments(r,u).pipe(Object(F.a)(r=>this.expandSegment(t,e,n,r.concat(s.slice(c)),i,!1)))}matchSegmentAgainstRoute(t,e,n,r,s){if("**"===n.path)return n.loadChildren?(n._loadedConfig?Object(o.a)(n._loadedConfig):this.configLoader.load(t.injector,n)).pipe(Object(C.a)(t=>(n._loadedConfig=t,new St(r,{})))):Object(o.a)(new St(r,{}));const{matched:i,consumedSegments:a,lastChild:c}=Ae(e,n,r);if(!i)return Me(e);const l=r.slice(c);return this.getChildConfig(t,n,r).pipe(Object(F.a)(t=>{const r=t.module,i=t.routes,{segmentGroup:c,slicedSegments:u}=Pe(e,a,l,i),h=new St(c.segments,c.children);if(0===u.length&&h.hasChildren())return this.expandChildren(r,i,h).pipe(Object(C.a)(t=>new St(a,t)));if(0===i.length&&0===u.length)return Object(o.a)(new St(a,{}));const d=ke(n)===s;return this.expandSegment(r,h,i,u,d?"primary":s,!0).pipe(Object(C.a)(t=>new St(a.concat(t.segments),t.children)))}))}getChildConfig(t,e,n){return e.children?Object(o.a)(new be(e.children,t)):e.loadChildren?void 0!==e._loadedConfig?Object(o.a)(e._loadedConfig):this.runCanLoadGuards(t.injector,e,n).pipe(Object(F.a)(n=>n?this.configLoader.load(t.injector,e).pipe(Object(C.a)(t=>(e._loadedConfig=t,t))):function(t){return new g.a(e=>e.error(lt(`Cannot load children because the guard of the route "path: '${t.path}'" returned false`)))}(e))):Object(o.a)(new be([],t))}runCanLoadGuards(t,e,n){const r=e.canLoad;if(!r||0===r.length)return Object(o.a)(!0);const s=r.map(r=>{const s=t.get(r);let i;if(function(t){return t&&_e(t.canLoad)}(s))i=s.canLoad(e,n);else{if(!_e(s))throw new Error("Invalid CanLoad guard");i=s(e,n)}return gt(i)});return Object(o.a)(s).pipe(Se(),Object(L.a)(t=>{if(!ve(t))return;const e=lt(`Redirecting to "${this.urlSerializer.serialize(t)}"`);throw e.url=t,e}),Object(C.a)(t=>!0===t))}lineralizeSegments(t,e){let n=[],r=e.root;for(;;){if(n=n.concat(r.segments),0===r.numberOfChildren)return Object(o.a)(n);if(r.numberOfChildren>1||!r.children.primary)return He(t.redirectTo);r=r.children.primary}}applyRedirectCommands(t,e,n){return this.applyRedirectCreatreUrlTree(e,this.urlSerializer.parse(e),t,n)}applyRedirectCreatreUrlTree(t,e,n,r){const s=this.createSegmentGroup(t,e.root,n,r);return new wt(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(t,e){const n={};return mt(t,(t,r)=>{if("string"==typeof t&&t.startsWith(":")){const s=t.substring(1);n[r]=e[s]}else n[r]=t}),n}createSegmentGroup(t,e,n,r){const s=this.createSegments(t,e.segments,n,r);let i={};return mt(e.children,(e,s)=>{i[s]=this.createSegmentGroup(t,e,n,r)}),new St(s,i)}createSegments(t,e,n,r){return e.map(e=>e.path.startsWith(":")?this.findPosParam(t,e,r):this.findOrReturn(e,n))}findPosParam(t,e,n){const r=n[e.path.substring(1)];if(!r)throw new Error(`Cannot redirect to '${t}'. Cannot find '${e.path}'.`);return r}findOrReturn(t,e){let n=0;for(const r of e){if(r.path===t.path)return e.splice(n),r;n++}return t}}function Ve(t){const e={};for(const n of Object.keys(t.children)){const r=Ve(t.children[n]);(r.segments.length>0||r.hasChildren())&&(e[n]=r)}return function(t){if(1===t.numberOfChildren&&t.children.primary){const e=t.children.primary;return new St(t.segments.concat(e.segments),e.children)}return t}(new St(t.segments,e))}class ze{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}}class Be{constructor(t,e){this.component=t,this.route=e}}function qe(t,e,n){const r=t._root;return Ge(r,e?e._root:null,n,[r.value])}function Qe(t,e,n){const r=function(t){if(!t)return null;for(let e=t.parent;e;e=e.parent){const t=e.routeConfig;if(t&&t._loadedConfig)return t._loadedConfig}return null}(e);return(r?r.module.injector:n).get(t)}function Ge(t,e,n,r,s={canDeactivateChecks:[],canActivateChecks:[]}){const i=qt(e);return t.children.forEach(t=>{!function(t,e,n,r,s={canDeactivateChecks:[],canActivateChecks:[]}){const i=t.value,o=e?e.value:null,a=n?n.getContext(t.value.outlet):null;if(o&&i.routeConfig===o.routeConfig){const c=function(t,e,n){if("function"==typeof n)return n(t,e);switch(n){case"pathParamsChange":return!Et(t.url,e.url);case"pathParamsOrQueryParamsChange":return!Et(t.url,e.url)||!ht(t.queryParams,e.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!ee(t,e)||!ht(t.queryParams,e.queryParams);case"paramsChange":default:return!ee(t,e)}}(o,i,i.routeConfig.runGuardsAndResolvers);c?s.canActivateChecks.push(new ze(r)):(i.data=o.data,i._resolvedData=o._resolvedData),Ge(t,e,i.component?a?a.children:null:n,r,s),c&&a&&a.outlet&&a.outlet.isActivated&&s.canDeactivateChecks.push(new Be(a.outlet.component,o))}else o&&Ze(e,a,s),s.canActivateChecks.push(new ze(r)),Ge(t,null,i.component?a?a.children:null:n,r,s)}(t,i[t.value.outlet],n,r.concat([t.value]),s),delete i[t.value.outlet]}),mt(i,(t,e)=>Ze(t,n.getContext(e),s)),s}function Ze(t,e,n){const r=qt(t),s=t.value;mt(r,(t,r)=>{Ze(t,s.component?e?e.children.getContext(r):null:e,n)}),n.canDeactivateChecks.push(new Be(s.component&&e&&e.outlet&&e.outlet.isActivated?e.outlet.component:null,s))}class We{}function Ke(t){return new g.a(e=>e.error(t))}class Je{constructor(t,e,n,r,s,i){this.rootComponentType=t,this.config=e,this.urlTree=n,this.url=r,this.paramsInheritanceStrategy=s,this.relativeLinkResolution=i}recognize(){const t=Pe(this.urlTree.root,[],[],this.config.filter(t=>void 0===t.redirectTo),this.relativeLinkResolution).segmentGroup,e=this.processSegmentGroup(this.config,t,"primary");if(null===e)return null;const n=new Kt([],Object.freeze({}),Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,{},"primary",this.rootComponentType,null,this.urlTree.root,-1,{}),r=new Bt(n,e),s=new Jt(this.url,r);return this.inheritParamsAndData(s._root),s}inheritParamsAndData(t){const e=t.value,n=Wt(e,this.paramsInheritanceStrategy);e.params=Object.freeze(n.params),e.data=Object.freeze(n.data),t.children.forEach(t=>this.inheritParamsAndData(t))}processSegmentGroup(t,e,n){return 0===e.segments.length&&e.hasChildren()?this.processChildren(t,e):this.processSegment(t,e,e.segments,n)}processChildren(t,e){const n=[];for(const s of Object.keys(e.children)){const r=e.children[s],i=je(t,s),o=this.processSegmentGroup(i,r,s);if(null===o)return null;n.push(...o)}const r=function(t){const e=[];for(const n of t){if(!Ye(n)){e.push(n);continue}const t=e.find(t=>n.value.routeConfig===t.value.routeConfig);void 0!==t?t.children.push(...n.children):e.push(n)}return e}(n);return r.sort((t,e)=>"primary"===t.value.outlet?-1:"primary"===e.value.outlet?1:t.value.outlet.localeCompare(e.value.outlet)),r}processSegment(t,e,n,r){for(const s of t){const t=this.processSegmentAgainstRoute(s,e,n,r);if(null!==t)return t}return Ne(e,n,r)?[]:null}processSegmentAgainstRoute(t,e,n,r){if(t.redirectTo||!De(t,e,n,r))return null;let s,i=[],o=[];if("**"===t.path){const r=n.length>0?pt(n).parameters:{};s=new Kt(n,r,Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,en(t),ke(t),t.component,t,Xe(e),tn(e)+n.length,nn(t))}else{const r=Ae(e,t,n);if(!r.matched)return null;i=r.consumedSegments,o=n.slice(r.lastChild),s=new Kt(i,r.parameters,Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,en(t),ke(t),t.component,t,Xe(e),tn(e)+i.length,nn(t))}const a=function(t){return t.children?t.children:t.loadChildren?t._loadedConfig.routes:[]}(t),{segmentGroup:c,slicedSegments:l}=Pe(e,i,o,a.filter(t=>void 0===t.redirectTo),this.relativeLinkResolution);if(0===l.length&&c.hasChildren()){const t=this.processChildren(a,c);return null===t?null:[new Bt(s,t)]}if(0===a.length&&0===l.length)return[new Bt(s,[])];const u=ke(t)===r,h=this.processSegment(a,c,l,u?"primary":r);return null===h?null:[new Bt(s,h)]}}function Ye(t){const e=t.value.routeConfig;return e&&""===e.path&&void 0===e.redirectTo}function Xe(t){let e=t;for(;e._sourceSegment;)e=e._sourceSegment;return e}function tn(t){let e=t,n=e._segmentIndexShift?e._segmentIndexShift:0;for(;e._sourceSegment;)e=e._sourceSegment,n+=e._segmentIndexShift?e._segmentIndexShift:0;return n-1}function en(t){return t.data||{}}function nn(t){return t.resolve||{}}function rn(t){return Object(E.a)(e=>{const n=t(e);return n?Object(i.a)(n).pipe(Object(C.a)(()=>e)):Object(o.a)(e)})}class sn extends class{shouldDetach(t){return!1}store(t,e){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,e){return t.routeConfig===e.routeConfig}}{}const on=new s.v("ROUTES");class an{constructor(t,e,n,r){this.loader=t,this.compiler=e,this.onLoadStartListener=n,this.onLoadEndListener=r}load(t,e){if(e._loader$)return e._loader$;this.onLoadStartListener&&this.onLoadStartListener(e);const n=this.loadModuleFactory(e.loadChildren).pipe(Object(C.a)(n=>{this.onLoadEndListener&&this.onLoadEndListener(e);const r=n.create(t);return new be(ft(r.injector.get(on,void 0,s.t.Self|s.t.Optional)).map(xe),r)}),Object(j.a)(t=>{throw e._loader$=void 0,t}));return e._loader$=new w.a(n,()=>new S.a).pipe(Object(M.a)()),e._loader$}loadModuleFactory(t){return"string"==typeof t?Object(i.a)(this.loader.load(t)):gt(t()).pipe(Object(F.a)(t=>t instanceof s.C?Object(o.a)(t):Object(i.a)(this.compiler.compileModuleAsync(t))))}}class cn{constructor(){this.outlet=null,this.route=null,this.resolver=null,this.children=new ln,this.attachRef=null}}class ln{constructor(){this.contexts=new Map}onChildOutletCreated(t,e){const n=this.getOrCreateContext(t);n.outlet=e,this.contexts.set(t,n)}onChildOutletDestroyed(t){const e=this.getContext(t);e&&(e.outlet=null)}onOutletDeactivated(){const t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let e=this.getContext(t);return e||(e=new cn,this.contexts.set(t,e)),e}getContext(t){return this.contexts.get(t)||null}}class un{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,e){return t}}function hn(t){throw t}function dn(t,e,n){return e.parse("/")}function fn(t,e){return Object(o.a)(null)}let pn=(()=>{class t{constructor(t,e,n,r,i,o,c,l){this.rootComponentType=t,this.urlSerializer=e,this.rootContexts=n,this.location=r,this.config=l,this.lastSuccessfulNavigation=null,this.currentNavigation=null,this.disposed=!1,this.lastLocationChangeInfo=null,this.navigationId=0,this.isNgZoneEnabled=!1,this.events=new S.a,this.errorHandler=hn,this.malformedUriErrorHandler=dn,this.navigated=!1,this.lastSuccessfulId=-1,this.hooks={beforePreactivation:fn,afterPreactivation:fn},this.urlHandlingStrategy=new un,this.routeReuseStrategy=new sn,this.onSameUrlNavigation="ignore",this.paramsInheritanceStrategy="emptyOnly",this.urlUpdateStrategy="deferred",this.relativeLinkResolution="corrected",this.ngModule=i.get(s.E),this.console=i.get(s.fb);const u=i.get(s.G);this.isNgZoneEnabled=u instanceof s.G&&s.G.isInAngularZone(),this.resetConfig(l),this.currentUrlTree=new wt(new St([],{}),{},null),this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.configLoader=new an(o,c,t=>this.triggerEvent(new tt(t)),t=>this.triggerEvent(new et(t))),this.routerState=Gt(this.currentUrlTree,this.rootComponentType),this.transitions=new a.a({id:0,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,extractedUrl:this.urlHandlingStrategy.extract(this.currentUrlTree),urlAfterRedirects:this.urlHandlingStrategy.extract(this.currentUrlTree),rawUrl:this.currentUrlTree,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:"imperative",restoredState:null,currentSnapshot:this.routerState.snapshot,targetSnapshot:null,currentRouterState:this.routerState,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.navigations=this.setupNavigations(this.transitions),this.processNavigations()}setupNavigations(t){const e=this.events;return t.pipe(Object(k.a)(t=>0!==t.id),Object(C.a)(t=>Object.assign(Object.assign({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl)})),Object(E.a)(t=>{let n=!1,r=!1;return Object(o.a)(t).pipe(Object(L.a)(t=>{this.currentNavigation={id:t.id,initialUrl:t.currentRawUrl,extractedUrl:t.extractedUrl,trigger:t.source,extras:t.extras,previousNavigation:this.lastSuccessfulNavigation?Object.assign(Object.assign({},this.lastSuccessfulNavigation),{previousNavigation:null}):null}}),Object(E.a)(t=>{const n=!this.navigated||t.extractedUrl.toString()!==this.browserUrlTree.toString();if(("reload"===this.onSameUrlNavigation||n)&&this.urlHandlingStrategy.shouldProcessUrl(t.rawUrl))return Object(o.a)(t).pipe(Object(E.a)(t=>{const n=this.transitions.getValue();return e.next(new q(t.id,this.serializeUrl(t.extractedUrl),t.source,t.restoredState)),n!==this.transitions.getValue()?_.a:Promise.resolve(t)}),(r=this.ngModule.injector,s=this.configLoader,i=this.urlSerializer,a=this.config,Object(E.a)(t=>function(t,e,n,r,s){return new $e(t,e,n,r,s).apply()}(r,s,i,t.extractedUrl,a).pipe(Object(C.a)(e=>Object.assign(Object.assign({},t),{urlAfterRedirects:e}))))),Object(L.a)(t=>{this.currentNavigation=Object.assign(Object.assign({},this.currentNavigation),{finalUrl:t.urlAfterRedirects})}),function(t,e,n,r,s){return Object(F.a)(i=>function(t,e,n,r,s="emptyOnly",i="legacy"){try{const a=new Je(t,e,n,r,s,i).recognize();return null===a?Ke(new We):Object(o.a)(a)}catch(a){return Ke(a)}}(t,e,i.urlAfterRedirects,n(i.urlAfterRedirects),r,s).pipe(Object(C.a)(t=>Object.assign(Object.assign({},i),{targetSnapshot:t}))))}(this.rootComponentType,this.config,t=>this.serializeUrl(t),this.paramsInheritanceStrategy,this.relativeLinkResolution),Object(L.a)(t=>{"eager"===this.urlUpdateStrategy&&(t.extras.skipLocationChange||this.setBrowserUrl(t.urlAfterRedirects,!!t.extras.replaceUrl,t.id,t.extras.state),this.browserUrlTree=t.urlAfterRedirects);const n=new W(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);e.next(n)}));var r,s,i,a;if(n&&this.rawUrlTree&&this.urlHandlingStrategy.shouldProcessUrl(this.rawUrlTree)){const{id:n,extractedUrl:r,source:s,restoredState:i,extras:a}=t,c=new q(n,this.serializeUrl(r),s,i);e.next(c);const l=Gt(r,this.rootComponentType).snapshot;return Object(o.a)(Object.assign(Object.assign({},t),{targetSnapshot:l,urlAfterRedirects:r,extras:Object.assign(Object.assign({},a),{skipLocationChange:!1,replaceUrl:!1})}))}return this.rawUrlTree=t.rawUrl,this.browserUrlTree=t.urlAfterRedirects,t.resolve(null),_.a}),rn(t=>{const{targetSnapshot:e,id:n,extractedUrl:r,rawUrl:s,extras:{skipLocationChange:i,replaceUrl:o}}=t;return this.hooks.beforePreactivation(e,{navigationId:n,appliedUrlTree:r,rawUrlTree:s,skipLocationChange:!!i,replaceUrl:!!o})}),Object(L.a)(t=>{const e=new K(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);this.triggerEvent(e)}),Object(C.a)(t=>Object.assign(Object.assign({},t),{guards:qe(t.targetSnapshot,t.currentSnapshot,this.rootContexts)})),function(t,e){return Object(F.a)(n=>{const{targetSnapshot:r,currentSnapshot:s,guards:{canActivateChecks:a,canDeactivateChecks:c}}=n;return 0===c.length&&0===a.length?Object(o.a)(Object.assign(Object.assign({},n),{guardsResult:!0})):function(t,e,n,r){return Object(i.a)(t).pipe(Object(F.a)(t=>function(t,e,n,r,s){const i=e&&e.routeConfig?e.routeConfig.canDeactivate:null;if(!i||0===i.length)return Object(o.a)(!0);const a=i.map(i=>{const o=Qe(i,e,s);let a;if(function(t){return t&&_e(t.canDeactivate)}(o))a=gt(o.canDeactivate(t,e,n,r));else{if(!_e(o))throw new Error("Invalid CanDeactivate guard");a=gt(o(t,e,n,r))}return a.pipe(Object(N.a)())});return Object(o.a)(a).pipe(Se())}(t.component,t.route,n,e,r)),Object(N.a)(t=>!0!==t,!0))}(c,r,s,t).pipe(Object(F.a)(n=>n&&"boolean"==typeof n?function(t,e,n,r){return Object(i.a)(e).pipe(Object(I.a)(e=>Object(b.a)(function(t,e){return null!==t&&e&&e(new nt(t)),Object(o.a)(!0)}(e.route.parent,r),function(t,e){return null!==t&&e&&e(new st(t)),Object(o.a)(!0)}(e.route,r),function(t,e,n){const r=e[e.length-1],s=e.slice(0,e.length-1).reverse().map(t=>function(t){const e=t.routeConfig?t.routeConfig.canActivateChild:null;return e&&0!==e.length?{node:t,guards:e}:null}(t)).filter(t=>null!==t).map(e=>v(()=>{const s=e.guards.map(s=>{const i=Qe(s,e.node,n);let o;if(function(t){return t&&_e(t.canActivateChild)}(i))o=gt(i.canActivateChild(r,t));else{if(!_e(i))throw new Error("Invalid CanActivateChild guard");o=gt(i(r,t))}return o.pipe(Object(N.a)())});return Object(o.a)(s).pipe(Se())}));return Object(o.a)(s).pipe(Se())}(t,e.path,n),function(t,e,n){const r=e.routeConfig?e.routeConfig.canActivate:null;if(!r||0===r.length)return Object(o.a)(!0);const s=r.map(r=>v(()=>{const s=Qe(r,e,n);let i;if(function(t){return t&&_e(t.canActivate)}(s))i=gt(s.canActivate(e,t));else{if(!_e(s))throw new Error("Invalid CanActivate guard");i=gt(s(e,t))}return i.pipe(Object(N.a)())}));return Object(o.a)(s).pipe(Se())}(t,e.route,n))),Object(N.a)(t=>!0!==t,!0))}(r,a,t,e):Object(o.a)(n)),Object(C.a)(t=>Object.assign(Object.assign({},n),{guardsResult:t})))})}(this.ngModule.injector,t=>this.triggerEvent(t)),Object(L.a)(t=>{if(ve(t.guardsResult)){const e=lt(`Redirecting to "${this.serializeUrl(t.guardsResult)}"`);throw e.url=t.guardsResult,e}const e=new J(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot,!!t.guardsResult);this.triggerEvent(e)}),Object(k.a)(t=>{if(!t.guardsResult){this.resetUrlToCurrentUrlTree();const n=new G(t.id,this.serializeUrl(t.extractedUrl),"");return e.next(n),t.resolve(!1),!1}return!0}),rn(t=>{if(t.guards.canActivateChecks.length)return Object(o.a)(t).pipe(Object(L.a)(t=>{const e=new Y(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);this.triggerEvent(e)}),Object(E.a)(t=>{let n=!1;return Object(o.a)(t).pipe((r=this.paramsInheritanceStrategy,s=this.ngModule.injector,Object(F.a)(t=>{const{targetSnapshot:e,guards:{canActivateChecks:n}}=t;if(!n.length)return Object(o.a)(t);let a=0;return Object(i.a)(n).pipe(Object(I.a)(t=>function(t,e,n,r){return function(t,e,n,r){const s=Object.keys(t);if(0===s.length)return Object(o.a)({});const a={};return Object(i.a)(s).pipe(Object(F.a)(s=>function(t,e,n,r){const s=Qe(t,e,r);return gt(s.resolve?s.resolve(e,n):s(e,n))}(t[s],e,n,r).pipe(Object(L.a)(t=>{a[s]=t}))),Object(A.a)(1),Object(F.a)(()=>Object.keys(a).length===s.length?Object(o.a)(a):_.a))}(t._resolve,t,e,r).pipe(Object(C.a)(e=>(t._resolvedData=e,t.data=Object.assign(Object.assign({},t.data),Wt(t,n).resolve),null)))}(t.route,e,r,s)),Object(L.a)(()=>a++),Object(A.a)(1),Object(F.a)(e=>a===n.length?Object(o.a)(t):_.a))})),Object(L.a)({next:()=>n=!0,complete:()=>{if(!n){const n=new G(t.id,this.serializeUrl(t.extractedUrl),"At least one route resolver didn't emit any value.");e.next(n),t.resolve(!1)}}}));var r,s}),Object(L.a)(t=>{const e=new X(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);this.triggerEvent(e)}))}),rn(t=>{const{targetSnapshot:e,id:n,extractedUrl:r,rawUrl:s,extras:{skipLocationChange:i,replaceUrl:o}}=t;return this.hooks.afterPreactivation(e,{navigationId:n,appliedUrlTree:r,rawUrlTree:s,skipLocationChange:!!i,replaceUrl:!!o})}),Object(C.a)(t=>{const e=function(t,e,n){const r=ne(t,e._root,n?n._root:void 0);return new Qt(r,e)}(this.routeReuseStrategy,t.targetSnapshot,t.currentRouterState);return Object.assign(Object.assign({},t),{targetRouterState:e})}),Object(L.a)(t=>{this.currentUrlTree=t.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.rawUrl),this.routerState=t.targetRouterState,"deferred"===this.urlUpdateStrategy&&(t.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,!!t.extras.replaceUrl,t.id,t.extras.state),this.browserUrlTree=t.urlAfterRedirects)}),(a=this.rootContexts,c=this.routeReuseStrategy,l=t=>this.triggerEvent(t),Object(C.a)(t=>(new ge(c,t.targetRouterState,t.currentRouterState,l).activate(a),t))),Object(L.a)({next(){n=!0},complete(){n=!0}}),(s=()=>{if(!n&&!r){this.resetUrlToCurrentUrlTree();const n=new G(t.id,this.serializeUrl(t.extractedUrl),`Navigation ID ${t.id} is not equal to the current navigation id ${this.navigationId}`);e.next(n),t.resolve(!1)}this.currentNavigation=null},t=>t.lift(new $(s))),Object(j.a)(n=>{if(r=!0,(s=n)&&s.ngNavigationCancelingError){const r=ve(n.url);r||(this.navigated=!0,this.resetStateAndUrl(t.currentRouterState,t.currentUrlTree,t.rawUrl));const s=new G(t.id,this.serializeUrl(t.extractedUrl),n.message);e.next(s),r?setTimeout(()=>{const e=this.urlHandlingStrategy.merge(n.url,this.rawUrlTree);this.scheduleNavigation(e,"imperative",null,{skipLocationChange:t.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy},{resolve:t.resolve,reject:t.reject,promise:t.promise})},0):t.resolve(!1)}else{this.resetStateAndUrl(t.currentRouterState,t.currentUrlTree,t.rawUrl);const r=new Z(t.id,this.serializeUrl(t.extractedUrl),n);e.next(r);try{t.resolve(this.errorHandler(n))}catch(i){t.reject(i)}}var s;return _.a}));var s,a,c,l}))}resetRootComponentType(t){this.rootComponentType=t,this.routerState.root.component=this.rootComponentType}getTransition(){const t=this.transitions.value;return t.urlAfterRedirects=this.browserUrlTree,t}setTransition(t){this.transitions.next(Object.assign(Object.assign({},this.getTransition()),t))}initialNavigation(){this.setUpLocationChangeListener(),0===this.navigationId&&this.navigateByUrl(this.location.path(!0),{replaceUrl:!0})}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(t=>{const e=this.extractLocationChangeInfoFromEvent(t);this.shouldScheduleNavigation(this.lastLocationChangeInfo,e)&&setTimeout(()=>{const{source:t,state:n,urlTree:r}=e,s={replaceUrl:!0};if(n){const t=Object.assign({},n);delete t.navigationId,0!==Object.keys(t).length&&(s.state=t)}this.scheduleNavigation(r,t,n,s)},0),this.lastLocationChangeInfo=e}))}extractLocationChangeInfoFromEvent(t){var e;return{source:"popstate"===t.type?"popstate":"hashchange",urlTree:this.parseUrl(t.url),state:(null===(e=t.state)||void 0===e?void 0:e.navigationId)?t.state:null,transitionId:this.getTransition().id}}shouldScheduleNavigation(t,e){if(!t)return!0;const n=e.urlTree.toString()===t.urlTree.toString();return!(e.transitionId===t.transitionId&&n&&("hashchange"===e.source&&"popstate"===t.source||"popstate"===e.source&&"hashchange"===t.source))}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.currentNavigation}triggerEvent(t){this.events.next(t)}resetConfig(t){Ee(t),this.config=t.map(xe),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.transitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0}createUrlTree(t,e={}){const{relativeTo:n,queryParams:r,fragment:s,queryParamsHandling:i,preserveFragment:o}=e,a=n||this.routerState.root,c=o?this.currentUrlTree.fragment:s;let l=null;switch(i){case"merge":l=Object.assign(Object.assign({},this.currentUrlTree.queryParams),r);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=r||null}return null!==l&&(l=this.removeEmptyProps(l)),function(t,e,n,r,s){if(0===n.length)return oe(e.root,e.root,e,r,s);const i=function(t){if("string"==typeof t[0]&&1===t.length&&"/"===t[0])return new ce(!0,0,t);let e=0,n=!1;const r=t.reduce((t,r,s)=>{if("object"==typeof r&&null!=r){if(r.outlets){const e={};return mt(r.outlets,(t,n)=>{e[n]="string"==typeof t?t.split("/"):t}),[...t,{outlets:e}]}if(r.segmentPath)return[...t,r.segmentPath]}return"string"!=typeof r?[...t,r]:0===s?(r.split("/").forEach((r,s)=>{0==s&&"."===r||(0==s&&""===r?n=!0:".."===r?e++:""!=r&&t.push(r))}),t):[...t,r]},[]);return new ce(n,e,r)}(n);if(i.toRoot())return oe(e.root,new St([],{}),e,r,s);const o=function(t,e,n){if(t.isAbsolute)return new le(e.root,!0,0);if(-1===n.snapshot._lastPathIndex){const t=n.snapshot._urlSegment;return new le(t,t===e.root,0)}const r=se(t.commands[0])?0:1;return function(t,e,n){let r=t,s=e,i=n;for(;i>s;){if(i-=s,r=r.parent,!r)throw new Error("Invalid number of '../'");s=r.segments.length}return new le(r,!1,s-i)}(n.snapshot._urlSegment,n.snapshot._lastPathIndex+r,t.numberOfDoubleDots)}(i,e,t),a=o.processChildren?he(o.segmentGroup,o.index,i.commands):ue(o.segmentGroup,o.index,i.commands);return oe(o.segmentGroup,a,e,r,s)}(a,this.currentUrlTree,t,l,c)}navigateByUrl(t,e={skipLocationChange:!1}){const n=ve(t)?t:this.parseUrl(t),r=this.urlHandlingStrategy.merge(n,this.rawUrlTree);return this.scheduleNavigation(r,"imperative",null,e)}navigate(t,e={skipLocationChange:!1}){return function(t){for(let e=0;e<t.length;e++){const n=t[e];if(null==n)throw new Error(`The requested path contains ${n} segment at index ${e}`)}}(t),this.navigateByUrl(this.createUrlTree(t,e),e)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){let e;try{e=this.urlSerializer.parse(t)}catch(n){e=this.malformedUriErrorHandler(n,this.urlSerializer,t)}return e}isActive(t,e){if(ve(t))return yt(this.currentUrlTree,t,e);const n=this.parseUrl(t);return yt(this.currentUrlTree,n,e)}removeEmptyProps(t){return Object.keys(t).reduce((e,n)=>{const r=t[n];return null!=r&&(e[n]=r),e},{})}processNavigations(){this.navigations.subscribe(t=>{this.navigated=!0,this.lastSuccessfulId=t.id,this.events.next(new Q(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(this.currentUrlTree))),this.lastSuccessfulNavigation=this.currentNavigation,this.currentNavigation=null,t.resolve(!0)},t=>{this.console.warn("Unhandled Navigation Error: ")})}scheduleNavigation(t,e,n,r,s){if(this.disposed)return Promise.resolve(!1);const i=this.getTransition(),o="imperative"!==e&&"imperative"===(null==i?void 0:i.source),a=(this.lastSuccessfulId===i.id||this.currentNavigation?i.rawUrl:i.urlAfterRedirects).toString()===t.toString();if(o&&a)return Promise.resolve(!0);let c,l,u;s?(c=s.resolve,l=s.reject,u=s.promise):u=new Promise((t,e)=>{c=t,l=e});const h=++this.navigationId;return this.setTransition({id:h,source:e,restoredState:n,currentUrlTree:this.currentUrlTree,currentRawUrl:this.rawUrlTree,rawUrl:t,extras:r,resolve:c,reject:l,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(t=>Promise.reject(t))}setBrowserUrl(t,e,n,r){const s=this.urlSerializer.serialize(t);r=r||{},this.location.isCurrentPathEqualTo(s)||e?this.location.replaceState(s,"",Object.assign(Object.assign({},r),{navigationId:n})):this.location.go(s,"",Object.assign(Object.assign({},r),{navigationId:n}))}resetStateAndUrl(t,e,n){this.routerState=t,this.currentUrlTree=e,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n),this.resetUrlToCurrentUrlTree()}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",{navigationId:this.lastSuccessfulId})}}return t.\u0275fac=function(e){return new(e||t)(s.ec(s.V),s.ec(Ot),s.ec(ln),s.ec(r.i),s.ec(s.w),s.ec(s.D),s.ec(s.j),s.ec(void 0))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})(),mn=(()=>{class t{constructor(t,e,n,r,s){this.router=t,this.route=e,this.commands=[],this.onChanges=new S.a,null==n&&r.setAttribute(s.nativeElement,"tabindex","0")}ngOnChanges(t){this.onChanges.next(this)}set routerLink(t){this.commands=null!=t?Array.isArray(t)?t:[t]:[]}onClick(){const t={skipLocationChange:yn(this.skipLocationChange),replaceUrl:yn(this.replaceUrl),state:this.state};return this.router.navigateByUrl(this.urlTree,t),!0}get urlTree(){return this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:yn(this.preserveFragment)})}}return t.\u0275fac=function(e){return new(e||t)(s.Ub(pn),s.Ub(Zt),s.fc("tabindex"),s.Ub(s.M),s.Ub(s.o))},t.\u0275dir=s.Pb({type:t,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(t,e){1&t&&s.hc("click",function(){return e.onClick()})},inputs:{routerLink:"routerLink",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",preserveFragment:"preserveFragment",skipLocationChange:"skipLocationChange",replaceUrl:"replaceUrl",state:"state",relativeTo:"relativeTo"},features:[s.Gb]}),t})(),gn=(()=>{class t{constructor(t,e,n){this.router=t,this.route=e,this.locationStrategy=n,this.commands=[],this.onChanges=new S.a,this.subscription=t.events.subscribe(t=>{t instanceof Q&&this.updateTargetUrlAndHref()})}set routerLink(t){this.commands=null!=t?Array.isArray(t)?t:[t]:[]}ngOnChanges(t){this.updateTargetUrlAndHref(),this.onChanges.next(this)}ngOnDestroy(){this.subscription.unsubscribe()}onClick(t,e,n,r,s){if(0!==t||e||n||r||s)return!0;if("string"==typeof this.target&&"_self"!=this.target)return!0;const i={skipLocationChange:yn(this.skipLocationChange),replaceUrl:yn(this.replaceUrl),state:this.state};return this.router.navigateByUrl(this.urlTree,i),!1}updateTargetUrlAndHref(){this.href=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.urlTree))}get urlTree(){return this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:yn(this.preserveFragment)})}}return t.\u0275fac=function(e){return new(e||t)(s.Ub(pn),s.Ub(Zt),s.Ub(r.j))},t.\u0275dir=s.Pb({type:t,selectors:[["a","routerLink",""],["area","routerLink",""]],hostVars:2,hostBindings:function(t,e){1&t&&s.hc("click",function(t){return e.onClick(t.button,t.ctrlKey,t.shiftKey,t.altKey,t.metaKey)}),2&t&&(s.dc("href",e.href,s.Fc),s.Jb("target",e.target))},inputs:{routerLink:"routerLink",target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",preserveFragment:"preserveFragment",skipLocationChange:"skipLocationChange",replaceUrl:"replaceUrl",state:"state",relativeTo:"relativeTo"},features:[s.Gb]}),t})();function yn(t){return""===t||!!t}let bn=(()=>{class t{constructor(t,e,n,r,i){this.parentContexts=t,this.location=e,this.resolver=n,this.changeDetector=i,this.activated=null,this._activatedRoute=null,this.activateEvents=new s.q,this.deactivateEvents=new s.q,this.name=r||"primary",t.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.parentContexts.onChildOutletDestroyed(this.name)}ngOnInit(){if(!this.activated){const t=this.parentContexts.getContext(this.name);t&&t.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.resolver||null))}}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new Error("Outlet is not activated");this.location.detach();const t=this.activated;return this.activated=null,this._activatedRoute=null,t}attach(t,e){this.activated=t,this._activatedRoute=e,this.location.insert(t.hostView)}deactivate(){if(this.activated){const t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,e){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;const n=(e=e||this.resolver).resolveComponentFactory(t._futureSnapshot.routeConfig.component),r=this.parentContexts.getOrCreateContext(this.name).children,s=new _n(t,r,this.location.injector);this.activated=this.location.createComponent(n,this.location.length,s),this.changeDetector.markForCheck(),this.activateEvents.emit(this.activated.instance)}}return t.\u0275fac=function(e){return new(e||t)(s.Ub(ln),s.Ub(s.X),s.Ub(s.l),s.fc("name"),s.Ub(s.i))},t.\u0275dir=s.Pb({type:t,selectors:[["router-outlet"]],outputs:{activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]}),t})();class _n{constructor(t,e,n){this.route=t,this.childContexts=e,this.parent=n}get(t,e){return t===Zt?this.route:t===ln?this.childContexts:this.parent.get(t,e)}}class vn{}class wn{preload(t,e){return Object(o.a)(null)}}let Sn=(()=>{class t{constructor(t,e,n,r,s){this.router=t,this.injector=r,this.preloadingStrategy=s,this.loader=new an(e,n,e=>t.triggerEvent(new tt(e)),e=>t.triggerEvent(new et(e)))}setUpPreloading(){this.subscription=this.router.events.pipe(Object(k.a)(t=>t instanceof Q),Object(I.a)(()=>this.preload())).subscribe(()=>{})}preload(){const t=this.injector.get(s.E);return this.processRoutes(t,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,e){const n=[];for(const r of e)if(r.loadChildren&&!r.canLoad&&r._loadedConfig){const t=r._loadedConfig;n.push(this.processRoutes(t.module,t.routes))}else r.loadChildren&&!r.canLoad?n.push(this.preloadConfig(t,r)):r.children&&n.push(this.processRoutes(t,r.children));return Object(i.a)(n).pipe(Object(z.a)(),Object(C.a)(t=>{}))}preloadConfig(t,e){return this.preloadingStrategy.preload(e,()=>(e._loadedConfig?Object(o.a)(e._loadedConfig):this.loader.load(t.injector,e)).pipe(Object(F.a)(t=>(e._loadedConfig=t,this.processRoutes(t.module,t.routes)))))}}return t.\u0275fac=function(e){return new(e||t)(s.ec(pn),s.ec(s.D),s.ec(s.j),s.ec(s.w),s.ec(vn))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})(),Cn=(()=>{class t{constructor(t,e,n={}){this.router=t,this.viewportScroller=e,this.options=n,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},n.scrollPositionRestoration=n.scrollPositionRestoration||"disabled",n.anchorScrolling=n.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.router.events.subscribe(t=>{t instanceof q?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Q&&(this.lastId=t.id,this.scheduleScrollEvent(t,this.router.parseUrl(t.urlAfterRedirects).fragment))})}consumeScrollEvents(){return this.router.events.subscribe(t=>{t instanceof ot&&(t.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(t.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,e){this.router.triggerEvent(new ot(t,"popstate"===this.lastSource?this.store[this.restoredId]:null,e))}ngOnDestroy(){this.routerEventsSubscription&&this.routerEventsSubscription.unsubscribe(),this.scrollEventsSubscription&&this.scrollEventsSubscription.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(s.ec(pn),s.ec(r.t),s.ec(void 0))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();const En=new s.v("ROUTER_CONFIGURATION"),On=new s.v("ROUTER_FORROOT_GUARD"),Tn=[r.i,{provide:Ot,useClass:Tt},{provide:pn,useFactory:function(t,e,n,s,i,o,a,c={},l,u){const h=new pn(null,t,e,n,s,i,o,ft(a));if(l&&(h.urlHandlingStrategy=l),u&&(h.routeReuseStrategy=u),function(t,e){t.errorHandler&&(e.errorHandler=t.errorHandler),t.malformedUriErrorHandler&&(e.malformedUriErrorHandler=t.malformedUriErrorHandler),t.onSameUrlNavigation&&(e.onSameUrlNavigation=t.onSameUrlNavigation),t.paramsInheritanceStrategy&&(e.paramsInheritanceStrategy=t.paramsInheritanceStrategy),t.relativeLinkResolution&&(e.relativeLinkResolution=t.relativeLinkResolution),t.urlUpdateStrategy&&(e.urlUpdateStrategy=t.urlUpdateStrategy)}(c,h),c.enableTracing){const t=Object(r.y)();h.events.subscribe(e=>{t.logGroup(`Router Event: ${e.constructor.name}`),t.log(e.toString()),t.log(e),t.logGroupEnd()})}return h},deps:[Ot,ln,r.i,s.w,s.D,s.j,on,En,[class{},new s.H],[class{},new s.H]]},ln,{provide:Zt,useFactory:function(t){return t.routerState.root},deps:[pn]},{provide:s.D,useClass:s.S},Sn,wn,class{preload(t,e){return e().pipe(Object(j.a)(()=>Object(o.a)(null)))}},{provide:En,useValue:{enableTracing:!1}}];function xn(){return new s.F("Router",pn)}let kn=(()=>{class t{constructor(t,e){}static forRoot(e,n){return{ngModule:t,providers:[Tn,Pn(e),{provide:On,useFactory:An,deps:[[pn,new s.H,new s.R]]},{provide:En,useValue:n||{}},{provide:r.j,useFactory:In,deps:[r.s,[new s.s(r.a),new s.H],En]},{provide:Cn,useFactory:jn,deps:[pn,r.t,En]},{provide:vn,useExisting:n&&n.preloadingStrategy?n.preloadingStrategy:wn},{provide:s.F,multi:!0,useFactory:xn},[Rn,{provide:s.d,multi:!0,useFactory:Dn,deps:[Rn]},{provide:Fn,useFactory:Nn,deps:[Rn]},{provide:s.b,multi:!0,useExisting:Fn}]]}}static forChild(e){return{ngModule:t,providers:[Pn(e)]}}}return t.\u0275fac=function(e){return new(e||t)(s.ec(On,8),s.ec(pn,8))},t.\u0275mod=s.Sb({type:t}),t.\u0275inj=s.Rb({}),t})();function jn(t,e,n){return n.scrollOffset&&e.setOffset(n.scrollOffset),new Cn(t,e,n)}function In(t,e,n={}){return n.useHash?new r.g(t,e):new r.r(t,e)}function An(t){return"guarded"}function Pn(t){return[{provide:s.a,multi:!0,useValue:t},{provide:on,multi:!0,useValue:t}]}let Rn=(()=>{class t{constructor(t){this.injector=t,this.initNavigation=!1,this.resultOfPreactivationDone=new S.a}appInitializer(){return this.injector.get(r.h,Promise.resolve(null)).then(()=>{let t=null;const e=new Promise(e=>t=e),n=this.injector.get(pn),r=this.injector.get(En);return"disabled"===r.initialNavigation?(n.setUpLocationChangeListener(),t(!0)):"enabled"===r.initialNavigation||"enabledBlocking"===r.initialNavigation?(n.hooks.afterPreactivation=()=>this.initNavigation?Object(o.a)(null):(this.initNavigation=!0,t(!0),this.resultOfPreactivationDone),n.initialNavigation()):t(!0),e})}bootstrapListener(t){const e=this.injector.get(En),n=this.injector.get(Sn),r=this.injector.get(Cn),i=this.injector.get(pn),o=this.injector.get(s.g);t===o.components[0]&&("enabledNonBlocking"!==e.initialNavigation&&void 0!==e.initialNavigation||i.initialNavigation(),n.setUpPreloading(),r.init(),i.resetRootComponentType(o.componentTypes[0]),this.resultOfPreactivationDone.next(null),this.resultOfPreactivationDone.complete())}}return t.\u0275fac=function(e){return new(e||t)(s.ec(s.w))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();function Dn(t){return t.appInitializer.bind(t)}function Nn(t){return t.bootstrapListener.bind(t)}const Fn=new s.v("Router Initializer")},vkgz:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("7o/Q"),s=n("KqfI"),i=n("n6bG");function o(t,e,n){return function(r){return r.lift(new a(t,e,n))}}class a{constructor(t,e,n){this.nextOrObserver=t,this.error=e,this.complete=n}call(t,e){return e.subscribe(new c(t,this.nextOrObserver,this.error,this.complete))}}class c extends r.a{constructor(t,e,n,r){super(t),this._tapNext=s.a,this._tapError=s.a,this._tapComplete=s.a,this._tapError=n||s.a,this._tapComplete=r||s.a,Object(i.a)(e)?(this._context=this,this._tapNext=e):e&&(this._context=e,this._tapNext=e.next||s.a,this._tapError=e.error||s.a,this._tapComplete=e.complete||s.a)}_next(t){try{this._tapNext.call(this._context,t)}catch(e){return void this.destination.error(e)}this.destination.next(t)}_error(t){try{this._tapError.call(this._context,t)}catch(t){return void this.destination.error(t)}this.destination.error(t)}_complete(){try{this._tapComplete.call(this._context)}catch(t){return void this.destination.error(t)}return this.destination.complete()}}},"x+ZX":function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");function s(){return function(t){return t.lift(new i(t))}}class i{constructor(t){this.connectable=t}call(t,e){const{connectable:n}=this;n._refCount++;const r=new o(t,n),s=e.subscribe(r);return r.closed||(r.connection=n.connect()),s}}class o extends r.a{constructor(t,e){super(t),this.connectable=e}_unsubscribe(){const{connectable:t}=this;if(!t)return void(this.connection=null);this.connectable=null;const e=t._refCount;if(e<=0)return void(this.connection=null);if(t._refCount=e-1,e>1)return void(this.connection=null);const{connection:n}=this,r=t._connection;this.connection=null,!r||n&&r!==n||r.unsubscribe()}}},xbPD:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("7o/Q");function s(t=null){return e=>e.lift(new i(t))}class i{constructor(t){this.defaultValue=t}call(t,e){return e.subscribe(new o(t,this.defaultValue))}}class o extends r.a{constructor(t,e){super(t),this.defaultValue=e,this.isEmpty=!0}_next(t){this.isEmpty=!1,this.destination.next(t)}_complete(){this.isEmpty&&this.destination.next(this.defaultValue),this.destination.complete()}}},yCtX:function(t,e,n){"use strict";n.d(e,"a",function(){return o});var r=n("HDdC"),s=n("ngJS"),i=n("jZKg");function o(t,e){return e?Object(i.a)(t,e):new r.a(Object(s.a)(t))}},"z+Ro":function(t,e,n){"use strict";function r(t){return t&&"function"==typeof t.schedule}n.d(e,"a",function(){return r})},z6cu:function(t,e,n){"use strict";n.d(e,"a",function(){return s});var r=n("HDdC");function s(t,e){return new r.a(e?n=>e.schedule(i,0,{error:t,subscriber:n}):e=>e.error(t))}function i({error:t,subscriber:e}){e.error(t)}},zUnb:function(t,e,n){"use strict";n.r(e);var r=n("jhN1"),s=n("fXoL"),i=n("R0Ic");function o(){return"undefined"!=typeof process&&"[object process]"==={}.toString.call(process)}function a(t){switch(t.length){case 0:return new i.d;case 1:return t[0];default:return new i.k(t)}}function c(t,e,n,r,s={},o={}){const a=[],c=[];let l=-1,u=null;if(r.forEach(t=>{const n=t.offset,r=n==l,h=r&&u||{};Object.keys(t).forEach(n=>{let r=n,c=t[n];if("offset"!==n)switch(r=e.normalizePropertyName(r,a),c){case i.l:c=s[n];break;case i.a:c=o[n];break;default:c=e.normalizeStyleValue(n,r,c,a)}h[r]=c}),r||c.push(h),u=h,l=n}),a.length){const t="\n - ";throw new Error(`Unable to animate due to the following errors:${t}${a.join(t)}`)}return c}function l(t,e,n,r){switch(e){case"start":t.onStart(()=>r(n&&u(n,"start",t)));break;case"done":t.onDone(()=>r(n&&u(n,"done",t)));break;case"destroy":t.onDestroy(()=>r(n&&u(n,"destroy",t)))}}function u(t,e,n){const r=n.totalTime,s=h(t.element,t.triggerName,t.fromState,t.toState,e||t.phaseName,null==r?t.totalTime:r,!!n.disabled),i=t._data;return null!=i&&(s._data=i),s}function h(t,e,n,r,s="",i=0,o){return{element:t,triggerName:e,fromState:n,toState:r,phaseName:s,totalTime:i,disabled:!!o}}function d(t,e,n){let r;return t instanceof Map?(r=t.get(e),r||t.set(e,r=n)):(r=t[e],r||(r=t[e]=n)),r}function f(t){const e=t.indexOf(":");return[t.substring(1,e),t.substr(e+1)]}let p=(t,e)=>!1,m=(t,e)=>!1,g=(t,e,n)=>[];const y=o();(y||"undefined"!=typeof Element)&&(p=(t,e)=>t.contains(e),m=(()=>{if(y||Element.prototype.matches)return(t,e)=>t.matches(e);{const t=Element.prototype,e=t.matchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector||t.webkitMatchesSelector;return e?(t,n)=>e.apply(t,[n]):m}})(),g=(t,e,n)=>{let r=[];if(n){const n=t.querySelectorAll(e);for(let t=0;t<n.length;t++)r.push(n[t])}else{const n=t.querySelector(e);n&&r.push(n)}return r});let b=null,_=!1;function v(t){b||(b=("undefined"!=typeof document?document.body:null)||{},_=!!b.style&&"WebkitAppearance"in b.style);let e=!0;return b.style&&!function(t){return"ebkit"==t.substring(1,6)}(t)&&(e=t in b.style,!e&&_)&&(e="Webkit"+t.charAt(0).toUpperCase()+t.substr(1)in b.style),e}const w=m,S=p,C=g;function E(t){const e={};return Object.keys(t).forEach(n=>{const r=n.replace(/([a-z])([A-Z])/g,"$1-$2");e[r]=t[n]}),e}let O=(()=>{class t{validateStyleProperty(t){return v(t)}matchesElement(t,e){return w(t,e)}containsElement(t,e){return S(t,e)}query(t,e,n){return C(t,e,n)}computeStyle(t,e,n){return n||""}animate(t,e,n,r,s,o=[],a){return new i.d(n,r)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})(),T=(()=>{class t{}return t.NOOP=new O,t})();function x(t){if("number"==typeof t)return t;const e=t.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:k(parseFloat(e[1]),e[2])}function k(t,e){switch(e){case"s":return 1e3*t;default:return t}}function j(t,e,n){return t.hasOwnProperty("duration")?t:function(t,e,n){let r,s=0,i="";if("string"==typeof t){const n=t.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===n)return e.push(`The provided timing value "${t}" is invalid.`),{duration:0,delay:0,easing:""};r=k(parseFloat(n[1]),n[2]);const o=n[3];null!=o&&(s=k(parseFloat(o),n[4]));const a=n[5];a&&(i=a)}else r=t;if(!n){let n=!1,i=e.length;r<0&&(e.push("Duration values below 0 are not allowed for this animation step."),n=!0),s<0&&(e.push("Delay values below 0 are not allowed for this animation step."),n=!0),n&&e.splice(i,0,`The provided timing value "${t}" is invalid.`)}return{duration:r,delay:s,easing:i}}(t,e,n)}function I(t,e={}){return Object.keys(t).forEach(n=>{e[n]=t[n]}),e}function A(t,e,n={}){if(e)for(let r in t)n[r]=t[r];else I(t,n);return n}function P(t,e,n){return n?e+":"+n+";":""}function R(t){let e="";for(let n=0;n<t.style.length;n++){const r=t.style.item(n);e+=P(0,r,t.style.getPropertyValue(r))}for(const n in t.style)t.style.hasOwnProperty(n)&&!n.startsWith("_")&&(e+=P(0,n.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),t.style[n]));t.setAttribute("style",e)}function D(t,e,n){t.style&&(Object.keys(e).forEach(r=>{const s=z(r);n&&!n.hasOwnProperty(r)&&(n[r]=t.style[s]),t.style[s]=e[r]}),o()&&R(t))}function N(t,e){t.style&&(Object.keys(e).forEach(e=>{const n=z(e);t.style[n]=""}),o()&&R(t))}function F(t){return Array.isArray(t)?1==t.length?t[0]:Object(i.f)(t):t}const L=new RegExp("{{\\s*(.+?)\\s*}}","g");function M(t){let e=[];if("string"==typeof t){let n;for(;n=L.exec(t);)e.push(n[1]);L.lastIndex=0}return e}function U(t,e,n){const r=t.toString(),s=r.replace(L,(t,r)=>{let s=e[r];return e.hasOwnProperty(r)||(n.push(`Please provide a value for the animation param ${r}`),s=""),s.toString()});return s==r?t:s}function H(t){const e=[];let n=t.next();for(;!n.done;)e.push(n.value),n=t.next();return e}const V=/-+([a-z0-9])/g;function z(t){return t.replace(V,(...t)=>t[1].toUpperCase())}function B(t,e){return 0===t||0===e}function q(t,e,n){const r=Object.keys(n);if(r.length&&e.length){let i=e[0],o=[];if(r.forEach(t=>{i.hasOwnProperty(t)||o.push(t),i[t]=n[t]}),o.length)for(var s=1;s<e.length;s++){let n=e[s];o.forEach(function(e){n[e]=G(t,e)})}}return e}function Q(t,e,n){switch(e.type){case 7:return t.visitTrigger(e,n);case 0:return t.visitState(e,n);case 1:return t.visitTransition(e,n);case 2:return t.visitSequence(e,n);case 3:return t.visitGroup(e,n);case 4:return t.visitAnimate(e,n);case 5:return t.visitKeyframes(e,n);case 6:return t.visitStyle(e,n);case 8:return t.visitReference(e,n);case 9:return t.visitAnimateChild(e,n);case 10:return t.visitAnimateRef(e,n);case 11:return t.visitQuery(e,n);case 12:return t.visitStagger(e,n);default:throw new Error(`Unable to resolve animation metadata node #${e.type}`)}}function G(t,e){return window.getComputedStyle(t)[e]}function Z(t,e){const n=[];return"string"==typeof t?t.split(/\s*,\s*/).forEach(t=>function(t,e,n){if(":"==t[0]){const r=function(t,e){switch(t){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,e)=>parseFloat(e)>parseFloat(t);case":decrement":return(t,e)=>parseFloat(e)<parseFloat(t);default:return e.push(`The transition alias value "${t}" is not supported`),"* => *"}}(t,n);if("function"==typeof r)return void e.push(r);t=r}const r=t.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==r||r.length<4)return n.push(`The provided transition expression "${t}" is not supported`),e;const s=r[1],i=r[2],o=r[3];e.push(J(s,o)),"<"!=i[0]||"*"==s&&"*"==o||e.push(J(o,s))}(t,n,e)):n.push(t),n}const W=new Set(["true","1"]),K=new Set(["false","0"]);function J(t,e){const n=W.has(t)||K.has(t),r=W.has(e)||K.has(e);return(s,i)=>{let o="*"==t||t==s,a="*"==e||e==i;return!o&&n&&"boolean"==typeof s&&(o=s?W.has(t):K.has(t)),!a&&r&&"boolean"==typeof i&&(a=i?W.has(e):K.has(e)),o&&a}}const Y=new RegExp("s*:selfs*,?","g");function X(t,e,n){return new tt(t).build(e,n)}class tt{constructor(t){this._driver=t}build(t,e){const n=new et(e);return this._resetContextStyleTimingState(n),Q(this,F(t),n)}_resetContextStyleTimingState(t){t.currentQuerySelector="",t.collectedStyles={},t.collectedStyles[""]={},t.currentTime=0}visitTrigger(t,e){let n=e.queryCount=0,r=e.depCount=0;const s=[],i=[];return"@"==t.name.charAt(0)&&e.errors.push("animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))"),t.definitions.forEach(t=>{if(this._resetContextStyleTimingState(e),0==t.type){const n=t,r=n.name;r.toString().split(/\s*,\s*/).forEach(t=>{n.name=t,s.push(this.visitState(n,e))}),n.name=r}else if(1==t.type){const s=this.visitTransition(t,e);n+=s.queryCount,r+=s.depCount,i.push(s)}else e.errors.push("only state() and transition() definitions can sit inside of a trigger()")}),{type:7,name:t.name,states:s,transitions:i,queryCount:n,depCount:r,options:null}}visitState(t,e){const n=this.visitStyle(t.styles,e),r=t.options&&t.options.params||null;if(n.containsDynamicStyles){const s=new Set,i=r||{};if(n.styles.forEach(t=>{if(nt(t)){const e=t;Object.keys(e).forEach(t=>{M(e[t]).forEach(t=>{i.hasOwnProperty(t)||s.add(t)})})}}),s.size){const n=H(s.values());e.errors.push(`state("${t.name}", ...) must define default values for all the following style substitutions: ${n.join(", ")}`)}}return{type:0,name:t.name,style:n,options:r?{params:r}:null}}visitTransition(t,e){e.queryCount=0,e.depCount=0;const n=Q(this,F(t.animation),e);return{type:1,matchers:Z(t.expr,e.errors),animation:n,queryCount:e.queryCount,depCount:e.depCount,options:rt(t.options)}}visitSequence(t,e){return{type:2,steps:t.steps.map(t=>Q(this,t,e)),options:rt(t.options)}}visitGroup(t,e){const n=e.currentTime;let r=0;const s=t.steps.map(t=>{e.currentTime=n;const s=Q(this,t,e);return r=Math.max(r,e.currentTime),s});return e.currentTime=r,{type:3,steps:s,options:rt(t.options)}}visitAnimate(t,e){const n=function(t,e){let n=null;if(t.hasOwnProperty("duration"))n=t;else if("number"==typeof t)return st(j(t,e).duration,0,"");const r=t;if(r.split(/\s+/).some(t=>"{"==t.charAt(0)&&"{"==t.charAt(1))){const t=st(0,0,"");return t.dynamic=!0,t.strValue=r,t}return n=n||j(r,e),st(n.duration,n.delay,n.easing)}(t.timings,e.errors);let r;e.currentAnimateTimings=n;let s=t.styles?t.styles:Object(i.h)({});if(5==s.type)r=this.visitKeyframes(s,e);else{let s=t.styles,o=!1;if(!s){o=!0;const t={};n.easing&&(t.easing=n.easing),s=Object(i.h)(t)}e.currentTime+=n.duration+n.delay;const a=this.visitStyle(s,e);a.isEmptyStep=o,r=a}return e.currentAnimateTimings=null,{type:4,timings:n,style:r,options:null}}visitStyle(t,e){const n=this._makeStyleAst(t,e);return this._validateStyleAst(n,e),n}_makeStyleAst(t,e){const n=[];Array.isArray(t.styles)?t.styles.forEach(t=>{"string"==typeof t?t==i.a?n.push(t):e.errors.push(`The provided style string value ${t} is not allowed.`):n.push(t)}):n.push(t.styles);let r=!1,s=null;return n.forEach(t=>{if(nt(t)){const e=t,n=e.easing;if(n&&(s=n,delete e.easing),!r)for(let t in e)if(e[t].toString().indexOf("{{")>=0){r=!0;break}}}),{type:6,styles:n,easing:s,offset:t.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(t,e){const n=e.currentAnimateTimings;let r=e.currentTime,s=e.currentTime;n&&s>0&&(s-=n.duration+n.delay),t.styles.forEach(t=>{"string"!=typeof t&&Object.keys(t).forEach(n=>{if(!this._driver.validateStyleProperty(n))return void e.errors.push(`The provided animation property "${n}" is not a supported CSS property for animations`);const i=e.collectedStyles[e.currentQuerySelector],o=i[n];let a=!0;o&&(s!=r&&s>=o.startTime&&r<=o.endTime&&(e.errors.push(`The CSS property "${n}" that exists between the times of "${o.startTime}ms" and "${o.endTime}ms" is also being animated in a parallel animation between the times of "${s}ms" and "${r}ms"`),a=!1),s=o.startTime),a&&(i[n]={startTime:s,endTime:r}),e.options&&function(t,e,n){const r=e.params||{},s=M(t);s.length&&s.forEach(t=>{r.hasOwnProperty(t)||n.push(`Unable to resolve the local animation param ${t} in the given list of values`)})}(t[n],e.options,e.errors)})})}visitKeyframes(t,e){const n={type:5,styles:[],options:null};if(!e.currentAnimateTimings)return e.errors.push("keyframes() must be placed inside of a call to animate()"),n;let r=0;const s=[];let i=!1,o=!1,a=0;const c=t.steps.map(t=>{const n=this._makeStyleAst(t,e);let c=null!=n.offset?n.offset:function(t){if("string"==typeof t)return null;let e=null;if(Array.isArray(t))t.forEach(t=>{if(nt(t)&&t.hasOwnProperty("offset")){const n=t;e=parseFloat(n.offset),delete n.offset}});else if(nt(t)&&t.hasOwnProperty("offset")){const n=t;e=parseFloat(n.offset),delete n.offset}return e}(n.styles),l=0;return null!=c&&(r++,l=n.offset=c),o=o||l<0||l>1,i=i||l<a,a=l,s.push(l),n});o&&e.errors.push("Please ensure that all keyframe offsets are between 0 and 1"),i&&e.errors.push("Please ensure that all keyframe offsets are in order");const l=t.steps.length;let u=0;r>0&&r<l?e.errors.push("Not all style() steps within the declared keyframes() contain offsets"):0==r&&(u=1/(l-1));const h=l-1,d=e.currentTime,f=e.currentAnimateTimings,p=f.duration;return c.forEach((t,r)=>{const i=u>0?r==h?1:u*r:s[r],o=i*p;e.currentTime=d+f.delay+o,f.duration=o,this._validateStyleAst(t,e),t.offset=i,n.styles.push(t)}),n}visitReference(t,e){return{type:8,animation:Q(this,F(t.animation),e),options:rt(t.options)}}visitAnimateChild(t,e){return e.depCount++,{type:9,options:rt(t.options)}}visitAnimateRef(t,e){return{type:10,animation:this.visitReference(t.animation,e),options:rt(t.options)}}visitQuery(t,e){const n=e.currentQuerySelector,r=t.options||{};e.queryCount++,e.currentQuery=t;const[s,i]=function(t){const e=!!t.split(/\s*,\s*/).find(t=>":self"==t);return e&&(t=t.replace(Y,"")),[t=t.replace(/@\*/g,".ng-trigger").replace(/@\w+/g,t=>".ng-trigger-"+t.substr(1)).replace(/:animating/g,".ng-animating"),e]}(t.selector);e.currentQuerySelector=n.length?n+" "+s:s,d(e.collectedStyles,e.currentQuerySelector,{});const o=Q(this,F(t.animation),e);return e.currentQuery=null,e.currentQuerySelector=n,{type:11,selector:s,limit:r.limit||0,optional:!!r.optional,includeSelf:i,animation:o,originalSelector:t.selector,options:rt(t.options)}}visitStagger(t,e){e.currentQuery||e.errors.push("stagger() can only be used inside of query()");const n="full"===t.timings?{duration:0,delay:0,easing:"full"}:j(t.timings,e.errors,!0);return{type:12,animation:Q(this,F(t.animation),e),timings:n,options:null}}}class et{constructor(t){this.errors=t,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles={},this.options=null}}function nt(t){return!Array.isArray(t)&&"object"==typeof t}function rt(t){var e;return t?(t=I(t)).params&&(t.params=(e=t.params)?I(e):null):t={},t}function st(t,e,n){return{duration:t,delay:e,easing:n}}function it(t,e,n,r,s,i,o=null,a=!1){return{type:1,element:t,keyframes:e,preStyleProps:n,postStyleProps:r,duration:s,delay:i,totalTime:s+i,easing:o,subTimeline:a}}class ot{constructor(){this._map=new Map}consume(t){let e=this._map.get(t);return e?this._map.delete(t):e=[],e}append(t,e){let n=this._map.get(t);n||this._map.set(t,n=[]),n.push(...e)}has(t){return this._map.has(t)}clear(){this._map.clear()}}const at=new RegExp(":enter","g"),ct=new RegExp(":leave","g");function lt(t,e,n,r,s,i={},o={},a,c,l=[]){return(new ut).buildKeyframes(t,e,n,r,s,i,o,a,c,l)}class ut{buildKeyframes(t,e,n,r,s,i,o,a,c,l=[]){c=c||new ot;const u=new dt(t,e,c,r,s,l,[]);u.options=a,u.currentTimeline.setStyles([i],null,u.errors,a),Q(this,n,u);const h=u.timelines.filter(t=>t.containsAnimation());if(h.length&&Object.keys(o).length){const t=h[h.length-1];t.allowOnlyTimelineStyles()||t.setStyles([o],null,u.errors,a)}return h.length?h.map(t=>t.buildKeyframes()):[it(e,[],[],[],0,0,"",!1)]}visitTrigger(t,e){}visitState(t,e){}visitTransition(t,e){}visitAnimateChild(t,e){const n=e.subInstructions.consume(e.element);if(n){const r=e.createSubContext(t.options),s=e.currentTimeline.currentTime,i=this._visitSubInstructions(n,r,r.options);s!=i&&e.transformIntoNewTimeline(i)}e.previousNode=t}visitAnimateRef(t,e){const n=e.createSubContext(t.options);n.transformIntoNewTimeline(),this.visitReference(t.animation,n),e.transformIntoNewTimeline(n.currentTimeline.currentTime),e.previousNode=t}_visitSubInstructions(t,e,n){let r=e.currentTimeline.currentTime;const s=null!=n.duration?x(n.duration):null,i=null!=n.delay?x(n.delay):null;return 0!==s&&t.forEach(t=>{const n=e.appendInstructionToTimeline(t,s,i);r=Math.max(r,n.duration+n.delay)}),r}visitReference(t,e){e.updateOptions(t.options,!0),Q(this,t.animation,e),e.previousNode=t}visitSequence(t,e){const n=e.subContextCount;let r=e;const s=t.options;if(s&&(s.params||s.delay)&&(r=e.createSubContext(s),r.transformIntoNewTimeline(),null!=s.delay)){6==r.previousNode.type&&(r.currentTimeline.snapshotCurrentStyles(),r.previousNode=ht);const t=x(s.delay);r.delayNextStep(t)}t.steps.length&&(t.steps.forEach(t=>Q(this,t,r)),r.currentTimeline.applyStylesToKeyframe(),r.subContextCount>n&&r.transformIntoNewTimeline()),e.previousNode=t}visitGroup(t,e){const n=[];let r=e.currentTimeline.currentTime;const s=t.options&&t.options.delay?x(t.options.delay):0;t.steps.forEach(i=>{const o=e.createSubContext(t.options);s&&o.delayNextStep(s),Q(this,i,o),r=Math.max(r,o.currentTimeline.currentTime),n.push(o.currentTimeline)}),n.forEach(t=>e.currentTimeline.mergeTimelineCollectedStyles(t)),e.transformIntoNewTimeline(r),e.previousNode=t}_visitTiming(t,e){if(t.dynamic){const n=t.strValue;return j(e.params?U(n,e.params,e.errors):n,e.errors)}return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,e){const n=e.currentAnimateTimings=this._visitTiming(t.timings,e),r=e.currentTimeline;n.delay&&(e.incrementTime(n.delay),r.snapshotCurrentStyles());const s=t.style;5==s.type?this.visitKeyframes(s,e):(e.incrementTime(n.duration),this.visitStyle(s,e),r.applyStylesToKeyframe()),e.currentAnimateTimings=null,e.previousNode=t}visitStyle(t,e){const n=e.currentTimeline,r=e.currentAnimateTimings;!r&&n.getCurrentStyleProperties().length&&n.forwardFrame();const s=r&&r.easing||t.easing;t.isEmptyStep?n.applyEmptyStep(s):n.setStyles(t.styles,s,e.errors,e.options),e.previousNode=t}visitKeyframes(t,e){const n=e.currentAnimateTimings,r=e.currentTimeline.duration,s=n.duration,i=e.createSubContext().currentTimeline;i.easing=n.easing,t.styles.forEach(t=>{i.forwardTime((t.offset||0)*s),i.setStyles(t.styles,t.easing,e.errors,e.options),i.applyStylesToKeyframe()}),e.currentTimeline.mergeTimelineCollectedStyles(i),e.transformIntoNewTimeline(r+s),e.previousNode=t}visitQuery(t,e){const n=e.currentTimeline.currentTime,r=t.options||{},s=r.delay?x(r.delay):0;s&&(6===e.previousNode.type||0==n&&e.currentTimeline.getCurrentStyleProperties().length)&&(e.currentTimeline.snapshotCurrentStyles(),e.previousNode=ht);let i=n;const o=e.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!r.optional,e.errors);e.currentQueryTotal=o.length;let a=null;o.forEach((n,r)=>{e.currentQueryIndex=r;const o=e.createSubContext(t.options,n);s&&o.delayNextStep(s),n===e.element&&(a=o.currentTimeline),Q(this,t.animation,o),o.currentTimeline.applyStylesToKeyframe(),i=Math.max(i,o.currentTimeline.currentTime)}),e.currentQueryIndex=0,e.currentQueryTotal=0,e.transformIntoNewTimeline(i),a&&(e.currentTimeline.mergeTimelineCollectedStyles(a),e.currentTimeline.snapshotCurrentStyles()),e.previousNode=t}visitStagger(t,e){const n=e.parentContext,r=e.currentTimeline,s=t.timings,i=Math.abs(s.duration),o=i*(e.currentQueryTotal-1);let a=i*e.currentQueryIndex;switch(s.duration<0?"reverse":s.easing){case"reverse":a=o-a;break;case"full":a=n.currentStaggerTime}const c=e.currentTimeline;a&&c.delayNextStep(a);const l=c.currentTime;Q(this,t.animation,e),e.previousNode=t,n.currentStaggerTime=r.currentTime-l+(r.startTime-n.currentTimeline.startTime)}}const ht={};class dt{constructor(t,e,n,r,s,i,o,a){this._driver=t,this.element=e,this.subInstructions=n,this._enterClassName=r,this._leaveClassName=s,this.errors=i,this.timelines=o,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=ht,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=a||new ft(this._driver,e,0),o.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,e){if(!t)return;const n=t;let r=this.options;null!=n.duration&&(r.duration=x(n.duration)),null!=n.delay&&(r.delay=x(n.delay));const s=n.params;if(s){let t=r.params;t||(t=this.options.params={}),Object.keys(s).forEach(n=>{e&&t.hasOwnProperty(n)||(t[n]=U(s[n],t,this.errors))})}}_copyOptions(){const t={};if(this.options){const e=this.options.params;if(e){const n=t.params={};Object.keys(e).forEach(t=>{n[t]=e[t]})}}return t}createSubContext(t=null,e,n){const r=e||this.element,s=new dt(this._driver,r,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(r,n||0));return s.previousNode=this.previousNode,s.currentAnimateTimings=this.currentAnimateTimings,s.options=this._copyOptions(),s.updateOptions(t),s.currentQueryIndex=this.currentQueryIndex,s.currentQueryTotal=this.currentQueryTotal,s.parentContext=this,this.subContextCount++,s}transformIntoNewTimeline(t){return this.previousNode=ht,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,e,n){const r={duration:null!=e?e:t.duration,delay:this.currentTimeline.currentTime+(null!=n?n:0)+t.delay,easing:""},s=new pt(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,r,t.stretchStartingKeyframe);return this.timelines.push(s),r}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,e,n,r,s,i){let o=[];if(r&&o.push(this.element),t.length>0){t=(t=t.replace(at,"."+this._enterClassName)).replace(ct,"."+this._leaveClassName);let e=this._driver.query(this.element,t,1!=n);0!==n&&(e=n<0?e.slice(e.length+n,e.length):e.slice(0,n)),o.push(...e)}return s||0!=o.length||i.push(`\`query("${e}")\` returned zero elements. (Use \`query("${e}", { optional: true })\` if you wish to allow this.)`),o}}class ft{constructor(t,e,n,r){this._driver=t,this.element=e,this.startTime=n,this._elementTimelineStylesLookup=r,this.duration=0,this._previousKeyframe={},this._currentKeyframe={},this._keyframes=new Map,this._styleSummary={},this._pendingStyles={},this._backFill={},this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._localTimelineStyles=Object.create(this._backFill,{}),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(e),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(e,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.getCurrentStyleProperties().length>0;default:return!0}}getCurrentStyleProperties(){return Object.keys(this._currentKeyframe)}get currentTime(){return this.startTime+this.duration}delayNextStep(t){const e=1==this._keyframes.size&&Object.keys(this._pendingStyles).length;this.duration||e?(this.forwardTime(this.currentTime+t),e&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,e){return this.applyStylesToKeyframe(),new ft(this._driver,t,e||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=Object.create(this._backFill,{}),this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=1,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,e){this._localTimelineStyles[t]=e,this._globalTimelineStyles[t]=e,this._styleSummary[t]={time:this.currentTime,value:e}}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&(this._previousKeyframe.easing=t),Object.keys(this._globalTimelineStyles).forEach(t=>{this._backFill[t]=this._globalTimelineStyles[t]||i.a,this._currentKeyframe[t]=i.a}),this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,e,n,r){e&&(this._previousKeyframe.easing=e);const s=r&&r.params||{},o=function(t,e){const n={};let r;return t.forEach(t=>{"*"===t?(r=r||Object.keys(e),r.forEach(t=>{n[t]=i.a})):A(t,!1,n)}),n}(t,this._globalTimelineStyles);Object.keys(o).forEach(t=>{const e=U(o[t],s,n);this._pendingStyles[t]=e,this._localTimelineStyles.hasOwnProperty(t)||(this._backFill[t]=this._globalTimelineStyles.hasOwnProperty(t)?this._globalTimelineStyles[t]:i.a),this._updateStyle(t,e)})}applyStylesToKeyframe(){const t=this._pendingStyles,e=Object.keys(t);0!=e.length&&(this._pendingStyles={},e.forEach(e=>{this._currentKeyframe[e]=t[e]}),Object.keys(this._localTimelineStyles).forEach(t=>{this._currentKeyframe.hasOwnProperty(t)||(this._currentKeyframe[t]=this._localTimelineStyles[t])}))}snapshotCurrentStyles(){Object.keys(this._localTimelineStyles).forEach(t=>{const e=this._localTimelineStyles[t];this._pendingStyles[t]=e,this._updateStyle(t,e)})}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){const t=[];for(let e in this._currentKeyframe)t.push(e);return t}mergeTimelineCollectedStyles(t){Object.keys(t._styleSummary).forEach(e=>{const n=this._styleSummary[e],r=t._styleSummary[e];(!n||r.time>n.time)&&this._updateStyle(e,r.value)})}buildKeyframes(){this.applyStylesToKeyframe();const t=new Set,e=new Set,n=1===this._keyframes.size&&0===this.duration;let r=[];this._keyframes.forEach((s,o)=>{const a=A(s,!0);Object.keys(a).forEach(n=>{const r=a[n];r==i.l?t.add(n):r==i.a&&e.add(n)}),n||(a.offset=o/this.duration),r.push(a)});const s=t.size?H(t.values()):[],o=e.size?H(e.values()):[];if(n){const t=r[0],e=I(t);t.offset=0,e.offset=1,r=[t,e]}return it(this.element,r,s,o,this.duration,this.startTime,this.easing,!1)}}class pt extends ft{constructor(t,e,n,r,s,i,o=!1){super(t,e,i.delay),this.element=e,this.keyframes=n,this.preStyleProps=r,this.postStyleProps=s,this._stretchStartingKeyframe=o,this.timings={duration:i.duration,delay:i.delay,easing:i.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:e,duration:n,easing:r}=this.timings;if(this._stretchStartingKeyframe&&e){const s=[],i=n+e,o=e/i,a=A(t[0],!1);a.offset=0,s.push(a);const c=A(t[0],!1);c.offset=mt(o),s.push(c);const l=t.length-1;for(let r=1;r<=l;r++){let o=A(t[r],!1);o.offset=mt((e+o.offset*n)/i),s.push(o)}n=i,e=0,r="",t=s}return it(this.element,t,this.preStyleProps,this.postStyleProps,n,e,r,!0)}}function mt(t,e=3){const n=Math.pow(10,e-1);return Math.round(t*n)/n}class gt{}class yt extends gt{normalizePropertyName(t,e){return z(t)}normalizeStyleValue(t,e,n,r){let s="";const i=n.toString().trim();if(bt[e]&&0!==n&&"0"!==n)if("number"==typeof n)s="px";else{const e=n.match(/^[+-]?[\d\.]+([a-z]*)$/);e&&0==e[1].length&&r.push(`Please provide a CSS unit value for ${t}:${n}`)}return i+s}}const bt=(()=>function(t){const e={};return t.forEach(t=>e[t]=!0),e}("width,height,minWidth,minHeight,maxWidth,maxHeight,left,top,bottom,right,fontSize,outlineWidth,outlineOffset,paddingTop,paddingLeft,paddingBottom,paddingRight,marginTop,marginLeft,marginBottom,marginRight,borderRadius,borderWidth,borderTopWidth,borderLeftWidth,borderRightWidth,borderBottomWidth,textIndent,perspective".split(",")))();function _t(t,e,n,r,s,i,o,a,c,l,u,h,d){return{type:0,element:t,triggerName:e,isRemovalTransition:s,fromState:n,fromStyles:i,toState:r,toStyles:o,timelines:a,queriedElements:c,preStyleProps:l,postStyleProps:u,totalTime:h,errors:d}}const vt={};class wt{constructor(t,e,n){this._triggerName=t,this.ast=e,this._stateStyles=n}match(t,e,n,r){return function(t,e,n,r,s){return t.some(t=>t(e,n,r,s))}(this.ast.matchers,t,e,n,r)}buildStyles(t,e,n){const r=this._stateStyles["*"],s=this._stateStyles[t],i=r?r.buildStyles(e,n):{};return s?s.buildStyles(e,n):i}build(t,e,n,r,s,i,o,a,c,l){const u=[],h=this.ast.options&&this.ast.options.params||vt,f=this.buildStyles(n,o&&o.params||vt,u),p=a&&a.params||vt,m=this.buildStyles(r,p,u),g=new Set,y=new Map,b=new Map,_="void"===r,v={params:Object.assign(Object.assign({},h),p)},w=l?[]:lt(t,e,this.ast.animation,s,i,f,m,v,c,u);let S=0;if(w.forEach(t=>{S=Math.max(t.duration+t.delay,S)}),u.length)return _t(e,this._triggerName,n,r,_,f,m,[],[],y,b,S,u);w.forEach(t=>{const n=t.element,r=d(y,n,{});t.preStyleProps.forEach(t=>r[t]=!0);const s=d(b,n,{});t.postStyleProps.forEach(t=>s[t]=!0),n!==e&&g.add(n)});const C=H(g.values());return _t(e,this._triggerName,n,r,_,f,m,w,C,y,b,S)}}class St{constructor(t,e){this.styles=t,this.defaultParams=e}buildStyles(t,e){const n={},r=I(this.defaultParams);return Object.keys(t).forEach(e=>{const n=t[e];null!=n&&(r[e]=n)}),this.styles.styles.forEach(t=>{if("string"!=typeof t){const s=t;Object.keys(s).forEach(t=>{let i=s[t];i.length>1&&(i=U(i,r,e)),n[t]=i})}}),n}}class Ct{constructor(t,e){this.name=t,this.ast=e,this.transitionFactories=[],this.states={},e.states.forEach(t=>{this.states[t.name]=new St(t.style,t.options&&t.options.params||{})}),Et(this.states,"true","1"),Et(this.states,"false","0"),e.transitions.forEach(e=>{this.transitionFactories.push(new wt(t,e,this.states))}),this.fallbackTransition=new wt(t,{type:1,animation:{type:2,steps:[],options:null},matchers:[(t,e)=>!0],options:null,queryCount:0,depCount:0},this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,e,n,r){return this.transitionFactories.find(s=>s.match(t,e,n,r))||null}matchStyles(t,e,n){return this.fallbackTransition.buildStyles(t,e,n)}}function Et(t,e,n){t.hasOwnProperty(e)?t.hasOwnProperty(n)||(t[n]=t[e]):t.hasOwnProperty(n)&&(t[e]=t[n])}const Ot=new ot;class Tt{constructor(t,e,n){this.bodyNode=t,this._driver=e,this._normalizer=n,this._animations={},this._playersById={},this.players=[]}register(t,e){const n=[],r=X(this._driver,e,n);if(n.length)throw new Error(`Unable to build the animation due to the following errors: ${n.join("\n")}`);this._animations[t]=r}_buildPlayer(t,e,n){const r=t.element,s=c(0,this._normalizer,0,t.keyframes,e,n);return this._driver.animate(r,s,t.duration,t.delay,t.easing,[],!0)}create(t,e,n={}){const r=[],s=this._animations[t];let o;const c=new Map;if(s?(o=lt(this._driver,e,s,"ng-enter","ng-leave",{},{},n,Ot,r),o.forEach(t=>{const e=d(c,t.element,{});t.postStyleProps.forEach(t=>e[t]=null)})):(r.push("The requested animation doesn't exist or has already been destroyed"),o=[]),r.length)throw new Error(`Unable to create the animation due to the following errors: ${r.join("\n")}`);c.forEach((t,e)=>{Object.keys(t).forEach(n=>{t[n]=this._driver.computeStyle(e,n,i.a)})});const l=a(o.map(t=>{const e=c.get(t.element);return this._buildPlayer(t,{},e)}));return this._playersById[t]=l,l.onDestroy(()=>this.destroy(t)),this.players.push(l),l}destroy(t){const e=this._getPlayer(t);e.destroy(),delete this._playersById[t];const n=this.players.indexOf(e);n>=0&&this.players.splice(n,1)}_getPlayer(t){const e=this._playersById[t];if(!e)throw new Error(`Unable to find the timeline player referenced by ${t}`);return e}listen(t,e,n,r){const s=h(e,"","","");return l(this._getPlayer(t),n,s,r),()=>{}}command(t,e,n,r){if("register"==n)return void this.register(t,r[0]);if("create"==n)return void this.create(t,e,r[0]||{});const s=this._getPlayer(t);switch(n){case"play":s.play();break;case"pause":s.pause();break;case"reset":s.reset();break;case"restart":s.restart();break;case"finish":s.finish();break;case"init":s.init();break;case"setPosition":s.setPosition(parseFloat(r[0]));break;case"destroy":this.destroy(t)}}}const xt=[],kt={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},jt={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0};class It{constructor(t,e=""){this.namespaceId=e;const n=t&&t.hasOwnProperty("value");if(this.value=null!=(r=n?t.value:t)?r:null,n){const e=I(t);delete e.value,this.options=e}else this.options={};var r;this.options.params||(this.options.params={})}get params(){return this.options.params}absorbOptions(t){const e=t.params;if(e){const t=this.options.params;Object.keys(e).forEach(n=>{null==t[n]&&(t[n]=e[n])})}}}const At=new It("void");class Pt{constructor(t,e,n){this.id=t,this.hostElement=e,this._engine=n,this.players=[],this._triggers={},this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+t,Ut(e,this._hostClassName)}listen(t,e,n,r){if(!this._triggers.hasOwnProperty(e))throw new Error(`Unable to listen on the animation trigger event "${n}" because the animation trigger "${e}" doesn't exist!`);if(null==n||0==n.length)throw new Error(`Unable to listen on the animation trigger "${e}" because the provided event is undefined!`);if("start"!=(s=n)&&"done"!=s)throw new Error(`The provided animation trigger event "${n}" for the animation trigger "${e}" is not supported!`);var s;const i=d(this._elementListeners,t,[]),o={name:e,phase:n,callback:r};i.push(o);const a=d(this._engine.statesByElement,t,{});return a.hasOwnProperty(e)||(Ut(t,"ng-trigger"),Ut(t,"ng-trigger-"+e),a[e]=At),()=>{this._engine.afterFlush(()=>{const t=i.indexOf(o);t>=0&&i.splice(t,1),this._triggers[e]||delete a[e]})}}register(t,e){return!this._triggers[t]&&(this._triggers[t]=e,!0)}_getTrigger(t){const e=this._triggers[t];if(!e)throw new Error(`The provided animation trigger "${t}" has not been registered!`);return e}trigger(t,e,n,r=!0){const s=this._getTrigger(e),i=new Dt(this.id,e,t);let o=this._engine.statesByElement.get(t);o||(Ut(t,"ng-trigger"),Ut(t,"ng-trigger-"+e),this._engine.statesByElement.set(t,o={}));let a=o[e];const c=new It(n,this.id);if(!(n&&n.hasOwnProperty("value"))&&a&&c.absorbOptions(a.options),o[e]=c,a||(a=At),"void"!==c.value&&a.value===c.value){if(!function(t,e){const n=Object.keys(t),r=Object.keys(e);if(n.length!=r.length)return!1;for(let s=0;s<n.length;s++){const r=n[s];if(!e.hasOwnProperty(r)||t[r]!==e[r])return!1}return!0}(a.params,c.params)){const e=[],n=s.matchStyles(a.value,a.params,e),r=s.matchStyles(c.value,c.params,e);e.length?this._engine.reportError(e):this._engine.afterFlush(()=>{N(t,n),D(t,r)})}return}const l=d(this._engine.playersByElement,t,[]);l.forEach(t=>{t.namespaceId==this.id&&t.triggerName==e&&t.queued&&t.destroy()});let u=s.matchTransition(a.value,c.value,t,c.params),h=!1;if(!u){if(!r)return;u=s.fallbackTransition,h=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:e,transition:u,fromState:a,toState:c,player:i,isFallbackTransition:h}),h||(Ut(t,"ng-animate-queued"),i.onStart(()=>{Ht(t,"ng-animate-queued")})),i.onDone(()=>{let e=this.players.indexOf(i);e>=0&&this.players.splice(e,1);const n=this._engine.playersByElement.get(t);if(n){let t=n.indexOf(i);t>=0&&n.splice(t,1)}}),this.players.push(i),l.push(i),i}deregister(t){delete this._triggers[t],this._engine.statesByElement.forEach((e,n)=>{delete e[t]}),this._elementListeners.forEach((e,n)=>{this._elementListeners.set(n,e.filter(e=>e.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);const e=this._engine.playersByElement.get(t);e&&(e.forEach(t=>t.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,e){const n=this._engine.driver.query(t,".ng-trigger",!0);n.forEach(t=>{if(t.__ng_removed)return;const n=this._engine.fetchNamespacesByElement(t);n.size?n.forEach(n=>n.triggerLeaveAnimation(t,e,!1,!0)):this.clearElementCache(t)}),this._engine.afterFlushAnimationsDone(()=>n.forEach(t=>this.clearElementCache(t)))}triggerLeaveAnimation(t,e,n,r){const s=this._engine.statesByElement.get(t);if(s){const i=[];if(Object.keys(s).forEach(e=>{if(this._triggers[e]){const n=this.trigger(t,e,"void",r);n&&i.push(n)}}),i.length)return this._engine.markElementAsRemoved(this.id,t,!0,e),n&&a(i).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){const e=this._elementListeners.get(t),n=this._engine.statesByElement.get(t);if(e&&n){const r=new Set;e.forEach(e=>{const s=e.name;if(r.has(s))return;r.add(s);const i=this._triggers[s].fallbackTransition,o=n[s]||At,a=new It("void"),c=new Dt(this.id,s,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:s,transition:i,fromState:o,toState:a,player:c,isFallbackTransition:!0})})}}removeNode(t,e){const n=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,e),this.triggerLeaveAnimation(t,e,!0))return;let r=!1;if(n.totalAnimations){const e=n.players.length?n.playersByQueriedElement.get(t):[];if(e&&e.length)r=!0;else{let e=t;for(;e=e.parentNode;)if(n.statesByElement.get(e)){r=!0;break}}}if(this.prepareLeaveAnimationListeners(t),r)n.markElementAsRemoved(this.id,t,!1,e);else{const r=t.__ng_removed;r&&r!==kt||(n.afterFlush(()=>this.clearElementCache(t)),n.destroyInnerAnimations(t),n._onRemovalComplete(t,e))}}insertNode(t,e){Ut(t,this._hostClassName)}drainQueuedTransitions(t){const e=[];return this._queue.forEach(n=>{const r=n.player;if(r.destroyed)return;const s=n.element,i=this._elementListeners.get(s);i&&i.forEach(e=>{if(e.name==n.triggerName){const r=h(s,n.triggerName,n.fromState.value,n.toState.value);r._data=t,l(n.player,e.phase,r,e.callback)}}),r.markedForDestroy?this._engine.afterFlush(()=>{r.destroy()}):e.push(n)}),this._queue=[],e.sort((t,e)=>{const n=t.transition.ast.depCount,r=e.transition.ast.depCount;return 0==n||0==r?n-r:this._engine.driver.containsElement(t.element,e.element)?1:-1})}destroy(t){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}elementContainsData(t){let e=!1;return this._elementListeners.has(t)&&(e=!0),e=!!this._queue.find(e=>e.element===t)||e,e}}class Rt{constructor(t,e,n){this.bodyNode=t,this.driver=e,this._normalizer=n,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(t,e)=>{}}_onRemovalComplete(t,e){this.onRemovalComplete(t,e)}get queuedPlayers(){const t=[];return this._namespaceList.forEach(e=>{e.players.forEach(e=>{e.queued&&t.push(e)})}),t}createNamespace(t,e){const n=new Pt(t,e,this);return e.parentNode?this._balanceNamespaceList(n,e):(this.newHostElements.set(e,n),this.collectEnterElement(e)),this._namespaceLookup[t]=n}_balanceNamespaceList(t,e){const n=this._namespaceList.length-1;if(n>=0){let r=!1;for(let s=n;s>=0;s--)if(this.driver.containsElement(this._namespaceList[s].hostElement,e)){this._namespaceList.splice(s+1,0,t),r=!0;break}r||this._namespaceList.splice(0,0,t)}else this._namespaceList.push(t);return this.namespacesByHostElement.set(e,t),t}register(t,e){let n=this._namespaceLookup[t];return n||(n=this.createNamespace(t,e)),n}registerTrigger(t,e,n){let r=this._namespaceLookup[t];r&&r.register(e,n)&&this.totalAnimations++}destroy(t,e){if(!t)return;const n=this._fetchNamespace(t);this.afterFlush(()=>{this.namespacesByHostElement.delete(n.hostElement),delete this._namespaceLookup[t];const e=this._namespaceList.indexOf(n);e>=0&&this._namespaceList.splice(e,1)}),this.afterFlushAnimationsDone(()=>n.destroy(e))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){const e=new Set,n=this.statesByElement.get(t);if(n){const t=Object.keys(n);for(let r=0;r<t.length;r++){const s=n[t[r]].namespaceId;if(s){const t=this._fetchNamespace(s);t&&e.add(t)}}}return e}trigger(t,e,n,r){if(Nt(e)){const s=this._fetchNamespace(t);if(s)return s.trigger(e,n,r),!0}return!1}insertNode(t,e,n,r){if(!Nt(e))return;const s=e.__ng_removed;if(s&&s.setForRemoval){s.setForRemoval=!1,s.setForMove=!0;const t=this.collectedLeaveElements.indexOf(e);t>=0&&this.collectedLeaveElements.splice(t,1)}if(t){const r=this._fetchNamespace(t);r&&r.insertNode(e,n)}r&&this.collectEnterElement(e)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,e){e?this.disabledNodes.has(t)||(this.disabledNodes.add(t),Ut(t,"ng-animate-disabled")):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),Ht(t,"ng-animate-disabled"))}removeNode(t,e,n,r){if(Nt(e)){const s=t?this._fetchNamespace(t):null;if(s?s.removeNode(e,r):this.markElementAsRemoved(t,e,!1,r),n){const n=this.namespacesByHostElement.get(e);n&&n.id!==t&&n.removeNode(e,r)}}else this._onRemovalComplete(e,r)}markElementAsRemoved(t,e,n,r){this.collectedLeaveElements.push(e),e.__ng_removed={namespaceId:t,setForRemoval:r,hasAnimation:n,removedBeforeQueried:!1}}listen(t,e,n,r,s){return Nt(e)?this._fetchNamespace(t).listen(e,n,r,s):()=>{}}_buildInstruction(t,e,n,r,s){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,n,r,t.fromState.options,t.toState.options,e,s)}destroyInnerAnimations(t){let e=this.driver.query(t,".ng-trigger",!0);e.forEach(t=>this.destroyActiveAnimationsForElement(t)),0!=this.playersByQueriedElement.size&&(e=this.driver.query(t,".ng-animating",!0),e.forEach(t=>this.finishActiveQueriedAnimationOnElement(t)))}destroyActiveAnimationsForElement(t){const e=this.playersByElement.get(t);e&&e.forEach(t=>{t.queued?t.markedForDestroy=!0:t.destroy()})}finishActiveQueriedAnimationOnElement(t){const e=this.playersByQueriedElement.get(t);e&&e.forEach(t=>t.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return a(this.players).onDone(()=>t());t()})}processLeaveNode(t){const e=t.__ng_removed;if(e&&e.setForRemoval){if(t.__ng_removed=kt,e.namespaceId){this.destroyInnerAnimations(t);const n=this._fetchNamespace(e.namespaceId);n&&n.clearElementCache(t)}this._onRemovalComplete(t,e.setForRemoval)}this.driver.matchesElement(t,".ng-animate-disabled")&&this.markElementAsDisabled(t,!1),this.driver.query(t,".ng-animate-disabled",!0).forEach(t=>{this.markElementAsDisabled(t,!1)})}flush(t=-1){let e=[];if(this.newHostElements.size&&(this.newHostElements.forEach((t,e)=>this._balanceNamespaceList(t,e)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let n=0;n<this.collectedEnterElements.length;n++)Ut(this.collectedEnterElements[n],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){const n=[];try{e=this._flushAnimations(n,t)}finally{for(let t=0;t<n.length;t++)n[t]()}}else for(let n=0;n<this.collectedLeaveElements.length;n++)this.processLeaveNode(this.collectedLeaveElements[n]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(t=>t()),this._flushFns=[],this._whenQuietFns.length){const t=this._whenQuietFns;this._whenQuietFns=[],e.length?a(e).onDone(()=>{t.forEach(t=>t())}):t.forEach(t=>t())}}reportError(t){throw new Error(`Unable to process animations due to the following failed trigger transitions\n ${t.join("\n")}`)}_flushAnimations(t,e){const n=new ot,r=[],s=new Map,o=[],c=new Map,l=new Map,u=new Map,h=new Set;this.disabledNodes.forEach(t=>{h.add(t);const e=this.driver.query(t,".ng-animate-queued",!0);for(let n=0;n<e.length;n++)h.add(e[n])});const f=this.bodyNode,p=Array.from(this.statesByElement.keys()),m=Mt(p,this.collectedEnterElements),g=new Map;let y=0;m.forEach((t,e)=>{const n="ng-enter"+y++;g.set(e,n),t.forEach(t=>Ut(t,n))});const b=[],_=new Set,v=new Set;for(let i=0;i<this.collectedLeaveElements.length;i++){const t=this.collectedLeaveElements[i],e=t.__ng_removed;e&&e.setForRemoval&&(b.push(t),_.add(t),e.hasAnimation?this.driver.query(t,".ng-star-inserted",!0).forEach(t=>_.add(t)):v.add(t))}const w=new Map,S=Mt(p,Array.from(_));S.forEach((t,e)=>{const n="ng-leave"+y++;w.set(e,n),t.forEach(t=>Ut(t,n))}),t.push(()=>{m.forEach((t,e)=>{const n=g.get(e);t.forEach(t=>Ht(t,n))}),S.forEach((t,e)=>{const n=w.get(e);t.forEach(t=>Ht(t,n))}),b.forEach(t=>{this.processLeaveNode(t)})});const C=[],E=[];for(let i=this._namespaceList.length-1;i>=0;i--)this._namespaceList[i].drainQueuedTransitions(e).forEach(t=>{const e=t.player,s=t.element;if(C.push(e),this.collectedEnterElements.length){const t=s.__ng_removed;if(t&&t.setForMove)return void e.destroy()}const i=!f||!this.driver.containsElement(f,s),a=w.get(s),h=g.get(s),p=this._buildInstruction(t,n,h,a,i);if(p.errors&&p.errors.length)E.push(p);else{if(i)return e.onStart(()=>N(s,p.fromStyles)),e.onDestroy(()=>D(s,p.toStyles)),void r.push(e);if(t.isFallbackTransition)return e.onStart(()=>N(s,p.fromStyles)),e.onDestroy(()=>D(s,p.toStyles)),void r.push(e);p.timelines.forEach(t=>t.stretchStartingKeyframe=!0),n.append(s,p.timelines),o.push({instruction:p,player:e,element:s}),p.queriedElements.forEach(t=>d(c,t,[]).push(e)),p.preStyleProps.forEach((t,e)=>{const n=Object.keys(t);if(n.length){let t=l.get(e);t||l.set(e,t=new Set),n.forEach(e=>t.add(e))}}),p.postStyleProps.forEach((t,e)=>{const n=Object.keys(t);let r=u.get(e);r||u.set(e,r=new Set),n.forEach(t=>r.add(t))})}});if(E.length){const t=[];E.forEach(e=>{t.push(`@${e.triggerName} has failed due to:\n`),e.errors.forEach(e=>t.push(`- ${e}\n`))}),C.forEach(t=>t.destroy()),this.reportError(t)}const O=new Map,T=new Map;o.forEach(t=>{const e=t.element;n.has(e)&&(T.set(e,e),this._beforeAnimationBuild(t.player.namespaceId,t.instruction,O))}),r.forEach(t=>{const e=t.element;this._getPreviousPlayers(e,!1,t.namespaceId,t.triggerName,null).forEach(t=>{d(O,e,[]).push(t),t.destroy()})});const x=b.filter(t=>zt(t,l,u)),k=new Map;Lt(k,this.driver,v,u,i.a).forEach(t=>{zt(t,l,u)&&x.push(t)});const j=new Map;m.forEach((t,e)=>{Lt(j,this.driver,new Set(t),l,i.l)}),x.forEach(t=>{const e=k.get(t),n=j.get(t);k.set(t,Object.assign(Object.assign({},e),n))});const I=[],A=[],P={};o.forEach(t=>{const{element:e,player:i,instruction:o}=t;if(n.has(e)){if(h.has(e))return i.onDestroy(()=>D(e,o.toStyles)),i.disabled=!0,i.overrideTotalTime(o.totalTime),void r.push(i);let t=P;if(T.size>1){let n=e;const r=[];for(;n=n.parentNode;){const e=T.get(n);if(e){t=e;break}r.push(n)}r.forEach(e=>T.set(e,t))}const n=this._buildAnimation(i.namespaceId,o,O,s,j,k);if(i.setRealPlayer(n),t===P)I.push(i);else{const e=this.playersByElement.get(t);e&&e.length&&(i.parentPlayer=a(e)),r.push(i)}}else N(e,o.fromStyles),i.onDestroy(()=>D(e,o.toStyles)),A.push(i),h.has(e)&&r.push(i)}),A.forEach(t=>{const e=s.get(t.element);if(e&&e.length){const n=a(e);t.setRealPlayer(n)}}),r.forEach(t=>{t.parentPlayer?t.syncPlayerEvents(t.parentPlayer):t.destroy()});for(let i=0;i<b.length;i++){const t=b[i],e=t.__ng_removed;if(Ht(t,"ng-leave"),e&&e.hasAnimation)continue;let n=[];if(c.size){let e=c.get(t);e&&e.length&&n.push(...e);let r=this.driver.query(t,".ng-animating",!0);for(let t=0;t<r.length;t++){let e=c.get(r[t]);e&&e.length&&n.push(...e)}}const r=n.filter(t=>!t.destroyed);r.length?$t(this,t,r):this.processLeaveNode(t)}return b.length=0,I.forEach(t=>{this.players.push(t),t.onDone(()=>{t.destroy();const e=this.players.indexOf(t);this.players.splice(e,1)}),t.play()}),I}elementContainsData(t,e){let n=!1;const r=e.__ng_removed;return r&&r.setForRemoval&&(n=!0),this.playersByElement.has(e)&&(n=!0),this.playersByQueriedElement.has(e)&&(n=!0),this.statesByElement.has(e)&&(n=!0),this._fetchNamespace(t).elementContainsData(e)||n}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,e,n,r,s){let i=[];if(e){const e=this.playersByQueriedElement.get(t);e&&(i=e)}else{const e=this.playersByElement.get(t);if(e){const t=!s||"void"==s;e.forEach(e=>{e.queued||(t||e.triggerName==r)&&i.push(e)})}}return(n||r)&&(i=i.filter(t=>!(n&&n!=t.namespaceId||r&&r!=t.triggerName))),i}_beforeAnimationBuild(t,e,n){const r=e.element,s=e.isRemovalTransition?void 0:t,i=e.isRemovalTransition?void 0:e.triggerName;for(const o of e.timelines){const t=o.element,a=t!==r,c=d(n,t,[]);this._getPreviousPlayers(t,a,s,i,e.toState).forEach(t=>{const e=t.getRealPlayer();e.beforeDestroy&&e.beforeDestroy(),t.destroy(),c.push(t)})}N(r,e.fromStyles)}_buildAnimation(t,e,n,r,s,o){const l=e.triggerName,u=e.element,h=[],f=new Set,p=new Set,m=e.timelines.map(e=>{const a=e.element;f.add(a);const d=a.__ng_removed;if(d&&d.removedBeforeQueried)return new i.d(e.duration,e.delay);const m=a!==u,g=function(t){const e=[];return Vt(t,e),e}((n.get(a)||xt).map(t=>t.getRealPlayer())).filter(t=>!!t.element&&t.element===a),y=s.get(a),b=o.get(a),_=c(0,this._normalizer,0,e.keyframes,y,b),v=this._buildPlayer(e,_,g);if(e.subTimeline&&r&&p.add(a),m){const e=new Dt(t,l,a);e.setRealPlayer(v),h.push(e)}return v});h.forEach(t=>{d(this.playersByQueriedElement,t.element,[]).push(t),t.onDone(()=>function(t,e,n){let r;if(t instanceof Map){if(r=t.get(e),r){if(r.length){const t=r.indexOf(n);r.splice(t,1)}0==r.length&&t.delete(e)}}else if(r=t[e],r){if(r.length){const t=r.indexOf(n);r.splice(t,1)}0==r.length&&delete t[e]}return r}(this.playersByQueriedElement,t.element,t))}),f.forEach(t=>Ut(t,"ng-animating"));const g=a(m);return g.onDestroy(()=>{f.forEach(t=>Ht(t,"ng-animating")),D(u,e.toStyles)}),p.forEach(t=>{d(r,t,[]).push(g)}),g}_buildPlayer(t,e,n){return e.length>0?this.driver.animate(t.element,e,t.duration,t.delay,t.easing,n):new i.d(t.duration,t.delay)}}class Dt{constructor(t,e,n){this.namespaceId=t,this.triggerName=e,this.element=n,this._player=new i.d,this._containsRealPlayer=!1,this._queuedCallbacks={},this.destroyed=!1,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(t){this._containsRealPlayer||(this._player=t,Object.keys(this._queuedCallbacks).forEach(e=>{this._queuedCallbacks[e].forEach(n=>l(t,e,void 0,n))}),this._queuedCallbacks={},this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){const e=this._player;e.triggerCallback&&t.onStart(()=>e.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,e){d(this._queuedCallbacks,t,[]).push(e)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return!this.queued&&this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){const e=this._player;e.triggerCallback&&e.triggerCallback(t)}}function Nt(t){return t&&1===t.nodeType}function Ft(t,e){const n=t.style.display;return t.style.display=null!=e?e:"none",n}function Lt(t,e,n,r,s){const i=[];n.forEach(t=>i.push(Ft(t)));const o=[];r.forEach((n,r)=>{const i={};n.forEach(t=>{const n=i[t]=e.computeStyle(r,t,s);n&&0!=n.length||(r.__ng_removed=jt,o.push(r))}),t.set(r,i)});let a=0;return n.forEach(t=>Ft(t,i[a++])),o}function Mt(t,e){const n=new Map;if(t.forEach(t=>n.set(t,[])),0==e.length)return n;const r=new Set(e),s=new Map;function i(t){if(!t)return 1;let e=s.get(t);if(e)return e;const o=t.parentNode;return e=n.has(o)?o:r.has(o)?1:i(o),s.set(t,e),e}return e.forEach(t=>{const e=i(t);1!==e&&n.get(e).push(t)}),n}function Ut(t,e){if(t.classList)t.classList.add(e);else{let n=t.$$classes;n||(n=t.$$classes={}),n[e]=!0}}function Ht(t,e){if(t.classList)t.classList.remove(e);else{let n=t.$$classes;n&&delete n[e]}}function $t(t,e,n){a(n).onDone(()=>t.processLeaveNode(e))}function Vt(t,e){for(let n=0;n<t.length;n++){const r=t[n];r instanceof i.k?Vt(r.players,e):e.push(r)}}function zt(t,e,n){const r=n.get(t);if(!r)return!1;let s=e.get(t);return s?r.forEach(t=>s.add(t)):e.set(t,r),n.delete(t),!0}class Bt{constructor(t,e,n){this.bodyNode=t,this._driver=e,this._triggerCache={},this.onRemovalComplete=(t,e)=>{},this._transitionEngine=new Rt(t,e,n),this._timelineEngine=new Tt(t,e,n),this._transitionEngine.onRemovalComplete=(t,e)=>this.onRemovalComplete(t,e)}registerTrigger(t,e,n,r,s){const i=t+"-"+r;let o=this._triggerCache[i];if(!o){const t=[],e=X(this._driver,s,t);if(t.length)throw new Error(`The animation trigger "${r}" has failed to build due to the following errors:\n - ${t.join("\n - ")}`);o=function(t,e){return new Ct(t,e)}(r,e),this._triggerCache[i]=o}this._transitionEngine.registerTrigger(e,r,o)}register(t,e){this._transitionEngine.register(t,e)}destroy(t,e){this._transitionEngine.destroy(t,e)}onInsert(t,e,n,r){this._transitionEngine.insertNode(t,e,n,r)}onRemove(t,e,n,r){this._transitionEngine.removeNode(t,e,r||!1,n)}disableAnimations(t,e){this._transitionEngine.markElementAsDisabled(t,e)}process(t,e,n,r){if("@"==n.charAt(0)){const[t,s]=f(n);this._timelineEngine.command(t,e,s,r)}else this._transitionEngine.trigger(t,e,n,r)}listen(t,e,n,r,s){if("@"==n.charAt(0)){const[t,r]=f(n);return this._timelineEngine.listen(t,e,r,s)}return this._transitionEngine.listen(t,e,n,r,s)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return this._transitionEngine.players.concat(this._timelineEngine.players)}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}}function qt(t,e){let n=null,r=null;return Array.isArray(e)&&e.length?(n=Gt(e[0]),e.length>1&&(r=Gt(e[e.length-1]))):e&&(n=Gt(e)),n||r?new Qt(t,n,r):null}let Qt=(()=>{class t{constructor(e,n,r){this._element=e,this._startStyles=n,this._endStyles=r,this._state=0;let s=t.initialStylesByElement.get(e);s||t.initialStylesByElement.set(e,s={}),this._initialStyles=s}start(){this._state<1&&(this._startStyles&&D(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(D(this._element,this._initialStyles),this._endStyles&&(D(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(t.initialStylesByElement.delete(this._element),this._startStyles&&(N(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(N(this._element,this._endStyles),this._endStyles=null),D(this._element,this._initialStyles),this._state=3)}}return t.initialStylesByElement=new WeakMap,t})();function Gt(t){let e=null;const n=Object.keys(t);for(let r=0;r<n.length;r++){const s=n[r];Zt(s)&&(e=e||{},e[s]=t[s])}return e}function Zt(t){return"display"===t||"position"===t}class Wt{constructor(t,e,n,r,s,i,o){this._element=t,this._name=e,this._duration=n,this._delay=r,this._easing=s,this._fillMode=i,this._onDoneFn=o,this._finished=!1,this._destroyed=!1,this._startTime=0,this._position=0,this._eventFn=t=>this._handleCallback(t)}apply(){!function(t,e){const n=ee(t,"").trim();n.length&&(function(t,e){let n=0;for(let r=0;r<t.length;r++)","===t.charAt(r)&&n++}(n),e=`${n}, ${e}`),te(t,"",e)}(this._element,`${this._duration}ms ${this._easing} ${this._delay}ms 1 normal ${this._fillMode} ${this._name}`),Xt(this._element,this._eventFn,!1),this._startTime=Date.now()}pause(){Kt(this._element,this._name,"paused")}resume(){Kt(this._element,this._name,"running")}setPosition(t){const e=Jt(this._element,this._name);this._position=t*this._duration,te(this._element,"Delay",`-${this._position}ms`,e)}getPosition(){return this._position}_handleCallback(t){const e=t._ngTestManualTimestamp||Date.now(),n=1e3*parseFloat(t.elapsedTime.toFixed(3));t.animationName==this._name&&Math.max(e-this._startTime,0)>=this._delay&&n>=this._duration&&this.finish()}finish(){this._finished||(this._finished=!0,this._onDoneFn(),Xt(this._element,this._eventFn,!0))}destroy(){this._destroyed||(this._destroyed=!0,this.finish(),function(t,e){const n=ee(t,"").split(","),r=Yt(n,e);r>=0&&(n.splice(r,1),te(t,"",n.join(",")))}(this._element,this._name))}}function Kt(t,e,n){te(t,"PlayState",n,Jt(t,e))}function Jt(t,e){const n=ee(t,"");return n.indexOf(",")>0?Yt(n.split(","),e):Yt([n],e)}function Yt(t,e){for(let n=0;n<t.length;n++)if(t[n].indexOf(e)>=0)return n;return-1}function Xt(t,e,n){n?t.removeEventListener("animationend",e):t.addEventListener("animationend",e)}function te(t,e,n,r){const s="animation"+e;if(null!=r){const e=t.style[s];if(e.length){const t=e.split(",");t[r]=n,n=t.join(",")}}t.style[s]=n}function ee(t,e){return t.style["animation"+e]||""}class ne{constructor(t,e,n,r,s,i,o,a){this.element=t,this.keyframes=e,this.animationName=n,this._duration=r,this._delay=s,this._finalStyles=o,this._specialStyles=a,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._started=!1,this.currentSnapshot={},this._state=0,this.easing=i||"linear",this.totalTime=r+s,this._buildStyler()}onStart(t){this._onStartFns.push(t)}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}destroy(){this.init(),this._state>=4||(this._state=4,this._styler.destroy(),this._flushStartFns(),this._flushDoneFns(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}_flushDoneFns(){this._onDoneFns.forEach(t=>t()),this._onDoneFns=[]}_flushStartFns(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}finish(){this.init(),this._state>=3||(this._state=3,this._styler.finish(),this._flushStartFns(),this._specialStyles&&this._specialStyles.finish(),this._flushDoneFns())}setPosition(t){this._styler.setPosition(t)}getPosition(){return this._styler.getPosition()}hasStarted(){return this._state>=2}init(){this._state>=1||(this._state=1,this._styler.apply(),this._delay&&this._styler.pause())}play(){this.init(),this.hasStarted()||(this._flushStartFns(),this._state=2,this._specialStyles&&this._specialStyles.start()),this._styler.resume()}pause(){this.init(),this._styler.pause()}restart(){this.reset(),this.play()}reset(){this._styler.destroy(),this._buildStyler(),this._styler.apply()}_buildStyler(){this._styler=new Wt(this.element,this.animationName,this._duration,this._delay,this.easing,"forwards",()=>this.finish())}triggerCallback(t){const e="start"==t?this._onStartFns:this._onDoneFns;e.forEach(t=>t()),e.length=0}beforeDestroy(){this.init();const t={};if(this.hasStarted()){const e=this._state>=3;Object.keys(this._finalStyles).forEach(n=>{"offset"!=n&&(t[n]=e?this._finalStyles[n]:G(this.element,n))})}this.currentSnapshot=t}}class re extends i.d{constructor(t,e){super(),this.element=t,this._startingStyles={},this.__initialized=!1,this._styles=E(e)}init(){!this.__initialized&&this._startingStyles&&(this.__initialized=!0,Object.keys(this._styles).forEach(t=>{this._startingStyles[t]=this.element.style[t]}),super.init())}play(){this._startingStyles&&(this.init(),Object.keys(this._styles).forEach(t=>this.element.style.setProperty(t,this._styles[t])),super.play())}destroy(){this._startingStyles&&(Object.keys(this._startingStyles).forEach(t=>{const e=this._startingStyles[t];e?this.element.style.setProperty(t,e):this.element.style.removeProperty(t)}),this._startingStyles=null,super.destroy())}}class se{constructor(){this._count=0,this._head=document.querySelector("head")}validateStyleProperty(t){return v(t)}matchesElement(t,e){return w(t,e)}containsElement(t,e){return S(t,e)}query(t,e,n){return C(t,e,n)}computeStyle(t,e,n){return window.getComputedStyle(t)[e]}buildKeyframeElement(t,e,n){n=n.map(t=>E(t));let r=`@keyframes ${e} {\n`,s="";n.forEach(t=>{s=" ";const e=parseFloat(t.offset);r+=`${s}${100*e}% {\n`,s+=" ",Object.keys(t).forEach(e=>{const n=t[e];switch(e){case"offset":return;case"easing":return void(n&&(r+=`${s}animation-timing-function: ${n};\n`));default:return void(r+=`${s}${e}: ${n};\n`)}}),r+=`${s}}\n`}),r+="}\n";const i=document.createElement("style");return i.textContent=r,i}animate(t,e,n,r,s,i=[],o){const a=i.filter(t=>t instanceof ne),c={};B(n,r)&&a.forEach(t=>{let e=t.currentSnapshot;Object.keys(e).forEach(t=>c[t]=e[t])});const l=function(t){let e={};return t&&(Array.isArray(t)?t:[t]).forEach(t=>{Object.keys(t).forEach(n=>{"offset"!=n&&"easing"!=n&&(e[n]=t[n])})}),e}(e=q(t,e,c));if(0==n)return new re(t,l);const u="gen_css_kf_"+this._count++,h=this.buildKeyframeElement(t,u,e);document.querySelector("head").appendChild(h);const d=qt(t,e),f=new ne(t,e,u,n,r,s,l,d);return f.onDestroy(()=>{var t;(t=h).parentNode.removeChild(t)}),f}}class ie{constructor(t,e,n,r){this.element=t,this.keyframes=e,this.options=n,this._specialStyles=r,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this.time=0,this.parentPlayer=null,this.currentSnapshot={},this._duration=n.duration,this._delay=n.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;const t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:{},this.domPlayer.addEventListener("finish",()=>this._onFinish())}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_triggerWebAnimation(t,e,n){return t.animate(e,n)}onStart(t){this._onStartFns.push(t)}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){void 0===this.domPlayer&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return this.domPlayer.currentTime/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){const t={};this.hasStarted()&&Object.keys(this._finalKeyframe).forEach(e=>{"offset"!=e&&(t[e]=this._finished?this._finalKeyframe[e]:G(this.element,e))}),this.currentSnapshot=t}triggerCallback(t){const e="start"==t?this._onStartFns:this._onDoneFns;e.forEach(t=>t()),e.length=0}}class oe{constructor(){this._isNativeImpl=/\{\s*\[native\s+code\]\s*\}/.test(ae().toString()),this._cssKeyframesDriver=new se}validateStyleProperty(t){return v(t)}matchesElement(t,e){return w(t,e)}containsElement(t,e){return S(t,e)}query(t,e,n){return C(t,e,n)}computeStyle(t,e,n){return window.getComputedStyle(t)[e]}overrideWebAnimationsSupport(t){this._isNativeImpl=t}animate(t,e,n,r,s,i=[],o){if(!o&&!this._isNativeImpl)return this._cssKeyframesDriver.animate(t,e,n,r,s,i);const a={duration:n,delay:r,fill:0==r?"both":"forwards"};s&&(a.easing=s);const c={},l=i.filter(t=>t instanceof ie);B(n,r)&&l.forEach(t=>{let e=t.currentSnapshot;Object.keys(e).forEach(t=>c[t]=e[t])});const u=qt(t,e=q(t,e=e.map(t=>A(t,!1)),c));return new ie(t,e,a,u)}}function ae(){return"undefined"!=typeof window&&void 0!==window.document&&Element.prototype.animate||{}}var ce=n("ofXK");let le=(()=>{class t extends i.b{constructor(t,e){super(),this._nextAnimationId=0,this._renderer=t.createRenderer(e.body,{id:"0",encapsulation:s.Y.None,styles:[],data:{animation:[]}})}build(t){const e=this._nextAnimationId.toString();this._nextAnimationId++;const n=Array.isArray(t)?Object(i.f)(t):t;return de(this._renderer,null,e,"register",[n]),new ue(e,this._renderer)}}return t.\u0275fac=function(e){return new(e||t)(s.ec(s.N),s.ec(ce.d))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();class ue extends i.c{constructor(t,e){super(),this._id=t,this._renderer=e}create(t,e){return new he(this._id,t,e||{},this._renderer)}}class he{constructor(t,e,n,r){this.id=t,this.element=e,this._renderer=r,this.parentPlayer=null,this._started=!1,this.totalTime=0,this._command("create",n)}_listen(t,e){return this._renderer.listen(this.element,`@@${this.id}:${t}`,e)}_command(t,...e){return de(this._renderer,this.element,this.id,t,e)}onDone(t){this._listen("done",t)}onStart(t){this._listen("start",t)}onDestroy(t){this._listen("destroy",t)}init(){this._command("init")}hasStarted(){return this._started}play(){this._command("play"),this._started=!0}pause(){this._command("pause")}restart(){this._command("restart")}finish(){this._command("finish")}destroy(){this._command("destroy")}reset(){this._command("reset")}setPosition(t){this._command("setPosition",t)}getPosition(){var t,e;return null!==(e=null===(t=this._renderer.engine.players[+this.id])||void 0===t?void 0:t.getPosition())&&void 0!==e?e:0}}function de(t,e,n,r,s){return t.setProperty(e,`@@${n}:${r}`,s)}let fe=(()=>{class t{constructor(t,e,n){this.delegate=t,this.engine=e,this._zone=n,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,this.promise=Promise.resolve(0),e.onRemovalComplete=(t,e)=>{e&&e.parentNode(t)&&e.removeChild(t.parentNode,t)}}createRenderer(t,e){const n=this.delegate.createRenderer(t,e);if(!(t&&e&&e.data&&e.data.animation)){let t=this._rendererCache.get(n);return t||(t=new pe("",n,this.engine),this._rendererCache.set(n,t)),t}const r=e.id,s=e.id+"-"+this._currentId;this._currentId++,this.engine.register(s,t);const i=e=>{Array.isArray(e)?e.forEach(i):this.engine.registerTrigger(r,s,t,e.name,e)};return e.data.animation.forEach(i),new me(this,s,n,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){this.promise.then(()=>{this._microtaskId++})}scheduleListenerCallback(t,e,n){t>=0&&t<this._microtaskId?this._zone.run(()=>e(n)):(0==this._animationCallbacksBuffer.length&&Promise.resolve(null).then(()=>{this._zone.run(()=>{this._animationCallbacksBuffer.forEach(t=>{const[e,n]=t;e(n)}),this._animationCallbacksBuffer=[]})}),this._animationCallbacksBuffer.push([e,n]))}end(){this._cdRecurDepth--,0==this._cdRecurDepth&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}}return t.\u0275fac=function(e){return new(e||t)(s.ec(s.N),s.ec(Bt),s.ec(s.G))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();class pe{constructor(t,e,n){this.namespaceId=t,this.delegate=e,this.engine=n,this.destroyNode=this.delegate.destroyNode?t=>e.destroyNode(t):null}get data(){return this.delegate.data}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.delegate.destroy()}createElement(t,e){return this.delegate.createElement(t,e)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,e){this.delegate.appendChild(t,e),this.engine.onInsert(this.namespaceId,e,t,!1)}insertBefore(t,e,n,r=!0){this.delegate.insertBefore(t,e,n),this.engine.onInsert(this.namespaceId,e,t,r)}removeChild(t,e,n){this.engine.onRemove(this.namespaceId,e,this.delegate,n)}selectRootElement(t,e){return this.delegate.selectRootElement(t,e)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,e,n,r){this.delegate.setAttribute(t,e,n,r)}removeAttribute(t,e,n){this.delegate.removeAttribute(t,e,n)}addClass(t,e){this.delegate.addClass(t,e)}removeClass(t,e){this.delegate.removeClass(t,e)}setStyle(t,e,n,r){this.delegate.setStyle(t,e,n,r)}removeStyle(t,e,n){this.delegate.removeStyle(t,e,n)}setProperty(t,e,n){"@"==e.charAt(0)&&"@.disabled"==e?this.disableAnimations(t,!!n):this.delegate.setProperty(t,e,n)}setValue(t,e){this.delegate.setValue(t,e)}listen(t,e,n){return this.delegate.listen(t,e,n)}disableAnimations(t,e){this.engine.disableAnimations(t,e)}}class me extends pe{constructor(t,e,n,r){super(e,n,r),this.factory=t,this.namespaceId=e}setProperty(t,e,n){"@"==e.charAt(0)?"."==e.charAt(1)&&"@.disabled"==e?this.disableAnimations(t,n=void 0===n||!!n):this.engine.process(this.namespaceId,t,e.substr(1),n):this.delegate.setProperty(t,e,n)}listen(t,e,n){if("@"==e.charAt(0)){const r=function(t){switch(t){case"body":return document.body;case"document":return document;case"window":return window;default:return t}}(t);let s=e.substr(1),i="";return"@"!=s.charAt(0)&&([s,i]=function(t){const e=t.indexOf(".");return[t.substring(0,e),t.substr(e+1)]}(s)),this.engine.listen(this.namespaceId,r,s,i,t=>{this.factory.scheduleListenerCallback(t._data||-1,n,t)})}return this.delegate.listen(t,e,n)}}let ge=(()=>{class t extends Bt{constructor(t,e,n){super(t.body,e,n)}}return t.\u0275fac=function(e){return new(e||t)(s.ec(ce.d),s.ec(T),s.ec(gt))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})();const ye=[{provide:T,useFactory:function(){return"function"==typeof ae()?new oe:new se}},{provide:new s.v("AnimationModuleType"),useValue:"BrowserAnimations"},{provide:i.b,useClass:le},{provide:gt,useFactory:function(){return new yt}},{provide:Bt,useClass:ge},{provide:s.N,useFactory:function(t,e,n){return new fe(t,e,n)},deps:[r.d,Bt,s.G]}];let be=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.Sb({type:t}),t.\u0275inj=s.Rb({providers:ye,imports:[r.a]}),t})();var _e=n("tyNb");const ve=[{path:"login",loadChildren:()=>Promise.all([n.e(6),n.e(4),n.e(8)]).then(n.bind(null,"X3zk")).then(t=>t.LoginModule)},{path:"error",loadChildren:()=>n.e(29).then(n.bind(null,"9ckT")).then(t=>t.ErrorpagesModule)},{path:"",loadChildren:()=>Promise.all([n.e(6),n.e(13)]).then(n.bind(null,"HYfV")).then(t=>t.AllModulesModule),canActivate:[n("3owW").a]},{path:"**",loadChildren:()=>Promise.all([n.e(6),n.e(4),n.e(8)]).then(n.bind(null,"X3zk")).then(t=>t.LoginModule)}];let we=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.Sb({type:t}),t.\u0275inj=s.Rb({imports:[[_e.f.forRoot(ve)],_e.f]}),t})(),Se=(()=>{class t{constructor(){this.title="smarthr"}ngOnInit(){$(document).on("click","#toggle_btn",()=>($("body").hasClass("mini-sidebar")?($("body").removeClass("mini-sidebar"),$(".subdrop + ul").slideDown()):($("body").addClass("mini-sidebar"),$(".subdrop + ul").slideUp()),!1)),$(document).on("mouseover",t=>{if(t.stopPropagation(),$("body").hasClass("mini-sidebar")&&$("#toggle_btn").is(":visible"))return $(t.target).closest(".sidebar").length?($("body").addClass("expand-menu"),$(".subdrop + ul").slideDown()):($("body").removeClass("expand-menu"),$(".subdrop + ul").slideUp()),!1}),$("body").append('<div class="sidebar-overlay"></div>'),$(document).on("click","#mobile_btn",function(){return $(".main-wrapper").toggleClass("slide-nav"),$(".sidebar-overlay").toggleClass("opened"),$("html").addClass("menu-opened"),$("#task_window").removeClass("opened"),!1}),$(".sidebar-overlay").on("click",function(){var t=$(".main-wrapper");$("html").removeClass("menu-opened"),$(this).removeClass("opened"),t.removeClass("slide-nav"),$(".sidebar-overlay").removeClass("opened"),$("#task_window").removeClass("opened")})}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.Ob({type:t,selectors:[["app-root"]],decls:1,vars:0,template:function(t,e){1&t&&s.Vb(0,"router-outlet")},directives:[_e.g],styles:[".big-checkbox[_ngcontent-%COMP%]{width:30px;height:30px}"]}),t})();var Ce=n("njyG"),Ee=n("5eHb"),Oe=n("tk/3"),Te=n("LRne"),xe=n("z6cu"),ke=n("JIr8"),je=n("d//k");const Ie=[{provide:Oe.a,useClass:(()=>{class t{constructor(t,e,n){this.login=t,this.router=e,this.toastr=n}handleAuthError(t){return 403===t.status?(this.toastr.error("you are not athorized or token has been expired","error"),this.router.navigate(["error/error403"]),Object(Te.a)(t.message)):(500==t.status?(console.log(t.message),t.message.includes("jwt token has expired")&&(this.login.logout(),this.router.navigate(["login"]))):401==t.status&&(this.login.logout(),this.router.navigate(["login"])),Object(xe.a)(t))}intercept(t,e){let n=t;const r=this.login.getToken();return null!=r&&(n=n.clone({setHeaders:{Authorization:`Bearer ${r}`}})),e.handle(n).pipe(Object(ke.a)(t=>this.handleAuthError(t)))}}return t.\u0275fac=function(e){return new(e||t)(s.ec(je.a),s.ec(_e.c),s.ec(Ee.b))},t.\u0275prov=s.Qb({token:t,factory:t.\u0275fac}),t})(),multi:!0}];let Ae=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.Sb({type:t,bootstrap:[Se]}),t.\u0275inj=s.Rb({providers:[Ie,ce.e],imports:[[r.a,be,we,Ce.b,Oe.d,Ee.a.forRoot({timeOut:3e3,positionClass:"toast-bottom-right",preventDuplicates:!0,closeButton:!0})]]}),t})();n("AytR").a.production&&Object(s.ab)(),r.c().bootstrapModule(Ae).catch(t=>console.error(t))},zn8P:function(t,e){function n(t){return Promise.resolve().then(function(){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e})}n.keys=function(){return[]},n.resolve=n,t.exports=n,n.id="zn8P"}},[[0,5]]]);