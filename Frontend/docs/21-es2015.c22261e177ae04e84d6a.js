(window.webpackJsonp=window.webpackJsonp||[]).push([[21],{"5HOg":function(t,e,i){"use strict";i.d(e,"a",function(){return o});var c=i("ofXK"),a=i("fXoL");let o=(()=>{class t{transform(t,...e){let i=t.replace(/(\d{2})-(\d{2})-(\d{4})/,"$2/$1/$3");return new c.e("en-US").transform(i,"MMM d, y")}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=a.Tb({name:"customDate",type:t,pure:!0}),t})()},Vc9h:function(t,e,i){"use strict";i.r(e),i.d(e,"ReportsModule",function(){return it});var c=i("ofXK"),a=i("AytR"),o=i("fXoL"),n=i("tk/3");let r=(()=>{class t{constructor(t){this.http=t,this.baseUrl=a.a.baseUrl}getTestReports(){return this.http.get(`${this.baseUrl}/reports/myAttendanceRpt2`,{responseType:"blob"})}employeeAttendanceRpt(t,e,i,c){return this.http.get(`${this.baseUrl}/reports/myAttendanceRpt?rptFileName=${t}&userId=${e}&startDate=${i}&endDate=${c}&outputFileName=${t}`,{responseType:"arraybuffer"})}payslipRpt(t,e){return this.http.get(`${this.baseUrl}/reports/payslipReport?rptFileName=${t}&payslipId=${e}&outputFileName=${t}`,{responseType:"arraybuffer"})}}return t.\u0275fac=function(e){return new(e||t)(o.ec(n.c))},t.\u0275prov=o.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var s=i("3Pt+"),d=i("jhN1"),l=i("tyNb");function b(t,e){if(1&t&&(o.ac(0,"div"),o.ac(1,"div",4),o.ac(2,"div",5),o.Vb(3,"iframe",6),o.Zb(),o.Zb(),o.Zb()),2&t){const t=o.jc();o.Ib(3),o.Jb("src",t.dataLocalUrl,o.Ec)}}let u=(()=>{class t{constructor(t,e,i,c){this.formBuilder=t,this._reportService=e,this.domSanitizer=i,this.activatedRoute=c,this.empPayslipData={rptFileName:"PaySlip",newWindow:!0,payslipId:""}}ngOnInit(){this.loadReport()}loadReport(){this._reportService.payslipRpt(this.empPayslipData.rptFileName,this.empPayslipData.payslipId).subscribe(t=>{const e=new Blob([t],{type:"application/pdf"}),i=URL.createObjectURL(e);this.dataLocalUrl=this.domSanitizer.bypassSecurityTrustResourceUrl(i)})}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(s.d),o.Ub(r),o.Ub(d.b),o.Ub(l.a))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-basic-inputs"]],decls:5,vars:1,consts:[[1,"content","container-fluid"],[1,"row"],[1,"col-sm-12"],[4,"ngIf"],[1,"card"],[1,"card-body"],["type","application/pdf",2,"width","100%","height","100%","min-width","700px","min-height","750px"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.Lc(3," Hi..... "),o.Jc(4,b,4,1,"div",3),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(4),o.pc("ngIf",null!=e.dataLocalUrl))},directives:[c.m],styles:[".content[_ngcontent-%COMP%]{padding:5px 30px 30px}.btn-ripple[_ngcontent-%COMP%]{position:relative;overflow:hidden}.btn-ripple[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:absolute;background:#fff;transform:translate(-50%,-50%);pointer-events:none;border-radius:50%;-webkit-animation:animate 1s linear infinite;animation:animate 1s linear infinite}@-webkit-keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}@keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}"]}),t})();const p=function(t){return{height:t}};let m=(()=>{let t=class{constructor(t){this.ngZone=t,window.onresize=t=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){}onResize(t){this.innerHeight=t.target.innerHeight+"px"}};return t.\u0275fac=function(e){return new(e||t)(o.Ub(o.G))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-reports"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.hc("resized",function(t){return e.onResize(t)}),o.Vb(1,"router-outlet"),o.Zb()),2&t&&o.pc("ngStyle",o.tc(1,p,e.innerHeight))},directives:[c.n,l.g],styles:[""]}),t})();var h=i("IhMt"),f=i("XNiG"),g=i("njyG"),v=i("oW1M"),Z=i("5HOg");function I(t,e){if(1&t&&(o.ac(0,"tr"),o.ac(1,"td"),o.ac(2,"strong"),o.Lc(3),o.Zb(),o.Zb(),o.ac(4,"td"),o.Lc(5),o.Zb(),o.ac(6,"td"),o.Lc(7),o.kc(8,"customDate"),o.Zb(),o.ac(9,"td"),o.ac(10,"a",28),o.Vb(11,"img",29),o.Zb(),o.ac(12,"h2"),o.ac(13,"a",30),o.Lc(14),o.Zb(),o.Zb(),o.Zb(),o.ac(15,"td"),o.Lc(16),o.Zb(),o.ac(17,"td"),o.Lc(18),o.Zb(),o.ac(19,"td",24),o.ac(20,"div",31),o.ac(21,"a",32),o.Vb(22,"i",33),o.Lc(23," Pending "),o.Zb(),o.ac(24,"div",34),o.ac(25,"a",35),o.Vb(26,"i",33),o.Lc(27," Pending"),o.Zb(),o.ac(28,"a",35),o.Vb(29,"i",36),o.Lc(30," Approved"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(31,"td",25),o.ac(32,"div",37),o.ac(33,"a",38),o.ac(34,"i",39),o.Lc(35,"more_vert"),o.Zb(),o.Zb(),o.ac(36,"div",34),o.ac(37,"a",40),o.Vb(38,"i",41),o.Lc(39," Edit"),o.Zb(),o.ac(40,"a",42),o.Vb(41,"i",43),o.Lc(42," Delete"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&t){const t=e.$implicit,i=e.index;o.Ib(3),o.Mc(t.item),o.Ib(2),o.Mc(t.purchaseFrom),o.Ib(2),o.Mc(o.lc(8,7,t.purchaseDate)),o.Ib(4),o.rc("src","assets/img/profiles/avatar-",i+1,".jpg",o.Fc),o.Ib(3),o.Mc(t.purchasedBy),o.Ib(2),o.Mc(t.amount),o.Ib(2),o.Mc(t.paidBy)}}function y(t,e){1&t&&(o.ac(0,"tr"),o.ac(1,"td",44),o.ac(2,"h5",45),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}const L=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let x=(()=>{class t{constructor(t){this.allModuleService=t,this.dtOptions={},this.url="expenseReport",this.allExpensesReport=[],this.rows=[],this.srch=[],this.dtTrigger=new f.a,this.pipe=new c.e("en-US")}ngOnInit(){$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur"),this.getExpensesReport(),this.dtOptions={pageLength:10,dom:"lrtip"}}getExpensesReport(){this.allModuleService.get(this.url).subscribe(t=>{this.allExpensesReport=t,this.dtTrigger.next(),this.rows=this.allExpensesReport,this.srch=[...this.rows]})}searchStatus(t){this.rows.splice(0,this.rows.length);let e=this.srch.filter(function(e){return t=t.toLowerCase(),-1!==e.purchasedBy.toLowerCase().indexOf(t)||!t});this.rows.push(...e)}searchByFrom(t){let e=this.pipe.transform(t,"dd-MM-yyyy");this.rows.splice(0,this.rows.length);let i=this.srch.filter(function(t){return-1!==t.purchaseDate.indexOf(e)||!e});this.rows.push(...i),$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur")}searchByTo(t){let e=this.pipe.transform(t,"dd-MM-yyyy");this.rows.splice(0,this.rows.length);let i=this.srch.filter(function(t){return-1!==t.purchaseDate.indexOf(e)||!e});this.rows.push(...i),$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur")}ngOnDestroy(){this.dtTrigger.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(h.a))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-expense-report"]],viewQuery:function(t,e){if(1&t&&o.Rc(g.a,1),2&t){let t;o.yc(t=o.ic())&&(e.dtElement=t.first)}},decls:64,vars:8,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row"],[1,"col-sm-12"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3"],[1,"form-group","form-focus","select-focus"],[1,"select","form-control",3,"input"],["value",""],["value","Loren Gatlin"],["value","Tarah Shropshire"],[1,"focus-label"],[1,"form-group","form-focus"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"btn","btn-success","btn-block"],[1,"col-md-12"],[1,"table-responsive"],["datatable","",1,"table","table-striped","custom-table","mb-0","datatable",3,"dtOptions","dtTrigger"],[1,"text-center"],[1,"text-right"],[4,"ngFor","ngForOf"],[4,"ngIf"],["routerLink","/employees/employeeprofile",1,"avatar","avatar-xs"],["alt","",3,"src"],["routerLink","/employees/employeeprofile"],[1,"dropdown","action-label"],["data-toggle","dropdown","aria-expanded","false",1,"btn","btn-white","btn-sm","btn-rounded","dropdown-toggle"],[1,"fa","fa-dot-circle-o","text-danger"],[1,"dropdown-menu","dropdown-menu-right"],[1,"dropdown-item"],[1,"fa","fa-dot-circle-o","text-success"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],["data-toggle","modal","data-target","#edit_leave",1,"dropdown-item"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_approve",1,"dropdown-item"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Expense Report"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Expense Report"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",9),o.ac(13,"div",10),o.ac(14,"div",11),o.ac(15,"select",12),o.hc("input",function(t){return e.searchStatus(t.target.value)}),o.ac(16,"option",13),o.Lc(17,"Select buyer"),o.Zb(),o.ac(18,"option",14),o.Lc(19,"Loren Gatlin"),o.Zb(),o.ac(20,"option",15),o.Lc(21,"Tarah Shropshire"),o.Zb(),o.Zb(),o.ac(22,"label",16),o.Lc(23,"Purchased By"),o.Zb(),o.Zb(),o.Zb(),o.ac(24,"div",10),o.ac(25,"div",17),o.ac(26,"div",18),o.ac(27,"input",19),o.hc("bsValueChange",function(t){return e.searchByFrom(t)}),o.Zb(),o.Zb(),o.ac(28,"label",16),o.Lc(29,"From"),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",10),o.ac(31,"div",17),o.ac(32,"div",18),o.ac(33,"input",19),o.hc("bsValueChange",function(t){return e.searchByTo(t)}),o.Zb(),o.Zb(),o.ac(34,"label",16),o.Lc(35,"To"),o.Zb(),o.Zb(),o.Zb(),o.ac(36,"div",10),o.ac(37,"a",20),o.Lc(38," Search "),o.Zb(),o.Zb(),o.Zb(),o.ac(39,"div",2),o.ac(40,"div",21),o.ac(41,"div",22),o.ac(42,"table",23),o.ac(43,"thead"),o.ac(44,"tr"),o.ac(45,"th"),o.Lc(46,"Item"),o.Zb(),o.ac(47,"th"),o.Lc(48,"Purchase From"),o.Zb(),o.ac(49,"th"),o.Lc(50,"Purchase Date"),o.Zb(),o.ac(51,"th"),o.Lc(52,"Purchased By"),o.Zb(),o.ac(53,"th"),o.Lc(54,"Amount"),o.Zb(),o.ac(55,"th"),o.Lc(56,"Paid By"),o.Zb(),o.ac(57,"th",24),o.Lc(58,"Status"),o.Zb(),o.ac(59,"th",25),o.Lc(60,"Actions"),o.Zb(),o.Zb(),o.Zb(),o.ac(61,"tbody"),o.Jc(62,I,43,9,"tr",26),o.Jc(63,y,4,0,"tr",27),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(27),o.pc("bsConfig",o.sc(6,L)),o.Ib(6),o.pc("bsConfig",o.sc(7,L)),o.Ib(9),o.pc("dtOptions",e.dtOptions)("dtTrigger",e.dtTrigger),o.Ib(20),o.pc("ngForOf",e.allExpensesReport),o.Ib(1),o.pc("ngIf",0===e.allExpensesReport.length))},directives:[l.e,s.s,s.y,v.b,v.a,g.a,c.l,c.m],pipes:[Z.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),t})();const w=function(t){return{id:t}};function F(t,e){if(1&t){const t=o.bc();o.ac(0,"tr"),o.ac(1,"td"),o.Lc(2),o.Zb(),o.ac(3,"td"),o.ac(4,"a",28),o.Lc(5),o.Zb(),o.Zb(),o.ac(6,"td"),o.Lc(7),o.Zb(),o.ac(8,"td"),o.Lc(9),o.kc(10,"customDate"),o.Zb(),o.ac(11,"td"),o.Lc(12),o.kc(13,"customDate"),o.Zb(),o.ac(14,"td"),o.Lc(15),o.Zb(),o.ac(16,"td",29),o.ac(17,"span",30),o.Lc(18),o.Zb(),o.Zb(),o.ac(19,"td",25),o.ac(20,"div",31),o.ac(21,"a",32),o.ac(22,"i",33),o.Lc(23,"more_vert"),o.Zb(),o.Zb(),o.ac(24,"div",34),o.ac(25,"a",35),o.Vb(26,"i",36),o.Lc(27," Edit"),o.Zb(),o.ac(28,"a",35),o.Vb(29,"i",37),o.Lc(30," View"),o.Zb(),o.ac(31,"a",38),o.Vb(32,"i",39),o.Lc(33," Download"),o.Zb(),o.ac(34,"a",40),o.hc("click",function(){o.Cc(t);const i=e.$implicit;return o.jc().deleteInvoice(i)}),o.Vb(35,"i",41),o.Lc(36," Delete"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()}if(2&t){const t=e.$implicit,i=e.index;o.Ib(2),o.Mc(i+1),o.Ib(3),o.Mc(t.number),o.Ib(2),o.Mc(t.client),o.Ib(2),o.Mc(o.lc(10,9,t.invoice_date)),o.Ib(3),o.Mc(o.lc(13,11,t.due_date)),o.Ib(3),o.Mc(t.grandTotal),o.Ib(3),o.Mc(t.status),o.Ib(7),o.pc("queryParams",o.tc(13,w,t.id)),o.Ib(3),o.pc("queryParams",o.tc(15,w,t.id))}}function _(t,e){1&t&&(o.ac(0,"tr"),o.ac(1,"td",42),o.ac(2,"h5",43),o.Lc(3,"No data found"),o.Zb(),o.Zb(),o.Zb())}const C=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let M=(()=>{class t{constructor(t,e){this.router=t,this.allModulesService=e,this.dtOptions={},this.invoices=[],this.rows=[],this.srch=[],this.pipe=new c.e("en-US"),this.dtTrigger=new f.a}ngOnInit(){$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur"),this.dtOptions={pageLength:10,dom:"lrtip"},this.getAllInvoices()}getAllInvoices(){this.allModulesService.get("invoiceReport").subscribe(t=>{this.invoices=t,this.dtTrigger.next(),this.rows=this.invoices,this.srch=[...this.rows]})}deleteInvoice(t){this.id=t.id}delete(){let t=Number(this.id);this.allModulesService.delete(t,"invoiceReport").subscribe(t=>{this.router.navigate(["/reports/edit-invoice-report"]),this.getAllInvoices(),this.dtElement.dtInstance.then(t=>{t.destroy()})})}searchFromDate(t){let e=this.pipe.transform(t,"dd-MM-yyyy");this.rows.splice(0,this.rows.length);let i=this.srch.filter(function(t){return-1!==t.invoice_date.indexOf(e)||!e});this.rows.push(...i),$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur")}searchToDate(t){let e=this.pipe.transform(t,"dd-MM-yyyy");this.rows.splice(0,this.rows.length);let i=this.srch.filter(function(t){return-1!==t.due_date.indexOf(e)||!e});this.rows.push(...i),$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur")}searchStatus(t){this.rows.splice(0,this.rows.length);let e=this.srch.filter(function(e){return t=t.toLowerCase(),-1!==e.client.toLowerCase().indexOf(t)||!t});this.rows.push(...e)}ngOnDestroy(){this.dtTrigger.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(l.c),o.Ub(h.a))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-invoice-report"]],viewQuery:function(t,e){if(1&t&&o.Rc(g.a,1),2&t){let t;o.yc(t=o.ic())&&(e.dtElement=t.first)}},decls:64,vars:8,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row"],[1,"col-sm-12"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3"],[1,"form-group","form-focus","select-focus"],[1,"select","form-control",3,"input"],["value",""],["value","Global Technologies"],["value","Delta Infotech"],[1,"focus-label"],[1,"form-group","form-focus"],[1,"cal-icon"],["type","text","type","text","bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],["type","text","bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"btn","btn-success","btn-block"],[1,"col-md-12"],[1,"table-responsive"],["datatable","",1,"table","table-striped","custom-table","mb-0","datatable",3,"dtOptions","dtTrigger"],[1,"text-right"],[4,"ngFor","ngForOf"],[4,"ngIf"],["routerLink","/accounts/invoice-view"],[1,"text-center"],[1,"badge","bg-inverse-success"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],["routerLink","/reports/edit-invoice-report",1,"dropdown-item",3,"queryParams"],[1,"fa","fa-pencil","m-r-5"],[1,"fa","fa-eye","m-r-5"],[1,"dropdown-item"],[1,"fa","fa-file-pdf-o","m-r-5"],["data-toggle","modal","data-target","#delete_estimate",1,"dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Invoice Report"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Invoice Report"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",9),o.ac(13,"div",10),o.ac(14,"div",11),o.ac(15,"select",12),o.hc("input",function(t){return e.searchStatus(t.target.value)}),o.ac(16,"option",13),o.Lc(17,"Select Client"),o.Zb(),o.ac(18,"option",14),o.Lc(19,"Global Technologies"),o.Zb(),o.ac(20,"option",15),o.Lc(21,"Delta Infotech"),o.Zb(),o.Zb(),o.ac(22,"label",16),o.Lc(23,"Client"),o.Zb(),o.Zb(),o.Zb(),o.ac(24,"div",10),o.ac(25,"div",17),o.ac(26,"div",18),o.ac(27,"input",19),o.hc("bsValueChange",function(t){return e.searchFromDate(t)}),o.Zb(),o.Zb(),o.ac(28,"label",16),o.Lc(29,"From"),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",10),o.ac(31,"div",17),o.ac(32,"div",18),o.ac(33,"input",20),o.hc("bsValueChange",function(t){return e.searchToDate(t)}),o.Zb(),o.Zb(),o.ac(34,"label",16),o.Lc(35,"To"),o.Zb(),o.Zb(),o.Zb(),o.ac(36,"div",10),o.ac(37,"a",21),o.Lc(38," Search "),o.Zb(),o.Zb(),o.Zb(),o.ac(39,"div",2),o.ac(40,"div",22),o.ac(41,"div",23),o.ac(42,"table",24),o.ac(43,"thead"),o.ac(44,"tr"),o.ac(45,"th"),o.Lc(46,"#"),o.Zb(),o.ac(47,"th"),o.Lc(48,"Invoice Number"),o.Zb(),o.ac(49,"th"),o.Lc(50,"Client"),o.Zb(),o.ac(51,"th"),o.Lc(52,"Created Date"),o.Zb(),o.ac(53,"th"),o.Lc(54,"Due Date"),o.Zb(),o.ac(55,"th"),o.Lc(56,"Amount"),o.Zb(),o.ac(57,"th"),o.Lc(58,"Status"),o.Zb(),o.ac(59,"th",25),o.Lc(60,"Action"),o.Zb(),o.Zb(),o.Zb(),o.ac(61,"tbody"),o.Jc(62,F,37,17,"tr",26),o.Jc(63,_,4,0,"tr",27),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(27),o.pc("bsConfig",o.sc(6,C)),o.Ib(6),o.pc("bsConfig",o.sc(7,C)),o.Ib(9),o.pc("dtOptions",e.dtOptions)("dtTrigger",e.dtTrigger),o.Ib(20),o.pc("ngForOf",e.invoices),o.Ib(1),o.pc("ngIf",0===e.invoices.length))},directives:[l.e,s.s,s.y,v.b,v.a,g.a,c.l,c.m],pipes:[Z.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),t})();var D=i("5eHb");function T(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Client is required"),o.Zb())}function O(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,T,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("client").invalid&&t.editInvoiceForm.get("client").touched)}}function S(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Project is required"),o.Zb())}function R(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,S,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("project").invalid&&t.editInvoiceForm.get("project").touched)}}function V(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Email is required"),o.Zb())}function k(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,V,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("email").invalid&&t.editInvoiceForm.get("email").touched)}}function N(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Tax is required"),o.Zb())}function j(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,N,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("tax").invalid&&t.editInvoiceForm.get("tax").touched)}}function P(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Client address is required"),o.Zb())}function U(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,P,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("client_address").invalid&&t.editInvoiceForm.get("client_address").touched)}}function J(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Billing address is required"),o.Zb())}function A(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,J,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("billing_address").invalid&&t.editInvoiceForm.get("billing_address").touched)}}function E(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Invoice date is required"),o.Zb())}function q(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,E,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("invoice_date").invalid&&t.editInvoiceForm.get("invoice_date").touched)}}function B(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Due date is required"),o.Zb())}function G(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,B,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("due_date").invalid&&t.editInvoiceForm.get("due_date").touched)}}function H(t,e){if(1&t){const t=o.bc();o.ac(0,"td"),o.ac(1,"a",56),o.hc("click",function(){o.Cc(t);const e=o.jc().index;return o.jc().removeItems(e)}),o.Vb(2,"i",57),o.Zb(),o.Zb()}}function Y(t,e){if(1&t){const t=o.bc();o.ac(0,"tr",48),o.ac(1,"td"),o.Lc(2),o.Zb(),o.ac(3,"td"),o.Vb(4,"input",49),o.Zb(),o.ac(5,"td"),o.Vb(6,"input",50),o.Zb(),o.ac(7,"td"),o.ac(8,"input",51),o.hc("input",function(){o.Cc(t);const i=e.index;return o.jc().changePrice(i)}),o.Zb(),o.Zb(),o.ac(9,"td"),o.ac(10,"input",52),o.hc("input",function(){o.Cc(t);const i=e.index;return o.jc().changePrice(i)}),o.Zb(),o.Zb(),o.ac(11,"td"),o.Vb(12,"input",53),o.Zb(),o.ac(13,"td"),o.ac(14,"a",54),o.hc("click",function(){return o.Cc(t),o.jc().addItems()}),o.Vb(15,"i",55),o.Zb(),o.Zb(),o.Jc(16,H,3,0,"td",15),o.Zb()}if(2&t){const t=e.index;o.pc("formGroupName",t),o.Ib(2),o.Mc(t+1),o.Ib(14),o.pc("ngIf",0!=t)}}function z(t,e){1&t&&(o.ac(0,"small",13),o.Lc(1," *Other information is required"),o.Zb())}function X(t,e){if(1&t&&(o.ac(0,"div"),o.Jc(1,z,2,0,"small",47),o.Zb()),2&t){const t=o.jc();o.Ib(1),o.pc("ngIf",t.editInvoiceForm.get("other_information").invalid&&t.editInvoiceForm.get("other_information").touched)}}const Q=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};function W(t,e){if(1&t&&(o.ac(0,"div"),o.ac(1,"div",4),o.ac(2,"div",5),o.Vb(3,"iframe",6),o.Zb(),o.Zb(),o.Zb()),2&t){const t=o.jc();o.Ib(3),o.Jb("src",t.dataLocalUrl,o.Ec)}}const K=[{path:"",component:m,children:[{path:"expense-report",component:x},{path:"invoice-report",component:M},{path:"edit-invoice-report",component:(()=>{class t{constructor(t,e,i,a,o){this.router=t,this.route=e,this.allModulesService=i,this.toastr=a,this.formBuilder=o,this.pipe=new c.e("en-US"),this.dateStatus=!1,this.tax=5}ngOnInit(){this.id=parseInt(this.route.snapshot.queryParams.id),this.editInvoiceForm=this.formBuilder.group({client:["",[s.w.required]],project:[""],email:[""],tax:[""],client_address:[""],billing_address:[""],invoice_date:[""],due_date:[""],other_information:[""],status:[],totalamount:"",discount:["",[s.w.required]],grandTotal:[""],items:this.formBuilder.array([])}),this.getEstimate(),this.addItems()}getEstimate(){this.allModulesService.get("invoiceReport").subscribe(t=>{this.allInvoices=t,this.edit(this.id)})}get itemsList(){return this.editInvoiceForm.get("items")}newItem(){return this.formBuilder.group({item:"",description:"",unit_cost:"",qty:"",amount:""})}addItems(){this.itemsList.push(this.newItem())}changePrice(t){let e=this.itemsList.at(t).get("qty").value,i=this.itemsList.at(t).get("unit_cost").value,c=Number(e)*Number(i);this.itemsList.at(t).get("amount").patchValue(c);let a=0;this.editInvoiceForm.get("items").value.forEach(t=>{a+=t.amount}),this.total=a,this.editInvoiceForm.get("totalamount").setValue(a),this.percentageTaxValue=this.total*Number(this.tax)/100,this.percentageDiscountValue=this.total*Number(this.editInvoiceForm.value.discount)/100,this.grandTotal=Number(this.total)+Number(this.percentageTaxValue)-Number(this.percentageDiscountValue),this.editInvoiceForm.get("grandTotal").setValue(this.grandTotal)}selected(t){this.dateStatus=t}savesend(){if(this.editInvoiceForm.valid){let t=this.editInvoiceForm.value;t.status=0,t.id=2,this.dateStatus?(this.estimateDateFormat=this.pipe.transform(this.editInvoiceForm.value.invoice_date,"dd-MM-yyyy"),this.expiryToDateFormat=this.pipe.transform(this.editInvoiceForm.value.due_date,"dd-MM-yyyy")):(this.estimateDateFormat=this.editInvoiceForm.value.invoice_date,this.expiryToDateFormat=this.editInvoiceForm.value.due_date);let e=this.editInvoiceForm.get("items").value,i=this.editInvoiceForm.value.totalamount.toString();this.allModulesService.update({number:"#INV-0001",client:this.editInvoiceForm.value.client,project:this.editInvoiceForm.value.project,invoice_date:this.estimateDateFormat,email:this.editInvoiceForm.value.email,tax:this.editInvoiceForm.value.tax,client_address:this.editInvoiceForm.value.client_address,due_date:this.expiryToDateFormat,billing_address:this.editInvoiceForm.value.billing_address,other_information:this.editInvoiceForm.value.other_information,status:"Pending",totalamount:i,id:this.id,discount:this.editInvoiceForm.value.discount,grandTotal:this.editInvoiceForm.value.grandTotal,items:[{item:e[0].item,description:e[0].description,unit_cost:e[0].unit_cost,qty:e[0].qty,amount:e[0].amount}]},"invoiceReport").subscribe(t=>{this.router.navigate(["/reports/invoice-report"]),this.toastr.success("","Edited successfully!")})}else this.toastr.error("","Please enter mandatory field!")}removeItems(t){this.itemsList.removeAt(t)}edit(t){this.editId=t;const e=this.allInvoices.findIndex(e=>e.id===t);let i=this.allInvoices[e];this.editInvoiceForm.setValue({client:i.client,project:i.project,email:i.email,tax:i.tax,client_address:i.client_address,billing_address:i.billing_address,invoice_date:i.invoice_date,due_date:i.due_date,other_information:i.other_information,status:i.status,totalamount:i.totalamount,discount:i.discount,grandTotal:i.grandTotal,items:[{item:i.items[0].item,description:i.items[0].description,unit_cost:i.items[0].unit_cost,qty:i.items[0].qty,amount:i.items[0].amount}]})}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(l.c),o.Ub(l.a),o.Ub(h.a),o.Ub(D.b),o.Ub(s.d))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-edit-invoice-report"]],decls:152,vars:37,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row"],[1,"col-sm-12"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-md-12"],[3,"formGroup"],[1,"col-sm-6","col-md-3"],[1,"form-group"],[1,"text-danger"],["name","client","formControlName","client",1,"select","form-control"],[4,"ngIf"],["formControlName","project",1,"select","form-control"],["selected",""],["type","email","name","email","formControlName","email",1,"form-control"],["name","tax","formControlName","tax",1,"select","form-control"],["rows","3","formControlName","client_address",1,"form-control"],["rows","3","formControlName","billing_address",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","invoice_date","bsDatepicker","","type","text",1,"form-control","datetimepicker",3,"bsConfig","onChange"],["type","text","formControlName","due_date","bsDatepicker","","type","text",1,"form-control","datetimepicker",3,"bsConfig","onChange"],[1,"col-md-12","col-sm-12"],[1,"table-responsive"],[1,"table","table-hover","table-white"],[2,"width","20px"],[1,"col-sm-2"],[1,"col-md-6"],[2,"width","100px"],[2,"width","80px"],["formArrayName","items"],[3,"formGroupName",4,"ngFor","ngForOf"],[1,"text-right"],[2,"text-align","right","width","230px"],["readonly","","type","text","formControlName","totalamount",1,"form-control","text-right"],["colspan","5",2,"text-align","right"],["value","5%","readonly","","type","text",1,"form-control","text-right"],["type","text","formControlName","discount",1,"form-control","text-right",3,"input"],["colspan","5",2,"text-align","right","font-weight","bold"],[2,"text-align","right","padding-right","30px","font-weight","bold","font-size","16px","width","230px"],["readonly","","type","text","formControlName","grandTotal",1,"form-control","text-right"],["rows","4","formControlName","other_information",1,"form-control"],[1,"submit-section"],[1,"btn","btn-primary","submit-btn",3,"click"],["class","text-danger",4,"ngIf"],[3,"formGroupName"],["name","item","formControlName","item","type","text",1,"form-control",2,"min-width","150px"],["formControlName","description","type","text",1,"form-control",2,"min-width","150px"],["formControlName","unit_cost","type","text",1,"form-control",2,"width","100px",3,"input"],["formControlName","qty","type","text",1,"form-control",2,"width","80px",3,"input"],["formControlName","amount","readonly","","type","text",1,"form-control",2,"width","120px"],["title","Add",1,"text-success","font-18",3,"click"],[1,"fa","fa-plus"],["title","Remove",1,"text-danger","font-18",3,"click"],[1,"fa","fa-trash-o"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Edit Invoice"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.ac(8,"a",7),o.Lc(9,"Dashboard"),o.Zb(),o.Zb(),o.ac(10,"li",8),o.Lc(11,"Edit Invoice"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",2),o.ac(13,"div",9),o.ac(14,"form",10),o.ac(15,"div",2),o.ac(16,"div",11),o.ac(17,"div",12),o.ac(18,"label"),o.Lc(19,"Client "),o.ac(20,"span",13),o.Lc(21,"*"),o.Zb(),o.Zb(),o.ac(22,"select",14),o.ac(23,"option"),o.Lc(24,"Please Select"),o.Zb(),o.ac(25,"option"),o.Lc(26,"Global Technologies"),o.Zb(),o.ac(27,"option"),o.Lc(28,"Delta Infotech"),o.Zb(),o.Zb(),o.Jc(29,O,2,1,"div",15),o.Zb(),o.Zb(),o.ac(30,"div",11),o.ac(31,"div",12),o.ac(32,"label"),o.Lc(33,"Project "),o.ac(34,"span",13),o.Lc(35,"*"),o.Zb(),o.Zb(),o.ac(36,"select",16),o.ac(37,"option"),o.Lc(38,"Select Project"),o.Zb(),o.ac(39,"option",17),o.Lc(40,"Office Management"),o.Zb(),o.ac(41,"option"),o.Lc(42,"Project Management"),o.Zb(),o.Zb(),o.Jc(43,R,2,1,"div",15),o.Zb(),o.Zb(),o.ac(44,"div",11),o.ac(45,"div",12),o.ac(46,"label"),o.Lc(47,"Email"),o.Zb(),o.Vb(48,"input",18),o.Jc(49,k,2,1,"div",15),o.Zb(),o.Zb(),o.ac(50,"div",11),o.ac(51,"div",12),o.ac(52,"label"),o.Lc(53,"Tax"),o.Zb(),o.ac(54,"select",19),o.ac(55,"option"),o.Lc(56,"Select Tax"),o.Zb(),o.ac(57,"option"),o.Lc(58,"VAT"),o.Zb(),o.ac(59,"option",17),o.Lc(60,"GST"),o.Zb(),o.ac(61,"option"),o.Lc(62,"No Tax"),o.Zb(),o.Zb(),o.Jc(63,j,2,1,"div",15),o.Zb(),o.Zb(),o.ac(64,"div",11),o.ac(65,"div",12),o.ac(66,"label"),o.Lc(67,"Client Address"),o.Zb(),o.Vb(68,"textarea",20),o.Jc(69,U,2,1,"div",15),o.Zb(),o.Zb(),o.ac(70,"div",11),o.ac(71,"div",12),o.ac(72,"label"),o.Lc(73,"Billing Address"),o.Zb(),o.Vb(74,"textarea",21),o.Jc(75,A,2,1,"div",15),o.Zb(),o.Zb(),o.ac(76,"div",11),o.ac(77,"div",12),o.ac(78,"label"),o.Lc(79,"Invoice date "),o.ac(80,"span",13),o.Lc(81,"*"),o.Zb(),o.Zb(),o.ac(82,"div",22),o.ac(83,"input",23),o.hc("onChange",function(){return e.selected(!0)}),o.Zb(),o.Jc(84,q,2,1,"div",15),o.Zb(),o.Zb(),o.Zb(),o.ac(85,"div",11),o.ac(86,"div",12),o.ac(87,"label"),o.Lc(88,"Due Date "),o.ac(89,"span",13),o.Lc(90,"*"),o.Zb(),o.Zb(),o.ac(91,"div",22),o.ac(92,"input",24),o.hc("onChange",function(){return e.selected(!0)}),o.Zb(),o.Jc(93,G,2,1,"div",15),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(94,"div",2),o.ac(95,"div",25),o.ac(96,"div",26),o.ac(97,"table",27),o.ac(98,"thead"),o.ac(99,"tr"),o.ac(100,"th",28),o.Lc(101,"#"),o.Zb(),o.ac(102,"th",29),o.Lc(103,"Item"),o.Zb(),o.ac(104,"th",30),o.Lc(105,"Description"),o.Zb(),o.ac(106,"th",31),o.Lc(107,"Unit Cost"),o.Zb(),o.ac(108,"th",32),o.Lc(109,"Qty"),o.Zb(),o.ac(110,"th"),o.Lc(111,"Amount"),o.Zb(),o.Vb(112,"th"),o.Zb(),o.Zb(),o.ac(113,"tbody",33),o.Jc(114,Y,17,3,"tr",34),o.Zb(),o.Zb(),o.Zb(),o.ac(115,"div",26),o.ac(116,"table",27),o.ac(117,"tbody"),o.ac(118,"tr"),o.Vb(119,"td"),o.Vb(120,"td"),o.Vb(121,"td"),o.Vb(122,"td"),o.ac(123,"td",35),o.Lc(124,"Total"),o.Zb(),o.ac(125,"td",36),o.Vb(126,"input",37),o.Zb(),o.Zb(),o.ac(127,"tr"),o.ac(128,"td",38),o.Lc(129,"Tax"),o.Zb(),o.ac(130,"td",36),o.Vb(131,"input",39),o.Zb(),o.Zb(),o.ac(132,"tr"),o.ac(133,"td",38),o.Lc(134," Discount % "),o.Zb(),o.ac(135,"td",36),o.ac(136,"input",40),o.hc("input",function(){return e.changePrice(0)}),o.Zb(),o.Zb(),o.Zb(),o.ac(137,"tr"),o.ac(138,"td",41),o.Lc(139," Grand Total "),o.Zb(),o.ac(140,"td",42),o.Vb(141,"input",43),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(142,"div",2),o.ac(143,"div",9),o.ac(144,"div",12),o.ac(145,"label"),o.Lc(146,"Other Information"),o.Zb(),o.Vb(147,"textarea",44),o.Jc(148,X,2,1,"div",15),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(149,"div",45),o.ac(150,"button",46),o.hc("click",function(){return e.savesend()}),o.Lc(151,"Save"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(14),o.pc("formGroup",e.editInvoiceForm),o.Ib(8),o.Mb("invalid",e.editInvoiceForm.get("client").invalid&&e.editInvoiceForm.get("client").touched),o.Ib(7),o.pc("ngIf",e.editInvoiceForm.get("client").invalid&&e.editInvoiceForm.get("client").touched),o.Ib(7),o.Mb("invalid",e.editInvoiceForm.get("project").invalid&&e.editInvoiceForm.get("project").touched),o.Ib(7),o.pc("ngIf",e.editInvoiceForm.get("project").invalid&&e.editInvoiceForm.get("project").touched),o.Ib(5),o.Mb("invalid",e.editInvoiceForm.get("email").invalid&&e.editInvoiceForm.get("email").touched),o.Ib(1),o.pc("ngIf",e.editInvoiceForm.get("email").invalid&&e.editInvoiceForm.get("email").touched),o.Ib(5),o.Mb("invalid",e.editInvoiceForm.get("tax").invalid&&e.editInvoiceForm.get("tax").touched),o.Ib(9),o.pc("ngIf",e.editInvoiceForm.get("tax").invalid&&e.editInvoiceForm.get("tax").touched),o.Ib(5),o.Mb("invalid",e.editInvoiceForm.get("client_address").invalid&&e.editInvoiceForm.get("client_address").touched),o.Ib(1),o.pc("ngIf",e.editInvoiceForm.get("client_address").invalid&&e.editInvoiceForm.get("client_address").touched),o.Ib(5),o.Mb("invalid",e.editInvoiceForm.get("billing_address").invalid&&e.editInvoiceForm.get("billing_address").touched),o.Ib(1),o.pc("ngIf",e.editInvoiceForm.get("billing_address").invalid&&e.editInvoiceForm.get("billing_address").touched),o.Ib(8),o.Mb("invalid",e.editInvoiceForm.get("invoice_date").invalid&&e.editInvoiceForm.get("invoice_date").touched),o.pc("bsConfig",o.sc(35,Q)),o.Ib(1),o.pc("ngIf",e.editInvoiceForm.get("invoice_date").invalid&&e.editInvoiceForm.get("invoice_date").touched),o.Ib(8),o.Mb("invalid",e.editInvoiceForm.get("due_date").invalid&&e.editInvoiceForm.get("due_date").touched),o.pc("bsConfig",o.sc(36,Q)),o.Ib(1),o.pc("ngIf",e.editInvoiceForm.get("due_date").invalid&&e.editInvoiceForm.get("due_date").touched),o.Ib(21),o.pc("ngForOf",e.editInvoiceForm.get("items").controls),o.Ib(22),o.Mb("invalid",e.editInvoiceForm.get("discount").invalid&&e.editInvoiceForm.get("discount").touched),o.Ib(5),o.Mb("invalid",e.editInvoiceForm.get("grandTotal").invalid&&e.editInvoiceForm.get("grandTotal").touched),o.Ib(6),o.Mb("invalid",e.editInvoiceForm.get("other_information").invalid&&e.editInvoiceForm.get("other_information").touched),o.Ib(1),o.pc("ngIf",e.editInvoiceForm.get("other_information").invalid&&e.editInvoiceForm.get("other_information").touched))},directives:[l.e,s.x,s.p,s.h,s.v,s.o,s.f,s.s,s.y,c.m,s.b,v.b,v.a,s.c,c.l,s.i],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),t})()},{path:"attendance-jsr-rpt",component:(()=>{class t{constructor(t,e,i,c){this.formBuilder=t,this._reportService=e,this.domSanitizer=i,this.activatedRoute=c,this.empAttnData={rptFileName:"EmpDailyAttendanceRpt",newWindow:!0,userId:"xxxx",startDate:"",endDate:""}}ngOnInit(){this.basicForm=this.formBuilder.group({txt:[""]}),this.empAttnData.startDate=this.activatedRoute.snapshot.queryParamMap.get("fromDate"),this.empAttnData.endDate=this.activatedRoute.snapshot.queryParamMap.get("toDate"),this.loadReport()}basicFormSubmit(){console.log("")}loadReport(){this._reportService.employeeAttendanceRpt(this.empAttnData.rptFileName,this.empAttnData.userId,this.empAttnData.startDate,this.empAttnData.endDate).subscribe(t=>{const e=new Blob([t],{type:"application/pdf"}),i=URL.createObjectURL(e);this.dataLocalUrl=this.domSanitizer.bypassSecurityTrustResourceUrl(i)})}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(s.d),o.Ub(r),o.Ub(d.b),o.Ub(l.a))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-basic-inputs"]],decls:4,vars:1,consts:[[1,"content","container-fluid"],[1,"row"],[1,"col-sm-12"],[4,"ngIf"],[1,"card"],[1,"card-body"],["type","application/pdf",2,"width","100%","height","100%","min-width","700px","min-height","750px"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.Jc(3,W,4,1,"div",3),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(3),o.pc("ngIf",null!=e.dataLocalUrl))},directives:[c.m],styles:[".content[_ngcontent-%COMP%]{padding:5px 30px 30px}.btn-ripple[_ngcontent-%COMP%]{position:relative;overflow:hidden}.btn-ripple[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:absolute;background:#fff;transform:translate(-50%,-50%);pointer-events:none;border-radius:50%;-webkit-animation:animate 1s linear infinite;animation:animate 1s linear infinite}@-webkit-keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}@keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}"]}),t})()},{path:"payslip-jsr-rpt",component:u}]}];let tt=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=o.Sb({type:t}),t.\u0275inj=o.Rb({imports:[[l.f.forChild(K)],l.f]}),t})();var et=i("0jEk");let it=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=o.Sb({type:t}),t.\u0275inj=o.Rb({imports:[[c.c,tt,g.b,s.j,s.u,et.a,v.c.forRoot()]]}),t})()}}]);