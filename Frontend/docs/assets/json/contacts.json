[{"name": "<PERSON>", "role": "Web Developer", "type": "company", "number": "9834553448", "email": "<EMAIL>", "id": 121}, {"name": "<PERSON>", "role": "React Developer", "type": "client", "number": "9834573448", "email": "<EMAIL>", "id": 122}, {"name": "<PERSON>", "role": "Angular Developer", "type": "staff", "number": "9834593448", "email": "<EMAIL>", "id": 124}, {"name": "<PERSON>", "role": "Web Developer", "type": "company", "number": "9834053448", "email": "<EMAIL>", "id": 125}, {"name": "<PERSON><PERSON><PERSON>", "role": "Front End Developer", "type": "client", "number": "9835553448", "email": "wilmerdel<PERSON>@stanley.com", "id": 126}, {"name": "<PERSON>", "role": "Back End Developer", "type": "company", "number": "6834553448", "email": "jeffery<PERSON><PERSON>@stanley.com", "id": 127}, {"name": "<PERSON><PERSON>", "role": "Web Developer", "type": "staff", "number": "9834552348", "email": "<EMAIL>", "id": 128}, {"name": "<PERSON>", "role": "Android Developer", "type": "company", "number": "9834233448", "email": "lesley<PERSON><PERSON>@stanley.com", "id": 129}]