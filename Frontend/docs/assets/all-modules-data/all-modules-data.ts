import { InMemoryDbService } from "angular-in-memory-web-api";

export class AllModulesData implements InMemoryDbService {
  createDb() {
    // Apps Module Contacts Database

    let contacts = [
      {
        name: "<PERSON>",
        role: "Web Developer",
        type: "company",
        number: "9834553448",
        email: "joh<PERSON><PERSON>@stanley.com",
        id: 121,
      },
      {
        name: "<PERSON>",
        role: "React Developer",
        type: "client",
        number: "9834573448",
        email: "<EMAIL>",
        id: 122,
      },
      {
        name: "<PERSON>",
        role: "Angular Developer",
        type: "staff",
        number: "9834593448",
        email: "<EMAIL>",
        id: 124,
      },
      {
        name: "<PERSON>",
        role: "Web Developer",
        type: "company",
        number: "9834053448",
        email: "<EMAIL>",
        id: 125,
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        role: "Front End Developer",
        type: "client",
        number: "9835553448",
        email: "wil<PERSON><PERSON><PERSON>@stanley.com",
        id: 126,
      },
      {
        name: "<PERSON>",
        role: "Back End Developer",
        type: "company",
        number: "6834553448",
        email: "<EMAIL>",
        id: 127,
      },
      {
        name: "Loren Gatlin",
        role: "Web Developer",
        type: "staff",
        number: "9834552348",
        email: "<EMAIL>",
        id: 128,
      },
      {
        name: "<PERSON> <PERSON>rauer",
        role: "Android Developer",
        type: "company",
        number: "9834233448",
        email: "<EMAIL>",
        id: 129,
      },
    ];
    // Client Database
    let clients = [
      {
        name: "Barry Cuda",
        role: "CEO",
        company: "Global Technologies",
        image: "avatar-19",
        clientId: "CLT-0008",
        email: "<EMAIL>",
        phone: "9876543210",
        status: "Active",
        id: 1,
      },
      {
        name: "Tressa Wexler",
        role: "Manager",
        company: "Delta Infotech",
        image: "avatar-29",
        clientId: "CLT-0003",
        email: "<EMAIL>",
        phone: "9876543211",
        status: "Inactive",
        id: 2,
      },
      {
        name: "Ruby Bartlett ",
        role: "CEO",
        company: "Cream Inc",
        image: "avatar-07",
        clientId: "CLT-0002",
        email: "<EMAIL>",
        phone: "9876543212",
        status: "Inactive",
        id: 3,
      },
      {
        name: "misty Tison",
        role: "CEO",
        company: "Wellware Company",
        image: "avatar-06",
        clientId: "CLT-0001",
        email: "<EMAIL>",
        phone: "9876543213",
        status: "Inactive",
        id: 4,
      },
      {
        name: "Daniel Deacon",
        role: "CEO",
        company: "Mustang Technologies",
        image: "avatar-14",
        clientId: "CLT-0006",
        email: "<EMAIL>",
        phone: "9876543214",
        status: "Active",
        id: 5,
      },
      {
        name: "Walter  Weaver",
        role: "CEO",
        company: "International Software",
        image: "avatar-18",
        clientId: "CLT-0007",
        email: "<EMAIL>",
        phone: "9876543215",
        status: "Active",
        id: 6,
      },
      {
        name: "Amanda Warren",
        role: "CEO",
        company: "Mercury Software Inc",
        image: "avatar-28",
        clientId: "CLT-0005",
        email: "<EMAIL>",
        phone: "9876543216",
        status: "Active",
        id: 7,
      },
      {
        name: "Bretty Carlson",
        role: "CEO",
        company: "Carlson Technologies",
        image: "avatar-13",
        clientId: "CLT-0004",
        email: "<EMAIL>",
        phone: "9876543217",
        status: "Inactive",
        id: 8,
      },
      {
        name: "Barry Cuda",
        role: "CEO",
        company: "Global Technologies",
        image: "avatar-19",
        clientId: "CLT-0008",
        email: "<EMAIL>",
        phone: "9876543210",
        status: "Active",
        id: 9,
      },
      {
        name: "Walter  Weaver",
        role: "CEO",
        company: "International Software",
        image: "avatar-18",
        clientId: "CLT-0007",
        email: "<EMAIL>",
        phone: "9876543215",
        status: "Active",
        id: 10,
      },
    ];
    let projects = [
      {
        name: "Office Management",
        description:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",
        endDate: "17-04-2019",
        startDate: "17-04-2019",
        priority: "High",
        projectleader: "Aravind",
        teamMember: "Prakash",
        projectId: "PRO-001",
        id: 1,
      },
      {
        name: "Hospital Administration",
        description:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",
        endDate: "17-04-2019",
        startDate: "17-04-2019",
        priority: "High",
        projectleader: "Ashok",
        teamMember: "Aravind",
        projectId: "PRO-001",
        id: 2,
      },
      {
        name: "Project Management",
        description:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",
        endDate: "17-08-2019",
        startDate: "17-07-2019",
        priority: "High",
        projectleader: "vijay",
        teamMember: "prakash",
        projectId: "PRO-001",
        id: 3,
      },
      {
        name: "Video Calling App",
        description:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",
        endDate: "17-04-2019",
        startDate: "17-03-2019",
        priority: "High",
        projectleader: "Ashok",
        teamMember: "Aravind",
        projectId: "PRO-001",
        id: 4,
      },
      {
        name: "Project Management",
        description:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",
        endDate: "17-08-2019",
        startDate: "17-07-2019",
        priority: "High",
        projectleader: "vijay",
        teamMember: "prakash",
        projectId: "PRO-001",
        id: 5,
      },
      {
        name: "Office Management",
        description:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...",
        endDate: "17-04-2019",
        startDate: "17-04-2019",
        priority: "High",
        projectleader: "Aravind",
        teamMember: "Prakash",
        projectId: "PRO-001",
        id: 6,
      },
    ];
    let leaders = [
      {
        name: "Wilmer deluna",
        id: 1,
      },
      {
        name: "John Doe",
        id: 2,
      },
      {
        name: "Wilmer deluna",
        id: 2,
      },
      {
        name: "Richard Miles",
        id: 2,
      },
      {
        name: "Mike Litorus",
        id: 2,
      },
    ];

    let employeepage = [
      {
        firstname: "John Doe",
        designation: "Web Developer",
        id: 1,
      },
      {
        firstname: "Richard Miles",
        designation: "Web Developer",
        id: 2,
      },
      {
        firstname: "John Smith",
        designation: "Web Developer",
        id: 3,
      },
      {
        firstname: "Mike Litorus",
        designation: "Web Developer",
        id: 4,
      },
      {
        firstname: "Wilmer Deluna",
        designation: "Team Leader",
        id: 5,
      },
      {
        firstname: "Jeffrey Warden",
        designation: "Web Developer",
        id: 6,
      },
      {
        firstname: "Bernardo Galaviz",
        designation: "Web Developer",
        id: 7,
      },
      {
        firstname: "Lesley Grauer",
        designation: "Team Leader",
        id: 8,
      },
      {
        firstname: "Jeffery Lalor",
        designation: "Team Leader",
        id: 9,
      },
      {
        firstname: "Loren Gatlin",
        designation: "Android  Developer",
        id: 10,
      },
      {
        firstname: "Tarah Shropshire",
        designation: "Android  Developer",
        id: 11,
      },
      {
        firstname: "Catherine Manseau",
        designation: "Web  Developer",
        id: 12,
      },
    ];
    let employeelist = [
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 1,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Front end Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 2,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "UI/Ux Designer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-05-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 3,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 4,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 5,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 6,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 7,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 8,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 9,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 10,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 11,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 12,
      },
      {
        firstname: "Catherine Manseau",
        lastname: "Manseau",
        username: "Manseau",
        password: "123445",
        confirmpassword: "123456",
        department: "software",
        designation: "Web  Developer",
        phone: "9842354254",
        email: "<EMAIL>",
        mobile: "9876543210",
        joindate: "18-04-2013",
        role: "Web Developer",
        employeeId: "FT-0001",
        company: "FT-0001",
        id: 13,
      },
    ];
    let holidays = [
      {
        id: 1,
        title: "New Year",
        holidaydate: "01-01-2020",
        day: "sun day",
      },
      {
        id: 2,
        title: "Diwali",
        holidaydate: "28-02-2020",
        day: "Thursday ",
      },
      {
        id: 3,
        title: "Christmas",
        holidaydate: "28-02-2020",
        day: "Friday",
      },
      {
        id: 4,
        title: "Ramzon",
        holidaydate: "17-02-2020",
        day: "sun day",
      },
      {
        id: 5,
        title: "Bakrid",
        holidaydate: "15-09-2020",
        day: "Saturday",
      },
    ];

    let adminleaves = [
      {
        id: 1,
        employeeName: "John Doe",
        designation: "web developer",
        leaveType: "Casual Leave",
        from: "08-09-2019",
        to: "11-09-2019",
        noofDays: "2 days",
        remainleaves: "12",
        reason: "Going to Hospital",
        status: "New",
      },
      {
        id: 2,
        employeeName: "John Smith",
        designation: "web developer",
        leaveType: "LOP",
        from: "08-09-2019",
        to: "11-09-2019",
        noofDays: "2 days",
        remainleaves: "4",
        reason: "Personnal",
        status: "Approved",
      },
      {
        id: 3,
        employeeName: "Mike Litorus",
        designation: "Android developer",
        leaveType: "Paternity Leave",
        from: "13-02-2019",
        to: "17-02-2019",
        noofDays: "5 days",
        remainleaves: "10",
        reason: "Personnal",
        status: "Declined",
      },
      {
        id: 4,
        employeeName: "Mike Litorus",
        designation: "web developer",
        leaveType: "Paternity Leave",
        from: "13-05-2019",
        to: "17-05-2019",
        noofDays: "5 days",
        remainleaves: "6",
        reason: "Medical leave",
        status: "Declined",
      },
      {
        id: 5,
        employeeName: "Catherine Manseau",
        designation: "web designer",
        leaveType: "Casual Leave",
        from: "13-04-2019",
        to: "17-06-2019",
        noofDays: "5 days",
        remainleaves: "7",
        reason: "Going to Hospital",
        status: "Approved",
      },
      {
        id: 6,
        employeeName: "Mike Litorus",
        designation: "web developer",
        leaveType: "Paternity Leave",
        from: "13-05-2019",
        to: "17-05-2019",
        noofDays: "5 days",
        remainleaves: "6",
        reason: "Medical leave",
        status: "Declined",
      },
      {
        id: 7,
        employeeName: "John Smith",
        designation: "web developer",
        leaveType: "LOP",
        from: "08-09-2019",
        to: "11-09-2019",
        noofDays: "2 days",
        remainleaves: "4",
        reason: "Personnal",
        status: "Approved",
      },
    ];

    let employeeleaves = [
      {
        id: 1,
        employeeName: "John Doe",
        designation: "web developer",
        leaveType: "Casual Leave",
        from: "08-03-2019",
        to: "09-04-2019",
        noofDays: "2 days",
        remainleaves: "12",
        reason: "Going to Hospital",
        status: "New",
      },
      {
        id: 2,
        employeeName: "John Smith",
        designation: "web developer",
        leaveType: "LOP",
        from: "24-02-2019",
        to: "25-02-2019",
        noofDays: "2 days",
        remainleaves: "4",
        reason: "Personnal",
        status: "Approved",
      },
      {
        id: 3,
        employeeName: "Mike Litorus",
        designation: "Android developer",
        leaveType: "Paternity Leave",
        from: "13-02-2019",
        to: "17-02-2019",
        noofDays: "5 days",
        remainleaves: "10",
        reason: "Personnal",
        status: "Declined",
      },
      {
        id: 4,
        employeeName: "Mike Litorus",
        designation: "web developer",
        leaveType: "Paternity Leave",
        from: "13-02-2019",
        to: "17-02-2019",
        noofDays: "5 days",
        remainleaves: "6",
        reason: "Medical leave",
        status: "Declined",
      },
      {
        id: 5,
        employeeName: "Catherine Manseau",
        designation: "web designer",
        leaveType: "Casual Leave",
        from: "13-02-2019",
        to: "17-02-2019",
        noofDays: "5 days",
        remainleaves: "7",
        reason: "Going to Hospital",
        status: "Approved",
      },
      {
        id: 6,
        employeeName: "Mike Litorus",
        designation: "web developer",
        leaveType: "Paternity Leave",
        from: "13-02-2019",
        to: "17-02-2019",
        noofDays: "5 days",
        remainleaves: "6",
        reason: "Medical leave",
        status: "Declined",
      },
      {
        id: 7,
        employeeName: "John Smith",
        designation: "web developer",
        leaveType: "LOP",
        from: "13-02-2019",
        to: "17-02-2019",
        noofDays: "2 days",
        remainleaves: "4",
        reason: "Personnal",
        status: "Approved",
      },
    ];
    let departments = [
      {
        id: 1,
        departmentName: "Web Development",
      },
      {
        id: 2,
        departmentName: "Application Development",
      },
      {
        id: 3,
        departmentName: "IT Management",
      },
      {
        id: 4,
        departmentName: "Accounts Development",
      },
      {
        id: 5,
        departmentName: "Support Management",
      },
      {
        id: 6,
        departmentName: "Marketing",
      },
    ];
    let designation = [
      {
        id: 1,
        designation: "Web Designer",
        departmentName: "Web Development",
      },
      {
        id: 2,
        designation: "Web Developer",
        departmentName: "Web Development",
      },
      {
        id: 3,
        designation: "Android Developer",
        departmentName: "Application Development",
      },
      {
        id: 4,
        designation: "IOS Developer",
        departmentName: "Application Development",
      },
      {
        id: 5,
        designation: "UI Designer",
        departmentName: "Application Development",
      },
      {
        id: 6,
        designation: "IT Technician",
        departmentName: "Application Development",
      },
      {
        id: 7,
        designation: "Product Manager",
        departmentName: "Application Development",
      },
      {
        id: 8,
        designation: "SEO Analyst",
        departmentName: "Application Development",
      },
      {
        id: 9,
        designation: "Front End Designer",
        departmentName: "Web Development",
      },
    ];

    let leads = [
      {
        leadName: "Wilmer Deluna",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Hospital Administration",
        status: "Working",
        created: "10 hours ago",
        id: 1,
      },
      {
        leadName: "Lesley Grauer",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Video Calling App",
        status: "Working",
        created: "5 Mar 2019",
        id: 2,
      },
      {
        leadName: "Jeffery Lalor",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Office Management",
        status: "Working",
        created: "27 Feb 2019",
        id: 3,
      },
      {
        leadName: "Lesley Grauer",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Video Calling App",
        status: "Working",
        created: "5 Mar 2019",
        id: 4,
      },
      {
        leadName: "Jeffery Lalor",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Office Management",
        status: "Working",
        created: "27 Feb 2019",
        id: 5,
      },
      {
        leadName: "Wilmer Deluna",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Hospital Administration",
        status: "Working",
        created: "10 hours ago",
        id: 6,
      },
      {
        leadName: "Wilmer Deluna",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Hospital Administration",
        status: "Working",
        created: "10 hours ago",
        id: 7,
      },
      {
        leadName: "Lesley Grauer",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Video Calling App",
        status: "Working",
        created: "5 Mar 2019",
        id: 8,
      },
      {
        leadName: "Jeffery Lalor",
        email: "<EMAIL>",
        phone: "9876543210",
        project: "Office Management",
        status: "Working",
        created: "27 Feb 2019",
        id: 9,
      },
    ];

    let tickets = [
      {
        ticketId: "#TKT-001",
        ticketSubject: "Laptop Issue",
        assignedStaff: "John Smith",
        client: "Delta Infotech",
        priority: "Low",
        cc: "ashok",
        assigne: "prakash",
        addfollow: "tested",
        description: "tested",
        createdDate: "05-05-2020",
        lastReply: "06-05-2020",
        status: "Approved",
        id: 1,
      },
      {
        ticketId: "#TKT-002",
        ticketSubject: "Laptop Issue",
        assignedStaff: "Mark Hentry",
        client: "International software Inc",
        priority: "High",
        cc: "ashok",
        assigne: "prakash",
        addfollow: "tested",
        description: "tested",
        createdDate: "05-05-2020",
        lastReply: "06-05-2020",
        status: "Pending",
        id: 2,
      },
      {
        ticketId: "#TKT-003",
        ticketSubject: "Mouse Issue",
        assignedStaff: "Mikel deo",
        client: "International software Inc",
        priority: "High",
        cc: "ashok",
        assigne: "prakash",
        addfollow: "tested",
        description: "tested",
        createdDate: "05-05-2020",
        lastReply: "06-05-2020",
        status: "Pending",
        id: 3,
      },
      {
        ticketId: "#TKT-004",
        ticketSubject: "Monitor Issue",
        assignedStaff: "Richared Deo",
        client: "International software Inc",
        priority: "High",
        cc: "ashok",
        assigne: "prakash",
        addfollow: "tested",
        description: "tested",
        createdDate: "05-05-2020",
        lastReply: "06-05-2020",
        status: "Pending",
        id: 4,
      },
    ];

    let timesheet = [
      {
        id: 1,
        employee: "Bernardo Galaviz",
        designation: "Web developer",
        date: "8 Mar 2019",
        deadline: "",
        project: "Video Calling App",
        assignedhours: "20",
        hrs: "12",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque",
      },
      {
        id: 2,
        employee: " Catherine Manseau",
        designation: "Android developer",
        date: "9 Mar 2019",
        deadline: "",
        totalhrs: "",
        remainHrs: "",
        project: "Video Calling App",
        assignedhours: "20",
        hrs: "12",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque",
      },
      {
        id: 3,
        employee: "Jeffry lalor Galaviz",
        designation: "Android developer",
        date: "10 Mar 2019",
        deadline: "",
        totalhrs: "",
        project: "Video Calling App",
        assignedhours: "20",
        hrs: "12",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque",
      },
      {
        id: 4,
        employee: "Jeffry Warden",
        designation: "Web developer",
        date: "11 Mar 2019",
        deadline: "",
        totalhrs: "",
        remainHrs: "",
        project: "Video Calling App",
        assignedhours: "20",
        hrs: "12",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque",
      },
      {
        id: 5,
        employee: "John doe Galaviz",
        designation: "Web developer",
        date: "13 Mar 2019",
        deadline: "",
        totalhrs: "",
        remainHrs: "",
        project: "Video Calling App",
        assignedhours: "20",
        hrs: "12",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec vel elit neque",
      },
    ];

    let overtime = [
      {
        id: 1,
        name: "Bernardo Galaviz",
        otDate: "08-03-2019",
        otHrs: "04",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 2,
        name: "John Deo",
        otDate: "25-04-2019",
        otHrs: "07",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 3,
        name: "Russia david",
        otDate: "12-09-2019",
        otHrs: "09",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 4,
        name: "Mark hentry",
        otDate: "15-10-2019",
        otHrs: "02",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 5,
        name: "Ruchared hentry",
        otDate: "23-04-2019",
        otHrs: "04",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 6,
        name: "Mark rio",
        otDate: "11-07-2019",
        otHrs: "05",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 7,
        name: "John Galaviz",
        otDate: "25-08-2019",
        otHrs: "08",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 8,
        name: "Loren Gatlin",
        otDate: "05-01-2019",
        otHrs: "5",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 9,
        name: "Tarah Shropshire",
        otDate: "05-01-2019",
        otHrs: "5",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 10,
        name: "John Doe",
        otDate: "13-01-2019",
        otHrs: "5",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 11,
        name: "John Smith",
        otDate: "20-01-2019",
        otHrs: "5",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
      {
        id: 12,
        name: "John Smith",
        otDate: "20-01-2019",
        otHrs: "5",
        otType: "Normal day OT 1.5x",
        status: "New",
        approvedBy: "Richard Miles",
        description: "Lorem ipsum dollar",
      },
    ];

    let expenses = [
      {
        item: "Dell Laptop",
        purchaseFrom: "Amazon",
        purchaseDate: "05-01-2019",
        purchasedBy: "Loren Gatlin",
        amount: "$1215",
        paidby: "Cash",
        id: 1,
      },
      {
        item: "Mac System",
        purchaseFrom: "Amazon",
        purchaseDate: "05-01-2019",
        purchasedBy: "Tarah Shropshire",
        amount: "$1215",
        paidby: "Cheque",
        id: 2,
      },
      {
        item: "Apple",
        purchaseFrom: "Amazon",
        purchaseDate: "05-01-2019",
        purchasedBy: "John Doe",
        amount: "$1215",
        paidby: "Cheque",
        id: 3,
      },
      {
        item: "HCL",
        purchaseFrom: "Amazon",
        purchaseDate: "01-01-2019",
        purchasedBy: "John Doe",
        amount: "$1215",
        paidby: "Cheque",
        id: 4,
      },
      {
        item: "HCL",
        purchaseFrom: "Flipkart",
        purchaseDate: "01-01-2019",
        purchasedBy: "Loren Gatlin",
        amount: "$1215",
        paidby: "Cheque",
        id: 5,
      },
      {
        item: "Sony",
        purchaseFrom: "Flipkart",
        purchaseDate: "20-01-2019",
        purchasedBy: "Loren Mac",
        amount: "$1215",
        paidby: "Cheque",
        id: 6,
      },
    ];
    let providentFund = [
      {
        employeeName: "Loren Mac",
        providentFundType: "Percentage of Basic Salary",
        employeeShare: "2%",
        organizationShare: "2%",
        id: 1,
      },
      {
        employeeName: "John Doe",
        providentFundType: "Percentage of Basic Salary",
        employeeShare: "4%",
        organizationShare: "5%",
        id: 2,
      },
      {
        employeeName: "Tarah Shropshire",
        providentFundType: "Percentage of Basic Salary",
        employeeShare: "9%",
        organizationShare: "5%",
        id: 3,
      },
      {
        employeeName: "John Doe",
        providentFundType: "Percentage of Basic Salary",
        employeeShare: "9%",
        organizationShare: "2%",
        id: 4,
      },
      {
        employeeName: "John michelin",
        providentFundType: "Percentage of Basic Salary",
        employeeShare: "4%",
        organizationShare: "2%",
        id: 5,
      },
      {
        employeeName: "Kennedy michelin",
        providentFundType: "Percentage of Basic Salary",
        employeeShare: "2%",
        organizationShare: "2%",
        id: 6,
      },
    ];

    let taxes = [
      {
        taxName: "VAT",
        taxPercentage: "14%",
        id: 1,
      },
      {
        taxName: "GST",
        taxPercentage: "30%",
        id: 2,
      },
      {
        taxName: "GST",
        taxPercentage: "30%",
        id: 3,
      },
      {
        taxName: "VAT",
        taxPercentage: "10%",
        id: 4,
      },
      {
        taxName: "GST",
        taxPercentage: "25%",
        id: 5,
      },
      {
        taxName: "VAT",
        taxPercentage: "20%",
        id: 6,
      },
    ];

    let policies = [
      {
        policyName: "Leave Policy",
        department: "All Departments",
        description: "Lorem ipsum dollar",
        createdDate: "19 Feb 2019",
        id: 1,
      },
      {
        policyName: "Permission Policy",
        department: "Marketing",
        description: "Lorem ipsum dollar",
        createdDate: "18 Feb 2019",
        id: 2,
      },
      {
        policyName: "Leave Policy",
        department: "All Departments",
        description: "Lorem ipsum dollar",
        createdDate: "19 Feb 2019",
        id: 3,
      },
      {
        policyName: "Permission Policy",
        department: "Marketing",
        description: "Lorem ipsum dollar",
        createdDate: "25 Feb 2019",
        id: 4,
      },
      {
        policyName: "Leave Policy",
        department: "All Departments",
        description: "Lorem ipsum dollar",
        createdDate: "18 Feb 2019",
        id: 5,
      },
      {
        policyName: "Permission Policy",
        department: "Marketing",
        description: "Lorem ipsum dollar",
        createdDate: "22 Feb 2019",
        id: 6,
      },
    ];

    let expenseReport = [
      {
        item: "Dell Laptop",
        purchaseFrom: "Amazon",
        purchaseDate: "12-01-2019",
        purchasedBy: "Loren Gatlin",
        amount: "$ 1210",
        paidBy: "Cash",
        id: 1,
      },
      {
        item: "Mac System",
        purchaseFrom: "Amazon",
        purchaseDate: "10-01-2019",
        purchasedBy: "Tarah Shropshire",
        amount: "$ 1215",
        paidBy: "Cheque",
        id: 2,
      },
      {
        item: "Dell Laptop",
        purchaseFrom: "Snap",
        purchaseDate: "24-01-2019",
        purchasedBy: "kenneth",
        amount: "$ 1205",
        paidBy: "Cash",
        id: 3,
      },
      {
        item: "Adobe System",
        purchaseFrom: "Orion",
        purchaseDate: "11-01-2019",
        purchasedBy: "John Doe",
        amount: "$ 134",
        paidBy: "Cheque",
        id: 4,
      },
      {
        item: "Adobe System",
        purchaseFrom: "Amazon",
        purchaseDate: "08-01-2019",
        purchasedBy: "John Michellin",
        amount: "$ 12",
        paidBy: "Cheque",
        id: 5,
      },
      {
        item: "Adobe System",
        purchaseFrom: "Flip",
        purchaseDate: "10-01-2019",
        purchasedBy: "Arnold",
        amount: "$ 121",
        paidBy: "Cheque",
        id: 6,
      },
    ];

    let appliedCandidates = [
      {
        name: "	John Doe",
        email: "<EMAIL>",
        phone: "9876543210",
        applyDate: "9 Feb 2019",
        id: 1,
      },
      {
        name: "Arnold",
        email: "<EMAIL>",
        phone: "9872543210",
        applyDate: "25 Mar 2019",
        id: 2,
      },
      {
        name: "kenneth",
        email: "<EMAIL>",
        phone: "9876543230",
        applyDate: "13 Feb 2019",
        id: 3,
      },
      {
        name: "Sam",
        email: "<EMAIL>",
        phone: "9876543297",
        applyDate: "25 Jan 2019",
        id: 4,
      },
      {
        name: "Michellin",
        email: "<EMAIL>",
        phone: "9876524210",
        applyDate: "26 Feb 2019",
        id: 5,
      },
      {
        name: "john Mckensey",
        email: "<EMAIL>",
        phone: "9876543410",
        applyDate: "18 Jun 2019",
        id: 6,
      },
    ];
    let knowledgeBase = [
      {
        title: "Installation & Activation",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 1,
      },
      {
        title: "Premium Members Features",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 2,
      },
      {
        title: "API Usage & Guide lines",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 3,
      },
      {
        title: "Getting Started",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 4,
      },
      {
        title: "Lorem ipsum dolor",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 5,
      },
      {
        title: "Lorem ipsum dolor",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 6,
      },
      {
        title: "Lorem ipsum dolor",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 7,
      },
      {
        title: "Lorem ipsum dolor",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 8,
      },
      {
        title: "Lorem ipsum dolor",
        list1: "Sed ut perspiciatis unde omnis?",
        list2: "Sed ut perspiciatis unde omnis?",
        list3: "Sed ut perspiciatis unde omnis?",
        list4: "Sed ut perspiciatis unde omnis?",
        list5: "Sed ut perspiciatis unde omnis?",
        id: 9,
      },
    ];

    let assets = [
      {
        assetUser: "Richard Miles",
        assetName: "Dell Laptop",
        assetId: "#AST-0001",
        assetStatus: "Pending",
        purchaseDate: "05-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "05-01-2020",
        Amount: "$1215",
        id: 1,
      },
      {
        assetUser: "John Doe",
        assetName: "Seagate Harddisk",
        assetId: "#AST-0002",
        assetStatus: "Pending",
        purchaseDate: "14-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "14-07-2019",
        Amount: "$300",
        id: 2,
      },
      {
        assetUser: "John Smith",
        assetName: "Canon Portable Printer",
        assetId: "#AST-0003",
        assetStatus: "Pending",
        purchaseDate: "14-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "14-08-2019",
        Amount: "$2500",
        id: 3,
      },
      {
        assetUser: "Mike Litorus",
        assetName: "Dell Laptop",
        assetId: "#AST-0004",
        assetStatus: "Pending",
        purchaseDate: "05-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "05-01-2020",
        Amount: "$1215",
        id: 4,
      },
      {
        assetUser: "Wilmer Deluna",
        assetName: "Seagate Harddisk",
        assetId: "#AST-0005",
        assetStatus: "Pending",
        purchaseDate: "14-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "14-01-2020",
        Amount: "$300",
        id: 5,
      },
      {
        assetUser: "Jeffrey Warden",
        assetName: "Canon Portable Printer",
        assetId: "#AST-0006",
        assetStatus: "Pending",
        purchaseDate: "14-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "14-01-2020",
        Amount: "$2500",
        id: 6,
      },
      {
        assetUser: "Bernardo Galaviz",
        assetName: "Dell Laptop",
        assetId: "#AST-0007",
        assetStatus: "Pending",
        purchaseDate: "05-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "05-02-2020",
        Amount: "$1215",
        id: 7,
      },
      {
        assetUser: "Lesley Grauer",
        assetName: "Seagate Harddisk",
        assetId: "#AST-0008",
        assetStatus: "Pending",
        purchaseDate: "14-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "14-01-2020",
        Amount: "$300",
        id: 8,
      },
      {
        assetUser: "Jeffery Lalor",
        assetName: "Canon Portable Printer",
        assetId: "#AST-0009",
        assetStatus: "Pending",
        purchaseDate: "14-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "14-01-2020",
        Amount: "$2500",
        id: 9,
      },
      {
        assetUser: "Loren Gatlin",
        assetName: "Dell Laptop",
        assetId: "#AST-0010",
        assetStatus: "Pending",
        purchaseDate: "05-01-2019",
        warrenty: "12 Months",
        warrentyEnd: "05-01-2020",
        Amount: "$1215",
        id: 10,
      },
    ];

    let employeeSalary = [
      {
        employee: "Bernardo Galaviz",
        employeeId: "FT-0007",
        email: "<EMAIL>",
        joinDate: "14-01-2019",
        role: "Web Developer",
        employeeRole: "Employee",
        status: "pending",
        salary: "76670",
        Basic: "55300",
        tDS: "7500",
        da: "11820",
        hra: "4300",
        pf: "4300",
        conveyance: "5400",
        leave: "4400",
        allowance: "2600",
        proTax: "3050",
        medAllowance: "6500",
        labourWelfare: "3900",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 1,
      },
      {
        employee: "Jeffery Lalor",
        employeeId: "FT-0009",
        email: "<EMAIL>",
        joinDate: "05-01-2019",
        role: "Team Leader",
        employeeRole: "Employee",
        status: "pending",
        salary: "63670",
        Basic: "45300",
        tDS: "7500",
        da: "10820",
        hra: "5500",
        pf: "5500",
        conveyance: "4800",
        leave: "4400",
        allowance: "3200",
        proTax: "3050",
        medAllowance: "4500",
        labourWelfare: "3900",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 2,
      },
      {
        employee: "Jeffrey Warden",
        employeeId: "FT-0006",
        email: "<EMAIL>",
        joinDate: "02-01-2019",
        role: "Web Designer",
        employeeRole: "Employee",
        status: "pending",
        salary: "63140",
        Basic: "53300",
        tDS: "7500",
        da: "9320",
        hra: "3400",
        pf: "5500",
        conveyance: "3800",
        leave: "4400",
        allowance: "2300",
        proTax: "3020",
        medAllowance: "3500",
        labourWelfare: "1900",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 3,
      },
      {
        employee: "John Doe",
        employeeId: "FT-0001",
        email: "<EMAIL>",
        joinDate: "07-01-2019",
        role: "Android Developer",
        employeeRole: "Employee",
        status: "pending",
        salary: "54840",
        Basic: "43300",
        tDS: "3500",
        da: "4320",
        hra: "3500",
        pf: "5400",
        conveyance: "2800",
        leave: "2500",
        allowance: "3300",
        proTax: "4020",
        medAllowance: "3200",
        labourWelfare: "1800",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 4,
      },
      {
        employee: "John Smith",
        employeeId: "FT-0003",
        email: "<EMAIL>",
        joinDate: "17-01-2019",
        role: "Frontend Developer",
        employeeRole: "Employee",
        status: "pending",
        salary: "69960",
        Basic: "55300",
        tDS: "3000",
        da: "5060",
        hra: "4000",
        pf: "5400",
        conveyance: "3000",
        leave: "2400",
        allowance: "3400",
        proTax: "4000",
        medAllowance: "2800",
        labourWelfare: "3200",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 5,
      },
      {
        employee: "Lesley Grauer",
        employeeId: "FT-0008",
        email: "<EMAIL>",
        joinDate: "20-01-2019",
        role: "Ios Developer",
        employeeRole: "Employee",
        status: "pending",
        salary: "50000",
        Basic: "39300",
        tDS: "1000",
        da: "5000",
        hra: "5000",
        pf: "5800",
        conveyance: "4000",
        leave: "4000",
        allowance: "5000",
        proTax: "3500",
        medAllowance: "1800",
        labourWelfare: "2800",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 6,
      },
      {
        employee: "Loren Gatlin",
        employeeId: "FT-0010",
        email: "<EMAIL>",
        joinDate: "22-01-2019",
        role: "Software Engineer",
        employeeRole: "Employee",
        status: "pending",
        salary: "34900",
        Basic: "18000",
        tDS: "1000",
        da: "5000",
        hra: "6000",
        pf: "5000",
        conveyance: "3500",
        leave: "3000",
        allowance: "4000",
        proTax: "3000",
        medAllowance: "2000",
        labourWelfare: "2400",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 7,
      },
      {
        employee: "Mike Litorus",
        employeeId: "FT-0004",
        email: "<EMAIL>",
        joinDate: "23-01-2019",
        role: "Web Developer",
        employeeRole: "Employee",
        status: "pending",
        salary: "28700",
        Basic: "15000",
        tdS: "100",
        da: "5000",
        hra: "4000",
        pf: "3400",
        conveyance: "2500",
        leave: "2500",
        allowance: "3000",
        proTax: "1000",
        medAllowance: "2000",
        labourWelfare: "2200",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 8,
      },
      {
        employee: "Richard Miles",
        employeeId: "FT-0002",
        email: "<EMAIL>",
        joinDate: "31-01-2019",
        role: "Ui/Ux Developer",
        employeeRole: "Employee",
        status: "pending",
        salary: "20450",
        Basic: "13000",
        tDS: "500",
        da: "3000",
        hra: "3000",
        pf: "3600",
        conveyance: "1500",
        leave: "2000",
        allowance: "3000",
        proTax: "850",
        medAllowance: "1000",
        labourWelfare: "1200",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 9,
      },
      {
        employee: "Tarah Shropshire",
        employeeId: "FT-0011",
        email: "<EMAIL>",
        joinDate: "11-01-2019",
        role: "Software Tester",
        employeeRole: "Employee",
        status: "pending",
        salary: "17250",
        Basic: "14000",
        tDS: "500",
        da: "1000",
        hra: "2000",
        pf: "3600",
        conveyance: "800",
        leave: "2000",
        allowance: "4000",
        proTax: "250",
        medAllowance: "500",
        labourWelfare: "800",
        othersAdd: "100",
        othersDed: "200",
        esi: "200",
        id: 10,
      },
    ];

    let users = [
      {
        name: "	BarryCuda",
        designation: "Android Developer",
        email: "<EMAIL>",
        company: "Global Technologies",
        role: "Client",
        id: 1,
      },
      {
        name: "	John Doe ",
        designation: "Web Designer",
        email: "<EMAIL>",
        company: "Dreamguy's Technologies",
        role: "Employee",
        id: 2,
      },
      {
        name: "Richard Miles",
        designation: "Admin",
        email: "<EMAIL>",
        company: "Dreamguy's Technologies",
        role: "Employee",
        id: 3,
      },
      {
        name: "	John Smith",
        designation: "Android Developer",
        email: "<EMAIL>",
        company: "Dreamguy's Technologies",
        role: "Employee",
        id: 4,
      },
      {
        name: "	Mike Litorus",
        designation: "IOS Developer",
        email: "<EMAIL>",
        company: "Dreamguy's Technologies",
        role: "Employee",
        id: 5,
      },
      {
        name: "Wilmer Deluna",
        designation: "Team Leader",
        email: "<EMAIL>",
        company: "Dreamguy's Technologies",
        role: "Employee",
        id: 6,
      },
      {
        name: "	BarryCuda",
        designation: "Team Leader",
        email: "<EMAIL>",
        company: "Global Technologies",
        role: "Client",
        id: 7,
      },
    ];
    let payments = [
      {
        invoiceId: "#INV-0001",
        client: "Global Technologies",
        paymenttype: "Paypal",
        paidDate: "8 Feb 2019",
        paidAmount: "$500",
        id: 1,
      },
      {
        invoiceId: "#INV-0002",
        client: "Delta Infotech",
        paymenttype: "Paypal",
        paidDate: "9 Jan 2019",
        paidAmount: "$420",
        id: 2,
      },
      {
        invoiceId: "#INV-0003",
        client: "Savior Inc",
        paymenttype: "Paypal",
        paidDate: "8 Ma2019",
        paidAmount: "$600",
        id: 3,
      },
      {
        invoiceId: "#INV-0004",
        client: "Nata ltd",
        paymenttype: "Paypal",
        paidDate: "10 Jul 2019",
        paidAmount: "$410",
        id: 4,
      },
      {
        invoiceId: "#INV-0005",
        client: "Paypal",
        paymenttype: "Paypal",
        paidDate: "10 Dec 2019",
        paidAmount: "$250",
        id: 5,
      },
      {
        invoiceId: "#INV-0006",
        client: "Tell Inc",
        paymenttype: "Paypal",
        paidDate: "10 Apr 2019",
        paidAmount: "$300",
        id: 6,
      },
    ];

    let payrollAddition = [
      {
        name: "Leave balance amount",
        category: "Monthly remuneration",
        unitCost: "5",
        id: 1,
      },
      {
        name: "Arrears of salary",
        category: "Additional remuneration",
        unitCost: "8",
        id: 2,
      },
      {
        name: "Gratuity",
        category: "Monthly remuneration",
        unitCost: "10",
        id: 3,
      },
      {
        name: "Arrears of salary",
        category: "Additional remuneration",
        unitCost: "10",
        id: 4,
      },
      {
        name: "Gratuity",
        category: "Monthly remuneration",
        unitCost: "20",
        id: 5,
      },
      {
        name: "Leave balance amount",
        category: "Additional remuneration",
        unitCost: "25",
        id: 6,
      },
    ];

    let payrollOvertime = [
      {
        name: "Normal day OT 1.5x",
        rate: "5",
        id: 1,
      },
      {
        name: "Public holiday OT 3.0x",
        rate: "13",
        id: 2,
      },
      {
        name: "Rest day OT 2.0x",
        rate: "20",
        id: 3,
      },
      {
        name: "Public holiday OT 3.0x",
        rate: "8",
        id: 4,
      },
      {
        name: "Normal day OT 1.5x",
        rate: "10",
        id: 5,
      },
      {
        name: "Public holiday OT 3.0x",
        rate: "10",
        id: 6,
      },
    ];
    let payrollDeduction = [
      {
        name: "Absent amount",
        unitCost: "5",
        id: 1,
      },
      {
        name: "Advance",
        unitCost: "10",
        id: 2,
      },
      {
        name: "Unpaid leave",
        unitCost: "20",
        id: 3,
      },
      {
        name: "Advance",
        unitCost: "8",
        id: 4,
      },
      {
        name: "Absent amount",
        unitCost: "21",
        id: 5,
      },
      {
        name: "Unpaid leave",
        unitCost: "20",
        id: 6,
      },
    ];

    let manageJobs = [
      {
        jobTitle: "Web Developer",
        department: "Development",
        startDate: "03-03-2019",
        expireDate: "11-05-2019",
        id: 1,
      },
      {
        jobTitle: "Web Designer",
        department: "Designing",
        startDate: "05-04-2019",
        expireDate: "21-05-2019",
        id: 2,
      },
      {
        jobTitle: "Android Developer",
        department: "Android",
        startDate: "03-08-2019",
        expireDate: "15-05-2019",
        id: 3,
      },
      {
        jobTitle: "Web Designer",
        department: "Designing",
        startDate: "03-09-2019",
        expireDate: "11-05-2019",
        id: 4,
      },
      {
        jobTitle: "Web Developer",
        department: "Development",
        startDate: "03-10-2019",
        expireDate: "31-05-2019",
        id: 5,
      },
      {
        jobTitle: "Android Developer",
        department: "Android",
        startDate: "03-12-2019",
        expireDate: "31-10-2019",
        id: 6,
      },
    ];
    let leaveType = [
      {
        leaveType: "Casual Leave",
        leaveDays: "12 Days",
        id: 1,
      },
      {
        leaveType: "Medical Leave",
        leaveDays: "12 Days",
        id: 2,
      },
      {
        leaveType: "Loss of Pay",
        leaveDays: "10 Days",
        id: 3,
      },
      {
        leaveType: "Medical Leave",
        leaveDays: "1 Days",
        id: 4,
      },
      {
        leaveType: "Casual Leave",
        leaveDays: "15 Days",
        id: 5,
      },
      {
        leaveType: "Loss of Pay",
        leaveDays: "10 Days",
        id: 6,
      },
    ];
    let roles = [
      {
        roleName: "Administrator",
        id: 1,
      },
      {
        roleName: "CEO",
        id: 2,
      },
      {
        roleName: "Manager",
        id: 3,
      },
      {
        roleName: "Team Leader",
        id: 4,
      },
      {
        roleName: "Accountant",
        id: 5,
      },
      {
        roleName: "Web Developer",
        id: 6,
      },
      {
        roleName: "Web Designer",
        id: 7,
      },
      {
        roleName: "HR",
        id: 8,
      },
      {
        roleName: "UI/UX Developer",
        id: 9,
      },
      {
        roleName: "SEO Analyst",
        id: 10,
      },
    ];

    let goallist = [
      {
        id: 1,
        goalType: "Event Goal",
        subject: "Test Goal",
        targetAchivement: "Lorem ipsum dollar",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        status: "Active",
        progress: "Completed 73%",
      },
      {
        id: 2,
        goalType: "Event Goal",
        subject: "Employee Goal",
        targetAchivement: "Lorem ipsum dollar",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        status: "Inactive",
        progress: "Completed 73%",
      },
      {
        id: 3,
        goalType: "Event Goal",
        subject: "Invoice Goal",
        targetAchivement: "Lorem ipsum dollar",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        status: "Inactive",
        progress: "Completed 43%",
      },
      {
        id: 4,
        goalType: "Event Goal",
        subject: "Project Goal",
        targetAchivement: "Lorem ipsum dollar",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        status: "Active",
        progress: "Completed 53%",
      },
    ];
    let goaltype = [
      {
        id: 1,
        type: "Event goal",
        description: "Event goal	Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 2,
        type: "Project goal",
        description: "Lorem ipsum dollar",
        status: "Inactive",
      },
      {
        id: 3,
        type: "Event goal",
        description: "Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 4,
        type: "Invoice goal",
        description: "Event goal tested",
        status: "active",
      },
      {
        id: 5,
        type: "Project goal",
        description: "Lorem ipsum dollar",
        status: "Inactive",
      },
      {
        id: 6,
        type: "Event goal",
        description: "Event goal	Lorem ipsum dollar",
        status: "active",
      },
    ];
    let trainingtype = [
      {
        id: 1,
        type: "Event goal",
        description: "Event goal	Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 2,
        type: "Project goal",
        description: "Lorem ipsum dollar",
        status: "Inactive",
      },
      {
        id: 3,
        type: "Event goal",
        description: "Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 4,
        type: "Invoice goal",
        description: "Event goal tested",
        status: "active",
      },
      {
        id: 5,
        type: "Project goal",
        description: "Lorem ipsum dollar",
        status: "Inactive",
      },
      {
        id: 6,
        type: "Project goal",
        description: "Lorem ipsum dollar",
        status: "Inactive",
      },
    ];

    let trainers = [
      {
        id: 1,
        name: "John Doe",
        lname: "Doe",
        role: "developer",
        contactNumber: "9876543210",
        mail: "<EMAIL>",
        description: "Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 2,
        name: "Mike Litorus",
        lname: "Litorus",
        role: "developer",
        contactNumber: "9876543120",
        mail: "<EMAIL>",
        description: "Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 3,
        name: "Wilmer Deluna",
        lname: "Deluna",
        role: "developer",
        contactNumber: "9876543210",
        mail: "<EMAIL>",
        description: "Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 4,
        name: "John Smith",
        lname: "Smith",
        role: "developer",
        contactNumber: "9876543210",
        mail: "<EMAIL>",
        description: "Lorem ipsum dollar",
        status: "active",
      },
      {
        id: 5,
        name: "Richard Milesh",
        lname: "Milesh",
        role: "developer",
        contactNumber: "9876543210",
        mail: "<EMAIL>",
        description: "Lorem ipsum dollar",
        status: "active",
      },
    ];

    let traininglist = [
      {
        id: 1,
        trainingType: "Node Training",
        trainer: "John Doe",
        employee: "",
        timeDuration: "7 May 2019 - 10 May 2019",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        cost: "$450",
        status: "active",
      },
      {
        id: 2,
        trainingType: "Git Training",
        trainer: "John Doe",
        employee: "",
        timeDuration: "7 May 2019 - 10 May 2019",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        cost: "$450",
        status: "active",
      },
      {
        id: 3,
        trainingType: "Angular Training",
        trainer: "John Doe",
        employee: "",
        timeDuration: "7 May 2019 - 10 May 2019",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        cost: "$450",
        status: "active",
      },
      {
        id: 4,
        trainingType: "Swift Training",
        trainer: "John Doe",
        employee: "",
        timeDuration: "7 May 2019 - 10 May 2019",
        startDate: "07-05-2019",
        endDate: "10-05-2019",
        description: "Lorem ipsum dollar",
        cost: "$450",
        status: "active",
      },
    ];
    let promotionmain = [
      {
        id: 1,
        employee: "John Doe",
        department: "Web development",
        designation: "Web Design",
        promotionFrom: "Web developer",
        promotionTo: "Sr.Web developer",
        promotionDate: "28-09-2019",
      },
      {
        id: 2,
        employee: "John Doe",
        department: "Web development",
        designation: "Web Design",
        promotionFrom: "Web developer",
        promotionTo: "Sr.Web developer",
        promotionDate: "28-09-2019",
      },
      {
        id: 3,
        employee: "John Doe",
        department: "Web development",
        designation: "Web Design",
        promotionFrom: "Web developer",
        promotionTo: "Sr.Web developer",
        promotionDate: "28-09-2019",
      },
      {
        id: 4,
        employee: "John Doe",
        department: "Web development",
        designation: "Web design",
        promotionFrom: "Web developer",
        promotionTo: "Sr.Web developer",
        promotionDate: "28-09-2019",
      },
    ];
    let resignationmain = [
      {
        id: 1,
        employee: "John Doe",
        department: "Web development",
        reason: "tested",
        noticedDate: "28-02-2019",
        resignDate: "28-03-2019",
      },
      {
        id: 2,
        employee: "Russia smith",
        department: "Web development",
        reason: "tested",
        noticedDate: "28-02-2019",
        resignDate: "28-03-2019",
      },
      {
        id: 3,
        employee: "Richared deo",
        department: "Web development",
        reason: "tested",
        noticedDate: "28-02-2019",
        resignDate: "28-03-2019",
      },
      {
        id: 4,
        employee: "Mark hentry",
        department: "Web development",
        reason: "tested",
        noticedDate: "28-02-2019",
        resignDate: "28-03-2019",
      },
    ];
    let terminationmain = [
      {
        id: 1,
        employee: "Richared dio",
        department: "Web development",
        terminationType: "Misconduct",
        terminationDate: "28-10-2019",
        reason: "Lorem Ipsum Dollar",
        noticedDate: "28-03-2019",
      },
      {
        id: 2,
        employee: "Mikel Rio",
        department: "Web development",
        terminationType: "Others",
        terminationDate: "28-02-2019",
        reason: "Lorem Ipsum Dollar",
        noticedDate: "28-03-2019",
      },
      {
        id: 3,
        employee: "John smith",
        department: "Web development",
        terminationType: "Others",
        terminationDate: "18-05-2019",
        reason: "Lorem Ipsum Dollar",
        noticedDate: "28-03-2019",
      },
      {
        id: 4,
        employee: "Russia hentry",
        department: "Web development",
        terminationType: "Misconduct",
        terminationDate: "28-08-2019",
        reason: "Lorem Ipsum Dollar",
        noticedDate: "28-03-2019",
      },
      {
        id: 5,
        employee: "Jackson feioz",
        department: "Web development",
        terminationType: "Others",
        terminationDate: "08-09-2019",
        reason: "Lorem Ipsum Dollar",
        noticedDate: "28-03-2019",
      },
      {
        id: 6,
        employee: "John Doe",
        department: "Web development",
        terminationType: "Misconduct",
        terminationDate: "27-10-2019",
        reason: "Lorem Ipsum Dollar",
        noticedDate: "28-03-2019",
      },
    ];

    let estimates = [
      {
        id: 1,
        number: "EST-0001",
        client: "Barry Cuda",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        estimate_date: "11-03-2019",
        expiry_date: "20-05-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Accepted",
      },
      {
        id: 2,
        number: "EST-0002",
        client: "Barry Cuda",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        estimate_date: "11-03-2019",
        expiry_date: "20-05-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Accepted",
      },
      {
        id: 3,
        number: "EST-0003",
        client: "Joshy",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Florida",
        estimate_date: "11-04-2019",
        expiry_date: "13-05-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Accepted",
      },
      {
        id: 4,
        number: "EST-0004",
        client: "Denver",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "VAT",
        client_address: "Texas",
        billing_address: "Washington",
        estimate_date: "11-04-2019",
        expiry_date: "13-05-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Accepted",
      },
      {
        id: 5,
        number: "EST-0006",
        client: "Kenneth",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        estimate_date: "10-04-2019",
        expiry_date: "20-10-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Decline",
      },
    ];

    let invoice = [
      {
        id: 1,
        number: "#INV-0001",
        client: "Barry Cuda",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        invoice_date: "11-03-2019",
        due_date: "20-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 2,
        number: "#INV-0002 ",
        client: "Shooshi",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        invoice_date: "11-03-2019",
        due_date: "20-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 3,
        number: "#INV-0003",
        client: "Kenneth",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        invoice_date: "10-03-2019",
        due_date: "20-04-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 4,
        number: "#INV-0004",
        client: "Barry Cuda",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "VAT",
        client_address: "Florida",
        billing_address: "Washington",
        invoice_date: "11-03-2019",
        due_date: "20-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 5,
        number: "#INV-0005",
        client: "Denver",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Vegas",
        invoice_date: "10-03-2019",
        due_date: "21-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 6,
        number: "#INV-0006",
        client: "John",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        invoice_date: "11-04-2019",
        due_date: "20-06-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
    ];
    let invoiceReport = [
      {
        id: 1,
        number: "#INV-0001",
        client: "Global Technologies",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        invoice_date: "11-03-2019",
        due_date: "20-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 2,
        number: "#INV-0002",
        client: "Delta Technologies",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "VAT",
        client_address: "Combodia",
        billing_address: "Washington",
        invoice_date: "11-02-2019",
        due_date: "21-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Pending",
      },
      {
        id: 3,
        number: "#INV-0003",
        client: "Aura Technologies",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Newyork",
        billing_address: "Vegas",
        invoice_date: "14-03-2019",
        due_date: "21-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 4,
        number: "#INV-0004",
        client: "Mine Technologies",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Washington",
        invoice_date: "11-05-2019",
        due_date: "23-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 5,
        number: "#INV-0005",
        client: "Global Technologies",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "VAT",
        client_address: "Texas",
        billing_address: "Arizona",
        invoice_date: "14-03-2019",
        due_date: "20-09-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
      {
        id: 6,
        number: "#INV-0006",
        client: "Senzer Technologies",
        project: "Office Management",
        email: "<EMAIL>",
        tax: "GST",
        client_address: "Texas",
        billing_address: "Florida",
        invoice_date: "11-03-2019",
        due_date: "23-03-2019",
        items: [
          {
            item: "Item",
            description: "Description",
            unit_cost: "10",
            qty: 10,
            amount: 100,
          },
        ],
        totalamount: 100,
        discount: 5,
        grandTotal: 100,
        other_information: "Description",
        status: "Paid",
      },
    ];

    let performanceindicator = [
      {
        id: 1,
        designation: "Web Designer",
        experience: "Beginner",
        integrirty: "",
        Marketing: "",
        professionalism: "",
        managementskill: "",
        teamwork: "",
        adminstartion: "",
        criticalthinking: "",
        presentationskil: "",
        conflictmanagement: "",
        qualityofwork: "",
        attendance: "",
        effientcy: "",
        meetdeadline: "",
        department: "Designing",
        addedBy: "John Doe",
        createdBy: "28 Feb 2019",
        status: "Active",
      },
      {
        id: 2,
        designation: "Ios developer",
        experience: "Beginner",
        department: "Ios",
        integrirty: "",
        Marketing: "",
        professionalism: "",
        managementskill: "",
        teamwork: "",
        adminstartion: "",
        criticalthinking: "",
        presentationskil: "",
        conflictmanagement: "",
        qualityofwork: "",
        attendance: "",
        effientcy: "",
        meetdeadline: "",
        addedBy: "Mike Litorus",
        createdBy: "28 Feb 2019",
        status: "Active",
      },
      {
        id: 3,
        designation: "Web developer",
        experience: "Beginner",
        department: "Web design",
        integrirty: "",
        Marketing: "",
        professionalism: "",
        managementskill: "",
        teamwork: "",
        adminstartion: "",
        criticalthinking: "",
        presentationskil: "",
        conflictmanagement: "",
        qualityofwork: "",
        attendance: "",
        effientcy: "",
        meetdeadline: "",
        addedBy: "John Smith",
        createdBy: "28 Feb 2019",
        status: "InActive",
      },
      {
        id: 4,
        designation: "Web Designer",
        experience: "Beginner",
        department: "Web development",
        integrirty: "",
        Marketing: "",
        professionalism: "",
        managementskill: "",
        teamwork: "",
        adminstartion: "",
        criticalthinking: "",
        presentationskil: "",
        conflictmanagement: "",
        qualityofwork: "",
        attendance: "",
        effientcy: "",
        meetdeadline: "",
        addedBy: "Jeffrey Warden",
        createdBy: "28 Feb 2019",
        status: "Active",
      },
    ];

    let performanceappraisal = [
      {
        id: 1,
        employee: "John deo",
        designation: "Web designer",
        apparaisaldate: "02-05-2020",
        department: "Web design",
        status: "Active",
      },
      {
        id: 2,
        employee: "Mixcle Rao",
        designation: "Ios developer",
        apparaisaldate: "02-05-2020",
        department: "Web design",
        status: "Active",
      },
      {
        id: 3,
        employee: "John rio",
        designation: "Web developer",
        apparaisaldate: "02-05-2020",
        department: "Web design",
        status: "Active",
      },
      {
        id: 4,
        employee: "effrey Warden",
        designation: "Web Designer",
        apparaisaldate: "02-05-2020",
        department: "Web development",
        status: "Active",
      },
    ];
    let taskboard = [
      {
        id: 1,
        taskname: "John deo",
        taskpriority: "Medium",
        duedate: "02-05-2020",
        followers: "John deo",
        status: "Active",
      },
      {
        id: 2,
        taskname: "John Mclaren",
        taskpriority: "Low",
        duedate: "02-10-2020",
        followers: "Richard Williams",
        status: "Active",
      },
      {
        id: 3,
        taskname: "Kennedy",
        taskpriority: "High",
        duedate: "05-11-2020",
        followers: "Richard deo",
        status: "Active",
      },
      {
        id: 4,
        taskname: "Barry cuda",
        taskpriority: "Medium",
        duedate: "02-05-2020",
        followers: "Williams",
        status: "Active",
      },
      {
        id: 5,
        taskname: "Joshy",
        taskpriority: "High",
        duedate: "02-05-2020",
        followers: "Loren",
        status: "Active",
      },
      {
        id: 6,
        taskname: "Hector",
        taskpriority: "Medium",
        duedate: "25-10-2020",
        followers: "Rihanna",
        status: "Active",
      },
    ];
    let pickListNames = [
      {
        id: 1,
        name: "John deo",
      },
      {
        id: 2,
        name: "John Mclaren",
      },
      {
        id: 3,
        name: "Kennedy",
      },
      {
        id: 4,
        name: "Barry cuda",
      },
    ];
    let customPolicy = [
      {
        id: 1,
        name: "John deo",
        days: 5,
      },
      {
        id: 2,
        name: "John Mclaren",
        days: 6,
      },
      {
        id: 3,
        name: "Kennedy",
        days: 8,
      },
      {
        id: 4,
        name: "Barry cuda",
        days: 9,
      },
    ];

    let categories = [
      {
        id: 1,
        categoryname: "Hardware",
        subcategoryname: "Hardware Expenses"
      },
      {
        id: 2,
        categoryname: "Material",
        subcategoryname: "Material Expenses"
      },
      {
        id: 3,
        categoryname: "Vehicle",
        subcategoryname: "Company Vehicle Information"
      }
    ];
    let revenue = [
      {
        id: 1,
        notes: "test",
        categoryname: "Project",
        subcategoryname: "Project Expenses",
        amount: "1000.00",
        revenuedate: "06 Jan 2020"
      },
      {
        id: 2,
        notes: "test",
        categoryname: "Hardware",
        subcategoryname: "Hardware Expenses",
        amount: "1000.00",
        revenuedate: "06 Jan 2020"
      }
    ];
    let useralljobs = [
      {
        id: 1,
        jobtitle: "Web Developer",
        department: "Development",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Full Time",
        status: "Open"
      },
      {
        id: 2,
        jobtitle: "Web Designer",
        department: "Designing",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Part Time",
        status: "Closed"
      },
      {
        id: 3,
        jobtitle: "Android Developer",
        department: "Android",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Internship",
        status: "Cancelled"
      }
    ];
    let offeredjobs = [
      {
        id: 1,
        jobtitle: "Web Developer",
        department: "Development",
        jobtype: "Full Time"
      },
      {
        id: 2,
        jobtitle: "Web Designer",
        department: "Designing",
        jobtype: "Part Time"
      },
      {
        id: 3,
        jobtitle: "Android Developer",
        department: "Android",
        jobtype: "Internship"
      }
    ];
    let visitedjobs = [
      {
        id: 1,
        jobtitle: "Web Developer",
        department: "Development",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Full Time",
        status: "Open"
      },
      {
        id: 2,
        jobtitle: "Web Designer",
        department: "Designing",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Part Time",
        status: "Closed"
      },
      {
        id: 3,
        jobtitle: "Android Developer",
        department: "Android",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Internship",
        status: "Cancelled"
      }
    ];
    let projectreports = [
      {
        id: 1,
        projecttitle: "Hospital Administration",
        clientname: "Global Technologies",
        startdate: "9 Jan 2021",
        expiredate: "10 Apr 2021",
        status: "Active"
      },
      {
        id: 2,
        projecttitle: "Office Management",
        clientname: "Delta Infotech",
        startdate: "10 Dec 2021",
        expiredate: "2 May 2021",
        status: "Pending"
      }
    ];
     let archivedjobs = [
      {
        id: 1,
        jobtitle: "Web Developer",
        department: "Development",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Full Time",
        status: "Open"
      },
      {
        id: 2,
        jobtitle: "Web Designer",
        department: "Designing",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Part Time",
        status: "Closed"
      },
      {
        id: 3,
        jobtitle: "Android Developer",
        department: "Android",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Internship",
        status: "Cancelled"
      }
    ];

    let appliedjobs = [
      {
        id: 1,
        jobtitle: "Web Developer",
        department: "Development",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Full Time",
        status: "Open"
      },
      {
        id: 2,
        jobtitle: "Web Designer",
        department: "Designing",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Part Time",
        status: "Closed"
      },
      {
        id: 3,
        jobtitle: "Android Developer",
        department: "Android",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Internship",
        status: "Cancelled"
      }
    ];


      let savedjobs = [
      {
        id: 1,
        jobtitle: "Web Developer",
        department: "Development",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Full Time",
        status: "Open"
      },
      {
        id: 2,
        jobtitle: "Web Designer",
        department: "Designing",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Part Time",
        status: "Closed"
      },
      {
        id: 3,
        jobtitle: "Android Developer",
        department: "Android",
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Internship",
        status: "Cancelled"
      }
    ];

     let interview = [
      {
        id: 1,
        questions: "IS management has decided to rewrite a legacy customer relations system using fourth generation languages (4GLs). Which of the following risks is MOST often associated with system development using 4GLs?",
        option1: "design facilities",
        option2: "language subsets",
        option3: "Lack of portability",
        option4: "Inability to perform data",
        correctanswer: "A"
      },
      {
        id: 2,
        questions: "Which of the following would be the BEST method for ensuring that critical fields in a master record have been updated properly?",
        option1: "design facilities",
        option2: "language subsets",
        option3: "Lack of portability",
        option4: "Inability to perform data",
        correctanswer: "A"
      }
    ];

    let candidate = [
      {
        id: 1,
        name: "John Doe",
        mobilenumber: "9876543210",
        email: "<EMAIL>",
        createddate: "1 Jan 2013"
      },
      {
        id: 2,
        name: "Richard Miles",
        mobilenumber: "9876543210",
        email: "<EMAIL>",
        createddate: "18 Mar 2014"
      },
      {
        id: 3,
        name: "John Smith",
        mobilenumber: "9876543210",
        email: "<EMAIL>",
        createddate: "1 Apr 2014"
      }
    ];

     let expire = [
      {
        id: 1,
        experience: "1-2",
        status: "Active"
      },
      {
        id: 2,
        experience: "1-3",
        status: "Active"
      },
      {
        id: 3,
       experience: "4-7",
        status: "Active"
      }
    ];
    let paymentreports = [
      {
        id: 1,
        transactionid: "834521",
        date: "2nd Dec 2020",
        clientname: "Dreams",
        paymentmethod: "Online",
        invoice: "INV0001",
        amount: "$4,329,970.7"
      },
      {
        id: 2,
        transactionid: "834521",
        date: "2nd Dec 2020",
        clientname: "Dreams",
        paymentmethod: "Online",
        invoice: "INV0001",
        amount: "$4,329,970.7"
      }
    ];
    let taskreports = [
      {
        id: 1,
        taskname: "Hospital Administration",
        startdate: "26 Mar 2019",
        enddate: "26 Apr 2021",
        status: "Active"
      },
      {
        id: 2,
        taskname: "Hospital Administration",
        startdate: "26 Mar 2019",
        enddate: "26 Apr 2021",
        status: "Active"
      }
    ];
    let userreports = [
      {
        id: 1,
        name1: "Barry Cuda",
        name2: "Global Technologies",
        company: "Global Technologies",
        email: "<EMAIL>",
        role: "Client",
        designation: "CEO",
        status: "Active"
      },
      {
        id: 2,
        name1: "Daniel Porter",
        name2: "Admin",
        company: "Focus Technologies",
        email: "<EMAIL>",
        role: "Admin",
        designation: "Admin Manager",
        status: "Active"
      }
    ];
    let attendancereports = [
      {
        id: 1,
        date: "1 Jan 2020",
        clockin: "-",
        clockout: "-",
        workstatus: "-"
      },
      {
        id: 2,
        date: "2 Jan 2020",
        clockin: "-",
        clockout: "-",
        workstatus: "-"
      },
      {
        id: 3,
        date: "3 Jan 2020",
        clockin: "-",
        clockout: "-",
        workstatus: "-"
      },
      {
        id: 4,
        date: "4 Jan 2020",
        clockin: "-",
        clockout: "Week Off",
        workstatus: "-"
      },
      {
        id: 5,
        date: "5 Jan 2020",
        clockin: "-",
        clockout: "Week Off",
        workstatus: "-"
      },
      {
        id: 6,
        date: "6 Jan 2020",
        clockin: "-",
        clockout: "-",
        workstatus: "-"
      },

    ];
    let leavereports = [
      {
        id: 1,
        name1: "John Doe",
        name2: "#0001",
        date: "20 Dec 2020",
        department: "Design",
        leavetype: "Sick Leave",
        noofdays: "05",
        remainingleave: "08",
        totalleaves: "20",
        totalleavetaken: "12",
        leavecarryforward: "08"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "#0002",
        date: "21 Dec 2020",
        department: "Web Developer",
        leavetype: "Parenting Leave",
        noofdays: "03",
        remainingleave: "08",
        totalleaves: "20",
        totalleavetaken: "12",
        leavecarryforward: "05"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "#0003",
        date: "22 Dec 2020",
        department: "Android Developer",
        leavetype: "Emergency Leave",
        noofdays: "01",
        remainingleave: "09",
        totalleaves: "20",
        totalleavetaken: "17",
        leavecarryforward: "03"
      },
      {
        id: 4,
        name1: "Mike Litorus",
        name2: "#0004",
        date: "23 Dec 2020",
        department: "IOS Developer",
        leavetype: "Sick Leave",
        noofdays: "15",
        remainingleave: "05",
        totalleaves: "20",
        totalleavetaken: "15",
        leavecarryforward: "05"
      },
      {
        id: 5,
        name1: "John Doe",
        name2: "#0001",
        date: "24 Dec 2020",
        department: "Team Leader",
        leavetype: "Sick Leave",
        noofdays: "10",
        remainingleave: "07",
        totalleaves: "20",
        totalleavetaken: "18",
        leavecarryforward: "2"
      }
    ];
     let dailyreport = [
      {
        id: 1,
        name1: "John Doe",
        name2: "#0001",
        date: "20 Dec 2020",
        department: "Design",
        status: "Week off"
      },
      {
        id: 2,
        name1: "John Smith",
        name2: "#0003",
        date: "20 Dec 2020",
        department: "Android Developer",
        status: "Week off"
      },
      {
        id: 3,
        name1: "Mike Litorus",
        name2: "#0004",
        date: "20 Dec 2020",
        department: "IOS Developer",
        status: "Week off"
      },
      {
        id: 4,
        name1: "Richard Miles",
        name2: "#0002",
        date: "20 Dec 2020",
        department: "Web Developer",
        status: "Absent"
      },
      {
        id: 5,
        name1: "Wilmer Deluna",
        name2: "#0005",
        date: "20 Dec 2020",
        department: "Team Leader",
        status: "Week off"
      }
    ];
      let candidatelist = [
      {
        id: 1,
        name1: "John Doe",
        name2: "Web Designer",
        jobtitle: "Web Developer",
        department: "Development",
        status: "Offered"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "Web Developer",
        jobtitle: "Web Designer",
        department: "Designing",
        status: "Offered"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "Android Developer",
        jobtitle: "Android Developer",
        department: "Android",
        status: "Offered"
      }
    ];

     let offer = [
      {
        id: 1,
        name1: "John Doe",
        name2: "Web Designer",
        jobtitle: "Web Developer",
        jobtype: "Temporary",
        pay: "$25000",
        annualip: "15%",
        longtermip: "No",
        status: "requested"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "Web Developer",
        jobtitle: "Web Designer",
        jobtype: "Contract",
        pay: "$25000",
        annualip: "15%",
        longtermip: "No",
        status: "rejected"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "Android Developer",
        jobtitle: "Android Developer",
        jobtype: "Salary",
        pay: "$25000",
        annualip: "15%",
        longtermip: "No",
        status: "Approved"
      }
    ];

    let scheduletiming = [
      {
        id: 1,
        name1: "John Doe",
        name2: "Web Designer",
        jobtitle: "Web Developer",
        useravailable: "11-03-2020", 
        useravailabletimings: "- 11:00 AM-12:00 PM",
        useravailable1: "11-03-2020", 
        useravailabletimings1: "- 10:00 AM-11:00 AM",
        useravailable2: "01-01-1970", 
        useravailabletimings2: "- 10:00 AM-11:00 AM"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "Web Developer",
        jobtitle: "Web Designer",
        useravailable: "11-03-2020", 
        useravailabletimings: "- 11:00 AM-12:00 PM",
        useravailable1: "11-03-2020", 
        useravailabletimings1: "- 10:00 AM-11:00 AM",
        useravailable2: "01-01-1970", 
        useravailabletimings2: "- 10:00 AM-11:00 AM"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "Android Developer",
        jobtitle: "Android Developer",
        useravailable: "11-03-2020", 
        useravailabletimings: "- 11:00 AM-12:00 PM",
        useravailable1: "11-03-2020", 
        useravailabletimings1: "- 10:00 AM-11:00 AM",
        useravailable2: "01-01-1970", 
        useravailabletimings2: "- 10:00 AM-11:00 AM"
      }
    ];
    let aptituteresult = [
      {
        id: 1,
        name1: "John Doe",
        name2: "Web Designer",
        jobtitle: "Web Developer",
        department: "Development", 
        categorywise: "html -",
        categorywisemark: "1",
        categorywise1: "Level1 -",
        categorywisemark1: "0",
        totalmark: "1", 
        status: "Action pending"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "Web Developer",
        jobtitle: "Web Designer",
        department: "Designing", 
        categorywise: "html -", 
        categorywisemark: "1",
        categorywise1: "Level1 -",
        categorywisemark1: "0",
        totalmark: "1", 
        status: "Action pending"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "Android Developer",
        jobtitle: "Android Developer",
        department: "Android", 
        categorywise: "html -",
        categorywisemark: "1",
        categorywise1: "Level1 -",
        categorywisemark1: "0",
        totalmark: "1", 
        status: "Action pending"
      }
    ];
    let manage = [
      {
        id: 1,
        name1: "John Doe",
        name2: "Web Designer",
        jobtitle: "Web Developer",
        department: "Development", 
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Full Time",
        status: "Open",
        resume: "Download"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "Web Developer",
        jobtitle: "Web Designer",
        department: "Designing", 
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Part Time",
        status: "Closed",
        resume: "Download"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "Android Developer",
        jobtitle: "Android Developer",
        department: "Android", 
        startdate: "3 Mar 2019",
        expiredate: "31 May 2019",
        jobtype: "Internship",
        status: "Cancelled",
        resume: "Download"
      }
    ];
    let payslip = [
      {
        id: 1,
        name1: "Bernardo Galaviz",
        name2: "Web Developer",
        paidamount: "$200",
        paymentmonth: "Apr", 
        paymentyear: "2019",
        actions: "PDF"
      },
      {
        id: 2,
        name1: "Jeffrey Warden",
        name2: "Web Developer",
        paidamount: "$300",
        paymentmonth: "Dec", 
        paymentyear: "2020",
        actions: "PDF"
      },
      {
        id: 3,
        name1: "John Doe",
        name2: "Web Designer",
        paidamount: "$400",
        paymentmonth: "Jun", 
        paymentyear: "2020",
        actions: "PDF"
      },
      {
        id: 4,
        name1: "John Smith",
        name2: "Android Developer",
        paidamount: "$500",
        paymentmonth: "Feb", 
        paymentyear: "2020",
        actions: "PDF"
      },
      {
        id: 5,
        name1: "Mike Litorus",
        name2: "IOS Developer",
        paidamount: "$600",
        paymentmonth: "Jan", 
        paymentyear: "2020",
        actions: "PDF"
      }
    ];
    let employeereport = [
      {
        id: 1,
        name1: "John Doe",
        name2: "#0001",
        employeetype: "Employee",
        email: "<EMAIL>", 
        department: "Designing",
        designation: "UI Design",
        joiningdate: "20 Aug 2020",
        dob: "03 Mar 1992",
        marritalstatus: "Married",
        gender: "Male",
        terminateddate: "-",
        relievingdate: "-",
        salary: "$20000",
        address: "1861 Bayonne Ave, Manchester Township, NJ, 08759",
        contactnumber: "7894561235",
        experience: "0 years 4 months and 9 days",
        status: "Active"
      },
      {
        id: 2,
        name1: "Richard Miles",
        name2: "#0002",
        employeetype: "Employee",
        email: "<EMAIL>", 
        department: "Android Developer",
        designation: "IT Support",
        joiningdate: "01 Jul 2020",
        dob: "05 Dec 1979",
        marritalstatus: "Married",
        gender: "Male",
        terminateddate: "-",
        relievingdate: "-",
        salary: "$20000",
        address: "1861 Bayonne Ave, Manchester Township, NJ, 08759",
        contactnumber: "7894561235",
        experience: "0 years 5 months and 24 days",
        status: "Active"
      },
      {
        id: 3,
        name1: "John Smith",
        name2: "#0003",
        employeetype: "Employee",
        email: "<EMAIL>", 
        department: "IOS Developer",
        designation: "Development Manager",
        joiningdate: "03 Sep 2020",
        dob: "16 Apr 1984",
        marritalstatus: "Married",
        gender: "Male",
        terminateddate: "-",
        relievingdate: "-",
        salary: "$27000",
        address: "1861 Bayonne Ave, Manchester Township, NJ, 08759",
        contactnumber: "7894561235",
        experience: "0 years 3 months and 21 days",
        status: "Active"
      },
      {
        id: 4,
        name1: "Mike Litorus",
        name2: "#0004",
        employeetype: "Employee",
        email: "<EMAIL>", 
        department: "Web Developer",
        designation: "IT Support",
        joiningdate: "15 Nov 2020",
        dob: "15 Jul 2005",
        marritalstatus: "Single",
        gender: "Male",
        terminateddate: "-",
        relievingdate: "-",
        salary: "$15000",
        address: "1861 Bayonne Ave, Manchester Township, NJ, 08759",
        contactnumber: "7894561235",
        experience: "0 years 1 months and 9 days",
        status: "Active"
      },
      {
        id: 5,
        name1: "Wilmer Deluna",
        name2: "#005",
        employeetype: "Employee",
        email: "<EMAIL>", 
        department: "Team Leader",
        designation: "Development Manager",
        joiningdate: "01 Dec 2020",
        dob: "21 Jun 1984",
        marritalstatus: "Married",
        gender: "Male",
        terminateddate: "-",
        relievingdate: "-",
        salary: "$25000",
        address: "1861 Bayonne Ave, Manchester Township, NJ, 08759",
        contactnumber: "7894561235",
        experience: "0 years 0 months and 24 days",
        status: "Active"
      }
    ];
    
    let budgetexpense = [
      {
        id: 1,
        notes: "test",
        categoryname: "Hardware",
        subcategoryname: "Hardware Expenses",
        amount: "1000.00",
        revenuedate: "06 Jan 2020"
      },
      {
        id: 2,
        notes: "test",
        categoryname: "Project",
        subcategoryname: "Project Expenses",
        amount: "1000.00",
        revenuedate: "06 Jan 2020"
      }
    ];
     let budget = [
      {
        id: 1,
        budgettitle: "Tender",
        budgettype: "project",
        startdate: "01 Jan 2021",
        enddate: "31 Dec 2021",
        totalrevenue: "2500000",
        totalexpenses: "1500000",
        taxamount: "10",
        budgetamount: "999990"
      },
      {
        id: 2,
        budgettitle: "Project",
        budgettype: "project",
        startdate: "01 Feb 2021",
        enddate: "31 Apr 2021",
        totalrevenue: "100000",
        totalexpenses: "50000",
        taxamount: "1000",
        budgetamount: "49000"
      }
    ];
    let shiftlist = [
      {
        id: 1,
        shiftname: "10'0 clock Shift",
        minstarttime: "09.00:00 am",
        starttime: "10:00:00 am",
        maxstarttime: "10:30:00 am",
        minendtime: "06:00:00 pm",
        endtime: "06:30:00 pm",
        maxendtime: "07:00:00 pm",
        breaktime: "30 mins",
        status: "Active"
      },
      {
        id: 2,
        shiftname: "10:30 shift",
        minstarttime: "10.00:00 am",
        starttime: "10:30:00 am",
        maxstarttime: "11:00:00 am",
        minendtime: "06:30:00 pm",
        endtime: "07:00:00 pm",
        maxendtime: "07:30:00 pm",
        breaktime: "45 mins",
        status: "Active"
      },
       {
        id: 3,
        shiftname: "Daily Rout",
        minstarttime: "06.00:00 am",
        starttime: "06:30:00 am",
        maxstarttime: "07:00:00 am",
        minendtime: "03:00:00 pm",
        endtime: "03:30:00 pm",
        maxendtime: "04:00:00 pm",
        breaktime: "60 mins",
        status: "Active"
      },
         {
        id: 4,
        shiftname: "New Shift",
        minstarttime: "06.11:00 am",
        starttime: "06:30:00 am",
        maxstarttime: "08:12:00 am",
        minendtime: "09:12:00 pm",
        endtime: "09:30:00 pm",
        maxendtime: "09:45:00 pm",
        breaktime: "45 mins",
        status: "Active"
      },
       {
        id: 5,
        shiftname: "Recurring Shift",
        minstarttime: "08.30:00 am",
        starttime: "09:00:00 am",
        maxstarttime: "09:30:00 am",
        minendtime: "05:30:00 pm",
        endtime: "06:00:00 pm",
        maxendtime: "06:30:00 pm",
        breaktime: "60 mins",
        status: "Active"
      }
    ];

    let shiftscheduling = [
      {
        id: 1,
        name1: "Brenardo Galaviz",
        name2: "Web Developer"
      },
      {
        id: 2,
        name1: "Brenardo Galaviz",
        name2: "Web Developer"
      },
       {
        id: 3,
        name1: "John Doe",
        name2: "Web Designer"
      },
         {
        id: 4,
        name1: "John Smith",
        name2: "Android Developer"
      },
       {
        id: 5,
        name1: "Mike Litorus",
        name2: "IOS Developer"
      },
       {
        id: 6,
        name1: "Richard Miles",
        name2: "Web Developer"
      },
      {
        id: 7,
        name1: "Wilmer Deluna",
        name2: "Team Leader"
      }
    ];

    return {
      invoice: invoice,
      contacts: contacts,
      clients: clients,
      projects: projects,
      leaders: leaders,
      employeepage: employeepage,
      employeelist: employeelist,
      holidays: holidays,
      adminleaves: adminleaves,
      leads: leads,
      tickets: tickets,
      employeeleaves: employeeleaves,
      departments: departments,
      designation: designation,
      timesheet: timesheet,
      overtime: overtime,
      expenses: expenses,
      providentFund: providentFund,
      goallist: goallist,
      goaltype: goaltype,
      trainingtype: trainingtype,
      trainers: trainers,
      traininglist: traininglist,
      promotionmain: promotionmain,
      resignationmain: resignationmain,
      terminationmain: terminationmain,
      taxes: taxes,
      policies: policies,
      expenseReport: expenseReport,
      appliedCandidates: appliedCandidates,
      knowledgeBase: knowledgeBase,
      assets: assets,
      users: users,
      payments: payments,
      manageJobs: manageJobs,
      estimates: estimates,
      leaveType: leaveType,
      performanceindicator: performanceindicator,
      performanceappraisal: performanceappraisal,
      roles: roles,
      payrollAddition: payrollAddition,
      payrollOvertime: payrollOvertime,
      payrollDeduction: payrollDeduction,
      invoiceReport: invoiceReport,
      employeeSalary: employeeSalary,
      taskboard: taskboard,
      pickListNames: pickListNames,
      customPolicy: customPolicy,
      categories: categories,
      revenue: revenue,
      useralljobs: useralljobs,
      offeredjobs: offeredjobs,
      visitedjobs: visitedjobs,
      projectreports: projectreports,
      archivedjobs: archivedjobs,
      appliedjobs: appliedjobs,
      savedjobs: savedjobs,
      interview: interview,
      candidate: candidate,
      expire: expire,
      paymentreports: paymentreports,
      taskreports: taskreports,
      userreports: userreports,
      attendancereports: attendancereports,
      leavereports: leavereports,
      dailyreport: dailyreport,
      candidatelist: candidatelist,
      offer: offer,
      scheduletiming: scheduletiming,
      aptituteresult: aptituteresult,
      manage: manage,
      payslip: payslip,
      employeereport: employeereport,
      budgetexpense: budgetexpense,
      budget: budget,
      shiftlist: shiftlist,
      shiftscheduling: shiftscheduling
    };
  }
}
