(window.webpackJsonp=window.webpackJsonp||[]).push([[24],{C3Jh:function(t,e,n){"use strict";n.r(e),n.d(e,"DashboardModule",function(){return P});var a=n("ofXK"),c=n("tyNb"),o=n("fXoL");const i=function(t){return{height:t}};let s=(()=>{let t=class{constructor(t,e){this.ngZone=t,this.router=e,window.onresize=t=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){this.router.navigateByUrl("/dashboard/employee")}onResize(t){this.innerHeight=t.target.innerHeight+"px"}};return t.\u0275fac=function(e){return new(e||t)(o.Ub(o.G),o.Ub(c.c))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-dashboard"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.hc("resized",function(t){return e.onResize(t)}),o.Vb(1,"router-outlet"),o.Zb()),2&t&&o.pc("ngStyle",o.tc(1,i,e.innerHeight))},directives:[a.n,c.g],styles:[""]}),t})();var r=n("AytR"),d=n("tk/3");let b=(()=>{class t{constructor(t){this.http=t,this.baseUrl=r.a.baseUrl}getTotalEmployees(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getTotalEmployees`)}getTotalEmployeesJoinedThisMonth(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/totalEmployeesJoinedLastMonth`)}getTotalEmployeesPresentToday(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getPresentEmployeesToday`)}getTotalEmployeesAbsentToday(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getAbsentEmployeesToday`)}getTotalLateEmployeesToday(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getLateEmployeesToday`)}getEarlyGoneEmployeesToday(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getEarlyGoneEmployeesToday`)}getOnTourEmployeesToday(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getOnTourEmployeesToday`)}getOnLeaveEmployeesToday(){return this.http.get(`${this.baseUrl}/api/v1/adminDashboard/getOnLeaveEmployeesToday`)}}return t.\u0275fac=function(e){return new(e||t)(o.ec(d.c))},t.\u0275prov=o.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),l=(()=>{class t{constructor(t){this._dashboardService=t}ngOnInit(){this._countTotalEmployees(),this._getEmployeesJoinedThisMonth(),this._getTotalEmployeesPresentToday(),this._getTotalEmployeesAbsentToday(),this._getTotalLateEmployeesToday(),this._getEarlyGoneEmployeesToday(),this._getOnTourEmployeesToday(),this._getOnLeaveEmployeesToday()}_countTotalEmployees(){this._dashboardService.getTotalEmployees().subscribe(t=>{this.empTotal=t})}_getEmployeesJoinedThisMonth(){this._dashboardService.getTotalEmployeesJoinedThisMonth().subscribe(t=>{this.empThisMonth=t})}_getTotalEmployeesPresentToday(){this._dashboardService.getTotalEmployeesPresentToday().subscribe(t=>{this.empPresent=t})}_getTotalEmployeesAbsentToday(){this._dashboardService.getTotalEmployeesAbsentToday().subscribe(t=>{this.empAbsent=t})}_getTotalLateEmployeesToday(){this._dashboardService.getTotalLateEmployeesToday().subscribe(t=>{this.lateEmployee=t})}_getEarlyGoneEmployeesToday(){this._dashboardService.getEarlyGoneEmployeesToday().subscribe(t=>{this.earlyGoneEmployee=t})}_getOnTourEmployeesToday(){this._dashboardService.getOnTourEmployeesToday().subscribe(t=>{this.onTourEmployee=t})}_getOnLeaveEmployeesToday(){this._dashboardService.getOnLeaveEmployeesToday().subscribe(t=>{this.onLeaveEmployee=t})}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(b))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-admin-dashboard"]],decls:132,vars:8,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row"],[1,"col-sm-12"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item","active"],[1,"col-md-6","col-sm-6","col-lg-6","col-xl-3"],[1,"card","dash-widget"],[1,"card-body"],[1,"dash-widget-icon"],["aria-hidden","true",1,"fa","fa-users"],[1,"dash-widget-info"],[1,"fa","fa-usd"],[1,"fa","fa-diamond"],[1,"fa","fa-user"],[1,"fa","fa-cubes"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"h3",4),o.Lc(5,"Welcome Admin!"),o.Zb(),o.ac(6,"ul",5),o.ac(7,"li",6),o.Lc(8,"Dashboard"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(9,"div",2),o.ac(10,"div",7),o.ac(11,"div",8),o.ac(12,"div",9),o.ac(13,"span",10),o.Vb(14,"i",11),o.Zb(),o.ac(15,"div",12),o.ac(16,"h3"),o.Lc(17),o.Zb(),o.ac(18,"span"),o.Lc(19,"Total Employees"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(20,"div",7),o.ac(21,"div",8),o.ac(22,"div",9),o.ac(23,"span",10),o.Vb(24,"i",13),o.Zb(),o.ac(25,"div",12),o.ac(26,"h3"),o.Lc(27),o.Zb(),o.ac(28,"span"),o.Lc(29,"New Employees"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(30,"div",7),o.ac(31,"div",8),o.ac(32,"div",9),o.ac(33,"span",10),o.Vb(34,"i",14),o.Zb(),o.ac(35,"div",12),o.ac(36,"h3"),o.Lc(37),o.Zb(),o.ac(38,"span"),o.Lc(39,"Total Present Today"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(40,"div",7),o.ac(41,"div",8),o.ac(42,"div",9),o.ac(43,"span",10),o.Vb(44,"i",15),o.Zb(),o.ac(45,"div",12),o.ac(46,"h3"),o.Lc(47),o.Zb(),o.ac(48,"span"),o.Lc(49,"Total Absent Today"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(50,"div",2),o.ac(51,"div",7),o.ac(52,"div",8),o.ac(53,"div",9),o.ac(54,"span",10),o.Vb(55,"i",16),o.Zb(),o.ac(56,"div",12),o.ac(57,"h3"),o.Lc(58),o.Zb(),o.ac(59,"span"),o.Lc(60,"Late In"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(61,"div",7),o.ac(62,"div",8),o.ac(63,"div",9),o.ac(64,"span",10),o.Vb(65,"i",13),o.Zb(),o.ac(66,"div",12),o.ac(67,"h3"),o.Lc(68),o.Zb(),o.ac(69,"span"),o.Lc(70,"Early Gone"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(71,"div",7),o.ac(72,"div",8),o.ac(73,"div",9),o.ac(74,"span",10),o.Vb(75,"i",14),o.Zb(),o.ac(76,"div",12),o.ac(77,"h3"),o.Lc(78),o.Zb(),o.ac(79,"span"),o.Lc(80,"On Tour"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(81,"div",7),o.ac(82,"div",8),o.ac(83,"div",9),o.ac(84,"span",10),o.Vb(85,"i",15),o.Zb(),o.ac(86,"div",12),o.ac(87,"h3"),o.Lc(88),o.Zb(),o.ac(89,"span"),o.Lc(90,"On Leave"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(91,"div",2),o.ac(92,"div",7),o.ac(93,"div",8),o.ac(94,"div",9),o.ac(95,"span",10),o.Vb(96,"i",16),o.Zb(),o.ac(97,"div",12),o.ac(98,"h3"),o.Lc(99,"0"),o.Zb(),o.ac(100,"span"),o.Lc(101,"Pending Recommendations"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(102,"div",7),o.ac(103,"div",8),o.ac(104,"div",9),o.ac(105,"span",10),o.Vb(106,"i",13),o.Zb(),o.ac(107,"div",12),o.ac(108,"h3"),o.Lc(109,"0"),o.Zb(),o.ac(110,"span"),o.Lc(111,"Pending Approvals"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(112,"div",7),o.ac(113,"div",8),o.ac(114,"div",9),o.ac(115,"span",10),o.Vb(116,"i",14),o.Zb(),o.ac(117,"div",12),o.ac(118,"h3"),o.Lc(119,"0"),o.Zb(),o.ac(120,"span"),o.Lc(121,"Total Salary"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(122,"div",7),o.ac(123,"div",8),o.ac(124,"div",9),o.ac(125,"span",10),o.Vb(126,"i",15),o.Zb(),o.ac(127,"div",12),o.ac(128,"h3"),o.Lc(129,"0"),o.Zb(),o.ac(130,"span"),o.Lc(131,"Total Bonus"),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(17),o.Mc(e.empTotal.totalEmployees),o.Ib(10),o.Mc(e.empThisMonth.lastMonthJoinedEmployees),o.Ib(10),o.Mc(e.empPresent.presentEmployees),o.Ib(10),o.Mc(e.empAbsent.absentEmployees),o.Ib(11),o.Mc(e.lateEmployee.lateEmployees),o.Ib(10),o.Mc(e.earlyGoneEmployee.earlyGoneEmployees),o.Ib(10),o.Mc(e.onTourEmployee.onTourEmployees),o.Ib(10),o.Mc(e.onLeaveEmployee.onLeaveEmployees))},styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),t})();var h=n("6Qlo"),m=n("d//k"),g=n("cRb6");const p=["hrHand"],u=["minHand"],y=["secHand"];function v(t,e){if(1&t&&(o.ac(0,"td",45),o.Lc(1),o.Zb()),2&t){const t=o.jc().$implicit;o.Ib(1),o.Nc(" ",t.attnDayStsFinalType," ")}}function Z(t,e){if(1&t&&(o.ac(0,"td",46),o.Lc(1),o.Zb()),2&t){const t=o.jc().$implicit;o.Ib(1),o.Nc(" ",t.attnDayStsFinalType," ")}}function f(t,e){if(1&t&&(o.ac(0,"td",47),o.Lc(1),o.Zb()),2&t){const t=o.jc().$implicit;o.Ib(1),o.Nc(" ",t.attnDayStsFinalType," ")}}function M(t,e){if(1&t&&(o.ac(0,"td",48),o.Lc(1),o.Zb()),2&t){const t=o.jc().$implicit;o.Ib(1),o.Nc(" ",t.attnDayStsFinalType," ")}}function T(t,e){if(1&t&&(o.ac(0,"td",48),o.Lc(1),o.Zb()),2&t){const t=o.jc().$implicit;o.Ib(1),o.Nc(" ",t.attnDayStsFinalType," ")}}function _(t,e){if(1&t&&(o.ac(0,"tr",40),o.ac(1,"td"),o.Lc(2),o.kc(3,"date"),o.Zb(),o.ac(4,"td"),o.Lc(5),o.Zb(),o.ac(6,"td"),o.Lc(7),o.Zb(),o.Jc(8,v,2,1,"td",41),o.Jc(9,Z,2,1,"td",42),o.Jc(10,f,2,1,"td",43),o.Jc(11,M,2,1,"td",44),o.Jc(12,T,2,1,"td",44),o.Zb()),2&t){const t=e.$implicit;o.Ib(2),o.Mc(o.lc(3,8,t.createDate)),o.Ib(3),o.Mc(t.inTime),o.Ib(2),o.Mc(t.outTime),o.Ib(1),o.pc("ngIf",1==t.isColor),o.Ib(1),o.pc("ngIf",2==t.isColor),o.Ib(1),o.pc("ngIf",null==t.isColor),o.Ib(1),o.pc("ngIf",4==t.isColor),o.Ib(1),o.pc("ngIf",3==t.isColor)}}const L=[{path:"",component:s,children:[{path:"admin",component:l},{path:"employee",component:(()=>{class t{constructor(t,e,n){this.empDeshbrd=t,this.login=e,this.headerService=n,this.daysArray=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],this.monthArray=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],this.date=new Date,this.baseUrl=r.a.baseUrl,this.listData=[]}ngOnInit(){setInterval(()=>{const t=new Date;this.updateClock(t)},1e3),this.day=this.daysArray[this.date.getDay()],this.dateCount=this.date.getDate(),this.month=this.monthArray[this.date.getMonth()],this.year=this.date.getFullYear(),this.lastSevenDaysAttn(),this.lodeLoginUser()}updateClock(t){this.secHand.nativeElement.style.transform="rotate("+6*t.getSeconds()+"deg)",this.minHand.nativeElement.style.transform="rotate("+6*t.getMinutes()+"deg)",this.hrHand.nativeElement.style.transform="rotate("+(30*t.getHours()+.5*t.getMinutes())+"deg)";const e=t.getHours();this.ampm=e>=12?"PM":"AM",this.hour=e%12,this.hour=this.hour?this.hour:12,this.hour=this.hour<10?"0"+this.hour:this.hour;const n=t.getMinutes();this.minute=n<10?"0"+n:n.toString();const a=t.getSeconds();this.second=a<10?"0"+a:a.toString()}lastSevenDaysAttn(){this.empDeshbrd.getLastSevenDaysAttn().subscribe(t=>{this.last7DaysAttn=t,console.log(this.last7DaysAttn)})}lodeLoginUser(){this.user=this.login.getUser(),this.profileImageUrl=this.baseUrl+this.user.pic_}}return t.\u0275fac=function(e){return new(e||t)(o.Ub(g.a),o.Ub(m.a),o.Ub(h.a))},t.\u0275cmp=o.Ob({type:t,selectors:[["app-employee-dashboard"]],viewQuery:function(t,e){if(1&t&&(o.Rc(p,1),o.Rc(u,1),o.Rc(y,1)),2&t){let t;o.yc(t=o.ic())&&(e.hrHand=t.first),o.yc(t=o.ic())&&(e.minHand=t.first),o.yc(t=o.ic())&&(e.secHand=t.first)}},decls:99,vars:14,consts:[[1,"content","container-fluid"],[1,"row"],[1,"col-md-12"],[1,"welcome-box"],[1,"welcome-img"],["onerror","this.src='assets/img/user-icon/u-sq-pic.jpg'","alt","",1,"img-design",3,"src"],[1,"welcome-det"],[1,"dayTitle"],[1,"col-lg-8","col-md-8"],[1,"dash-section"],[1,"dash-sec-title"],[1,"col-lg-4","col-md-4"],[1,"dash-sidebar"],[1,"card"],[1,"card-body"],[1,"center-clock"],[1,"clock"],[1,"num","num1"],[1,"num","num2"],[1,"num","num3"],[1,"num","num4"],[1,"num","num5"],[1,"num","num6"],[1,"num","num7"],[1,"num","num8"],[1,"num","num9"],[1,"num","num10"],[1,"num","num11"],[1,"num","num12"],[1,"hr-hand"],["hrHand",""],[1,"min-hand"],["minHand",""],[1,"sec-hand"],["secHand",""],[1,"digitalClock"],[1,"digitalTime"],[1,"table","table-sm"],["scope","col"],["style","font-size: 12px;",4,"ngFor","ngForOf"],[2,"font-size","12px"],["style","color: green;",4,"ngIf"],["style","color: #5c592d;",4,"ngIf"],["style","color: rgb(228, 19, 36);",4,"ngIf"],["style","color: rgb(251, 182, 54);",4,"ngIf"],[2,"color","green"],[2,"color","#5c592d"],[2,"color","rgb(228, 19, 36)"],[2,"color","rgb(251, 182, 54)"]],template:function(t,e){1&t&&(o.ac(0,"div",0),o.ac(1,"div",1),o.ac(2,"div",2),o.ac(3,"div",3),o.ac(4,"div",4),o.Vb(5,"img",5),o.Zb(),o.ac(6,"div",6),o.ac(7,"div"),o.ac(8,"h3"),o.Lc(9),o.Zb(),o.Zb(),o.ac(10,"div",7),o.Lc(11),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(12,"div",1),o.ac(13,"div",8),o.ac(14,"section",9),o.ac(15,"h1",10),o.Lc(16,"Notifications"),o.Zb(),o.Zb(),o.Zb(),o.ac(17,"div",11),o.ac(18,"div",12),o.ac(19,"section"),o.ac(20,"div",13),o.ac(21,"div",14),o.ac(22,"div",15),o.ac(23,"div",1),o.ac(24,"div",2),o.ac(25,"div",16),o.ac(26,"div",17),o.ac(27,"div"),o.Lc(28,"1"),o.Zb(),o.Zb(),o.ac(29,"div",18),o.ac(30,"div"),o.Lc(31,"2"),o.Zb(),o.Zb(),o.ac(32,"div",19),o.ac(33,"div"),o.Lc(34,"3"),o.Zb(),o.Zb(),o.ac(35,"div",20),o.ac(36,"div"),o.Lc(37,"4"),o.Zb(),o.Zb(),o.ac(38,"div",21),o.ac(39,"div"),o.Lc(40,"5"),o.Zb(),o.Zb(),o.ac(41,"div",22),o.ac(42,"div"),o.Lc(43,"6"),o.Zb(),o.Zb(),o.ac(44,"div",23),o.ac(45,"div"),o.Lc(46,"7"),o.Zb(),o.Zb(),o.ac(47,"div",24),o.ac(48,"div"),o.Lc(49,"8"),o.Zb(),o.Zb(),o.ac(50,"div",25),o.ac(51,"div"),o.Lc(52,"9"),o.Zb(),o.Zb(),o.ac(53,"div",26),o.ac(54,"div"),o.Lc(55,"10"),o.Zb(),o.Zb(),o.ac(56,"div",27),o.ac(57,"div"),o.Lc(58,"11"),o.Zb(),o.Zb(),o.ac(59,"div",28),o.ac(60,"div"),o.Lc(61,"12"),o.Zb(),o.Zb(),o.Vb(62,"div",29,30),o.Vb(64,"div",31,32),o.Vb(66,"div",33,34),o.Zb(),o.Zb(),o.ac(68,"div",2),o.ac(69,"div",15),o.ac(70,"div",35),o.ac(71,"div",36),o.ac(72,"div"),o.Lc(73),o.Zb(),o.ac(74,"div"),o.Lc(75),o.Zb(),o.ac(76,"div"),o.Lc(77),o.Zb(),o.ac(78,"div"),o.Lc(79),o.Zb(),o.Lc(80),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.ac(81,"section"),o.ac(82,"h3"),o.Lc(83,"My Last 7 Days Attendance"),o.Zb(),o.ac(84,"div",13),o.ac(85,"div",14),o.ac(86,"table",37),o.ac(87,"thead"),o.ac(88,"tr"),o.ac(89,"th",38),o.Lc(90,"Date"),o.Zb(),o.ac(91,"th",38),o.Lc(92,"In "),o.Zb(),o.ac(93,"th",38),o.Lc(94,"Out "),o.Zb(),o.ac(95,"th",38),o.Lc(96,"Status"),o.Zb(),o.Zb(),o.Zb(),o.ac(97,"tbody"),o.Jc(98,_,13,10,"tr",39),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb(),o.Zb()),2&t&&(o.Ib(5),o.pc("src",e.profileImageUrl,o.Fc),o.Ib(4),o.Nc("Welcome, ",e.user.firstName,""),o.Ib(2),o.Qc(" ",e.day,", ",e.dateCount," ",e.month," ",e.year," "),o.Ib(62),o.Nc("",e.hour," : \xa0"),o.Ib(2),o.Nc("",e.minute," : \xa0"),o.Ib(2),o.Nc("",e.second," \xa0"),o.Ib(2),o.Mc(e.ampm),o.Ib(1),o.Pc(" \xa0\xa0 | \xa0\xa0",e.dateCount," ",e.month," ",e.year," "),o.Ib(18),o.pc("ngForOf",e.last7DaysAttn))},directives:[a.l,a.m],pipes:[a.e],styles:['.content[_ngcontent-%COMP%]{padding:30px}.clock[_ngcontent-%COMP%]{position:relative;width:170px;height:170px;display:flex;justify-content:center;align-items:center;background:#fff;border-radius:50%;border:3px solid #9f9d9d;box-shadow:inset 0 0 30px rgba(0,0,0,.1),0 20px 20px rgba(0,0,0,.2),0 0 0 4px #fff;margin-left:auto;margin-right:auto}.clock[_ngcontent-%COMP%]:before{content:"";position:absolute;width:10px;height:10px;background:#848484;border:2px solid #fff;z-index:100000;border-radius:50%}.sec-hand[_ngcontent-%COMP%]{width:1px;height:50%;background:#ff6767;top:10%;left:50%}.min-hand[_ngcontent-%COMP%], .sec-hand[_ngcontent-%COMP%]{transform-origin:50% 80%;position:absolute;border-radius:100% 100% 0 0}.min-hand[_ngcontent-%COMP%]{width:3px;height:40%;background:#d6d6d6;top:18%;left:calc(50% - 1px)}.hr-hand[_ngcontent-%COMP%]{width:5px;height:25%;background:#848484;transform-origin:50% 80%;top:30%;left:calc(50% + -2px);border-radius:100% 100% 0 0}.hr-hand[_ngcontent-%COMP%], .num[_ngcontent-%COMP%]{position:absolute}.num[_ngcontent-%COMP%]{height:100%;left:calc(50% - .5em)}.num[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{width:1em;line-height:2em;color:#858383;text-align:center;vertical-align:middle}.num1[_ngcontent-%COMP%]{transform:rotate(30deg)}.num1[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-30deg)}.num2[_ngcontent-%COMP%]{transform:rotate(60deg)}.num2[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-60deg)}.num3[_ngcontent-%COMP%]{transform:rotate(90deg)}.num3[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-90deg)}.num4[_ngcontent-%COMP%]{transform:rotate(120deg)}.num4[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-120deg)}.num5[_ngcontent-%COMP%]{transform:rotate(150deg)}.num5[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-150deg)}.num6[_ngcontent-%COMP%]{transform:rotate(180deg)}.num6[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-180deg)}.num7[_ngcontent-%COMP%]{transform:rotate(210deg)}.num7[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-210deg)}.num8[_ngcontent-%COMP%]{transform:rotate(240deg)}.num8[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-240deg)}.num9[_ngcontent-%COMP%]{transform:rotate(270deg)}.num9[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-270deg)}.num10[_ngcontent-%COMP%]{transform:rotate(300deg)}.num10[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-300deg)}.num11[_ngcontent-%COMP%]{transform:rotate(330deg)}.num11[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-330deg)}.num12[_ngcontent-%COMP%]{transform:rotate(1turn)}.num12[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{transform:rotate(-1turn)}[_nghost-%COMP%]{justify-items:center}.digitalClock[_ngcontent-%COMP%], [_nghost-%COMP%]{display:flex;align-items:center;height:auto;width:auto}.digitalClock[_ngcontent-%COMP%]{margin-top:40px;position:relative;flex-direction:column;justify-content:center;background-color:#fdfdfd;border:3px solid #9f9d9d;border-radius:5px;box-shadow:inset 0 0 15px 0 20px 20px #000 rgba(0,0,0,.4);color:#484646;text-transform:uppercase}.digitalTime[_ngcontent-%COMP%]{display:flex}.dayTitle[_ngcontent-%COMP%]{font-size:16px}.img-design[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%}.center-clock[_ngcontent-%COMP%]{justify-content:center;align-items:left}']}),t})()}]}];let O=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=o.Sb({type:t}),t.\u0275inj=o.Rb({imports:[[c.f.forChild(L)],c.f]}),t})(),C=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=o.Sb({type:t}),t.\u0275inj=o.Rb({}),t})(),P=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=o.Sb({type:t}),t.\u0275inj=o.Rb({imports:[[a.c,O,C]]}),t})()}}]);