(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{"7K3g":function(e,t,a){"use strict";a.r(t),a.d(t,"SelfServiceModule",function(){return ee});var i=a("ofXK"),c=a("tyNb"),r=a("xrk7"),o=a("AytR"),n=a("un/a"),s=a("fXoL"),l=a("tk/3");let b=(()=>{class e{constructor(e){this.http=e}sendGetSelfRequest(e,t){return console.log("@sendGetSelfRequest"),this.http.get(e,{params:t}).pipe(Object(n.a)(3))}sendPostRequest(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}sendGetRequest(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(n.a)(3))}sendDeleteRequest(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}}return e.\u0275fac=function(t){return new(t||e)(s.ec(l.c))},e.\u0275prov=s.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var d=a("3Pt+"),p=a("5eHb"),m=a("ZOsW"),g=a("oW1M");let u=(()=>{class e{constructor(e,t,a,i,c,r,n){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.leaveService=c,this.toastr=r,this.commonService=n,this.baseUrl=o.a.baseUrl,this.leaveList=[],this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm(),this.loadAlkpLeave()}initializeForm(){this.myForm=this.formBuilder.group({hrCrEmp:{},contactNo:[""],hrCrEmpResponsible:{},alkpLeaveType:[""],startDate:[""],endDate:[""],addressDuringLeave:[""],reasonForLeave:[""],remarks:[""]})}loadAlkpLeave(){this.commonService.getAlkpByKeyword("LEAVETYPE").subscribe(e=>{this.alkpLeave=e,this.leaveList=this.alkpLeave.subALKP,console.log(this.leaveList)})}myFormSubmit(){if(this.checkSomeCondition())return;const e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null,alkpLeaveType:this.getAlkpLeaveId.value?{id:this.getAlkpLeaveId.value}:null});let t=this.baseUrl+"/leaveTrnse/save",a={};a=e,a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.leaveService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.router.navigate(["/sefl-service/employeeleaves"],{relativeTo:this.route})},e=>{console.log(e),this.toastr.error(e.error.message)})}checkSomeCondition(){if(57==this.myForm.value.alkpLeaveType){let e=new Date;return(this.myForm.value.startDate>e||this.myForm.value.endDate>e)&&(this.toastr.info("ML is not created"),!0)}return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}resetFormValues(){this.myForm.reset()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.listData=this.configDDL.append?this.configDDL.listData.concat(e.objectList):e.objectList,this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}get getAlkpLeaveId(){return this.myForm.get("alkpLeaveType")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(i.e),s.Ub(c.a),s.Ub(c.c),s.Ub(b),s.Ub(p.b),s.Ub(r.a))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-create-leave"]],decls:84,vars:12,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/employeeleaves",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","alkpLeaveType","bindLabel","title","bindValue","id","placeholder","Select","appendTo","body",3,"items"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","addressDuringLeave",1,"form-control"],["type","text","formControlName","reasonForLeave",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/employeeleaves",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"Leave"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"Leave"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Create"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"form",16),s.hc("ngSubmit",function(){return t.myFormSubmit()}),s.ac(25,"div",17),s.ac(26,"label",18),s.Lc(27,"Employee "),s.Zb(),s.ac(28,"div",19),s.ac(29,"ng-select",20),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(30,"div",17),s.ac(31,"label",18),s.Lc(32,"Responsible Employee "),s.Zb(),s.ac(33,"div",19),s.ac(34,"ng-select",21),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",17),s.ac(36,"label",18),s.Lc(37,"Responsible Employee Contact No"),s.Zb(),s.ac(38,"div",19),s.Vb(39,"input",22),s.Zb(),s.Zb(),s.ac(40,"div",17),s.ac(41,"label",18),s.Lc(42,"Leave Type *"),s.Zb(),s.ac(43,"div",19),s.Vb(44,"ng-select",23),s.Zb(),s.Zb(),s.ac(45,"div",17),s.ac(46,"label",18),s.Lc(47,"Start Date"),s.Zb(),s.ac(48,"div",19),s.ac(49,"div",24),s.Vb(50,"input",25),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",17),s.ac(52,"label",18),s.Lc(53,"End Date"),s.Zb(),s.ac(54,"div",19),s.ac(55,"div",24),s.Vb(56,"input",26),s.Zb(),s.Zb(),s.Zb(),s.ac(57,"div",17),s.ac(58,"label",18),s.Lc(59,"Address During Leave"),s.Zb(),s.ac(60,"div",19),s.Vb(61,"textarea",27),s.Zb(),s.Zb(),s.ac(62,"div",17),s.ac(63,"label",18),s.Lc(64,"Reason For Leave"),s.Zb(),s.ac(65,"div",19),s.Vb(66,"textarea",28),s.Zb(),s.Zb(),s.ac(67,"div",17),s.ac(68,"label",18),s.Lc(69,"Remarks"),s.Zb(),s.ac(70,"div",19),s.Vb(71,"textarea",29),s.Zb(),s.Zb(),s.ac(72,"div",30),s.ac(73,"a",31),s.Vb(74,"i",11),s.Lc(75," Cancel"),s.Zb(),s.Lc(76," \xa0 \xa0 "),s.ac(77,"button",32),s.hc("click",function(){return t.resetFormValues()}),s.Vb(78,"i",33),s.Lc(79," Reset "),s.Zb(),s.Lc(80," \xa0 \xa0 "),s.ac(81,"button",34),s.Vb(82,"i",35),s.Lc(83," Save \xa0\xa0\xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(24),s.pc("formGroup",t.myForm),s.Ib(5),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),s.Ib(5),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),s.Ib(10),s.pc("items",t.leaveList))},directives:[c.e,d.x,d.p,d.h,m.a,d.o,d.f,d.t,d.b,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();var h=a("JqCM");let D=(()=>{class e{constructor(e,t,a,i,c,r,n,s){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.leaveService=c,this.toastr=r,this.spinnerService=n,this.commonService=s,this.baseUrl=o.a.baseUrl,this.myFormData={},this.leaveList=[],this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm(),this.getFormData(),this.loadAlkpLeave()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:[""],contactNo:[""],hrCrEmpResponsible:[""],alkpLeaveType:[""],startDate:[""],endDate:[""],addressDuringLeave:[""],reasonForLeave:[""],remarks:[""]})}loadAlkpLeave(){this.commonService.getAlkpByKeyword("LEAVETYPE").subscribe(e=>{this.alkpLeave=e,this.leaveList=this.alkpLeave.subALKP,console.log(this.leaveList)})}getFormData(){let e=this.baseUrl+"/leaveTrnse/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.leaveService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide(),this.configDDL.listData=[{ddlCode:e.hrCrEmp.id,ddlDescription:e.hrCrEmp.loginCode+"-"+e.hrCrEmp.displayName}],this.myFormData.hrCrEmp=e.hrCrEmp.id,this.configDDL.listData2=[{ddlCode:e.hrCrEmpResponsible.id,ddlDescription:e.hrCrEmpResponsible.loginCode+"-"+e.hrCrEmpResponsible.displayName}],this.myFormData.hrCrEmpResponsible=e.hrCrEmpResponsible.id,this.myFormData.startDate=this.datePipe.transform(e.startDate,"MM-dd-yyyy").toString().slice(0,10),this.myFormData.endDate=this.datePipe.transform(e.endDate,"MM-dd-yyyy").toString().slice(0,10),this.myFormData.alkpLeaveType=e.alkpLeaveType.id,this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){if(this.checkSomeCondition())return;const e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null,alkpLeaveType:this.getAlkpLeaveId.value?{id:this.getAlkpLeaveId.value}:null});let t=this.baseUrl+"/leaveTrnse/save",a={};a=e,a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.leaveService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.router.navigate(["/sefl-service/employeeleaves"],{relativeTo:this.route})},e=>{console.log(e),this.toastr.error(e.error.message)})}checkSomeCondition(){if(57==this.myForm.value.alkpLeaveType){let e=new Date;return(this.myForm.value.startDate>e||this.myForm.value.endDate>e)&&(this.toastr.info("ML is not created"),!0)}return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}resetFormValues(){this.getFormData()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?(this.configDDL.listData=this.configDDL.listData.concat(e.objectList),this.configDDL.listData2=this.configDDL.listData2.concat(e.objectList)):(this.configDDL.listData=e.objectList,this.configDDL.listData2=e.objectList),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}get getAlkpLeaveId(){return this.myForm.get("alkpLeaveType")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(i.e),s.Ub(c.a),s.Ub(c.c),s.Ub(b),s.Ub(p.b),s.Ub(h.c),s.Ub(r.a))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-edit-leave"]],decls:84,vars:12,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/employeeleaves",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","alkpLeaveType","bindLabel","title","bindValue","id","placeholder","Select","appendTo","body",3,"items"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","addressDuringLeave",1,"form-control"],["type","text","formControlName","reasonForLeave",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/employeeleaves",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"Leave"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"Leave"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Edit"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"form",16),s.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),s.ac(25,"div",17),s.ac(26,"label",18),s.Lc(27,"Employee "),s.Zb(),s.ac(28,"div",19),s.ac(29,"ng-select",20),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(30,"div",17),s.ac(31,"label",18),s.Lc(32,"Responsible Employee "),s.Zb(),s.ac(33,"div",19),s.ac(34,"ng-select",21),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",17),s.ac(36,"label",18),s.Lc(37,"Responsible Employee Contact No"),s.Zb(),s.ac(38,"div",19),s.Vb(39,"input",22),s.Zb(),s.Zb(),s.ac(40,"div",17),s.ac(41,"label",18),s.Lc(42,"Leave Type *"),s.Zb(),s.ac(43,"div",19),s.Vb(44,"ng-select",23),s.Zb(),s.Zb(),s.ac(45,"div",17),s.ac(46,"label",18),s.Lc(47,"Start Date"),s.Zb(),s.ac(48,"div",19),s.ac(49,"div",24),s.Vb(50,"input",25),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",17),s.ac(52,"label",18),s.Lc(53,"End Date"),s.Zb(),s.ac(54,"div",19),s.ac(55,"div",24),s.Vb(56,"input",26),s.Zb(),s.Zb(),s.Zb(),s.ac(57,"div",17),s.ac(58,"label",18),s.Lc(59,"Address During Leave"),s.Zb(),s.ac(60,"div",19),s.Vb(61,"textarea",27),s.Zb(),s.Zb(),s.ac(62,"div",17),s.ac(63,"label",18),s.Lc(64,"Reason For Leave"),s.Zb(),s.ac(65,"div",19),s.Vb(66,"textarea",28),s.Zb(),s.Zb(),s.ac(67,"div",17),s.ac(68,"label",18),s.Lc(69,"Remarks"),s.Zb(),s.ac(70,"div",19),s.Vb(71,"textarea",29),s.Zb(),s.Zb(),s.ac(72,"div",30),s.ac(73,"a",31),s.Vb(74,"i",11),s.Lc(75," Cancel"),s.Zb(),s.Lc(76," \xa0 \xa0 "),s.ac(77,"button",32),s.hc("click",function(){return t.resetFormValues()}),s.Vb(78,"i",33),s.Lc(79," Reset "),s.Zb(),s.Lc(80," \xa0 \xa0 "),s.ac(81,"button",34),s.Vb(82,"i",35),s.Lc(83," Save \xa0\xa0\xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(24),s.pc("formGroup",t.myForm),s.Ib(5),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),s.Ib(5),s.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),s.Ib(10),s.pc("items",t.leaveList))},directives:[c.e,d.x,d.p,d.h,m.a,d.o,d.f,d.t,d.b,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();var f=a("QMHJ"),L=a("d//k"),v=a("oOf3");function Z(e,t){if(1&e&&(s.ac(0,"div",58),s.ac(1,"div",59),s.ac(2,"h4"),s.Lc(3),s.Zb(),s.Vb(4,"br"),s.ac(5,"h6"),s.Lc(6),s.Zb(),s.ac(7,"h6"),s.Lc(8),s.Zb(),s.ac(9,"h6"),s.Lc(10),s.Zb(),s.Zb(),s.Zb()),2&e){const e=t.$implicit;s.Ib(3),s.Nc("Leave Type : ",e.leaveType,""),s.Ib(3),s.Nc("Leave Days : ",e.leaveDays,""),s.Ib(2),s.Nc("Taken Days : ",e.takenDays,""),s.Ib(2),s.Nc("Carry Days : ",e.carryDays,"")}}function y(e,t){if(1&e){const e=s.bc();s.ac(0,"tr"),s.ac(1,"td"),s.Lc(2),s.Zb(),s.ac(3,"td"),s.Lc(4),s.Zb(),s.ac(5,"td"),s.Lc(6),s.Zb(),s.ac(7,"td"),s.Lc(8),s.Zb(),s.ac(9,"td"),s.Lc(10),s.kc(11,"date"),s.Zb(),s.ac(12,"td"),s.Lc(13),s.kc(14,"date"),s.Zb(),s.ac(15,"td"),s.ac(16,"span",60),s.Lc(17),s.Zb(),s.Zb(),s.ac(18,"td"),s.Lc(19),s.Zb(),s.ac(20,"td",38),s.ac(21,"div",61),s.ac(22,"a",62),s.ac(23,"i",63),s.Lc(24,"more_vert"),s.Zb(),s.Zb(),s.ac(25,"div",64),s.ac(26,"a",65),s.Vb(27,"i",66),s.Lc(28,"View "),s.Zb(),s.ac(29,"a",67),s.Vb(30,"i",68),s.Lc(31,"Edit "),s.Zb(),s.ac(32,"a",69),s.hc("click",function(){s.Cc(e);const a=t.$implicit;return s.jc().tempId=a.id}),s.Vb(33,"i",70),s.Lc(34,"Delete "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=s.jc();s.Mb("active",a==i.currentIndex),s.Ib(2),s.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),s.Ib(2),s.Mc(e.leaveType),s.Ib(2),s.Mc(e.leaveDays),s.Ib(2),s.Mc(e.createDate),s.Ib(2),s.Mc(s.mc(11,12,e.startDate,"yyyy-MM-dd")),s.Ib(3),s.Mc(s.mc(14,15,e.endDate,"yyyy-MM-dd")),s.Ib(4),s.Mc(e.leaveApprovalStatus),s.Ib(2),s.Mc(e.hrLeavePrd.title),s.Ib(7),s.rc("routerLink","./view/",e.id,""),s.Ib(3),s.rc("routerLink","./edit/",e.id,"")}}function S(e,t){1&e&&(s.ac(0,"tr"),s.ac(1,"td",71),s.ac(2,"h5",72),s.Lc(3,"No data found"),s.Zb(),s.Zb(),s.Zb())}function C(e,t){if(1&e&&(s.ac(0,"option",73),s.Lc(1),s.Zb()),2&e){const e=t.$implicit;s.pc("value",e),s.Ib(1),s.Nc(" ",e," ")}}const P=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let x=(()=>{class e{constructor(e,t,a,i,c,r){this.formBuilder=e,this.leaveCnfService=t,this.leaveService=a,this.login=i,this.toastr=c,this.spinnerService=r,this.baseUrl=o.a.baseUrl,this.selfLeaveList=[],this.selfCreatedLeaveList=[],this.incharge=[],this.leaveList=[],this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.loginUser(),this.loadSelfLeave(),this.loadSelfCreatedLeave()}loginUser(){this.user=this.login.getUser(),console.log(this.user)}loadSelfLeave(){this.leaveCnfService.getselfLeave().subscribe(e=>{this.selfLeaveList=e,console.log(this.selfLeaveList)})}loadSelfCreatedLeave(){let e=this.baseUrl+"/leaveTrnse/getAllHrEmpLeaves",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),t.hrCrEmp=this.user.id,this.spinnerService.show(),this.leaveService.sendGetSelfRequest(e,t).subscribe(e=>{this.selfCreatedLeaveList=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),console.log(this.selfCreatedLeaveList),this.spinnerService.hide()},e=>{console.log(e)})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}deleteEnityData(e){let t=this.baseUrl+"/leaveTrnse/delete/"+e;console.log(t),this.spinnerService.show(),this.leaveService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.loadSelfCreatedLeave()},e=>{console.log(e),this.spinnerService.hide()})}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.loadSelfCreatedLeave()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.loadSelfCreatedLeave()}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(f.a),s.Ub(b),s.Ub(L.a),s.Ub(p.b),s.Ub(h.c))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-leaves-employee"]],decls:116,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"row"],["class","col-md-3",4,"ngFor","ngForOf"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/sefl-service/employeeleaves/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"text-right"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"col-md-3"],[1,"stats-info"],[1,"badge","badge-success"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],[1,"btn","btn-sm","btn-info","dropdown-item",3,"routerLink"],[1,"fa","fa-eye","m-r-5"],[1,"btn","btn-sm","btn-primary","dropdown-item",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger","dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"Leaves"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"Leaves"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"List"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"div",10),s.ac(18,"button",11),s.Lc(19,"Excel"),s.Zb(),s.ac(20,"button",11),s.Lc(21,"PDF"),s.Zb(),s.ac(22,"button",11),s.Vb(23,"i",12),s.Lc(24," Print"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(25,"div",13),s.Jc(26,Z,11,4,"div",14),s.Zb(),s.ac(27,"div",15),s.ac(28,"div",16),s.ac(29,"div",17),s.ac(30,"div",18),s.ac(31,"div",19),s.Vb(32,"input",20),s.ac(33,"label",21),s.Lc(34,"Employee Code"),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",22),s.ac(36,"div",19),s.ac(37,"div",23),s.Vb(38,"input",24),s.Zb(),s.ac(39,"label",21),s.Lc(40,"From"),s.Zb(),s.Zb(),s.Zb(),s.ac(41,"div",22),s.ac(42,"div",19),s.ac(43,"div",23),s.Vb(44,"input",24),s.Zb(),s.ac(45,"label",21),s.Lc(46,"To"),s.Zb(),s.Zb(),s.Zb(),s.ac(47,"div",22),s.ac(48,"a",25),s.Lc(49," Search "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(50,"div",13),s.ac(51,"div",26),s.ac(52,"div",27),s.ac(53,"div",28),s.ac(54,"div",29),s.ac(55,"a",30),s.Vb(56,"i",31),s.Lc(57," New \xa0\xa0\xa0"),s.Zb(),s.Zb(),s.Zb(),s.ac(58,"div",32),s.ac(59,"div",33),s.ac(60,"div",34),s.ac(61,"div",35),s.ac(62,"span",36),s.Lc(63),s.Zb(),s.Zb(),s.Zb(),s.ac(64,"table",37),s.ac(65,"thead"),s.ac(66,"tr"),s.ac(67,"th"),s.Lc(68,"#"),s.Zb(),s.ac(69,"th"),s.Lc(70,"Leave Type"),s.Zb(),s.ac(71,"th"),s.Lc(72,"Leave Days"),s.Zb(),s.ac(73,"th"),s.Lc(74,"Apply Date"),s.Zb(),s.ac(75,"th"),s.Lc(76,"From Date"),s.Zb(),s.ac(77,"th"),s.Lc(78,"To Date"),s.Zb(),s.ac(79,"th"),s.Lc(80,"Approved Sts"),s.Zb(),s.ac(81,"th"),s.Lc(82,"Leave Prd"),s.Zb(),s.ac(83,"th",38),s.Lc(84,"Action"),s.Zb(),s.Zb(),s.Zb(),s.ac(85,"tbody"),s.Jc(86,y,35,18,"tr",39),s.kc(87,"paginate"),s.Jc(88,S,4,0,"tr",40),s.Zb(),s.Zb(),s.ac(89,"div",41),s.ac(90,"div",42),s.Lc(91," Items per Page "),s.ac(92,"select",43),s.hc("change",function(e){return t.handlePageSizeChange(e)}),s.Jc(93,C,2,2,"option",44),s.Zb(),s.Zb(),s.ac(94,"div",45),s.ac(95,"pagination-controls",46),s.hc("pageChange",function(e){return t.handlePageChange(e)}),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(96,"ngx-spinner",47),s.ac(97,"p",48),s.Lc(98," Processing... "),s.Zb(),s.Zb(),s.ac(99,"div",49),s.ac(100,"div",50),s.ac(101,"div",51),s.ac(102,"div",52),s.ac(103,"div",53),s.ac(104,"h3"),s.Lc(105,"Delete Item"),s.Zb(),s.ac(106,"p"),s.Lc(107,"Are you sure want to delete?"),s.Zb(),s.Zb(),s.ac(108,"div",54),s.ac(109,"div",13),s.ac(110,"div",55),s.ac(111,"a",56),s.Lc(112,"Delete"),s.Zb(),s.Zb(),s.ac(113,"div",55),s.ac(114,"a",57),s.Lc(115,"Cancel"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(26),s.pc("ngForOf",t.selfLeaveList),s.Ib(12),s.pc("bsConfig",s.sc(13,P)),s.Ib(6),s.pc("bsConfig",s.sc(14,P)),s.Ib(19),s.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),s.Ib(23),s.pc("ngForOf",s.mc(87,10,t.selfCreatedLeaveList,t.configPgn)),s.Ib(2),s.pc("ngIf",0===t.selfCreatedLeaveList.length),s.Ib(5),s.pc("ngForOf",t.configPgn.pageSizes),s.Ib(3),s.pc("fullScreen",!1))},directives:[c.e,i.l,g.b,g.a,i.m,v.c,h.a,d.s,d.y],pipes:[v.b,i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}.title[_ngcontent-%COMP%]{background-color:#aabbdc;color:#105ff1;text-align:center}"]}),e})(),F=(()=>{class e{constructor(e,t,a){this.route=e,this.spinnerService=t,this.leaveService=a,this.baseUrl=o.a.baseUrl,this.myData={}}ngOnInit(){this.getFormData()}getFormData(){let e=this.baseUrl+"/leaveTrnse/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.leaveService.sendGetRequest(e,{}).subscribe(e=>{this.myData=e,console.log(this.myData),this.spinnerService.hide()},e=>{console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(c.a),s.Ub(h.c),s.Ub(b))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-view-leave"]],decls:150,vars:24,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/employeeleaves",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"row","fieldsetBorder","logBox"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/sefl-service/employeeleaves",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self-service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"OnTour"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Show"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"fieldset",16),s.Vb(25,"legend"),s.ac(26,"div",17),s.ac(27,"div",18),s.ac(28,"label",19),s.Lc(29,"Employee"),s.Zb(),s.ac(30,"div",20),s.ac(31,"span"),s.Lc(32,": \xa0"),s.Zb(),s.ac(33,"span"),s.Lc(34),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",18),s.ac(36,"label",19),s.Lc(37,"Responsible Employee"),s.Zb(),s.ac(38,"div",20),s.ac(39,"span"),s.Lc(40,": \xa0"),s.Zb(),s.ac(41,"span"),s.Lc(42),s.Zb(),s.Zb(),s.Zb(),s.ac(43,"div",18),s.ac(44,"label",19),s.Lc(45,"Leave Type"),s.Zb(),s.ac(46,"div",20),s.ac(47,"span"),s.Lc(48,": \xa0"),s.Zb(),s.ac(49,"span"),s.Lc(50),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",18),s.ac(52,"label",19),s.Lc(53,"Start Date"),s.Zb(),s.ac(54,"div",20),s.ac(55,"span"),s.Lc(56,": \xa0"),s.Zb(),s.ac(57,"span"),s.Lc(58),s.kc(59,"date"),s.Zb(),s.Zb(),s.Zb(),s.ac(60,"div",18),s.ac(61,"label",19),s.Lc(62,"End Date"),s.Zb(),s.ac(63,"div",20),s.ac(64,"span"),s.Lc(65,": \xa0"),s.Zb(),s.ac(66,"span"),s.Lc(67),s.kc(68,"date"),s.Zb(),s.Zb(),s.Zb(),s.ac(69,"div",18),s.ac(70,"label",19),s.Lc(71,"Leave Days"),s.Zb(),s.ac(72,"div",20),s.ac(73,"span"),s.Lc(74,": \xa0"),s.Zb(),s.ac(75,"span"),s.Lc(76),s.Zb(),s.Zb(),s.Zb(),s.ac(77,"div",18),s.ac(78,"label",19),s.Lc(79,"Leave Location"),s.Zb(),s.ac(80,"div",20),s.ac(81,"span"),s.Lc(82,": \xa0"),s.Zb(),s.ac(83,"span"),s.Lc(84),s.Zb(),s.Zb(),s.Zb(),s.ac(85,"div",18),s.ac(86,"label",19),s.Lc(87,"Couse Of Leave"),s.Zb(),s.ac(88,"div",20),s.ac(89,"span"),s.Lc(90,": \xa0"),s.Zb(),s.ac(91,"span"),s.Lc(92),s.Zb(),s.Zb(),s.Zb(),s.ac(93,"div",18),s.ac(94,"label",19),s.Lc(95,"Remarks"),s.Zb(),s.ac(96,"div",20),s.ac(97,"span"),s.Lc(98,": \xa0"),s.Zb(),s.ac(99,"span"),s.Lc(100),s.Zb(),s.Zb(),s.Zb(),s.ac(101,"div",18),s.ac(102,"label",19),s.Lc(103,"Approval Status"),s.Zb(),s.ac(104,"div",20),s.ac(105,"span"),s.Lc(106,": \xa0"),s.Zb(),s.ac(107,"span"),s.Lc(108),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(109,"fieldset",21),s.ac(110,"legend"),s.Lc(111,"System Log Information"),s.Zb(),s.ac(112,"div",22),s.ac(113,"label",23),s.Lc(114,"ID"),s.Zb(),s.ac(115,"div",24),s.ac(116,"span"),s.Lc(117),s.Zb(),s.Zb(),s.Zb(),s.ac(118,"div",22),s.ac(119,"label",23),s.Lc(120,"Creation Time"),s.Zb(),s.ac(121,"div",24),s.ac(122,"span"),s.Lc(123),s.Zb(),s.Zb(),s.Zb(),s.ac(124,"div",22),s.ac(125,"label",23),s.Lc(126,"Creation User"),s.Zb(),s.ac(127,"div",24),s.ac(128,"span"),s.Lc(129),s.Zb(),s.Zb(),s.Zb(),s.ac(130,"div",22),s.ac(131,"label",23),s.Lc(132,"Last Update Time"),s.Zb(),s.ac(133,"div",24),s.ac(134,"span"),s.Lc(135),s.Zb(),s.Zb(),s.Zb(),s.ac(136,"div",22),s.ac(137,"label",23),s.Lc(138,"Last Update User"),s.Zb(),s.ac(139,"div",24),s.ac(140,"span"),s.Lc(141),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(142,"div",25),s.ac(143,"a",26),s.Vb(144,"i",11),s.Lc(145," Close"),s.Zb(),s.Lc(146," \xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(147,"ngx-spinner",27),s.ac(148,"p",28),s.Lc(149," Processing... "),s.Zb(),s.Zb()),2&e&&(s.Ib(34),s.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),s.Ib(8),s.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),s.Ib(8),s.Mc(t.myData.leaveType),s.Ib(8),s.Mc(s.mc(59,18,t.myData.startDate,"yyyy-MM-dd")),s.Ib(9),s.Mc(s.mc(68,21,t.myData.endDate,"yyyy-MM-dd")),s.Ib(9),s.Mc(t.myData.leaveDays),s.Ib(8),s.Mc(t.myData.addressDuringLeave),s.Ib(8),s.Mc(t.myData.reasonForLeave),s.Ib(8),s.Mc(t.myData.remarks),s.Ib(8),s.Mc(t.myData.leaveApprovalStatus),s.Ib(9),s.Mc(t.myData.id),s.Ib(6),s.Mc(t.myData.createDate),s.Ib(6),s.Mc(t.myData.createdByHrCrEmp.user.username),s.Ib(6),s.Mc(t.myData.updateDateTime),s.Ib(6),s.Mc(t.myData.lastUpdateUser),s.Ib(6),s.pc("fullScreen",!1))},directives:[c.e,h.a],pipes:[i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),e})(),T=(()=>{class e{constructor(e){this.http=e}sendGetSelfRequest(e,t){return console.log("@sendGetSelfRequest"),this.http.get(e,{params:t}).pipe(Object(n.a)(3))}sendPostRequest(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}sendGetRequest(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(n.a)(3))}sendDeleteRequest(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}}return e.\u0275fac=function(t){return new(t||e)(s.ec(l.c))},e.\u0275prov=s.Qb({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),k=(()=>{class e{constructor(e,t,a,i,c,r,n,s){this.formBuilder=e,this.datePipe=t,this.login=a,this.route=i,this.router=c,this.onTourService=r,this.toastr=n,this.commonService=s,this.baseUrl=o.a.baseUrl,this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm(),this.liginUser(),this.setValue()}initializeForm(){this.myForm=this.formBuilder.group({hrCrEmp:{},contactNo:["",[d.w.required]],hrCrEmpResponsible:[{},[d.w.required]],tourType:["",[d.w.required]],startDate:["",[d.w.required]],endDate:["",[d.w.required]],addressDuringTour:["",[d.w.required]],reasonForTour:["",[d.w.required]],remarks:[""]})}liginUser(){this.user=this.login.getUser(),console.log(this.user)}setValue(){this.myForm.get("hrCrEmp").setValue(this.user.firstName+" "+this.user.lastName),this.myForm.get("contactNo").setValue(this.user.mobCode)}myFormSubmit(){if(this.myForm.invalid)return void this.toastr.info("Please insert valid data");if(this.checkSomeCondition())return;const e=Object.assign(this.myForm.value,{hrCrEmp:{id:this.user.id},hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null});let t=this.baseUrl+"/onTourTnx/save",a={};a=e,console.log(a),a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.onTourService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.router.navigate(["/sefl-service/onTour"],{relativeTo:this.route})},e=>{console.log(e)})}checkSomeCondition(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}contactFind(e){alert(e)}resetFormValues(){this.myForm.reset()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.listData=this.configDDL.append?this.configDDL.listData.concat(e.objectList):e.objectList,this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(i.e),s.Ub(L.a),s.Ub(c.a),s.Ub(c.c),s.Ub(T),s.Ub(p.b),s.Ub(r.a))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-create"]],decls:90,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTour",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","hrCrEmp","disabled","",1,"form-control"],["oninput","javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);","type","number","maxlength","11","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTour",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Create"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"form",16),s.hc("ngSubmit",function(){return t.myFormSubmit()}),s.ac(25,"div",17),s.ac(26,"label",18),s.Lc(27,"Employee"),s.Zb(),s.ac(28,"div",19),s.Vb(29,"input",20),s.Zb(),s.Zb(),s.ac(30,"div",17),s.ac(31,"label",18),s.Lc(32,"Contact No"),s.Zb(),s.ac(33,"div",19),s.Vb(34,"input",21),s.Zb(),s.Zb(),s.ac(35,"div",17),s.ac(36,"label",18),s.Lc(37,"Responsible Employee "),s.Zb(),s.ac(38,"div",19),s.ac(39,"ng-select",22),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(40,"div",17),s.ac(41,"label",18),s.Lc(42,"Tour Type"),s.Zb(),s.ac(43,"div",19),s.ac(44,"select",23),s.ac(45,"option",24),s.Lc(46,"Select"),s.Zb(),s.ac(47,"option",25),s.Lc(48,"Local"),s.Zb(),s.ac(49,"option",26),s.Lc(50,"Global"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",17),s.ac(52,"label",18),s.Lc(53,"Start Date"),s.Zb(),s.ac(54,"div",19),s.ac(55,"div",27),s.Vb(56,"input",28),s.Zb(),s.Zb(),s.Zb(),s.ac(57,"div",17),s.ac(58,"label",18),s.Lc(59,"End Date"),s.Zb(),s.ac(60,"div",19),s.ac(61,"div",27),s.Vb(62,"input",29),s.Zb(),s.Zb(),s.Zb(),s.ac(63,"div",17),s.ac(64,"label",18),s.Lc(65,"Address During Tour"),s.Zb(),s.ac(66,"div",19),s.Vb(67,"textarea",30),s.Zb(),s.Zb(),s.ac(68,"div",17),s.ac(69,"label",18),s.Lc(70,"Reason For Tour"),s.Zb(),s.ac(71,"div",19),s.Vb(72,"textarea",31),s.Zb(),s.Zb(),s.ac(73,"div",17),s.ac(74,"label",18),s.Lc(75,"Remarks"),s.Zb(),s.ac(76,"div",19),s.Vb(77,"textarea",32),s.Zb(),s.Zb(),s.ac(78,"div",33),s.ac(79,"a",34),s.Vb(80,"i",11),s.Lc(81," Cancel"),s.Zb(),s.Lc(82," \xa0 \xa0 "),s.ac(83,"button",35),s.hc("click",function(){return t.resetFormValues()}),s.Vb(84,"i",36),s.Lc(85," Reset "),s.Zb(),s.Lc(86," \xa0 \xa0 "),s.ac(87,"button",37),s.Vb(88,"i",38),s.Lc(89," Save \xa0\xa0\xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(24),s.pc("formGroup",t.myForm),s.Ib(15),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[c.e,d.x,d.p,d.h,d.b,d.o,d.f,d.t,d.k,m.a,d.v,d.s,d.y,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})(),N=(()=>{class e{constructor(e,t,a,i,c,r,n,s){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.onTourService=c,this.toastr=r,this.commonService=n,this.spinnerService=s,this.baseUrl=o.a.baseUrl,this.myFormData={},this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",[d.w.required]],contactNo:["",[d.w.required]],hrCrEmpResponsible:["",[d.w.required]],tourType:["",[d.w.required]],startDate:["",[d.w.required]],endDate:["",[d.w.required]],addressDuringTour:["",[d.w.required]],reasonForTour:["",[d.w.required]],remarks:[""]})}getFormData(){let e=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide(),this.configDDL.listData=[{ddlCode:e.hrCrEmp.id,ddlDescription:e.hrCrEmp.loginCode+"-"+e.hrCrEmp.displayName}],this.myFormData.hrCrEmp=e.hrCrEmp.id,this.configDDL.listData2=[{ddlCode:e.hrCrEmpResponsible.id,ddlDescription:e.hrCrEmpResponsible.loginCode+"-"+e.hrCrEmpResponsible.displayName}],this.myFormData.hrCrEmpResponsible=e.hrCrEmpResponsible.id,this.myFormData.startDate=this.datePipe.transform(e.startDate,"MM-dd-yyyy").toString().slice(0,10),this.myFormData.endDate=this.datePipe.transform(e.endDate,"MM-dd-yyyy").toString().slice(0,10),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){if(this.myForm.invalid)return void this.toastr.info("Please insert valid data");if(this.checkSomeCondition())return;const e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null});let t=this.baseUrl+"/onTourTnx/save";console.log(t);let a={};a=e,a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.onTourService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/sefl-service/onTour"],{relativeTo:this.route})},e=>{console.log(e),this.toastr.warning(e.error.message),this.spinnerService.hide()})}checkSomeCondition(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}resetFormValues(){this.getFormData()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?(this.configDDL.listData=this.configDDL.listData.concat(e.objectList),this.configDDL.listData2=this.configDDL.listData2.concat(e.objectList)):(this.configDDL.listData=e.objectList,this.configDDL.listData2=e.objectList),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(i.e),s.Ub(c.a),s.Ub(c.c),s.Ub(T),s.Ub(p.b),s.Ub(r.a),s.Ub(h.c))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-edit"]],decls:85,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTour",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTour",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Edit"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"form",16),s.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),s.ac(25,"div",17),s.ac(26,"label",18),s.Lc(27,"Contact No"),s.Zb(),s.ac(28,"div",19),s.Vb(29,"input",20),s.Zb(),s.Zb(),s.ac(30,"div",17),s.ac(31,"label",18),s.Lc(32,"Responsible Employee "),s.Zb(),s.ac(33,"div",19),s.ac(34,"ng-select",21),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",17),s.ac(36,"label",18),s.Lc(37,"Tour Type"),s.Zb(),s.ac(38,"div",19),s.ac(39,"select",22),s.ac(40,"option",23),s.Lc(41,"Select"),s.Zb(),s.ac(42,"option",24),s.Lc(43,"Local"),s.Zb(),s.ac(44,"option",25),s.Lc(45,"Global"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(46,"div",17),s.ac(47,"label",18),s.Lc(48,"Start Date"),s.Zb(),s.ac(49,"div",19),s.ac(50,"div",26),s.Vb(51,"input",27),s.Zb(),s.Zb(),s.Zb(),s.ac(52,"div",17),s.ac(53,"label",18),s.Lc(54,"End Date"),s.Zb(),s.ac(55,"div",19),s.ac(56,"div",26),s.Vb(57,"input",28),s.Zb(),s.Zb(),s.Zb(),s.ac(58,"div",17),s.ac(59,"label",18),s.Lc(60,"Address During Tour"),s.Zb(),s.ac(61,"div",19),s.Vb(62,"textarea",29),s.Zb(),s.Zb(),s.ac(63,"div",17),s.ac(64,"label",18),s.Lc(65,"Reason For Tour"),s.Zb(),s.ac(66,"div",19),s.Vb(67,"textarea",30),s.Zb(),s.Zb(),s.ac(68,"div",17),s.ac(69,"label",18),s.Lc(70,"Remarks"),s.Zb(),s.ac(71,"div",19),s.Vb(72,"textarea",31),s.Zb(),s.Zb(),s.ac(73,"div",32),s.ac(74,"a",33),s.Vb(75,"i",11),s.Lc(76," Cancel"),s.Zb(),s.Lc(77," \xa0 \xa0 "),s.ac(78,"button",34),s.hc("click",function(){return t.resetFormValues()}),s.Vb(79,"i",35),s.Lc(80," Reset "),s.Zb(),s.Lc(81," \xa0 \xa0 "),s.ac(82,"button",36),s.Vb(83,"i",37),s.Lc(84," Save \xa0\xa0\xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(24),s.pc("formGroup",t.myForm),s.Ib(10),s.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[c.e,d.x,d.p,d.h,d.t,d.b,d.o,d.f,m.a,d.v,d.s,d.y,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})();function E(e,t){if(1&e){const e=s.bc();s.ac(0,"tr"),s.ac(1,"td"),s.Lc(2),s.Zb(),s.ac(3,"td"),s.Lc(4),s.Zb(),s.ac(5,"td"),s.Lc(6),s.Zb(),s.ac(7,"td"),s.Lc(8),s.kc(9,"date"),s.Zb(),s.ac(10,"td"),s.Lc(11),s.kc(12,"date"),s.Zb(),s.ac(13,"td"),s.Lc(14),s.kc(15,"date"),s.Zb(),s.ac(16,"td"),s.ac(17,"span",56),s.Lc(18),s.Zb(),s.Zb(),s.ac(19,"td"),s.ac(20,"a",57),s.Lc(21,"View"),s.Zb(),s.Lc(22," \xa0 "),s.ac(23,"a",58),s.Vb(24,"i",59),s.Zb(),s.Lc(25,"\xa0\xa0 "),s.ac(26,"a",60),s.hc("click",function(){s.Cc(e);const a=t.$implicit;return s.jc().tempId=a.id}),s.Vb(27,"i",61),s.Zb(),s.Zb(),s.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=s.jc();s.Mb("active",a==i.currentIndex),s.Ib(2),s.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),s.Ib(2),s.Mc(e.tourType),s.Ib(2),s.Mc(e.tourDays),s.Ib(2),s.Mc(s.mc(9,11,e.createDate,"yyyy-MM-dd")),s.Ib(3),s.Mc(s.mc(12,14,e.startDate,"yyyy-MM-dd")),s.Ib(3),s.Mc(s.mc(15,17,e.endDate,"yyyy-MM-dd")),s.Ib(4),s.Mc(e.tourApprovalStatus),s.Ib(2),s.rc("routerLink","./view/",e.id,""),s.Ib(3),s.rc("routerLink","./edit/",e.id,"")}}function I(e,t){1&e&&(s.ac(0,"tr"),s.ac(1,"td",62),s.ac(2,"h5",63),s.Lc(3,"No data found"),s.Zb(),s.Zb(),s.Zb())}function M(e,t){if(1&e&&(s.ac(0,"option",64),s.Lc(1),s.Zb()),2&e){const e=t.$implicit;s.pc("value",e),s.Ib(1),s.Nc(" ",e," ")}}const O=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let w=(()=>{class e{constructor(e,t,a,c){this.onTourService=e,this.login=t,this.spinnerService=a,this.toastr=c,this.baseUrl=o.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.loginUser(),this.getSelfListData()}loginUser(){this.user=this.login.getUser(),console.log(this.user)}getSelfListData(){let e=this.baseUrl+"/onTourTnx/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.onTourService.sendGetSelfRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/onTourTnx/delete/"+e;console.log(t),this.spinnerService.show(),this.onTourService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getSelfListData()},e=>{$("#delete_entity").modal("hide"),this.toastr.warning(e.error.message),this.spinnerService.hide()})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.user.id&&(a.hrCrEmp=this.user.id),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getSelfListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getSelfListData()}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(T),s.Ub(L.a),s.Ub(h.c),s.Ub(p.b))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-on-tour"]],decls:112,vars:14,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/sefl-service/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"badge","badge-success"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"List"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"div",10),s.ac(18,"button",11),s.Lc(19,"Excel"),s.Zb(),s.ac(20,"button",11),s.Lc(21,"PDF"),s.Zb(),s.ac(22,"button",11),s.Vb(23,"i",12),s.Lc(24," Print"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(25,"div",13),s.ac(26,"div",14),s.ac(27,"div",15),s.ac(28,"div",16),s.ac(29,"div",17),s.Vb(30,"input",18),s.ac(31,"label",19),s.Lc(32,"Employee Code"),s.Zb(),s.Zb(),s.Zb(),s.ac(33,"div",20),s.ac(34,"div",17),s.ac(35,"div",21),s.Vb(36,"input",22),s.Zb(),s.ac(37,"label",19),s.Lc(38,"From"),s.Zb(),s.Zb(),s.Zb(),s.ac(39,"div",20),s.ac(40,"div",17),s.ac(41,"div",21),s.Vb(42,"input",22),s.Zb(),s.ac(43,"label",19),s.Lc(44,"To"),s.Zb(),s.Zb(),s.Zb(),s.ac(45,"div",20),s.ac(46,"a",23),s.Lc(47," Search "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(48,"div",24),s.ac(49,"div",25),s.ac(50,"div",26),s.ac(51,"div",27),s.ac(52,"div",28),s.ac(53,"a",29),s.Vb(54,"i",30),s.Lc(55," New \xa0\xa0\xa0"),s.Zb(),s.Zb(),s.Zb(),s.ac(56,"div",31),s.ac(57,"div",32),s.ac(58,"div",33),s.ac(59,"div",34),s.ac(60,"span",35),s.Lc(61),s.Zb(),s.Zb(),s.Zb(),s.ac(62,"table",36),s.ac(63,"thead"),s.ac(64,"tr"),s.ac(65,"th"),s.Lc(66,"SL"),s.Zb(),s.ac(67,"th"),s.Lc(68,"Tour Type"),s.Zb(),s.ac(69,"th"),s.Lc(70,"Total Days"),s.Zb(),s.ac(71,"th"),s.Lc(72,"Apply Date"),s.Zb(),s.ac(73,"th"),s.Lc(74,"From Date"),s.Zb(),s.ac(75,"th"),s.Lc(76,"To Date"),s.Zb(),s.ac(77,"th"),s.Lc(78,"Approval Status"),s.Zb(),s.ac(79,"th"),s.Lc(80,"Action"),s.Zb(),s.Zb(),s.Zb(),s.ac(81,"tbody"),s.Jc(82,E,28,20,"tr",37),s.kc(83,"paginate"),s.Jc(84,I,4,0,"tr",38),s.Zb(),s.Zb(),s.ac(85,"div",39),s.ac(86,"div",40),s.Lc(87," Items per Page "),s.ac(88,"select",41),s.hc("change",function(e){return t.handlePageSizeChange(e)}),s.Jc(89,M,2,2,"option",42),s.Zb(),s.Zb(),s.ac(90,"div",43),s.ac(91,"pagination-controls",44),s.hc("pageChange",function(e){return t.handlePageChange(e)}),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(92,"ngx-spinner",45),s.ac(93,"p",46),s.Lc(94," Processing... "),s.Zb(),s.Zb(),s.ac(95,"div",47),s.ac(96,"div",48),s.ac(97,"div",49),s.ac(98,"div",50),s.ac(99,"div",51),s.ac(100,"h3"),s.Lc(101,"Delete Item"),s.Zb(),s.ac(102,"p"),s.Lc(103,"Are you sure want to delete?"),s.Zb(),s.Zb(),s.ac(104,"div",52),s.ac(105,"div",24),s.ac(106,"div",53),s.ac(107,"a",54),s.hc("click",function(){return t.deleteEnityData(t.tempId)}),s.Lc(108,"Delete"),s.Zb(),s.Zb(),s.ac(109,"div",53),s.ac(110,"a",55),s.Lc(111,"Cancel"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(36),s.pc("bsConfig",s.sc(12,O)),s.Ib(6),s.pc("bsConfig",s.sc(13,O)),s.Ib(19),s.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),s.Ib(21),s.pc("ngForOf",s.mc(83,9,t.listData,t.configPgn)),s.Ib(2),s.pc("ngIf",0===t.listData.length),s.Ib(5),s.pc("ngForOf",t.configPgn.pageSizes),s.Ib(3),s.pc("fullScreen",!1))},directives:[c.e,g.b,g.a,i.l,i.m,v.c,h.a,d.s,d.y],pipes:[v.b,i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();var U=a("NllD");function R(e,t){if(1&e&&(s.ac(0,"tr"),s.ac(1,"td"),s.Lc(2),s.Zb(),s.ac(3,"td"),s.Lc(4),s.Zb(),s.ac(5,"td"),s.Lc(6),s.Zb(),s.ac(7,"td"),s.Lc(8),s.Zb(),s.ac(9,"td"),s.Lc(10),s.Zb(),s.ac(11,"td"),s.Lc(12),s.Zb(),s.Zb()),2&e){const e=t.$implicit,a=t.index;s.Ib(2),s.Nc(" ",1+a," "),s.Ib(2),s.Mc(e.approvalStep.approvalGroupName?e.approvalStep.approvalGroupName:"null"),s.Ib(2),s.Mc(e.approvalStepApproverEmp?e.approvalStepApproverEmp.displayName:"null"),s.Ib(2),s.Mc(e.actionStatus?e.actionStatus:"null"),s.Ib(2),s.Mc(e.updateDateTime?e.updateDateTime:"null"),s.Ib(2),s.Mc(e.remarks?e.remarks:"null")}}let A=(()=>{class e{constructor(e,t,a,i,c,r,n){this.route=e,this.spinnerService=t,this.onTourService=a,this.approvalService=i,this.toastr=c,this.formBuilder=r,this.router=n,this.baseUrl=o.a.baseUrl,this.myData={},this.listData=[],this.listData2=[]}ngOnInit(){this.initializeForm(),this.getFormData(),this.getSelfListData(),this.getApprovalStepAction()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],approvalStepAction:[{},d.w.required],remarks:["",d.w.required]})}getFormData(){let e=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(e,{}).subscribe(e=>{this.myData=e,console.log(this.myData),this.spinnerService.hide()},e=>{console.log(e)})}getSelfListData(){let e=this.baseUrl+"/approvalProcTnxHtry/getSelfApprovalProcTnxList/"+this.route.snapshot.params.id,t={};t=this.getUserQueryParams(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData=e,console.log(this.listData)},e=>{console.log(e)})}getApprovalStepAction(){let e=this.baseUrl+"/approvalStepAction/getApprovalStepAction/"+this.route.snapshot.params.id,t={};t=this.getUserQueryParams(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData2=e},e=>{console.log(e)})}tackAction(){if(this.myForm.invalid)return;let e=Object.assign(this.myForm.value,{referenceId:this.route.snapshot.params.id,referenceEntity:"ONTOUR_PROCESS",approvalStepAction:this.get.value?{id:this.get.value}:null});console.log(e);let t={};t=e,this.approvalService.sendPutRequest(this.baseUrl+"/approvalProcTnxHtry/edit",t).subscribe(e=>{console.log(e),this.getFormData(),this.getSelfListData(),this.getApprovalStepAction(),this.resetFormValues()},e=>{console.log(e),this.toastr.info(e.error.message)})}resetFormValues(){this.myForm.reset()}getUserQueryParams(){return{approvalProcess:"ONTOUR_PROCESS"}}get get(){return this.myForm.get("approvalStepAction")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(c.a),s.Ub(h.c),s.Ub(T),s.Ub(U.a),s.Ub(p.b),s.Ub(d.d),s.Ub(c.c))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-view"]],decls:207,vars:29,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTour",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"col-8"],[1,"row","fieldsetBorder","logBox"],[1,"table"],[4,"ngFor","ngForOf"],[1,"col-4"],["novalidate","",3,"formGroup","ngSubmit"],[1,"col-form-label","col-md-4"],[1,"col-md-8"],["formControlName","approvalStepAction","bindLabel","activityStatusTitle","bindValue","id","placeholder","Select","appendTo","body",3,"items"],["formControlName","remarks",1,"form-control","mb-3"],[1,"col-md-9"],[1,"col-md-3"],["type","submit",1,"btn","btn-secondary","btn-sm","mb-2"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/sefl-service/onTour",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self-service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"OnTour"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Show"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"fieldset",16),s.Vb(25,"legend"),s.ac(26,"div",17),s.ac(27,"div",18),s.ac(28,"label",19),s.Lc(29,"Employee"),s.Zb(),s.ac(30,"div",20),s.ac(31,"span"),s.Lc(32,": \xa0"),s.Zb(),s.ac(33,"span"),s.Lc(34),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",18),s.ac(36,"label",19),s.Lc(37,"Mobile No"),s.Zb(),s.ac(38,"div",20),s.ac(39,"span"),s.Lc(40,": \xa0"),s.Zb(),s.ac(41,"span"),s.Lc(42),s.Zb(),s.Zb(),s.Zb(),s.ac(43,"div",18),s.ac(44,"label",19),s.Lc(45,"Responsible Employee"),s.Zb(),s.ac(46,"div",20),s.ac(47,"span"),s.Lc(48,": \xa0"),s.Zb(),s.ac(49,"span"),s.Lc(50),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",18),s.ac(52,"label",19),s.Lc(53,"Mobile No"),s.Zb(),s.ac(54,"div",20),s.ac(55,"span"),s.Lc(56,": \xa0"),s.Zb(),s.ac(57,"span"),s.Lc(58),s.Zb(),s.Zb(),s.Zb(),s.ac(59,"div",18),s.ac(60,"label",19),s.Lc(61,"Tour Type"),s.Zb(),s.ac(62,"div",20),s.ac(63,"span"),s.Lc(64,": \xa0"),s.Zb(),s.ac(65,"span"),s.Lc(66),s.Zb(),s.Zb(),s.Zb(),s.ac(67,"div",18),s.ac(68,"label",19),s.Lc(69,"Start Date"),s.Zb(),s.ac(70,"div",20),s.ac(71,"span"),s.Lc(72,": \xa0"),s.Zb(),s.ac(73,"span"),s.Lc(74),s.kc(75,"date"),s.Zb(),s.Zb(),s.Zb(),s.ac(76,"div",18),s.ac(77,"label",19),s.Lc(78,"End Date"),s.Zb(),s.ac(79,"div",20),s.ac(80,"span"),s.Lc(81,": \xa0"),s.Zb(),s.ac(82,"span"),s.Lc(83),s.kc(84,"date"),s.Zb(),s.Zb(),s.Zb(),s.ac(85,"div",18),s.ac(86,"label",19),s.Lc(87,"Total Days"),s.Zb(),s.ac(88,"div",20),s.ac(89,"span"),s.Lc(90,": \xa0"),s.Zb(),s.ac(91,"span"),s.Lc(92),s.Zb(),s.Zb(),s.Zb(),s.ac(93,"div",18),s.ac(94,"label",19),s.Lc(95,"Tour Location"),s.Zb(),s.ac(96,"div",20),s.ac(97,"span"),s.Lc(98,": \xa0"),s.Zb(),s.ac(99,"span"),s.Lc(100),s.Zb(),s.Zb(),s.Zb(),s.ac(101,"div",18),s.ac(102,"label",19),s.Lc(103,"Couse Of Tour"),s.Zb(),s.ac(104,"div",20),s.ac(105,"span"),s.Lc(106,": \xa0"),s.Zb(),s.ac(107,"span"),s.Lc(108),s.Zb(),s.Zb(),s.Zb(),s.ac(109,"div",18),s.ac(110,"label",19),s.Lc(111,"Remarks"),s.Zb(),s.ac(112,"div",20),s.ac(113,"span"),s.Lc(114,": \xa0"),s.Zb(),s.ac(115,"span"),s.Lc(116),s.Zb(),s.Zb(),s.Zb(),s.ac(117,"div",18),s.ac(118,"label",19),s.Lc(119,"Approval Status"),s.Zb(),s.ac(120,"div",20),s.ac(121,"span"),s.Lc(122,": \xa0"),s.Zb(),s.ac(123,"span"),s.Lc(124),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(125,"div",12),s.ac(126,"div",21),s.ac(127,"fieldset",22),s.ac(128,"legend"),s.Lc(129,"Approval Status"),s.Zb(),s.ac(130,"table",23),s.ac(131,"thead"),s.ac(132,"tr"),s.ac(133,"th"),s.Lc(134,"S/L"),s.Zb(),s.ac(135,"th"),s.Lc(136,"Approval Step"),s.Zb(),s.ac(137,"th"),s.Lc(138,"Sign By"),s.Zb(),s.ac(139,"th"),s.Lc(140,"Action"),s.Zb(),s.ac(141,"th"),s.Lc(142,"Date"),s.Zb(),s.ac(143,"th"),s.Lc(144,"Remarks"),s.Zb(),s.Zb(),s.Zb(),s.ac(145,"tbody"),s.Jc(146,R,13,6,"tr",24),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(147,"div",25),s.ac(148,"form",26),s.hc("ngSubmit",function(){return t.tackAction()}),s.ac(149,"fieldset",22),s.ac(150,"legend"),s.Lc(151,"Take Action"),s.Zb(),s.ac(152,"label",27),s.Lc(153,"Status"),s.Zb(),s.ac(154,"div",28),s.Vb(155,"ng-select",29),s.Zb(),s.Vb(156,"br"),s.Vb(157,"br"),s.ac(158,"label",27),s.Lc(159,"Remarks"),s.Zb(),s.ac(160,"div",28),s.Vb(161,"textarea",30),s.Zb(),s.Vb(162,"div",31),s.ac(163,"div",32),s.ac(164,"button",33),s.Lc(165," Save "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(166,"fieldset",22),s.ac(167,"legend"),s.Lc(168,"System Log Information"),s.Zb(),s.ac(169,"div",34),s.ac(170,"label",35),s.Lc(171,"ID"),s.Zb(),s.ac(172,"div",36),s.ac(173,"span"),s.Lc(174),s.Zb(),s.Zb(),s.Zb(),s.ac(175,"div",34),s.ac(176,"label",35),s.Lc(177,"Creation Time"),s.Zb(),s.ac(178,"div",36),s.ac(179,"span"),s.Lc(180),s.Zb(),s.Zb(),s.Zb(),s.ac(181,"div",34),s.ac(182,"label",35),s.Lc(183,"Creation User"),s.Zb(),s.ac(184,"div",36),s.ac(185,"span"),s.Lc(186),s.Zb(),s.Zb(),s.Zb(),s.ac(187,"div",34),s.ac(188,"label",35),s.Lc(189,"Last Update Time"),s.Zb(),s.ac(190,"div",36),s.ac(191,"span"),s.Lc(192),s.Zb(),s.Zb(),s.Zb(),s.ac(193,"div",34),s.ac(194,"label",35),s.Lc(195,"Last Update User"),s.Zb(),s.ac(196,"div",36),s.ac(197,"span"),s.Lc(198),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(199,"div",37),s.ac(200,"a",38),s.Vb(201,"i",11),s.Lc(202," Close"),s.Zb(),s.Lc(203," \xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(204,"ngx-spinner",39),s.ac(205,"p",40),s.Lc(206," Processing... "),s.Zb(),s.Zb()),2&e&&(s.Ib(34),s.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),s.Ib(8),s.Nc("",t.myData.hrCrEmp.mobCode," "),s.Ib(8),s.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),s.Ib(8),s.Nc("",t.myData.hrCrEmpResponsible.mobCode," "),s.Ib(8),s.Mc(t.myData.tourType),s.Ib(8),s.Mc(s.mc(75,23,t.myData.startDate,"yyyy-MM-dd")),s.Ib(9),s.Mc(s.mc(84,26,t.myData.endDate,"yyyy-MM-dd")),s.Ib(9),s.Mc(t.myData.tourDays),s.Ib(8),s.Mc(t.myData.addressDuringTour),s.Ib(8),s.Mc(t.myData.reasonForTour),s.Ib(8),s.Mc(t.myData.remarks),s.Ib(8),s.Mc(t.myData.tourApprovalStatus),s.Ib(22),s.pc("ngForOf",t.listData),s.Ib(2),s.pc("formGroup",t.myForm),s.Ib(7),s.pc("items",t.listData2),s.Ib(19),s.Mc(t.myData.id),s.Ib(6),s.Mc(t.myData.createDate),s.Ib(6),s.Mc(t.myData.createdByHrCrEmp.user.username),s.Ib(6),s.Mc(t.myData.updateDateTime),s.Ib(6),s.Mc(t.myData.lastUpdateUser),s.Ib(6),s.pc("fullScreen",!1))},directives:[c.e,i.l,d.x,d.p,d.h,m.a,d.o,d.f,d.b,h.a],pipes:[i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),e})(),q=(()=>{class e{constructor(e,t,a,i,c,r,n,s){this.formBuilder=e,this.datePipe=t,this.login=a,this.route=i,this.router=c,this.onTourService=r,this.toastr=n,this.commonService=s,this.baseUrl=o.a.baseUrl,this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm()}initializeForm(){this.myForm=this.formBuilder.group({hrCrEmp:[{},[d.w.required]],contactNo:["",[d.w.required]],hrCrEmpResponsible:[{},[d.w.required]],tourType:["",[d.w.required]],startDate:["",[d.w.required]],endDate:["",[d.w.required]],addressDuringTour:["",[d.w.required]],reasonForTour:["",[d.w.required]],remarks:[""]})}myFormSubmit(){if(this.myForm.invalid)return void this.toastr.info("Please insert valid data");if(this.checkSomeCondition())return;const e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null});let t=this.baseUrl+"/onTourTnx/save",a={};a=e,console.log(a),a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.onTourService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.router.navigate(["/sefl-service/onTourHrAdmin"],{relativeTo:this.route})},e=>{console.log(e)})}checkSomeCondition(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}contactFind(e){alert(e)}resetFormValues(){this.myForm.reset()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.listData=this.configDDL.append?this.configDDL.listData.concat(e.objectList):e.objectList,this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(i.e),s.Ub(L.a),s.Ub(c.a),s.Ub(c.c),s.Ub(T),s.Ub(p.b),s.Ub(r.a))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-create"]],decls:90,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["oninput","javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);","type","number","maxlength","11","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour (Hr Admin)"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour (Hr Admin)"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Create"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"form",16),s.hc("ngSubmit",function(){return t.myFormSubmit()}),s.ac(25,"div",17),s.ac(26,"label",18),s.Lc(27,"Employee "),s.Zb(),s.ac(28,"div",19),s.ac(29,"ng-select",20),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(30,"div",17),s.ac(31,"label",18),s.Lc(32,"Contact No"),s.Zb(),s.ac(33,"div",19),s.Vb(34,"input",21),s.Zb(),s.Zb(),s.ac(35,"div",17),s.ac(36,"label",18),s.Lc(37,"Responsible Employee "),s.Zb(),s.ac(38,"div",19),s.ac(39,"ng-select",22),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(40,"div",17),s.ac(41,"label",18),s.Lc(42,"Tour Type"),s.Zb(),s.ac(43,"div",19),s.ac(44,"select",23),s.ac(45,"option",24),s.Lc(46,"Select"),s.Zb(),s.ac(47,"option",25),s.Lc(48,"Local"),s.Zb(),s.ac(49,"option",26),s.Lc(50,"Global"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",17),s.ac(52,"label",18),s.Lc(53,"Start Date"),s.Zb(),s.ac(54,"div",19),s.ac(55,"div",27),s.Vb(56,"input",28),s.Zb(),s.Zb(),s.Zb(),s.ac(57,"div",17),s.ac(58,"label",18),s.Lc(59,"End Date"),s.Zb(),s.ac(60,"div",19),s.ac(61,"div",27),s.Vb(62,"input",29),s.Zb(),s.Zb(),s.Zb(),s.ac(63,"div",17),s.ac(64,"label",18),s.Lc(65,"Address During Tour"),s.Zb(),s.ac(66,"div",19),s.Vb(67,"textarea",30),s.Zb(),s.Zb(),s.ac(68,"div",17),s.ac(69,"label",18),s.Lc(70,"Reason For Tour"),s.Zb(),s.ac(71,"div",19),s.Vb(72,"textarea",31),s.Zb(),s.Zb(),s.ac(73,"div",17),s.ac(74,"label",18),s.Lc(75,"Remarks"),s.Zb(),s.ac(76,"div",19),s.Vb(77,"textarea",32),s.Zb(),s.Zb(),s.ac(78,"div",33),s.ac(79,"a",34),s.Vb(80,"i",11),s.Lc(81," Cancel"),s.Zb(),s.Lc(82," \xa0 \xa0 "),s.ac(83,"button",35),s.hc("click",function(){return t.resetFormValues()}),s.Vb(84,"i",36),s.Lc(85," Reset "),s.Zb(),s.Lc(86," \xa0 \xa0 "),s.ac(87,"button",37),s.Vb(88,"i",38),s.Lc(89," Save \xa0\xa0\xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(24),s.pc("formGroup",t.myForm),s.Ib(5),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),s.Ib(10),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[c.e,d.x,d.p,d.h,m.a,d.o,d.f,d.t,d.b,d.k,d.v,d.s,d.y,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})(),V=(()=>{class e{constructor(e,t,a,i,c,r,n,s){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.onTourService=c,this.toastr=r,this.commonService=n,this.spinnerService=s,this.baseUrl=o.a.baseUrl,this.myFormData={},this._initConfigDDL(),this._customInitLoadData()}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",[d.w.required]],contactNo:["",[d.w.required]],hrCrEmpResponsible:["",[d.w.required]],tourType:["",[d.w.required]],startDate:["",[d.w.required]],endDate:["",[d.w.required]],addressDuringTour:["",[d.w.required]],reasonForTour:["",[d.w.required]],remarks:[""]})}getFormData(){let e=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide(),this.configDDL.listData=[{ddlCode:e.hrCrEmp.id,ddlDescription:e.hrCrEmp.loginCode+"-"+e.hrCrEmp.displayName}],this.myFormData.hrCrEmp=e.hrCrEmp.id,this.configDDL.listData2=[{ddlCode:e.hrCrEmpResponsible.id,ddlDescription:e.hrCrEmpResponsible.loginCode+"-"+e.hrCrEmpResponsible.displayName}],this.myFormData.hrCrEmpResponsible=e.hrCrEmpResponsible.id,this.myFormData.startDate=this.datePipe.transform(e.startDate,"MM-dd-yyyy").toString().slice(0,10),this.myFormData.endDate=this.datePipe.transform(e.endDate,"MM-dd-yyyy").toString().slice(0,10),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){if(this.myForm.invalid)return void this.toastr.info("Please insert valid data");if(this.checkSomeCondition())return;const e=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null});let t=this.baseUrl+"/onTourTnx/save";console.log(t);let a={};a=e,a.startDate=a.startDate?this.datePipe.transform(a.startDate,"yyyy-MM-dd").toString().slice(0,10):null,a.endDate=a.endDate?this.datePipe.transform(a.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.onTourService.sendPostRequest(t,a).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/sefl-service/onTour"],{relativeTo:this.route})},e=>{console.log(e),this.toastr.warning(e.error.message),this.spinnerService.hide()})}checkSomeCondition(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}resetFormValues(){this.getFormData()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}_customInitLoadData(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?(this.configDDL.listData=this.configDDL.listData.concat(e.objectList),this.configDDL.listData2=this.configDDL.listData2.concat(e.objectList)):(this.configDDL.listData=e.objectList,this.configDDL.listData2=e.objectList),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getHrCrResponsibleEmp(){return this.myForm.get("hrCrEmpResponsible")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(d.d),s.Ub(i.e),s.Ub(c.a),s.Ub(c.c),s.Ub(T),s.Ub(p.b),s.Ub(r.a),s.Ub(h.c))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-edit"]],decls:90,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour (Hr Admin)"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour (Hr Admin)"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Edit"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"form",16),s.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),s.ac(25,"div",17),s.ac(26,"label",18),s.Lc(27,"Employee "),s.Zb(),s.ac(28,"div",19),s.ac(29,"ng-select",20),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(30,"div",17),s.ac(31,"label",18),s.Lc(32,"Contact No"),s.Zb(),s.ac(33,"div",19),s.Vb(34,"input",21),s.Zb(),s.Zb(),s.ac(35,"div",17),s.ac(36,"label",18),s.Lc(37,"Responsible Employee "),s.Zb(),s.ac(38,"div",19),s.ac(39,"ng-select",22),s.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),s.Zb(),s.Zb(),s.Zb(),s.ac(40,"div",17),s.ac(41,"label",18),s.Lc(42,"Tour Type"),s.Zb(),s.ac(43,"div",19),s.ac(44,"select",23),s.ac(45,"option",24),s.Lc(46,"Select"),s.Zb(),s.ac(47,"option",25),s.Lc(48,"Local"),s.Zb(),s.ac(49,"option",26),s.Lc(50,"Global"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",17),s.ac(52,"label",18),s.Lc(53,"Start Date"),s.Zb(),s.ac(54,"div",19),s.ac(55,"div",27),s.Vb(56,"input",28),s.Zb(),s.Zb(),s.Zb(),s.ac(57,"div",17),s.ac(58,"label",18),s.Lc(59,"End Date"),s.Zb(),s.ac(60,"div",19),s.ac(61,"div",27),s.Vb(62,"input",29),s.Zb(),s.Zb(),s.Zb(),s.ac(63,"div",17),s.ac(64,"label",18),s.Lc(65,"Address During Tour"),s.Zb(),s.ac(66,"div",19),s.Vb(67,"textarea",30),s.Zb(),s.Zb(),s.ac(68,"div",17),s.ac(69,"label",18),s.Lc(70,"Reason For Tour"),s.Zb(),s.ac(71,"div",19),s.Vb(72,"textarea",31),s.Zb(),s.Zb(),s.ac(73,"div",17),s.ac(74,"label",18),s.Lc(75,"Remarks"),s.Zb(),s.ac(76,"div",19),s.Vb(77,"textarea",32),s.Zb(),s.Zb(),s.ac(78,"div",33),s.ac(79,"a",34),s.Vb(80,"i",11),s.Lc(81," Cancel"),s.Zb(),s.Lc(82," \xa0 \xa0 "),s.ac(83,"button",35),s.hc("click",function(){return t.resetFormValues()}),s.Vb(84,"i",36),s.Lc(85," Reset "),s.Zb(),s.Lc(86," \xa0 \xa0 "),s.ac(87,"button",37),s.Vb(88,"i",38),s.Lc(89," Save \xa0\xa0\xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(24),s.pc("formGroup",t.myForm),s.Ib(5),s.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),s.Ib(10),s.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[c.e,d.x,d.p,d.h,m.a,d.o,d.f,d.t,d.b,d.v,d.s,d.y,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),e})();function _(e,t){if(1&e){const e=s.bc();s.ac(0,"tr"),s.ac(1,"td"),s.Lc(2),s.Zb(),s.ac(3,"td"),s.Lc(4),s.Zb(),s.ac(5,"td"),s.Lc(6),s.Zb(),s.ac(7,"td"),s.Lc(8),s.Zb(),s.ac(9,"td"),s.Lc(10),s.kc(11,"date"),s.Zb(),s.ac(12,"td"),s.Lc(13),s.kc(14,"date"),s.Zb(),s.ac(15,"td"),s.Lc(16),s.kc(17,"date"),s.Zb(),s.ac(18,"td"),s.ac(19,"span",56),s.Lc(20),s.Zb(),s.Zb(),s.ac(21,"td"),s.ac(22,"a",57),s.Lc(23,"View"),s.Zb(),s.Lc(24," \xa0 "),s.ac(25,"a",58),s.Vb(26,"i",59),s.Zb(),s.Lc(27,"\xa0\xa0 "),s.ac(28,"a",60),s.hc("click",function(){s.Cc(e);const a=t.$implicit;return s.jc().tempId=a.id}),s.Vb(29,"i",61),s.Zb(),s.Zb(),s.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=s.jc();s.Mb("active",a==i.currentIndex),s.Ib(2),s.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),s.Ib(2),s.Mc(e.hrCrEmp.displayName),s.Ib(2),s.Mc(e.tourType),s.Ib(2),s.Mc(e.tourDays),s.Ib(2),s.Mc(s.mc(11,12,e.createDate,"yyyy-MM-dd")),s.Ib(3),s.Mc(s.mc(14,15,e.startDate,"yyyy-MM-dd")),s.Ib(3),s.Mc(s.mc(17,18,e.endDate,"yyyy-MM-dd")),s.Ib(4),s.Mc(e.tourApprovalStatus),s.Ib(2),s.rc("routerLink","./view/",e.id,""),s.Ib(3),s.rc("routerLink","./edit/",e.id,"")}}function z(e,t){1&e&&(s.ac(0,"tr"),s.ac(1,"td",62),s.ac(2,"h5",63),s.Lc(3,"No data found"),s.Zb(),s.Zb(),s.Zb())}function G(e,t){if(1&e&&(s.ac(0,"option",64),s.Lc(1),s.Zb()),2&e){const e=t.$implicit;s.pc("value",e),s.Ib(1),s.Nc(" ",e," ")}}const H=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}};let B=(()=>{class e{constructor(e,t,a,c){this.onTourService=e,this.login=t,this.spinnerService=a,this.toastr=c,this.baseUrl=o.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.loginUser(),this.getSelfListData()}loginUser(){this.user=this.login.getUser(),console.log(this.user)}getSelfListData(){let e=this.baseUrl+"/onTourTnx/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.onTourService.sendGetSelfRequest(e,t).subscribe(e=>{this.listData=e.objectList,console.log(this.listData),this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/onTourTnx/delete/"+e;console.log(t),this.spinnerService.show(),this.onTourService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getSelfListData()},e=>{$("#delete_entity").modal("hide"),this.toastr.warning(e.error.message),this.spinnerService.hide()})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getSelfListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getSelfListData()}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(T),s.Ub(L.a),s.Ub(h.c),s.Ub(p.b))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-on-tour"]],decls:114,vars:14,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/sefl-service/onTourHrAdmin/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"badge","badge-success"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour (Hr Admin)"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self Service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour (Hr Admin)"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"List"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"div",10),s.ac(18,"button",11),s.Lc(19,"Excel"),s.Zb(),s.ac(20,"button",11),s.Lc(21,"PDF"),s.Zb(),s.ac(22,"button",11),s.Vb(23,"i",12),s.Lc(24," Print"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(25,"div",13),s.ac(26,"div",14),s.ac(27,"div",15),s.ac(28,"div",16),s.ac(29,"div",17),s.Vb(30,"input",18),s.ac(31,"label",19),s.Lc(32,"Employee Code"),s.Zb(),s.Zb(),s.Zb(),s.ac(33,"div",20),s.ac(34,"div",17),s.ac(35,"div",21),s.Vb(36,"input",22),s.Zb(),s.ac(37,"label",19),s.Lc(38,"From"),s.Zb(),s.Zb(),s.Zb(),s.ac(39,"div",20),s.ac(40,"div",17),s.ac(41,"div",21),s.Vb(42,"input",22),s.Zb(),s.ac(43,"label",19),s.Lc(44,"To"),s.Zb(),s.Zb(),s.Zb(),s.ac(45,"div",20),s.ac(46,"a",23),s.Lc(47," Search "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(48,"div",24),s.ac(49,"div",25),s.ac(50,"div",26),s.ac(51,"div",27),s.ac(52,"div",28),s.ac(53,"a",29),s.Vb(54,"i",30),s.Lc(55," New \xa0\xa0\xa0"),s.Zb(),s.Zb(),s.Zb(),s.ac(56,"div",31),s.ac(57,"div",32),s.ac(58,"div",33),s.ac(59,"div",34),s.ac(60,"span",35),s.Lc(61),s.Zb(),s.Zb(),s.Zb(),s.ac(62,"table",36),s.ac(63,"thead"),s.ac(64,"tr"),s.ac(65,"th"),s.Lc(66,"SL"),s.Zb(),s.ac(67,"th"),s.Lc(68,"Emp"),s.Zb(),s.ac(69,"th"),s.Lc(70,"Tour Type"),s.Zb(),s.ac(71,"th"),s.Lc(72,"Total Days"),s.Zb(),s.ac(73,"th"),s.Lc(74,"Apply Date"),s.Zb(),s.ac(75,"th"),s.Lc(76,"From Date"),s.Zb(),s.ac(77,"th"),s.Lc(78,"To Date"),s.Zb(),s.ac(79,"th"),s.Lc(80,"Approval Status"),s.Zb(),s.ac(81,"th"),s.Lc(82,"Action"),s.Zb(),s.Zb(),s.Zb(),s.ac(83,"tbody"),s.Jc(84,_,30,21,"tr",37),s.kc(85,"paginate"),s.Jc(86,z,4,0,"tr",38),s.Zb(),s.Zb(),s.ac(87,"div",39),s.ac(88,"div",40),s.Lc(89," Items per Page "),s.ac(90,"select",41),s.hc("change",function(e){return t.handlePageSizeChange(e)}),s.Jc(91,G,2,2,"option",42),s.Zb(),s.Zb(),s.ac(92,"div",43),s.ac(93,"pagination-controls",44),s.hc("pageChange",function(e){return t.handlePageChange(e)}),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(94,"ngx-spinner",45),s.ac(95,"p",46),s.Lc(96," Processing... "),s.Zb(),s.Zb(),s.ac(97,"div",47),s.ac(98,"div",48),s.ac(99,"div",49),s.ac(100,"div",50),s.ac(101,"div",51),s.ac(102,"h3"),s.Lc(103,"Delete Item"),s.Zb(),s.ac(104,"p"),s.Lc(105,"Are you sure want to delete?"),s.Zb(),s.Zb(),s.ac(106,"div",52),s.ac(107,"div",24),s.ac(108,"div",53),s.ac(109,"a",54),s.hc("click",function(){return t.deleteEnityData(t.tempId)}),s.Lc(110,"Delete"),s.Zb(),s.Zb(),s.ac(111,"div",53),s.ac(112,"a",55),s.Lc(113,"Cancel"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(36),s.pc("bsConfig",s.sc(12,H)),s.Ib(6),s.pc("bsConfig",s.sc(13,H)),s.Ib(19),s.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),s.Ib(23),s.pc("ngForOf",s.mc(85,9,t.listData,t.configPgn)),s.Ib(2),s.pc("ngIf",0===t.listData.length),s.Ib(5),s.pc("ngForOf",t.configPgn.pageSizes),s.Ib(3),s.pc("fullScreen",!1))},directives:[c.e,g.b,g.a,i.l,i.m,v.c,h.a,d.s,d.y],pipes:[v.b,i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();function j(e,t){if(1&e&&(s.ac(0,"tr"),s.ac(1,"td"),s.Lc(2),s.Zb(),s.ac(3,"td"),s.Lc(4),s.Zb(),s.ac(5,"td"),s.Lc(6),s.Zb(),s.ac(7,"td"),s.Lc(8),s.Zb(),s.ac(9,"td"),s.Lc(10),s.Zb(),s.ac(11,"td"),s.Lc(12),s.Zb(),s.Zb()),2&e){const e=t.$implicit,a=t.index;s.Ib(2),s.Nc(" ",1+a," "),s.Ib(2),s.Mc(e.approvalStep.approvalGroupName?e.approvalStep.approvalGroupName:"null"),s.Ib(2),s.Mc(e.approvalStepApproverEmp?e.approvalStepApproverEmp.displayName:"null"),s.Ib(2),s.Mc(e.actionStatus?e.actionStatus:"null"),s.Ib(2),s.Mc(e.updateDateTime?e.updateDateTime:"null"),s.Ib(2),s.Mc(e.remarks?e.remarks:"null")}}let Q=(()=>{class e{constructor(e,t,a,i,c,r,n){this.route=e,this.spinnerService=t,this.onTourService=a,this.approvalService=i,this.toastr=c,this.formBuilder=r,this.router=n,this.baseUrl=o.a.baseUrl,this.myData={},this.listData=[],this.listData2=[]}ngOnInit(){this.initializeForm(),this.getFormData(),this.getSelfListData(),this.getApprovalStepAction()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],approvalStepAction:["",d.w.required],remarks:["",d.w.required]})}getFormData(){let e=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(e,{}).subscribe(e=>{this.myData=e,console.log(this.myData),this.spinnerService.hide()},e=>{console.log(e)})}getSelfListData(){let e=this.baseUrl+"/approvalProcTnxHtry/getSelfApprovalProcTnxList/"+this.route.snapshot.params.id,t={};t=this.getUserQueryParams(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData=e},e=>{console.log(e)})}getApprovalStepAction(){let e=this.baseUrl+"/approvalStepAction/getApprovalStepAction/"+this.route.snapshot.params.id,t={};t=this.getUserQueryParams(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData2=e},e=>{console.log(e)})}tackAction(){if(this.myForm.invalid)return;let e=Object.assign(this.myForm.value,{referenceId:this.route.snapshot.params.id,referenceEntity:"ONTOUR_PROCESS",approvalStepAction:this.get.value?{id:this.get.value}:null});console.log(e);let t={};t=e,this.approvalService.sendPutRequest(this.baseUrl+"/approvalProcTnxHtry/edit",t).subscribe(e=>{console.log(e),this.getFormData(),this.getSelfListData(),this.getApprovalStepAction(),this.resetFormValues()},e=>{console.log(e),this.toastr.info(e.error.message)})}resetFormValues(){this.myForm.reset()}getUserQueryParams(){return{approvalProcess:"ONTOUR_PROCESS"}}get get(){return this.myForm.get("approvalStepAction")}}return e.\u0275fac=function(t){return new(t||e)(s.Ub(c.a),s.Ub(h.c),s.Ub(T),s.Ub(U.a),s.Ub(p.b),s.Ub(d.d),s.Ub(c.c))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-view"]],decls:207,vars:29,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"col-8"],[1,"row","fieldsetBorder","logBox"],[1,"table"],[4,"ngFor","ngForOf"],[1,"col-4"],["novalidate","",3,"formGroup","ngSubmit"],[1,"col-form-label","col-md-4"],[1,"col-md-8"],["formControlName","approvalStepAction","bindLabel","activityStatusTitle","bindValue","id","placeholder","Select","appendTo","body",3,"items"],["formControlName","remarks",1,"form-control","mb-3"],[1,"col-md-9"],[1,"col-md-3"],["type","submit",1,"btn","btn-secondary","btn-sm","mb-2"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"h3",4),s.Lc(5,"On Tour (Hr Admin)"),s.Zb(),s.ac(6,"ul",5),s.ac(7,"li",6),s.ac(8,"a",7),s.Lc(9,"Home"),s.Zb(),s.Zb(),s.ac(10,"li",8),s.Lc(11,"Self-service"),s.Zb(),s.ac(12,"li",8),s.Lc(13,"On Tour (Hr Admin)"),s.Zb(),s.ac(14,"li",8),s.Lc(15,"Show"),s.Zb(),s.Zb(),s.Zb(),s.ac(16,"div",9),s.ac(17,"a",10),s.Vb(18,"i",11),s.Lc(19," Back To List"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(20,"div",12),s.ac(21,"div",13),s.ac(22,"div",14),s.ac(23,"div",15),s.ac(24,"fieldset",16),s.Vb(25,"legend"),s.ac(26,"div",17),s.ac(27,"div",18),s.ac(28,"label",19),s.Lc(29,"Employee"),s.Zb(),s.ac(30,"div",20),s.ac(31,"span"),s.Lc(32,": \xa0"),s.Zb(),s.ac(33,"span"),s.Lc(34),s.Zb(),s.Zb(),s.Zb(),s.ac(35,"div",18),s.ac(36,"label",19),s.Lc(37,"Mobile No"),s.Zb(),s.ac(38,"div",20),s.ac(39,"span"),s.Lc(40,": \xa0"),s.Zb(),s.ac(41,"span"),s.Lc(42),s.Zb(),s.Zb(),s.Zb(),s.ac(43,"div",18),s.ac(44,"label",19),s.Lc(45,"Responsible Employee"),s.Zb(),s.ac(46,"div",20),s.ac(47,"span"),s.Lc(48,": \xa0"),s.Zb(),s.ac(49,"span"),s.Lc(50),s.Zb(),s.Zb(),s.Zb(),s.ac(51,"div",18),s.ac(52,"label",19),s.Lc(53,"Mobile No"),s.Zb(),s.ac(54,"div",20),s.ac(55,"span"),s.Lc(56,": \xa0"),s.Zb(),s.ac(57,"span"),s.Lc(58),s.Zb(),s.Zb(),s.Zb(),s.ac(59,"div",18),s.ac(60,"label",19),s.Lc(61,"Tour Type"),s.Zb(),s.ac(62,"div",20),s.ac(63,"span"),s.Lc(64,": \xa0"),s.Zb(),s.ac(65,"span"),s.Lc(66),s.Zb(),s.Zb(),s.Zb(),s.ac(67,"div",18),s.ac(68,"label",19),s.Lc(69,"Start Date"),s.Zb(),s.ac(70,"div",20),s.ac(71,"span"),s.Lc(72,": \xa0"),s.Zb(),s.ac(73,"span"),s.Lc(74),s.kc(75,"date"),s.Zb(),s.Zb(),s.Zb(),s.ac(76,"div",18),s.ac(77,"label",19),s.Lc(78,"End Date"),s.Zb(),s.ac(79,"div",20),s.ac(80,"span"),s.Lc(81,": \xa0"),s.Zb(),s.ac(82,"span"),s.Lc(83),s.kc(84,"date"),s.Zb(),s.Zb(),s.Zb(),s.ac(85,"div",18),s.ac(86,"label",19),s.Lc(87,"Total Days"),s.Zb(),s.ac(88,"div",20),s.ac(89,"span"),s.Lc(90,": \xa0"),s.Zb(),s.ac(91,"span"),s.Lc(92),s.Zb(),s.Zb(),s.Zb(),s.ac(93,"div",18),s.ac(94,"label",19),s.Lc(95,"Tour Location"),s.Zb(),s.ac(96,"div",20),s.ac(97,"span"),s.Lc(98,": \xa0"),s.Zb(),s.ac(99,"span"),s.Lc(100),s.Zb(),s.Zb(),s.Zb(),s.ac(101,"div",18),s.ac(102,"label",19),s.Lc(103,"Couse Of Tour"),s.Zb(),s.ac(104,"div",20),s.ac(105,"span"),s.Lc(106,": \xa0"),s.Zb(),s.ac(107,"span"),s.Lc(108),s.Zb(),s.Zb(),s.Zb(),s.ac(109,"div",18),s.ac(110,"label",19),s.Lc(111,"Remarks"),s.Zb(),s.ac(112,"div",20),s.ac(113,"span"),s.Lc(114,": \xa0"),s.Zb(),s.ac(115,"span"),s.Lc(116),s.Zb(),s.Zb(),s.Zb(),s.ac(117,"div",18),s.ac(118,"label",19),s.Lc(119,"Approval Status"),s.Zb(),s.ac(120,"div",20),s.ac(121,"span"),s.Lc(122,": \xa0"),s.Zb(),s.ac(123,"span"),s.Lc(124),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(125,"div",12),s.ac(126,"div",21),s.ac(127,"fieldset",22),s.ac(128,"legend"),s.Lc(129,"Approval Status"),s.Zb(),s.ac(130,"table",23),s.ac(131,"thead"),s.ac(132,"tr"),s.ac(133,"th"),s.Lc(134,"S/L"),s.Zb(),s.ac(135,"th"),s.Lc(136,"Approval Step"),s.Zb(),s.ac(137,"th"),s.Lc(138,"Sign By"),s.Zb(),s.ac(139,"th"),s.Lc(140,"Action"),s.Zb(),s.ac(141,"th"),s.Lc(142,"Date"),s.Zb(),s.ac(143,"th"),s.Lc(144,"Remarks"),s.Zb(),s.Zb(),s.Zb(),s.ac(145,"tbody"),s.Jc(146,j,13,6,"tr",24),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(147,"div",25),s.ac(148,"form",26),s.hc("ngSubmit",function(){return t.tackAction()}),s.ac(149,"fieldset",22),s.ac(150,"legend"),s.Lc(151,"Take Action"),s.Zb(),s.ac(152,"label",27),s.Lc(153,"Status"),s.Zb(),s.ac(154,"div",28),s.Vb(155,"ng-select",29),s.Zb(),s.Vb(156,"br"),s.Vb(157,"br"),s.ac(158,"label",27),s.Lc(159,"Remarks"),s.Zb(),s.ac(160,"div",28),s.Vb(161,"textarea",30),s.Zb(),s.Vb(162,"div",31),s.ac(163,"div",32),s.ac(164,"button",33),s.Lc(165," Save "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(166,"fieldset",22),s.ac(167,"legend"),s.Lc(168,"System Log Information"),s.Zb(),s.ac(169,"div",34),s.ac(170,"label",35),s.Lc(171,"ID"),s.Zb(),s.ac(172,"div",36),s.ac(173,"span"),s.Lc(174),s.Zb(),s.Zb(),s.Zb(),s.ac(175,"div",34),s.ac(176,"label",35),s.Lc(177,"Creation Time"),s.Zb(),s.ac(178,"div",36),s.ac(179,"span"),s.Lc(180),s.Zb(),s.Zb(),s.Zb(),s.ac(181,"div",34),s.ac(182,"label",35),s.Lc(183,"Creation User"),s.Zb(),s.ac(184,"div",36),s.ac(185,"span"),s.Lc(186),s.Zb(),s.Zb(),s.Zb(),s.ac(187,"div",34),s.ac(188,"label",35),s.Lc(189,"Last Update Time"),s.Zb(),s.ac(190,"div",36),s.ac(191,"span"),s.Lc(192),s.Zb(),s.Zb(),s.Zb(),s.ac(193,"div",34),s.ac(194,"label",35),s.Lc(195,"Last Update User"),s.Zb(),s.ac(196,"div",36),s.ac(197,"span"),s.Lc(198),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(199,"div",37),s.ac(200,"a",38),s.Vb(201,"i",11),s.Lc(202," Close"),s.Zb(),s.Lc(203," \xa0 "),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(204,"ngx-spinner",39),s.ac(205,"p",40),s.Lc(206," Processing... "),s.Zb(),s.Zb()),2&e&&(s.Ib(34),s.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),s.Ib(8),s.Nc("",t.myData.hrCrEmp.mobCode," "),s.Ib(8),s.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),s.Ib(8),s.Nc("",t.myData.hrCrEmpResponsible.mobCode," "),s.Ib(8),s.Mc(t.myData.tourType),s.Ib(8),s.Mc(s.mc(75,23,t.myData.startDate,"yyyy-MM-dd")),s.Ib(9),s.Mc(s.mc(84,26,t.myData.endDate,"yyyy-MM-dd")),s.Ib(9),s.Mc(t.myData.tourDays),s.Ib(8),s.Mc(t.myData.addressDuringTour),s.Ib(8),s.Mc(t.myData.reasonForTour),s.Ib(8),s.Mc(t.myData.remarks),s.Ib(8),s.Mc(t.myData.tourApprovalStatus),s.Ib(22),s.pc("ngForOf",t.listData),s.Ib(2),s.pc("formGroup",t.myForm),s.Ib(7),s.pc("items",t.listData2),s.Ib(19),s.Mc(t.myData.id),s.Ib(6),s.Mc(t.myData.createDate),s.Ib(6),s.Mc(t.myData.createdByHrCrEmp.user.username),s.Ib(6),s.Mc(t.myData.updateDateTime),s.Ib(6),s.Mc(t.myData.lastUpdateUser),s.Ib(6),s.pc("fullScreen",!1))},directives:[c.e,i.l,d.x,d.p,d.h,m.a,d.o,d.f,d.b,h.a],pipes:[i.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),e})();const J=function(e){return{height:e}},Y=[{path:"",component:(()=>{let e=class{constructor(e){this.ngZone=e,window.onresize=e=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){}onResize(e){this.innerHeight=e.target.innerHeight+"px"}};return e.\u0275fac=function(t){return new(t||e)(s.Ub(s.G))},e.\u0275cmp=s.Ob({type:e,selectors:[["app-self-service"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.hc("resized",function(e){return t.onResize(e)}),s.Vb(1,"router-outlet"),s.Zb()),2&e&&s.pc("ngStyle",s.tc(1,J,t.innerHeight))},directives:[i.n,c.g],styles:[""]}),e})(),children:[{path:"onTour",component:w},{path:"create",component:k},{path:"onTour/view/:id",component:A},{path:"onTour/edit/:id",component:N},{path:"employeeleaves",component:x},{path:"employeeleaves/create",component:u},{path:"employeeleaves/edit/:id",component:D},{path:"employeeleaves/view/:id",component:F},{path:"onTourHrAdmin",component:B},{path:"onTourHrAdmin/create",component:q},{path:"onTourHrAdmin/view/:id",component:Q},{path:"onTourHrAdmin/edit/:id",component:V}]}];let W=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=s.Sb({type:e}),e.\u0275inj=s.Rb({imports:[[c.f.forChild(Y)],c.f]}),e})();var K=a("iHf9"),X=a("0jEk");let ee=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=s.Sb({type:e}),e.\u0275inj=s.Rb({imports:[[i.c,W,g.c.forRoot(),X.a,d.u,K.b,v.a,h.b,m.b]]}),e})()},rmPI:function(e,t,a){"use strict";t.a="http://localhost:9090/hrms_api"}}]);