!function(){function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,a,i){return a&&t(e.prototype,a),i&&t(e,i),e}(window.webpackJsonp=window.webpackJsonp||[]).push([[16],{DRj9:function(t,i,c){"use strict";c.r(i),c.d(i,"PayrollModule",function(){return et});var o,n=c("ofXK"),r=c("tyNb"),l=c("fXoL"),d=function(e){return{height:e}},s=((o=function(){function t(a){var i=this;e(this,t),this.ngZone=a,window.onresize=function(e){i.ngZone.run(function(){i.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}return a(t,[{key:"getScreenHeight",value:function(){this.innerHeight=window.innerHeight+"px"}},{key:"ngOnInit",value:function(){}},{key:"onResize",value:function(e){this.innerHeight=e.target.innerHeight+"px"}}]),t}()).\u0275fac=function(e){return new(e||o)(l.Ub(l.G))},o.\u0275cmp=l.Ob({type:o,selectors:[["app-payroll"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.hc("resized",function(e){return t.onResize(e)}),l.Vb(1,"router-outlet"),l.Zb()),2&e&&l.pc("ngStyle",l.tc(1,d,t.innerHeight))},directives:[n.n,r.g],styles:[""]}),o),b=c("bNXq"),u=c("AytR"),p=c("3Pt+"),m=c("JqCM"),g=c("oW1M"),f=c("oOf3");function v(e,t){if(1&e&&(l.ac(0,"tr"),l.ac(1,"td"),l.Lc(2),l.Zb(),l.ac(3,"td",34),l.Lc(4),l.Zb(),l.ac(5,"td"),l.Lc(6),l.Zb(),l.ac(7,"td"),l.Lc(8),l.Zb(),l.ac(9,"td"),l.Lc(10),l.Zb(),l.ac(11,"td"),l.Lc(12),l.Zb(),l.ac(13,"td"),l.Lc(14),l.Zb(),l.ac(15,"td"),l.ac(16,"a",45),l.Lc(17),l.Zb(),l.Zb(),l.Zb()),2&e){var a=t.$implicit,i=t.index,c=l.jc();l.Mb("active",i==c.currentIndex),l.Ib(2),l.Mc((c.pngConfig.pageNum-1)*c.pngConfig.pageSize+(i+1)),l.Ib(2),l.Mc(a.id),l.Ib(2),l.Mc(a.empName),l.Ib(2),l.Mc(a.empCode),l.Ib(2),l.Mc(a.salaryDayMonthYear),l.Ib(2),l.Mc(a.prlElmntGross),l.Ib(2),l.Mc(a.netPayableAmount),l.Ib(2),l.rc("routerLink","/payroll/payslip/",a.id,""),l.Ib(1),l.Mc(a.paySlipNum)}}function h(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",46),l.ac(2,"h5",47),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function Z(e,t){if(1&e&&(l.ac(0,"option",48),l.Lc(1),l.Zb()),2&e){var a=t.$implicit;l.pc("value",a),l.Ib(1),l.Nc(" ",a," ")}}var y,L,D=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},P=((L=function(){function t(a,i){e(this,t),this.payrollService=a,this.spinnerService=i,this.baseUrl=u.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.pngConfig={pageNum:1,pageSize:10,totalItem:50,pageSizes:[3,5,10,25,50,100,200,500,1e3],pngDiplayLastSeq:10,itemsPerPage:10,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){this.myFromGroup=new p.g({pageSize:new p.e}),this.myFromGroup.get("pageSize").setValue(this.pngConfig.pageSize),this.bindFromFloatingLabel(),this.getListData()}},{key:"ngAfterViewInit",value:function(){setTimeout(function(){},1e3)}},{key:"bindFromFloatingLabel",value:function(){var e=this;$(".floating").length>0&&$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),$(".filter-row").find("input, select, textarea").keyup(function(t){console.log(t.keyCode),13==t.keyCode&&e.getSearchData()})}},{key:"searchByFromDate",value:function(e){var t=this.pipe.transform(e,"yyyy-MM-dd");this.srcFromDate=t,console.log(t),this.bindFromFloatingLabel()}},{key:"searchByToDate",value:function(e){var t=this.pipe.transform(e,"yyyy-MM-dd");this.srcToDate=t,console.log(t),this.bindFromFloatingLabel()}},{key:"searchByEmpName",value:function(e){console.log(e),this.srcEmpName=e}},{key:"searchByEmpCode",value:function(e){console.log(e),this.srcEmpCode=e}},{key:"searchBySearchButton",value:function(){console.log(this.srcFromDate),console.log(this.srcToDate),console.log(this.srcEmpName),console.log(this.srcEmpCode),this.getListData()}},{key:"getSearchData",value:function(){this.getListData()}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.srcEmpCode&&(a.empCode=this.srcEmpCode),this.srcEmpName&&(a.empName=this.srcEmpName),this.srcFromDate&&this.srcToDate&&(a.fromDate=this.srcFromDate,a.toDate=this.srcToDate),a}},{key:"getListData",value:function(){var e,t=this,a=this.baseUrl+"/empSalary";e=this.getUserQueryParams(this.pngConfig.pageNum,this.pngConfig.pageSize),this.spinnerService.show(),this.payrollService.sendGetRequest(a,e).subscribe(function(e){t.listData=e.objectList,t.pngConfig.totalItem=e.totalItems,t.pngConfig.totalItems=e.totalItems,t.setDisplayLastSequence(),t.spinnerService.hide()},function(e){console.log(e)})}},{key:"setDisplayLastSequence",value:function(){this.pngConfig.pngDiplayLastSeq=(this.pngConfig.pageNum-1)*this.pngConfig.pageSize+this.pngConfig.pageSize,this.listData.length<this.pngConfig.pageSize&&(this.pngConfig.pngDiplayLastSeq=(this.pngConfig.pageNum-1)*this.pngConfig.pageSize+this.pngConfig.pageSize),this.pngConfig.totalItem<this.pngConfig.pngDiplayLastSeq&&(this.pngConfig.pngDiplayLastSeq=this.pngConfig.totalItem)}},{key:"handlePageChange",value:function(e){this.pngConfig.pageNum=e,this.pngConfig.currentPage=this.pngConfig.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.pngConfig.pageSize=e.target.value,this.pngConfig.pageNum=1,this.pngConfig.itemsPerPage=this.pngConfig.pageSize,this.getListData()}},{key:"ngOnDestroy",value:function(){}}]),t}()).\u0275fac=function(e){return new(e||L)(l.Ub(b.a),l.Ub(m.c))},L.\u0275cmp=l.Ob({type:L,selectors:[["app-employee-salary"]],decls:91,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-2","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-2","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"btn","btn-success","btn-block",3,"click"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"d-none"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"",3,"formGroup"],["formControlName","pageSize",1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Employee Salary"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Dashboard"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Salary"),l.Zb(),l.Zb(),l.Zb(),l.ac(12,"div",9),l.ac(13,"div",10),l.ac(14,"button",11),l.Lc(15,"Excel"),l.Zb(),l.ac(16,"button",11),l.Lc(17,"PDF"),l.Zb(),l.ac(18,"button",11),l.Vb(19,"i",12),l.Lc(20," Print"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(21,"div",13),l.ac(22,"div",14),l.ac(23,"div",15),l.ac(24,"div",16),l.ac(25,"div",17),l.ac(26,"input",18),l.hc("input",function(e){return t.searchByEmpName(e.target.value)}),l.Zb(),l.ac(27,"label",19),l.Lc(28,"Employee Name"),l.Zb(),l.Zb(),l.Zb(),l.ac(29,"div",20),l.ac(30,"div",17),l.ac(31,"input",18),l.hc("input",function(e){return t.searchByEmpCode(e.target.value)}),l.Zb(),l.ac(32,"label",19),l.Lc(33,"Employee Code"),l.Zb(),l.Zb(),l.Zb(),l.ac(34,"div",21),l.ac(35,"div",17),l.ac(36,"div",22),l.ac(37,"input",23),l.hc("bsValueChange",function(e){return t.searchByFromDate(e)}),l.Zb(),l.Zb(),l.ac(38,"label",19),l.Lc(39,"From"),l.Zb(),l.Zb(),l.Zb(),l.ac(40,"div",21),l.ac(41,"div",17),l.ac(42,"div",22),l.ac(43,"input",23),l.hc("bsValueChange",function(e){return t.searchByToDate(e)}),l.Zb(),l.Zb(),l.ac(44,"label",19),l.Lc(45,"To"),l.Zb(),l.Zb(),l.Zb(),l.ac(46,"div",21),l.ac(47,"a",24),l.hc("click",function(){return t.searchBySearchButton()}),l.Lc(48," Search "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(49,"div",25),l.ac(50,"div",26),l.ac(51,"div",27),l.ac(52,"div",28),l.ac(53,"div",29),l.ac(54,"div",30),l.ac(55,"div",31),l.ac(56,"span",32),l.Lc(57),l.Zb(),l.Zb(),l.Zb(),l.ac(58,"table",33),l.ac(59,"thead"),l.ac(60,"tr"),l.ac(61,"th"),l.Lc(62,"SL"),l.Zb(),l.ac(63,"th",34),l.Lc(64,"TB_ROW_ID"),l.Zb(),l.ac(65,"th"),l.Lc(66,"Employee"),l.Zb(),l.ac(67,"th"),l.Lc(68,"Employee Code"),l.Zb(),l.ac(69,"th"),l.Lc(70,"Salary Disburse Date"),l.Zb(),l.ac(71,"th"),l.Lc(72,"Gross Salary"),l.Zb(),l.ac(73,"th"),l.Lc(74,"Net Pay Amount"),l.Zb(),l.ac(75,"th"),l.Lc(76,"Payslip"),l.Zb(),l.Zb(),l.Zb(),l.ac(77,"tbody"),l.Jc(78,v,18,11,"tr",35),l.kc(79,"paginate"),l.Jc(80,h,4,0,"tr",36),l.Zb(),l.Zb(),l.ac(81,"div",37),l.ac(82,"div",38),l.Lc(83," Items per Page "),l.ac(84,"select",39),l.hc("change",function(e){return t.handlePageSizeChange(e)}),l.Jc(85,Z,2,2,"option",40),l.Zb(),l.Zb(),l.ac(86,"div",41),l.ac(87,"pagination-controls",42),l.hc("pageChange",function(e){return t.handlePageChange(e)}),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(88,"ngx-spinner",43),l.ac(89,"p",44),l.Lc(90," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(37),l.pc("bsConfig",l.sc(13,D)),l.Ib(6),l.pc("bsConfig",l.sc(14,D)),l.Ib(14),l.Pc("Displaying ( ",(t.pngConfig.pageNum-1)*t.pngConfig.pageSize+1," to ",t.pngConfig.pngDiplayLastSeq," of ",t.pngConfig.totalItem," ) entries"),l.Ib(21),l.pc("ngForOf",l.mc(79,10,t.listData,t.pngConfig)),l.Ib(2),l.pc("ngIf",0===t.listData.length),l.Ib(2),l.pc("formGroup",t.myFromGroup),l.Ib(3),l.pc("ngForOf",t.pngConfig.pageSizes),l.Ib(3),l.pc("fullScreen",!1))},directives:[r.e,g.b,g.a,n.l,n.m,p.p,p.h,p.v,p.o,p.f,f.c,m.a,p.s,p.y],pipes:[f.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}.pgn-pageSizeOption[_ngcontent-%COMP%]{padding:3px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),L),C=((y=function(){function t(a,i,c){e(this,t),this.payrollService=a,this.spinnerService=i,this.route=c,this.baseUrl=u.a.baseUrl,this.payslipData={}}return a(t,[{key:"ngOnInit",value:function(){this.loadPayslipData()}},{key:"loadPayslipData",value:function(){var e=this;this.payslipId=this.route.snapshot.params.id;var t=this.baseUrl+"/getEmpPayslip/"+this.payslipId;this.spinnerService.show(),this.payrollService.sendGetRequest(t,{}).subscribe(function(t){e.payslipData=t,e.spinnerService.hide()},function(e){console.log(e)})}}]),t}()).\u0275fac=function(e){return new(e||y)(l.Ub(b.a),l.Ub(m.c),l.Ub(r.a))},y.\u0275cmp=l.Ob({type:y,selectors:[["app-salary-view"]],decls:141,vars:36,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group-sm"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-body"],[1,"payslip-title"],[1,"col-sm-6","m-b-20"],[1,"list-unstyled","mb-0"],[1,"invoice-details"],[1,"text-uppercase"],[1,"list-unstyled"],[1,"col-lg-12","m-b-20"],[1,"mb-0"],[1,"col-sm-6"],[1,"m-b-10"],[1,"table","table-bordered"],[1,"float-right"],[1,"col-sm-12"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Payslip"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Dashboard"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payslip"),l.Zb(),l.Zb(),l.Zb(),l.ac(12,"div",9),l.ac(13,"div",10),l.ac(14,"button",11),l.Lc(15,"CSV"),l.Zb(),l.ac(16,"button",11),l.Lc(17,"PDF"),l.Zb(),l.ac(18,"button",11),l.Vb(19,"i",12),l.Lc(20," Print"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(21,"div",13),l.ac(22,"div",14),l.ac(23,"div",15),l.ac(24,"div",16),l.ac(25,"h4",17),l.Lc(26),l.Zb(),l.ac(27,"div",13),l.ac(28,"div",18),l.ac(29,"ul",19),l.ac(30,"li"),l.Lc(31),l.Zb(),l.ac(32,"li"),l.Lc(33),l.Zb(),l.ac(34,"li"),l.Lc(35),l.Zb(),l.Zb(),l.Zb(),l.ac(36,"div",18),l.ac(37,"div",20),l.ac(38,"h3",21),l.Lc(39),l.Zb(),l.ac(40,"ul",22),l.ac(41,"li"),l.Lc(42,"Salary Month: "),l.ac(43,"span"),l.Lc(44),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(45,"div",13),l.ac(46,"div",23),l.ac(47,"ul",22),l.ac(48,"li"),l.ac(49,"h5",24),l.ac(50,"strong"),l.Lc(51),l.Zb(),l.Zb(),l.Zb(),l.ac(52,"li"),l.ac(53,"span"),l.Lc(54),l.Zb(),l.Zb(),l.ac(55,"li"),l.Lc(56),l.Zb(),l.ac(57,"li"),l.Lc(58),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(59,"div",13),l.ac(60,"div",25),l.ac(61,"div"),l.ac(62,"h4",26),l.ac(63,"strong"),l.Lc(64,"Earnings"),l.Zb(),l.Zb(),l.ac(65,"table",27),l.ac(66,"tbody"),l.ac(67,"tr"),l.ac(68,"td"),l.ac(69,"strong"),l.Lc(70,"Basic Pay"),l.Zb(),l.ac(71,"span",28),l.Lc(72),l.kc(73,"number"),l.Zb(),l.Zb(),l.Zb(),l.ac(74,"tr"),l.ac(75,"td"),l.ac(76,"strong"),l.Lc(77,"House Rent Allowance"),l.Zb(),l.ac(78,"span",28),l.Lc(79),l.kc(80,"number"),l.Zb(),l.Zb(),l.Zb(),l.ac(81,"tr"),l.ac(82,"td"),l.ac(83,"strong"),l.Lc(84,"Conveyance"),l.Zb(),l.ac(85,"span",28),l.Lc(86,"0.00"),l.Zb(),l.Zb(),l.Zb(),l.ac(87,"tr"),l.ac(88,"td"),l.ac(89,"strong"),l.Lc(90,"Other Allowance"),l.Zb(),l.ac(91,"span",28),l.Lc(92,"0.00"),l.Zb(),l.Zb(),l.Zb(),l.ac(93,"tr"),l.ac(94,"td"),l.ac(95,"strong"),l.Lc(96,"Total Earnings"),l.Zb(),l.ac(97,"span",28),l.ac(98,"strong"),l.Lc(99),l.kc(100,"number"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(101,"div",25),l.ac(102,"div"),l.ac(103,"h4",26),l.ac(104,"strong"),l.Lc(105,"Deductions"),l.Zb(),l.Zb(),l.ac(106,"table",27),l.ac(107,"tbody"),l.ac(108,"tr"),l.ac(109,"td"),l.ac(110,"strong"),l.Lc(111,"AIT"),l.Zb(),l.ac(112,"span",28),l.Lc(113,"0.00"),l.Zb(),l.Zb(),l.Zb(),l.ac(114,"tr"),l.ac(115,"td"),l.ac(116,"strong"),l.Lc(117,"Food Deduction"),l.Zb(),l.ac(118,"span",28),l.Lc(119,"0.00"),l.Zb(),l.Zb(),l.Zb(),l.ac(120,"tr"),l.ac(121,"td"),l.ac(122,"strong"),l.Lc(123,"Absent Deduction"),l.Zb(),l.ac(124,"span",28),l.Lc(125),l.kc(126,"number"),l.Zb(),l.Zb(),l.Zb(),l.ac(127,"tr"),l.ac(128,"td"),l.ac(129,"strong"),l.Lc(130,"Total Deductions"),l.Zb(),l.ac(131,"span",28),l.ac(132,"strong"),l.Lc(133),l.kc(134,"number"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(135,"div",29),l.ac(136,"p"),l.ac(137,"strong"),l.Lc(138),l.kc(139,"number"),l.Zb(),l.Lc(140),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(26),l.Mc(t.payslipData.payslipNarration),l.Ib(5),l.Mc(t.payslipData.orgName),l.Ib(2),l.Mc(t.payslipData.orgAddress),l.Ib(2),l.Mc(t.payslipData.orgAddressLine2),l.Ib(4),l.Mc(t.payslipData.payslipNum),l.Ib(5),l.Oc("",t.payslipData.monthFullName,", ",t.payslipData.salaryYear,""),l.Ib(7),l.Mc(t.payslipData.empName),l.Ib(3),l.Mc(t.payslipData.empDesignation),l.Ib(2),l.Nc("Employee Code: ",t.payslipData.empCode,""),l.Ib(2),l.Nc("Joining Date: ",t.payslipData.joiningDate,""),l.Ib(14),l.Mc(l.mc(73,18,t.payslipData.basicSalary,"1.2-2")),l.Ib(7),l.Mc(l.mc(80,21,t.payslipData.houseRentAlwAmt,"1.2-2")),l.Ib(20),l.Mc(l.mc(100,24,t.payslipData.totalEarnings,"1.2-2")),l.Ib(26),l.Mc(l.mc(126,27,t.payslipData.absentDeduction,"1.2-2")),l.Ib(8),l.Mc(l.mc(134,30,t.payslipData.totalDeductions,"1.2-2")),l.Ib(5),l.Nc("Net Pay: ",l.mc(139,33,t.payslipData.netPayable,"1.2-2"),""),l.Ib(2),l.Nc(" ( ",t.payslipData.netPayableStr," )"))},directives:[r.e],pipes:[n.f],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),y),S=c("IhMt"),F=c("5eHb");function k(e,t){if(1&e){var a=l.bc();l.ac(0,"tr"),l.ac(1,"th"),l.Lc(2),l.Zb(),l.ac(3,"td"),l.Lc(4),l.Zb(),l.ac(5,"td"),l.Lc(6),l.Zb(),l.ac(7,"td",25),l.ac(8,"div",113),l.ac(9,"a",114),l.ac(10,"i",115),l.Lc(11,"more_vert"),l.Zb(),l.Zb(),l.ac(12,"div",116),l.ac(13,"a",117),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().editAdd(e.id)}),l.Vb(14,"i",118),l.Lc(15," Edit"),l.Zb(),l.ac(16,"a",119),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().tempAddId=e.id}),l.Vb(17,"i",120),l.Lc(18," Delete"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()}if(2&e){var i=t.$implicit;l.Ib(2),l.Mc(i.name),l.Ib(2),l.Mc(i.category),l.Ib(2),l.Mc(i.unitCost)}}function I(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",121),l.ac(2,"h5",122),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function O(e,t){if(1&e){var a=l.bc();l.ac(0,"tr"),l.ac(1,"th"),l.Lc(2),l.Zb(),l.ac(3,"td"),l.Lc(4),l.Zb(),l.ac(5,"td",25),l.ac(6,"div",113),l.ac(7,"a",114),l.ac(8,"i",115),l.Lc(9,"more_vert"),l.Zb(),l.Zb(),l.ac(10,"div",116),l.ac(11,"a",123),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().editOver(e.id)}),l.Vb(12,"i",118),l.Lc(13," Edit"),l.Zb(),l.ac(14,"a",124),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().tempOverId=e.id}),l.Vb(15,"i",120),l.Lc(16," Delete"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()}if(2&e){var i=t.$implicit;l.Ib(2),l.Mc(i.name),l.Ib(2),l.Mc(i.rate)}}function M(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",121),l.ac(2,"h5",122),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function x(e,t){if(1&e){var a=l.bc();l.ac(0,"tr"),l.ac(1,"th"),l.Lc(2),l.Zb(),l.ac(3,"td"),l.Lc(4),l.Zb(),l.ac(5,"td",25),l.ac(6,"div",113),l.ac(7,"a",114),l.ac(8,"i",115),l.Lc(9,"more_vert"),l.Zb(),l.Zb(),l.ac(10,"div",116),l.ac(11,"a",125),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().editDeduct(e.id)}),l.Vb(12,"i",118),l.Lc(13," Edit"),l.Zb(),l.ac(14,"a",126),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().tempDeductId=e.id}),l.Vb(15,"i",120),l.Lc(16," Delete"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()}if(2&e){var i=t.$implicit;l.Ib(2),l.Mc(i.name),l.Ib(2),l.Mc(i.unitCost)}}function _(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",121),l.ac(2,"h5",122),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function N(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Name is required"),l.Zb())}function w(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,N,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addPayrollForm.get("addPayrollName").invalid&&a.addPayrollForm.get("addPayrollName").touched)}}function E(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Category is required"),l.Zb())}function A(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,E,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addPayrollForm.get("addPayrollCategory").invalid&&a.addPayrollForm.get("addPayrollCategory").touched)}}function U(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Category is required"),l.Zb())}function V(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,U,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addPayrollForm.get("addPayrollUnit").invalid&&a.addPayrollForm.get("addPayrollUnit").touched)}}function R(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Name is required"),l.Zb())}function T(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,R,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editPayrollForm.get("editPayrollName").invalid&&a.editPayrollForm.get("editPayrollName").touched)}}function B(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Category is required"),l.Zb())}function q(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,B,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editPayrollForm.get("editPayrollCategory").invalid&&a.editPayrollForm.get("editPayrollCategory").touched)}}function z(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Unit is required"),l.Zb())}function G(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,z,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editPayrollForm.get("editPayrollUnit").invalid&&a.editPayrollForm.get("editPayrollUnit").touched)}}function j(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Name is required"),l.Zb())}function J(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,j,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addOverForm.get("addOverName").invalid&&a.addOverForm.get("addOverName").touched)}}function H(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Rate is required"),l.Zb())}function W(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,H,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addOverForm.get("addOverRate").invalid&&a.addOverForm.get("addOverRate").touched)}}function Y(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Name is required"),l.Zb())}function Q(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,Y,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editOverForm.get("editOverName").invalid&&a.editOverForm.get("editOverName").touched)}}function X(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Rate is required"),l.Zb())}function K(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,X,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editOverForm.get("editOverRate").invalid&&a.editOverForm.get("editOverRate").touched)}}function ee(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Name is required"),l.Zb())}function te(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,ee,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addDeductForm.get("addDeductName").invalid&&a.addDeductForm.get("addDeductName").touched)}}function ae(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Unit is required"),l.Zb())}function ie(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,ae,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.addDeductForm.get("addDeductUnit").invalid&&a.addDeductForm.get("addDeductUnit").touched)}}function ce(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Name is required"),l.Zb())}function oe(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,ce,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editDeductForm.get("editDeductName").invalid&&a.editDeductForm.get("editDeductName").touched)}}function ne(e,t){1&e&&(l.ac(0,"small",42),l.Lc(1," *Unit is required"),l.Zb())}function re(e,t){if(1&e&&(l.ac(0,"div"),l.Jc(1,ne,2,0,"small",127),l.Zb()),2&e){var a=l.jc();l.Ib(1),l.pc("ngIf",a.editDeductForm.get("editDeductunit").invalid&&a.editDeductForm.get("editDeductunit").touched)}}var le,de,se=((de=function(){function t(a,i,c){e(this,t),this.allModuleService=a,this.formBuilder=i,this.toastr=c,this.urlAdd="payrollAddition",this.urlOver="payrollOvertime",this.urlDeduct="payrollDeduction",this.allAddPayroll=[],this.allOverPayroll=[],this.allDeductPayroll=[]}return a(t,[{key:"ngOnInit",value:function(){this.getAddPayroll(),this.getOverpayroll(),this.getDeductPayroll(),this.addPayrollForm=this.formBuilder.group({addPayrollName:["",[p.w.required]],addPayrollCategory:["",[p.w.required]],addPayrollUnit:["",[p.w.required]]}),this.editPayrollForm=this.formBuilder.group({editPayrollName:["",[p.w.required]],editPayrollCategory:["",[p.w.required]],editPayrollUnit:["",[p.w.required]]}),this.addOverForm=this.formBuilder.group({addOverName:["",[p.w.required]],addOverRate:["",[p.w.required]]}),this.editOverForm=this.formBuilder.group({editOverName:["",[p.w.required]],editOverRate:["",[p.w.required]]}),this.addDeductForm=this.formBuilder.group({addDeductName:["",[p.w.required]],addDeductUnit:["",[p.w.required]]}),this.editDeductForm=this.formBuilder.group({editDeductName:["",[p.w.required]],editDeductunit:["",[p.w.required]]})}},{key:"getAddPayroll",value:function(){var e=this;this.allModuleService.get(this.urlAdd).subscribe(function(t){e.allAddPayroll=t,$("#datatable1").DataTable().clear()})}},{key:"getOverpayroll",value:function(){var e=this;this.allModuleService.get(this.urlOver).subscribe(function(t){e.allOverPayroll=t,$("#datatable2").DataTable().clear()})}},{key:"getDeductPayroll",value:function(){var e=this;this.allModuleService.get(this.urlDeduct).subscribe(function(t){e.allDeductPayroll=t,$("#datatable3").DataTable().clear()})}},{key:"addPayroll",value:function(){this.addPayrollForm.valid&&(this.allModuleService.add({name:this.addPayrollForm.value.addPayrollName,category:this.addPayrollForm.value.addPayrollCategory,unitCost:this.addPayrollForm.value.addPayrollUnit},this.urlAdd).subscribe(function(e){}),this.getAddPayroll(),$("#add_addition").modal("hide"),this.addPayrollForm.reset(),this.toastr.success("Payroll added","Success"))}},{key:"editPayroll",value:function(){this.allModuleService.update({name:this.editPayrollForm.value.editPayrollName,category:this.editPayrollForm.value.editPayrollCategory,unitCost:this.editPayrollForm.value.editPayrollUnit,id:this.editAddId},this.urlAdd).subscribe(function(e){}),this.getAddPayroll(),$("#edit_addition").modal("hide"),this.toastr.success("Payroll edited","Success")}},{key:"editAdd",value:function(e){this.editAddId=e;var t=this.allAddPayroll.findIndex(function(t){return t.id===e}),a=this.allAddPayroll[t];this.editPayrollForm.setValue({editPayrollName:a.name,editPayrollCategory:a.category,editPayrollUnit:a.unitCost})}},{key:"deletePayroll",value:function(){var e=this;this.allModuleService.delete(this.tempAddId,this.urlAdd).subscribe(function(t){e.getAddPayroll(),$("#delete_addition").modal("hide")}),this.toastr.success("Payroll deleted","Success")}},{key:"addOver",value:function(){this.addOverForm.valid&&(this.allModuleService.add({name:this.addOverForm.value.addOverName,rate:this.addOverForm.value.addOverRate},this.urlOver).subscribe(function(e){}),this.getOverpayroll(),$("#add_overtime").modal("hide"),this.addOverForm.reset(),this.toastr.success("Overtime added","Success"))}},{key:"editOverSubmit",value:function(){this.allModuleService.update({name:this.editOverForm.value.editOverName,rate:this.editOverForm.value.editOverRate,id:this.editOverId},this.urlOver).subscribe(function(e){}),this.getOverpayroll(),$("#edit_overtime").modal("hide"),this.toastr.success("Overtime edited","Success")}},{key:"editOver",value:function(e){this.editOverId=e;var t=this.allOverPayroll.findIndex(function(t){return t.id===e}),a=this.allOverPayroll[t];this.editOverForm.setValue({editOverName:a.name,editOverRate:a.rate})}},{key:"deleteOver",value:function(){var e=this;this.allModuleService.delete(this.tempOverId,this.urlOver).subscribe(function(t){e.getOverpayroll(),$("#delete_overtime").modal("hide")}),this.toastr.success("Overtime deleted","Success")}},{key:"addDeducts",value:function(){this.addDeductForm.valid&&(this.allModuleService.add({name:this.addDeductForm.value.addDeductName,unitCost:this.addDeductForm.value.addDeductUnit},this.urlDeduct).subscribe(function(e){}),this.getDeductPayroll(),$("#add_deduction").modal("hide"),this.addDeductForm.reset(),this.toastr.success("Deduction added","Success"))}},{key:"editDeductSubmit",value:function(){this.allModuleService.update({name:this.editDeductForm.value.editDeductName,unitCost:this.editDeductForm.value.editDeductunit,id:this.editDeductId},this.urlDeduct).subscribe(function(e){}),this.getDeductPayroll(),$("#edit_deduction").modal("hide"),this.toastr.success("Deducts edited","Success")}},{key:"editDeduct",value:function(e){this.editDeductId=e;var t=this.allDeductPayroll.findIndex(function(t){return t.id===e}),a=this.allDeductPayroll[t];this.editDeductForm.setValue({editDeductName:a.name,editDeductunit:a.unitCost})}},{key:"deleteDeduct",value:function(){var e=this;this.allModuleService.delete(this.tempDeductId,this.urlDeduct).subscribe(function(t){e.getDeductPayroll(),$("#delete_deduction").modal("hide")}),this.toastr.success("Deduction deleted","Success")}}]),t}()).\u0275fac=function(e){return new(e||de)(l.Ub(S.a),l.Ub(p.d),l.Ub(F.b))},de.\u0275cmp=l.Ob({type:de,selectors:[["app-payroll-items"]],decls:502,vars:54,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"page-menu"],[1,"row"],[1,"col-sm-12"],[1,"nav","nav-tabs","nav-tabs-bottom"],[1,"nav-item"],["data-toggle","tab","href","#tab_additions",1,"nav-link","active"],["data-toggle","tab","href","#tab_overtime",1,"nav-link"],["data-toggle","tab","href","#tab_deductions",1,"nav-link"],[1,"tab-content"],["id","tab_additions",1,"tab-pane","show","active"],[1,"text-right","mb-4","clearfix"],["type","button","data-toggle","modal","data-target","#add_addition",1,"btn","btn-primary","add-btn"],[1,"fa","fa-plus"],[1,"payroll-table","card"],[1,"table-responsive"],[1,"table","table-hover","table-radius"],[1,"text-right"],[4,"ngFor","ngForOf"],[4,"ngIf"],["id","tab_overtime",1,"tab-pane"],["type","button","data-toggle","modal","data-target","#add_overtime",1,"btn","btn-primary","add-btn"],["id","tab_deductions",1,"tab-pane"],["type","button","data-toggle","modal","data-target","#add_deduction",1,"btn","btn-primary","add-btn"],["id","add_addition","role","dialog",1,"modal","custom-modal","fade"],["role","document",1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["type","button","data-dismiss","modal","aria-label","Close",1,"close"],["aria-hidden","true"],[1,"modal-body"],[3,"formGroup","ngSubmit"],[1,"form-group"],[1,"text-danger"],["type","text","formControlName","addPayrollName",1,"form-control"],["formControlName","addPayrollCategory",1,"select","form-control"],[1,"d-block"],[1,"status-toggle"],["type","checkbox","id","unit_calculation",1,"check"],["for","unit_calculation",1,"checktoggle"],[1,"input-group"],[1,"input-group-prepend"],[1,"input-group-text"],["type","text","formControlName","addPayrollUnit",1,"form-control"],[1,"input-group-append"],[1,"form-check","form-check-inline"],["type","radio","name","addition_assignee","id","addition_no_emp","value","option1","checked","",1,"form-check-input"],["for","addition_no_emp",1,"form-check-label"],["type","radio","name","addition_assignee","id","addition_all_emp","value","option2",1,"form-check-input"],["for","addition_all_emp",1,"form-check-label"],["type","radio","name","addition_assignee","id","addition_single_emp","value","option3",1,"form-check-input"],["for","addition_single_emp",1,"form-check-label"],[1,"select","form-control"],[1,"submit-section"],[1,"btn","btn-primary","submit-btn"],["id","edit_addition","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","editPayrollName",1,"form-control"],["formControlName","editPayrollCategory",1,"select","form-control"],["type","checkbox","id","edit_unit_calculation",1,"check"],["for","edit_unit_calculation",1,"checktoggle"],["type","text","formControlName","editPayrollUnit",1,"form-control"],["type","radio","name","edit_addition_assignee","id","edit_addition_no_emp","value","option1","checked","",1,"form-check-input"],["for","edit_addition_no_emp",1,"form-check-label"],["type","radio","name","edit_addition_assignee","id","edit_addition_all_emp","value","option2",1,"form-check-input"],["for","edit_addition_all_emp",1,"form-check-label"],["type","radio","name","edit_addition_assignee","id","edit_addition_single_emp","value","option3",1,"form-check-input"],["for","edit_addition_single_emp",1,"form-check-label"],["id","delete_addition","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],["id","add_overtime","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","addOverName",1,"form-control"],["type","text","formControlName","addOverRate",1,"form-control"],["id","edit_overtime","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","editOverName",1,"form-control"],["type","text","formControlName","editOverRate",1,"form-control"],["id","delete_overtime","role","dialog",1,"modal","custom-modal","fade"],["id","add_deduction","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","addDeductName",1,"form-control"],["type","checkbox","id","unit_calculation_deduction",1,"check"],["for","unit_calculation_deduction",1,"checktoggle"],["type","text","formControlName","addDeductUnit",1,"form-control"],["type","radio","name","deduction_assignee","id","deduction_no_emp","value","option1","checked","",1,"form-check-input"],["for","deduction_no_emp",1,"form-check-label"],["type","radio","name","deduction_assignee","id","deduction_all_emp","value","option2",1,"form-check-input"],["for","deduction_all_emp",1,"form-check-label"],["type","radio","name","deduction_assignee","id","deduction_single_emp","value","option3",1,"form-check-input"],["for","deduction_single_emp",1,"form-check-label"],["id","edit_deduction","role","dialog",1,"modal","custom-modal","fade"],["type","text","formControlName","editDeductName",1,"form-control"],["type","checkbox","id","edit_unit_calculation_deduction",1,"check"],["for","edit_unit_calculation_deduction",1,"checktoggle"],["type","text","formControlName","editDeductunit",1,"form-control"],["type","radio","name","edit_deduction_assignee","id","edit_deduction_no_emp","value","option1","checked","",1,"form-check-input"],["for","edit_deduction_no_emp",1,"form-check-label"],["type","radio","name","edit_deduction_assignee","id","edit_deduction_all_emp","value","option2",1,"form-check-input"],["for","edit_deduction_all_emp",1,"form-check-label"],["type","radio","name","edit_deduction_assignee","id","edit_deduction_single_emp","value","option3",1,"form-check-input"],["for","edit_deduction_single_emp",1,"form-check-label"],["id","delete_deduction","role","dialog",1,"modal","custom-modal","fade"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],["data-toggle","modal","data-target","#edit_addition",1,"dropdown-item",3,"click"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_addition",1,"dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],["data-toggle","modal","data-target","#edit_overtime",1,"dropdown-item",3,"click"],["data-toggle","modal","data-target","#delete_overtime",1,"dropdown-item",3,"click"],["data-toggle","modal","data-target","#edit_deduction",1,"dropdown-item",3,"click"],["data-toggle","modal","data-target","#delete_deduction",1,"dropdown-item",3,"click"],["class","text-danger",4,"ngIf"]],template:function(e,t){if(1&e){l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Payroll Items"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Dashboard"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payroll Items"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(12,"div",9),l.ac(13,"div",10),l.ac(14,"div",11),l.ac(15,"ul",12),l.ac(16,"li",13),l.ac(17,"a",14),l.Lc(18,"Additions"),l.Zb(),l.Zb(),l.ac(19,"li",13),l.ac(20,"a",15),l.Lc(21,"Overtime"),l.Zb(),l.Zb(),l.ac(22,"li",13),l.ac(23,"a",16),l.Lc(24,"Deductions"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(25,"div",17),l.ac(26,"div",18),l.ac(27,"div",19),l.ac(28,"button",20),l.Vb(29,"i",21),l.Lc(30," Add Addition"),l.Zb(),l.Zb(),l.ac(31,"div",22),l.ac(32,"div",23),l.ac(33,"table",24),l.ac(34,"thead"),l.ac(35,"tr"),l.ac(36,"th"),l.Lc(37,"Name"),l.Zb(),l.ac(38,"th"),l.Lc(39,"Category"),l.Zb(),l.ac(40,"th"),l.Lc(41,"Default/Unit Amount"),l.Zb(),l.ac(42,"th",25),l.Lc(43,"Action"),l.Zb(),l.Zb(),l.Zb(),l.ac(44,"tbody"),l.Jc(45,k,19,3,"tr",26),l.Jc(46,I,4,0,"tr",27),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(47,"div",28),l.ac(48,"div",19),l.ac(49,"button",29),l.Vb(50,"i",21),l.Lc(51," Add Overtime"),l.Zb(),l.Zb(),l.ac(52,"div",22),l.ac(53,"div",23),l.ac(54,"table",24),l.ac(55,"thead"),l.ac(56,"tr"),l.ac(57,"th"),l.Lc(58,"Name"),l.Zb(),l.ac(59,"th"),l.Lc(60,"Rate"),l.Zb(),l.ac(61,"th",25),l.Lc(62,"Action"),l.Zb(),l.Zb(),l.Zb(),l.ac(63,"tbody"),l.Jc(64,O,17,2,"tr",26),l.Jc(65,M,4,0,"tr",27),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(66,"div",30),l.ac(67,"div",19),l.ac(68,"button",31),l.Vb(69,"i",21),l.Lc(70," Add Deduction"),l.Zb(),l.Zb(),l.ac(71,"div",22),l.ac(72,"div",23),l.ac(73,"table",24),l.ac(74,"thead"),l.ac(75,"tr"),l.ac(76,"th"),l.Lc(77,"Name"),l.Zb(),l.ac(78,"th"),l.Lc(79,"Default/Unit Amount"),l.Zb(),l.ac(80,"th",25),l.Lc(81,"Action"),l.Zb(),l.Zb(),l.Zb(),l.ac(82,"tbody"),l.Jc(83,x,17,2,"tr",26),l.Jc(84,_,4,0,"tr",27),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(85,"div",32),l.ac(86,"div",33),l.ac(87,"div",34),l.ac(88,"div",35),l.ac(89,"h5",36),l.Lc(90,"Add Addition"),l.Zb(),l.ac(91,"button",37),l.ac(92,"span",38),l.Lc(93,"\xd7"),l.Zb(),l.Zb(),l.Zb(),l.ac(94,"div",39),l.ac(95,"form",40),l.hc("ngSubmit",function(){return t.addPayroll()}),l.ac(96,"div",41),l.ac(97,"label"),l.Lc(98,"Name "),l.ac(99,"span",42),l.Lc(100,"*"),l.Zb(),l.Zb(),l.Vb(101,"input",43),l.Jc(102,w,2,1,"div",27),l.Zb(),l.ac(103,"div",41),l.ac(104,"label"),l.Lc(105,"Category "),l.ac(106,"span",42),l.Lc(107,"*"),l.Zb(),l.Zb(),l.ac(108,"select",44),l.ac(109,"option"),l.Lc(110,"Select a category"),l.Zb(),l.ac(111,"option"),l.Lc(112,"Monthly remuneration"),l.Zb(),l.ac(113,"option"),l.Lc(114,"Additional remuneration"),l.Zb(),l.Zb(),l.Jc(115,A,2,1,"div",27),l.Zb(),l.ac(116,"div",41),l.ac(117,"label",45),l.Lc(118,"Unit calculation"),l.Zb(),l.ac(119,"div",46),l.Vb(120,"input",47),l.ac(121,"label",48),l.Lc(122,"checkbox"),l.Zb(),l.Zb(),l.Zb(),l.ac(123,"div",41),l.ac(124,"label"),l.Lc(125,"Unit Amount"),l.Zb(),l.ac(126,"div",49),l.ac(127,"div",50),l.ac(128,"span",51),l.Lc(129,"$"),l.Zb(),l.Zb(),l.Vb(130,"input",52),l.Jc(131,V,2,1,"div",27),l.ac(132,"div",53),l.ac(133,"span",51),l.Lc(134,".00"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(135,"div",41),l.ac(136,"label",45),l.Lc(137,"Assignee"),l.Zb(),l.ac(138,"div",54),l.Vb(139,"input",55),l.ac(140,"label",56),l.Lc(141," No assignee "),l.Zb(),l.Zb(),l.ac(142,"div",54),l.Vb(143,"input",57),l.ac(144,"label",58),l.Lc(145," All employees "),l.Zb(),l.Zb(),l.ac(146,"div",54),l.Vb(147,"input",59),l.ac(148,"label",60),l.Lc(149," Select Employee "),l.Zb(),l.Zb(),l.ac(150,"div",41),l.ac(151,"select",61),l.ac(152,"option"),l.Lc(153,"-"),l.Zb(),l.ac(154,"option"),l.Lc(155,"Select All"),l.Zb(),l.ac(156,"option"),l.Lc(157,"John Doe"),l.Zb(),l.ac(158,"option"),l.Lc(159,"Richard Miles"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(160,"div",62),l.ac(161,"button",63),l.Lc(162,"Submit"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(163,"div",64),l.ac(164,"div",33),l.ac(165,"div",34),l.ac(166,"div",35),l.ac(167,"h5",36),l.Lc(168,"Edit Addition"),l.Zb(),l.ac(169,"button",37),l.ac(170,"span",38),l.Lc(171,"\xd7"),l.Zb(),l.Zb(),l.Zb(),l.ac(172,"div",39),l.ac(173,"form",40),l.hc("ngSubmit",function(){return t.editPayroll()}),l.ac(174,"div",41),l.ac(175,"label"),l.Lc(176,"Name "),l.ac(177,"span",42),l.Lc(178,"*"),l.Zb(),l.Zb(),l.Vb(179,"input",65),l.Jc(180,T,2,1,"div",27),l.Zb(),l.ac(181,"div",41),l.ac(182,"label"),l.Lc(183,"Category "),l.ac(184,"span",42),l.Lc(185,"*"),l.Zb(),l.Zb(),l.ac(186,"select",66),l.ac(187,"option"),l.Lc(188,"Select a category"),l.Zb(),l.ac(189,"option"),l.Lc(190,"Monthly remuneration"),l.Zb(),l.ac(191,"option"),l.Lc(192,"Additional remuneration"),l.Zb(),l.Zb(),l.Jc(193,q,2,1,"div",27),l.Zb(),l.ac(194,"div",41),l.ac(195,"label",45),l.Lc(196,"Unit calculation"),l.Zb(),l.ac(197,"div",46),l.Vb(198,"input",67),l.ac(199,"label",68),l.Lc(200,"checkbox"),l.Zb(),l.Zb(),l.Zb(),l.ac(201,"div",41),l.ac(202,"label"),l.Lc(203,"Unit Amount"),l.Zb(),l.ac(204,"div",49),l.ac(205,"div",50),l.ac(206,"span",51),l.Lc(207,"$"),l.Zb(),l.Zb(),l.Vb(208,"input",69),l.Jc(209,G,2,1,"div",27),l.ac(210,"div",53),l.ac(211,"span",51),l.Lc(212,".00"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(213,"div",41),l.ac(214,"label",45),l.Lc(215,"Assignee"),l.Zb(),l.ac(216,"div",54),l.Vb(217,"input",70),l.ac(218,"label",71),l.Lc(219," No assignee "),l.Zb(),l.Zb(),l.ac(220,"div",54),l.Vb(221,"input",72),l.ac(222,"label",73),l.Lc(223," All employees "),l.Zb(),l.Zb(),l.ac(224,"div",54),l.Vb(225,"input",74),l.ac(226,"label",75),l.Lc(227," Select Employee "),l.Zb(),l.Zb(),l.ac(228,"div",41),l.ac(229,"select",61),l.ac(230,"option"),l.Lc(231,"-"),l.Zb(),l.ac(232,"option"),l.Lc(233,"Select All"),l.Zb(),l.ac(234,"option"),l.Lc(235,"John Doe"),l.Zb(),l.ac(236,"option"),l.Lc(237,"Richard Miles"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(238,"div",62),l.ac(239,"button",63),l.Lc(240,"Save"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(241,"div",76),l.ac(242,"div",77),l.ac(243,"div",34),l.ac(244,"div",39),l.ac(245,"div",78),l.ac(246,"h3"),l.Lc(247,"Delete Addition"),l.Zb(),l.ac(248,"p"),l.Lc(249,"Are you sure want to delete?"),l.Zb(),l.Zb(),l.ac(250,"div",79),l.ac(251,"div",10),l.ac(252,"div",80),l.ac(253,"a",81),l.hc("click",function(){return t.deletePayroll()}),l.Lc(254,"Delete"),l.Zb(),l.Zb(),l.ac(255,"div",80),l.ac(256,"a",82),l.Lc(257,"Cancel"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(258,"div",83),l.ac(259,"div",33),l.ac(260,"div",34),l.ac(261,"div",35),l.ac(262,"h5",36),l.Lc(263,"Add Overtime"),l.Zb(),l.ac(264,"button",37),l.ac(265,"span",38),l.Lc(266,"\xd7"),l.Zb(),l.Zb(),l.Zb(),l.ac(267,"div",39),l.ac(268,"form",40),l.hc("ngSubmit",function(){return t.addOver()}),l.ac(269,"div",41),l.ac(270,"label"),l.Lc(271,"Name "),l.ac(272,"span",42),l.Lc(273,"*"),l.Zb(),l.Zb(),l.Vb(274,"input",84),l.Jc(275,J,2,1,"div",27),l.Zb(),l.ac(276,"div",41),l.ac(277,"label"),l.Lc(278,"Rate Type "),l.ac(279,"span",42),l.Lc(280,"*"),l.Zb(),l.Zb(),l.ac(281,"select",61),l.ac(282,"option"),l.Lc(283,"-"),l.Zb(),l.ac(284,"option"),l.Lc(285,"Daily Rate"),l.Zb(),l.ac(286,"option"),l.Lc(287,"Hourly Rate"),l.Zb(),l.Zb(),l.Zb(),l.ac(288,"div",41),l.ac(289,"label"),l.Lc(290,"Rate "),l.ac(291,"span",42),l.Lc(292,"*"),l.Zb(),l.Zb(),l.Vb(293,"input",85),l.Jc(294,W,2,1,"div",27),l.Zb(),l.ac(295,"div",62),l.ac(296,"button",63),l.Lc(297,"Submit"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(298,"div",86),l.ac(299,"div",33),l.ac(300,"div",34),l.ac(301,"div",35),l.ac(302,"h5",36),l.Lc(303,"Edit Overtime"),l.Zb(),l.ac(304,"button",37),l.ac(305,"span",38),l.Lc(306,"\xd7"),l.Zb(),l.Zb(),l.Zb(),l.ac(307,"div",39),l.ac(308,"form",40),l.hc("ngSubmit",function(){return t.editOverSubmit()}),l.ac(309,"div",41),l.ac(310,"label"),l.Lc(311,"Name "),l.ac(312,"span",42),l.Lc(313,"*"),l.Zb(),l.Zb(),l.Vb(314,"input",87),l.Jc(315,Q,2,1,"div",27),l.Zb(),l.ac(316,"div",41),l.ac(317,"label"),l.Lc(318,"Rate Type "),l.ac(319,"span",42),l.Lc(320,"*"),l.Zb(),l.Zb(),l.ac(321,"select",61),l.ac(322,"option"),l.Lc(323,"-"),l.Zb(),l.ac(324,"option"),l.Lc(325,"Daily Rate"),l.Zb(),l.ac(326,"option"),l.Lc(327,"Hourly Rate"),l.Zb(),l.Zb(),l.Zb(),l.ac(328,"div",41),l.ac(329,"label"),l.Lc(330,"Rate "),l.ac(331,"span",42),l.Lc(332,"*"),l.Zb(),l.Zb(),l.Vb(333,"input",88),l.Jc(334,K,2,1,"div",27),l.Zb(),l.ac(335,"div",62),l.ac(336,"button",63),l.Lc(337,"Save"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(338,"div",89),l.ac(339,"div",77),l.ac(340,"div",34),l.ac(341,"div",39),l.ac(342,"div",78),l.ac(343,"h3"),l.Lc(344,"Delete Overtime"),l.Zb(),l.ac(345,"p"),l.Lc(346,"Are you sure want to delete?"),l.Zb(),l.Zb(),l.ac(347,"div",79),l.ac(348,"div",10),l.ac(349,"div",80),l.ac(350,"a",81),l.hc("click",function(){return t.deleteOver()}),l.Lc(351,"Delete"),l.Zb(),l.Zb(),l.ac(352,"div",80),l.ac(353,"a",82),l.Lc(354,"Cancel"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(355,"div",90),l.ac(356,"div",33),l.ac(357,"div",34),l.ac(358,"div",35),l.ac(359,"h5",36),l.Lc(360,"Add Deduction"),l.Zb(),l.ac(361,"button",37),l.ac(362,"span",38),l.Lc(363,"\xd7"),l.Zb(),l.Zb(),l.Zb(),l.ac(364,"div",39),l.ac(365,"form",40),l.hc("ngSubmit",function(){return t.addDeducts()}),l.ac(366,"div",41),l.ac(367,"label"),l.Lc(368,"Name "),l.ac(369,"span",42),l.Lc(370,"*"),l.Zb(),l.Zb(),l.Vb(371,"input",91),l.Jc(372,te,2,1,"div",27),l.Zb(),l.ac(373,"div",41),l.ac(374,"label",45),l.Lc(375,"Unit calculation"),l.Zb(),l.ac(376,"div",46),l.Vb(377,"input",92),l.ac(378,"label",93),l.Lc(379,"checkbox"),l.Zb(),l.Zb(),l.Zb(),l.ac(380,"div",41),l.ac(381,"label"),l.Lc(382,"Unit Amount"),l.Zb(),l.ac(383,"div",49),l.ac(384,"div",50),l.ac(385,"span",51),l.Lc(386,"$"),l.Zb(),l.Zb(),l.Vb(387,"input",94),l.Jc(388,ie,2,1,"div",27),l.ac(389,"div",53),l.ac(390,"span",51),l.Lc(391,".00"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(392,"div",41),l.ac(393,"label",45),l.Lc(394,"Assignee"),l.Zb(),l.ac(395,"div",54),l.Vb(396,"input",95),l.ac(397,"label",96),l.Lc(398," No assignee "),l.Zb(),l.Zb(),l.ac(399,"div",54),l.Vb(400,"input",97),l.ac(401,"label",98),l.Lc(402," All employees "),l.Zb(),l.Zb(),l.ac(403,"div",54),l.Vb(404,"input",99),l.ac(405,"label",100),l.Lc(406," Select Employee "),l.Zb(),l.Zb(),l.ac(407,"div",41),l.ac(408,"select",61),l.ac(409,"option"),l.Lc(410,"-"),l.Zb(),l.ac(411,"option"),l.Lc(412,"Select All"),l.Zb(),l.ac(413,"option"),l.Lc(414,"John Doe"),l.Zb(),l.ac(415,"option"),l.Lc(416,"Richard Miles"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(417,"div",62),l.ac(418,"button",63),l.Lc(419,"Submit"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(420,"div",101),l.ac(421,"div",33),l.ac(422,"div",34),l.ac(423,"div",35),l.ac(424,"h5",36),l.Lc(425,"Edit Deduction"),l.Zb(),l.ac(426,"button",37),l.ac(427,"span",38),l.Lc(428,"\xd7"),l.Zb(),l.Zb(),l.Zb(),l.ac(429,"div",39),l.ac(430,"form",40),l.hc("ngSubmit",function(){return t.editDeductSubmit()}),l.ac(431,"div",41),l.ac(432,"label"),l.Lc(433,"Name "),l.ac(434,"span",42),l.Lc(435,"*"),l.Zb(),l.Zb(),l.Vb(436,"input",102),l.Jc(437,oe,2,1,"div",27),l.Zb(),l.ac(438,"div",41),l.ac(439,"label",45),l.Lc(440,"Unit calculation"),l.Zb(),l.ac(441,"div",46),l.Vb(442,"input",103),l.ac(443,"label",104),l.Lc(444,"checkbox"),l.Zb(),l.Zb(),l.Zb(),l.ac(445,"div",41),l.ac(446,"label"),l.Lc(447,"Unit Amount"),l.Zb(),l.ac(448,"div",49),l.ac(449,"div",50),l.ac(450,"span",51),l.Lc(451,"$"),l.Zb(),l.Zb(),l.Vb(452,"input",105),l.Jc(453,re,2,1,"div",27),l.ac(454,"div",53),l.ac(455,"span",51),l.Lc(456,".00"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(457,"div",41),l.ac(458,"label",45),l.Lc(459,"Assignee"),l.Zb(),l.ac(460,"div",54),l.Vb(461,"input",106),l.ac(462,"label",107),l.Lc(463," No assignee "),l.Zb(),l.Zb(),l.ac(464,"div",54),l.Vb(465,"input",108),l.ac(466,"label",109),l.Lc(467," All employees "),l.Zb(),l.Zb(),l.ac(468,"div",54),l.Vb(469,"input",110),l.ac(470,"label",111),l.Lc(471," Select Employee "),l.Zb(),l.Zb(),l.ac(472,"div",41),l.ac(473,"select",61),l.ac(474,"option"),l.Lc(475,"-"),l.Zb(),l.ac(476,"option"),l.Lc(477,"Select All"),l.Zb(),l.ac(478,"option"),l.Lc(479,"John Doe"),l.Zb(),l.ac(480,"option"),l.Lc(481,"Richard Miles"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(482,"div",62),l.ac(483,"button",63),l.Lc(484,"Save"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(485,"div",112),l.ac(486,"div",77),l.ac(487,"div",34),l.ac(488,"div",39),l.ac(489,"div",78),l.ac(490,"h3");l.Lc(491,"Delete Deduction"),l.Zb(),l.ac(492,"p"),l.Lc(493,"Are you sure want to delete?"),l.Zb(),l.Zb(),l.ac(494,"div",79),l.ac(495,"div",10),l.ac(496,"div",80),l.ac(497,"a",81),l.hc("click",function(){return t.deleteDeduct()}),l.Lc(498,"Delete"),l.Zb(),l.Zb(),l.ac(499,"div",80),l.ac(500,"a",82),l.Lc(501,"Cancel"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()}2&e&&(l.Ib(45),l.pc("ngForOf",t.allAddPayroll),l.Ib(1),l.pc("ngIf",0===t.allAddPayroll.length),l.Ib(18),l.pc("ngForOf",t.allOverPayroll),l.Ib(1),l.pc("ngIf",0===t.allOverPayroll.length),l.Ib(18),l.pc("ngForOf",t.allDeductPayroll),l.Ib(1),l.pc("ngIf",0===t.allDeductPayroll.length),l.Ib(11),l.pc("formGroup",t.addPayrollForm),l.Ib(6),l.Mb("invalid",t.addPayrollForm.get("addPayrollName").invalid&&t.addPayrollForm.get("addPayrollName").touched),l.Ib(1),l.pc("ngIf",t.addPayrollForm.get("addPayrollName").invalid&&t.addPayrollForm.get("addPayrollName").touched),l.Ib(6),l.Mb("invalid",t.addPayrollForm.get("addPayrollCategory").invalid&&t.addPayrollForm.get("addPayrollCategory").touched),l.Ib(7),l.pc("ngIf",t.addPayrollForm.get("addPayrollCategory").invalid&&t.addPayrollForm.get("addPayrollCategory").touched),l.Ib(15),l.Mb("invalid",t.addPayrollForm.get("addPayrollUnit").invalid&&t.addPayrollForm.get("addPayrollUnit").touched),l.Ib(1),l.pc("ngIf",t.addPayrollForm.get("addPayrollUnit").invalid&&t.addPayrollForm.get("addPayrollUnit").touched),l.Ib(42),l.pc("formGroup",t.editPayrollForm),l.Ib(6),l.Mb("invalid",t.editPayrollForm.get("editPayrollName").invalid&&t.editPayrollForm.get("editPayrollName").touched),l.Ib(1),l.pc("ngIf",t.editPayrollForm.get("editPayrollName").invalid&&t.editPayrollForm.get("editPayrollName").touched),l.Ib(6),l.Mb("invalid",t.editPayrollForm.get("editPayrollCategory").invalid&&t.editPayrollForm.get("editPayrollCategory").touched),l.Ib(7),l.pc("ngIf",t.editPayrollForm.get("editPayrollCategory").invalid&&t.editPayrollForm.get("editPayrollCategory").touched),l.Ib(15),l.Mb("invalid",t.editPayrollForm.get("editPayrollUnit").invalid&&t.editPayrollForm.get("editPayrollUnit").touched),l.Ib(1),l.pc("ngIf",t.editPayrollForm.get("editPayrollUnit").invalid&&t.editPayrollForm.get("editPayrollUnit").touched),l.Ib(59),l.pc("formGroup",t.addOverForm),l.Ib(6),l.Mb("invalid",t.addOverForm.get("addOverName").invalid&&t.addOverForm.get("addOverName").touched),l.Ib(1),l.pc("ngIf",t.addOverForm.get("addOverName").invalid&&t.addOverForm.get("addOverName").touched),l.Ib(18),l.Mb("invalid",t.addOverForm.get("addOverRate").invalid&&t.addOverForm.get("addOverRate").touched),l.Ib(1),l.pc("ngIf",t.addOverForm.get("addOverRate").invalid&&t.addOverForm.get("addOverRate").touched),l.Ib(14),l.pc("formGroup",t.editOverForm),l.Ib(6),l.Mb("invalid",t.editOverForm.get("editOverName").invalid&&t.editOverForm.get("editOverName").touched),l.Ib(1),l.pc("ngIf",t.editOverForm.get("editOverName").invalid&&t.editOverForm.get("editOverName").touched),l.Ib(18),l.Mb("invalid",t.editOverForm.get("editOverRate").invalid&&t.editOverForm.get("editOverRate").touched),l.Ib(1),l.pc("ngIf",t.editOverForm.get("editOverRate").invalid&&t.editOverForm.get("editOverRate").touched),l.Ib(31),l.pc("formGroup",t.addDeductForm),l.Ib(6),l.Mb("invalid",t.addDeductForm.get("addDeductName").invalid&&t.addDeductForm.get("addDeductName").touched),l.Ib(1),l.pc("ngIf",t.addDeductForm.get("addDeductName").invalid&&t.addDeductForm.get("addDeductName").touched),l.Ib(15),l.Mb("invalid",t.addDeductForm.get("addDeductUnit").invalid&&t.addDeductForm.get("addDeductUnit").touched),l.Ib(1),l.pc("ngIf",t.addDeductForm.get("addDeductUnit").invalid&&t.addDeductForm.get("addDeductUnit").touched),l.Ib(42),l.pc("formGroup",t.editDeductForm),l.Ib(6),l.Mb("invalid",t.editDeductForm.get("editDeductName").invalid&&t.editDeductForm.get("editDeductName").touched),l.Ib(1),l.pc("ngIf",t.editDeductForm.get("editDeductName").invalid&&t.editDeductForm.get("editDeductName").touched),l.Ib(15),l.Mb("invalid",t.editDeductForm.get("editDeductunit").invalid&&t.editDeductForm.get("editDeductunit").touched),l.Ib(1),l.pc("ngIf",t.editDeductForm.get("editDeductunit").invalid&&t.editDeductForm.get("editDeductunit").touched))},directives:[r.e,n.l,n.m,p.x,p.p,p.h,p.b,p.o,p.f,p.v,p.s,p.y],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),de),be=((le=function(){function t(a,i,c,o,n,r){e(this,t),this.formBuilder=a,this.datePipe=i,this.payrollService=c,this.route=o,this.router=n,this.spinnerService=r,this.baseUrl=u.a.baseUrl}return a(t,[{key:"ngOnInit",value:function(){this.basicForm=this.formBuilder.group({year:[""],month:[""],totalPayableDay:[""],totalDisburseDay:[""],fromDate:[""],toDate:[""]}),this.initializeForm(),this.loadData(),document.querySelectorAll(".btn-ripple").forEach(function(e){e.addEventListener("click",function(e){!function(e){var t=e.currentTarget,a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left="".concat(a,"px"),c.style.top="".concat(i,"px"),t.appendChild(c),setTimeout(function(){c.remove()},1e3)}(e)})})}},{key:"initializeForm",value:function(){this.setFormDefaultValues()}},{key:"basicFormSubmit",value:function(){var e=this,t=this.baseUrl+"/salaryProcess/start",a={abc:"OK"};(a=this.basicForm.value).procFromDate=a.fromDate?this.datePipe.transform(a.fromDate,"yyyy-MM-dd").toString().slice(0,10):null,a.procToDate=a.toDate?this.datePipe.transform(a.toDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.payrollService.sendPostRequest(t,a).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/payroll/salary-process-list"],{relativeTo:e.route})},function(t){console.log(t),e.spinnerService.hide()})}},{key:"loadData",value:function(){}},{key:"daysInThisMonth",value:function(e){return new Date(e.getFullYear(),e.getMonth()+1,0).getDate()}},{key:"getMonthFirstDate",value:function(e){return new Date(e.getFullYear(),e.getMonth(),1)}},{key:"getMonthLastDate",value:function(e){return e=new Date,new Date(e.getFullYear(),e.getMonth()+1,0)}},{key:"onChangeYear",value:function(e){console.log(e)}},{key:"onChangeMonth",value:function(e){console.log(e),console.log(parseInt(e));var t=this.basicForm.get("year").value,a=parseInt(e),i=new Date(t,a-1,1),c=this.daysInThisMonth(i);this.basicForm.controls.totalPayableDay.setValue(c),this.basicForm.controls.totalDisburseDay.setValue(c),this.basicForm.controls.fromDate.setValue(this.getMonthFirstDate(i)),this.basicForm.controls.toDate.setValue(this.getMonthLastDate(i))}},{key:"setFormDefaultValues",value:function(){var e=(new Date).getFullYear();this.basicForm.patchValue({year:e}),this.basicForm.controls.year.setValue(e)}},{key:"resetFormValues",value:function(){this.basicForm=this.formBuilder.group({year:[""],month:[""],totalPayableDay:[""],totalDisburseDay:[""],fromDate:[""],toDate:[""]}),this.setFormDefaultValues()}}]),t}()).\u0275fac=function(e){return new(e||le)(l.Ub(p.d),l.Ub(n.e),l.Ub(b.a),l.Ub(r.a),l.Ub(r.c),l.Ub(m.c))},le.\u0275cmp=l.Ob({type:le,selectors:[["app-payroll-salary-process"]],decls:105,vars:2,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/salary-process-list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-header"],[1,"card-title","mb-0"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","year",1,"select","form-control",3,"change"],["value",""],["value","2021","selected",""],["value","2022"],["value","2023"],["value","2024"],["value","2025"],["formControlName","month",1,"select","form-control",3,"change"],["value","","selected",""],["value","01"],["value","02"],["value","03"],["value","04"],["value","05"],["value","06"],["value","07"],["value","08"],["value","09"],["value","10"],["value","11"],["value","12"],["type","text","formControlName","totalPayableDay",1,"form-control"],["type","text","formControlName","totalDisburseDay",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","fromDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","toDate","bsDatepicker","",1,"form-control"],[1,"text-right"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-arrow-circle-o-right"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Salary Process"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Dashboard"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Salary Process"),l.Zb(),l.Zb(),l.Zb(),l.ac(12,"div",9),l.ac(13,"a",10),l.Vb(14,"i",11),l.Lc(15," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",12),l.ac(17,"div",13),l.ac(18,"div",14),l.ac(19,"div",15),l.ac(20,"h4",16),l.Lc(21,"Process Parameters"),l.Zb(),l.Zb(),l.ac(22,"div",17),l.ac(23,"form",18),l.hc("ngSubmit",function(){return t.basicFormSubmit()}),l.ac(24,"div",19),l.ac(25,"label",20),l.Lc(26,"Year"),l.Zb(),l.ac(27,"div",21),l.ac(28,"select",22),l.hc("change",function(e){return t.onChangeYear(e.target.value)}),l.ac(29,"option",23),l.Lc(30,"Select Year"),l.Zb(),l.ac(31,"option",24),l.Lc(32,"2021"),l.Zb(),l.ac(33,"option",25),l.Lc(34,"2022"),l.Zb(),l.ac(35,"option",26),l.Lc(36,"2023"),l.Zb(),l.ac(37,"option",27),l.Lc(38,"2024"),l.Zb(),l.ac(39,"option",28),l.Lc(40,"2025"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(41,"div",19),l.ac(42,"label",20),l.Lc(43,"Month"),l.Zb(),l.ac(44,"div",21),l.ac(45,"select",29),l.hc("change",function(e){return t.onChangeMonth(e.target.value)}),l.ac(46,"option",30),l.Lc(47,"Select Month"),l.Zb(),l.ac(48,"option",31),l.Lc(49,"January"),l.Zb(),l.ac(50,"option",32),l.Lc(51,"February"),l.Zb(),l.ac(52,"option",33),l.Lc(53,"March"),l.Zb(),l.ac(54,"option",34),l.Lc(55,"April"),l.Zb(),l.ac(56,"option",35),l.Lc(57,"May"),l.Zb(),l.ac(58,"option",36),l.Lc(59,"Jun"),l.Zb(),l.ac(60,"option",37),l.Lc(61,"July"),l.Zb(),l.ac(62,"option",38),l.Lc(63,"August"),l.Zb(),l.ac(64,"option",39),l.Lc(65,"September"),l.Zb(),l.ac(66,"option",40),l.Lc(67,"October"),l.Zb(),l.ac(68,"option",41),l.Lc(69,"November"),l.Zb(),l.ac(70,"option",42),l.Lc(71,"December"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(72,"div",19),l.ac(73,"label",20),l.Lc(74,"Total Payable Day"),l.Zb(),l.ac(75,"div",21),l.Vb(76,"input",43),l.Zb(),l.Zb(),l.ac(77,"div",19),l.ac(78,"label",20),l.Lc(79,"Total Disburse Day"),l.Zb(),l.ac(80,"div",21),l.Vb(81,"input",44),l.Zb(),l.Zb(),l.ac(82,"div",19),l.ac(83,"label",20),l.Lc(84,"Process From Date"),l.Zb(),l.ac(85,"div",21),l.ac(86,"div",45),l.Vb(87,"input",46),l.Zb(),l.Zb(),l.Zb(),l.ac(88,"div",19),l.ac(89,"label",20),l.Lc(90,"Process To Date"),l.Zb(),l.ac(91,"div",21),l.ac(92,"div",45),l.Vb(93,"input",47),l.Zb(),l.Zb(),l.Zb(),l.ac(94,"div",48),l.ac(95,"button",49),l.hc("click",function(){return t.resetFormValues()}),l.Vb(96,"i",50),l.Lc(97," Reset "),l.Zb(),l.Lc(98," \xa0 \xa0 \xa0 "),l.ac(99,"button",51),l.Vb(100,"i",52),l.Lc(101," Start Process "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(102,"ngx-spinner",53),l.ac(103,"p",54),l.Lc(104," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(23),l.pc("formGroup",t.basicForm),l.Ib(79),l.pc("fullScreen",!1))},directives:[r.e,p.x,p.p,p.h,p.v,p.o,p.f,p.s,p.y,p.b,g.b,g.a,m.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}.btn-ripple[_ngcontent-%COMP%]{position:relative;overflow:hidden}.btn-ripple[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:absolute;background:#fff;transform:translate(-50%,-50%);pointer-events:none;border-radius:50%;-webkit-animation:animate 1s linear infinite;animation:animate 1s linear infinite}@-webkit-keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}@keyframes animate{0%{width:0;height:0;opacity:.5}to{width:500px;height:500px;opacity:0}}"]}),le),ue=c("PqYM");function pe(e,t){if(1&e&&(l.ac(0,"tr"),l.ac(1,"td"),l.Lc(2),l.Zb(),l.ac(3,"td",35),l.Lc(4),l.Zb(),l.ac(5,"td"),l.Lc(6),l.Zb(),l.ac(7,"td"),l.Lc(8),l.Zb(),l.ac(9,"td"),l.Lc(10),l.Zb(),l.ac(11,"td"),l.Lc(12),l.Zb(),l.ac(13,"td"),l.Lc(14),l.Zb(),l.ac(15,"td"),l.Lc(16),l.Zb(),l.ac(17,"td"),l.Vb(18,"i",46),l.Zb(),l.ac(19,"td"),l.Lc(20),l.Zb(),l.ac(21,"td"),l.Lc(22),l.Zb(),l.ac(23,"td"),l.Lc(24),l.Zb(),l.ac(25,"td"),l.ac(26,"a",47),l.Lc(27,"View"),l.Zb(),l.Zb(),l.Zb()),2&e){var a=t.$implicit,i=t.index,c=l.jc();l.Mb("active",i==c.currentIndex),l.Ib(2),l.Mc((c.configPgn.pageNum-1)*c.configPgn.pageSize+(i+1)),l.Ib(2),l.Mc(a.id),l.Ib(2),l.Mc(a.year),l.Ib(2),l.Mc(a.month),l.Ib(2),l.Mc(a.totalPayableDay),l.Ib(2),l.Mc(a.totalDisburseDay),l.Ib(2),l.Mc(a.procFromDate),l.Ib(2),l.Mc(a.procToDate),l.Ib(4),l.Mc(a.jobStartTime),l.Ib(2),l.Mc(a.jobEndTime),l.Ib(2),l.Mc(a.jobRunUser),l.Ib(2),l.rc("routerLink","/payroll/payslip/",a.id,"")}}function me(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",48),l.ac(2,"h5",49),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function ge(e,t){if(1&e&&(l.ac(0,"option",50),l.Lc(1),l.Zb()),2&e){var a=t.$implicit;l.pc("value",a),l.Ib(1),l.Nc(" ",a," ")}}var fe,ve=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},he=((fe=function(){function t(a,i){e(this,t),this.payrollService=a,this.spinnerService=i,this.baseUrl=u.a.baseUrl,this.everyFiveSeconds=Object(ue.a)(0,11e3),this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:10,totalItem:50,pageSizes:[3,5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:10,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){var e=this;this.myFromGroup=new p.g({pageSize:new p.e}),this.myFromGroup.get("pageSize").setValue(this.configPgn.pageSize),this.bindFromFloatingLabel(),this.getListData(),this.subscription=this.everyFiveSeconds.subscribe(function(){e.getListData()})}},{key:"ngAfterViewInit",value:function(){setTimeout(function(){},1e3)}},{key:"bindFromFloatingLabel",value:function(){var e=this;$(".floating").length>0&&$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),$(".filter-row").find("input, select, textarea").keyup(function(t){console.log(t.keyCode),13==t.keyCode&&e.getSearchData()})}},{key:"searchByFromDate",value:function(e){var t=this.pipe.transform(e,"yyyy-MM-dd");this.srcFromDate=t,console.log(t),this.bindFromFloatingLabel()}},{key:"searchByToDate",value:function(e){var t=this.pipe.transform(e,"yyyy-MM-dd");this.srcToDate=t,console.log(t),this.bindFromFloatingLabel()}},{key:"searchByEmpCode",value:function(e){console.log(e),this.srcEmpCode=e}},{key:"searchBySearchButton",value:function(){console.log(this.srcFromDate),console.log(this.srcToDate),console.log(this.srcEmpCode),this.getListData()}},{key:"getSearchData",value:function(){this.getListData()}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.srcEmpCode&&(a.empCode=this.srcEmpCode),this.srcFromDate&&this.srcToDate&&(a.fromDate=this.srcFromDate,a.toDate=this.srcToDate),a}},{key:"getListData",value:function(){var e,t=this,a=this.baseUrl+"/api/salaryProcessJP/getList";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.payrollService.sendGetRequest(a,e).subscribe(function(e){t.listData=e.objectList,t.configPgn.totalItem=e.totalItems,t.configPgn.totalItems=e.totalItems,t.setDisplayLastSequence(),t.spinnerService.hide()},function(e){console.log(e)})}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}},{key:"ngOnDestroy",value:function(){this.subscription.unsubscribe()}}]),t}()).\u0275fac=function(e){return new(e||fe)(l.Ub(b.a),l.Ub(m.c))},fe.\u0275cmp=l.Ob({type:fe,selectors:[["app-salary-process-job-list"]],decls:94,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-2","col-12"],[1,"btn","btn-success","btn-block",3,"click"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/payroll/salary-process/",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"d-none"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"",3,"formGroup"],["formControlName","pageSize",1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],[1,"fa","fa-spinner","fa-pulse"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Salary Process"),l.Zb(),l.Vb(6,"ul",5),l.Zb(),l.ac(7,"div",6),l.ac(8,"div",7),l.ac(9,"button",8),l.Lc(10,"Excel"),l.Zb(),l.ac(11,"button",8),l.Lc(12,"PDF"),l.Zb(),l.ac(13,"button",8),l.Vb(14,"i",9),l.Lc(15," Print"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",10),l.ac(17,"div",11),l.ac(18,"div",12),l.ac(19,"div",13),l.ac(20,"div",14),l.ac(21,"input",15),l.hc("input",function(e){return t.searchByEmpCode(e.target.value)}),l.Zb(),l.ac(22,"label",16),l.Lc(23,"Employee Code"),l.Zb(),l.Zb(),l.Zb(),l.ac(24,"div",17),l.ac(25,"div",14),l.ac(26,"div",18),l.ac(27,"input",19),l.hc("bsValueChange",function(e){return t.searchByFromDate(e)}),l.Zb(),l.Zb(),l.ac(28,"label",16),l.Lc(29,"From"),l.Zb(),l.Zb(),l.Zb(),l.ac(30,"div",17),l.ac(31,"div",14),l.ac(32,"div",18),l.ac(33,"input",19),l.hc("bsValueChange",function(e){return t.searchByToDate(e)}),l.Zb(),l.Zb(),l.ac(34,"label",16),l.Lc(35,"To"),l.Zb(),l.Zb(),l.Zb(),l.ac(36,"div",20),l.ac(37,"a",21),l.hc("click",function(){return t.searchBySearchButton()}),l.Lc(38," Search "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(39,"div",22),l.ac(40,"div",23),l.ac(41,"div",24),l.ac(42,"div",25),l.ac(43,"div",26),l.ac(44,"a",27),l.Vb(45,"i",28),l.Lc(46," New \xa0\xa0\xa0"),l.Zb(),l.Zb(),l.Zb(),l.ac(47,"div",29),l.ac(48,"div",30),l.ac(49,"div",31),l.ac(50,"div",32),l.ac(51,"span",33),l.Lc(52),l.Zb(),l.Zb(),l.Zb(),l.ac(53,"table",34),l.ac(54,"thead"),l.ac(55,"tr"),l.ac(56,"th"),l.Lc(57,"SL"),l.Zb(),l.ac(58,"th",35),l.Lc(59,"TB_ROW_ID"),l.Zb(),l.ac(60,"th"),l.Lc(61,"Year"),l.Zb(),l.ac(62,"th"),l.Lc(63,"Month"),l.Zb(),l.ac(64,"th"),l.Lc(65,"Total Payable Day"),l.Zb(),l.ac(66,"th"),l.Lc(67,"Total Disburse Day"),l.Zb(),l.ac(68,"th"),l.Lc(69,"Proc From Date"),l.Zb(),l.ac(70,"th"),l.Lc(71,"Proc To Date"),l.Zb(),l.ac(72,"th"),l.Lc(73,"Status"),l.Zb(),l.ac(74,"th"),l.Lc(75,"Job Start Time"),l.Zb(),l.ac(76,"th"),l.Lc(77,"Job End Time"),l.Zb(),l.ac(78,"th"),l.Lc(79,"Job Run User"),l.Zb(),l.Zb(),l.Zb(),l.ac(80,"tbody"),l.Jc(81,pe,28,14,"tr",36),l.kc(82,"paginate"),l.Jc(83,me,4,0,"tr",37),l.Zb(),l.Zb(),l.ac(84,"div",38),l.ac(85,"div",39),l.Lc(86," Items per Page "),l.ac(87,"select",40),l.hc("change",function(e){return t.handlePageSizeChange(e)}),l.Jc(88,ge,2,2,"option",41),l.Zb(),l.Zb(),l.ac(89,"div",42),l.ac(90,"pagination-controls",43),l.hc("pageChange",function(e){return t.handlePageChange(e)}),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(91,"ngx-spinner",44),l.ac(92,"p",45),l.Lc(93," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(27),l.pc("bsConfig",l.sc(13,ve)),l.Ib(6),l.pc("bsConfig",l.sc(14,ve)),l.Ib(19),l.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),l.Ib(29),l.pc("ngForOf",l.mc(82,10,t.listData,t.configPgn)),l.Ib(2),l.pc("ngIf",0===t.listData.length),l.Ib(2),l.pc("formGroup",t.myFromGroup),l.Ib(3),l.pc("ngForOf",t.configPgn.pageSizes),l.Ib(3),l.pc("fullScreen",!1))},directives:[g.b,g.a,r.e,n.l,n.m,p.p,p.h,p.v,p.o,p.f,f.c,m.a,p.s,p.y],pipes:[f.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}.page-header[_ngcontent-%COMP%]{margin-bottom:1.1rem}"]}),fe);function Ze(e,t){if(1&e){var a=l.bc();l.ac(0,"tr"),l.ac(1,"td"),l.Lc(2),l.Zb(),l.ac(3,"td",34),l.Lc(4),l.Zb(),l.ac(5,"td"),l.Lc(6),l.Zb(),l.ac(7,"td"),l.Lc(8),l.Zb(),l.ac(9,"td"),l.Lc(10),l.Zb(),l.ac(11,"td"),l.Lc(12),l.Zb(),l.ac(13,"td"),l.Lc(14),l.Zb(),l.ac(15,"td"),l.Lc(16),l.Zb(),l.ac(17,"td"),l.Lc(18),l.Zb(),l.ac(19,"td"),l.Lc(20),l.Zb(),l.ac(21,"td"),l.ac(22,"a",54),l.Vb(23,"i",55),l.Lc(24,"View"),l.Zb(),l.Lc(25," \xa0 "),l.ac(26,"a",56),l.Vb(27,"i",57),l.Zb(),l.Lc(28,"\xa0\xa0 "),l.ac(29,"a",58),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().tempId=e.id}),l.Vb(30,"i",59),l.Zb(),l.Zb(),l.Zb()}if(2&e){var i=t.$implicit,c=t.index,o=l.jc();l.Mb("active",c==o.currentIndex),l.Ib(2),l.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),l.Ib(2),l.Mc(i.id),l.Ib(2),l.Mc(i.empTitle),l.Ib(2),l.Mc(o.titleDecode[i.elementTitle]),l.Ib(2),l.Mc(i.elementAmount),l.Ib(2),l.Mc(i.activeStartDate),l.Ib(2),l.Mc(i.activeEndDate),l.Ib(2),l.Mc(i.isActive),l.Ib(2),l.Mc(i.creationDateTime),l.Ib(2),l.Mc(i.creationUser),l.Ib(2),l.rc("routerLink","/payroll/element-value/show/",i.id,""),l.Ib(4),l.rc("routerLink","/payroll/element-value/edit/",i.id,"")}}function ye(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",60),l.ac(2,"h5",61),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function Le(e,t){if(1&e&&(l.ac(0,"option",62),l.Lc(1),l.Zb()),2&e){var a=t.$implicit;l.pc("value",a),l.Ib(1),l.Nc(" ",a," ")}}var De,Pe,Ce,Se,Fe,ke=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},Ie=((Pe=function(){function t(a,i,c,o,r){e(this,t),this.payrollService=a,this.spinnerService=i,this.route=c,this.router=o,this.toastr=r,this.baseUrl=u.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.titleDecode={HRA:"House Rent Allowance",MDL_ALW:"Medical Allowance",OT_ALW:"Overtime Allowance",LTA:"Leave Travel Allowance",EA:"Entertainment Allowance",UCA_ALW:"Uniform Allowance / Corporate Attire",FAMILY_ALW:"Family Allowance",EDA:"Education Allowance",PRJ_ALW:"Project Allowance",HOSTEL_ALW:"Hostel Allowance",CHILD_EDU_ALW:"Children's education Allowance",CHILD_HOSTEL_ALW:"Children's hostel Allowance",PROF_ALW:"Professional pursuit/research Allowance"},this.configPgn={pageNum:1,pageSize:10,totalItem:50,pageSizes:[3,5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:10,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){this.myFromGroup=new p.g({pageSize:new p.e}),this.myFromGroup.get("pageSize").setValue(this.configPgn.pageSize),this.bindFromFloatingLabel(),this.getListData()}},{key:"ngAfterViewInit",value:function(){setTimeout(function(){},1e3)}},{key:"bindFromFloatingLabel",value:function(){var e=this;$(".floating").length>0&&$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),$(".filter-row").find("input, select, textarea").keyup(function(t){console.log(t.keyCode),13==t.keyCode&&e.getSearchData()})}},{key:"searchByFromDate",value:function(e){var t=this.pipe.transform(e,"yyyy-MM-dd");this.srcFromDate=t,console.log(t),this.bindFromFloatingLabel()}},{key:"searchByToDate",value:function(e){var t=this.pipe.transform(e,"yyyy-MM-dd");this.srcToDate=t,console.log(t),this.bindFromFloatingLabel()}},{key:"searchByEmpCode",value:function(e){console.log(e),this.srcEmpCode=e}},{key:"searchBySearchButton",value:function(){console.log(this.srcFromDate),console.log(this.srcToDate),console.log(this.srcEmpCode),this.getListData()}},{key:"getSearchData",value:function(){this.getListData()}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.srcEmpCode&&(a.empCode=this.srcEmpCode),this.srcFromDate&&this.srcToDate&&(a.fromDate=this.srcFromDate,a.toDate=this.srcToDate),a}},{key:"getListData",value:function(){var e,t=this,a=this.baseUrl+"/api/payrollElValue/getList";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.payrollService.sendGetRequest(a,e).subscribe(function(e){t.listData=e.objectList,t.configPgn.totalItem=e.totalItems,t.configPgn.totalItems=e.totalItems,t.setDisplayLastSequence(),t.spinnerService.hide()},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var t=this,a=this.baseUrl+"/api/payrollElValue/delete/"+e;console.log(a),this.spinnerService.show(),this.payrollService.sendDeleteRequest(a,{rEntityName:"PayrollElementValue",rActiveOperation:"delete"}).subscribe(function(e){console.log(e),t.spinnerService.hide(),$("#delete_entity").modal("hide"),t.toastr.success("Successfully item is deleted","Success"),t.getListData()},function(e){console.log(e),t.spinnerService.hide()})}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}},{key:"ngOnDestroy",value:function(){}}]),t}()).\u0275fac=function(e){return new(e||Pe)(l.Ub(b.a),l.Ub(m.c),l.Ub(r.a),l.Ub(r.c),l.Ub(F.b))},Pe.\u0275cmp=l.Ob({type:Pe,selectors:[["app-payroll-element-value-list"]],decls:109,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig","bsValueChange"],[1,"btn","btn-success","btn-block",3,"click"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/payroll/element-value/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"d-none"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"",3,"formGroup"],["formControlName","pageSize",1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"fa","fa-eye","m-r-5"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Payroll Element"),l.Zb(),l.Vb(6,"ul",5),l.Zb(),l.ac(7,"div",6),l.ac(8,"div",7),l.ac(9,"button",8),l.Lc(10,"Excel"),l.Zb(),l.ac(11,"button",8),l.Lc(12,"PDF"),l.Zb(),l.ac(13,"button",8),l.Vb(14,"i",9),l.Lc(15," Print"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",10),l.ac(17,"div",11),l.ac(18,"div",12),l.ac(19,"div",13),l.ac(20,"div",14),l.ac(21,"input",15),l.hc("input",function(e){return t.searchByEmpCode(e.target.value)}),l.Zb(),l.ac(22,"label",16),l.Lc(23,"Employee Code"),l.Zb(),l.Zb(),l.Zb(),l.ac(24,"div",17),l.ac(25,"div",14),l.ac(26,"div",18),l.ac(27,"input",19),l.hc("bsValueChange",function(e){return t.searchByFromDate(e)}),l.Zb(),l.Zb(),l.ac(28,"label",16),l.Lc(29,"From"),l.Zb(),l.Zb(),l.Zb(),l.ac(30,"div",17),l.ac(31,"div",14),l.ac(32,"div",18),l.ac(33,"input",19),l.hc("bsValueChange",function(e){return t.searchByToDate(e)}),l.Zb(),l.Zb(),l.ac(34,"label",16),l.Lc(35,"To"),l.Zb(),l.Zb(),l.Zb(),l.ac(36,"div",17),l.ac(37,"a",20),l.hc("click",function(){return t.searchBySearchButton()}),l.Lc(38," Search "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(39,"div",21),l.ac(40,"div",22),l.ac(41,"div",23),l.ac(42,"div",24),l.ac(43,"div",25),l.ac(44,"a",26),l.Vb(45,"i",27),l.Lc(46," New \xa0\xa0\xa0"),l.Zb(),l.Zb(),l.Zb(),l.ac(47,"div",28),l.ac(48,"div",29),l.ac(49,"div",30),l.ac(50,"div",31),l.ac(51,"span",32),l.Lc(52),l.Zb(),l.Zb(),l.Zb(),l.ac(53,"table",33),l.ac(54,"thead"),l.ac(55,"tr"),l.ac(56,"th"),l.Lc(57,"SL"),l.Zb(),l.ac(58,"th",34),l.Lc(59,"TB_ROW_ID"),l.Zb(),l.ac(60,"th"),l.Lc(61,"Employee"),l.Zb(),l.ac(62,"th"),l.Lc(63,"Element Title"),l.Zb(),l.ac(64,"th"),l.Lc(65,"Amount"),l.Zb(),l.ac(66,"th"),l.Lc(67,"Start Date"),l.Zb(),l.ac(68,"th"),l.Lc(69,"End Date"),l.Zb(),l.ac(70,"th"),l.Lc(71,"Status"),l.Zb(),l.ac(72,"th"),l.Lc(73,"Creation Time"),l.Zb(),l.ac(74,"th"),l.Lc(75,"Creation User"),l.Zb(),l.ac(76,"th"),l.Lc(77,"Action"),l.Zb(),l.Zb(),l.Zb(),l.ac(78,"tbody"),l.Jc(79,Ze,31,14,"tr",35),l.kc(80,"paginate"),l.Jc(81,ye,4,0,"tr",36),l.Zb(),l.Zb(),l.ac(82,"div",37),l.ac(83,"div",38),l.Lc(84," Items per Page "),l.ac(85,"select",39),l.hc("change",function(e){return t.handlePageSizeChange(e)}),l.Jc(86,Le,2,2,"option",40),l.Zb(),l.Zb(),l.ac(87,"div",41),l.ac(88,"pagination-controls",42),l.hc("pageChange",function(e){return t.handlePageChange(e)}),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(89,"ngx-spinner",43),l.ac(90,"p",44),l.Lc(91," Processing... "),l.Zb(),l.Zb(),l.ac(92,"div",45),l.ac(93,"div",46),l.ac(94,"div",47),l.ac(95,"div",48),l.ac(96,"div",49),l.ac(97,"h3"),l.Lc(98,"Delete Item"),l.Zb(),l.ac(99,"p"),l.Lc(100,"Are you sure want to delete?"),l.Zb(),l.Zb(),l.ac(101,"div",50),l.ac(102,"div",21),l.ac(103,"div",51),l.ac(104,"a",52),l.hc("click",function(){return t.deleteEnityData(t.tempId)}),l.Lc(105,"Delete"),l.Zb(),l.Zb(),l.ac(106,"div",51),l.ac(107,"a",53),l.Lc(108,"Cancel"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(27),l.pc("bsConfig",l.sc(13,ke)),l.Ib(6),l.pc("bsConfig",l.sc(14,ke)),l.Ib(19),l.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),l.Ib(27),l.pc("ngForOf",l.mc(80,10,t.listData,t.configPgn)),l.Ib(2),l.pc("ngIf",0===t.listData.length),l.Ib(2),l.pc("formGroup",t.myFromGroup),l.Ib(3),l.pc("ngForOf",t.configPgn.pageSizes),l.Ib(3),l.pc("fullScreen",!1))},directives:[g.b,g.a,r.e,n.l,n.m,p.p,p.h,p.v,p.o,p.f,f.c,m.a,p.s,p.y],pipes:[f.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}.content[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{margin-bottom:1.075rem}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),Pe),Oe=((De=function(){function t(a,i,c,o,n,r){e(this,t),this.formBuilder=a,this.datePipe=i,this.payrollService=c,this.route=o,this.router=n,this.spinnerService=r,this.baseUrl=u.a.baseUrl}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.setFormDefaultValues(),this.initButtonsRippleEffect(),this.loadData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({empTitle:[""],elementTitle:[""],elementAmount:[""],activeStartDate:[""],activeEndDate:[""],isActive:[""]})}},{key:"setFormDefaultValues",value:function(){(new Date).getFullYear()}},{key:"resetFormValues",value:function(){this.myForm.reset(),this.setFormDefaultValues()}},{key:"initButtonsRippleEffect",value:function(){document.querySelectorAll(".btn-ripple").forEach(function(e){e.addEventListener("click",function(e){!function(e){var t=e.currentTarget,a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left="".concat(a,"px"),c.style.top="".concat(i,"px"),t.appendChild(c),setTimeout(function(){c.remove()},1e3)}(e)})})}},{key:"myFormSubmit",value:function(){var e=this,t=this.baseUrl+"/api/payrollElValue/create",a={};(a=this.myForm.value).rActiveOperation="Create",a.activeStartDate=a.activeStartDate?this.datePipe.transform(a.activeStartDate,"yyyy-MM-dd").toString().slice(0,10):null,a.activeEndDate=a.activeEndDate?this.datePipe.transform(a.activeEndDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.payrollService.sendPostRequest(t,a).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/payroll/element-value/list"],{relativeTo:e.route})},function(t){console.log(t),e.spinnerService.hide()})}},{key:"loadData",value:function(){}}]),t}()).\u0275fac=function(e){return new(e||De)(l.Ub(p.d),l.Ub(n.e),l.Ub(b.a),l.Ub(r.a),l.Ub(r.c),l.Ub(m.c))},De.\u0275cmp=l.Ob({type:De,selectors:[["app-payroll-element-value-create"]],decls:93,vars:2,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/element-value/list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","empTitle",1,"form-control"],["formControlName","elementTitle",1,"form-control"],["value",""],["value","HRA"],["value","MDL_ALW"],["value","OT_ALW"],["value","LTA"],["value","EA"],["value","UCA_ALW"],["value","FAMILY_ALW"],["value","EDA"],["value","PRJ_ALW"],["value","HOSTEL_ALW"],["value","CHILD_EDU_ALW"],["value","CHILD_HOSTEL_ALW"],["value","PROF_ALW"],["type","text","formControlName","elementAmount",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","activeStartDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","activeEndDate","bsDatepicker","",1,"form-control"],[1,"text-right"],["routerLink","/payroll/element-value/list",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Payroll Element"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payroll Element"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Create"),l.Zb(),l.Zb(),l.Zb(),l.ac(14,"div",9),l.ac(15,"a",10),l.Vb(16,"i",11),l.Lc(17," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(18,"div",12),l.ac(19,"div",13),l.ac(20,"div",14),l.ac(21,"div",15),l.ac(22,"form",16),l.hc("ngSubmit",function(){return t.myFormSubmit()}),l.ac(23,"div",17),l.ac(24,"label",18),l.Lc(25,"Employee"),l.Zb(),l.ac(26,"div",19),l.Vb(27,"input",20),l.Zb(),l.Zb(),l.ac(28,"div",17),l.ac(29,"label",18),l.Lc(30,"Element Title"),l.Zb(),l.ac(31,"div",19),l.ac(32,"select",21),l.ac(33,"option",22),l.Lc(34,"Select"),l.Zb(),l.ac(35,"option",23),l.Lc(36,"House Rent Allowance"),l.Zb(),l.ac(37,"option",24),l.Lc(38,"Medical Allowance"),l.Zb(),l.ac(39,"option",25),l.Lc(40,"Overtime Allowance"),l.Zb(),l.ac(41,"option",26),l.Lc(42,"Leave Travel Allowance"),l.Zb(),l.ac(43,"option",27),l.Lc(44,"Entertainment Allowance"),l.Zb(),l.ac(45,"option",28),l.Lc(46,"Uniform Allowance / Corporate Attire"),l.Zb(),l.ac(47,"option",29),l.Lc(48,"Family Allowance"),l.Zb(),l.ac(49,"option",30),l.Lc(50,"Education Allowance"),l.Zb(),l.ac(51,"option",31),l.Lc(52,"Project Allowance"),l.Zb(),l.ac(53,"option",32),l.Lc(54,"Hostel Allowance"),l.Zb(),l.ac(55,"option",33),l.Lc(56,"Children's education Allowance"),l.Zb(),l.ac(57,"option",34),l.Lc(58,"Children's hostel Allowance"),l.Zb(),l.ac(59,"option",35),l.Lc(60,"Professional pursuit/research Allowance"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(61,"div",17),l.ac(62,"label",18),l.Lc(63,"Amount"),l.Zb(),l.ac(64,"div",19),l.Vb(65,"input",36),l.Zb(),l.Zb(),l.ac(66,"div",17),l.ac(67,"label",18),l.Lc(68,"Start Date"),l.Zb(),l.ac(69,"div",19),l.ac(70,"div",37),l.Vb(71,"input",38),l.Zb(),l.Zb(),l.Zb(),l.ac(72,"div",17),l.ac(73,"label",18),l.Lc(74,"End Date"),l.Zb(),l.ac(75,"div",19),l.ac(76,"div",37),l.Vb(77,"input",39),l.Zb(),l.Zb(),l.Zb(),l.ac(78,"div",40),l.ac(79,"a",41),l.Vb(80,"i",11),l.Lc(81," Cancel"),l.Zb(),l.Lc(82," \xa0 \xa0 "),l.ac(83,"button",42),l.hc("click",function(){return t.resetFormValues()}),l.Vb(84,"i",43),l.Lc(85," Reset "),l.Zb(),l.Lc(86," \xa0 \xa0 "),l.ac(87,"button",44),l.Vb(88,"i",45),l.Lc(89," Save \xa0\xa0\xa0 "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(90,"ngx-spinner",46),l.ac(91,"p",47),l.Lc(92," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(22),l.pc("formGroup",t.myForm),l.Ib(68),l.pc("fullScreen",!1))},directives:[r.e,p.x,p.p,p.h,p.b,p.o,p.f,p.v,p.s,p.y,g.b,g.a,m.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),De),Me=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},xe=((Se=function(){function t(a,i,c,o,n,r){e(this,t),this.formBuilder=a,this.datePipe=i,this.payrollService=c,this.route=o,this.router=n,this.spinnerService=r,this.baseUrl=u.a.baseUrl,this.myFormData={}}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.setFormDefaultValues(),this.initButtonsRippleEffect(),this.getFormData()}},{key:"ngOnDestroy",value:function(){}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],empTitle:[""],elementTitle:[""],elementAmount:[""],activeStartDate:[""],activeEndDate:[""],isActive:[""]})}},{key:"setFormDefaultValues",value:function(){}},{key:"resetFormValues",value:function(){this.myForm.reset(),this.setFormDefaultValues()}},{key:"initButtonsRippleEffect",value:function(){document.querySelectorAll(".btn-ripple").forEach(function(e){e.addEventListener("click",function(e){!function(e){var t=e.currentTarget,a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left="".concat(a,"px"),c.style.top="".concat(i,"px"),t.appendChild(c),setTimeout(function(){c.remove()},1e3)}(e)})})}},{key:"convertToISODateString",value:function(e){var t=e.split("-");return t[2]+"-"+t[1]+t[0]}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/api/payrollElValue/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.payrollService.sendGetRequest(t,{rEntityName:"PayrollElementValue",rActiveOpetation:"read"}).subscribe(function(t){e.myFormData=t,e.myFormData.activeStartDate=e.myFormData.activeStartDate?e.datePipe.transform(e.myFormData.activeStartDate,"dd-MM-yyyy"):null,e.myFormData.activeEndDate=e.myFormData.activeEndDate?e.datePipe.transform(e.myFormData.activeEndDate,"dd-MM-yyyy"):null,e.spinnerService.hide(),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e=this,t=this.baseUrl+"/api/payrollElValue/update/"+this.myForm.value.id;console.log(t);var a={};(a=this.myForm.value).rEntityName="PayrollElementValue",a.rActiveOperation="update",a.activeStartDate=a.activeStartDate?this.datePipe.transform(this.convertToISODateString(a.activeStartDate),"yyyy-MM-dd"):null,a.activeEndDate=a.activeEndDate?this.datePipe.transform(this.convertToISODateString(a.activeEndDate),"yyyy-MM-dd"):null,this.spinnerService.show(),this.payrollService.sendPutRequest(t,a).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/payroll/element-value/list"],{relativeTo:e.route})},function(t){console.log(t),e.spinnerService.hide()})}}]),t}()).\u0275fac=function(e){return new(e||Se)(l.Ub(p.d),l.Ub(n.e),l.Ub(b.a),l.Ub(r.a),l.Ub(r.c),l.Ub(m.c))},Se.\u0275cmp=l.Ob({type:Se,selectors:[["app-payroll-element-value-edit"]],decls:94,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/element-value/list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","id","readonly","","disabled","",1,"form-control"],["type","text","formControlName","empTitle",1,"form-control"],["formControlName","elementTitle",1,"form-control"],["value",""],["value","HRA"],["value","MDL_ALW"],["value","OT_ALW"],["value","LTA"],["value","EA"],["value","UCA_ALW"],["value","FAMILY_ALW"],["value","EDA"],["value","PRJ_ALW"],["value","HOSTEL_ALW"],["value","CHILD_EDU_ALW"],["value","CHILD_HOSTEL_ALW"],["value","PROF_ALW"],["type","text","formControlName","elementAmount",1,"form-control"],[1,"cal-icon"],["type","text","formControlName","activeStartDate","bsDatepicker","",1,"form-control","datetimepicker",3,"bsConfig"],["type","text","formControlName","activeEndDate","bsDatepicker","",1,"form-control",3,"bsConfig"],[1,"text-right"],["routerLink","/payroll/element-value/list",1,"btn","btn-warning","btn-ripple"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Payroll Element"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payroll Element"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Edit"),l.Zb(),l.Zb(),l.Zb(),l.ac(14,"div",9),l.ac(15,"a",10),l.Vb(16,"i",11),l.Lc(17," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(18,"div",12),l.ac(19,"div",13),l.ac(20,"div",14),l.ac(21,"div",15),l.ac(22,"form",16),l.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),l.ac(23,"div",17),l.ac(24,"label",18),l.Lc(25,"ID"),l.Zb(),l.ac(26,"div",19),l.Vb(27,"input",20),l.Zb(),l.Zb(),l.ac(28,"div",17),l.ac(29,"label",18),l.Lc(30,"Employee"),l.Zb(),l.ac(31,"div",19),l.Vb(32,"input",21),l.Zb(),l.Zb(),l.ac(33,"div",17),l.ac(34,"label",18),l.Lc(35,"Element Title"),l.Zb(),l.ac(36,"div",19),l.ac(37,"select",22),l.ac(38,"option",23),l.Lc(39,"Select"),l.Zb(),l.ac(40,"option",24),l.Lc(41,"House Rent Allowance"),l.Zb(),l.ac(42,"option",25),l.Lc(43,"Medical Allowance"),l.Zb(),l.ac(44,"option",26),l.Lc(45,"Overtime Allowance"),l.Zb(),l.ac(46,"option",27),l.Lc(47,"Leave Travel Allowance"),l.Zb(),l.ac(48,"option",28),l.Lc(49,"Entertainment Allowance"),l.Zb(),l.ac(50,"option",29),l.Lc(51,"Uniform Allowance / Corporate Attire"),l.Zb(),l.ac(52,"option",30),l.Lc(53,"Family Allowance"),l.Zb(),l.ac(54,"option",31),l.Lc(55,"Education Allowance"),l.Zb(),l.ac(56,"option",32),l.Lc(57,"Project Allowance"),l.Zb(),l.ac(58,"option",33),l.Lc(59,"Hostel Allowance"),l.Zb(),l.ac(60,"option",34),l.Lc(61,"Children's education Allowance"),l.Zb(),l.ac(62,"option",35),l.Lc(63,"Children's hostel Allowance"),l.Zb(),l.ac(64,"option",36),l.Lc(65,"Professional pursuit/research Allowance"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(66,"div",17),l.ac(67,"label",18),l.Lc(68,"Amount"),l.Zb(),l.ac(69,"div",19),l.Vb(70,"input",37),l.Zb(),l.Zb(),l.ac(71,"div",17),l.ac(72,"label",18),l.Lc(73,"Start Date"),l.Zb(),l.ac(74,"div",19),l.ac(75,"div",38),l.Vb(76,"input",39),l.Zb(),l.Zb(),l.Zb(),l.ac(77,"div",17),l.ac(78,"label",18),l.Lc(79,"End Date"),l.Zb(),l.ac(80,"div",19),l.ac(81,"div",38),l.Vb(82,"input",40),l.Zb(),l.Zb(),l.Zb(),l.ac(83,"div",41),l.ac(84,"a",42),l.Vb(85,"i",11),l.Lc(86," Cancel"),l.Zb(),l.Lc(87," \xa0 \xa0 \xa0 "),l.ac(88,"button",43),l.Vb(89,"i",44),l.Lc(90," Save \xa0\xa0\xa0 "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(91,"ngx-spinner",45),l.ac(92,"p",46),l.Lc(93," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(22),l.pc("formGroup",t.myForm),l.Ib(54),l.pc("bsConfig",l.sc(4,Me)),l.Ib(6),l.pc("bsConfig",l.sc(5,Me)),l.Ib(9),l.pc("fullScreen",!1))},directives:[r.e,p.x,p.p,p.h,p.b,p.o,p.f,p.v,p.s,p.y,g.b,g.a,m.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),Se),_e=((Ce=function(){function t(a,i,c,o,n,r){e(this,t),this.formBuilder=a,this.datePipe=i,this.payrollService=c,this.route=o,this.router=n,this.spinnerService=r,this.baseUrl=u.a.baseUrl,this.myFormData={},this.titleDecode={HRA:"House Rent Allowance",MDL_ALW:"Medical Allowance",OT_ALW:"Overtime Allowance",LTA:"Leave Travel Allowance",EA:"Entertainment Allowance",UCA_ALW:"Uniform Allowance / Corporate Attire",FAMILY_ALW:"Family Allowance",EDA:"Education Allowance",PRJ_ALW:"Project Allowance",HOSTEL_ALW:"Hostel Allowance",CHILD_EDU_ALW:"Children's education Allowance",CHILD_HOSTEL_ALW:"Children's hostel Allowance",PROF_ALW:"Professional pursuit/research Allowance"}}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.setFormDefaultValues(),this.initButtonsRippleEffect(),this.getFormData()}},{key:"ngOnDestroy",value:function(){}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({empTitle:[""],elementTitle:[""],elementAmount:[""],activeStartDate:[""],activeEndDate:[""],isActive:[""]})}},{key:"setFormDefaultValues",value:function(){}},{key:"resetFormValues",value:function(){this.myForm.reset(),this.setFormDefaultValues()}},{key:"initButtonsRippleEffect",value:function(){document.querySelectorAll(".btn-ripple").forEach(function(e){e.addEventListener("click",function(e){!function(e){var t=e.currentTarget,a=e.clientX-e.target.getBoundingClientRect().left,i=e.clientY-e.target.getBoundingClientRect().top,c=document.createElement("span");c.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 1s linear infinite;",c.style.left="".concat(a,"px"),c.style.top="".concat(i,"px"),t.appendChild(c),setTimeout(function(){c.remove()},1e3)}(e)})})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/api/payrollElValue/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.payrollService.sendGetRequest(t,{rEntityName:"PayrollElementValue",rActiveOpetation:"read"}).subscribe(function(t){e.myFormData=t,e.spinnerService.hide(),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e=this,t=this.baseUrl+"/api/payrollElValue/create",a={};(a=this.myForm.value).rActiveOperation="Create",a.activeStartDate=a.activeStartDate?this.datePipe.transform(a.activeStartDate,"yyyy-MM-dd").toString().slice(0,10):null,a.activeEndDate=a.activeEndDate?this.datePipe.transform(a.activeEndDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.payrollService.sendPostRequest(t,a).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/payroll/element-value/list"],{relativeTo:e.route})},function(t){console.log(t),e.spinnerService.hide()})}}]),t}()).\u0275fac=function(e){return new(e||Ce)(l.Ub(p.d),l.Ub(n.e),l.Ub(b.a),l.Ub(r.a),l.Ub(r.c),l.Ub(m.c))},Ce.\u0275cmp=l.Ob({type:Ce,selectors:[["app-payroll-element-value-show"]],decls:107,vars:12,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/element-value/list",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["id","formERP","novalidate","",3,"formGroup","ngSubmit"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"row","fieldsetBorder","logBox"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/payroll/element-value/list",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Payroll Element"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payroll Element"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Show"),l.Zb(),l.Zb(),l.Zb(),l.ac(14,"div",9),l.ac(15,"a",10),l.Vb(16,"i",11),l.Lc(17," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(18,"div",12),l.ac(19,"div",13),l.ac(20,"div",14),l.ac(21,"div",15),l.ac(22,"form",16),l.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),l.ac(23,"fieldset",17),l.Vb(24,"legend"),l.ac(25,"div",18),l.ac(26,"div",19),l.ac(27,"label",20),l.Lc(28,"Employee"),l.Zb(),l.ac(29,"div",21),l.ac(30,"span"),l.Lc(31,": \xa0"),l.Zb(),l.ac(32,"span"),l.Lc(33),l.Zb(),l.Zb(),l.Zb(),l.ac(34,"div",19),l.ac(35,"label",20),l.Lc(36,"Element Title"),l.Zb(),l.ac(37,"div",21),l.ac(38,"span"),l.Lc(39,": \xa0"),l.Zb(),l.ac(40,"span"),l.Lc(41),l.Zb(),l.Zb(),l.Zb(),l.ac(42,"div",19),l.ac(43,"label",20),l.Lc(44,"Amount"),l.Zb(),l.ac(45,"div",21),l.ac(46,"span"),l.Lc(47,": \xa0"),l.Zb(),l.ac(48,"span"),l.Lc(49),l.Zb(),l.Zb(),l.Zb(),l.ac(50,"div",19),l.ac(51,"label",20),l.Lc(52,"Start Date"),l.Zb(),l.ac(53,"div",21),l.ac(54,"span"),l.Lc(55,": \xa0"),l.Zb(),l.ac(56,"span"),l.Lc(57),l.Zb(),l.Zb(),l.Zb(),l.ac(58,"div",19),l.ac(59,"label",20),l.Lc(60,"End Date"),l.Zb(),l.ac(61,"div",21),l.ac(62,"span"),l.Lc(63,": \xa0"),l.Zb(),l.ac(64,"span"),l.Lc(65),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(66,"fieldset",22),l.ac(67,"legend"),l.Lc(68,"System Log Information"),l.Zb(),l.ac(69,"div",23),l.ac(70,"label",24),l.Lc(71,"ID"),l.Zb(),l.ac(72,"div",25),l.ac(73,"span"),l.Lc(74),l.Zb(),l.Zb(),l.Zb(),l.ac(75,"div",23),l.ac(76,"label",24),l.Lc(77,"Creation Time"),l.Zb(),l.ac(78,"div",25),l.ac(79,"span"),l.Lc(80),l.Zb(),l.Zb(),l.Zb(),l.ac(81,"div",23),l.ac(82,"label",24),l.Lc(83,"Creation User"),l.Zb(),l.ac(84,"div",25),l.ac(85,"span"),l.Lc(86),l.Zb(),l.Zb(),l.Zb(),l.ac(87,"div",23),l.ac(88,"label",24),l.Lc(89,"Last Update Time"),l.Zb(),l.ac(90,"div",25),l.ac(91,"span"),l.Lc(92),l.Zb(),l.Zb(),l.Zb(),l.ac(93,"div",23),l.ac(94,"label",24),l.Lc(95,"Last Update User"),l.Zb(),l.ac(96,"div",25),l.ac(97,"span"),l.Lc(98),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(99,"div",26),l.ac(100,"a",27),l.Vb(101,"i",11),l.Lc(102," Close"),l.Zb(),l.Lc(103," \xa0 "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(104,"ngx-spinner",28),l.ac(105,"p",29),l.Lc(106," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(22),l.pc("formGroup",t.myForm),l.Ib(11),l.Mc(t.myFormData.empTitle),l.Ib(8),l.Mc(t.titleDecode[t.myFormData.elementTitle]),l.Ib(8),l.Mc(t.myFormData.elementAmount),l.Ib(8),l.Mc(t.myFormData.activeStartDate),l.Ib(8),l.Mc(t.myFormData.activeEndDate),l.Ib(9),l.Mc(t.myFormData.id),l.Ib(6),l.Mc(t.myFormData.creationDateTime),l.Ib(6),l.Mc(t.myFormData.creationUser),l.Ib(6),l.Mc(t.myFormData.lastUpdateDateTime),l.Ib(6),l.Mc(t.myFormData.lastUpdateUser),l.Ib(6),l.pc("fullScreen",!1))},directives:[r.e,p.x,p.p,p.h,m.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}#formERP[_ngcontent-%COMP%]   fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}#formERP[_ngcontent-%COMP%]   fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}#formERP[_ngcontent-%COMP%]   fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}#formERP[_ngcontent-%COMP%]   .logBox[_ngcontent-%COMP%]{font-size:13px}"]}),Ce),Ne=c("un/a"),we=c("tk/3"),Ee=((Fe=function(){function t(a){e(this,t),this.http=a}return a(t,[{key:"sendGetSelfRequest",value:function(e,t){return console.log("@sendGetSelfRequest"),this.http.get(e,{params:t}).pipe(Object(Ne.a)(3))}},{key:"sendPostRequest",value:function(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}},{key:"sendPutRequest",value:function(e,t){return console.log("@sendPostRequest"),this.http.put(e,t)}},{key:"sendGetRequest",value:function(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(Ne.a)(3))}},{key:"sendDeleteRequest",value:function(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}}]),t}()).\u0275fac=function(e){return new(e||Fe)(l.ec(we.c))},Fe.\u0275prov=l.Qb({token:Fe,factory:Fe.\u0275fac,providedIn:"root"}),Fe);function Ae(e,t){if(1&e){var a=l.bc();l.ac(0,"tr"),l.ac(1,"td"),l.Lc(2),l.Zb(),l.ac(3,"td"),l.Lc(4),l.Zb(),l.ac(5,"td"),l.Lc(6),l.kc(7,"date"),l.Zb(),l.ac(8,"td"),l.Lc(9),l.kc(10,"date"),l.Zb(),l.ac(11,"td"),l.Lc(12),l.kc(13,"date"),l.Zb(),l.ac(14,"td"),l.Lc(15),l.Zb(),l.ac(16,"td"),l.ac(17,"span",56),l.Lc(18),l.Zb(),l.Zb(),l.ac(19,"td"),l.ac(20,"a",57),l.Lc(21,"View"),l.Zb(),l.Lc(22," \xa0 "),l.ac(23,"a",58),l.Vb(24,"i",59),l.Zb(),l.Lc(25,"\xa0\xa0 "),l.ac(26,"a",60),l.hc("click",function(){l.Cc(a);var e=t.$implicit;return l.jc().tempId=e.id}),l.Vb(27,"i",61),l.Zb(),l.Zb(),l.Zb()}if(2&e){var i=t.$implicit,c=t.index,o=l.jc();l.Mb("active",c==o.currentIndex),l.Ib(2),l.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),l.Ib(2),l.Mc(i.hrCrEmp.displayName),l.Ib(2),l.Mc(l.mc(7,11,i.createDate,"yyyy-MM-dd")),l.Ib(3),l.Mc(l.mc(10,14,i.startDate,"yyyy-MM-dd")),l.Ib(3),l.Mc(l.mc(13,17,i.endDate,"yyyy-MM-dd")),l.Ib(3),l.Mc(i.offDays),l.Ib(3),l.Mc(i.offDayBillApprovalStatus),l.Ib(2),l.rc("routerLink","./show/",i.id,""),l.Ib(3),l.rc("routerLink","./edit/",i.id,"")}}function Ue(e,t){1&e&&(l.ac(0,"tr"),l.ac(1,"td",62),l.ac(2,"h5",63),l.Lc(3,"No data found"),l.Zb(),l.Zb(),l.Zb())}function Ve(e,t){if(1&e&&(l.ac(0,"option",64),l.Lc(1),l.Zb()),2&e){var a=t.$implicit;l.pc("value",a),l.Ib(1),l.Nc(" ",a," ")}}var Re,Te,Be,qe,ze,Ge,je=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},Je=((Re=function(){function t(a,i,c){e(this,t),this.offDayBillService=a,this.spinnerService=i,this.toastr=c,this.baseUrl=u.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){this.getListData()}},{key:"getListData",value:function(){var e,t=this,a=this.baseUrl+"/offDayBill/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.offDayBillService.sendGetSelfRequest(a,e).subscribe(function(e){t.listData=e.objectList,t.configPgn.totalItem=e.totalItems,t.configPgn.totalItems=e.totalItems,t.setDisplayLastSequence(),console.log(t.listData),t.spinnerService.hide()},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var t=this,a=this.baseUrl+"/offDayBill/delete/"+e;console.log(a),this.spinnerService.show(),this.offDayBillService.sendDeleteRequest(a,{}).subscribe(function(e){console.log(e),t.spinnerService.hide(),$("#delete_entity").modal("hide"),t.toastr.success("Successfully item is deleted","Success"),t.getListData()},function(e){console.log(e),t.spinnerService.hide()})}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}]),t}()).\u0275fac=function(e){return new(e||Re)(l.Ub(Ee),l.Ub(m.c),l.Ub(F.b))},Re.\u0275cmp=l.Ob({type:Re,selectors:[["app-off-day-bills"]],decls:112,vars:14,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","./create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"badge","badge-success"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Off Day Bill"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payroll"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Off Day Bill"),l.Zb(),l.ac(14,"li",8),l.Lc(15,"List"),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",9),l.ac(17,"div",10),l.ac(18,"button",11),l.Lc(19,"Excel"),l.Zb(),l.ac(20,"button",11),l.Lc(21,"PDF"),l.Zb(),l.ac(22,"button",11),l.Vb(23,"i",12),l.Lc(24," Print"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(25,"div",13),l.ac(26,"div",14),l.ac(27,"div",15),l.ac(28,"div",16),l.ac(29,"div",17),l.Vb(30,"input",18),l.ac(31,"label",19),l.Lc(32,"Employee Code"),l.Zb(),l.Zb(),l.Zb(),l.ac(33,"div",20),l.ac(34,"div",17),l.ac(35,"div",21),l.Vb(36,"input",22),l.Zb(),l.ac(37,"label",19),l.Lc(38,"From"),l.Zb(),l.Zb(),l.Zb(),l.ac(39,"div",20),l.ac(40,"div",17),l.ac(41,"div",21),l.Vb(42,"input",22),l.Zb(),l.ac(43,"label",19),l.Lc(44,"To"),l.Zb(),l.Zb(),l.Zb(),l.ac(45,"div",20),l.ac(46,"a",23),l.Lc(47," Search "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(48,"div",24),l.ac(49,"div",25),l.ac(50,"div",26),l.ac(51,"div",27),l.ac(52,"div",28),l.ac(53,"a",29),l.Vb(54,"i",30),l.Lc(55," New \xa0\xa0\xa0"),l.Zb(),l.Zb(),l.Zb(),l.ac(56,"div",31),l.ac(57,"div",32),l.ac(58,"div",33),l.ac(59,"div",34),l.ac(60,"span",35),l.Lc(61),l.Zb(),l.Zb(),l.Zb(),l.ac(62,"table",36),l.ac(63,"thead"),l.ac(64,"tr"),l.ac(65,"th"),l.Lc(66,"SL"),l.Zb(),l.ac(67,"th"),l.Lc(68,"Employee"),l.Zb(),l.ac(69,"th"),l.Lc(70,"Apply Date"),l.Zb(),l.ac(71,"th"),l.Lc(72,"From Date"),l.Zb(),l.ac(73,"th"),l.Lc(74,"To Date"),l.Zb(),l.ac(75,"th"),l.Lc(76,"Total Days"),l.Zb(),l.ac(77,"th"),l.Lc(78,"Approval Status"),l.Zb(),l.ac(79,"th"),l.Lc(80,"Action"),l.Zb(),l.Zb(),l.Zb(),l.ac(81,"tbody"),l.Jc(82,Ae,28,20,"tr",37),l.kc(83,"paginate"),l.Jc(84,Ue,4,0,"tr",38),l.Zb(),l.Zb(),l.ac(85,"div",39),l.ac(86,"div",40),l.Lc(87," Items per Page "),l.ac(88,"select",41),l.hc("change",function(e){return t.handlePageSizeChange(e)}),l.Jc(89,Ve,2,2,"option",42),l.Zb(),l.Zb(),l.ac(90,"div",43),l.ac(91,"pagination-controls",44),l.hc("pageChange",function(e){return t.handlePageChange(e)}),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(92,"ngx-spinner",45),l.ac(93,"p",46),l.Lc(94," Processing... "),l.Zb(),l.Zb(),l.ac(95,"div",47),l.ac(96,"div",48),l.ac(97,"div",49),l.ac(98,"div",50),l.ac(99,"div",51),l.ac(100,"h3"),l.Lc(101,"Delete Item"),l.Zb(),l.ac(102,"p"),l.Lc(103,"Are you sure want to delete?"),l.Zb(),l.Zb(),l.ac(104,"div",52),l.ac(105,"div",24),l.ac(106,"div",53),l.ac(107,"a",54),l.hc("click",function(){return t.deleteEnityData(t.tempId)}),l.Lc(108,"Delete"),l.Zb(),l.Zb(),l.ac(109,"div",53),l.ac(110,"a",55),l.Lc(111,"Cancel"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(36),l.pc("bsConfig",l.sc(12,je)),l.Ib(6),l.pc("bsConfig",l.sc(13,je)),l.Ib(19),l.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),l.Ib(21),l.pc("ngForOf",l.mc(83,9,t.listData,t.configPgn)),l.Ib(2),l.pc("ngIf",0===t.listData.length),l.Ib(5),l.pc("ngForOf",t.configPgn.pageSizes),l.Ib(3),l.pc("fullScreen",!1))},directives:[r.e,g.b,g.a,n.l,n.m,f.c,m.a,p.s,p.y],pipes:[f.b,n.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),Re),He=c("xrk7"),$e=c("ZOsW"),We=[{path:"",component:s,children:[{path:"employee-salary",component:P},{path:"payslip/:id",component:C},{path:"payroll-items",component:se},{path:"salary-process",component:be},{path:"salary-process-list",component:he},{path:"element-value/list",component:Ie},{path:"element-value/create",component:Oe},{path:"element-value/edit/:id",component:xe},{path:"element-value/show/:id",component:_e},{path:"off-day-bill",component:Je},{path:"off-day-bill/create",component:(qe=function(){function t(a,i,c,o,n,r,l){e(this,t),this.formBuilder=a,this.datePipe=i,this.route=c,this.router=o,this.offDayBillService=n,this.toastr=r,this.commonService=l,this.baseUrl=u.a.baseUrl,this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({hrCrEmp:[{},p.w.required],contactNo:[""],hrCrEmpResponsible:[{},p.w.required],startDate:[""],endDate:[""],remarks:[""]})}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"myFormSubmit",value:function(){var e=this;if((this.myForm.value.endDate.getTime()-this.myForm.value.startDate.getTime())/864e5+1<1)this.toastr.error("End Date must be equal or greater");else if(!this.myForm.invalid){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),a=this.baseUrl+"/offDayBill/save",i={};(i=t).startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.offDayBillService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.router.navigate(["/payroll/off-day-bill"],{relativeTo:e.route})},function(e){console.log(e)})}}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.listData=e.configDDL.append?e.configDDL.listData.concat(t.objectList):t.objectList,e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}}]),t}(),qe.\u0275fac=function(e){return new(e||qe)(l.Ub(p.d),l.Ub(n.e),l.Ub(r.a),l.Ub(r.c),l.Ub(Ee),l.Ub(F.b),l.Ub(He.a))},qe.\u0275cmp=l.Ob({type:qe,selectors:[["app-off-day-bill-create"]],decls:69,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/off-day-bill",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/payroll/off-day-bill",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Off Day Bill"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Off Day Bill"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Payroll"),l.Zb(),l.ac(14,"li",8),l.Lc(15,"Create"),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",9),l.ac(17,"a",10),l.Vb(18,"i",11),l.Lc(19," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(20,"div",12),l.ac(21,"div",13),l.ac(22,"div",14),l.ac(23,"div",15),l.ac(24,"form",16),l.hc("ngSubmit",function(){return t.myFormSubmit()}),l.ac(25,"div",17),l.ac(26,"label",18),l.Lc(27,"Employee "),l.Zb(),l.ac(28,"div",19),l.ac(29,"ng-select",20),l.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),l.Zb(),l.Zb(),l.Zb(),l.ac(30,"div",17),l.ac(31,"label",18),l.Lc(32,"Contact No"),l.Zb(),l.ac(33,"div",19),l.Vb(34,"input",21),l.Zb(),l.Zb(),l.ac(35,"div",17),l.ac(36,"label",18),l.Lc(37,"Responsible Employee "),l.Zb(),l.ac(38,"div",19),l.ac(39,"ng-select",22),l.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),l.Zb(),l.Zb(),l.Zb(),l.ac(40,"div",17),l.ac(41,"label",18),l.Lc(42,"Start Date"),l.Zb(),l.ac(43,"div",19),l.ac(44,"div",23),l.Vb(45,"input",24),l.Zb(),l.Zb(),l.Zb(),l.ac(46,"div",17),l.ac(47,"label",18),l.Lc(48,"End Date"),l.Zb(),l.ac(49,"div",19),l.ac(50,"div",23),l.Vb(51,"input",25),l.Zb(),l.Zb(),l.Zb(),l.ac(52,"div",17),l.ac(53,"label",18),l.Lc(54,"Remarks"),l.Zb(),l.ac(55,"div",19),l.Vb(56,"textarea",26),l.Zb(),l.Zb(),l.ac(57,"div",27),l.ac(58,"a",28),l.Vb(59,"i",11),l.Lc(60," Cancel"),l.Zb(),l.Lc(61," \xa0 \xa0 "),l.ac(62,"button",29),l.hc("click",function(){return t.resetFormValues()}),l.Vb(63,"i",30),l.Lc(64," Reset "),l.Zb(),l.Lc(65," \xa0 \xa0 "),l.ac(66,"button",31),l.Vb(67,"i",32),l.Lc(68," Save \xa0\xa0\xa0 "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(24),l.pc("formGroup",t.myForm),l.Ib(5),l.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),l.Ib(10),l.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[r.e,p.x,p.p,p.h,$e.a,p.o,p.f,p.t,p.b,g.b,g.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),qe)},{path:"off-day-bill/show/:id",component:(Be=function(){function t(a,i,c){e(this,t),this.route=a,this.spinnerService=i,this.offDayBillService=c,this.baseUrl=u.a.baseUrl,this.myData={}}return a(t,[{key:"ngOnInit",value:function(){this.getFormData()}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/offDayBill/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.offDayBillService.sendGetRequest(t,{}).subscribe(function(t){e.myData=t,console.log(e.myData),e.spinnerService.hide()},function(e){console.log(e)})}}]),t}(),Be.\u0275fac=function(e){return new(e||Be)(l.Ub(r.a),l.Ub(m.c),l.Ub(Ee))},Be.\u0275cmp=l.Ob({type:Be,selectors:[["app-off-day-bill-show"]],decls:142,vars:23,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/off-day-bill",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"row","fieldsetBorder","logBox"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/payroll/off-day-bill",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Off Day Bill"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Payroll"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Off Day Bill"),l.Zb(),l.ac(14,"li",8),l.Lc(15,"Show"),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",9),l.ac(17,"a",10),l.Vb(18,"i",11),l.Lc(19," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(20,"div",12),l.ac(21,"div",13),l.ac(22,"div",14),l.ac(23,"div",15),l.ac(24,"fieldset",16),l.Vb(25,"legend"),l.ac(26,"div",17),l.ac(27,"div",18),l.ac(28,"label",19),l.Lc(29,"Employee"),l.Zb(),l.ac(30,"div",20),l.ac(31,"span"),l.Lc(32,": \xa0"),l.Zb(),l.ac(33,"span"),l.Lc(34),l.Zb(),l.Zb(),l.Zb(),l.ac(35,"div",18),l.ac(36,"label",19),l.Lc(37,"Responsible Employee"),l.Zb(),l.ac(38,"div",20),l.ac(39,"span"),l.Lc(40,": \xa0"),l.Zb(),l.ac(41,"span"),l.Lc(42),l.Zb(),l.Zb(),l.Zb(),l.ac(43,"div",18),l.ac(44,"label",19),l.Lc(45,"Contact Number(Given)"),l.Zb(),l.ac(46,"div",20),l.ac(47,"span"),l.Lc(48,": \xa0"),l.Zb(),l.ac(49,"span"),l.Lc(50),l.Zb(),l.Zb(),l.Zb(),l.ac(51,"div",18),l.ac(52,"label",19),l.Lc(53,"Contact Number"),l.Zb(),l.ac(54,"div",20),l.ac(55,"span"),l.Lc(56,": \xa0"),l.Zb(),l.ac(57,"span"),l.Lc(58),l.Zb(),l.Zb(),l.Zb(),l.ac(59,"div",18),l.ac(60,"label",19),l.Lc(61,"Start Date"),l.Zb(),l.ac(62,"div",20),l.ac(63,"span"),l.Lc(64,": \xa0"),l.Zb(),l.ac(65,"span"),l.Lc(66),l.kc(67,"date"),l.Zb(),l.Zb(),l.Zb(),l.ac(68,"div",18),l.ac(69,"label",19),l.Lc(70,"End Date"),l.Zb(),l.ac(71,"div",20),l.ac(72,"span"),l.Lc(73,": \xa0"),l.Zb(),l.ac(74,"span"),l.Lc(75),l.kc(76,"date"),l.Zb(),l.Zb(),l.Zb(),l.ac(77,"div",18),l.ac(78,"label",19),l.Lc(79,"Total Days"),l.Zb(),l.ac(80,"div",20),l.ac(81,"span"),l.Lc(82,": \xa0"),l.Zb(),l.ac(83,"span"),l.Lc(84),l.Zb(),l.Zb(),l.Zb(),l.ac(85,"div",18),l.ac(86,"label",19),l.Lc(87,"Remarks"),l.Zb(),l.ac(88,"div",20),l.ac(89,"span"),l.Lc(90,": \xa0"),l.Zb(),l.ac(91,"span"),l.Lc(92),l.Zb(),l.Zb(),l.Zb(),l.ac(93,"div",18),l.ac(94,"label",19),l.Lc(95,"Approval Status"),l.Zb(),l.ac(96,"div",20),l.ac(97,"span"),l.Lc(98,": \xa0"),l.Zb(),l.ac(99,"span"),l.Lc(100),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(101,"fieldset",21),l.ac(102,"legend"),l.Lc(103,"System Log Information"),l.Zb(),l.ac(104,"div",22),l.ac(105,"label",23),l.Lc(106,"ID"),l.Zb(),l.ac(107,"div",24),l.ac(108,"span"),l.Lc(109),l.Zb(),l.Zb(),l.Zb(),l.ac(110,"div",22),l.ac(111,"label",23),l.Lc(112,"Creation Time"),l.Zb(),l.ac(113,"div",24),l.ac(114,"span"),l.Lc(115),l.Zb(),l.Zb(),l.Zb(),l.ac(116,"div",22),l.ac(117,"label",23),l.Lc(118,"Creation User"),l.Zb(),l.ac(119,"div",24),l.ac(120,"span"),l.Lc(121),l.Zb(),l.Zb(),l.Zb(),l.ac(122,"div",22),l.ac(123,"label",23),l.Lc(124,"Last Update Time"),l.Zb(),l.ac(125,"div",24),l.ac(126,"span"),l.Lc(127),l.Zb(),l.Zb(),l.Zb(),l.ac(128,"div",22),l.ac(129,"label",23),l.Lc(130,"Last Update User"),l.Zb(),l.ac(131,"div",24),l.ac(132,"span"),l.Lc(133),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(134,"div",25),l.ac(135,"a",26),l.Vb(136,"i",11),l.Lc(137," Close"),l.Zb(),l.Lc(138," \xa0 "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(139,"ngx-spinner",27),l.ac(140,"p",28),l.Lc(141," Processing... "),l.Zb(),l.Zb()),2&e&&(l.Ib(34),l.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),l.Ib(8),l.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),l.Ib(8),l.Mc(t.myData.contactNo),l.Ib(8),l.Mc(t.myData.hrCrEmp.mobCode),l.Ib(8),l.Mc(l.mc(67,17,t.myData.startDate,"yyyy-MM-dd")),l.Ib(9),l.Mc(l.mc(76,20,t.myData.endDate,"yyyy-MM-dd")),l.Ib(9),l.Mc(t.myData.offDays),l.Ib(8),l.Mc(t.myData.remarks),l.Ib(8),l.Mc(t.myData.offDayBillApprovalStatus),l.Ib(9),l.Mc(t.myData.id),l.Ib(6),l.Mc(t.myData.createDate),l.Ib(6),l.Mc(t.myData.createdByHrCrEmp.user.username),l.Ib(6),l.Mc(t.myData.updateDateTime),l.Ib(6),l.Mc(t.myData.updatedByHrCrEmp.user.username),l.Ib(6),l.pc("fullScreen",!1))},directives:[r.e,m.a],pipes:[n.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),Be)},{path:"off-day-bill/edit/:id",component:(Te=function(){function t(a,i,c,o,n,r,l,d){e(this,t),this.formBuilder=a,this.datePipe=i,this.route=c,this.router=o,this.offDayBillService=n,this.toastr=r,this.commonService=l,this.spinnerService=d,this.baseUrl=u.a.baseUrl,this.myFormData={},this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",p.w.required],contactNo:[""],hrCrEmpResponsible:["",p.w.required],startDate:[""],endDate:[""],remarks:[""]})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/offDayBill/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.offDayBillService.sendGetRequest(t,{}).subscribe(function(t){e.myFormData=t,console.log(e.myFormData),e.spinnerService.hide(),e.configDDL.listData=[{ddlCode:t.hrCrEmp.id,ddlDescription:t.hrCrEmp.loginCode+"-"+t.hrCrEmp.displayName}],e.myFormData.hrCrEmp=t.hrCrEmp.id,e.configDDL.listData2=[{ddlCode:t.hrCrEmpResponsible.id,ddlDescription:t.hrCrEmpResponsible.loginCode+"-"+t.hrCrEmpResponsible.displayName}],e.myFormData.hrCrEmpResponsible=t.hrCrEmpResponsible.id,e.myFormData.startDate=e.datePipe.transform(t.startDate,"MM-dd-yyyy").toString().slice(0,10),e.myFormData.endDate=e.datePipe.transform(t.endDate,"MM-dd-yyyy").toString().slice(0,10),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e=this;if(!this.myForm.invalid){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),a=this.baseUrl+"/offDayBill/edit",i={};(i=t).startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.offDayBillService.sendPutRequest(a,i).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/payroll/off-day-bill"],{relativeTo:e.route})},function(t){console.log(t),e.spinnerService.hide(),e.toastr.error(t.error.message)})}}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.append?(e.configDDL.listData=e.configDDL.listData.concat(t.objectList),e.configDDL.listData2=e.configDDL.listData2.concat(t.objectList)):(e.configDDL.listData=t.objectList,e.configDDL.listData2=t.objectList),e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}}]),t}(),Te.\u0275fac=function(e){return new(e||Te)(l.Ub(p.d),l.Ub(n.e),l.Ub(r.a),l.Ub(r.c),l.Ub(Ee),l.Ub(F.b),l.Ub(He.a),l.Ub(m.c))},Te.\u0275cmp=l.Ob({type:Te,selectors:[["app-off-day-bill-edit"]],decls:69,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/payroll/off-day-bill",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/payroll/off-day-bill",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"h3",4),l.Lc(5,"Off Day Bill"),l.Zb(),l.ac(6,"ul",5),l.ac(7,"li",6),l.ac(8,"a",7),l.Lc(9,"Home"),l.Zb(),l.Zb(),l.ac(10,"li",8),l.Lc(11,"Off Day Bill"),l.Zb(),l.ac(12,"li",8),l.Lc(13,"Payroll"),l.Zb(),l.ac(14,"li",8),l.Lc(15,"edit"),l.Zb(),l.Zb(),l.Zb(),l.ac(16,"div",9),l.ac(17,"a",10),l.Vb(18,"i",11),l.Lc(19," Back To List"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(20,"div",12),l.ac(21,"div",13),l.ac(22,"div",14),l.ac(23,"div",15),l.ac(24,"form",16),l.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),l.ac(25,"div",17),l.ac(26,"label",18),l.Lc(27,"Employee "),l.Zb(),l.ac(28,"div",19),l.ac(29,"ng-select",20),l.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),l.Zb(),l.Zb(),l.Zb(),l.ac(30,"div",17),l.ac(31,"label",18),l.Lc(32,"Contact No"),l.Zb(),l.ac(33,"div",19),l.Vb(34,"input",21),l.Zb(),l.Zb(),l.ac(35,"div",17),l.ac(36,"label",18),l.Lc(37,"Responsible Employee "),l.Zb(),l.ac(38,"div",19),l.ac(39,"ng-select",22),l.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),l.Zb(),l.Zb(),l.Zb(),l.ac(40,"div",17),l.ac(41,"label",18),l.Lc(42,"Start Date"),l.Zb(),l.ac(43,"div",19),l.ac(44,"div",23),l.Vb(45,"input",24),l.Zb(),l.Zb(),l.Zb(),l.ac(46,"div",17),l.ac(47,"label",18),l.Lc(48,"End Date"),l.Zb(),l.ac(49,"div",19),l.ac(50,"div",23),l.Vb(51,"input",25),l.Zb(),l.Zb(),l.Zb(),l.ac(52,"div",17),l.ac(53,"label",18),l.Lc(54,"Remarks"),l.Zb(),l.ac(55,"div",19),l.Vb(56,"textarea",26),l.Zb(),l.Zb(),l.ac(57,"div",27),l.ac(58,"a",28),l.Vb(59,"i",11),l.Lc(60," Cancel"),l.Zb(),l.Lc(61," \xa0 \xa0 "),l.ac(62,"button",29),l.hc("click",function(){return t.resetFormValues()}),l.Vb(63,"i",30),l.Lc(64," Reset "),l.Zb(),l.Lc(65," \xa0 \xa0 "),l.ac(66,"button",31),l.Vb(67,"i",32),l.Lc(68," Save \xa0\xa0\xa0 "),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&e&&(l.Ib(24),l.pc("formGroup",t.myForm),l.Ib(5),l.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),l.Ib(10),l.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[r.e,p.x,p.p,p.h,$e.a,p.o,p.f,p.t,p.b,g.b,g.a],styles:[""]}),Te)}]}],Ye=((ze=function t(){e(this,t)}).\u0275fac=function(e){return new(e||ze)},ze.\u0275mod=l.Sb({type:ze}),ze.\u0275inj=l.Rb({imports:[[r.f.forChild(We)],r.f]}),ze),Qe=c("njyG"),Xe=c("0jEk"),Ke=c("iHf9"),et=((Ge=function t(){e(this,t)}).\u0275fac=function(e){return new(e||Ge)},Ge.\u0275mod=l.Sb({type:Ge}),Ge.\u0275inj=l.Rb({imports:[[n.c,Ye,Qe.b,g.c.forRoot(),Xe.a,p.u,Ke.b,f.a,m.b,$e.b]]}),Ge)},bNXq:function(t,i,c){"use strict";c.d(i,"a",function(){return d});var o=c("un/a"),n=c("rmPI"),r=c("fXoL"),l=c("tk/3"),d=function(){var t=function(){function t(a){e(this,t),this.http=a}return a(t,[{key:"sendGetRequest",value:function(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(o.a)(3))}},{key:"sendPostRequest",value:function(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}},{key:"sendPutRequest",value:function(e,t){return console.log("@sendPutRequest"),this.http.put(e,t)}},{key:"sendDeleteRequest",value:function(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}},{key:"getPayrollElementAssignmentByEmpId",value:function(e){return this.http.get("".concat(n.a,"/empPayrollAssignment/get/").concat(e))}}]),t}();return t.\u0275fac=function(e){return new(e||t)(r.ec(l.c))},t.\u0275prov=r.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t}()},rmPI:function(e,t,a){"use strict";t.a="http://localhost:9090/hrms_api"}}])}();