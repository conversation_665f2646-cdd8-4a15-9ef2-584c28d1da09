!function(){function t(e,n,r){return(t="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=o(t)););return t}(t,e);if(r){var i=Object.getOwnPropertyDescriptor(r,e);return i.get?i.get.call(n):i.value}})(e,n,r||e)}function e(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&n(t,e)}function n(t,e){return(n=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function r(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var n,r=o(t);if(e){var c=o(this).constructor;n=Reflect.construct(r,arguments,c)}else n=r.apply(this,arguments);return i(this,n)}}function i(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function o(t){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function s(t,e,n){return e&&a(t.prototype,e),n&&a(t,n),t}(window.webpackJsonp=window.webpackJsonp||[]).push([[8],{"3E0/":function(t,n,i){"use strict";i.d(n,"a",function(){return l});var o=i("D0XW"),a=i("7o/Q"),u=i("WMd4");function l(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o.a,r=(e=t)instanceof Date&&!isNaN(+e)?+t-n.now():Math.abs(t);return function(t){return t.lift(new b(r,n))}}var b=function(){function t(e,n){c(this,t),this.delay=e,this.scheduler=n}return s(t,[{key:"call",value:function(t,e){return e.subscribe(new d(t,this.delay,this.scheduler))}}]),t}(),d=function(t){e(i,t);var n=r(i);function i(t,e,r){var o;return c(this,i),(o=n.call(this,t)).delay=e,o.scheduler=r,o.queue=[],o.active=!1,o.errored=!1,o}return s(i,[{key:"_schedule",value:function(t){this.active=!0,this.destination.add(t.schedule(i.dispatch,this.delay,{source:this,destination:this.destination,scheduler:t}))}},{key:"scheduleNotification",value:function(t){if(!0!==this.errored){var e=this.scheduler,n=new f(e.now()+this.delay,t);this.queue.push(n),!1===this.active&&this._schedule(e)}}},{key:"_next",value:function(t){this.scheduleNotification(u.a.createNext(t))}},{key:"_error",value:function(t){this.errored=!0,this.queue=[],this.destination.error(t),this.unsubscribe()}},{key:"_complete",value:function(){this.scheduleNotification(u.a.createComplete()),this.unsubscribe()}}],[{key:"dispatch",value:function(t){for(var e=t.source,n=e.queue,r=t.scheduler,i=t.destination;n.length>0&&n[0].time-r.now()<=0;)n.shift().notification.observe(i);if(n.length>0){var o=Math.max(0,n[0].time-r.now());this.schedule(t,o)}else this.unsubscribe(),e.active=!1}}]),i}(a.a),f=function t(e,n){c(this,t),this.time=e,this.notification=n}},AuF9:function(t,e,n){"use strict";n.d(e,"a",function(){return u});var r=n("un/a"),i=n("AytR"),o=n("fXoL"),a=n("tk/3"),u=function(){var t=function(){function t(e){c(this,t),this.http=e,this.baseUrl=i.a.baseUrl}return s(t,[{key:"getEmployees",value:function(){return this.http.get("".concat(this.baseUrl,"/hrCrEmp/empList"))}},{key:"getEmpListView",value:function(t,e){return this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"sendGetRequest",value:function(t,e){return console.log("@sendGetRequest"),this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"createEmploy",value:function(t){return this.http.post("".concat(this.baseUrl,"/hrCrEmp/create"),t)}},{key:"updateEmploy",value:function(t){return this.http.put("".concat(this.baseUrl,"/hrCrEmp/edit"),t)}},{key:"getEmployeeById",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmp/getData/").concat(t))}},{key:"findEmployeeById",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmp/find/").concat(t))}},{key:"getEmployeeByLoginCode",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmp/findByLoginCode/").concat(t))}},{key:"uploadProfileImage",value:function(t,e){return this.http.post("".concat(this.baseUrl,"/multimedia/profile/").concat(t),e)}},{key:"getAlkpSearchByKeyword",value:function(t){return this.http.get("".concat(this.baseUrl,"/alkp/search/").concat(t))}},{key:"saveEmployeeAssignemntData",value:function(t){return this.http.post("".concat(this.baseUrl,"/hrCrEmpAssgnmnt/create"),t)}},{key:"updateEmployeeAssignment",value:function(t){return this.http.put("".concat(this.baseUrl,"/hrCrEmpAssgnmnt/edit"),t)}},{key:"getLastAssignmentByHrCrEmpId",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmpAssgnmnt/getByHrCrEmp/").concat(t))}},{key:"getEmployeeAssignmentByHrCrEmpId",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmpAssgnmnt/getByHrCrEmpId/").concat(t))}},{key:"saveOrUpdateBankAndPayroll",value:function(t){return this.http.post("".concat(this.baseUrl,"/hrCrEmpAssgnmnt/saveBankAndPayroll"),t)}},{key:"getDesignations",value:function(){return this.http.get("".concat(this.baseUrl,"/designation/getAll"))}},{key:"getALLDivisions",value:function(t){return this.http.get("".concat(this.baseUrl,"/address/division/").concat(t))}},{key:"fetchAllDivision",value:function(){return this.http.get("".concat(this.baseUrl,"/address/division/getAll"))}},{key:"getDistrictByDivId",value:function(t){return this.http.get("".concat(this.baseUrl,"/address/division/").concat(t))}},{key:"getAllDistrict",value:function(t,e){return console.log("@getAllDistrict"),this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"getAllUpazila",value:function(t,e){return console.log("@getAllUpazila"),this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"getAllUnions",value:function(t,e){return console.log("@sendGetRequest"),this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"saveHrCrEmpEdu",value:function(t){return this.http.post("".concat(this.baseUrl,"/hrCrEmpEdu/create"),t)}},{key:"findhrCrEmpEduByEmpId",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmpEdu/find/").concat(t))}},{key:"findhrCrEmpEduById",value:function(t){return this.http.get("".concat(this.baseUrl,"/hrCrEmpEdu/get/").concat(t))}},{key:"edithrCrEmpEducation",value:function(t){return this.http.put("".concat(this.baseUrl,"/hrCrEmpEdu/edit"),t)}},{key:"deleteHrCrEmpEducation",value:function(t){return this.http.delete("".concat(this.baseUrl,"/hrCrEmpEdu/delete/").concat(t))}},{key:"getAllRawAttendanceData",value:function(){return this.http.get("".concat(this.baseUrl,"/attn/findAllBySrcType"))}},{key:"getAllRawAttendanceData2",value:function(t,e){return this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"createAttnViaHr",value:function(t){return this.http.post("".concat(this.baseUrl,"/AttnViaHr/save"),t)}},{key:"getAllViaHrAttnData",value:function(){return this.http.get("".concat(this.baseUrl,"/AttnViaHr/findAllBySrcType"))}},{key:"getAllViaHrAttnData2",value:function(t,e){return this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"getSearchAttn",value:function(t,e){return this.http.get(t,{params:e}).pipe(Object(r.a)(3))}},{key:"createLeave",value:function(t){return this.http.post("".concat(this.baseUrl,"/leaveTrnse/save"),t)}}]),t}();return t.\u0275fac=function(e){return new(e||t)(o.ec(a.c))},t.\u0275prov=o.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t}()},WMd4:function(t,e,n){"use strict";n.d(e,"a",function(){return a});var r=n("EY2u"),i=n("LRne"),o=n("z6cu"),a=function(){var t=function(){function t(e,n,r){c(this,t),this.kind=e,this.value=n,this.error=r,this.hasValue="N"===e}return s(t,[{key:"observe",value:function(t){switch(this.kind){case"N":return t.next&&t.next(this.value);case"E":return t.error&&t.error(this.error);case"C":return t.complete&&t.complete()}}},{key:"do",value:function(t,e,n){switch(this.kind){case"N":return t&&t(this.value);case"E":return e&&e(this.error);case"C":return n&&n()}}},{key:"accept",value:function(t,e,n){return t&&"function"==typeof t.next?this.observe(t):this.do(t,e,n)}},{key:"toObservable",value:function(){switch(this.kind){case"N":return Object(i.a)(this.value);case"E":return Object(o.a)(this.error);case"C":return Object(r.b)()}throw new Error("unexpected notification kind value")}}],[{key:"createNext",value:function(e){return void 0!==e?new t("N",e):t.undefinedValueNotification}},{key:"createError",value:function(e){return new t("E",void 0,e)}},{key:"createComplete",value:function(){return t.completeNotification}}]),t}();return t.completeNotification=new t("C"),t.undefinedValueNotification=new t("N",void 0),t}()},X3zk:function(t,e,n){"use strict";n.r(e),n.d(e,"LoginModule",function(){return nt});var r,i,o=n("ofXK"),a=n("3Pt+"),u=n("tyNb"),l=n("fXoL"),b=((i=function(){function t(){c(this,t)}return s(t,[{key:"ngOnInit",value:function(){}}]),t}()).\u0275fac=function(t){return new(t||i)},i.\u0275cmp=l.Ob({type:i,selectors:[["app-forgot"]],decls:26,vars:0,consts:[[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/logo2.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"account-title"],[1,"account-subtitle"],[1,"form-group"],["type","text",1,"form-control"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn"],[1,"account-footer"],["routerLink","/login/login"]],template:function(t,e){1&t&&(l.ac(0,"div",0),l.ac(1,"a",1),l.Lc(2,"Apply Job"),l.Zb(),l.ac(3,"div",2),l.ac(4,"div",3),l.ac(5,"a",4),l.Vb(6,"img",5),l.Zb(),l.Zb(),l.ac(7,"div",6),l.ac(8,"div",7),l.ac(9,"h3",8),l.Lc(10,"Forgot Password?"),l.Zb(),l.ac(11,"p",9),l.Lc(12,"Enter your email to get a password reset link"),l.Zb(),l.ac(13,"form"),l.ac(14,"div",10),l.ac(15,"label"),l.Lc(16,"Email Address"),l.Zb(),l.Vb(17,"input",11),l.Zb(),l.ac(18,"div",12),l.ac(19,"button",13),l.Lc(20,"Reset Password"),l.Zb(),l.Zb(),l.ac(21,"div",14),l.ac(22,"p"),l.Lc(23,"Remember your password? "),l.ac(24,"a",15),l.Lc(25,"Login"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb())},directives:[u.e,a.x,a.p,a.q],styles:[""]}),i),d=((r=function(){function t(){c(this,t)}return s(t,[{key:"ngOnInit",value:function(){}}]),t}()).\u0275fac=function(t){return new(t||r)},r.\u0275cmp=l.Ob({type:r,selectors:[["app-lockscreen"]],decls:27,vars:0,consts:[[1,"main-wrapper"],[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/logo2.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"lock-user"],["alt","","src","assets/img/profiles/avatar-2.jpg",1,"rounded-circle"],["action","dashboard"],[1,"form-group"],["type","password",1,"form-control"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn"],[1,"account-footer"],["routerLink","/login/register"]],template:function(t,e){1&t&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"a",2),l.Lc(3,"Apply Job"),l.Zb(),l.ac(4,"div",3),l.ac(5,"div",4),l.ac(6,"a",5),l.Vb(7,"img",6),l.Zb(),l.Zb(),l.ac(8,"div",7),l.ac(9,"div",8),l.ac(10,"div",9),l.Vb(11,"img",10),l.ac(12,"h4"),l.Lc(13,"John Doe"),l.Zb(),l.Zb(),l.ac(14,"form",11),l.ac(15,"div",12),l.ac(16,"label"),l.Lc(17,"Password"),l.Zb(),l.Vb(18,"input",13),l.Zb(),l.ac(19,"div",14),l.ac(20,"button",15),l.Lc(21,"Enter"),l.Zb(),l.Zb(),l.ac(22,"div",16),l.ac(23,"p"),l.Lc(24,"Sign in as a different user? "),l.ac(25,"a",17),l.Lc(26,"Login"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb())},directives:[u.e,a.x,a.p,a.q],styles:[""]}),r),f=n("mrSG"),p=n("AuF9"),h=n("d//k"),g=n("3E0/"),m=n("5eHb");function v(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Username is required"),l.Zb())}function y(t,e){if(1&t&&(l.ac(0,"div",30),l.Jc(1,v,2,0,"div",31),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.username.errors.required)}}function Z(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Password is required"),l.Zb())}function w(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Password must be at least 4 characters"),l.Zb())}function k(t,e){if(1&t&&(l.ac(0,"div",30),l.Jc(1,Z,2,0,"div",31),l.Jc(2,w,2,0,"div",31),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.password.errors.required),l.Ib(1),l.pc("ngIf",n.f.password.errors.minlength)}}function C(t,e){1&t&&l.Vb(0,"span",32)}var I,L,O=function(t){return{"is-invalid":t}},E=((L=function(){function t(e,n,r,i,o){c(this,t),this.formBuilder=e,this.toastr=n,this.loginService=r,this.router=i,this.hrcremp=o,this.loading=!1,this.submitted=!1}return s(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.initButtonRippleEffect(),this.loginService.isLoggedIn&&this.router.navigate(["dashboard"])}},{key:"f",get:function(){return this.loginForm.controls}},{key:"initializeForm",value:function(){this.loginForm=this.formBuilder.group({username:["",a.w.required],password:["",[a.w.minLength(4),a.w.required]]})}},{key:"onSubmit",value:function(){return Object(f.a)(this,void 0,void 0,regeneratorRuntime.mark(function t(){var e,n=this;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.submitted=!0,this.loading=!0,!this.loginForm.invalid){t.next=2;break}return t.abrupt("return");case 2:e=Object.assign(this.loginForm.value),console.log("Above Generate Token"),this.loginService.generateToken(e).pipe(Object(g.a)(1300)).subscribe(function(t){console.log("Inside Generate Token"),n.loginService.loginUser(t.token),n.loginService.getCurrentUser().subscribe(function(t){console.log("Inside Current User"),console.log(t),n.loginService.setUser(t);var e=n.loginService.getLoginUserRole();e.includes("ROLE_USER")||e.includes("ROLE_ADMIN")||e.includes("ROLE_SUPER_ADMIN")?(n.loading=!1,n.toastr.success("You are now authenticated","Success",{positionClass:"toast-custom"}),n.router.navigate(["dashboard"]),n.loginService.loginStatusSubject.next(!0)):(n.loading=!1,n.loginService.logout())})},function(t){n.loading=!1,console.log("Error !"),console.log(t),n.toastr.error(""+t.error.message)});case 4:case"end":return t.stop()}},t,this)}))}},{key:"initButtonRippleEffect",value:function(){document.querySelectorAll(".btn-ripple").forEach(function(t){t.addEventListener("click",function(t){!function(t){var e=t.currentTarget,n=t.clientX-t.target.getBoundingClientRect().left,r=t.clientY-t.target.getBoundingClientRect().top,i=document.createElement("span");i.style.cssText="position: absolute; background: #fff; transform: translate(-50%, -50%); pointer-events: none; border-radius: 50%; animation: animate 0.8s linear infinite;",i.style.left="".concat(n,"px"),i.style.top="".concat(r,"px"),e.appendChild(i),setTimeout(function(){i.remove()},800)}(t)})})}}]),t}()).\u0275fac=function(t){return new(t||L)(l.Ub(a.d),l.Ub(m.b),l.Ub(h.a),l.Ub(u.c),l.Ub(p.a))},L.\u0275cmp=l.Ob({type:L,selectors:[["app-login"]],decls:46,vars:11,consts:[[1,"account-content"],[1,"center-screen"],[1,"container"],[1,"row"],[1,"col-12"],[2,"height","0px"],[1,"row","login-box-ct"],[1,"col-sm-12","col-md-6","col-xl-6","login-box-title"],[1,"account-box"],[1,"account-wrapper"],[1,"account-logo"],["href","javascript:"],["src","assets/img/one_direction_logo.png","alt","Dreamguy's Technologies"],[1,"company-title",2,"color","aliceblue"],[1,"account-title",2,"color","aliceblue"],[1,"account-subtitle",2,"color","aliceblue"],[1,"col-6","login-box-form"],[1,"account-title"],[1,"account-subtitle"],[3,"formGroup","ngSubmit"],[1,"form-group"],["formControlName","username","type","text",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"col"],[1,"col-auto"],["formControlName","password","formControlName","password","type","password",1,"form-control",3,"ngClass"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn","btn-ripple",3,"disabled"],["class","spinner-border spinner-border-sm mr-1",4,"ngIf"],[1,"account-footer"],[1,"invalid-feedback"],[4,"ngIf"],[1,"spinner-border","spinner-border-sm","mr-1"]],template:function(t,e){1&t&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"div",2),l.ac(3,"div",3),l.ac(4,"div",4),l.Vb(5,"div",5),l.Zb(),l.ac(6,"div",4),l.ac(7,"div",6),l.ac(8,"div",7),l.ac(9,"div",8),l.ac(10,"div",9),l.ac(11,"div",10),l.ac(12,"a",11),l.Vb(13,"img",12),l.Zb(),l.Zb(),l.ac(14,"h3",13),l.Lc(15,"One Direction Company"),l.Zb(),l.ac(16,"h4",14),l.Lc(17,"( Smart HRMS )"),l.Zb(),l.ac(18,"p",15),l.Lc(19,"Manage Your Resource Smartly"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.ac(20,"div",16),l.ac(21,"div",8),l.ac(22,"div",9),l.ac(23,"h3",17),l.Lc(24,"Login"),l.Zb(),l.ac(25,"p",18),l.Lc(26,"Access your account"),l.Zb(),l.ac(27,"form",19),l.hc("ngSubmit",function(){return e.onSubmit()}),l.ac(28,"div",20),l.ac(29,"label"),l.Lc(30,"Username"),l.Zb(),l.Vb(31,"input",21),l.Jc(32,y,2,1,"div",22),l.Zb(),l.ac(33,"div",20),l.ac(34,"div",3),l.ac(35,"div",23),l.ac(36,"label"),l.Lc(37,"Password"),l.Zb(),l.Zb(),l.Vb(38,"div",24),l.Zb(),l.Vb(39,"input",25),l.Jc(40,k,3,2,"div",22),l.Zb(),l.ac(41,"div",26),l.ac(42,"button",27),l.Jc(43,C,1,0,"span",28),l.Lc(44," Login "),l.Zb(),l.Zb(),l.Vb(45,"div",29),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&t&&(l.Ib(27),l.pc("formGroup",e.loginForm),l.Ib(4),l.pc("ngClass",l.tc(7,O,e.submitted&&e.f.username.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.username.errors),l.Ib(7),l.pc("ngClass",l.tc(9,O,e.submitted&&e.f.password.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.password.errors),l.Ib(2),l.pc("disabled",e.loading),l.Ib(1),l.pc("ngIf",e.loading))},directives:[a.x,a.p,a.h,a.b,a.o,a.f,o.k,o.m],styles:[".center-screen[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh}body[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom right,hsla(0,0%,100%,.3) 30%,rgba(102,126,234,.5) 50%,rgba(118,75,162,.3)),url(/src/assets/img/ROIKBN.jpg)}div.main-wrapper[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom right,hsla(0,0%,100%,.3) 30%,rgba(102,126,234,.5) 50%,rgba(118,75,162,.3)),url(ROIKBN.118a4b073dacdb8abebb.jpg)}div.account-content[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom right,hsla(0,0%,100%,.3) 30%,rgba(176,188,240,.****************) 50%,rgba(43,0,73,.4)),url(ROIKBN.118a4b073dacdb8abebb.jpg)}.account-box[_ngcontent-%COMP%]{background-color:#fff;border:2px solid #ededed;border-radius:4px;box-shadow:0 1px 1px 0 rgb(0 0 0/20%);margin:0 auto;overflow:hidden;width:350px;height:100%}company-title[_ngcontent-%COMP%]{font-size:26px;font-weight:500;margin-bottom:5px;text-align:center}.account-title[_ngcontent-%COMP%]{font-size:20px}.login-box-ct[_ngcontent-%COMP%]   .login-box-title[_ngcontent-%COMP%]{padding-right:0}.login-box-ct[_ngcontent-%COMP%]   .login-box-form[_ngcontent-%COMP%]{padding-left:0}.login-box-ct[_ngcontent-%COMP%]   .login-box-title[_ngcontent-%COMP%]   .account-box[_ngcontent-%COMP%]{margin-right:0}.login-box-ct[_ngcontent-%COMP%]   .login-box-form[_ngcontent-%COMP%]   .account-box[_ngcontent-%COMP%]{margin-left:0}.login-box-title[_ngcontent-%COMP%]   .account-box[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#fff 30%,rgba(0,0,0,.5) 50%),url(login-bg-img-1.4d62662e11e36d35684e.png);background-position:50%;background-repeat:no-repeat;background-size:cover}.btn-ripple[_ngcontent-%COMP%]{position:relative;overflow:hidden}@-webkit-keyframes animate{0%{width:0;height:0;opacity:.5}to{width:700px;height:700px;opacity:0}}@keyframes animate{0%{width:0;height:0;opacity:.5}to{width:700px;height:700px;opacity:0}}"]}),L),x=((I=function(){function t(){c(this,t)}return s(t,[{key:"ngOnInit",value:function(){}}]),t}()).\u0275fac=function(t){return new(t||I)},I.\u0275cmp=l.Ob({type:I,selectors:[["app-otp"]],decls:28,vars:0,consts:[[1,"main-wrapper"],[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/logo2.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"account-title"],[1,"account-subtitle"],["action","dashboard"],[1,"otp-wrap"],["type","text","placeholder","0","maxlength","1",1,"otp-input"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn"],[1,"account-footer"]],template:function(t,e){1&t&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"a",2),l.Lc(3,"Apply Job"),l.Zb(),l.ac(4,"div",3),l.ac(5,"div",4),l.ac(6,"a",5),l.Vb(7,"img",6),l.Zb(),l.Zb(),l.ac(8,"div",7),l.ac(9,"div",8),l.ac(10,"h3",9),l.Lc(11,"OTP"),l.Zb(),l.ac(12,"p",10),l.Lc(13,"Verification your account"),l.Zb(),l.ac(14,"form",11),l.ac(15,"div",12),l.Vb(16,"input",13),l.Vb(17,"input",13),l.Vb(18,"input",13),l.Vb(19,"input",13),l.Zb(),l.ac(20,"div",14),l.ac(21,"button",15),l.Lc(22,"Enter"),l.Zb(),l.Zb(),l.ac(23,"div",16),l.ac(24,"p"),l.Lc(25,"Not yet received? "),l.ac(26,"a"),l.Lc(27,"Resend OTP"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb())},directives:[u.e,a.x,a.p,a.q],styles:[""]}),I),P=n("SxV6"),A=n("ur0Y");function U(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Username is required"),l.Zb())}function j(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,U,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.username.errors.required)}}function R(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Email is required"),l.Zb())}function N(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Email must be a valid email address"),l.Zb())}function S(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,R,2,0,"div",27),l.Jc(2,N,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.email.errors.required),l.Ib(1),l.pc("ngIf",n.f.email.errors.email)}}function _(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"First Name is required"),l.Zb())}function q(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,_,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.firstName.errors.required)}}function V(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Last Name is required"),l.Zb())}function M(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,V,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.lastName.errors.required)}}function J(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Phone is required"),l.Zb())}function B(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,J,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.phone.errors.required)}}function D(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Password is required"),l.Zb())}function T(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Password must be at least 4 characters"),l.Zb())}function F(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,D,2,0,"div",27),l.Jc(2,T,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.password.errors.required),l.Ib(1),l.pc("ngIf",n.f.password.errors.minlength)}}function H(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Confirm Password is required"),l.Zb())}function z(t,e){1&t&&(l.ac(0,"div"),l.Lc(1,"Passwords must match"),l.Zb())}function G(t,e){if(1&t&&(l.ac(0,"div",26),l.Jc(1,H,2,0,"div",27),l.Jc(2,z,2,0,"div",27),l.Zb()),2&t){var n=l.jc();l.Ib(1),l.pc("ngIf",n.f.confirmPassword.errors.required),l.Ib(1),l.pc("ngIf",n.f.confirmPassword.errors.mustMatch)}}function K(t,e){1&t&&l.Vb(0,"span",28)}var X,Y,Q,W=function(t){return{"is-invalid":t}},$=[{path:"",component:E},{path:"forgot",component:b},{path:"register",component:(X=function(){function t(e,n,r,i,o){c(this,t),this.formBuilder=e,this.loginService=n,this.route=r,this.router=i,this.toastr=o,this.loading=!1,this.submitted=!1}return s(t,[{key:"ngOnInit",value:function(){this.id=this.route.snapshot.params.id,this.isAddMode=!this.id,this.initializeForm()}},{key:"initializeForm",value:function(){var t=this,e={validators:Object(A.a)("password","confirmPassword")};this.form=this.formBuilder.group({username:["",a.w.required],firstName:["",a.w.required],lastName:["",a.w.required],phone:["",a.w.required],email:["",[a.w.required,a.w.email]],password:["",[a.w.minLength(4),a.w.required]],confirmPassword:["",a.w.required]},e),this.isAddMode||this.loginService.getById(this.id).pipe(Object(P.a)()).subscribe(function(e){return t.form.patchValue(e)})}},{key:"f",get:function(){return this.form.controls}},{key:"onSubmit",value:function(){this.submitted=!0,console.log("OK"+this.isAddMode),this.form.invalid||(this.loading=!0,this.isAddMode&&this.createUser())}},{key:"createUser",value:function(){var t=this;this.loginService.register(this.form.value).pipe(Object(P.a)()).subscribe(function(){t.toastr.success("Successfully Registered"),t.router.navigate(["../"],{relativeTo:t.route})},function(e){t.toastr.error(e.error.message)}).add(function(){return t.loading=!1})}}]),t}(),X.\u0275fac=function(t){return new(t||X)(l.Ub(a.d),l.Ub(h.a),l.Ub(u.a),l.Ub(u.c),l.Ub(m.b))},X.\u0275cmp=l.Ob({type:X,selectors:[["app-register"]],decls:59,vars:31,consts:[[1,"main-wrapper"],[1,"account-content"],["routerLink","/login/joblist",1,"btn","btn-primary","apply-btn"],[1,"container"],[1,"account-logo"],["routerLink","/dashboard"],["src","assets/img/w_logo.png","alt","Dreamguy's Technologies"],[1,"account-box"],[1,"account-wrapper"],[1,"account-title"],[1,"account-subtitle"],[3,"formGroup","ngSubmit"],[1,"form-group"],["formControlName","username","type","text",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],["formControlName","email","type","email",1,"form-control",3,"ngClass"],["formControlName","firstName","type","text",1,"form-control",3,"ngClass"],["formControlName","lastName","type","text",1,"form-control",3,"ngClass"],["formControlName","phone","type","number",1,"form-control",3,"ngClass"],["type","password","formControlName","password",1,"form-control",3,"ngClass"],["type","password","formControlName","confirmPassword",1,"form-control",3,"ngClass"],[1,"form-group","text-center"],["type","submit",1,"btn","btn-primary","account-btn",3,"disabled"],["class","spinner-border spinner-border-sm mr-1",4,"ngIf"],[1,"account-footer"],["routerLink","/login/login"],[1,"invalid-feedback"],[4,"ngIf"],[1,"spinner-border","spinner-border-sm","mr-1"]],template:function(t,e){1&t&&(l.ac(0,"div",0),l.ac(1,"div",1),l.ac(2,"a",2),l.Lc(3,"Apply Job"),l.Zb(),l.ac(4,"div",3),l.ac(5,"div",4),l.ac(6,"a",5),l.Vb(7,"img",6),l.Zb(),l.Zb(),l.ac(8,"div",7),l.ac(9,"div",8),l.ac(10,"h3",9),l.Lc(11,"Register"),l.Zb(),l.ac(12,"p",10),l.Lc(13,"Access to our dashboard"),l.Zb(),l.ac(14,"form",11),l.hc("ngSubmit",function(){return e.onSubmit()}),l.ac(15,"div",12),l.ac(16,"label"),l.Lc(17,"Username"),l.Zb(),l.Vb(18,"input",13),l.Jc(19,j,2,1,"div",14),l.Zb(),l.ac(20,"div",12),l.ac(21,"label"),l.Lc(22,"Email"),l.Zb(),l.Vb(23,"input",15),l.Jc(24,S,3,2,"div",14),l.Zb(),l.ac(25,"div",12),l.ac(26,"label"),l.Lc(27,"First Name"),l.Zb(),l.Vb(28,"input",16),l.Jc(29,q,2,1,"div",14),l.Zb(),l.ac(30,"div",12),l.ac(31,"label"),l.Lc(32,"Last Name"),l.Zb(),l.Vb(33,"input",17),l.Jc(34,M,2,1,"div",14),l.Zb(),l.ac(35,"div",12),l.ac(36,"label"),l.Lc(37,"Phone"),l.Zb(),l.Vb(38,"input",18),l.Jc(39,B,2,1,"div",14),l.Zb(),l.ac(40,"div",12),l.ac(41,"label"),l.Lc(42,"Password"),l.Zb(),l.Vb(43,"input",19),l.Jc(44,F,3,2,"div",14),l.Zb(),l.ac(45,"div",12),l.ac(46,"label"),l.Lc(47,"Repeat Password"),l.Zb(),l.Vb(48,"input",20),l.Jc(49,G,3,2,"div",14),l.Zb(),l.ac(50,"div",21),l.ac(51,"button",22),l.Jc(52,K,1,0,"span",23),l.Lc(53," Register "),l.Zb(),l.Zb(),l.ac(54,"div",24),l.ac(55,"p"),l.Lc(56,"Already have an account? "),l.ac(57,"a",25),l.Lc(58,"Login"),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb(),l.Zb()),2&t&&(l.Ib(14),l.pc("formGroup",e.form),l.Ib(4),l.pc("ngClass",l.tc(17,W,e.submitted&&e.f.username.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.username.errors),l.Ib(4),l.pc("ngClass",l.tc(19,W,e.submitted&&e.f.email.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.email.errors),l.Ib(4),l.pc("ngClass",l.tc(21,W,e.submitted&&e.f.firstName.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.firstName.errors),l.Ib(4),l.pc("ngClass",l.tc(23,W,e.submitted&&e.f.lastName.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.lastName.errors),l.Ib(4),l.pc("ngClass",l.tc(25,W,e.submitted&&e.f.phone.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.phone.errors),l.Ib(4),l.pc("ngClass",l.tc(27,W,e.submitted&&e.f.password.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.password.errors),l.Ib(4),l.pc("ngClass",l.tc(29,W,e.submitted&&e.f.confirmPassword.errors)),l.Ib(1),l.pc("ngIf",e.submitted&&e.f.confirmPassword.errors),l.Ib(2),l.pc("disabled",e.loading),l.Ib(1),l.pc("ngIf",e.loading))},directives:[u.e,a.x,a.p,a.h,a.b,a.o,a.f,o.k,o.m,a.t],styles:[""]}),X)},{path:"otp",component:x},{path:"lockscreen",component:d}],tt=((Y=function t(){c(this,t)}).\u0275fac=function(t){return new(t||Y)},Y.\u0275mod=l.Sb({type:Y}),Y.\u0275inj=l.Rb({imports:[[u.f.forChild($)],u.f]}),Y),et=n("tk/3"),nt=((Q=function t(){c(this,t)}).\u0275fac=function(t){return new(t||Q)},Q.\u0275mod=l.Sb({type:Q}),Q.\u0275inj=l.Rb({imports:[[o.c,tt,a.j,a.u,et.d]]}),Q)},mrSG:function(t,e,n){"use strict";function r(t,e,n,r){var i,o=arguments.length,c=o<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)c=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(c=(o<3?i(c):o>3?i(e,n,c):i(e,n))||c);return o>3&&c&&Object.defineProperty(e,n,c),c}function i(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function o(t,e,n,r){return new(n||(n=Promise))(function(i,o){function c(t){try{s(r.next(t))}catch(e){o(e)}}function a(t){try{s(r.throw(t))}catch(e){o(e)}}function s(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n(function(t){t(e)})).then(c,a)}s((r=r.apply(t,e||[])).next())})}n.d(e,"b",function(){return r}),n.d(e,"c",function(){return i}),n.d(e,"a",function(){return o})},"un/a":function(n,i,a){"use strict";a.d(i,"a",function(){return l});var u=a("7o/Q");function l(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;return function(e){return e.lift(new b(t,e))}}var b=function(){function t(e,n){c(this,t),this.count=e,this.source=n}return s(t,[{key:"call",value:function(t,e){return e.subscribe(new d(t,this.count,this.source))}}]),t}(),d=function(n){e(a,n);var i=r(a);function a(t,e,n){var r;return c(this,a),(r=i.call(this,t)).count=e,r.source=n,r}return s(a,[{key:"error",value:function(e){if(!this.isStopped){var n=this.source,r=this.count;if(0===r)return t(o(a.prototype),"error",this).call(this,e);r>-1&&(this.count=r-1),n.subscribe(this._unsubscribeAndRecycle())}}}]),a}(u.a)}}])}();