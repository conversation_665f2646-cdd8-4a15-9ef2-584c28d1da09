(window.webpackJsonp=window.webpackJsonp||[]).push([[23],{Nyzf:function(t,e,o){"use strict";o.r(e),o.d(e,"AttendancereportsModule",function(){return U});var r=o("ofXK"),n=o("tyNb"),c=o("fXoL");let a=(()=>{let t=class{constructor(t){this.ngZone=t,window.onresize=t=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){}onResize(t){this.innerHeight=t.target.innerHeight+"px"}};return t.\u0275fac=function(e){return new(e||t)(c.Ub(c.G))},t.\u0275cmp=c.Ob({type:t,selectors:[["app-attendancereports"]],decls:1,vars:0,template:function(t,e){1&t&&c.Vb(0,"router-outlet")},directives:[n.g],styles:[""]}),t})();var i=o("3Pt+"),s=o("AytR"),l=o("tk/3");let b=(()=>{class t{constructor(t){this.http=t,this.baseUrl=s.a.baseUrl}getReportBetweenDate(t){return this.http.post(`${this.baseUrl}/attnReport/reportBetweenDate`,t)}getReportBetweenDateAllEmp(t){return console.log(t),this.http.post(`${this.baseUrl}/attnReport/reportBetweenDateAllEmp`,t)}}return t.\u0275fac=function(e){return new(e||t)(c.ec(l.c))},t.\u0275prov=c.Qb({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var p=o("JqCM"),d=o("oW1M");function u(t,e){if(1&t&&(c.ac(0,"td",35),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function f(t,e){if(1&t&&(c.ac(0,"td",36),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function m(t,e){if(1&t&&(c.ac(0,"td",37),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function h(t,e){if(1&t&&(c.ac(0,"td",36),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function g(t,e){if(1&t&&(c.ac(0,"td",36),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function Z(t,e){if(1&t&&(c.ac(0,"tr"),c.ac(1,"td",28),c.Lc(2),c.Zb(),c.ac(3,"td",28),c.Lc(4),c.Zb(),c.ac(5,"td",28),c.Lc(6),c.Zb(),c.ac(7,"td",28),c.Lc(8),c.Zb(),c.Jc(9,u,2,1,"td",32),c.Jc(10,f,2,1,"td",33),c.Jc(11,m,2,1,"td",34),c.Jc(12,h,2,1,"td",33),c.Jc(13,g,2,1,"td",33),c.Zb()),2&t){const t=e.$implicit,o=e.index;c.Ib(2),c.Mc(o+1),c.Ib(2),c.Mc(t.createDate),c.Ib(2),c.Mc(t.inTime),c.Ib(2),c.Mc(t.outTime),c.Ib(1),c.pc("ngIf",1==t.isColor),c.Ib(1),c.pc("ngIf",2==t.isColor),c.Ib(1),c.pc("ngIf",null==t.isColor),c.Ib(1),c.pc("ngIf",4==t.isColor),c.Ib(1),c.pc("ngIf",3==t.isColor)}}function D(t,e){if(1&t&&(c.ac(0,"td",36),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function y(t,e){if(1&t&&(c.ac(0,"td",37),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function w(t,e){if(1&t&&(c.ac(0,"td",38),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function v(t,e){if(1&t&&(c.ac(0,"td",37),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function S(t,e){if(1&t&&(c.ac(0,"td",37),c.Lc(1),c.Zb()),2&t){const t=c.jc().$implicit;c.Ib(1),c.Nc(" ",t.attnDayStsFinalType," ")}}function I(t,e){if(1&t&&(c.ac(0,"tr"),c.ac(1,"td"),c.Lc(2),c.Zb(),c.ac(3,"td"),c.Lc(4),c.Zb(),c.ac(5,"td"),c.Lc(6),c.Zb(),c.ac(7,"td"),c.Lc(8),c.Zb(),c.ac(9,"td"),c.Lc(10),c.Zb(),c.ac(11,"td"),c.Lc(12),c.Zb(),c.Jc(13,D,2,1,"td",33),c.Jc(14,y,2,1,"td",34),c.Jc(15,w,2,1,"td",35),c.Jc(16,v,2,1,"td",34),c.Jc(17,S,2,1,"td",34),c.Zb()),2&t){const t=e.$implicit,o=e.index;c.Ib(2),c.Mc(o+1),c.Ib(2),c.Mc(t.hrCrEmpId?t.hrCrEmpId.displayName:"null"),c.Ib(2),c.Mc(t.hrCrEmpId?t.hrCrEmpId.loginCode:"null"),c.Ib(2),c.Mc(t.createDate),c.Ib(2),c.Mc(t.inTime),c.Ib(2),c.Mc(t.outTime),c.Ib(1),c.pc("ngIf",1==t.isColor),c.Ib(1),c.pc("ngIf",2==t.isColor),c.Ib(1),c.pc("ngIf",null==t.isColor),c.Ib(1),c.pc("ngIf",4==t.isColor),c.Ib(1),c.pc("ngIf",3==t.isColor)}}function L(t,e){1&t&&(c.ac(0,"tr"),c.ac(1,"td",39),c.ac(2,"h5",40),c.Lc(3,"No data found"),c.Zb(),c.Zb(),c.Zb())}const F=[{path:"",component:a,children:[{path:"attendance-reports",component:(()=>{class t{constructor(t,e,o,r,n){this.formBuilder=t,this.attnReport=e,this.datePipe=o,this.router=r,this.spinnerService=n,this.contextPth=s.a.contextPath,this.AttnReport=[]}ngOnInit(){$(".floating").length>0&&$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur"),this.initializeForm(),this.loadAttnDada(),this.selInitialDefaultValues()}initializeForm(){this.reportBetweenDateForm=this.formBuilder.group({fromDate:["",[i.w.required]],toDate:["",[i.w.required]]})}selInitialDefaultValues(){let t=new Date,e=new Date(t.getFullYear(),t.getMonth(),1).toLocaleDateString("en-US"),o=t.toLocaleDateString("en-US");this.reportBetweenDateForm=this.formBuilder.group({fromDate:[e],toDate:[o]}),setTimeout(()=>{const t=document.querySelector(".fromDate");t.focus(),t.blur()},500),setTimeout(()=>{const t=document.querySelector(".toDate");t.focus(),t.blur()},700)}formSubmit(){let t=Object.assign(this.reportBetweenDateForm.value),e=this.datePipe.transform(t.fromDate,"yyyy-MM-dd").toString().slice(0,10);t.fromDate=e;let o=this.datePipe.transform(t.toDate,"yyyy-MM-dd").toString().slice(0,10);t.toDate=o,this.spinnerService.show(),this.attnReport.getReportBetweenDate(t).subscribe(t=>{this.AttnReport=t,this.spinnerService.hide()})}loadAttnDada(){let t=Object.assign(this.reportBetweenDateForm.value);t.fromDate=null,t.toDate=null,this.todate=new Date,this.startdate=new Date(this.todate.getFullYear(),this.todate.getMonth(),1),this.todate=this.todate.toLocaleDateString("en-US"),this.startdate=this.startdate.toLocaleDateString("en-US"),this.spinnerService.show(),this.attnReport.getReportBetweenDate(t).subscribe(t=>{this.AttnReport=t,this.spinnerService.hide()})}renderJasperReport(){let t=this.reportBetweenDateForm.value,e=t.toDate,o=this.datePipe.transform(t.fromDate,"yyyy-MM-dd").toString().slice(0,10),r=this.datePipe.transform(e,"yyyy-MM-dd").toString().slice(0,10);const n=this.router.serializeUrl(this.router.createUrlTree([this.contextPth+"reports/attendance-jsr-rpt"],{queryParams:{fromDate:o,toDate:r}}));window.open(n,"_blank")}}return t.\u0275fac=function(e){return new(e||t)(c.Ub(i.d),c.Ub(b),c.Ub(r.e),c.Ub(n.c),c.Ub(p.c))},t.\u0275cmp=c.Ob({type:t,selectors:[["app-attendancereports-list"]],decls:60,vars:3,consts:[[1,"page-wrapper"],[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["_ngcontent-pcf-c133","",1,"btn-group","btn-group-sm"],["_ngcontent-pcf-c133","",1,"btn","btn-white"],["_ngcontent-pcf-c133","",1,"btn","btn-white",3,"click"],["_ngcontent-pcf-c133","",1,"fa","fa-print","fa-lg"],[3,"formGroup","ngSubmit"],[1,"row","filter-row","mb-4"],[1,"col-sm-6","col-md-3"],[1,"form-group","form-focus"],[1,"cal-icon"],["formControlName","fromDate","type","text","bsDatepicker","",1,"form-control","floating","datetimepicker","fromDate","dateEntryField"],[1,"focus-label"],["formControlName","toDate","type","text","bsDatepicker","",1,"form-control","floating","datetimepicker","toDate","dateEntryField"],["type","submit",1,"btn","btn-primary","submit-btn"],[1,"row"],[1,"col-md-12"],[1,"table-responsive"],[1,"table","table-striped","custom-table"],[1,"tdthwidth"],[4,"ngFor","ngForOf"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["style","color: green;",4,"ngIf"],["style","color: rgb(231, 163, 15);",4,"ngIf"],["style","color: rgb(228, 19, 36);",4,"ngIf"],[2,"color","green"],[2,"color","rgb(231, 163, 15)"],[2,"color","rgb(228, 19, 36)"]],template:function(t,e){1&t&&(c.ac(0,"div",0),c.ac(1,"div",1),c.ac(2,"div",2),c.ac(3,"div",3),c.ac(4,"div",4),c.ac(5,"h3",5),c.Lc(6,"Attendance Reports"),c.Zb(),c.ac(7,"ul",6),c.ac(8,"li",7),c.ac(9,"a",8),c.Lc(10,"Dashboard"),c.Zb(),c.Zb(),c.ac(11,"li",9),c.Lc(12,"Attendance Reports"),c.Zb(),c.Zb(),c.Zb(),c.ac(13,"div",10),c.ac(14,"div",11),c.ac(15,"button",12),c.Lc(16,"CSV"),c.Zb(),c.ac(17,"button",13),c.hc("click",function(){return e.renderJasperReport()}),c.Lc(18,"PDF"),c.Zb(),c.ac(19,"button",12),c.Vb(20,"i",14),c.Lc(21," Print"),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.ac(22,"form",15),c.hc("ngSubmit",function(){return e.formSubmit()}),c.ac(23,"div",16),c.ac(24,"div",17),c.ac(25,"div",18),c.ac(26,"div",19),c.Vb(27,"input",20),c.Zb(),c.ac(28,"label",21),c.Lc(29,"Form Date"),c.Zb(),c.Zb(),c.Zb(),c.ac(30,"div",17),c.ac(31,"div",18),c.ac(32,"div",19),c.Vb(33,"input",22),c.Zb(),c.ac(34,"label",21),c.Lc(35,"To Date"),c.Zb(),c.Zb(),c.Zb(),c.ac(36,"div",17),c.ac(37,"button",23),c.Lc(38," Search "),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.ac(39,"div",24),c.ac(40,"div",25),c.ac(41,"div",26),c.ac(42,"table",27),c.ac(43,"thead"),c.ac(44,"tr"),c.ac(45,"th",28),c.Lc(46,"#"),c.Zb(),c.ac(47,"th",28),c.Lc(48,"Date"),c.Zb(),c.ac(49,"th",28),c.Lc(50,"In Time"),c.Zb(),c.ac(51,"th",28),c.Lc(52,"Out Time"),c.Zb(),c.ac(53,"th",28),c.Lc(54,"Status"),c.Zb(),c.Zb(),c.Zb(),c.ac(55,"tbody"),c.Jc(56,Z,14,9,"tr",29),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.ac(57,"ngx-spinner",30),c.ac(58,"p",31),c.Lc(59," Processing... "),c.Zb(),c.Zb(),c.Zb()),2&t&&(c.Ib(22),c.pc("formGroup",e.reportBetweenDateForm),c.Ib(34),c.pc("ngForOf",e.AttnReport),c.Ib(1),c.pc("fullScreen",!1))},directives:[n.e,i.x,i.p,i.h,i.b,d.b,i.o,i.f,d.a,r.l,p.a,r.m],styles:["input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}.tdthwidth[_ngcontent-%COMP%]{width:40px;max-width:40px}"]}),t})()},{path:"attendance-reports-hr-admin",component:(()=>{class t{constructor(t,e,o,r,n){this.formBuilder=t,this.attnReport=e,this.datePipe=o,this.router=r,this.spinnerService=n,this.contextPth=s.a.contextPath,this.AttnReport=[]}ngOnInit(){$(".floating").length>0&&$(".floating").on("focus blur",function(t){$(this).parents(".form-focus").toggleClass("focused","focus"===t.type||this.value.length>0)}).trigger("blur"),this.initializeForm(),this.setValue(),this.loadAttnDada()}initializeForm(){this.reportBetweenDateForm=this.formBuilder.group({fromDate:["",[i.w.required]],toDate:["",[i.w.required]],empCode:["",[i.w.required]]})}setValue(){let t=new Date,e=t.toLocaleDateString("en-US"),o=new Date(t.getFullYear(),t.getMonth(),1).toLocaleDateString("en-US");this.reportBetweenDateForm.get("fromDate").setValue(o),this.reportBetweenDateForm.get("toDate").setValue(e),setTimeout(()=>{const t=document.querySelector(".fromDate");t.focus(),t.blur()},500),setTimeout(()=>{const t=document.querySelector(".toDate");t.focus(),t.blur()},700)}formSubmit(){let t=Object.assign(this.reportBetweenDateForm.value),e=this.datePipe.transform(t.fromDate,"yyyy-MM-dd").toString().slice(0,10);t.fromDate=e;let o=this.datePipe.transform(t.toDate,"yyyy-MM-dd").toString().slice(0,10);t.toDate=o,this.spinnerService.show(),this.attnReport.getReportBetweenDateAllEmp(t).subscribe(t=>{this.AttnReport=t,this.spinnerService.hide()})}loadAttnDada(){let t=Object.assign(this.reportBetweenDateForm.value);t.fromDate=null,t.toDate=null,this.todate=new Date,this.startdate=new Date(this.todate.getFullYear(),this.todate.getMonth(),1),this.todate=this.todate.toLocaleDateString("en-US"),this.startdate=this.startdate.toLocaleDateString("en-US"),this.spinnerService.show(),this.attnReport.getReportBetweenDateAllEmp(t).subscribe(t=>{this.AttnReport=t,this.spinnerService.hide(),console.log(this.AttnReport)})}renderJasperReport(){let t=this.reportBetweenDateForm.value,e=t.toDate,o=this.datePipe.transform(t.fromDate,"yyyy-MM-dd").toString().slice(0,10),r=this.datePipe.transform(e,"yyyy-MM-dd").toString().slice(0,10);const n=this.router.serializeUrl(this.router.createUrlTree([this.contextPth+"reports/attendance-jsr-rpt"],{queryParams:{fromDate:o,toDate:r}}));window.open(n,"_blank")}}return t.\u0275fac=function(e){return new(e||t)(c.Ub(i.d),c.Ub(b),c.Ub(r.e),c.Ub(n.c),c.Ub(p.c))},t.\u0275cmp=c.Ob({type:t,selectors:[["app-attendancereports-list"]],decls:71,vars:4,consts:[[1,"page-wrapper"],[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["_ngcontent-pcf-c133","",1,"btn-group","btn-group-sm"],["_ngcontent-pcf-c133","",1,"btn","btn-white"],["_ngcontent-pcf-c133","",1,"btn","btn-white",3,"click"],["_ngcontent-pcf-c133","",1,"fa","fa-print","fa-lg"],[3,"formGroup","ngSubmit"],[1,"row","filter-row","mb-4"],[1,"col-sm-6","col-md-3"],[1,"form-group","form-focus"],["formControlName","empCode","type","text",1,"form-control","floating"],[1,"focus-label"],[1,"cal-icon"],["formControlName","fromDate","type","text","bsDatepicker","",1,"form-control","floating","datetimepicker","fromDate"],["formControlName","toDate","type","text","bsDatepicker","",1,"form-control","floating","datetimepicker","toDate"],["type","submit",1,"btn","btn-primary","submit-btn"],[1,"row"],[1,"col-md-12"],[1,"table-responsive"],[1,"table","table-striped","custom-table"],[4,"ngFor","ngForOf"],[4,"ngIf"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["style","color: green;",4,"ngIf"],["style","color: rgb(231, 163, 15);",4,"ngIf"],["style","color: rgb(228, 19, 36);",4,"ngIf"],[2,"color","green"],[2,"color","rgb(231, 163, 15)"],[2,"color","rgb(228, 19, 36)"],["colspan","10"],[2,"text-align","center"]],template:function(t,e){1&t&&(c.ac(0,"div",0),c.ac(1,"div",1),c.ac(2,"div",2),c.ac(3,"div",3),c.ac(4,"div",4),c.ac(5,"h3",5),c.Lc(6,"Attendance Reports (Hr Admin)"),c.Zb(),c.ac(7,"ul",6),c.ac(8,"li",7),c.ac(9,"a",8),c.Lc(10,"Dashboard"),c.Zb(),c.Zb(),c.ac(11,"li",9),c.Lc(12,"Attendance Reports (Hr Admin)"),c.Zb(),c.Zb(),c.Zb(),c.ac(13,"div",10),c.ac(14,"div",11),c.ac(15,"button",12),c.Lc(16,"CSV"),c.Zb(),c.ac(17,"button",13),c.hc("click",function(){return e.renderJasperReport()}),c.Lc(18,"PDF"),c.Zb(),c.ac(19,"button",12),c.Vb(20,"i",14),c.Lc(21," Print"),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.ac(22,"form",15),c.hc("ngSubmit",function(){return e.formSubmit()}),c.ac(23,"div",16),c.ac(24,"div",17),c.ac(25,"div",18),c.ac(26,"div"),c.Vb(27,"input",19),c.Zb(),c.ac(28,"label",20),c.Lc(29,"Emp Code"),c.Zb(),c.Zb(),c.Zb(),c.ac(30,"div",17),c.ac(31,"div",18),c.ac(32,"div",21),c.Vb(33,"input",22),c.Zb(),c.ac(34,"label",20),c.Lc(35,"Form Date"),c.Zb(),c.Zb(),c.Zb(),c.ac(36,"div",17),c.ac(37,"div",18),c.ac(38,"div",21),c.Vb(39,"input",23),c.Zb(),c.ac(40,"label",20),c.Lc(41,"To Date"),c.Zb(),c.Zb(),c.Zb(),c.ac(42,"div",17),c.ac(43,"button",24),c.Lc(44," Search "),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.ac(45,"div",25),c.ac(46,"div",26),c.ac(47,"div",27),c.ac(48,"table",28),c.ac(49,"thead"),c.ac(50,"tr"),c.ac(51,"th"),c.Lc(52,"#"),c.Zb(),c.ac(53,"th"),c.Lc(54,"Emp"),c.Zb(),c.ac(55,"th"),c.Lc(56,"Emp Code"),c.Zb(),c.ac(57,"th"),c.Lc(58,"Date"),c.Zb(),c.ac(59,"th"),c.Lc(60,"In Time"),c.Zb(),c.ac(61,"th"),c.Lc(62,"Out Time"),c.Zb(),c.ac(63,"th"),c.Lc(64,"Status"),c.Zb(),c.Zb(),c.Zb(),c.ac(65,"tbody"),c.Jc(66,I,18,11,"tr",29),c.Jc(67,L,4,0,"tr",30),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.Zb(),c.ac(68,"ngx-spinner",31),c.ac(69,"p",32),c.Lc(70," Processing... "),c.Zb(),c.Zb(),c.Zb()),2&t&&(c.Ib(22),c.pc("formGroup",e.reportBetweenDateForm),c.Ib(44),c.pc("ngForOf",e.AttnReport),c.Ib(1),c.pc("ngIf",0===e.AttnReport.length),c.Ib(1),c.pc("fullScreen",!1))},directives:[n.e,i.x,i.p,i.h,i.b,i.o,i.f,d.b,d.a,r.l,r.m,p.a],styles:["input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}.tdthwidth[_ngcontent-%COMP%]{width:40px;max-width:40px}"]}),t})()}]}];let M=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=c.Sb({type:t}),t.\u0275inj=c.Rb({imports:[[n.f.forChild(F)],n.f]}),t})();var C=o("njyG"),x=o("iHf9"),R=o("0jEk"),P=o("e/yp");let U=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=c.Sb({type:t}),t.\u0275inj=c.Rb({imports:[[r.c,M,r.c,i.j,R.a,i.u,x.b,P.a,d.c.forRoot(),C.b,l.d,p.b]]}),t})()}}]);