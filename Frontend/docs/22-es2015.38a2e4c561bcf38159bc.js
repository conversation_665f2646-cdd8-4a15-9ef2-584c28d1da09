(window.webpackJsonp=window.webpackJsonp||[]).push([[22],{"oV/K":function(e,t,a){"use strict";a.r(t),a.d(t,"ApprovalModule",function(){return K});var i=a("ofXK"),c=a("njyG"),o=a("oW1M"),r=a("0jEk"),n=a("3Pt+"),l=a("iHf9"),s=a("oOf3"),p=a("JqCM"),b=a("tyNb"),d=a("fXoL");const g=function(e){return{height:e}};let u=(()=>{let e=class{constructor(e){this.ngZone=e,window.onresize=e=>{this.ngZone.run(()=>{this.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}getScreenHeight(){this.innerHeight=window.innerHeight+"px"}ngOnInit(){}onResize(e){this.innerHeight=e.target.innerHeight+"px"}};return e.\u0275fac=function(t){return new(t||e)(d.Ub(d.G))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.hc("resized",function(e){return t.onResize(e)}),d.Vb(1,"router-outlet"),d.Zb()),2&e&&d.pc("ngStyle",d.tc(1,g,t.innerHeight))},directives:[i.n,b.g],styles:[""]}),e})();var m=a("AytR"),h=a("NllD"),v=a("5eHb");function f(e,t){if(1&e){const e=d.bc();d.ac(0,"tr"),d.ac(1,"td"),d.Lc(2),d.Zb(),d.ac(3,"td",54),d.Lc(4),d.Zb(),d.ac(5,"td"),d.Lc(6),d.Zb(),d.ac(7,"td"),d.Lc(8),d.Zb(),d.ac(9,"td"),d.Lc(10),d.Zb(),d.ac(11,"td"),d.Lc(12),d.Zb(),d.ac(13,"td"),d.Lc(14," \xa0 "),d.ac(15,"a",55),d.Vb(16,"i",56),d.Zb(),d.Lc(17,"\xa0\xa0 "),d.ac(18,"a",57),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().tempId=a.id}),d.Vb(19,"i",58),d.Zb(),d.Zb(),d.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=d.jc();d.Mb("active",a==i.currentIndex),d.Ib(2),d.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),d.Ib(2),d.Mc(e.id),d.Ib(2),d.Mc(e.code),d.Ib(2),d.Mc(e.processName),d.Ib(2),d.Mc(e.sequence),d.Ib(2),d.Mc(e.remarks),d.Ib(3),d.rc("routerLink","./edit/",e.id,"")}}function D(e,t){1&e&&(d.ac(0,"tr"),d.ac(1,"td",59),d.ac(2,"h5",60),d.Lc(3,"No data found"),d.Zb(),d.Zb(),d.Zb())}function L(e,t){if(1&e&&(d.ac(0,"option",61),d.Lc(1),d.Zb()),2&e){const e=t.$implicit;d.pc("value",e),d.Ib(1),d.Nc(" ",e," ")}}let Z=(()=>{class e{constructor(e,t,a){this.spinnerService=e,this.toastr=t,this.approvalProcessService=a,this.baseUrl=m.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.getListData()}getListData(){let e=this.baseUrl+"/approvalProc/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalProcessService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}deleteEnityData(e){let t=this.baseUrl+"/approvalProc/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalProcessService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getListData()},e=>{console.log(e),this.spinnerService.hide()})}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(p.c),d.Ub(v.b),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-process"]],decls:96,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-process/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Process"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Process"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"List"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"div",10),d.ac(18,"button",11),d.Lc(19,"Excel"),d.Zb(),d.ac(20,"button",11),d.Lc(21,"PDF"),d.Zb(),d.ac(22,"button",11),d.Vb(23,"i",12),d.Lc(24," Print"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(25,"div",13),d.ac(26,"div",14),d.ac(27,"div",15),d.ac(28,"div",16),d.ac(29,"div",17),d.Vb(30,"input",18),d.ac(31,"label",19),d.Lc(32,"Code"),d.Zb(),d.Zb(),d.Zb(),d.ac(33,"div",20),d.ac(34,"a",21),d.Lc(35," Search "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(36,"div",22),d.ac(37,"div",23),d.ac(38,"div",24),d.ac(39,"div",25),d.ac(40,"div",26),d.ac(41,"a",27),d.Vb(42,"i",28),d.Lc(43," New \xa0\xa0\xa0"),d.Zb(),d.Zb(),d.Zb(),d.ac(44,"div",29),d.ac(45,"div",30),d.ac(46,"div",31),d.ac(47,"div",32),d.ac(48,"span",33),d.Lc(49),d.Zb(),d.Zb(),d.Zb(),d.ac(50,"table",34),d.ac(51,"thead"),d.ac(52,"tr"),d.ac(53,"th"),d.Lc(54,"SL"),d.Zb(),d.ac(55,"th"),d.Lc(56,"Code"),d.Zb(),d.ac(57,"th"),d.Lc(58,"Process Name"),d.Zb(),d.ac(59,"th"),d.Lc(60,"Sequence"),d.Zb(),d.ac(61,"th"),d.Lc(62,"Remarks"),d.Zb(),d.ac(63,"th"),d.Lc(64,"Action"),d.Zb(),d.Zb(),d.Zb(),d.ac(65,"tbody"),d.Jc(66,f,20,9,"tr",35),d.kc(67,"paginate"),d.Jc(68,D,4,0,"tr",36),d.Zb(),d.Zb(),d.ac(69,"div",37),d.ac(70,"div",38),d.Lc(71," Items per Page "),d.ac(72,"select",39),d.hc("change",function(e){return t.handlePageSizeChange(e)}),d.Jc(73,L,2,2,"option",40),d.Zb(),d.Zb(),d.ac(74,"div",41),d.ac(75,"pagination-controls",42),d.hc("pageChange",function(e){return t.handlePageChange(e)}),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(76,"ngx-spinner",43),d.ac(77,"p",44),d.Lc(78," Processing... "),d.Zb(),d.Zb(),d.ac(79,"div",45),d.ac(80,"div",46),d.ac(81,"div",47),d.ac(82,"div",48),d.ac(83,"div",49),d.ac(84,"h3"),d.Lc(85,"Delete Item"),d.Zb(),d.ac(86,"p"),d.Lc(87,"Are you sure want to delete?"),d.Zb(),d.Zb(),d.ac(88,"div",50),d.ac(89,"div",22),d.ac(90,"div",51),d.ac(91,"a",52),d.hc("click",function(){return t.deleteEnityData(t.tempId)}),d.Lc(92,"Delete"),d.Zb(),d.Zb(),d.ac(93,"div",51),d.ac(94,"a",53),d.Lc(95,"Cancel"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(49),d.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),d.Ib(17),d.pc("ngForOf",d.mc(67,7,t.listData,t.configPgn)),d.Ib(2),d.pc("ngIf",0===t.listData.length),d.Ib(5),d.pc("ngForOf",t.configPgn.pageSizes),d.Ib(3),d.pc("fullScreen",!1))},directives:[b.e,i.l,i.m,s.c,p.a,n.s,n.y],pipes:[s.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),S=(()=>{class e{constructor(e,t,a,i,c){this.formBuilder=e,this.datePipe=t,this.route=a,this.router=i,this.approvalProcessService=c,this.baseUrl=m.a.baseUrl}ngOnInit(){this.initializeForm()}initializeForm(){this.myForm=this.formBuilder.group({code:[""],processName:[""],sequence:[""],remarks:[""]})}myFormSubmit(){let e=this.baseUrl+"/approvalProc/save",t={};t=this.myForm.value,t.rActiveOperation="Create",this.approvalProcessService.sendPostRequest(e,t).subscribe(e=>{console.log(e),this.router.navigate(["/approval/approval-process"],{relativeTo:this.route})},e=>{console.log(e)})}resetFormValues(){this.myForm.reset()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(n.d),d.Ub(i.e),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-create"]],decls:57,vars:1,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-process",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","code",1,"form-control"],["type","text","formControlName","processName",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/approval/approval-process",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Process"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Process"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Create"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.myFormSubmit()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Code"),d.Zb(),d.ac(28,"div",19),d.Vb(29,"input",20),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Process Name"),d.Zb(),d.ac(33,"div",19),d.Vb(34,"input",21),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Sequence"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",17),d.ac(41,"label",18),d.Lc(42,"Remarks"),d.Zb(),d.ac(43,"div",19),d.Vb(44,"textarea",23),d.Zb(),d.Zb(),d.ac(45,"div",24),d.ac(46,"a",25),d.Vb(47,"i",11),d.Lc(48," Cancel"),d.Zb(),d.Lc(49," \xa0 \xa0 "),d.ac(50,"button",26),d.hc("click",function(){return t.resetFormValues()}),d.Vb(51,"i",27),d.Lc(52," Reset "),d.Zb(),d.Lc(53," \xa0 \xa0 "),d.ac(54,"button",28),d.Vb(55,"i",29),d.Lc(56," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm))},directives:[b.e,n.x,n.p,n.h,n.b,n.o,n.f,n.t],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),P=(()=>{class e{constructor(e,t,a,i,c){this.formBuilder=e,this.route=t,this.router=a,this.approvalProcessService=i,this.spinnerService=c,this.baseUrl=m.a.baseUrl,this.myFormData={}}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],code:[""],processName:[""],sequence:[""],remarks:[""]})}getFormData(){let e=this.baseUrl+"/approvalProc/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalProcessService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide(),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}resetFormValues(){this.getFormData()}saveUpdatedFormData(){let e=this.baseUrl+"/approvalProc/save";console.log(e);let t={};t=this.myForm.value,this.spinnerService.show(),this.approvalProcessService.sendPostRequest(e,t).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/approval/approval-process"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide()})}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(n.d),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a),d.Ub(p.c))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-edit"]],decls:57,vars:1,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-process",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","code",1,"form-control"],["type","text","formControlName","processName",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/approval/approval-process",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Process"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Process"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Edit"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Code"),d.Zb(),d.ac(28,"div",19),d.Vb(29,"input",20),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Process Namee"),d.Zb(),d.ac(33,"div",19),d.Vb(34,"input",21),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Sequence"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",17),d.ac(41,"label",18),d.Lc(42,"Remarks"),d.Zb(),d.ac(43,"div",19),d.Vb(44,"textarea",23),d.Zb(),d.Zb(),d.ac(45,"div",24),d.ac(46,"a",25),d.Vb(47,"i",11),d.Lc(48," Cancel"),d.Zb(),d.Lc(49," \xa0 \xa0 "),d.ac(50,"button",26),d.hc("click",function(){return t.resetFormValues()}),d.Vb(51,"i",27),d.Lc(52," Reset "),d.Zb(),d.Lc(53," \xa0 \xa0 "),d.ac(54,"button",28),d.Vb(55,"i",29),d.Lc(56," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm))},directives:[b.e,n.x,n.p,n.h,n.b,n.o,n.f,n.t],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();function y(e,t){if(1&e){const e=d.bc();d.ac(0,"tr"),d.ac(1,"td"),d.Lc(2),d.Zb(),d.ac(3,"td",54),d.Lc(4),d.Zb(),d.ac(5,"td"),d.Lc(6),d.Zb(),d.ac(7,"td"),d.Lc(8),d.Zb(),d.ac(9,"td"),d.Lc(10),d.Zb(),d.ac(11,"td"),d.Lc(12),d.Zb(),d.ac(13,"td"),d.Lc(14),d.Zb(),d.ac(15,"td"),d.Lc(16),d.Zb(),d.ac(17,"td"),d.Lc(18),d.Zb(),d.ac(19,"td"),d.Lc(20),d.Zb(),d.ac(21,"td"),d.Lc(22),d.Zb(),d.ac(23,"td"),d.Lc(24," \xa0 "),d.ac(25,"a",55),d.Vb(26,"i",56),d.Zb(),d.Lc(27,"\xa0\xa0 "),d.ac(28,"a",57),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().tempId=a.id}),d.Vb(29,"i",58),d.Zb(),d.Zb(),d.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=d.jc();d.Mb("active",a==i.currentIndex),d.Ib(2),d.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),d.Ib(2),d.Mc(e.id),d.Ib(2),d.Mc(e.referenceEntity?e.referenceEntity:"null"),d.Ib(2),d.Mc(e.approvalProcess.processName?e.approvalProcess.processName:"null"),d.Ib(2),d.Mc(e.approvalStep.approvalGroupName?e.approvalStep.approvalGroupName:"null"),d.Ib(2),d.Mc(e.sentToStepId?e.sentToStepId.approvalGroupName:"null"),d.Ib(2),d.Mc(e.approvalStepApprover?e.approvalStepApprover.bindLevel:"null"),d.Ib(2),d.Mc(e.approvalStepAction?e.approvalStepAction.activityStatusTitle:"null"),d.Ib(2),d.Mc(e.actionStatus?e.actionStatus:"null"),d.Ib(2),d.Mc(e.remarks?e.remarks:"null"),d.Ib(2),d.Mc(e.sequence?e.sequence:"null"),d.Ib(3),d.rc("routerLink","./edit/",e.id,"")}}function N(e,t){1&e&&(d.ac(0,"tr"),d.ac(1,"td",59),d.ac(2,"h5",60),d.Lc(3,"No data found"),d.Zb(),d.Zb(),d.Zb())}function A(e,t){if(1&e&&(d.ac(0,"option",61),d.Lc(1),d.Zb()),2&e){const e=t.$implicit;d.pc("value",e),d.Ib(1),d.Nc(" ",e," ")}}let F=(()=>{class e{constructor(e,t,a){this.spinnerService=e,this.toastr=t,this.approvalService=a,this.baseUrl=m.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.getListData()}getListData(){let e=this.baseUrl+"/approvalProcTnxHtry/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide(),console.log(this.listData)},e=>{console.log(e)})}deleteEnityData(e){}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(p.c),d.Ub(v.b),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-process-tnx-history"]],decls:106,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-process-tnx-history/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Process Tnx History"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Process Tnx History"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"List"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"div",10),d.ac(18,"button",11),d.Lc(19,"Excel"),d.Zb(),d.ac(20,"button",11),d.Lc(21,"PDF"),d.Zb(),d.ac(22,"button",11),d.Vb(23,"i",12),d.Lc(24," Print"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(25,"div",13),d.ac(26,"div",14),d.ac(27,"div",15),d.ac(28,"div",16),d.ac(29,"div",17),d.Vb(30,"input",18),d.ac(31,"label",19),d.Lc(32,"Code"),d.Zb(),d.Zb(),d.Zb(),d.ac(33,"div",20),d.ac(34,"a",21),d.Lc(35," Search "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(36,"div",22),d.ac(37,"div",23),d.ac(38,"div",24),d.ac(39,"div",25),d.ac(40,"div",26),d.ac(41,"a",27),d.Vb(42,"i",28),d.Lc(43," New \xa0\xa0\xa0"),d.Zb(),d.Zb(),d.Zb(),d.ac(44,"div",29),d.ac(45,"div",30),d.ac(46,"div",31),d.ac(47,"div",32),d.ac(48,"span",33),d.Lc(49),d.Zb(),d.Zb(),d.Zb(),d.ac(50,"table",34),d.ac(51,"thead"),d.ac(52,"tr"),d.ac(53,"th"),d.Lc(54,"SL"),d.Zb(),d.ac(55,"th"),d.Lc(56,"Referance Entity Code"),d.Zb(),d.ac(57,"th"),d.Lc(58,"Approval Process"),d.Zb(),d.ac(59,"th"),d.Lc(60,"Approval Step"),d.Zb(),d.ac(61,"th"),d.Lc(62,"Send To Step"),d.Zb(),d.ac(63,"th"),d.Lc(64,"Approval Step Approver"),d.Zb(),d.ac(65,"th"),d.Lc(66,"Approval Step Action"),d.Zb(),d.ac(67,"th"),d.Lc(68,"Action Status"),d.Zb(),d.ac(69,"th"),d.Lc(70,"Remarks"),d.Zb(),d.ac(71,"th"),d.Lc(72,"Sequence"),d.Zb(),d.ac(73,"th"),d.Lc(74,"Action"),d.Zb(),d.Zb(),d.Zb(),d.ac(75,"tbody"),d.Jc(76,y,30,14,"tr",35),d.kc(77,"paginate"),d.Jc(78,N,4,0,"tr",36),d.Zb(),d.Zb(),d.ac(79,"div",37),d.ac(80,"div",38),d.Lc(81," Items per Page "),d.ac(82,"select",39),d.hc("change",function(e){return t.handlePageSizeChange(e)}),d.Jc(83,A,2,2,"option",40),d.Zb(),d.Zb(),d.ac(84,"div",41),d.ac(85,"pagination-controls",42),d.hc("pageChange",function(e){return t.handlePageChange(e)}),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(86,"ngx-spinner",43),d.ac(87,"p",44),d.Lc(88," Processing... "),d.Zb(),d.Zb(),d.ac(89,"div",45),d.ac(90,"div",46),d.ac(91,"div",47),d.ac(92,"div",48),d.ac(93,"div",49),d.ac(94,"h3"),d.Lc(95,"Delete Item"),d.Zb(),d.ac(96,"p"),d.Lc(97,"Are you sure want to delete?"),d.Zb(),d.Zb(),d.ac(98,"div",50),d.ac(99,"div",22),d.ac(100,"div",51),d.ac(101,"a",52),d.Lc(102,"Delete"),d.Zb(),d.Zb(),d.ac(103,"div",51),d.ac(104,"a",53),d.Lc(105,"Cancel"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(49),d.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),d.Ib(27),d.pc("ngForOf",d.mc(77,7,t.listData,t.configPgn)),d.Ib(2),d.pc("ngIf",0===t.listData.length),d.Ib(5),d.pc("ngForOf",t.configPgn.pageSizes),d.Ib(3),d.pc("fullScreen",!1))},directives:[b.e,i.l,i.m,s.c,p.a,n.s,n.y],pipes:[s.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();var I=a("xrk7"),x=a("ZOsW");let k=(()=>{class e{constructor(e,t,a,i,c){this.formBuilder=e,this.route=t,this.commonService=a,this.router=i,this.approvalService=c,this.baseUrl=m.a.baseUrl,this._initConfigDDL()}ngOnInit(){this.initializeForm()}initializeForm(){this.myForm=this.formBuilder.group({approvalGroupName:["",n.w.required],approvalProcess:[{},n.w.required],preApprovalNode:["",n.w.required],thisApprovalNode:["",n.w.required],nextApprovalNode:["",n.w.required],putOnStatusPositive:["",n.w.required],putOnStatusNegative:["",n.w.required],sequence:["",n.w.required],isActive:["",n.w.required]})}myFormSubmit(){if(this.myForm.invalid)return;let e={};e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null}),this.approvalService.sendPostRequest(this.baseUrl+"/approvalStep/save",e).subscribe(e=>{console.log(e),this.router.navigate(["/approval/approval-step"],{relativeTo:this.route})},e=>{console.log(e)})}resetFormValues(){this.myForm.reset()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?this.configDDL.listData=this.configDDL.listData.concat(e.objectList):(this.configDDL.listData=e.objectList,console.log(this.configDDL.listData)),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getApprovalProcess(){return this.myForm.get("approvalProcess")}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(n.d),d.Ub(b.a),d.Ub(I.a),d.Ub(b.c),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-create"]],decls:82,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","approvalGroupName",1,"form-control"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","preApprovalNode",1,"form-control"],["type","number","formControlName","thisApprovalNode",1,"form-control"],["type","number","formControlName","nextApprovalNode",1,"form-control"],["type","number","formControlName","putOnStatusPositive",1,"form-control"],["type","text","formControlName","putOnStatusNegative",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Create"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.myFormSubmit()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Group Name"),d.Zb(),d.ac(28,"div",19),d.Vb(29,"input",20),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Approval Process *"),d.Zb(),d.ac(33,"div",19),d.ac(34,"ng-select",21),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/approvalProc/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Pre Approval Node"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",17),d.ac(41,"label",18),d.Lc(42,"This Approval Node"),d.Zb(),d.ac(43,"div",19),d.Vb(44,"input",23),d.Zb(),d.Zb(),d.ac(45,"div",17),d.ac(46,"label",18),d.Lc(47,"Next Approval Node"),d.Zb(),d.ac(48,"div",19),d.Vb(49,"input",24),d.Zb(),d.Zb(),d.ac(50,"div",17),d.ac(51,"label",18),d.Lc(52,"Positive Statuse"),d.Zb(),d.ac(53,"div",19),d.Vb(54,"input",25),d.Zb(),d.Zb(),d.ac(55,"div",17),d.ac(56,"label",18),d.Lc(57,"Negative Status"),d.Zb(),d.ac(58,"div",19),d.Vb(59,"input",26),d.Zb(),d.Zb(),d.ac(60,"div",17),d.ac(61,"label",18),d.Lc(62,"Sequence"),d.Zb(),d.ac(63,"div",19),d.Vb(64,"input",27),d.Zb(),d.Zb(),d.ac(65,"div",17),d.ac(66,"label",18),d.Lc(67,"Is Active"),d.Zb(),d.ac(68,"div",19),d.Vb(69,"input",28),d.Zb(),d.Zb(),d.ac(70,"div",29),d.ac(71,"a",30),d.Vb(72,"i",11),d.Lc(73," Cancel"),d.Zb(),d.Lc(74," \xa0 \xa0 "),d.ac(75,"button",31),d.hc("click",function(){return t.resetFormValues()}),d.Vb(76,"i",32),d.Lc(77," Reset "),d.Zb(),d.Lc(78," \xa0 \xa0 "),d.ac(79,"button",33),d.Vb(80,"i",34),d.Lc(81," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm),d.Ib(10),d.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[b.e,n.x,n.p,n.h,n.b,n.o,n.f,x.a,n.t,n.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),C=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.route=t,this.router=a,this.approvalService=i,this.spinnerService=c,this.commonService=o,this.baseUrl=m.a.baseUrl,this.myFormData={},this._initConfigDDL()}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],approvalGroupName:["",n.w.required],approvalProcess:["",n.w.required],preApprovalNode:["",n.w.required],thisApprovalNode:["",n.w.required],nextApprovalNode:["",n.w.required],putOnStatusPositive:["",n.w.required],putOnStatusNegative:["",n.w.required],sequence:["",n.w.required],isActive:["",n.w.required]})}getFormData(){let e=this.baseUrl+"/approvalStep/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData);let t=[{id:e.approvalProcess.id,processName:e.approvalProcess.processName}];console.log(t),this.configDDL.listData=t,this.myFormData.approvalProcess=e.approvalProcess.id,this.spinnerService.hide(),this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}resetFormValues(){this.getFormData()}saveUpdatedFormData(){if(this.myForm.invalid)return;let e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null});console.log(e);let t=this.baseUrl+"/approvalStep/edit";console.log(t);let a={};a=e,this.spinnerService.show(),this.approvalService.sendPutRequest(t,a).subscribe(e=>{console.log(e),this.spinnerService.hide(),this.router.navigate(["/approval/approval-step"],{relativeTo:this.route})},e=>{console.log(e),this.spinnerService.hide()})}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?this.configDDL.listData=this.configDDL.listData.concat(e.objectList):(this.configDDL.listData=e.objectList,console.log(this.configDDL.listData)),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getApprovalProcess(){return this.myForm.get("approvalProcess")}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(n.d),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a),d.Ub(p.c),d.Ub(I.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-edit"]],decls:82,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","approvalGroupName",1,"form-control"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","preApprovalNode",1,"form-control"],["type","number","formControlName","thisApprovalNode",1,"form-control"],["type","number","formControlName","nextApprovalNode",1,"form-control"],["type","number","formControlName","putOnStatusPositive",1,"form-control"],["type","text","formControlName","putOnStatusNegative",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Edit"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Group Name"),d.Zb(),d.ac(28,"div",19),d.Vb(29,"input",20),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Approval Process *"),d.Zb(),d.ac(33,"div",19),d.ac(34,"ng-select",21),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/approvalProc/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Pre Approval Node"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",17),d.ac(41,"label",18),d.Lc(42,"This Approval Node"),d.Zb(),d.ac(43,"div",19),d.Vb(44,"input",23),d.Zb(),d.Zb(),d.ac(45,"div",17),d.ac(46,"label",18),d.Lc(47,"Next Approval Node"),d.Zb(),d.ac(48,"div",19),d.Vb(49,"input",24),d.Zb(),d.Zb(),d.ac(50,"div",17),d.ac(51,"label",18),d.Lc(52,"Positive Statuse"),d.Zb(),d.ac(53,"div",19),d.Vb(54,"input",25),d.Zb(),d.Zb(),d.ac(55,"div",17),d.ac(56,"label",18),d.Lc(57,"Negative Status"),d.Zb(),d.ac(58,"div",19),d.Vb(59,"input",26),d.Zb(),d.Zb(),d.ac(60,"div",17),d.ac(61,"label",18),d.Lc(62,"Sequence"),d.Zb(),d.ac(63,"div",19),d.Vb(64,"input",27),d.Zb(),d.Zb(),d.ac(65,"div",17),d.ac(66,"label",18),d.Lc(67,"Is Active"),d.Zb(),d.ac(68,"div",19),d.Vb(69,"input",28),d.Zb(),d.Zb(),d.ac(70,"div",29),d.ac(71,"a",30),d.Vb(72,"i",11),d.Lc(73," Cancel"),d.Zb(),d.Lc(74," \xa0 \xa0 "),d.ac(75,"button",31),d.hc("click",function(){return t.resetFormValues()}),d.Vb(76,"i",32),d.Lc(77," Reset "),d.Zb(),d.Lc(78," \xa0 \xa0 "),d.ac(79,"button",33),d.Vb(80,"i",34),d.Lc(81," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm),d.Ib(10),d.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[b.e,n.x,n.p,n.h,n.b,n.o,n.f,x.a,n.t,n.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();function q(e,t){if(1&e){const e=d.bc();d.ac(0,"tr"),d.ac(1,"td"),d.Lc(2),d.Zb(),d.ac(3,"td",54),d.Lc(4),d.Zb(),d.ac(5,"td"),d.Lc(6),d.Zb(),d.ac(7,"td"),d.Lc(8),d.Zb(),d.ac(9,"td"),d.Lc(10),d.Zb(),d.ac(11,"td"),d.Lc(12),d.Zb(),d.ac(13,"td"),d.Lc(14),d.Zb(),d.ac(15,"td"),d.Lc(16),d.Zb(),d.ac(17,"td"),d.Lc(18),d.Zb(),d.ac(19,"td"),d.Lc(20),d.Zb(),d.ac(21,"td"),d.Lc(22),d.Zb(),d.ac(23,"td"),d.Lc(24," \xa0 "),d.ac(25,"a",55),d.Vb(26,"i",56),d.Zb(),d.Lc(27,"\xa0\xa0 "),d.ac(28,"a",57),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().tempId=a.id}),d.Vb(29,"i",58),d.Zb(),d.Zb(),d.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=d.jc();d.Mb("active",a==i.currentIndex),d.Ib(2),d.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),d.Ib(2),d.Mc(e.id),d.Ib(2),d.Mc(e.approvalGroupName),d.Ib(2),d.Mc(e.approvalProcess.processName),d.Ib(2),d.Mc(e.preApprovalNode),d.Ib(2),d.Mc(e.thisApprovalNode),d.Ib(2),d.Mc(e.nextApprovalNode),d.Ib(2),d.Mc(e.putOnStatusPositive),d.Ib(2),d.Mc(e.putOnStatusNegative),d.Ib(2),d.Mc(e.sequence),d.Ib(2),d.Mc(e.isActive),d.Ib(3),d.rc("routerLink","./edit/",e.id,"")}}function w(e,t){1&e&&(d.ac(0,"tr"),d.ac(1,"td",59),d.ac(2,"h5",60),d.Lc(3,"No data found"),d.Zb(),d.Zb(),d.Zb())}function U(e,t){if(1&e&&(d.ac(0,"option",61),d.Lc(1),d.Zb()),2&e){const e=t.$implicit;d.pc("value",e),d.Ib(1),d.Nc(" ",e," ")}}let z=(()=>{class e{constructor(e,t,a){this.spinnerService=e,this.toastr=t,this.approvalService=a,this.baseUrl=m.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.getListData()}getListData(){let e=this.baseUrl+"/approvalStep/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide()},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/approvalStep/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getListData()},e=>{console.log(e),this.spinnerService.hide()})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(p.c),d.Ub(v.b),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step"]],decls:106,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-step/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"List"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"div",10),d.ac(18,"button",11),d.Lc(19,"Excel"),d.Zb(),d.ac(20,"button",11),d.Lc(21,"PDF"),d.Zb(),d.ac(22,"button",11),d.Vb(23,"i",12),d.Lc(24," Print"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(25,"div",13),d.ac(26,"div",14),d.ac(27,"div",15),d.ac(28,"div",16),d.ac(29,"div",17),d.Vb(30,"input",18),d.ac(31,"label",19),d.Lc(32,"Code"),d.Zb(),d.Zb(),d.Zb(),d.ac(33,"div",20),d.ac(34,"a",21),d.Lc(35," Search "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(36,"div",22),d.ac(37,"div",23),d.ac(38,"div",24),d.ac(39,"div",25),d.ac(40,"div",26),d.ac(41,"a",27),d.Vb(42,"i",28),d.Lc(43," New \xa0\xa0\xa0"),d.Zb(),d.Zb(),d.Zb(),d.ac(44,"div",29),d.ac(45,"div",30),d.ac(46,"div",31),d.ac(47,"div",32),d.ac(48,"span",33),d.Lc(49),d.Zb(),d.Zb(),d.Zb(),d.ac(50,"table",34),d.ac(51,"thead"),d.ac(52,"tr"),d.ac(53,"th"),d.Lc(54,"SL"),d.Zb(),d.ac(55,"th"),d.Lc(56,"Approval Group"),d.Zb(),d.ac(57,"th"),d.Lc(58,"Approval Process"),d.Zb(),d.ac(59,"th"),d.Lc(60,"Pre Approval Node"),d.Zb(),d.ac(61,"th"),d.Lc(62,"This Approval Node"),d.Zb(),d.ac(63,"th"),d.Lc(64,"Next Approval Node"),d.Zb(),d.ac(65,"th"),d.Lc(66,"Psoitive Status"),d.Zb(),d.ac(67,"th"),d.Lc(68,"Negative Status"),d.Zb(),d.ac(69,"th"),d.Lc(70,"Sequence"),d.Zb(),d.ac(71,"th"),d.Lc(72,"Is Active"),d.Zb(),d.ac(73,"th"),d.Lc(74,"Action"),d.Zb(),d.Zb(),d.Zb(),d.ac(75,"tbody"),d.Jc(76,q,30,14,"tr",35),d.kc(77,"paginate"),d.Jc(78,w,4,0,"tr",36),d.Zb(),d.Zb(),d.ac(79,"div",37),d.ac(80,"div",38),d.Lc(81," Items per Page "),d.ac(82,"select",39),d.hc("change",function(e){return t.handlePageSizeChange(e)}),d.Jc(83,U,2,2,"option",40),d.Zb(),d.Zb(),d.ac(84,"div",41),d.ac(85,"pagination-controls",42),d.hc("pageChange",function(e){return t.handlePageChange(e)}),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(86,"ngx-spinner",43),d.ac(87,"p",44),d.Lc(88," Processing... "),d.Zb(),d.Zb(),d.ac(89,"div",45),d.ac(90,"div",46),d.ac(91,"div",47),d.ac(92,"div",48),d.ac(93,"div",49),d.ac(94,"h3"),d.Lc(95,"Delete Item"),d.Zb(),d.ac(96,"p"),d.Lc(97,"Are you sure want to delete?"),d.Zb(),d.Zb(),d.ac(98,"div",50),d.ac(99,"div",22),d.ac(100,"div",51),d.ac(101,"a",52),d.hc("click",function(){return t.deleteEnityData(t.tempId)}),d.Lc(102,"Delete"),d.Zb(),d.Zb(),d.ac(103,"div",51),d.ac(104,"a",53),d.Lc(105,"Cancel"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(49),d.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),d.Ib(27),d.pc("ngForOf",d.mc(77,7,t.listData,t.configPgn)),d.Ib(2),d.pc("ngIf",0===t.listData.length),d.Ib(5),d.pc("ngForOf",t.configPgn.pageSizes),d.Ib(3),d.pc("fullScreen",!1))},directives:[b.e,i.l,i.m,s.c,p.a,n.s,n.y],pipes:[s.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),V=(()=>{class e{constructor(e,t,a,i,c,o){this.commonService=e,this.formBuilder=t,this.datePipe=a,this.route=i,this.router=c,this.approvalService=o,this.baseUrl=m.a.baseUrl,this._initConfigDDL()}ngOnInit(){this.initializeForm()}initializeForm(){this.myForm=this.formBuilder.group({approvalProcess:[{},n.w.required],approvalStep:[{},n.w.required],activityStatusCode:["",n.w.required],activityStatusTitle:["",n.w.required],sequence:["",n.w.required],isActive:["",n.w.required]})}myFormSubmit(){if(this.myForm.invalid)return;let e={};e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPostRequest(this.baseUrl+"/approvalStepAction/save",e).subscribe(e=>{console.log(e),this.router.navigate(["/approval/approval-step-action"],{relativeTo:this.route})},e=>{console.log(e)})}resetFormValues(){this.myForm.reset()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?this.configDDL.listData[this.configDDL.activeFieldName]=this.configDDL.listData.concat(e.objectList):(this.configDDL.listData[this.configDDL.activeFieldName]=e.objectList,console.log(this.configDDL.listData)),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:{},append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getApprovalStep(){return this.myForm.get("approvalStep")}get getApprovalProcess(){return this.myForm.get("approvalProcess")}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(I.a),d.Ub(n.d),d.Ub(i.e),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-action-create"]],decls:67,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-action",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","processName",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","approvalGroupName",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","text","formControlName","activityStatusCode",1,"form-control"],["type","text","formControlName","activityStatusTitle",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step-action",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step Actoin"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step Actoin"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Create"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.myFormSubmit()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Approval Process *"),d.Zb(),d.ac(28,"div",19),d.ac(29,"ng-select",20),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"processName","/approvalProc/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Approval Step *"),d.Zb(),d.ac(33,"div",19),d.ac(34,"ng-select",21),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"approvalGroupName","/approvalStep/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Activity Status Code"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",17),d.ac(41,"label",18),d.Lc(42,"Activity Status Title"),d.Zb(),d.ac(43,"div",19),d.Vb(44,"input",23),d.Zb(),d.Zb(),d.ac(45,"div",17),d.ac(46,"label",18),d.Lc(47,"Sequence"),d.Zb(),d.ac(48,"div",19),d.Vb(49,"input",24),d.Zb(),d.Zb(),d.ac(50,"div",17),d.ac(51,"label",18),d.Lc(52,"Is Active"),d.Zb(),d.ac(53,"div",19),d.Vb(54,"input",25),d.Zb(),d.Zb(),d.ac(55,"div",26),d.ac(56,"a",27),d.Vb(57,"i",11),d.Lc(58," Cancel"),d.Zb(),d.Lc(59," \xa0 \xa0 "),d.ac(60,"button",28),d.hc("click",function(){return t.resetFormValues()}),d.Vb(61,"i",29),d.Lc(62," Reset "),d.Zb(),d.Lc(63," \xa0 \xa0 "),d.ac(64,"button",30),d.Vb(65,"i",31),d.Lc(66," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm),d.Ib(5),d.pc("items",t.configDDL.listData.processName)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),d.Ib(5),d.pc("items",t.configDDL.listData.approvalGroupName)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[b.e,n.x,n.p,n.h,x.a,n.o,n.f,n.b,n.t,n.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),O=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.route=t,this.router=a,this.approvalService=i,this.spinnerService=c,this.commonService=o,this.baseUrl=m.a.baseUrl,this.myFormData={},this._initConfigDDL()}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],approvalProcess:["",n.w.required],approvalStep:["",n.w.required],activityStatusCode:["",n.w.required],activityStatusTitle:["",n.w.required],sequence:["",n.w.required],isActive:["",n.w.required]})}getFormData(){let e=this.baseUrl+"/approvalStepAction/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide();let t=[{id:e.approvalProcess.id,processName:e.approvalProcess.processName}];console.log(t),this.configDDL.listData=t,this.myFormData.approvalProcess=e.approvalProcess.id;let a=[{id:e.approvalStep.id,approvalGroupName:e.approvalStep.approvalGroupName}];console.log(a),this.configDDL.listData2=a,this.myFormData.approvalStep=e.approvalStep.id,this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){if(this.myForm.invalid)return;let e={};e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPutRequest(this.baseUrl+"/approvalStepAction/edit",e).subscribe(e=>{console.log(e),this.router.navigate(["/approval/approval-step-action"],{relativeTo:this.route})},e=>{console.log(e)})}resetFormValues(){this.getFormData()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?(this.configDDL.listData=this.configDDL.listData.concat(e.objectList),this.configDDL.listData2=this.configDDL.listData2.concat(e.objectList)):(this.configDDL.listData=e.objectList,this.configDDL.listData2=e.objectList),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getApprovalStep(){return this.myForm.get("approvalStep")}get getApprovalProcess(){return this.myForm.get("approvalProcess")}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(n.d),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a),d.Ub(p.c),d.Ub(I.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-action-edit"]],decls:67,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-action",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","text","formControlName","activityStatusCode",1,"form-control"],["type","text","formControlName","activityStatusTitle",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step-action",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step Action"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step Action"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Edit"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Approval Process *"),d.Zb(),d.ac(28,"div",19),d.ac(29,"ng-select",20),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/approvalProc/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Approval Step *"),d.Zb(),d.ac(33,"div",19),d.ac(34,"ng-select",21),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/approvalStep/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Activity Status Code"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",17),d.ac(41,"label",18),d.Lc(42,"Activity Status Title"),d.Zb(),d.ac(43,"div",19),d.Vb(44,"input",23),d.Zb(),d.Zb(),d.ac(45,"div",17),d.ac(46,"label",18),d.Lc(47,"Sequence"),d.Zb(),d.ac(48,"div",19),d.Vb(49,"input",24),d.Zb(),d.Zb(),d.ac(50,"div",17),d.ac(51,"label",18),d.Lc(52,"Is Active"),d.Zb(),d.ac(53,"div",19),d.Vb(54,"input",25),d.Zb(),d.Zb(),d.ac(55,"div",26),d.ac(56,"a",27),d.Vb(57,"i",11),d.Lc(58," Cancel"),d.Zb(),d.Lc(59," \xa0 \xa0 "),d.ac(60,"button",28),d.hc("click",function(){return t.resetFormValues()}),d.Vb(61,"i",29),d.Lc(62," Reset "),d.Zb(),d.Lc(63," \xa0 \xa0 "),d.ac(64,"button",30),d.Vb(65,"i",31),d.Lc(66," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm),d.Ib(5),d.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),d.Ib(5),d.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[b.e,n.x,n.p,n.h,x.a,n.o,n.f,n.b,n.t,n.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();function G(e,t){if(1&e){const e=d.bc();d.ac(0,"tr"),d.ac(1,"td"),d.Lc(2),d.Zb(),d.ac(3,"td",54),d.Lc(4),d.Zb(),d.ac(5,"td"),d.Lc(6),d.Zb(),d.ac(7,"td"),d.Lc(8),d.Zb(),d.ac(9,"td"),d.Lc(10),d.Zb(),d.ac(11,"td"),d.Lc(12),d.Zb(),d.ac(13,"td"),d.Lc(14),d.Zb(),d.ac(15,"td"),d.Lc(16),d.Zb(),d.ac(17,"td"),d.Lc(18," \xa0 "),d.ac(19,"a",55),d.Vb(20,"i",56),d.Zb(),d.Lc(21,"\xa0\xa0 "),d.ac(22,"a",57),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().tempId=a.id}),d.Vb(23,"i",58),d.Zb(),d.Zb(),d.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=d.jc();d.Mb("active",a==i.currentIndex),d.Ib(2),d.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),d.Ib(2),d.Mc(e.id),d.Ib(2),d.Mc(e.approvalProcess.processName),d.Ib(2),d.Mc(e.approvalStep.approvalGroupName),d.Ib(2),d.Mc(e.activityStatusCode),d.Ib(2),d.Mc(e.activityStatusTitle),d.Ib(2),d.Mc(e.sequence),d.Ib(2),d.Mc(e.isActive),d.Ib(3),d.rc("routerLink","./edit/",e.id,"")}}function E(e,t){1&e&&(d.ac(0,"tr"),d.ac(1,"td",59),d.ac(2,"h5",60),d.Lc(3,"No data found"),d.Zb(),d.Zb(),d.Zb())}function T(e,t){if(1&e&&(d.ac(0,"option",61),d.Lc(1),d.Zb()),2&e){const e=t.$implicit;d.pc("value",e),d.Ib(1),d.Nc(" ",e," ")}}let M=(()=>{class e{constructor(e,t,a){this.spinnerService=e,this.toastr=t,this.approvalService=a,this.baseUrl=m.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.getListData()}getListData(){let e=this.baseUrl+"/approvalStepAction/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide(),console.log(this.listData)},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/approvalStepAction/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getListData()},e=>{console.log(e),this.spinnerService.hide()})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(p.c),d.Ub(v.b),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-action"]],decls:100,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-step-action/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step Action"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step Action"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"List"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"div",10),d.ac(18,"button",11),d.Lc(19,"Excel"),d.Zb(),d.ac(20,"button",11),d.Lc(21,"PDF"),d.Zb(),d.ac(22,"button",11),d.Vb(23,"i",12),d.Lc(24," Print"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(25,"div",13),d.ac(26,"div",14),d.ac(27,"div",15),d.ac(28,"div",16),d.ac(29,"div",17),d.Vb(30,"input",18),d.ac(31,"label",19),d.Lc(32,"Code"),d.Zb(),d.Zb(),d.Zb(),d.ac(33,"div",20),d.ac(34,"a",21),d.Lc(35," Search "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(36,"div",22),d.ac(37,"div",23),d.ac(38,"div",24),d.ac(39,"div",25),d.ac(40,"div",26),d.ac(41,"a",27),d.Vb(42,"i",28),d.Lc(43," New \xa0\xa0\xa0"),d.Zb(),d.Zb(),d.Zb(),d.ac(44,"div",29),d.ac(45,"div",30),d.ac(46,"div",31),d.ac(47,"div",32),d.ac(48,"span",33),d.Lc(49),d.Zb(),d.Zb(),d.Zb(),d.ac(50,"table",34),d.ac(51,"thead"),d.ac(52,"tr"),d.ac(53,"th"),d.Lc(54,"SL"),d.Zb(),d.ac(55,"th"),d.Lc(56,"Approval Process"),d.Zb(),d.ac(57,"th"),d.Lc(58,"Approval Step"),d.Zb(),d.ac(59,"th"),d.Lc(60,"Activity Status Code"),d.Zb(),d.ac(61,"th"),d.Lc(62,"Activity Status Title"),d.Zb(),d.ac(63,"th"),d.Lc(64,"Sequence"),d.Zb(),d.ac(65,"th"),d.Lc(66,"Is Active"),d.Zb(),d.ac(67,"th"),d.Lc(68,"Action"),d.Zb(),d.Zb(),d.Zb(),d.ac(69,"tbody"),d.Jc(70,G,24,11,"tr",35),d.kc(71,"paginate"),d.Jc(72,E,4,0,"tr",36),d.Zb(),d.Zb(),d.ac(73,"div",37),d.ac(74,"div",38),d.Lc(75," Items per Page "),d.ac(76,"select",39),d.hc("change",function(e){return t.handlePageSizeChange(e)}),d.Jc(77,T,2,2,"option",40),d.Zb(),d.Zb(),d.ac(78,"div",41),d.ac(79,"pagination-controls",42),d.hc("pageChange",function(e){return t.handlePageChange(e)}),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(80,"ngx-spinner",43),d.ac(81,"p",44),d.Lc(82," Processing... "),d.Zb(),d.Zb(),d.ac(83,"div",45),d.ac(84,"div",46),d.ac(85,"div",47),d.ac(86,"div",48),d.ac(87,"div",49),d.ac(88,"h3"),d.Lc(89,"Delete Item"),d.Zb(),d.ac(90,"p"),d.Lc(91,"Are you sure want to delete?"),d.Zb(),d.Zb(),d.ac(92,"div",50),d.ac(93,"div",22),d.ac(94,"div",51),d.ac(95,"a",52),d.hc("click",function(){return t.deleteEnityData(t.tempId)}),d.Lc(96,"Delete"),d.Zb(),d.Zb(),d.ac(97,"div",51),d.ac(98,"a",53),d.Lc(99,"Cancel"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(49),d.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),d.Ib(21),d.pc("ngForOf",d.mc(71,7,t.listData,t.configPgn)),d.Ib(2),d.pc("ngIf",0===t.listData.length),d.Ib(5),d.pc("ngForOf",t.configPgn.pageSizes),d.Ib(3),d.pc("fullScreen",!1))},directives:[b.e,i.l,i.m,s.c,p.a,n.s,n.y],pipes:[s.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})(),j=(()=>{class e{constructor(e,t,a,i,c,o){this.commonService=e,this.formBuilder=t,this.datePipe=a,this.route=i,this.router=c,this.approvalService=o,this.baseUrl=m.a.baseUrl,this._initConfigDDL()}ngOnInit(){this.initializeForm()}initializeForm(){this.myForm=this.formBuilder.group({hrCrEmp:["",n.w.required],approvalStep:["",n.w.required],isActive:["",n.w.required]})}myFormSubmit(){if(this.myForm.invalid)return;let e={};e=Object.assign(this.myForm.value,{approvalMemberId:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPostRequest(this.baseUrl+"/approvalStepApprover/save",e).subscribe(e=>{console.log(e),this.router.navigate(["/approval/approval-step-approver"],{relativeTo:this.route})},e=>{console.log(e)})}resetFormValues(){this.myForm.reset()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?this.configDDL.listData=this.configDDL.listData.concat(e.objectList):(this.configDDL.listData=e.objectList,console.log(this.configDDL.listData)),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getApprovalStep(){return this.myForm.get("approvalStep")}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(I.a),d.Ub(n.d),d.Ub(i.e),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-approver-create"]],decls:52,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-approver",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","checkbox","formControlName","isActive",1,"big-checkbox"],[1,"text-right"],["routerLink","/approval/approval-step-approver",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step Approver"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step Approver"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Create"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.myFormSubmit()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Employee "),d.Zb(),d.ac(28,"div",19),d.ac(29,"ng-select",20),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),d.Zb(),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Approval Step *"),d.Zb(),d.ac(33,"div",19),d.ac(34,"ng-select",21),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/approvalStep/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Is Active"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",23),d.ac(41,"a",24),d.Vb(42,"i",11),d.Lc(43," Cancel"),d.Zb(),d.Lc(44," \xa0 \xa0 "),d.ac(45,"button",25),d.hc("click",function(){return t.resetFormValues()}),d.Vb(46,"i",26),d.Lc(47," Reset "),d.Zb(),d.Lc(48," \xa0 \xa0 "),d.ac(49,"button",27),d.Vb(50,"i",28),d.Lc(51," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm),d.Ib(5),d.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),d.Ib(5),d.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[b.e,n.x,n.p,n.h,x.a,n.o,n.f,n.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}.big-checkbox[_ngcontent-%COMP%]{width:30px;height:30px}"]}),e})(),R=(()=>{class e{constructor(e,t,a,i,c,o){this.formBuilder=e,this.route=t,this.router=a,this.approvalService=i,this.spinnerService=c,this.commonService=o,this.baseUrl=m.a.baseUrl,this.myFormData={},this._initConfigDDL()}ngOnInit(){this.initializeForm(),this.getFormData()}initializeForm(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",n.w.required],approvalStep:["",n.w.required],isActive:["",n.w.required]})}getFormData(){let e=this.baseUrl+"/approvalStepApprover/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalService.sendGetRequest(e,{}).subscribe(e=>{this.myFormData=e,console.log(this.myFormData),this.spinnerService.hide();let t=[{ddlCode:e.approvalMemberId.id,ddlDescription:e.approvalMemberId.code+"-"+e.approvalMemberId.displayName}];console.log(t),this.configDDL.listData=t,this.myFormData.hrCrEmp=e.approvalMemberId.id;let a=[{id:e.approvalStep.id,approvalGroupName:e.approvalStep.approvalGroupName}];console.log(a),this.configDDL.listData2=a,this.myFormData.approvalStep=e.approvalStep.id,this.myForm.patchValue(this.myFormData)},e=>{console.log(e)})}saveUpdatedFormData(){if(this.myForm.invalid)return;let e={};e=Object.assign(this.myForm.value,{approvalMemberId:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPutRequest(this.baseUrl+"/approvalStepApprover/edit",e).subscribe(e=>{console.log(e),this.router.navigate(["/approval/approval-step-approver"],{relativeTo:this.route})},e=>{console.log(e)})}resetFormValues(){this.getFormData()}searchDDL(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}scrollToEndDDL(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}clearDDL(){this.configDDL.q=""}getListDataDDL(){let e=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(e,t).subscribe(e=>{this.configDDL.append?(this.configDDL.listData=this.configDDL.listData.concat(e.objectList),this.configDDL.listData2=this.configDDL.listData2.concat(e.objectList)):(this.configDDL.listData=e.objectList,this.configDDL.listData2=e.objectList),this.configDDL.totalItem=e.totalItems},e=>{console.log(e)})}setDefaultParamsDDL(){this._initConfigDDL()}_initConfigDDL(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}initSysParamsDDL(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}get getHrCrEmp(){return this.myForm.get("hrCrEmp")}get getApprovalStep(){return this.myForm.get("approvalStep")}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(n.d),d.Ub(b.a),d.Ub(b.c),d.Ub(h.a),d.Ub(p.c),d.Ub(I.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-approver-edit"]],decls:52,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-approver",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step-approver",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step Approver"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step Approver"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"Edit"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"a",10),d.Vb(18,"i",11),d.Lc(19," Back To List"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(20,"div",12),d.ac(21,"div",13),d.ac(22,"div",14),d.ac(23,"div",15),d.ac(24,"form",16),d.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),d.ac(25,"div",17),d.ac(26,"label",18),d.Lc(27,"Employee "),d.Zb(),d.ac(28,"div",19),d.ac(29,"ng-select",20),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),d.Zb(),d.Zb(),d.Zb(),d.ac(30,"div",17),d.ac(31,"label",18),d.Lc(32,"Approval Step *"),d.Zb(),d.ac(33,"div",19),d.ac(34,"ng-select",21),d.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/approvalStep/getAll","")}),d.Zb(),d.Zb(),d.Zb(),d.ac(35,"div",17),d.ac(36,"label",18),d.Lc(37,"Is Active"),d.Zb(),d.ac(38,"div",19),d.Vb(39,"input",22),d.Zb(),d.Zb(),d.ac(40,"div",23),d.ac(41,"a",24),d.Vb(42,"i",11),d.Lc(43," Cancel"),d.Zb(),d.Lc(44," \xa0 \xa0 "),d.ac(45,"button",25),d.hc("click",function(){return t.resetFormValues()}),d.Vb(46,"i",26),d.Lc(47," Reset "),d.Zb(),d.Lc(48," \xa0 \xa0 "),d.ac(49,"button",27),d.Vb(50,"i",28),d.Lc(51," Save \xa0\xa0\xa0 "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(24),d.pc("formGroup",t.myForm),d.Ib(5),d.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),d.Ib(5),d.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[b.e,n.x,n.p,n.h,x.a,n.o,n.f,n.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})();function _(e,t){if(1&e){const e=d.bc();d.ac(0,"tr"),d.ac(1,"td"),d.Lc(2),d.Zb(),d.ac(3,"td",54),d.Lc(4),d.Zb(),d.ac(5,"td"),d.Lc(6),d.Zb(),d.ac(7,"td"),d.Lc(8),d.Zb(),d.ac(9,"td"),d.Lc(10),d.Zb(),d.ac(11,"td"),d.Lc(12," \xa0 "),d.ac(13,"a",55),d.Vb(14,"i",56),d.Zb(),d.Lc(15,"\xa0\xa0 "),d.ac(16,"a",57),d.hc("click",function(){d.Cc(e);const a=t.$implicit;return d.jc().tempId=a.id}),d.Vb(17,"i",58),d.Zb(),d.Zb(),d.Zb()}if(2&e){const e=t.$implicit,a=t.index,i=d.jc();d.Mb("active",a==i.currentIndex),d.Ib(2),d.Mc((i.configPgn.pageNum-1)*i.configPgn.pageSize+(a+1)),d.Ib(2),d.Mc(e.id),d.Ib(2),d.Mc(e.approvalStep.approvalGroupName),d.Ib(2),d.Mc(e.approvalMemberId.displayName),d.Ib(2),d.Mc(e.isActive),d.Ib(3),d.rc("routerLink","./edit/",e.id,"")}}function B(e,t){1&e&&(d.ac(0,"tr"),d.ac(1,"td",59),d.ac(2,"h5",60),d.Lc(3,"No data found"),d.Zb(),d.Zb(),d.Zb())}function H(e,t){if(1&e&&(d.ac(0,"option",61),d.Lc(1),d.Zb()),2&e){const e=t.$implicit;d.pc("value",e),d.Ib(1),d.Nc(" ",e," ")}}const Q=[{path:"",component:u,children:[{path:"approval-process",component:Z},{path:"approval-process/create",component:S},{path:"approval-process/edit/:id",component:P},{path:"approval-step",component:z},{path:"approval-step/create",component:k},{path:"approval-step/edit/:id",component:C},{path:"approval-step-approver",component:(()=>{class e{constructor(e,t,a){this.spinnerService=e,this.toastr=t,this.approvalService=a,this.baseUrl=m.a.baseUrl,this.pipe=new i.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}ngOnInit(){this.getListData()}getListData(){let e=this.baseUrl+"/approvalStepApprover/getAll",t={};t=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(e,t).subscribe(e=>{this.listData=e.objectList,this.configPgn.totalItem=e.totalItems,this.configPgn.totalItems=e.totalItems,this.setDisplayLastSequence(),this.spinnerService.hide(),console.log(this.listData)},e=>{console.log(e)})}deleteEnityData(e){let t=this.baseUrl+"/approvalStepApprover/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalService.sendDeleteRequest(t,{}).subscribe(e=>{console.log(e),this.spinnerService.hide(),$("#delete_entity").modal("hide"),this.toastr.success("Successfully item is deleted","Success"),this.getListData()},e=>{console.log(e),this.spinnerService.hide()})}getUserQueryParams(e,t){let a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}setDisplayLastSequence(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}handlePageChange(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}handlePageSizeChange(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}return e.\u0275fac=function(t){return new(t||e)(d.Ub(p.c),d.Ub(v.b),d.Ub(h.a))},e.\u0275cmp=d.Ob({type:e,selectors:[["app-approval-step-approver"]],decls:94,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-step-approver/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(d.ac(0,"div",0),d.ac(1,"div",1),d.ac(2,"div",2),d.ac(3,"div",3),d.ac(4,"h3",4),d.Lc(5,"Approval Step Approver"),d.Zb(),d.ac(6,"ul",5),d.ac(7,"li",6),d.ac(8,"a",7),d.Lc(9,"Home"),d.Zb(),d.Zb(),d.ac(10,"li",8),d.Lc(11,"Approval"),d.Zb(),d.ac(12,"li",8),d.Lc(13,"Approval Step Approver"),d.Zb(),d.ac(14,"li",8),d.Lc(15,"List"),d.Zb(),d.Zb(),d.Zb(),d.ac(16,"div",9),d.ac(17,"div",10),d.ac(18,"button",11),d.Lc(19,"Excel"),d.Zb(),d.ac(20,"button",11),d.Lc(21,"PDF"),d.Zb(),d.ac(22,"button",11),d.Vb(23,"i",12),d.Lc(24," Print"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(25,"div",13),d.ac(26,"div",14),d.ac(27,"div",15),d.ac(28,"div",16),d.ac(29,"div",17),d.Vb(30,"input",18),d.ac(31,"label",19),d.Lc(32,"Code"),d.Zb(),d.Zb(),d.Zb(),d.ac(33,"div",20),d.ac(34,"a",21),d.Lc(35," Search "),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(36,"div",22),d.ac(37,"div",23),d.ac(38,"div",24),d.ac(39,"div",25),d.ac(40,"div",26),d.ac(41,"a",27),d.Vb(42,"i",28),d.Lc(43," New \xa0\xa0\xa0"),d.Zb(),d.Zb(),d.Zb(),d.ac(44,"div",29),d.ac(45,"div",30),d.ac(46,"div",31),d.ac(47,"div",32),d.ac(48,"span",33),d.Lc(49),d.Zb(),d.Zb(),d.Zb(),d.ac(50,"table",34),d.ac(51,"thead"),d.ac(52,"tr"),d.ac(53,"th"),d.Lc(54,"SL"),d.Zb(),d.ac(55,"th"),d.Lc(56,"Approval Step"),d.Zb(),d.ac(57,"th"),d.Lc(58,"Approval Member"),d.Zb(),d.ac(59,"th"),d.Lc(60,"Is Active"),d.Zb(),d.ac(61,"th"),d.Lc(62,"Action"),d.Zb(),d.Zb(),d.Zb(),d.ac(63,"tbody"),d.Jc(64,_,18,8,"tr",35),d.kc(65,"paginate"),d.Jc(66,B,4,0,"tr",36),d.Zb(),d.Zb(),d.ac(67,"div",37),d.ac(68,"div",38),d.Lc(69," Items per Page "),d.ac(70,"select",39),d.hc("change",function(e){return t.handlePageSizeChange(e)}),d.Jc(71,H,2,2,"option",40),d.Zb(),d.Zb(),d.ac(72,"div",41),d.ac(73,"pagination-controls",42),d.hc("pageChange",function(e){return t.handlePageChange(e)}),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.ac(74,"ngx-spinner",43),d.ac(75,"p",44),d.Lc(76," Processing... "),d.Zb(),d.Zb(),d.ac(77,"div",45),d.ac(78,"div",46),d.ac(79,"div",47),d.ac(80,"div",48),d.ac(81,"div",49),d.ac(82,"h3"),d.Lc(83,"Delete Item"),d.Zb(),d.ac(84,"p"),d.Lc(85,"Are you sure want to delete?"),d.Zb(),d.Zb(),d.ac(86,"div",50),d.ac(87,"div",22),d.ac(88,"div",51),d.ac(89,"a",52),d.hc("click",function(){return t.deleteEnityData(t.tempId)}),d.Lc(90,"Delete"),d.Zb(),d.Zb(),d.ac(91,"div",51),d.ac(92,"a",53),d.Lc(93,"Cancel"),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb(),d.Zb()),2&e&&(d.Ib(49),d.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),d.Ib(15),d.pc("ngForOf",d.mc(65,7,t.listData,t.configPgn)),d.Ib(2),d.pc("ngIf",0===t.listData.length),d.Ib(5),d.pc("ngForOf",t.configPgn.pageSizes),d.Ib(3),d.pc("fullScreen",!1))},directives:[b.e,i.l,i.m,s.c,p.a,n.s,n.y],pipes:[s.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),e})()},{path:"approval-step-approver/create",component:j},{path:"approval-step-approver/edit/:id",component:R},{path:"approval-step-action",component:M},{path:"approval-step-action/create",component:V},{path:"approval-step-action/edit/:id",component:O},{path:"approval-process-tnx-history",component:F}]}];let J=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=d.Sb({type:e}),e.\u0275inj=d.Rb({imports:[[b.f.forChild(Q)],b.f]}),e})(),K=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275mod=d.Sb({type:e}),e.\u0275inj=d.Rb({imports:[[i.c,J,c.b,o.c.forRoot(),r.a,n.u,l.b,s.a,p.b,x.b]]}),e})()}}]);