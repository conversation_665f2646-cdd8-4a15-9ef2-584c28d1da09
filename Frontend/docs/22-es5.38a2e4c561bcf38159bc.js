!function(){function e(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}function a(e,a){for(var t=0;t<a.length;t++){var i=a[t];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function t(e,t,i){return t&&a(e.prototype,t),i&&a(e,i),e}(window.webpackJsonp=window.webpackJsonp||[]).push([[22],{"oV/K":function(a,i,c){"use strict";c.r(i),c.d(i,"ApprovalModule",function(){return ge});var o,n=c("ofXK"),r=c("njyG"),l=c("oW1M"),s=c("0jEk"),p=c("3Pt+"),b=c("iHf9"),u=c("oOf3"),d=c("JqCM"),g=c("tyNb"),v=c("fXoL"),m=function(e){return{height:e}},f=((o=function(){function a(t){var i=this;e(this,a),this.ngZone=t,window.onresize=function(e){i.ngZone.run(function(){i.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}return t(a,[{key:"getScreenHeight",value:function(){this.innerHeight=window.innerHeight+"px"}},{key:"ngOnInit",value:function(){}},{key:"onResize",value:function(e){this.innerHeight=e.target.innerHeight+"px"}}]),a}()).\u0275fac=function(e){return new(e||o)(v.Ub(v.G))},o.\u0275cmp=v.Ob({type:o,selectors:[["app-approval"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.hc("resized",function(e){return a.onResize(e)}),v.Vb(1,"router-outlet"),v.Zb()),2&e&&v.pc("ngStyle",v.tc(1,m,a.innerHeight))},directives:[n.n,g.g],styles:[""]}),o),h=c("AytR"),D=c("NllD"),L=c("5eHb");function Z(e,a){if(1&e){var t=v.bc();v.ac(0,"tr"),v.ac(1,"td"),v.Lc(2),v.Zb(),v.ac(3,"td",54),v.Lc(4),v.Zb(),v.ac(5,"td"),v.Lc(6),v.Zb(),v.ac(7,"td"),v.Lc(8),v.Zb(),v.ac(9,"td"),v.Lc(10),v.Zb(),v.ac(11,"td"),v.Lc(12),v.Zb(),v.ac(13,"td"),v.Lc(14," \xa0 "),v.ac(15,"a",55),v.Vb(16,"i",56),v.Zb(),v.Lc(17,"\xa0\xa0 "),v.ac(18,"a",57),v.hc("click",function(){v.Cc(t);var e=a.$implicit;return v.jc().tempId=e.id}),v.Vb(19,"i",58),v.Zb(),v.Zb(),v.Zb()}if(2&e){var i=a.$implicit,c=a.index,o=v.jc();v.Mb("active",c==o.currentIndex),v.Ib(2),v.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),v.Ib(2),v.Mc(i.id),v.Ib(2),v.Mc(i.code),v.Ib(2),v.Mc(i.processName),v.Ib(2),v.Mc(i.sequence),v.Ib(2),v.Mc(i.remarks),v.Ib(3),v.rc("routerLink","./edit/",i.id,"")}}function y(e,a){1&e&&(v.ac(0,"tr"),v.ac(1,"td",59),v.ac(2,"h5",60),v.Lc(3,"No data found"),v.Zb(),v.Zb(),v.Zb())}function S(e,a){if(1&e&&(v.ac(0,"option",61),v.Lc(1),v.Zb()),2&e){var t=a.$implicit;v.pc("value",t),v.Ib(1),v.Nc(" ",t," ")}}var P,k,N,A=((N=function(){function a(t,i,c){e(this,a),this.spinnerService=t,this.toastr=i,this.approvalProcessService=c,this.baseUrl=h.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return t(a,[{key:"ngOnInit",value:function(){this.getListData()}},{key:"getListData",value:function(){var e,a=this,t=this.baseUrl+"/approvalProc/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalProcessService.sendGetRequest(t,e).subscribe(function(e){a.listData=e.objectList,a.configPgn.totalItem=e.totalItems,a.configPgn.totalItems=e.totalItems,a.setDisplayLastSequence(),a.spinnerService.hide()},function(e){console.log(e)})}},{key:"getUserQueryParams",value:function(e,a){var t={};return e&&(t.pageNum=e-0),a&&(t.pageSize=a),t}},{key:"deleteEnityData",value:function(e){var a=this,t=this.baseUrl+"/approvalProc/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalProcessService.sendDeleteRequest(t,{}).subscribe(function(e){console.log(e),a.spinnerService.hide(),$("#delete_entity").modal("hide"),a.toastr.success("Successfully item is deleted","Success"),a.getListData()},function(e){console.log(e),a.spinnerService.hide()})}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}]),a}()).\u0275fac=function(e){return new(e||N)(v.Ub(d.c),v.Ub(L.b),v.Ub(D.a))},N.\u0275cmp=v.Ob({type:N,selectors:[["app-approval-process"]],decls:96,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-process/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Process"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Process"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"List"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"div",10),v.ac(18,"button",11),v.Lc(19,"Excel"),v.Zb(),v.ac(20,"button",11),v.Lc(21,"PDF"),v.Zb(),v.ac(22,"button",11),v.Vb(23,"i",12),v.Lc(24," Print"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(25,"div",13),v.ac(26,"div",14),v.ac(27,"div",15),v.ac(28,"div",16),v.ac(29,"div",17),v.Vb(30,"input",18),v.ac(31,"label",19),v.Lc(32,"Code"),v.Zb(),v.Zb(),v.Zb(),v.ac(33,"div",20),v.ac(34,"a",21),v.Lc(35," Search "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(36,"div",22),v.ac(37,"div",23),v.ac(38,"div",24),v.ac(39,"div",25),v.ac(40,"div",26),v.ac(41,"a",27),v.Vb(42,"i",28),v.Lc(43," New \xa0\xa0\xa0"),v.Zb(),v.Zb(),v.Zb(),v.ac(44,"div",29),v.ac(45,"div",30),v.ac(46,"div",31),v.ac(47,"div",32),v.ac(48,"span",33),v.Lc(49),v.Zb(),v.Zb(),v.Zb(),v.ac(50,"table",34),v.ac(51,"thead"),v.ac(52,"tr"),v.ac(53,"th"),v.Lc(54,"SL"),v.Zb(),v.ac(55,"th"),v.Lc(56,"Code"),v.Zb(),v.ac(57,"th"),v.Lc(58,"Process Name"),v.Zb(),v.ac(59,"th"),v.Lc(60,"Sequence"),v.Zb(),v.ac(61,"th"),v.Lc(62,"Remarks"),v.Zb(),v.ac(63,"th"),v.Lc(64,"Action"),v.Zb(),v.Zb(),v.Zb(),v.ac(65,"tbody"),v.Jc(66,Z,20,9,"tr",35),v.kc(67,"paginate"),v.Jc(68,y,4,0,"tr",36),v.Zb(),v.Zb(),v.ac(69,"div",37),v.ac(70,"div",38),v.Lc(71," Items per Page "),v.ac(72,"select",39),v.hc("change",function(e){return a.handlePageSizeChange(e)}),v.Jc(73,S,2,2,"option",40),v.Zb(),v.Zb(),v.ac(74,"div",41),v.ac(75,"pagination-controls",42),v.hc("pageChange",function(e){return a.handlePageChange(e)}),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(76,"ngx-spinner",43),v.ac(77,"p",44),v.Lc(78," Processing... "),v.Zb(),v.Zb(),v.ac(79,"div",45),v.ac(80,"div",46),v.ac(81,"div",47),v.ac(82,"div",48),v.ac(83,"div",49),v.ac(84,"h3"),v.Lc(85,"Delete Item"),v.Zb(),v.ac(86,"p"),v.Lc(87,"Are you sure want to delete?"),v.Zb(),v.Zb(),v.ac(88,"div",50),v.ac(89,"div",22),v.ac(90,"div",51),v.ac(91,"a",52),v.hc("click",function(){return a.deleteEnityData(a.tempId)}),v.Lc(92,"Delete"),v.Zb(),v.Zb(),v.ac(93,"div",51),v.ac(94,"a",53),v.Lc(95,"Cancel"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(49),v.Pc("Displaying ( ",(a.configPgn.pageNum-1)*a.configPgn.pageSize+1," to ",a.configPgn.pngDiplayLastSeq," of ",a.configPgn.totalItem," ) entries"),v.Ib(17),v.pc("ngForOf",v.mc(67,7,a.listData,a.configPgn)),v.Ib(2),v.pc("ngIf",0===a.listData.length),v.Ib(5),v.pc("ngForOf",a.configPgn.pageSizes),v.Ib(3),v.pc("fullScreen",!1))},directives:[g.e,n.l,n.m,u.c,d.a,p.s,p.y],pipes:[u.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),N),F=((k=function(){function a(t,i,c,o,n){e(this,a),this.formBuilder=t,this.datePipe=i,this.route=c,this.router=o,this.approvalProcessService=n,this.baseUrl=h.a.baseUrl}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({code:[""],processName:[""],sequence:[""],remarks:[""]})}},{key:"myFormSubmit",value:function(){var e=this,a=this.baseUrl+"/approvalProc/save",t={};(t=this.myForm.value).rActiveOperation="Create",this.approvalProcessService.sendPostRequest(a,t).subscribe(function(a){console.log(a),e.router.navigate(["/approval/approval-process"],{relativeTo:e.route})},function(e){console.log(e)})}},{key:"resetFormValues",value:function(){this.myForm.reset()}}]),a}()).\u0275fac=function(e){return new(e||k)(v.Ub(p.d),v.Ub(n.e),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a))},k.\u0275cmp=v.Ob({type:k,selectors:[["app-create"]],decls:57,vars:1,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-process",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","code",1,"form-control"],["type","text","formControlName","processName",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/approval/approval-process",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Process"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Process"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Create"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.myFormSubmit()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Code"),v.Zb(),v.ac(28,"div",19),v.Vb(29,"input",20),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Process Name"),v.Zb(),v.ac(33,"div",19),v.Vb(34,"input",21),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Sequence"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",17),v.ac(41,"label",18),v.Lc(42,"Remarks"),v.Zb(),v.ac(43,"div",19),v.Vb(44,"textarea",23),v.Zb(),v.Zb(),v.ac(45,"div",24),v.ac(46,"a",25),v.Vb(47,"i",11),v.Lc(48," Cancel"),v.Zb(),v.Lc(49," \xa0 \xa0 "),v.ac(50,"button",26),v.hc("click",function(){return a.resetFormValues()}),v.Vb(51,"i",27),v.Lc(52," Reset "),v.Zb(),v.Lc(53," \xa0 \xa0 "),v.ac(54,"button",28),v.Vb(55,"i",29),v.Lc(56," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm))},directives:[g.e,p.x,p.p,p.h,p.b,p.o,p.f,p.t],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),k),I=((P=function(){function a(t,i,c,o,n){e(this,a),this.formBuilder=t,this.route=i,this.router=c,this.approvalProcessService=o,this.spinnerService=n,this.baseUrl=h.a.baseUrl,this.myFormData={}}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],code:[""],processName:[""],sequence:[""],remarks:[""]})}},{key:"getFormData",value:function(){var e=this,a=this.baseUrl+"/approvalProc/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalProcessService.sendGetRequest(a,{}).subscribe(function(a){e.myFormData=a,console.log(e.myFormData),e.spinnerService.hide(),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"saveUpdatedFormData",value:function(){var e,a=this,t=this.baseUrl+"/approvalProc/save";console.log(t),e=this.myForm.value,this.spinnerService.show(),this.approvalProcessService.sendPostRequest(t,e).subscribe(function(e){console.log(e),a.spinnerService.hide(),a.router.navigate(["/approval/approval-process"],{relativeTo:a.route})},function(e){console.log(e),a.spinnerService.hide()})}}]),a}()).\u0275fac=function(e){return new(e||P)(v.Ub(p.d),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a),v.Ub(d.c))},P.\u0275cmp=v.Ob({type:P,selectors:[["app-edit"]],decls:57,vars:1,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-process",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","code",1,"form-control"],["type","text","formControlName","processName",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/approval/approval-process",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Process"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Process"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Edit"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.saveUpdatedFormData()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Code"),v.Zb(),v.ac(28,"div",19),v.Vb(29,"input",20),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Process Namee"),v.Zb(),v.ac(33,"div",19),v.Vb(34,"input",21),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Sequence"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",17),v.ac(41,"label",18),v.Lc(42,"Remarks"),v.Zb(),v.ac(43,"div",19),v.Vb(44,"textarea",23),v.Zb(),v.Zb(),v.ac(45,"div",24),v.ac(46,"a",25),v.Vb(47,"i",11),v.Lc(48," Cancel"),v.Zb(),v.Lc(49," \xa0 \xa0 "),v.ac(50,"button",26),v.hc("click",function(){return a.resetFormValues()}),v.Vb(51,"i",27),v.Lc(52," Reset "),v.Zb(),v.Lc(53," \xa0 \xa0 "),v.ac(54,"button",28),v.Vb(55,"i",29),v.Lc(56," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm))},directives:[g.e,p.x,p.p,p.h,p.b,p.o,p.f,p.t],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),P);function x(e,a){if(1&e){var t=v.bc();v.ac(0,"tr"),v.ac(1,"td"),v.Lc(2),v.Zb(),v.ac(3,"td",54),v.Lc(4),v.Zb(),v.ac(5,"td"),v.Lc(6),v.Zb(),v.ac(7,"td"),v.Lc(8),v.Zb(),v.ac(9,"td"),v.Lc(10),v.Zb(),v.ac(11,"td"),v.Lc(12),v.Zb(),v.ac(13,"td"),v.Lc(14),v.Zb(),v.ac(15,"td"),v.Lc(16),v.Zb(),v.ac(17,"td"),v.Lc(18),v.Zb(),v.ac(19,"td"),v.Lc(20),v.Zb(),v.ac(21,"td"),v.Lc(22),v.Zb(),v.ac(23,"td"),v.Lc(24," \xa0 "),v.ac(25,"a",55),v.Vb(26,"i",56),v.Zb(),v.Lc(27,"\xa0\xa0 "),v.ac(28,"a",57),v.hc("click",function(){v.Cc(t);var e=a.$implicit;return v.jc().tempId=e.id}),v.Vb(29,"i",58),v.Zb(),v.Zb(),v.Zb()}if(2&e){var i=a.$implicit,c=a.index,o=v.jc();v.Mb("active",c==o.currentIndex),v.Ib(2),v.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),v.Ib(2),v.Mc(i.id),v.Ib(2),v.Mc(i.referenceEntity?i.referenceEntity:"null"),v.Ib(2),v.Mc(i.approvalProcess.processName?i.approvalProcess.processName:"null"),v.Ib(2),v.Mc(i.approvalStep.approvalGroupName?i.approvalStep.approvalGroupName:"null"),v.Ib(2),v.Mc(i.sentToStepId?i.sentToStepId.approvalGroupName:"null"),v.Ib(2),v.Mc(i.approvalStepApprover?i.approvalStepApprover.bindLevel:"null"),v.Ib(2),v.Mc(i.approvalStepAction?i.approvalStepAction.activityStatusTitle:"null"),v.Ib(2),v.Mc(i.actionStatus?i.actionStatus:"null"),v.Ib(2),v.Mc(i.remarks?i.remarks:"null"),v.Ib(2),v.Mc(i.sequence?i.sequence:"null"),v.Ib(3),v.rc("routerLink","./edit/",i.id,"")}}function C(e,a){1&e&&(v.ac(0,"tr"),v.ac(1,"td",59),v.ac(2,"h5",60),v.Lc(3,"No data found"),v.Zb(),v.Zb(),v.Zb())}function q(e,a){if(1&e&&(v.ac(0,"option",61),v.Lc(1),v.Zb()),2&e){var t=a.$implicit;v.pc("value",t),v.Ib(1),v.Nc(" ",t," ")}}var w,U,z,V=((w=function(){function a(t,i,c){e(this,a),this.spinnerService=t,this.toastr=i,this.approvalService=c,this.baseUrl=h.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return t(a,[{key:"ngOnInit",value:function(){this.getListData()}},{key:"getListData",value:function(){var e,a=this,t=this.baseUrl+"/approvalProcTnxHtry/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(t,e).subscribe(function(e){a.listData=e.objectList,a.configPgn.totalItem=e.totalItems,a.configPgn.totalItems=e.totalItems,a.setDisplayLastSequence(),a.spinnerService.hide(),console.log(a.listData)},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){}},{key:"getUserQueryParams",value:function(e,a){var t={};return e&&(t.pageNum=e-0),a&&(t.pageSize=a),t}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}]),a}()).\u0275fac=function(e){return new(e||w)(v.Ub(d.c),v.Ub(L.b),v.Ub(D.a))},w.\u0275cmp=v.Ob({type:w,selectors:[["app-approval-process-tnx-history"]],decls:106,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-process-tnx-history/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Process Tnx History"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Process Tnx History"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"List"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"div",10),v.ac(18,"button",11),v.Lc(19,"Excel"),v.Zb(),v.ac(20,"button",11),v.Lc(21,"PDF"),v.Zb(),v.ac(22,"button",11),v.Vb(23,"i",12),v.Lc(24," Print"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(25,"div",13),v.ac(26,"div",14),v.ac(27,"div",15),v.ac(28,"div",16),v.ac(29,"div",17),v.Vb(30,"input",18),v.ac(31,"label",19),v.Lc(32,"Code"),v.Zb(),v.Zb(),v.Zb(),v.ac(33,"div",20),v.ac(34,"a",21),v.Lc(35," Search "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(36,"div",22),v.ac(37,"div",23),v.ac(38,"div",24),v.ac(39,"div",25),v.ac(40,"div",26),v.ac(41,"a",27),v.Vb(42,"i",28),v.Lc(43," New \xa0\xa0\xa0"),v.Zb(),v.Zb(),v.Zb(),v.ac(44,"div",29),v.ac(45,"div",30),v.ac(46,"div",31),v.ac(47,"div",32),v.ac(48,"span",33),v.Lc(49),v.Zb(),v.Zb(),v.Zb(),v.ac(50,"table",34),v.ac(51,"thead"),v.ac(52,"tr"),v.ac(53,"th"),v.Lc(54,"SL"),v.Zb(),v.ac(55,"th"),v.Lc(56,"Referance Entity Code"),v.Zb(),v.ac(57,"th"),v.Lc(58,"Approval Process"),v.Zb(),v.ac(59,"th"),v.Lc(60,"Approval Step"),v.Zb(),v.ac(61,"th"),v.Lc(62,"Send To Step"),v.Zb(),v.ac(63,"th"),v.Lc(64,"Approval Step Approver"),v.Zb(),v.ac(65,"th"),v.Lc(66,"Approval Step Action"),v.Zb(),v.ac(67,"th"),v.Lc(68,"Action Status"),v.Zb(),v.ac(69,"th"),v.Lc(70,"Remarks"),v.Zb(),v.ac(71,"th"),v.Lc(72,"Sequence"),v.Zb(),v.ac(73,"th"),v.Lc(74,"Action"),v.Zb(),v.Zb(),v.Zb(),v.ac(75,"tbody"),v.Jc(76,x,30,14,"tr",35),v.kc(77,"paginate"),v.Jc(78,C,4,0,"tr",36),v.Zb(),v.Zb(),v.ac(79,"div",37),v.ac(80,"div",38),v.Lc(81," Items per Page "),v.ac(82,"select",39),v.hc("change",function(e){return a.handlePageSizeChange(e)}),v.Jc(83,q,2,2,"option",40),v.Zb(),v.Zb(),v.ac(84,"div",41),v.ac(85,"pagination-controls",42),v.hc("pageChange",function(e){return a.handlePageChange(e)}),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(86,"ngx-spinner",43),v.ac(87,"p",44),v.Lc(88," Processing... "),v.Zb(),v.Zb(),v.ac(89,"div",45),v.ac(90,"div",46),v.ac(91,"div",47),v.ac(92,"div",48),v.ac(93,"div",49),v.ac(94,"h3"),v.Lc(95,"Delete Item"),v.Zb(),v.ac(96,"p"),v.Lc(97,"Are you sure want to delete?"),v.Zb(),v.Zb(),v.ac(98,"div",50),v.ac(99,"div",22),v.ac(100,"div",51),v.ac(101,"a",52),v.Lc(102,"Delete"),v.Zb(),v.Zb(),v.ac(103,"div",51),v.ac(104,"a",53),v.Lc(105,"Cancel"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(49),v.Pc("Displaying ( ",(a.configPgn.pageNum-1)*a.configPgn.pageSize+1," to ",a.configPgn.pngDiplayLastSeq," of ",a.configPgn.totalItem," ) entries"),v.Ib(27),v.pc("ngForOf",v.mc(77,7,a.listData,a.configPgn)),v.Ib(2),v.pc("ngIf",0===a.listData.length),v.Ib(5),v.pc("ngForOf",a.configPgn.pageSizes),v.Ib(3),v.pc("fullScreen",!1))},directives:[g.e,n.l,n.m,u.c,d.a,p.s,p.y],pipes:[u.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),w),O=c("xrk7"),G=c("ZOsW"),E=((z=function(){function a(t,i,c,o,n){e(this,a),this.formBuilder=t,this.route=i,this.commonService=c,this.router=o,this.approvalService=n,this.baseUrl=h.a.baseUrl,this._initConfigDDL()}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({approvalGroupName:["",p.w.required],approvalProcess:[{},p.w.required],preApprovalNode:["",p.w.required],thisApprovalNode:["",p.w.required],nextApprovalNode:["",p.w.required],putOnStatusPositive:["",p.w.required],putOnStatusNegative:["",p.w.required],sequence:["",p.w.required],isActive:["",p.w.required]})}},{key:"myFormSubmit",value:function(){var e,a=this;this.myForm.invalid||(e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null}),this.approvalService.sendPostRequest(this.baseUrl+"/approvalStep/save",e).subscribe(function(e){console.log(e),a.router.navigate(["/approval/approval-step"],{relativeTo:a.route})},function(e){console.log(e)}))}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,a=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(a,t).subscribe(function(a){e.configDDL.append?e.configDDL.listData=e.configDDL.listData.concat(a.objectList):(e.configDDL.listData=a.objectList,console.log(e.configDDL.listData)),e.configDDL.totalItem=a.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,a,t,i){console.log("..."),console.log("ddlActiveFieldName:"+a),console.log("dataGetApiPathDDL:"+t),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=a&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=a,this.configDDL.dataGetApiPath=t,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getApprovalProcess",get:function(){return this.myForm.get("approvalProcess")}}]),a}()).\u0275fac=function(e){return new(e||z)(v.Ub(p.d),v.Ub(g.a),v.Ub(O.a),v.Ub(g.c),v.Ub(D.a))},z.\u0275cmp=v.Ob({type:z,selectors:[["app-approval-step-create"]],decls:82,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","approvalGroupName",1,"form-control"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","preApprovalNode",1,"form-control"],["type","number","formControlName","thisApprovalNode",1,"form-control"],["type","number","formControlName","nextApprovalNode",1,"form-control"],["type","number","formControlName","putOnStatusPositive",1,"form-control"],["type","text","formControlName","putOnStatusNegative",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Create"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.myFormSubmit()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Group Name"),v.Zb(),v.ac(28,"div",19),v.Vb(29,"input",20),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Approval Process *"),v.Zb(),v.ac(33,"div",19),v.ac(34,"ng-select",21),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/approvalProc/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Pre Approval Node"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",17),v.ac(41,"label",18),v.Lc(42,"This Approval Node"),v.Zb(),v.ac(43,"div",19),v.Vb(44,"input",23),v.Zb(),v.Zb(),v.ac(45,"div",17),v.ac(46,"label",18),v.Lc(47,"Next Approval Node"),v.Zb(),v.ac(48,"div",19),v.Vb(49,"input",24),v.Zb(),v.Zb(),v.ac(50,"div",17),v.ac(51,"label",18),v.Lc(52,"Positive Statuse"),v.Zb(),v.ac(53,"div",19),v.Vb(54,"input",25),v.Zb(),v.Zb(),v.ac(55,"div",17),v.ac(56,"label",18),v.Lc(57,"Negative Status"),v.Zb(),v.ac(58,"div",19),v.Vb(59,"input",26),v.Zb(),v.Zb(),v.ac(60,"div",17),v.ac(61,"label",18),v.Lc(62,"Sequence"),v.Zb(),v.ac(63,"div",19),v.Vb(64,"input",27),v.Zb(),v.Zb(),v.ac(65,"div",17),v.ac(66,"label",18),v.Lc(67,"Is Active"),v.Zb(),v.ac(68,"div",19),v.Vb(69,"input",28),v.Zb(),v.Zb(),v.ac(70,"div",29),v.ac(71,"a",30),v.Vb(72,"i",11),v.Lc(73," Cancel"),v.Zb(),v.Lc(74," \xa0 \xa0 "),v.ac(75,"button",31),v.hc("click",function(){return a.resetFormValues()}),v.Vb(76,"i",32),v.Lc(77," Reset "),v.Zb(),v.Lc(78," \xa0 \xa0 "),v.ac(79,"button",33),v.Vb(80,"i",34),v.Lc(81," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm),v.Ib(10),v.pc("items",a.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[g.e,p.x,p.p,p.h,p.b,p.o,p.f,G.a,p.t,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),z),T=((U=function(){function a(t,i,c,o,n,r){e(this,a),this.formBuilder=t,this.route=i,this.router=c,this.approvalService=o,this.spinnerService=n,this.commonService=r,this.baseUrl=h.a.baseUrl,this.myFormData={},this._initConfigDDL()}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],approvalGroupName:["",p.w.required],approvalProcess:["",p.w.required],preApprovalNode:["",p.w.required],thisApprovalNode:["",p.w.required],nextApprovalNode:["",p.w.required],putOnStatusPositive:["",p.w.required],putOnStatusNegative:["",p.w.required],sequence:["",p.w.required],isActive:["",p.w.required]})}},{key:"getFormData",value:function(){var e=this,a=this.baseUrl+"/approvalStep/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalService.sendGetRequest(a,{}).subscribe(function(a){e.myFormData=a,console.log(e.myFormData);var t=[{id:a.approvalProcess.id,processName:a.approvalProcess.processName}];console.log(t),e.configDDL.listData=t,e.myFormData.approvalProcess=a.approvalProcess.id,e.spinnerService.hide(),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"saveUpdatedFormData",value:function(){var e=this;if(!this.myForm.invalid){var a=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null});console.log(a);var t,i=this.baseUrl+"/approvalStep/edit";console.log(i),t=a,this.spinnerService.show(),this.approvalService.sendPutRequest(i,t).subscribe(function(a){console.log(a),e.spinnerService.hide(),e.router.navigate(["/approval/approval-step"],{relativeTo:e.route})},function(a){console.log(a),e.spinnerService.hide()})}}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,a=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(a,t).subscribe(function(a){e.configDDL.append?e.configDDL.listData=e.configDDL.listData.concat(a.objectList):(e.configDDL.listData=a.objectList,console.log(e.configDDL.listData)),e.configDDL.totalItem=a.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,a,t,i){console.log("..."),console.log("ddlActiveFieldName:"+a),console.log("dataGetApiPathDDL:"+t),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=a&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=a,this.configDDL.dataGetApiPath=t,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getApprovalProcess",get:function(){return this.myForm.get("approvalProcess")}}]),a}()).\u0275fac=function(e){return new(e||U)(v.Ub(p.d),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a),v.Ub(d.c),v.Ub(O.a))},U.\u0275cmp=v.Ob({type:U,selectors:[["app-approval-step-edit"]],decls:82,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","approvalGroupName",1,"form-control"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","preApprovalNode",1,"form-control"],["type","number","formControlName","thisApprovalNode",1,"form-control"],["type","number","formControlName","nextApprovalNode",1,"form-control"],["type","number","formControlName","putOnStatusPositive",1,"form-control"],["type","text","formControlName","putOnStatusNegative",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Edit"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.saveUpdatedFormData()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Group Name"),v.Zb(),v.ac(28,"div",19),v.Vb(29,"input",20),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Approval Process *"),v.Zb(),v.ac(33,"div",19),v.ac(34,"ng-select",21),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/approvalProc/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Pre Approval Node"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",17),v.ac(41,"label",18),v.Lc(42,"This Approval Node"),v.Zb(),v.ac(43,"div",19),v.Vb(44,"input",23),v.Zb(),v.Zb(),v.ac(45,"div",17),v.ac(46,"label",18),v.Lc(47,"Next Approval Node"),v.Zb(),v.ac(48,"div",19),v.Vb(49,"input",24),v.Zb(),v.Zb(),v.ac(50,"div",17),v.ac(51,"label",18),v.Lc(52,"Positive Statuse"),v.Zb(),v.ac(53,"div",19),v.Vb(54,"input",25),v.Zb(),v.Zb(),v.ac(55,"div",17),v.ac(56,"label",18),v.Lc(57,"Negative Status"),v.Zb(),v.ac(58,"div",19),v.Vb(59,"input",26),v.Zb(),v.Zb(),v.ac(60,"div",17),v.ac(61,"label",18),v.Lc(62,"Sequence"),v.Zb(),v.ac(63,"div",19),v.Vb(64,"input",27),v.Zb(),v.Zb(),v.ac(65,"div",17),v.ac(66,"label",18),v.Lc(67,"Is Active"),v.Zb(),v.ac(68,"div",19),v.Vb(69,"input",28),v.Zb(),v.Zb(),v.ac(70,"div",29),v.ac(71,"a",30),v.Vb(72,"i",11),v.Lc(73," Cancel"),v.Zb(),v.Lc(74," \xa0 \xa0 "),v.ac(75,"button",31),v.hc("click",function(){return a.resetFormValues()}),v.Vb(76,"i",32),v.Lc(77," Reset "),v.Zb(),v.Lc(78," \xa0 \xa0 "),v.ac(79,"button",33),v.Vb(80,"i",34),v.Lc(81," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm),v.Ib(10),v.pc("items",a.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[g.e,p.x,p.p,p.h,p.b,p.o,p.f,G.a,p.t,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),U);function M(e,a){if(1&e){var t=v.bc();v.ac(0,"tr"),v.ac(1,"td"),v.Lc(2),v.Zb(),v.ac(3,"td",54),v.Lc(4),v.Zb(),v.ac(5,"td"),v.Lc(6),v.Zb(),v.ac(7,"td"),v.Lc(8),v.Zb(),v.ac(9,"td"),v.Lc(10),v.Zb(),v.ac(11,"td"),v.Lc(12),v.Zb(),v.ac(13,"td"),v.Lc(14),v.Zb(),v.ac(15,"td"),v.Lc(16),v.Zb(),v.ac(17,"td"),v.Lc(18),v.Zb(),v.ac(19,"td"),v.Lc(20),v.Zb(),v.ac(21,"td"),v.Lc(22),v.Zb(),v.ac(23,"td"),v.Lc(24," \xa0 "),v.ac(25,"a",55),v.Vb(26,"i",56),v.Zb(),v.Lc(27,"\xa0\xa0 "),v.ac(28,"a",57),v.hc("click",function(){v.Cc(t);var e=a.$implicit;return v.jc().tempId=e.id}),v.Vb(29,"i",58),v.Zb(),v.Zb(),v.Zb()}if(2&e){var i=a.$implicit,c=a.index,o=v.jc();v.Mb("active",c==o.currentIndex),v.Ib(2),v.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),v.Ib(2),v.Mc(i.id),v.Ib(2),v.Mc(i.approvalGroupName),v.Ib(2),v.Mc(i.approvalProcess.processName),v.Ib(2),v.Mc(i.preApprovalNode),v.Ib(2),v.Mc(i.thisApprovalNode),v.Ib(2),v.Mc(i.nextApprovalNode),v.Ib(2),v.Mc(i.putOnStatusPositive),v.Ib(2),v.Mc(i.putOnStatusNegative),v.Ib(2),v.Mc(i.sequence),v.Ib(2),v.Mc(i.isActive),v.Ib(3),v.rc("routerLink","./edit/",i.id,"")}}function j(e,a){1&e&&(v.ac(0,"tr"),v.ac(1,"td",59),v.ac(2,"h5",60),v.Lc(3,"No data found"),v.Zb(),v.Zb(),v.Zb())}function R(e,a){if(1&e&&(v.ac(0,"option",61),v.Lc(1),v.Zb()),2&e){var t=a.$implicit;v.pc("value",t),v.Ib(1),v.Nc(" ",t," ")}}var _,B,H,Q=((H=function(){function a(t,i,c){e(this,a),this.spinnerService=t,this.toastr=i,this.approvalService=c,this.baseUrl=h.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return t(a,[{key:"ngOnInit",value:function(){this.getListData()}},{key:"getListData",value:function(){var e,a=this,t=this.baseUrl+"/approvalStep/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(t,e).subscribe(function(e){a.listData=e.objectList,a.configPgn.totalItem=e.totalItems,a.configPgn.totalItems=e.totalItems,a.setDisplayLastSequence(),a.spinnerService.hide()},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var a=this,t=this.baseUrl+"/approvalStep/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalService.sendDeleteRequest(t,{}).subscribe(function(e){console.log(e),a.spinnerService.hide(),$("#delete_entity").modal("hide"),a.toastr.success("Successfully item is deleted","Success"),a.getListData()},function(e){console.log(e),a.spinnerService.hide()})}},{key:"getUserQueryParams",value:function(e,a){var t={};return e&&(t.pageNum=e-0),a&&(t.pageSize=a),t}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}]),a}()).\u0275fac=function(e){return new(e||H)(v.Ub(d.c),v.Ub(L.b),v.Ub(D.a))},H.\u0275cmp=v.Ob({type:H,selectors:[["app-approval-step"]],decls:106,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-step/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"List"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"div",10),v.ac(18,"button",11),v.Lc(19,"Excel"),v.Zb(),v.ac(20,"button",11),v.Lc(21,"PDF"),v.Zb(),v.ac(22,"button",11),v.Vb(23,"i",12),v.Lc(24," Print"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(25,"div",13),v.ac(26,"div",14),v.ac(27,"div",15),v.ac(28,"div",16),v.ac(29,"div",17),v.Vb(30,"input",18),v.ac(31,"label",19),v.Lc(32,"Code"),v.Zb(),v.Zb(),v.Zb(),v.ac(33,"div",20),v.ac(34,"a",21),v.Lc(35," Search "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(36,"div",22),v.ac(37,"div",23),v.ac(38,"div",24),v.ac(39,"div",25),v.ac(40,"div",26),v.ac(41,"a",27),v.Vb(42,"i",28),v.Lc(43," New \xa0\xa0\xa0"),v.Zb(),v.Zb(),v.Zb(),v.ac(44,"div",29),v.ac(45,"div",30),v.ac(46,"div",31),v.ac(47,"div",32),v.ac(48,"span",33),v.Lc(49),v.Zb(),v.Zb(),v.Zb(),v.ac(50,"table",34),v.ac(51,"thead"),v.ac(52,"tr"),v.ac(53,"th"),v.Lc(54,"SL"),v.Zb(),v.ac(55,"th"),v.Lc(56,"Approval Group"),v.Zb(),v.ac(57,"th"),v.Lc(58,"Approval Process"),v.Zb(),v.ac(59,"th"),v.Lc(60,"Pre Approval Node"),v.Zb(),v.ac(61,"th"),v.Lc(62,"This Approval Node"),v.Zb(),v.ac(63,"th"),v.Lc(64,"Next Approval Node"),v.Zb(),v.ac(65,"th"),v.Lc(66,"Psoitive Status"),v.Zb(),v.ac(67,"th"),v.Lc(68,"Negative Status"),v.Zb(),v.ac(69,"th"),v.Lc(70,"Sequence"),v.Zb(),v.ac(71,"th"),v.Lc(72,"Is Active"),v.Zb(),v.ac(73,"th"),v.Lc(74,"Action"),v.Zb(),v.Zb(),v.Zb(),v.ac(75,"tbody"),v.Jc(76,M,30,14,"tr",35),v.kc(77,"paginate"),v.Jc(78,j,4,0,"tr",36),v.Zb(),v.Zb(),v.ac(79,"div",37),v.ac(80,"div",38),v.Lc(81," Items per Page "),v.ac(82,"select",39),v.hc("change",function(e){return a.handlePageSizeChange(e)}),v.Jc(83,R,2,2,"option",40),v.Zb(),v.Zb(),v.ac(84,"div",41),v.ac(85,"pagination-controls",42),v.hc("pageChange",function(e){return a.handlePageChange(e)}),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(86,"ngx-spinner",43),v.ac(87,"p",44),v.Lc(88," Processing... "),v.Zb(),v.Zb(),v.ac(89,"div",45),v.ac(90,"div",46),v.ac(91,"div",47),v.ac(92,"div",48),v.ac(93,"div",49),v.ac(94,"h3"),v.Lc(95,"Delete Item"),v.Zb(),v.ac(96,"p"),v.Lc(97,"Are you sure want to delete?"),v.Zb(),v.Zb(),v.ac(98,"div",50),v.ac(99,"div",22),v.ac(100,"div",51),v.ac(101,"a",52),v.hc("click",function(){return a.deleteEnityData(a.tempId)}),v.Lc(102,"Delete"),v.Zb(),v.Zb(),v.ac(103,"div",51),v.ac(104,"a",53),v.Lc(105,"Cancel"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(49),v.Pc("Displaying ( ",(a.configPgn.pageNum-1)*a.configPgn.pageSize+1," to ",a.configPgn.pngDiplayLastSeq," of ",a.configPgn.totalItem," ) entries"),v.Ib(27),v.pc("ngForOf",v.mc(77,7,a.listData,a.configPgn)),v.Ib(2),v.pc("ngIf",0===a.listData.length),v.Ib(5),v.pc("ngForOf",a.configPgn.pageSizes),v.Ib(3),v.pc("fullScreen",!1))},directives:[g.e,n.l,n.m,u.c,d.a,p.s,p.y],pipes:[u.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),H),J=((B=function(){function a(t,i,c,o,n,r){e(this,a),this.commonService=t,this.formBuilder=i,this.datePipe=c,this.route=o,this.router=n,this.approvalService=r,this.baseUrl=h.a.baseUrl,this._initConfigDDL()}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({approvalProcess:[{},p.w.required],approvalStep:[{},p.w.required],activityStatusCode:["",p.w.required],activityStatusTitle:["",p.w.required],sequence:["",p.w.required],isActive:["",p.w.required]})}},{key:"myFormSubmit",value:function(){var e,a=this;this.myForm.invalid||(e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPostRequest(this.baseUrl+"/approvalStepAction/save",e).subscribe(function(e){console.log(e),a.router.navigate(["/approval/approval-step-action"],{relativeTo:a.route})},function(e){console.log(e)}))}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,a=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(a,t).subscribe(function(a){e.configDDL.append?e.configDDL.listData[e.configDDL.activeFieldName]=e.configDDL.listData.concat(a.objectList):(e.configDDL.listData[e.configDDL.activeFieldName]=a.objectList,console.log(e.configDDL.listData)),e.configDDL.totalItem=a.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:{},append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,a,t,i){console.log("..."),console.log("ddlActiveFieldName:"+a),console.log("dataGetApiPathDDL:"+t),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=a&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=a,this.configDDL.dataGetApiPath=t,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getApprovalStep",get:function(){return this.myForm.get("approvalStep")}},{key:"getApprovalProcess",get:function(){return this.myForm.get("approvalProcess")}}]),a}()).\u0275fac=function(e){return new(e||B)(v.Ub(O.a),v.Ub(p.d),v.Ub(n.e),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a))},B.\u0275cmp=v.Ob({type:B,selectors:[["app-approval-step-action-create"]],decls:67,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-action",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","processName",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","approvalGroupName",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","text","formControlName","activityStatusCode",1,"form-control"],["type","text","formControlName","activityStatusTitle",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step-action",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step Actoin"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step Actoin"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Create"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.myFormSubmit()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Approval Process *"),v.Zb(),v.ac(28,"div",19),v.ac(29,"ng-select",20),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"processName","/approvalProc/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Approval Step *"),v.Zb(),v.ac(33,"div",19),v.ac(34,"ng-select",21),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"approvalGroupName","/approvalStep/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Activity Status Code"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",17),v.ac(41,"label",18),v.Lc(42,"Activity Status Title"),v.Zb(),v.ac(43,"div",19),v.Vb(44,"input",23),v.Zb(),v.Zb(),v.ac(45,"div",17),v.ac(46,"label",18),v.Lc(47,"Sequence"),v.Zb(),v.ac(48,"div",19),v.Vb(49,"input",24),v.Zb(),v.Zb(),v.ac(50,"div",17),v.ac(51,"label",18),v.Lc(52,"Is Active"),v.Zb(),v.ac(53,"div",19),v.Vb(54,"input",25),v.Zb(),v.Zb(),v.ac(55,"div",26),v.ac(56,"a",27),v.Vb(57,"i",11),v.Lc(58," Cancel"),v.Zb(),v.Lc(59," \xa0 \xa0 "),v.ac(60,"button",28),v.hc("click",function(){return a.resetFormValues()}),v.Vb(61,"i",29),v.Lc(62," Reset "),v.Zb(),v.Lc(63," \xa0 \xa0 "),v.ac(64,"button",30),v.Vb(65,"i",31),v.Lc(66," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm),v.Ib(5),v.pc("items",a.configDDL.listData.processName)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),v.Ib(5),v.pc("items",a.configDDL.listData.approvalGroupName)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[g.e,p.x,p.p,p.h,G.a,p.o,p.f,p.b,p.t,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),B),K=((_=function(){function a(t,i,c,o,n,r){e(this,a),this.formBuilder=t,this.route=i,this.router=c,this.approvalService=o,this.spinnerService=n,this.commonService=r,this.baseUrl=h.a.baseUrl,this.myFormData={},this._initConfigDDL()}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],approvalProcess:["",p.w.required],approvalStep:["",p.w.required],activityStatusCode:["",p.w.required],activityStatusTitle:["",p.w.required],sequence:["",p.w.required],isActive:["",p.w.required]})}},{key:"getFormData",value:function(){var e=this,a=this.baseUrl+"/approvalStepAction/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalService.sendGetRequest(a,{}).subscribe(function(a){e.myFormData=a,console.log(e.myFormData),e.spinnerService.hide();var t=[{id:a.approvalProcess.id,processName:a.approvalProcess.processName}];console.log(t),e.configDDL.listData=t,e.myFormData.approvalProcess=a.approvalProcess.id;var i=[{id:a.approvalStep.id,approvalGroupName:a.approvalStep.approvalGroupName}];console.log(i),e.configDDL.listData2=i,e.myFormData.approvalStep=a.approvalStep.id,e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e,a=this;this.myForm.invalid||(e=Object.assign(this.myForm.value,{approvalProcess:this.getApprovalProcess.value?{id:this.getApprovalProcess.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPutRequest(this.baseUrl+"/approvalStepAction/edit",e).subscribe(function(e){console.log(e),a.router.navigate(["/approval/approval-step-action"],{relativeTo:a.route})},function(e){console.log(e)}))}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,a=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(a,t).subscribe(function(a){e.configDDL.append?(e.configDDL.listData=e.configDDL.listData.concat(a.objectList),e.configDDL.listData2=e.configDDL.listData2.concat(a.objectList)):(e.configDDL.listData=a.objectList,e.configDDL.listData2=a.objectList),e.configDDL.totalItem=a.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,a,t,i){console.log("..."),console.log("ddlActiveFieldName:"+a),console.log("dataGetApiPathDDL:"+t),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=a&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=a,this.configDDL.dataGetApiPath=t,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getApprovalStep",get:function(){return this.myForm.get("approvalStep")}},{key:"getApprovalProcess",get:function(){return this.myForm.get("approvalProcess")}}]),a}()).\u0275fac=function(e){return new(e||_)(v.Ub(p.d),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a),v.Ub(d.c),v.Ub(O.a))},_.\u0275cmp=v.Ob({type:_,selectors:[["app-approval-step-action-edit"]],decls:67,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-action",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","approvalProcess","placeholder","Select Approval Process","bindLabel","processName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","text","formControlName","activityStatusCode",1,"form-control"],["type","text","formControlName","activityStatusTitle",1,"form-control"],["type","number","formControlName","sequence",1,"form-control"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step-action",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step Action"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step Action"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Edit"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.saveUpdatedFormData()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Approval Process *"),v.Zb(),v.ac(28,"div",19),v.ac(29,"ng-select",20),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/approvalProc/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Approval Step *"),v.Zb(),v.ac(33,"div",19),v.ac(34,"ng-select",21),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/approvalStep/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Activity Status Code"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",17),v.ac(41,"label",18),v.Lc(42,"Activity Status Title"),v.Zb(),v.ac(43,"div",19),v.Vb(44,"input",23),v.Zb(),v.Zb(),v.ac(45,"div",17),v.ac(46,"label",18),v.Lc(47,"Sequence"),v.Zb(),v.ac(48,"div",19),v.Vb(49,"input",24),v.Zb(),v.Zb(),v.ac(50,"div",17),v.ac(51,"label",18),v.Lc(52,"Is Active"),v.Zb(),v.ac(53,"div",19),v.Vb(54,"input",25),v.Zb(),v.Zb(),v.ac(55,"div",26),v.ac(56,"a",27),v.Vb(57,"i",11),v.Lc(58," Cancel"),v.Zb(),v.Lc(59," \xa0 \xa0 "),v.ac(60,"button",28),v.hc("click",function(){return a.resetFormValues()}),v.Vb(61,"i",29),v.Lc(62," Reset "),v.Zb(),v.Lc(63," \xa0 \xa0 "),v.ac(64,"button",30),v.Vb(65,"i",31),v.Lc(66," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm),v.Ib(5),v.pc("items",a.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),v.Ib(5),v.pc("items",a.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[g.e,p.x,p.p,p.h,G.a,p.o,p.f,p.b,p.t,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),_);function W(e,a){if(1&e){var t=v.bc();v.ac(0,"tr"),v.ac(1,"td"),v.Lc(2),v.Zb(),v.ac(3,"td",54),v.Lc(4),v.Zb(),v.ac(5,"td"),v.Lc(6),v.Zb(),v.ac(7,"td"),v.Lc(8),v.Zb(),v.ac(9,"td"),v.Lc(10),v.Zb(),v.ac(11,"td"),v.Lc(12),v.Zb(),v.ac(13,"td"),v.Lc(14),v.Zb(),v.ac(15,"td"),v.Lc(16),v.Zb(),v.ac(17,"td"),v.Lc(18," \xa0 "),v.ac(19,"a",55),v.Vb(20,"i",56),v.Zb(),v.Lc(21,"\xa0\xa0 "),v.ac(22,"a",57),v.hc("click",function(){v.Cc(t);var e=a.$implicit;return v.jc().tempId=e.id}),v.Vb(23,"i",58),v.Zb(),v.Zb(),v.Zb()}if(2&e){var i=a.$implicit,c=a.index,o=v.jc();v.Mb("active",c==o.currentIndex),v.Ib(2),v.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),v.Ib(2),v.Mc(i.id),v.Ib(2),v.Mc(i.approvalProcess.processName),v.Ib(2),v.Mc(i.approvalStep.approvalGroupName),v.Ib(2),v.Mc(i.activityStatusCode),v.Ib(2),v.Mc(i.activityStatusTitle),v.Ib(2),v.Mc(i.sequence),v.Ib(2),v.Mc(i.isActive),v.Ib(3),v.rc("routerLink","./edit/",i.id,"")}}function X(e,a){1&e&&(v.ac(0,"tr"),v.ac(1,"td",59),v.ac(2,"h5",60),v.Lc(3,"No data found"),v.Zb(),v.Zb(),v.Zb())}function Y(e,a){if(1&e&&(v.ac(0,"option",61),v.Lc(1),v.Zb()),2&e){var t=a.$implicit;v.pc("value",t),v.Ib(1),v.Nc(" ",t," ")}}var ee,ae,te,ie=((te=function(){function a(t,i,c){e(this,a),this.spinnerService=t,this.toastr=i,this.approvalService=c,this.baseUrl=h.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return t(a,[{key:"ngOnInit",value:function(){this.getListData()}},{key:"getListData",value:function(){var e,a=this,t=this.baseUrl+"/approvalStepAction/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(t,e).subscribe(function(e){a.listData=e.objectList,a.configPgn.totalItem=e.totalItems,a.configPgn.totalItems=e.totalItems,a.setDisplayLastSequence(),a.spinnerService.hide(),console.log(a.listData)},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var a=this,t=this.baseUrl+"/approvalStepAction/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalService.sendDeleteRequest(t,{}).subscribe(function(e){console.log(e),a.spinnerService.hide(),$("#delete_entity").modal("hide"),a.toastr.success("Successfully item is deleted","Success"),a.getListData()},function(e){console.log(e),a.spinnerService.hide()})}},{key:"getUserQueryParams",value:function(e,a){var t={};return e&&(t.pageNum=e-0),a&&(t.pageSize=a),t}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}]),a}()).\u0275fac=function(e){return new(e||te)(v.Ub(d.c),v.Ub(L.b),v.Ub(D.a))},te.\u0275cmp=v.Ob({type:te,selectors:[["app-approval-step-action"]],decls:100,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-step-action/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step Action"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step Action"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"List"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"div",10),v.ac(18,"button",11),v.Lc(19,"Excel"),v.Zb(),v.ac(20,"button",11),v.Lc(21,"PDF"),v.Zb(),v.ac(22,"button",11),v.Vb(23,"i",12),v.Lc(24," Print"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(25,"div",13),v.ac(26,"div",14),v.ac(27,"div",15),v.ac(28,"div",16),v.ac(29,"div",17),v.Vb(30,"input",18),v.ac(31,"label",19),v.Lc(32,"Code"),v.Zb(),v.Zb(),v.Zb(),v.ac(33,"div",20),v.ac(34,"a",21),v.Lc(35," Search "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(36,"div",22),v.ac(37,"div",23),v.ac(38,"div",24),v.ac(39,"div",25),v.ac(40,"div",26),v.ac(41,"a",27),v.Vb(42,"i",28),v.Lc(43," New \xa0\xa0\xa0"),v.Zb(),v.Zb(),v.Zb(),v.ac(44,"div",29),v.ac(45,"div",30),v.ac(46,"div",31),v.ac(47,"div",32),v.ac(48,"span",33),v.Lc(49),v.Zb(),v.Zb(),v.Zb(),v.ac(50,"table",34),v.ac(51,"thead"),v.ac(52,"tr"),v.ac(53,"th"),v.Lc(54,"SL"),v.Zb(),v.ac(55,"th"),v.Lc(56,"Approval Process"),v.Zb(),v.ac(57,"th"),v.Lc(58,"Approval Step"),v.Zb(),v.ac(59,"th"),v.Lc(60,"Activity Status Code"),v.Zb(),v.ac(61,"th"),v.Lc(62,"Activity Status Title"),v.Zb(),v.ac(63,"th"),v.Lc(64,"Sequence"),v.Zb(),v.ac(65,"th"),v.Lc(66,"Is Active"),v.Zb(),v.ac(67,"th"),v.Lc(68,"Action"),v.Zb(),v.Zb(),v.Zb(),v.ac(69,"tbody"),v.Jc(70,W,24,11,"tr",35),v.kc(71,"paginate"),v.Jc(72,X,4,0,"tr",36),v.Zb(),v.Zb(),v.ac(73,"div",37),v.ac(74,"div",38),v.Lc(75," Items per Page "),v.ac(76,"select",39),v.hc("change",function(e){return a.handlePageSizeChange(e)}),v.Jc(77,Y,2,2,"option",40),v.Zb(),v.Zb(),v.ac(78,"div",41),v.ac(79,"pagination-controls",42),v.hc("pageChange",function(e){return a.handlePageChange(e)}),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(80,"ngx-spinner",43),v.ac(81,"p",44),v.Lc(82," Processing... "),v.Zb(),v.Zb(),v.ac(83,"div",45),v.ac(84,"div",46),v.ac(85,"div",47),v.ac(86,"div",48),v.ac(87,"div",49),v.ac(88,"h3"),v.Lc(89,"Delete Item"),v.Zb(),v.ac(90,"p"),v.Lc(91,"Are you sure want to delete?"),v.Zb(),v.Zb(),v.ac(92,"div",50),v.ac(93,"div",22),v.ac(94,"div",51),v.ac(95,"a",52),v.hc("click",function(){return a.deleteEnityData(a.tempId)}),v.Lc(96,"Delete"),v.Zb(),v.Zb(),v.ac(97,"div",51),v.ac(98,"a",53),v.Lc(99,"Cancel"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(49),v.Pc("Displaying ( ",(a.configPgn.pageNum-1)*a.configPgn.pageSize+1," to ",a.configPgn.pngDiplayLastSeq," of ",a.configPgn.totalItem," ) entries"),v.Ib(21),v.pc("ngForOf",v.mc(71,7,a.listData,a.configPgn)),v.Ib(2),v.pc("ngIf",0===a.listData.length),v.Ib(5),v.pc("ngForOf",a.configPgn.pageSizes),v.Ib(3),v.pc("fullScreen",!1))},directives:[g.e,n.l,n.m,u.c,d.a,p.s,p.y],pipes:[u.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),te),ce=((ae=function(){function a(t,i,c,o,n,r){e(this,a),this.commonService=t,this.formBuilder=i,this.datePipe=c,this.route=o,this.router=n,this.approvalService=r,this.baseUrl=h.a.baseUrl,this._initConfigDDL()}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({hrCrEmp:["",p.w.required],approvalStep:["",p.w.required],isActive:["",p.w.required]})}},{key:"myFormSubmit",value:function(){var e,a=this;this.myForm.invalid||(e=Object.assign(this.myForm.value,{approvalMemberId:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPostRequest(this.baseUrl+"/approvalStepApprover/save",e).subscribe(function(e){console.log(e),a.router.navigate(["/approval/approval-step-approver"],{relativeTo:a.route})},function(e){console.log(e)}))}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,a=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(a,t).subscribe(function(a){e.configDDL.append?e.configDDL.listData=e.configDDL.listData.concat(a.objectList):(e.configDDL.listData=a.objectList,console.log(e.configDDL.listData)),e.configDDL.totalItem=a.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,a,t,i){console.log("..."),console.log("ddlActiveFieldName:"+a),console.log("dataGetApiPathDDL:"+t),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=a&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=a,this.configDDL.dataGetApiPath=t,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getApprovalStep",get:function(){return this.myForm.get("approvalStep")}}]),a}()).\u0275fac=function(e){return new(e||ae)(v.Ub(O.a),v.Ub(p.d),v.Ub(n.e),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a))},ae.\u0275cmp=v.Ob({type:ae,selectors:[["app-approval-step-approver-create"]],decls:52,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-approver",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","checkbox","formControlName","isActive",1,"big-checkbox"],[1,"text-right"],["routerLink","/approval/approval-step-approver",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step Approver"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step Approver"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Create"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.myFormSubmit()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Employee "),v.Zb(),v.ac(28,"div",19),v.ac(29,"ng-select",20),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),v.Zb(),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Approval Step *"),v.Zb(),v.ac(33,"div",19),v.ac(34,"ng-select",21),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/approvalStep/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Is Active"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",23),v.ac(41,"a",24),v.Vb(42,"i",11),v.Lc(43," Cancel"),v.Zb(),v.Lc(44," \xa0 \xa0 "),v.ac(45,"button",25),v.hc("click",function(){return a.resetFormValues()}),v.Vb(46,"i",26),v.Lc(47," Reset "),v.Zb(),v.Lc(48," \xa0 \xa0 "),v.ac(49,"button",27),v.Vb(50,"i",28),v.Lc(51," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm),v.Ib(5),v.pc("items",a.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),v.Ib(5),v.pc("items",a.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[g.e,p.x,p.p,p.h,G.a,p.o,p.f,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}.big-checkbox[_ngcontent-%COMP%]{width:30px;height:30px}"]}),ae),oe=((ee=function(){function a(t,i,c,o,n,r){e(this,a),this.formBuilder=t,this.route=i,this.router=c,this.approvalService=o,this.spinnerService=n,this.commonService=r,this.baseUrl=h.a.baseUrl,this.myFormData={},this._initConfigDDL()}return t(a,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",p.w.required],approvalStep:["",p.w.required],isActive:["",p.w.required]})}},{key:"getFormData",value:function(){var e=this,a=this.baseUrl+"/approvalStepApprover/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.approvalService.sendGetRequest(a,{}).subscribe(function(a){e.myFormData=a,console.log(e.myFormData),e.spinnerService.hide();var t=[{ddlCode:a.approvalMemberId.id,ddlDescription:a.approvalMemberId.code+"-"+a.approvalMemberId.displayName}];console.log(t),e.configDDL.listData=t,e.myFormData.hrCrEmp=a.approvalMemberId.id;var i=[{id:a.approvalStep.id,approvalGroupName:a.approvalStep.approvalGroupName}];console.log(i),e.configDDL.listData2=i,e.myFormData.approvalStep=a.approvalStep.id,e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e,a=this;this.myForm.invalid||(e=Object.assign(this.myForm.value,{approvalMemberId:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,approvalStep:this.getApprovalStep.value?{id:this.getApprovalStep.value}:null}),this.approvalService.sendPutRequest(this.baseUrl+"/approvalStepApprover/edit",e).subscribe(function(e){console.log(e),a.router.navigate(["/approval/approval-step-approver"],{relativeTo:a.route})},function(e){console.log(e)}))}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,a=this.baseUrl+this.configDDL.dataGetApiPath,t={};t.pageNum=this.configDDL.pageNum,t.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(t[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(a,t).subscribe(function(a){e.configDDL.append?(e.configDDL.listData=e.configDDL.listData.concat(a.objectList),e.configDDL.listData2=e.configDDL.listData2.concat(a.objectList)):(e.configDDL.listData=a.objectList,e.configDDL.listData2=a.objectList),e.configDDL.totalItem=a.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,a,t,i){console.log("..."),console.log("ddlActiveFieldName:"+a),console.log("dataGetApiPathDDL:"+t),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=a&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=a,this.configDDL.dataGetApiPath=t,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getApprovalStep",get:function(){return this.myForm.get("approvalStep")}}]),a}()).\u0275fac=function(e){return new(e||ee)(v.Ub(p.d),v.Ub(g.a),v.Ub(g.c),v.Ub(D.a),v.Ub(d.c),v.Ub(O.a))},ee.\u0275cmp=v.Ob({type:ee,selectors:[["app-approval-step-approver-edit"]],decls:52,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/approval/approval-step-approver",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","approvalStep","placeholder","Select Approval Step","bindLabel","approvalGroupName","bindValue","id","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","checkbox","formControlName","isActive"],[1,"text-right"],["routerLink","/approval/approval-step-approver",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step Approver"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step Approver"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"Edit"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"a",10),v.Vb(18,"i",11),v.Lc(19," Back To List"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(20,"div",12),v.ac(21,"div",13),v.ac(22,"div",14),v.ac(23,"div",15),v.ac(24,"form",16),v.hc("ngSubmit",function(){return a.saveUpdatedFormData()}),v.ac(25,"div",17),v.ac(26,"label",18),v.Lc(27,"Employee "),v.Zb(),v.ac(28,"div",19),v.ac(29,"ng-select",20),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),v.Zb(),v.Zb(),v.Zb(),v.ac(30,"div",17),v.ac(31,"label",18),v.Lc(32,"Approval Step *"),v.Zb(),v.ac(33,"div",19),v.ac(34,"ng-select",21),v.hc("search",function(e){return a.searchDDL(e)})("scrollToEnd",function(){return a.scrollToEndDDL()})("clear",function(){return a.clearDDL()})("click",function(e){return a.initSysParamsDDL(e,"ddlDescription","/approvalStep/getAll","")}),v.Zb(),v.Zb(),v.Zb(),v.ac(35,"div",17),v.ac(36,"label",18),v.Lc(37,"Is Active"),v.Zb(),v.ac(38,"div",19),v.Vb(39,"input",22),v.Zb(),v.Zb(),v.ac(40,"div",23),v.ac(41,"a",24),v.Vb(42,"i",11),v.Lc(43," Cancel"),v.Zb(),v.Lc(44," \xa0 \xa0 "),v.ac(45,"button",25),v.hc("click",function(){return a.resetFormValues()}),v.Vb(46,"i",26),v.Lc(47," Reset "),v.Zb(),v.Lc(48," \xa0 \xa0 "),v.ac(49,"button",27),v.Vb(50,"i",28),v.Lc(51," Save \xa0\xa0\xa0 "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(24),v.pc("formGroup",a.myForm),v.Ib(5),v.pc("items",a.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),v.Ib(5),v.pc("items",a.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[g.e,p.x,p.p,p.h,G.a,p.o,p.f,p.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),ee);function ne(e,a){if(1&e){var t=v.bc();v.ac(0,"tr"),v.ac(1,"td"),v.Lc(2),v.Zb(),v.ac(3,"td",54),v.Lc(4),v.Zb(),v.ac(5,"td"),v.Lc(6),v.Zb(),v.ac(7,"td"),v.Lc(8),v.Zb(),v.ac(9,"td"),v.Lc(10),v.Zb(),v.ac(11,"td"),v.Lc(12," \xa0 "),v.ac(13,"a",55),v.Vb(14,"i",56),v.Zb(),v.Lc(15,"\xa0\xa0 "),v.ac(16,"a",57),v.hc("click",function(){v.Cc(t);var e=a.$implicit;return v.jc().tempId=e.id}),v.Vb(17,"i",58),v.Zb(),v.Zb(),v.Zb()}if(2&e){var i=a.$implicit,c=a.index,o=v.jc();v.Mb("active",c==o.currentIndex),v.Ib(2),v.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),v.Ib(2),v.Mc(i.id),v.Ib(2),v.Mc(i.approvalStep.approvalGroupName),v.Ib(2),v.Mc(i.approvalMemberId.displayName),v.Ib(2),v.Mc(i.isActive),v.Ib(3),v.rc("routerLink","./edit/",i.id,"")}}function re(e,a){1&e&&(v.ac(0,"tr"),v.ac(1,"td",59),v.ac(2,"h5",60),v.Lc(3,"No data found"),v.Zb(),v.Zb(),v.Zb())}function le(e,a){if(1&e&&(v.ac(0,"option",61),v.Lc(1),v.Zb()),2&e){var t=a.$implicit;v.pc("value",t),v.Ib(1),v.Nc(" ",t," ")}}var se,pe,be,ue=[{path:"",component:f,children:[{path:"approval-process",component:A},{path:"approval-process/create",component:F},{path:"approval-process/edit/:id",component:I},{path:"approval-step",component:Q},{path:"approval-step/create",component:E},{path:"approval-step/edit/:id",component:T},{path:"approval-step-approver",component:(se=function(){function a(t,i,c){e(this,a),this.spinnerService=t,this.toastr=i,this.approvalService=c,this.baseUrl=h.a.baseUrl,this.pipe=new n.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return t(a,[{key:"ngOnInit",value:function(){this.getListData()}},{key:"getListData",value:function(){var e,a=this,t=this.baseUrl+"/approvalStepApprover/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.approvalService.sendGetRequest(t,e).subscribe(function(e){a.listData=e.objectList,a.configPgn.totalItem=e.totalItems,a.configPgn.totalItems=e.totalItems,a.setDisplayLastSequence(),a.spinnerService.hide(),console.log(a.listData)},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var a=this,t=this.baseUrl+"/approvalStepApprover/delete/"+e;console.log(t),this.spinnerService.show(),this.approvalService.sendDeleteRequest(t,{}).subscribe(function(e){console.log(e),a.spinnerService.hide(),$("#delete_entity").modal("hide"),a.toastr.success("Successfully item is deleted","Success"),a.getListData()},function(e){console.log(e),a.spinnerService.hide()})}},{key:"getUserQueryParams",value:function(e,a){var t={};return e&&(t.pageNum=e-0),a&&(t.pageSize=a),t}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getListData()}}]),a}(),se.\u0275fac=function(e){return new(e||se)(v.Ub(d.c),v.Ub(L.b),v.Ub(D.a))},se.\u0275cmp=v.Ob({type:se,selectors:[["app-approval-step-approver"]],decls:94,vars:10,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/approval/approval-step-approver/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"d-none"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,a){1&e&&(v.ac(0,"div",0),v.ac(1,"div",1),v.ac(2,"div",2),v.ac(3,"div",3),v.ac(4,"h3",4),v.Lc(5,"Approval Step Approver"),v.Zb(),v.ac(6,"ul",5),v.ac(7,"li",6),v.ac(8,"a",7),v.Lc(9,"Home"),v.Zb(),v.Zb(),v.ac(10,"li",8),v.Lc(11,"Approval"),v.Zb(),v.ac(12,"li",8),v.Lc(13,"Approval Step Approver"),v.Zb(),v.ac(14,"li",8),v.Lc(15,"List"),v.Zb(),v.Zb(),v.Zb(),v.ac(16,"div",9),v.ac(17,"div",10),v.ac(18,"button",11),v.Lc(19,"Excel"),v.Zb(),v.ac(20,"button",11),v.Lc(21,"PDF"),v.Zb(),v.ac(22,"button",11),v.Vb(23,"i",12),v.Lc(24," Print"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(25,"div",13),v.ac(26,"div",14),v.ac(27,"div",15),v.ac(28,"div",16),v.ac(29,"div",17),v.Vb(30,"input",18),v.ac(31,"label",19),v.Lc(32,"Code"),v.Zb(),v.Zb(),v.Zb(),v.ac(33,"div",20),v.ac(34,"a",21),v.Lc(35," Search "),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(36,"div",22),v.ac(37,"div",23),v.ac(38,"div",24),v.ac(39,"div",25),v.ac(40,"div",26),v.ac(41,"a",27),v.Vb(42,"i",28),v.Lc(43," New \xa0\xa0\xa0"),v.Zb(),v.Zb(),v.Zb(),v.ac(44,"div",29),v.ac(45,"div",30),v.ac(46,"div",31),v.ac(47,"div",32),v.ac(48,"span",33),v.Lc(49),v.Zb(),v.Zb(),v.Zb(),v.ac(50,"table",34),v.ac(51,"thead"),v.ac(52,"tr"),v.ac(53,"th"),v.Lc(54,"SL"),v.Zb(),v.ac(55,"th"),v.Lc(56,"Approval Step"),v.Zb(),v.ac(57,"th"),v.Lc(58,"Approval Member"),v.Zb(),v.ac(59,"th"),v.Lc(60,"Is Active"),v.Zb(),v.ac(61,"th"),v.Lc(62,"Action"),v.Zb(),v.Zb(),v.Zb(),v.ac(63,"tbody"),v.Jc(64,ne,18,8,"tr",35),v.kc(65,"paginate"),v.Jc(66,re,4,0,"tr",36),v.Zb(),v.Zb(),v.ac(67,"div",37),v.ac(68,"div",38),v.Lc(69," Items per Page "),v.ac(70,"select",39),v.hc("change",function(e){return a.handlePageSizeChange(e)}),v.Jc(71,le,2,2,"option",40),v.Zb(),v.Zb(),v.ac(72,"div",41),v.ac(73,"pagination-controls",42),v.hc("pageChange",function(e){return a.handlePageChange(e)}),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.ac(74,"ngx-spinner",43),v.ac(75,"p",44),v.Lc(76," Processing... "),v.Zb(),v.Zb(),v.ac(77,"div",45),v.ac(78,"div",46),v.ac(79,"div",47),v.ac(80,"div",48),v.ac(81,"div",49),v.ac(82,"h3"),v.Lc(83,"Delete Item"),v.Zb(),v.ac(84,"p"),v.Lc(85,"Are you sure want to delete?"),v.Zb(),v.Zb(),v.ac(86,"div",50),v.ac(87,"div",22),v.ac(88,"div",51),v.ac(89,"a",52),v.hc("click",function(){return a.deleteEnityData(a.tempId)}),v.Lc(90,"Delete"),v.Zb(),v.Zb(),v.ac(91,"div",51),v.ac(92,"a",53),v.Lc(93,"Cancel"),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb(),v.Zb()),2&e&&(v.Ib(49),v.Pc("Displaying ( ",(a.configPgn.pageNum-1)*a.configPgn.pageSize+1," to ",a.configPgn.pngDiplayLastSeq," of ",a.configPgn.totalItem," ) entries"),v.Ib(15),v.pc("ngForOf",v.mc(65,7,a.listData,a.configPgn)),v.Ib(2),v.pc("ngIf",0===a.listData.length),v.Ib(5),v.pc("ngForOf",a.configPgn.pageSizes),v.Ib(3),v.pc("fullScreen",!1))},directives:[g.e,n.l,n.m,u.c,d.a,p.s,p.y],pipes:[u.b],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),se)},{path:"approval-step-approver/create",component:ce},{path:"approval-step-approver/edit/:id",component:oe},{path:"approval-step-action",component:ie},{path:"approval-step-action/create",component:J},{path:"approval-step-action/edit/:id",component:K},{path:"approval-process-tnx-history",component:V}]}],de=((be=function a(){e(this,a)}).\u0275fac=function(e){return new(e||be)},be.\u0275mod=v.Sb({type:be}),be.\u0275inj=v.Rb({imports:[[g.f.forChild(ue)],g.f]}),be),ge=((pe=function a(){e(this,a)}).\u0275fac=function(e){return new(e||pe)},pe.\u0275mod=v.Sb({type:pe}),pe.\u0275inj=v.Rb({imports:[[n.c,de,r.b,l.c.forRoot(),s.a,p.u,b.b,u.a,d.b,G.b]]}),pe)}}])}();