!function(){function e(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,a){if(!e)return;if("string"==typeof e)return t(e,a);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return t(e,a)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=new Array(t);a<t;a++)i[a]=e[a];return i}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function c(e,t,a){return t&&i(e.prototype,t),a&&i(e,a),e}(window.webpackJsonp=window.webpackJsonp||[]).push([[28],{Zqwr:function(t,i,d){"use strict";d.r(i),d.d(i,"UsersModule",function(){return O});var n,r=d("ofXK"),o=d("tyNb"),s=d("fXoL"),b=((n=function(){function e(t){var i=this;a(this,e),this.ngZone=t,window.onresize=function(e){i.ngZone.run(function(){i.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}return c(e,[{key:"getScreenHeight",value:function(){this.innerHeight=window.innerHeight+"px"}},{key:"ngOnInit",value:function(){}},{key:"onResize",value:function(e){this.innerHeight=e.target.innerHeight+"px"}}]),e}()).\u0275fac=function(e){return new(e||n)(s.Ub(s.G))},n.\u0275cmp=s.Ob({type:n,selectors:[["app-users"]],decls:1,vars:0,template:function(e,t){1&e&&s.Vb(0,"router-outlet")},directives:[o.g],styles:[""]}),n),l=d("IhMt"),u=d("3Pt+"),Z=d("XNiG"),p=d("njyG"),m=d("5eHb");function v(e,t){if(1&e){var a=s.bc();s.ac(0,"tr"),s.ac(1,"td"),s.ac(2,"h2",77),s.ac(3,"a",78),s.Vb(4,"img",79),s.Zb(),s.ac(5,"a",80),s.Lc(6),s.ac(7,"span"),s.Lc(8),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(9,"td"),s.Lc(10),s.Zb(),s.ac(11,"td"),s.Lc(12),s.Zb(),s.ac(13,"td"),s.Lc(14,"1 Jan 2013"),s.Zb(),s.ac(15,"td"),s.ac(16,"span",81),s.Lc(17),s.Zb(),s.Zb(),s.ac(18,"td",32),s.ac(19,"div",82),s.ac(20,"a",83),s.ac(21,"i",84),s.Lc(22,"more_vert"),s.Zb(),s.Zb(),s.ac(23,"div",85),s.ac(24,"a",86),s.hc("click",function(){s.Cc(a);var e=t.$implicit;return s.jc().edit(e.id)}),s.Vb(25,"i",87),s.Lc(26," Edit"),s.Zb(),s.ac(27,"a",88),s.hc("click",function(){s.Cc(a);var e=t.$implicit;return s.jc().tempId=e.id}),s.Vb(28,"i",89),s.Lc(29," Delete"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()}if(2&e){var i=t.$implicit,c=t.index;s.Ib(4),s.rc("src","assets/img/profiles/avatar-",c+1,".jpg",s.Fc),s.Ib(2),s.Nc("",i.name," "),s.Ib(2),s.Mc(i.designation),s.Ib(2),s.Mc(i.email),s.Ib(2),s.Mc(i.company),s.Ib(5),s.Mc(i.role)}}function f(e,t){1&e&&(s.ac(0,"tr"),s.ac(1,"td",90),s.ac(2,"h5",91),s.Lc(3,"No data found"),s.Zb(),s.Zb(),s.Zb())}function h(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *User name is required"),s.Zb())}function g(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,h,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.addUsers.get("addUserName").invalid&&a.addUsers.get("addUserName").touched)}}function L(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *Email is required"),s.Zb())}function U(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,L,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.addUsers.get("addEmail").invalid&&a.addUsers.get("addEmail").touched)}}function y(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *Role is required"),s.Zb())}function I(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,y,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.addUsers.get("addRole").invalid&&a.addUsers.get("addRole").touched)}}function w(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *Company is required"),s.Zb())}function V(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,w,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.addUsers.get("addCompany").invalid&&a.addUsers.get("addCompany").touched)}}function C(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *User name is required"),s.Zb())}function E(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,C,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.editUsers.get("editUsersName").invalid&&a.editUsers.get("editUsersName").touched)}}function N(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *Email is required"),s.Zb())}function R(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,N,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.editUsers.get("editEmail").invalid&&a.editUsers.get("editEmail").touched)}}function x(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *Role is required"),s.Zb())}function S(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,x,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.editUsers.get("editRole").invalid&&a.editUsers.get("editRole").touched)}}function k(e,t){1&e&&(s.ac(0,"small",46),s.Lc(1," *Company is required"),s.Zb())}function D(e,t){if(1&e&&(s.ac(0,"div"),s.Jc(1,k,2,0,"small",92),s.Zb()),2&e){var a=s.jc();s.Ib(1),s.pc("ngIf",a.editUsers.get("editCompany").invalid&&a.editUsers.get("editCompany").touched)}}var M,T,J,j=[{path:"",component:b,children:[{path:"user-main",component:(M=function(){function t(e,i,c){a(this,t),this.allModuleService=e,this.formBuilder=i,this.toastr=c,this.dtOptions={},this.url="users",this.allUsers=[],this.rows=[],this.srch=[],this.dtTrigger=new Z.a}return c(t,[{key:"ngOnInit",value:function(){$(".floating").on("focus blur",function(e){$(this).parents(".form-focus").toggleClass("focused","focus"===e.type||this.value.length>0)}).trigger("blur"),this.getUsers(),this.addUsers=this.formBuilder.group({addUserName:["",[u.w.required]],addEmail:["",[u.w.required]],addRole:["",[u.w.required]],addCompany:["",[u.w.required]]}),this.editUsers=this.formBuilder.group({editUsersName:["",[u.w.required]],editEmail:["",[u.w.required]],editRole:["",[u.w.required]],editCompany:["",[u.w.required]]}),this.dtOptions={pageLength:10,dom:"lrtip"}}},{key:"ngAfterViewInit",value:function(){var e=this;setTimeout(function(){e.dtTrigger.next()},1e3)}},{key:"rerender",value:function(){var e=this;$("#datatable").DataTable().clear(),this.dtElement.dtInstance.then(function(e){e.destroy()}),this.allUsers=[],this.getUsers(),setTimeout(function(){e.dtTrigger.next()},1e3)}},{key:"getUsers",value:function(){var t=this;this.allModuleService.get(this.url).subscribe(function(a){t.allUsers=a,t.rows=t.allUsers,t.srch=e(t.rows)})}},{key:"addUsersSubmit",value:function(){var e=this;this.addUsers.valid?(this.allModuleService.add({name:this.addUsers.value.addUserName,designation:"Web Designer",email:this.addUsers.value.addEmail,role:this.addUsers.value.addRole,company:this.addUsers.value.addCompany},this.url).subscribe(function(t){$("#datatable").DataTable().clear(),e.dtElement.dtInstance.then(function(e){e.destroy()}),e.dtTrigger.next()}),this.getUsers(),$("#add_user").modal("hide"),this.addUsers.reset(),this.toastr.success("Users is added","Success")):this.toastr.warning("Mandatory fields required","")}},{key:"editUsersSubmit",value:function(){var e=this;this.editUsers.valid?(this.allModuleService.update({name:this.editUsers.value.editUsersName,designation:"Android Developer",email:this.editUsers.value.editEmail,company:this.editUsers.value.editCompany,role:this.editUsers.value.editRole,id:this.editId},this.url).subscribe(function(t){$("#datatable").DataTable().clear(),e.dtElement.dtInstance.then(function(e){e.destroy()}),e.dtTrigger.next()}),this.getUsers(),$("#edit_user").modal("hide"),this.toastr.success("Users is edited","Success")):this.toastr.warning("Mandatory fields required","")}},{key:"edit",value:function(e){this.editId=e;var t=this.allUsers.findIndex(function(t){return t.id===e}),a=this.allUsers[t];this.editUsers.setValue({editUsersName:a.name,editEmail:a.email,editRole:a.role,editCompany:a.company})}},{key:"deleteUsers",value:function(){var e=this;this.allModuleService.delete(this.tempId,this.url).subscribe(function(t){$("#datatable").DataTable().clear(),e.dtElement.dtInstance.then(function(e){e.destroy()}),e.dtTrigger.next()}),this.getUsers(),$("#delete_user").modal("hide"),this.toastr.success("Users is deleted","Success")}},{key:"searchName",value:function(t){var a;this.rows.splice(0,this.rows.length);var i=this.srch.filter(function(e){return t=t.toLowerCase(),-1!==e.name.toLowerCase().indexOf(t)||!t});(a=this.rows).push.apply(a,e(i))}},{key:"searchStatus",value:function(t){var a;this.rows.splice(0,this.rows.length);var i=this.srch.filter(function(e){return t=t.toLowerCase(),-1!==e.company.toLowerCase().indexOf(t)||!t});(a=this.rows).push.apply(a,e(i))}},{key:"searchRole",value:function(t){var a;this.rows.splice(0,this.rows.length);var i=this.srch.filter(function(e){return t=t.toLowerCase(),-1!==e.role.toLowerCase().indexOf(t)||!t});(a=this.rows).push.apply(a,e(i))}},{key:"ngOnDestroy",value:function(){this.dtTrigger.unsubscribe()}}]),t}(),M.\u0275fac=function(e){return new(e||M)(s.Ub(l.a),s.Ub(u.d),s.Ub(m.b))},M.\u0275cmp=s.Ob({type:M,selectors:[["app-user-main"]],viewQuery:function(e,t){var a;1&e&&s.Rc(p.a,1),2&e&&s.yc(a=s.ic())&&(t.dtElement=a.first)},decls:422,vars:30,consts:[[1,"page-wrapper"],[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["data-toggle","modal","data-target","#add_user",1,"btn","add-btn"],[1,"fa","fa-plus"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating",3,"input","focusout"],[1,"focus-label"],[1,"form-group","form-focus","select-focus"],[1,"select","form-control",3,"input"],["value",""],["value","Global Technologies"],["value","Delta Infotech"],["value","Web Developer"],["value","Web Designer"],["value","Android Developer"],["value","Ios Developer"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"table-responsive"],["datatable","","id","datatable",1,"table","table-striped","custom-table","datatable",3,"dtOptions","dtTrigger"],[1,"text-right"],[4,"ngFor","ngForOf"],[4,"ngIf"],["id","add_user","role","dialog",1,"modal","custom-modal","fade"],["role","document",1,"modal-dialog","modal-dialog-centered","modal-lg"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["type","button","data-dismiss","modal","aria-label","Close",1,"close"],["aria-hidden","true"],[1,"modal-body"],[3,"formGroup","ngSubmit"],[1,"col-sm-6"],[1,"form-group"],[1,"text-danger"],["type","text",1,"form-control"],["type","text","formControlName","addUserName",1,"form-control"],["type","email","formControlName","addEmail",1,"form-control"],["type","password",1,"form-control"],["formControlName","addRole",1,"select","form-control"],["formControlName","addCompany",1,"select","form-control"],["type","text",1,"form-control","floating"],[1,"table-responsive","m-t-15"],[1,"table","table-striped","custom-table"],[1,"text-center"],["checked","","type","checkbox"],[1,"submit-section"],[1,"btn","btn-primary","submit-btn"],["id","edit_user","role","dialog",1,"modal","custom-modal","fade"],["value","John","type","text",1,"form-control"],["value","Doe","type","text",1,"form-control"],["type","text","formControlName","editUsersName",1,"form-control"],["type","email","formControlName","editEmail",1,"form-control"],["value","9876543210","type","text",1,"form-control"],["formControlName","editRole",1,"select","form-control"],["selected",""],["formControlName","editCompany",1,"select","form-control"],["type","text","value","FT-0001",1,"form-control","floating"],["id","delete_user","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"table-avatar"],["routerLink","/employees/employeeprofile",1,"avatar"],["alt","",3,"src"],["routerLink","/employees/employeeprofile"],[1,"badge","bg-inverse-danger"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],["data-toggle","modal","data-target","#edit_user",1,"dropdown-item",3,"click"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_user",1,"dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],["class","text-danger",4,"ngIf"]],template:function(e,t){1&e&&(s.ac(0,"div",0),s.ac(1,"div",1),s.ac(2,"div",2),s.ac(3,"div",3),s.ac(4,"div",4),s.ac(5,"h3",5),s.Lc(6,"Users"),s.Zb(),s.ac(7,"ul",6),s.ac(8,"li",7),s.ac(9,"a",8),s.Lc(10,"Dashboard"),s.Zb(),s.Zb(),s.ac(11,"li",9),s.Lc(12,"Users"),s.Zb(),s.Zb(),s.Zb(),s.ac(13,"div",10),s.ac(14,"a",11),s.Vb(15,"i",12),s.Lc(16," Add User"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(17,"div",13),s.ac(18,"div",14),s.ac(19,"div",15),s.ac(20,"input",16),s.hc("input",function(e){return t.searchName(e.target.value)})("focusout",function(){return t.rerender()}),s.Zb(),s.ac(21,"label",17),s.Lc(22,"Name"),s.Zb(),s.Zb(),s.Zb(),s.ac(23,"div",14),s.ac(24,"div",18),s.ac(25,"select",19),s.hc("input",function(e){return t.searchStatus(e.target.value)}),s.ac(26,"option",20),s.Lc(27,"Select Company"),s.Zb(),s.ac(28,"option",21),s.Lc(29,"Global Technologies"),s.Zb(),s.ac(30,"option",22),s.Lc(31,"Delta Infotech"),s.Zb(),s.Zb(),s.ac(32,"label",17),s.Lc(33,"Company"),s.Zb(),s.Zb(),s.Zb(),s.ac(34,"div",14),s.ac(35,"div",18),s.ac(36,"select",19),s.hc("input",function(e){return t.searchRole(e.target.value)}),s.ac(37,"option",20),s.Lc(38,"Select Roll"),s.Zb(),s.ac(39,"option",23),s.Lc(40,"Web Developer"),s.Zb(),s.ac(41,"option",24),s.Lc(42,"Web Designer"),s.Zb(),s.ac(43,"option",25),s.Lc(44,"Android Developer"),s.Zb(),s.ac(45,"option",26),s.Lc(46,"Ios Developer"),s.Zb(),s.Zb(),s.ac(47,"label",17),s.Lc(48,"Role"),s.Zb(),s.Zb(),s.Zb(),s.ac(49,"div",14),s.ac(50,"a",27),s.Lc(51," Search "),s.Zb(),s.Zb(),s.Zb(),s.ac(52,"div",28),s.ac(53,"div",29),s.ac(54,"div",30),s.ac(55,"table",31),s.ac(56,"thead"),s.ac(57,"tr"),s.ac(58,"th"),s.Lc(59,"Name"),s.Zb(),s.ac(60,"th"),s.Lc(61,"Email"),s.Zb(),s.ac(62,"th"),s.Lc(63,"Company"),s.Zb(),s.ac(64,"th"),s.Lc(65,"Created Date"),s.Zb(),s.ac(66,"th"),s.Lc(67,"Role"),s.Zb(),s.ac(68,"th",32),s.Lc(69,"Action"),s.Zb(),s.Zb(),s.Zb(),s.ac(70,"tbody"),s.Jc(71,v,30,6,"tr",33),s.Jc(72,f,4,0,"tr",34),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(73,"div",35),s.ac(74,"div",36),s.ac(75,"div",37),s.ac(76,"div",38),s.ac(77,"h5",39),s.Lc(78,"Add User"),s.Zb(),s.ac(79,"button",40),s.ac(80,"span",41),s.Lc(81,"\xd7"),s.Zb(),s.Zb(),s.Zb(),s.ac(82,"div",42),s.ac(83,"form",43),s.hc("ngSubmit",function(){return t.addUsersSubmit()}),s.ac(84,"div",28),s.ac(85,"div",44),s.ac(86,"div",45),s.ac(87,"label"),s.Lc(88,"First Name "),s.ac(89,"span",46),s.Lc(90,"*"),s.Zb(),s.Zb(),s.Vb(91,"input",47),s.Zb(),s.Zb(),s.ac(92,"div",44),s.ac(93,"div",45),s.ac(94,"label"),s.Lc(95,"Last Name"),s.Zb(),s.Vb(96,"input",47),s.Zb(),s.Zb(),s.ac(97,"div",44),s.ac(98,"div",45),s.ac(99,"label"),s.Lc(100,"Username "),s.ac(101,"span",46),s.Lc(102,"*"),s.Zb(),s.Zb(),s.Vb(103,"input",48),s.Jc(104,g,2,1,"div",34),s.Zb(),s.Zb(),s.ac(105,"div",44),s.ac(106,"div",45),s.ac(107,"label"),s.Lc(108,"Email "),s.ac(109,"span",46),s.Lc(110,"*"),s.Zb(),s.Zb(),s.Vb(111,"input",49),s.Jc(112,U,2,1,"div",34),s.Zb(),s.Zb(),s.ac(113,"div",44),s.ac(114,"div",45),s.ac(115,"label"),s.Lc(116,"Password"),s.Zb(),s.Vb(117,"input",50),s.Zb(),s.Zb(),s.ac(118,"div",44),s.ac(119,"div",45),s.ac(120,"label"),s.Lc(121,"Confirm Password"),s.Zb(),s.Vb(122,"input",50),s.Zb(),s.Zb(),s.ac(123,"div",44),s.ac(124,"div",45),s.ac(125,"label"),s.Lc(126,"Phone "),s.Zb(),s.Vb(127,"input",47),s.Zb(),s.Zb(),s.ac(128,"div",44),s.ac(129,"div",45),s.ac(130,"label"),s.Lc(131,"Role"),s.Zb(),s.ac(132,"select",51),s.ac(133,"option"),s.Lc(134,"Admin"),s.Zb(),s.ac(135,"option"),s.Lc(136,"Client"),s.Zb(),s.ac(137,"option"),s.Lc(138,"Employee"),s.Zb(),s.Zb(),s.Jc(139,I,2,1,"div",34),s.Zb(),s.Zb(),s.ac(140,"div",44),s.ac(141,"div",45),s.ac(142,"label"),s.Lc(143,"Company"),s.Zb(),s.ac(144,"select",52),s.ac(145,"option"),s.Lc(146,"Global Technologies"),s.Zb(),s.ac(147,"option"),s.Lc(148,"Delta Infotech"),s.Zb(),s.Zb(),s.Jc(149,V,2,1,"div",34),s.Zb(),s.Zb(),s.ac(150,"div",44),s.ac(151,"div",45),s.ac(152,"label"),s.Lc(153,"Employee ID "),s.ac(154,"span",46),s.Lc(155,"*"),s.Zb(),s.Zb(),s.Vb(156,"input",53),s.Zb(),s.Zb(),s.Zb(),s.ac(157,"div",54),s.ac(158,"table",55),s.ac(159,"thead"),s.ac(160,"tr"),s.ac(161,"th"),s.Lc(162,"Module Permission"),s.Zb(),s.ac(163,"th",56),s.Lc(164,"Read"),s.Zb(),s.ac(165,"th",56),s.Lc(166,"Write"),s.Zb(),s.ac(167,"th",56),s.Lc(168,"Create"),s.Zb(),s.ac(169,"th",56),s.Lc(170,"Delete"),s.Zb(),s.ac(171,"th",56),s.Lc(172,"Import"),s.Zb(),s.ac(173,"th",56),s.Lc(174,"Export"),s.Zb(),s.Zb(),s.Zb(),s.ac(175,"tbody"),s.ac(176,"tr"),s.ac(177,"td"),s.Lc(178,"Employee"),s.Zb(),s.ac(179,"td",56),s.Vb(180,"input",57),s.Zb(),s.ac(181,"td",56),s.Vb(182,"input",57),s.Zb(),s.ac(183,"td",56),s.Vb(184,"input",57),s.Zb(),s.ac(185,"td",56),s.Vb(186,"input",57),s.Zb(),s.ac(187,"td",56),s.Vb(188,"input",57),s.Zb(),s.ac(189,"td",56),s.Vb(190,"input",57),s.Zb(),s.Zb(),s.ac(191,"tr"),s.ac(192,"td"),s.Lc(193,"Holidays"),s.Zb(),s.ac(194,"td",56),s.Vb(195,"input",57),s.Zb(),s.ac(196,"td",56),s.Vb(197,"input",57),s.Zb(),s.ac(198,"td",56),s.Vb(199,"input",57),s.Zb(),s.ac(200,"td",56),s.Vb(201,"input",57),s.Zb(),s.ac(202,"td",56),s.Vb(203,"input",57),s.Zb(),s.ac(204,"td",56),s.Vb(205,"input",57),s.Zb(),s.Zb(),s.ac(206,"tr"),s.ac(207,"td"),s.Lc(208,"Leaves"),s.Zb(),s.ac(209,"td",56),s.Vb(210,"input",57),s.Zb(),s.ac(211,"td",56),s.Vb(212,"input",57),s.Zb(),s.ac(213,"td",56),s.Vb(214,"input",57),s.Zb(),s.ac(215,"td",56),s.Vb(216,"input",57),s.Zb(),s.ac(217,"td",56),s.Vb(218,"input",57),s.Zb(),s.ac(219,"td",56),s.Vb(220,"input",57),s.Zb(),s.Zb(),s.ac(221,"tr"),s.ac(222,"td"),s.Lc(223,"Events"),s.Zb(),s.ac(224,"td",56),s.Vb(225,"input",57),s.Zb(),s.ac(226,"td",56),s.Vb(227,"input",57),s.Zb(),s.ac(228,"td",56),s.Vb(229,"input",57),s.Zb(),s.ac(230,"td",56),s.Vb(231,"input",57),s.Zb(),s.ac(232,"td",56),s.Vb(233,"input",57),s.Zb(),s.ac(234,"td",56),s.Vb(235,"input",57),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(236,"div",58),s.ac(237,"button",59),s.Lc(238,"Submit"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(239,"div",60),s.ac(240,"div",36),s.ac(241,"div",37),s.ac(242,"div",38),s.ac(243,"h5",39),s.Lc(244,"Edit User"),s.Zb(),s.ac(245,"button",40),s.ac(246,"span",41),s.Lc(247,"\xd7"),s.Zb(),s.Zb(),s.Zb(),s.ac(248,"div",42),s.ac(249,"form",43),s.hc("ngSubmit",function(){return t.editUsersSubmit()}),s.ac(250,"div",28),s.ac(251,"div",44),s.ac(252,"div",45),s.ac(253,"label"),s.Lc(254,"First Name "),s.ac(255,"span",46),s.Lc(256,"*"),s.Zb(),s.Zb(),s.Vb(257,"input",61),s.Zb(),s.Zb(),s.ac(258,"div",44),s.ac(259,"div",45),s.ac(260,"label"),s.Lc(261,"Last Name"),s.Zb(),s.Vb(262,"input",62),s.Zb(),s.Zb(),s.ac(263,"div",44),s.ac(264,"div",45),s.ac(265,"label"),s.Lc(266,"Username "),s.ac(267,"span",46),s.Lc(268,"*"),s.Zb(),s.Zb(),s.Vb(269,"input",63),s.Jc(270,E,2,1,"div",34),s.Zb(),s.Zb(),s.ac(271,"div",44),s.ac(272,"div",45),s.ac(273,"label"),s.Lc(274,"Email "),s.ac(275,"span",46),s.Lc(276,"*"),s.Zb(),s.Zb(),s.Vb(277,"input",64),s.Jc(278,R,2,1,"div",34),s.Zb(),s.Zb(),s.ac(279,"div",44),s.ac(280,"div",45),s.ac(281,"label"),s.Lc(282,"Password"),s.Zb(),s.Vb(283,"input",50),s.Zb(),s.Zb(),s.ac(284,"div",44),s.ac(285,"div",45),s.ac(286,"label"),s.Lc(287,"Confirm Password"),s.Zb(),s.Vb(288,"input",50),s.Zb(),s.Zb(),s.ac(289,"div",44),s.ac(290,"div",45),s.ac(291,"label"),s.Lc(292,"Phone "),s.Zb(),s.Vb(293,"input",65),s.Zb(),s.Zb(),s.ac(294,"div",44),s.ac(295,"div",45),s.ac(296,"label"),s.Lc(297,"Role"),s.Zb(),s.ac(298,"select",66),s.ac(299,"option"),s.Lc(300,"Admin"),s.Zb(),s.ac(301,"option"),s.Lc(302,"Client"),s.Zb(),s.ac(303,"option",67),s.Lc(304,"Employee"),s.Zb(),s.Zb(),s.Jc(305,S,2,1,"div",34),s.Zb(),s.Zb(),s.ac(306,"div",44),s.ac(307,"div",45),s.ac(308,"label"),s.Lc(309,"Company"),s.Zb(),s.ac(310,"select",68),s.ac(311,"option"),s.Lc(312,"Global Technologies"),s.Zb(),s.ac(313,"option"),s.Lc(314,"Delta Infotech"),s.Zb(),s.Zb(),s.Jc(315,D,2,1,"div",34),s.Zb(),s.Zb(),s.ac(316,"div",44),s.ac(317,"div",45),s.ac(318,"label"),s.Lc(319,"Employee ID "),s.ac(320,"span",46),s.Lc(321,"*"),s.Zb(),s.Zb(),s.Vb(322,"input",69),s.Zb(),s.Zb(),s.Zb(),s.ac(323,"div",54),s.ac(324,"table",55),s.ac(325,"thead"),s.ac(326,"tr"),s.ac(327,"th"),s.Lc(328,"Module Permission"),s.Zb(),s.ac(329,"th",56),s.Lc(330,"Read"),s.Zb(),s.ac(331,"th",56),s.Lc(332,"Write"),s.Zb(),s.ac(333,"th",56),s.Lc(334,"Create"),s.Zb(),s.ac(335,"th",56),s.Lc(336,"Delete"),s.Zb(),s.ac(337,"th",56),s.Lc(338,"Import"),s.Zb(),s.ac(339,"th",56),s.Lc(340,"Export"),s.Zb(),s.Zb(),s.Zb(),s.ac(341,"tbody"),s.ac(342,"tr"),s.ac(343,"td"),s.Lc(344,"Employee"),s.Zb(),s.ac(345,"td",56),s.Vb(346,"input",57),s.Zb(),s.ac(347,"td",56),s.Vb(348,"input",57),s.Zb(),s.ac(349,"td",56),s.Vb(350,"input",57),s.Zb(),s.ac(351,"td",56),s.Vb(352,"input",57),s.Zb(),s.ac(353,"td",56),s.Vb(354,"input",57),s.Zb(),s.ac(355,"td",56),s.Vb(356,"input",57),s.Zb(),s.Zb(),s.ac(357,"tr"),s.ac(358,"td"),s.Lc(359,"Holidays"),s.Zb(),s.ac(360,"td",56),s.Vb(361,"input",57),s.Zb(),s.ac(362,"td",56),s.Vb(363,"input",57),s.Zb(),s.ac(364,"td",56),s.Vb(365,"input",57),s.Zb(),s.ac(366,"td",56),s.Vb(367,"input",57),s.Zb(),s.ac(368,"td",56),s.Vb(369,"input",57),s.Zb(),s.ac(370,"td",56),s.Vb(371,"input",57),s.Zb(),s.Zb(),s.ac(372,"tr"),s.ac(373,"td"),s.Lc(374,"Leaves"),s.Zb(),s.ac(375,"td",56),s.Vb(376,"input",57),s.Zb(),s.ac(377,"td",56),s.Vb(378,"input",57),s.Zb(),s.ac(379,"td",56),s.Vb(380,"input",57),s.Zb(),s.ac(381,"td",56),s.Vb(382,"input",57),s.Zb(),s.ac(383,"td",56),s.Vb(384,"input",57),s.Zb(),s.ac(385,"td",56),s.Vb(386,"input",57),s.Zb(),s.Zb(),s.ac(387,"tr"),s.ac(388,"td"),s.Lc(389,"Events"),s.Zb(),s.ac(390,"td",56),s.Vb(391,"input",57),s.Zb(),s.ac(392,"td",56),s.Vb(393,"input",57),s.Zb(),s.ac(394,"td",56),s.Vb(395,"input",57),s.Zb(),s.ac(396,"td",56),s.Vb(397,"input",57),s.Zb(),s.ac(398,"td",56),s.Vb(399,"input",57),s.Zb(),s.ac(400,"td",56),s.Vb(401,"input",57),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(402,"div",58),s.ac(403,"button",59),s.Lc(404,"Save"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.ac(405,"div",70),s.ac(406,"div",71),s.ac(407,"div",37),s.ac(408,"div",42),s.ac(409,"div",72),s.ac(410,"h3"),s.Lc(411,"Delete User"),s.Zb(),s.ac(412,"p"),s.Lc(413,"Are you sure want to delete?"),s.Zb(),s.Zb(),s.ac(414,"div",73),s.ac(415,"div",28),s.ac(416,"div",74),s.ac(417,"a",75),s.hc("click",function(){return t.deleteUsers()}),s.Lc(418,"Delete"),s.Zb(),s.Zb(),s.ac(419,"div",74),s.ac(420,"a",76),s.Lc(421,"Cancel"),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb(),s.Zb()),2&e&&(s.Ib(55),s.pc("dtOptions",t.dtOptions)("dtTrigger",t.dtTrigger),s.Ib(16),s.pc("ngForOf",t.allUsers),s.Ib(1),s.pc("ngIf",0===t.allUsers.length),s.Ib(11),s.pc("formGroup",t.addUsers),s.Ib(20),s.Mb("invalid",t.addUsers.get("addUserName").invalid&&t.addUsers.get("addUserName").touched),s.Ib(1),s.pc("ngIf",t.addUsers.get("addUserName").invalid&&t.addUsers.get("addUserName").touched),s.Ib(7),s.Mb("invalid",t.addUsers.get("addEmail").invalid&&t.addUsers.get("addEmail").touched),s.Ib(1),s.pc("ngIf",t.addUsers.get("addEmail").invalid&&t.addUsers.get("addEmail").touched),s.Ib(20),s.Mb("invalid",t.addUsers.get("addRole").invalid&&t.addUsers.get("addRole").touched),s.Ib(7),s.pc("ngIf",t.addUsers.get("addRole").invalid&&t.addUsers.get("addRole").touched),s.Ib(5),s.Mb("invalid",t.addUsers.get("addCompany").invalid&&t.addUsers.get("addCompany").touched),s.Ib(5),s.pc("ngIf",t.addUsers.get("addCompany").invalid&&t.addUsers.get("addCompany").touched),s.Ib(100),s.pc("formGroup",t.editUsers),s.Ib(20),s.Mb("invalid",t.editUsers.get("editUsersName").invalid&&t.editUsers.get("editUsersName").touched),s.Ib(1),s.pc("ngIf",t.editUsers.get("editUsersName").invalid&&t.editUsers.get("editUsersName").touched),s.Ib(7),s.Mb("invalid",t.editUsers.get("editEmail").invalid&&t.editUsers.get("editEmail").touched),s.Ib(1),s.pc("ngIf",t.editUsers.get("editEmail").invalid&&t.editUsers.get("editEmail").touched),s.Ib(20),s.Mb("invalid",t.editUsers.get("editRole").invalid&&t.editUsers.get("editRole").touched),s.Ib(7),s.pc("ngIf",t.editUsers.get("editRole").invalid&&t.editUsers.get("editRole").touched),s.Ib(5),s.Mb("invalid",t.editUsers.get("editCompany").invalid&&t.editUsers.get("editCompany").touched),s.Ib(5),s.pc("ngIf",t.editUsers.get("editCompany").invalid&&t.editUsers.get("editCompany").touched))},directives:[o.e,u.s,u.y,p.a,r.l,r.m,u.x,u.p,u.h,u.b,u.o,u.f,u.v],styles:[""]}),M)}]}],q=((J=function e(){a(this,e)}).\u0275fac=function(e){return new(e||J)},J.\u0275mod=s.Sb({type:J}),J.\u0275inj=s.Rb({imports:[[o.f.forChild(j)],o.f]}),J),O=((T=function e(){a(this,e)}).\u0275fac=function(e){return new(e||T)},T.\u0275mod=s.Sb({type:T}),T.\u0275inj=s.Rb({imports:[[r.c,q,p.b,u.j,u.u]]}),T)}}])}();