!function(){function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e,t){for(var a=0;a<t.length;a++){var i=t[a];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,a,i){return a&&t(e.prototype,a),i&&t(e,i),e}(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{"7K3g":function(t,i,c){"use strict";c.r(i),c.d(i,"SelfServiceModule",function(){return De});var o,n,r,l=c("ofXK"),s=c("tyNb"),b=c("xrk7"),d=c("AytR"),p=c("un/a"),u=c("fXoL"),m=c("tk/3"),g=((o=function(){function t(a){e(this,t),this.http=a}return a(t,[{key:"sendGetSelfRequest",value:function(e,t){return console.log("@sendGetSelfRequest"),this.http.get(e,{params:t}).pipe(Object(p.a)(3))}},{key:"sendPostRequest",value:function(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}},{key:"sendGetRequest",value:function(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(p.a)(3))}},{key:"sendDeleteRequest",value:function(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}}]),t}()).\u0275fac=function(e){return new(e||o)(u.ec(m.c))},o.\u0275prov=u.Qb({token:o,factory:o.\u0275fac,providedIn:"root"}),o),f=c("3Pt+"),h=c("5eHb"),v=c("ZOsW"),D=c("oW1M"),L=((n=function(){function t(a,i,c,o,n,r,l){e(this,t),this.formBuilder=a,this.datePipe=i,this.route=c,this.router=o,this.leaveService=n,this.toastr=r,this.commonService=l,this.baseUrl=d.a.baseUrl,this.leaveList=[],this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.loadAlkpLeave()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({hrCrEmp:{},contactNo:[""],hrCrEmpResponsible:{},alkpLeaveType:[""],startDate:[""],endDate:[""],addressDuringLeave:[""],reasonForLeave:[""],remarks:[""]})}},{key:"loadAlkpLeave",value:function(){var e=this;this.commonService.getAlkpByKeyword("LEAVETYPE").subscribe(function(t){e.alkpLeave=t,e.leaveList=e.alkpLeave.subALKP,console.log(e.leaveList)})}},{key:"myFormSubmit",value:function(){var e=this;if(!this.checkSomeCondition()){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null,alkpLeaveType:this.getAlkpLeaveId.value?{id:this.getAlkpLeaveId.value}:null}),a=this.baseUrl+"/leaveTrnse/save",i={};(i=t).startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.leaveService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.router.navigate(["/sefl-service/employeeleaves"],{relativeTo:e.route})},function(t){console.log(t),e.toastr.error(t.error.message)})}}},{key:"checkSomeCondition",value:function(){if(57==this.myForm.value.alkpLeaveType){var e=new Date;return(this.myForm.value.startDate>e||this.myForm.value.endDate>e)&&(this.toastr.info("ML is not created"),!0)}return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.listData=e.configDDL.append?e.configDDL.listData.concat(t.objectList):t.objectList,e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}},{key:"getAlkpLeaveId",get:function(){return this.myForm.get("alkpLeaveType")}}]),t}()).\u0275fac=function(e){return new(e||n)(u.Ub(f.d),u.Ub(l.e),u.Ub(s.a),u.Ub(s.c),u.Ub(g),u.Ub(h.b),u.Ub(b.a))},n.\u0275cmp=u.Ob({type:n,selectors:[["app-create-leave"]],decls:84,vars:12,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/employeeleaves",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","alkpLeaveType","bindLabel","title","bindValue","id","placeholder","Select","appendTo","body",3,"items"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","addressDuringLeave",1,"form-control"],["type","text","formControlName","reasonForLeave",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/employeeleaves",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"Leave"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"Leave"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Create"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"form",16),u.hc("ngSubmit",function(){return t.myFormSubmit()}),u.ac(25,"div",17),u.ac(26,"label",18),u.Lc(27,"Employee "),u.Zb(),u.ac(28,"div",19),u.ac(29,"ng-select",20),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(30,"div",17),u.ac(31,"label",18),u.Lc(32,"Responsible Employee "),u.Zb(),u.ac(33,"div",19),u.ac(34,"ng-select",21),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",17),u.ac(36,"label",18),u.Lc(37,"Responsible Employee Contact No"),u.Zb(),u.ac(38,"div",19),u.Vb(39,"input",22),u.Zb(),u.Zb(),u.ac(40,"div",17),u.ac(41,"label",18),u.Lc(42,"Leave Type *"),u.Zb(),u.ac(43,"div",19),u.Vb(44,"ng-select",23),u.Zb(),u.Zb(),u.ac(45,"div",17),u.ac(46,"label",18),u.Lc(47,"Start Date"),u.Zb(),u.ac(48,"div",19),u.ac(49,"div",24),u.Vb(50,"input",25),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",17),u.ac(52,"label",18),u.Lc(53,"End Date"),u.Zb(),u.ac(54,"div",19),u.ac(55,"div",24),u.Vb(56,"input",26),u.Zb(),u.Zb(),u.Zb(),u.ac(57,"div",17),u.ac(58,"label",18),u.Lc(59,"Address During Leave"),u.Zb(),u.ac(60,"div",19),u.Vb(61,"textarea",27),u.Zb(),u.Zb(),u.ac(62,"div",17),u.ac(63,"label",18),u.Lc(64,"Reason For Leave"),u.Zb(),u.ac(65,"div",19),u.Vb(66,"textarea",28),u.Zb(),u.Zb(),u.ac(67,"div",17),u.ac(68,"label",18),u.Lc(69,"Remarks"),u.Zb(),u.ac(70,"div",19),u.Vb(71,"textarea",29),u.Zb(),u.Zb(),u.ac(72,"div",30),u.ac(73,"a",31),u.Vb(74,"i",11),u.Lc(75," Cancel"),u.Zb(),u.Lc(76," \xa0 \xa0 "),u.ac(77,"button",32),u.hc("click",function(){return t.resetFormValues()}),u.Vb(78,"i",33),u.Lc(79," Reset "),u.Zb(),u.Lc(80," \xa0 \xa0 "),u.ac(81,"button",34),u.Vb(82,"i",35),u.Lc(83," Save \xa0\xa0\xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(24),u.pc("formGroup",t.myForm),u.Ib(5),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),u.Ib(5),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),u.Ib(10),u.pc("items",t.leaveList))},directives:[s.e,f.x,f.p,f.h,v.a,f.o,f.f,f.t,f.b,D.b,D.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),n),Z=c("JqCM"),y=((r=function(){function t(a,i,c,o,n,r,l,s){e(this,t),this.formBuilder=a,this.datePipe=i,this.route=c,this.router=o,this.leaveService=n,this.toastr=r,this.spinnerService=l,this.commonService=s,this.baseUrl=d.a.baseUrl,this.myFormData={},this.leaveList=[],this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData(),this.loadAlkpLeave()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:[""],contactNo:[""],hrCrEmpResponsible:[""],alkpLeaveType:[""],startDate:[""],endDate:[""],addressDuringLeave:[""],reasonForLeave:[""],remarks:[""]})}},{key:"loadAlkpLeave",value:function(){var e=this;this.commonService.getAlkpByKeyword("LEAVETYPE").subscribe(function(t){e.alkpLeave=t,e.leaveList=e.alkpLeave.subALKP,console.log(e.leaveList)})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/leaveTrnse/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.leaveService.sendGetRequest(t,{}).subscribe(function(t){e.myFormData=t,console.log(e.myFormData),e.spinnerService.hide(),e.configDDL.listData=[{ddlCode:t.hrCrEmp.id,ddlDescription:t.hrCrEmp.loginCode+"-"+t.hrCrEmp.displayName}],e.myFormData.hrCrEmp=t.hrCrEmp.id,e.configDDL.listData2=[{ddlCode:t.hrCrEmpResponsible.id,ddlDescription:t.hrCrEmpResponsible.loginCode+"-"+t.hrCrEmpResponsible.displayName}],e.myFormData.hrCrEmpResponsible=t.hrCrEmpResponsible.id,e.myFormData.startDate=e.datePipe.transform(t.startDate,"MM-dd-yyyy").toString().slice(0,10),e.myFormData.endDate=e.datePipe.transform(t.endDate,"MM-dd-yyyy").toString().slice(0,10),e.myFormData.alkpLeaveType=t.alkpLeaveType.id,e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e=this;if(!this.checkSomeCondition()){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null,alkpLeaveType:this.getAlkpLeaveId.value?{id:this.getAlkpLeaveId.value}:null}),a=this.baseUrl+"/leaveTrnse/save",i={};(i=t).startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.leaveService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.router.navigate(["/sefl-service/employeeleaves"],{relativeTo:e.route})},function(t){console.log(t),e.toastr.error(t.error.message)})}}},{key:"checkSomeCondition",value:function(){if(57==this.myForm.value.alkpLeaveType){var e=new Date;return(this.myForm.value.startDate>e||this.myForm.value.endDate>e)&&(this.toastr.info("ML is not created"),!0)}return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.append?(e.configDDL.listData=e.configDDL.listData.concat(t.objectList),e.configDDL.listData2=e.configDDL.listData2.concat(t.objectList)):(e.configDDL.listData=t.objectList,e.configDDL.listData2=t.objectList),e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}},{key:"getAlkpLeaveId",get:function(){return this.myForm.get("alkpLeaveType")}}]),t}()).\u0275fac=function(e){return new(e||r)(u.Ub(f.d),u.Ub(l.e),u.Ub(s.a),u.Ub(s.c),u.Ub(g),u.Ub(h.b),u.Ub(Z.c),u.Ub(b.a))},r.\u0275cmp=u.Ob({type:r,selectors:[["app-edit-leave"]],decls:84,vars:12,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/employeeleaves",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","alkpLeaveType","bindLabel","title","bindValue","id","placeholder","Select","appendTo","body",3,"items"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","addressDuringLeave",1,"form-control"],["type","text","formControlName","reasonForLeave",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/employeeleaves",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"Leave"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"Leave"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Edit"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"form",16),u.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),u.ac(25,"div",17),u.ac(26,"label",18),u.Lc(27,"Employee "),u.Zb(),u.ac(28,"div",19),u.ac(29,"ng-select",20),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(30,"div",17),u.ac(31,"label",18),u.Lc(32,"Responsible Employee "),u.Zb(),u.ac(33,"div",19),u.ac(34,"ng-select",21),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",17),u.ac(36,"label",18),u.Lc(37,"Responsible Employee Contact No"),u.Zb(),u.ac(38,"div",19),u.Vb(39,"input",22),u.Zb(),u.Zb(),u.ac(40,"div",17),u.ac(41,"label",18),u.Lc(42,"Leave Type *"),u.Zb(),u.ac(43,"div",19),u.Vb(44,"ng-select",23),u.Zb(),u.Zb(),u.ac(45,"div",17),u.ac(46,"label",18),u.Lc(47,"Start Date"),u.Zb(),u.ac(48,"div",19),u.ac(49,"div",24),u.Vb(50,"input",25),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",17),u.ac(52,"label",18),u.Lc(53,"End Date"),u.Zb(),u.ac(54,"div",19),u.ac(55,"div",24),u.Vb(56,"input",26),u.Zb(),u.Zb(),u.Zb(),u.ac(57,"div",17),u.ac(58,"label",18),u.Lc(59,"Address During Leave"),u.Zb(),u.ac(60,"div",19),u.Vb(61,"textarea",27),u.Zb(),u.Zb(),u.ac(62,"div",17),u.ac(63,"label",18),u.Lc(64,"Reason For Leave"),u.Zb(),u.ac(65,"div",19),u.Vb(66,"textarea",28),u.Zb(),u.Zb(),u.ac(67,"div",17),u.ac(68,"label",18),u.Lc(69,"Remarks"),u.Zb(),u.ac(70,"div",19),u.Vb(71,"textarea",29),u.Zb(),u.Zb(),u.ac(72,"div",30),u.ac(73,"a",31),u.Vb(74,"i",11),u.Lc(75," Cancel"),u.Zb(),u.Lc(76," \xa0 \xa0 "),u.ac(77,"button",32),u.hc("click",function(){return t.resetFormValues()}),u.Vb(78,"i",33),u.Lc(79," Reset "),u.Zb(),u.Lc(80," \xa0 \xa0 "),u.ac(81,"button",34),u.Vb(82,"i",35),u.Lc(83," Save \xa0\xa0\xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(24),u.pc("formGroup",t.myForm),u.Ib(5),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),u.Ib(5),u.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),u.Ib(10),u.pc("items",t.leaveList))},directives:[s.e,f.x,f.p,f.h,v.a,f.o,f.f,f.t,f.b,D.b,D.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),r),S=c("QMHJ"),C=c("d//k"),k=c("oOf3");function P(e,t){if(1&e&&(u.ac(0,"div",58),u.ac(1,"div",59),u.ac(2,"h4"),u.Lc(3),u.Zb(),u.Vb(4,"br"),u.ac(5,"h6"),u.Lc(6),u.Zb(),u.ac(7,"h6"),u.Lc(8),u.Zb(),u.ac(9,"h6"),u.Lc(10),u.Zb(),u.Zb(),u.Zb()),2&e){var a=t.$implicit;u.Ib(3),u.Nc("Leave Type : ",a.leaveType,""),u.Ib(3),u.Nc("Leave Days : ",a.leaveDays,""),u.Ib(2),u.Nc("Taken Days : ",a.takenDays,""),u.Ib(2),u.Nc("Carry Days : ",a.carryDays,"")}}function x(e,t){if(1&e){var a=u.bc();u.ac(0,"tr"),u.ac(1,"td"),u.Lc(2),u.Zb(),u.ac(3,"td"),u.Lc(4),u.Zb(),u.ac(5,"td"),u.Lc(6),u.Zb(),u.ac(7,"td"),u.Lc(8),u.Zb(),u.ac(9,"td"),u.Lc(10),u.kc(11,"date"),u.Zb(),u.ac(12,"td"),u.Lc(13),u.kc(14,"date"),u.Zb(),u.ac(15,"td"),u.ac(16,"span",60),u.Lc(17),u.Zb(),u.Zb(),u.ac(18,"td"),u.Lc(19),u.Zb(),u.ac(20,"td",38),u.ac(21,"div",61),u.ac(22,"a",62),u.ac(23,"i",63),u.Lc(24,"more_vert"),u.Zb(),u.Zb(),u.ac(25,"div",64),u.ac(26,"a",65),u.Vb(27,"i",66),u.Lc(28,"View "),u.Zb(),u.ac(29,"a",67),u.Vb(30,"i",68),u.Lc(31,"Edit "),u.Zb(),u.ac(32,"a",69),u.hc("click",function(){u.Cc(a);var e=t.$implicit;return u.jc().tempId=e.id}),u.Vb(33,"i",70),u.Lc(34,"Delete "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()}if(2&e){var i=t.$implicit,c=t.index,o=u.jc();u.Mb("active",c==o.currentIndex),u.Ib(2),u.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),u.Ib(2),u.Mc(i.leaveType),u.Ib(2),u.Mc(i.leaveDays),u.Ib(2),u.Mc(i.createDate),u.Ib(2),u.Mc(u.mc(11,12,i.startDate,"yyyy-MM-dd")),u.Ib(3),u.Mc(u.mc(14,15,i.endDate,"yyyy-MM-dd")),u.Ib(4),u.Mc(i.leaveApprovalStatus),u.Ib(2),u.Mc(i.hrLeavePrd.title),u.Ib(7),u.rc("routerLink","./view/",i.id,""),u.Ib(3),u.rc("routerLink","./edit/",i.id,"")}}function F(e,t){1&e&&(u.ac(0,"tr"),u.ac(1,"td",71),u.ac(2,"h5",72),u.Lc(3,"No data found"),u.Zb(),u.Zb(),u.Zb())}function T(e,t){if(1&e&&(u.ac(0,"option",73),u.Lc(1),u.Zb()),2&e){var a=t.$implicit;u.pc("value",a),u.Ib(1),u.Nc(" ",a," ")}}var E,N,I,M,w,O=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},U=((w=function(){function t(a,i,c,o,n,r){e(this,t),this.formBuilder=a,this.leaveCnfService=i,this.leaveService=c,this.login=o,this.toastr=n,this.spinnerService=r,this.baseUrl=d.a.baseUrl,this.selfLeaveList=[],this.selfCreatedLeaveList=[],this.incharge=[],this.leaveList=[],this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){this.loginUser(),this.loadSelfLeave(),this.loadSelfCreatedLeave()}},{key:"loginUser",value:function(){this.user=this.login.getUser(),console.log(this.user)}},{key:"loadSelfLeave",value:function(){var e=this;this.leaveCnfService.getselfLeave().subscribe(function(t){e.selfLeaveList=t,console.log(e.selfLeaveList)})}},{key:"loadSelfCreatedLeave",value:function(){var e=this,t=this.baseUrl+"/leaveTrnse/getAllHrEmpLeaves",a={};(a=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize)).hrCrEmp=this.user.id,this.spinnerService.show(),this.leaveService.sendGetSelfRequest(t,a).subscribe(function(t){e.selfCreatedLeaveList=t.objectList,e.configPgn.totalItem=t.totalItems,e.configPgn.totalItems=t.totalItems,e.setDisplayLastSequence(),console.log(e.selfCreatedLeaveList),e.spinnerService.hide()},function(e){console.log(e)})}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}},{key:"deleteEnityData",value:function(e){var t=this,a=this.baseUrl+"/leaveTrnse/delete/"+e;console.log(a),this.spinnerService.show(),this.leaveService.sendDeleteRequest(a,{}).subscribe(function(e){console.log(e),t.spinnerService.hide(),$("#delete_entity").modal("hide"),t.toastr.success("Successfully item is deleted","Success"),t.loadSelfCreatedLeave()},function(e){console.log(e),t.spinnerService.hide()})}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.loadSelfCreatedLeave()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.loadSelfCreatedLeave()}},{key:"ngOnDestroy",value:function(){}}]),t}()).\u0275fac=function(e){return new(e||w)(u.Ub(f.d),u.Ub(S.a),u.Ub(g),u.Ub(C.a),u.Ub(h.b),u.Ub(Z.c))},w.\u0275cmp=u.Ob({type:w,selectors:[["app-leaves-employee"]],decls:116,vars:15,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"row"],["class","col-md-3",4,"ngFor","ngForOf"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/sefl-service/employeeleaves/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[1,"text-right"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"col-md-3"],[1,"stats-info"],[1,"badge","badge-success"],[1,"dropdown","dropdown-action"],["data-toggle","dropdown","aria-expanded","false",1,"action-icon","dropdown-toggle"],[1,"material-icons"],[1,"dropdown-menu","dropdown-menu-right"],[1,"btn","btn-sm","btn-info","dropdown-item",3,"routerLink"],[1,"fa","fa-eye","m-r-5"],[1,"btn","btn-sm","btn-primary","dropdown-item",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger","dropdown-item",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"Leaves"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"Leaves"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"List"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"div",10),u.ac(18,"button",11),u.Lc(19,"Excel"),u.Zb(),u.ac(20,"button",11),u.Lc(21,"PDF"),u.Zb(),u.ac(22,"button",11),u.Vb(23,"i",12),u.Lc(24," Print"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(25,"div",13),u.Jc(26,P,11,4,"div",14),u.Zb(),u.ac(27,"div",15),u.ac(28,"div",16),u.ac(29,"div",17),u.ac(30,"div",18),u.ac(31,"div",19),u.Vb(32,"input",20),u.ac(33,"label",21),u.Lc(34,"Employee Code"),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",22),u.ac(36,"div",19),u.ac(37,"div",23),u.Vb(38,"input",24),u.Zb(),u.ac(39,"label",21),u.Lc(40,"From"),u.Zb(),u.Zb(),u.Zb(),u.ac(41,"div",22),u.ac(42,"div",19),u.ac(43,"div",23),u.Vb(44,"input",24),u.Zb(),u.ac(45,"label",21),u.Lc(46,"To"),u.Zb(),u.Zb(),u.Zb(),u.ac(47,"div",22),u.ac(48,"a",25),u.Lc(49," Search "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(50,"div",13),u.ac(51,"div",26),u.ac(52,"div",27),u.ac(53,"div",28),u.ac(54,"div",29),u.ac(55,"a",30),u.Vb(56,"i",31),u.Lc(57," New \xa0\xa0\xa0"),u.Zb(),u.Zb(),u.Zb(),u.ac(58,"div",32),u.ac(59,"div",33),u.ac(60,"div",34),u.ac(61,"div",35),u.ac(62,"span",36),u.Lc(63),u.Zb(),u.Zb(),u.Zb(),u.ac(64,"table",37),u.ac(65,"thead"),u.ac(66,"tr"),u.ac(67,"th"),u.Lc(68,"#"),u.Zb(),u.ac(69,"th"),u.Lc(70,"Leave Type"),u.Zb(),u.ac(71,"th"),u.Lc(72,"Leave Days"),u.Zb(),u.ac(73,"th"),u.Lc(74,"Apply Date"),u.Zb(),u.ac(75,"th"),u.Lc(76,"From Date"),u.Zb(),u.ac(77,"th"),u.Lc(78,"To Date"),u.Zb(),u.ac(79,"th"),u.Lc(80,"Approved Sts"),u.Zb(),u.ac(81,"th"),u.Lc(82,"Leave Prd"),u.Zb(),u.ac(83,"th",38),u.Lc(84,"Action"),u.Zb(),u.Zb(),u.Zb(),u.ac(85,"tbody"),u.Jc(86,x,35,18,"tr",39),u.kc(87,"paginate"),u.Jc(88,F,4,0,"tr",40),u.Zb(),u.Zb(),u.ac(89,"div",41),u.ac(90,"div",42),u.Lc(91," Items per Page "),u.ac(92,"select",43),u.hc("change",function(e){return t.handlePageSizeChange(e)}),u.Jc(93,T,2,2,"option",44),u.Zb(),u.Zb(),u.ac(94,"div",45),u.ac(95,"pagination-controls",46),u.hc("pageChange",function(e){return t.handlePageChange(e)}),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(96,"ngx-spinner",47),u.ac(97,"p",48),u.Lc(98," Processing... "),u.Zb(),u.Zb(),u.ac(99,"div",49),u.ac(100,"div",50),u.ac(101,"div",51),u.ac(102,"div",52),u.ac(103,"div",53),u.ac(104,"h3"),u.Lc(105,"Delete Item"),u.Zb(),u.ac(106,"p"),u.Lc(107,"Are you sure want to delete?"),u.Zb(),u.Zb(),u.ac(108,"div",54),u.ac(109,"div",13),u.ac(110,"div",55),u.ac(111,"a",56),u.Lc(112,"Delete"),u.Zb(),u.Zb(),u.ac(113,"div",55),u.ac(114,"a",57),u.Lc(115,"Cancel"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(26),u.pc("ngForOf",t.selfLeaveList),u.Ib(12),u.pc("bsConfig",u.sc(13,O)),u.Ib(6),u.pc("bsConfig",u.sc(14,O)),u.Ib(19),u.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),u.Ib(23),u.pc("ngForOf",u.mc(87,10,t.selfCreatedLeaveList,t.configPgn)),u.Ib(2),u.pc("ngIf",0===t.selfCreatedLeaveList.length),u.Ib(5),u.pc("ngForOf",t.configPgn.pageSizes),u.Ib(3),u.pc("fullScreen",!1))},directives:[s.e,l.l,D.b,D.a,l.m,k.c,Z.a,f.s,f.y],pipes:[k.b,l.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}.title[_ngcontent-%COMP%]{background-color:#aabbdc;color:#105ff1;text-align:center}"]}),w),R=((M=function(){function t(a,i,c){e(this,t),this.route=a,this.spinnerService=i,this.leaveService=c,this.baseUrl=d.a.baseUrl,this.myData={}}return a(t,[{key:"ngOnInit",value:function(){this.getFormData()}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/leaveTrnse/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.leaveService.sendGetRequest(t,{}).subscribe(function(t){e.myData=t,console.log(e.myData),e.spinnerService.hide()},function(e){console.log(e)})}}]),t}()).\u0275fac=function(e){return new(e||M)(u.Ub(s.a),u.Ub(Z.c),u.Ub(g))},M.\u0275cmp=u.Ob({type:M,selectors:[["app-view-leave"]],decls:150,vars:24,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/employeeleaves",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"row","fieldsetBorder","logBox"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/sefl-service/employeeleaves",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self-service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"OnTour"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Show"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"fieldset",16),u.Vb(25,"legend"),u.ac(26,"div",17),u.ac(27,"div",18),u.ac(28,"label",19),u.Lc(29,"Employee"),u.Zb(),u.ac(30,"div",20),u.ac(31,"span"),u.Lc(32,": \xa0"),u.Zb(),u.ac(33,"span"),u.Lc(34),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",18),u.ac(36,"label",19),u.Lc(37,"Responsible Employee"),u.Zb(),u.ac(38,"div",20),u.ac(39,"span"),u.Lc(40,": \xa0"),u.Zb(),u.ac(41,"span"),u.Lc(42),u.Zb(),u.Zb(),u.Zb(),u.ac(43,"div",18),u.ac(44,"label",19),u.Lc(45,"Leave Type"),u.Zb(),u.ac(46,"div",20),u.ac(47,"span"),u.Lc(48,": \xa0"),u.Zb(),u.ac(49,"span"),u.Lc(50),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",18),u.ac(52,"label",19),u.Lc(53,"Start Date"),u.Zb(),u.ac(54,"div",20),u.ac(55,"span"),u.Lc(56,": \xa0"),u.Zb(),u.ac(57,"span"),u.Lc(58),u.kc(59,"date"),u.Zb(),u.Zb(),u.Zb(),u.ac(60,"div",18),u.ac(61,"label",19),u.Lc(62,"End Date"),u.Zb(),u.ac(63,"div",20),u.ac(64,"span"),u.Lc(65,": \xa0"),u.Zb(),u.ac(66,"span"),u.Lc(67),u.kc(68,"date"),u.Zb(),u.Zb(),u.Zb(),u.ac(69,"div",18),u.ac(70,"label",19),u.Lc(71,"Leave Days"),u.Zb(),u.ac(72,"div",20),u.ac(73,"span"),u.Lc(74,": \xa0"),u.Zb(),u.ac(75,"span"),u.Lc(76),u.Zb(),u.Zb(),u.Zb(),u.ac(77,"div",18),u.ac(78,"label",19),u.Lc(79,"Leave Location"),u.Zb(),u.ac(80,"div",20),u.ac(81,"span"),u.Lc(82,": \xa0"),u.Zb(),u.ac(83,"span"),u.Lc(84),u.Zb(),u.Zb(),u.Zb(),u.ac(85,"div",18),u.ac(86,"label",19),u.Lc(87,"Couse Of Leave"),u.Zb(),u.ac(88,"div",20),u.ac(89,"span"),u.Lc(90,": \xa0"),u.Zb(),u.ac(91,"span"),u.Lc(92),u.Zb(),u.Zb(),u.Zb(),u.ac(93,"div",18),u.ac(94,"label",19),u.Lc(95,"Remarks"),u.Zb(),u.ac(96,"div",20),u.ac(97,"span"),u.Lc(98,": \xa0"),u.Zb(),u.ac(99,"span"),u.Lc(100),u.Zb(),u.Zb(),u.Zb(),u.ac(101,"div",18),u.ac(102,"label",19),u.Lc(103,"Approval Status"),u.Zb(),u.ac(104,"div",20),u.ac(105,"span"),u.Lc(106,": \xa0"),u.Zb(),u.ac(107,"span"),u.Lc(108),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(109,"fieldset",21),u.ac(110,"legend"),u.Lc(111,"System Log Information"),u.Zb(),u.ac(112,"div",22),u.ac(113,"label",23),u.Lc(114,"ID"),u.Zb(),u.ac(115,"div",24),u.ac(116,"span"),u.Lc(117),u.Zb(),u.Zb(),u.Zb(),u.ac(118,"div",22),u.ac(119,"label",23),u.Lc(120,"Creation Time"),u.Zb(),u.ac(121,"div",24),u.ac(122,"span"),u.Lc(123),u.Zb(),u.Zb(),u.Zb(),u.ac(124,"div",22),u.ac(125,"label",23),u.Lc(126,"Creation User"),u.Zb(),u.ac(127,"div",24),u.ac(128,"span"),u.Lc(129),u.Zb(),u.Zb(),u.Zb(),u.ac(130,"div",22),u.ac(131,"label",23),u.Lc(132,"Last Update Time"),u.Zb(),u.ac(133,"div",24),u.ac(134,"span"),u.Lc(135),u.Zb(),u.Zb(),u.Zb(),u.ac(136,"div",22),u.ac(137,"label",23),u.Lc(138,"Last Update User"),u.Zb(),u.ac(139,"div",24),u.ac(140,"span"),u.Lc(141),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(142,"div",25),u.ac(143,"a",26),u.Vb(144,"i",11),u.Lc(145," Close"),u.Zb(),u.Lc(146," \xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(147,"ngx-spinner",27),u.ac(148,"p",28),u.Lc(149," Processing... "),u.Zb(),u.Zb()),2&e&&(u.Ib(34),u.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),u.Ib(8),u.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),u.Ib(8),u.Mc(t.myData.leaveType),u.Ib(8),u.Mc(u.mc(59,18,t.myData.startDate,"yyyy-MM-dd")),u.Ib(9),u.Mc(u.mc(68,21,t.myData.endDate,"yyyy-MM-dd")),u.Ib(9),u.Mc(t.myData.leaveDays),u.Ib(8),u.Mc(t.myData.addressDuringLeave),u.Ib(8),u.Mc(t.myData.reasonForLeave),u.Ib(8),u.Mc(t.myData.remarks),u.Ib(8),u.Mc(t.myData.leaveApprovalStatus),u.Ib(9),u.Mc(t.myData.id),u.Ib(6),u.Mc(t.myData.createDate),u.Ib(6),u.Mc(t.myData.createdByHrCrEmp.user.username),u.Ib(6),u.Mc(t.myData.updateDateTime),u.Ib(6),u.Mc(t.myData.lastUpdateUser),u.Ib(6),u.pc("fullScreen",!1))},directives:[s.e,Z.a],pipes:[l.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),M),A=((I=function(){function t(a){e(this,t),this.http=a}return a(t,[{key:"sendGetSelfRequest",value:function(e,t){return console.log("@sendGetSelfRequest"),this.http.get(e,{params:t}).pipe(Object(p.a)(3))}},{key:"sendPostRequest",value:function(e,t){return console.log("@sendPostRequest"),this.http.post(e,t)}},{key:"sendGetRequest",value:function(e,t){return console.log("@sendGetRequest"),this.http.get(e,{params:t}).pipe(Object(p.a)(3))}},{key:"sendDeleteRequest",value:function(e,t){return console.log("@sendDeleteRequest"),this.http.delete(e,t)}}]),t}()).\u0275fac=function(e){return new(e||I)(u.ec(m.c))},I.\u0275prov=u.Qb({token:I,factory:I.\u0275fac,providedIn:"root"}),I),q=((N=function(){function t(a,i,c,o,n,r,l,s){e(this,t),this.formBuilder=a,this.datePipe=i,this.login=c,this.route=o,this.router=n,this.onTourService=r,this.toastr=l,this.commonService=s,this.baseUrl=d.a.baseUrl,this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.liginUser(),this.setValue()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({hrCrEmp:{},contactNo:["",[f.w.required]],hrCrEmpResponsible:[{},[f.w.required]],tourType:["",[f.w.required]],startDate:["",[f.w.required]],endDate:["",[f.w.required]],addressDuringTour:["",[f.w.required]],reasonForTour:["",[f.w.required]],remarks:[""]})}},{key:"liginUser",value:function(){this.user=this.login.getUser(),console.log(this.user)}},{key:"setValue",value:function(){this.myForm.get("hrCrEmp").setValue(this.user.firstName+" "+this.user.lastName),this.myForm.get("contactNo").setValue(this.user.mobCode)}},{key:"myFormSubmit",value:function(){var e=this;if(this.myForm.invalid)this.toastr.info("Please insert valid data");else if(!this.checkSomeCondition()){var t=Object.assign(this.myForm.value,{hrCrEmp:{id:this.user.id},hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),a=this.baseUrl+"/onTourTnx/save",i={};i=t,console.log(i),i.startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.onTourService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.router.navigate(["/sefl-service/onTour"],{relativeTo:e.route})},function(e){console.log(e)})}}},{key:"checkSomeCondition",value:function(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}},{key:"contactFind",value:function(e){alert(e)}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.listData=e.configDDL.append?e.configDDL.listData.concat(t.objectList):t.objectList,e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}}]),t}()).\u0275fac=function(e){return new(e||N)(u.Ub(f.d),u.Ub(l.e),u.Ub(C.a),u.Ub(s.a),u.Ub(s.c),u.Ub(A),u.Ub(h.b),u.Ub(b.a))},N.\u0275cmp=u.Ob({type:N,selectors:[["app-create"]],decls:90,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTour",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","text","formControlName","hrCrEmp","disabled","",1,"form-control"],["oninput","javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);","type","number","maxlength","11","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTour",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Create"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"form",16),u.hc("ngSubmit",function(){return t.myFormSubmit()}),u.ac(25,"div",17),u.ac(26,"label",18),u.Lc(27,"Employee"),u.Zb(),u.ac(28,"div",19),u.Vb(29,"input",20),u.Zb(),u.Zb(),u.ac(30,"div",17),u.ac(31,"label",18),u.Lc(32,"Contact No"),u.Zb(),u.ac(33,"div",19),u.Vb(34,"input",21),u.Zb(),u.Zb(),u.ac(35,"div",17),u.ac(36,"label",18),u.Lc(37,"Responsible Employee "),u.Zb(),u.ac(38,"div",19),u.ac(39,"ng-select",22),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(40,"div",17),u.ac(41,"label",18),u.Lc(42,"Tour Type"),u.Zb(),u.ac(43,"div",19),u.ac(44,"select",23),u.ac(45,"option",24),u.Lc(46,"Select"),u.Zb(),u.ac(47,"option",25),u.Lc(48,"Local"),u.Zb(),u.ac(49,"option",26),u.Lc(50,"Global"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",17),u.ac(52,"label",18),u.Lc(53,"Start Date"),u.Zb(),u.ac(54,"div",19),u.ac(55,"div",27),u.Vb(56,"input",28),u.Zb(),u.Zb(),u.Zb(),u.ac(57,"div",17),u.ac(58,"label",18),u.Lc(59,"End Date"),u.Zb(),u.ac(60,"div",19),u.ac(61,"div",27),u.Vb(62,"input",29),u.Zb(),u.Zb(),u.Zb(),u.ac(63,"div",17),u.ac(64,"label",18),u.Lc(65,"Address During Tour"),u.Zb(),u.ac(66,"div",19),u.Vb(67,"textarea",30),u.Zb(),u.Zb(),u.ac(68,"div",17),u.ac(69,"label",18),u.Lc(70,"Reason For Tour"),u.Zb(),u.ac(71,"div",19),u.Vb(72,"textarea",31),u.Zb(),u.Zb(),u.ac(73,"div",17),u.ac(74,"label",18),u.Lc(75,"Remarks"),u.Zb(),u.ac(76,"div",19),u.Vb(77,"textarea",32),u.Zb(),u.Zb(),u.ac(78,"div",33),u.ac(79,"a",34),u.Vb(80,"i",11),u.Lc(81," Cancel"),u.Zb(),u.Lc(82," \xa0 \xa0 "),u.ac(83,"button",35),u.hc("click",function(){return t.resetFormValues()}),u.Vb(84,"i",36),u.Lc(85," Reset "),u.Zb(),u.Lc(86," \xa0 \xa0 "),u.ac(87,"button",37),u.Vb(88,"i",38),u.Lc(89," Save \xa0\xa0\xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(24),u.pc("formGroup",t.myForm),u.Ib(15),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[s.e,f.x,f.p,f.h,f.b,f.o,f.f,f.t,f.k,v.a,f.v,f.s,f.y,D.b,D.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),N),V=((E=function(){function t(a,i,c,o,n,r,l,s){e(this,t),this.formBuilder=a,this.datePipe=i,this.route=c,this.router=o,this.onTourService=n,this.toastr=r,this.commonService=l,this.spinnerService=s,this.baseUrl=d.a.baseUrl,this.myFormData={},this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",[f.w.required]],contactNo:["",[f.w.required]],hrCrEmpResponsible:["",[f.w.required]],tourType:["",[f.w.required]],startDate:["",[f.w.required]],endDate:["",[f.w.required]],addressDuringTour:["",[f.w.required]],reasonForTour:["",[f.w.required]],remarks:[""]})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(t,{}).subscribe(function(t){e.myFormData=t,console.log(e.myFormData),e.spinnerService.hide(),e.configDDL.listData=[{ddlCode:t.hrCrEmp.id,ddlDescription:t.hrCrEmp.loginCode+"-"+t.hrCrEmp.displayName}],e.myFormData.hrCrEmp=t.hrCrEmp.id,e.configDDL.listData2=[{ddlCode:t.hrCrEmpResponsible.id,ddlDescription:t.hrCrEmpResponsible.loginCode+"-"+t.hrCrEmpResponsible.displayName}],e.myFormData.hrCrEmpResponsible=t.hrCrEmpResponsible.id,e.myFormData.startDate=e.datePipe.transform(t.startDate,"MM-dd-yyyy").toString().slice(0,10),e.myFormData.endDate=e.datePipe.transform(t.endDate,"MM-dd-yyyy").toString().slice(0,10),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e=this;if(this.myForm.invalid)this.toastr.info("Please insert valid data");else if(!this.checkSomeCondition()){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),a=this.baseUrl+"/onTourTnx/save";console.log(a);var i={};(i=t).startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.onTourService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/sefl-service/onTour"],{relativeTo:e.route})},function(t){console.log(t),e.toastr.warning(t.error.message),e.spinnerService.hide()})}}},{key:"checkSomeCondition",value:function(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.append?(e.configDDL.listData=e.configDDL.listData.concat(t.objectList),e.configDDL.listData2=e.configDDL.listData2.concat(t.objectList)):(e.configDDL.listData=t.objectList,e.configDDL.listData2=t.objectList),e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}}]),t}()).\u0275fac=function(e){return new(e||E)(u.Ub(f.d),u.Ub(l.e),u.Ub(s.a),u.Ub(s.c),u.Ub(A),u.Ub(h.b),u.Ub(b.a),u.Ub(Z.c))},E.\u0275cmp=u.Ob({type:E,selectors:[["app-edit"]],decls:85,vars:6,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTour",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTour",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Edit"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"form",16),u.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),u.ac(25,"div",17),u.ac(26,"label",18),u.Lc(27,"Contact No"),u.Zb(),u.ac(28,"div",19),u.Vb(29,"input",20),u.Zb(),u.Zb(),u.ac(30,"div",17),u.ac(31,"label",18),u.Lc(32,"Responsible Employee "),u.Zb(),u.ac(33,"div",19),u.ac(34,"ng-select",21),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",17),u.ac(36,"label",18),u.Lc(37,"Tour Type"),u.Zb(),u.ac(38,"div",19),u.ac(39,"select",22),u.ac(40,"option",23),u.Lc(41,"Select"),u.Zb(),u.ac(42,"option",24),u.Lc(43,"Local"),u.Zb(),u.ac(44,"option",25),u.Lc(45,"Global"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(46,"div",17),u.ac(47,"label",18),u.Lc(48,"Start Date"),u.Zb(),u.ac(49,"div",19),u.ac(50,"div",26),u.Vb(51,"input",27),u.Zb(),u.Zb(),u.Zb(),u.ac(52,"div",17),u.ac(53,"label",18),u.Lc(54,"End Date"),u.Zb(),u.ac(55,"div",19),u.ac(56,"div",26),u.Vb(57,"input",28),u.Zb(),u.Zb(),u.Zb(),u.ac(58,"div",17),u.ac(59,"label",18),u.Lc(60,"Address During Tour"),u.Zb(),u.ac(61,"div",19),u.Vb(62,"textarea",29),u.Zb(),u.Zb(),u.ac(63,"div",17),u.ac(64,"label",18),u.Lc(65,"Reason For Tour"),u.Zb(),u.ac(66,"div",19),u.Vb(67,"textarea",30),u.Zb(),u.Zb(),u.ac(68,"div",17),u.ac(69,"label",18),u.Lc(70,"Remarks"),u.Zb(),u.ac(71,"div",19),u.Vb(72,"textarea",31),u.Zb(),u.Zb(),u.ac(73,"div",32),u.ac(74,"a",33),u.Vb(75,"i",11),u.Lc(76," Cancel"),u.Zb(),u.Lc(77," \xa0 \xa0 "),u.ac(78,"button",34),u.hc("click",function(){return t.resetFormValues()}),u.Vb(79,"i",35),u.Lc(80," Reset "),u.Zb(),u.Lc(81," \xa0 \xa0 "),u.ac(82,"button",36),u.Vb(83,"i",37),u.Lc(84," Save \xa0\xa0\xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(24),u.pc("formGroup",t.myForm),u.Ib(10),u.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[s.e,f.x,f.p,f.h,f.t,f.b,f.o,f.f,v.a,f.v,f.s,f.y,D.b,D.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),E);function _(e,t){if(1&e){var a=u.bc();u.ac(0,"tr"),u.ac(1,"td"),u.Lc(2),u.Zb(),u.ac(3,"td"),u.Lc(4),u.Zb(),u.ac(5,"td"),u.Lc(6),u.Zb(),u.ac(7,"td"),u.Lc(8),u.kc(9,"date"),u.Zb(),u.ac(10,"td"),u.Lc(11),u.kc(12,"date"),u.Zb(),u.ac(13,"td"),u.Lc(14),u.kc(15,"date"),u.Zb(),u.ac(16,"td"),u.ac(17,"span",56),u.Lc(18),u.Zb(),u.Zb(),u.ac(19,"td"),u.ac(20,"a",57),u.Lc(21,"View"),u.Zb(),u.Lc(22," \xa0 "),u.ac(23,"a",58),u.Vb(24,"i",59),u.Zb(),u.Lc(25,"\xa0\xa0 "),u.ac(26,"a",60),u.hc("click",function(){u.Cc(a);var e=t.$implicit;return u.jc().tempId=e.id}),u.Vb(27,"i",61),u.Zb(),u.Zb(),u.Zb()}if(2&e){var i=t.$implicit,c=t.index,o=u.jc();u.Mb("active",c==o.currentIndex),u.Ib(2),u.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),u.Ib(2),u.Mc(i.tourType),u.Ib(2),u.Mc(i.tourDays),u.Ib(2),u.Mc(u.mc(9,11,i.createDate,"yyyy-MM-dd")),u.Ib(3),u.Mc(u.mc(12,14,i.startDate,"yyyy-MM-dd")),u.Ib(3),u.Mc(u.mc(15,17,i.endDate,"yyyy-MM-dd")),u.Ib(4),u.Mc(i.tourApprovalStatus),u.Ib(2),u.rc("routerLink","./view/",i.id,""),u.Ib(3),u.rc("routerLink","./edit/",i.id,"")}}function z(e,t){1&e&&(u.ac(0,"tr"),u.ac(1,"td",62),u.ac(2,"h5",63),u.Lc(3,"No data found"),u.Zb(),u.Zb(),u.Zb())}function G(e,t){if(1&e&&(u.ac(0,"option",64),u.Lc(1),u.Zb()),2&e){var a=t.$implicit;u.pc("value",a),u.Ib(1),u.Nc(" ",a," ")}}var H,B=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},j=((H=function(){function t(a,i,c,o){e(this,t),this.onTourService=a,this.login=i,this.spinnerService=c,this.toastr=o,this.baseUrl=d.a.baseUrl,this.pipe=new l.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){this.loginUser(),this.getSelfListData()}},{key:"loginUser",value:function(){this.user=this.login.getUser(),console.log(this.user)}},{key:"getSelfListData",value:function(){var e,t=this,a=this.baseUrl+"/onTourTnx/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.onTourService.sendGetSelfRequest(a,e).subscribe(function(e){t.listData=e.objectList,t.configPgn.totalItem=e.totalItems,t.configPgn.totalItems=e.totalItems,t.setDisplayLastSequence(),t.spinnerService.hide()},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var t=this,a=this.baseUrl+"/onTourTnx/delete/"+e;console.log(a),this.spinnerService.show(),this.onTourService.sendDeleteRequest(a,{}).subscribe(function(e){console.log(e),t.spinnerService.hide(),$("#delete_entity").modal("hide"),t.toastr.success("Successfully item is deleted","Success"),t.getSelfListData()},function(e){$("#delete_entity").modal("hide"),t.toastr.warning(e.error.message),t.spinnerService.hide()})}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),this.user.id&&(a.hrCrEmp=this.user.id),a}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getSelfListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getSelfListData()}}]),t}()).\u0275fac=function(e){return new(e||H)(u.Ub(A),u.Ub(C.a),u.Ub(Z.c),u.Ub(h.b))},H.\u0275cmp=u.Ob({type:H,selectors:[["app-on-tour"]],decls:112,vars:14,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/sefl-service/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"badge","badge-success"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"List"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"div",10),u.ac(18,"button",11),u.Lc(19,"Excel"),u.Zb(),u.ac(20,"button",11),u.Lc(21,"PDF"),u.Zb(),u.ac(22,"button",11),u.Vb(23,"i",12),u.Lc(24," Print"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(25,"div",13),u.ac(26,"div",14),u.ac(27,"div",15),u.ac(28,"div",16),u.ac(29,"div",17),u.Vb(30,"input",18),u.ac(31,"label",19),u.Lc(32,"Employee Code"),u.Zb(),u.Zb(),u.Zb(),u.ac(33,"div",20),u.ac(34,"div",17),u.ac(35,"div",21),u.Vb(36,"input",22),u.Zb(),u.ac(37,"label",19),u.Lc(38,"From"),u.Zb(),u.Zb(),u.Zb(),u.ac(39,"div",20),u.ac(40,"div",17),u.ac(41,"div",21),u.Vb(42,"input",22),u.Zb(),u.ac(43,"label",19),u.Lc(44,"To"),u.Zb(),u.Zb(),u.Zb(),u.ac(45,"div",20),u.ac(46,"a",23),u.Lc(47," Search "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(48,"div",24),u.ac(49,"div",25),u.ac(50,"div",26),u.ac(51,"div",27),u.ac(52,"div",28),u.ac(53,"a",29),u.Vb(54,"i",30),u.Lc(55," New \xa0\xa0\xa0"),u.Zb(),u.Zb(),u.Zb(),u.ac(56,"div",31),u.ac(57,"div",32),u.ac(58,"div",33),u.ac(59,"div",34),u.ac(60,"span",35),u.Lc(61),u.Zb(),u.Zb(),u.Zb(),u.ac(62,"table",36),u.ac(63,"thead"),u.ac(64,"tr"),u.ac(65,"th"),u.Lc(66,"SL"),u.Zb(),u.ac(67,"th"),u.Lc(68,"Tour Type"),u.Zb(),u.ac(69,"th"),u.Lc(70,"Total Days"),u.Zb(),u.ac(71,"th"),u.Lc(72,"Apply Date"),u.Zb(),u.ac(73,"th"),u.Lc(74,"From Date"),u.Zb(),u.ac(75,"th"),u.Lc(76,"To Date"),u.Zb(),u.ac(77,"th"),u.Lc(78,"Approval Status"),u.Zb(),u.ac(79,"th"),u.Lc(80,"Action"),u.Zb(),u.Zb(),u.Zb(),u.ac(81,"tbody"),u.Jc(82,_,28,20,"tr",37),u.kc(83,"paginate"),u.Jc(84,z,4,0,"tr",38),u.Zb(),u.Zb(),u.ac(85,"div",39),u.ac(86,"div",40),u.Lc(87," Items per Page "),u.ac(88,"select",41),u.hc("change",function(e){return t.handlePageSizeChange(e)}),u.Jc(89,G,2,2,"option",42),u.Zb(),u.Zb(),u.ac(90,"div",43),u.ac(91,"pagination-controls",44),u.hc("pageChange",function(e){return t.handlePageChange(e)}),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(92,"ngx-spinner",45),u.ac(93,"p",46),u.Lc(94," Processing... "),u.Zb(),u.Zb(),u.ac(95,"div",47),u.ac(96,"div",48),u.ac(97,"div",49),u.ac(98,"div",50),u.ac(99,"div",51),u.ac(100,"h3"),u.Lc(101,"Delete Item"),u.Zb(),u.ac(102,"p"),u.Lc(103,"Are you sure want to delete?"),u.Zb(),u.Zb(),u.ac(104,"div",52),u.ac(105,"div",24),u.ac(106,"div",53),u.ac(107,"a",54),u.hc("click",function(){return t.deleteEnityData(t.tempId)}),u.Lc(108,"Delete"),u.Zb(),u.Zb(),u.ac(109,"div",53),u.ac(110,"a",55),u.Lc(111,"Cancel"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(36),u.pc("bsConfig",u.sc(12,B)),u.Ib(6),u.pc("bsConfig",u.sc(13,B)),u.Ib(19),u.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),u.Ib(21),u.pc("ngForOf",u.mc(83,9,t.listData,t.configPgn)),u.Ib(2),u.pc("ngIf",0===t.listData.length),u.Ib(5),u.pc("ngForOf",t.configPgn.pageSizes),u.Ib(3),u.pc("fullScreen",!1))},directives:[s.e,D.b,D.a,l.l,l.m,k.c,Z.a,f.s,f.y],pipes:[k.b,l.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),H),Q=c("NllD");function J(e,t){if(1&e&&(u.ac(0,"tr"),u.ac(1,"td"),u.Lc(2),u.Zb(),u.ac(3,"td"),u.Lc(4),u.Zb(),u.ac(5,"td"),u.Lc(6),u.Zb(),u.ac(7,"td"),u.Lc(8),u.Zb(),u.ac(9,"td"),u.Lc(10),u.Zb(),u.ac(11,"td"),u.Lc(12),u.Zb(),u.Zb()),2&e){var a=t.$implicit,i=t.index;u.Ib(2),u.Nc(" ",1+i," "),u.Ib(2),u.Mc(a.approvalStep.approvalGroupName?a.approvalStep.approvalGroupName:"null"),u.Ib(2),u.Mc(a.approvalStepApproverEmp?a.approvalStepApproverEmp.displayName:"null"),u.Ib(2),u.Mc(a.actionStatus?a.actionStatus:"null"),u.Ib(2),u.Mc(a.updateDateTime?a.updateDateTime:"null"),u.Ib(2),u.Mc(a.remarks?a.remarks:"null")}}var Y,W,K,X=((K=function(){function t(a,i,c,o,n,r,l){e(this,t),this.route=a,this.spinnerService=i,this.onTourService=c,this.approvalService=o,this.toastr=n,this.formBuilder=r,this.router=l,this.baseUrl=d.a.baseUrl,this.myData={},this.listData=[],this.listData2=[]}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData(),this.getSelfListData(),this.getApprovalStepAction()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],approvalStepAction:[{},f.w.required],remarks:["",f.w.required]})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(t,{}).subscribe(function(t){e.myData=t,console.log(e.myData),e.spinnerService.hide()},function(e){console.log(e)})}},{key:"getSelfListData",value:function(){var e,t=this,a=this.baseUrl+"/approvalProcTnxHtry/getSelfApprovalProcTnxList/"+this.route.snapshot.params.id;e=this.getUserQueryParams(),this.approvalService.sendGetRequest(a,e).subscribe(function(e){t.listData=e,console.log(t.listData)},function(e){console.log(e)})}},{key:"getApprovalStepAction",value:function(){var e,t=this,a=this.baseUrl+"/approvalStepAction/getApprovalStepAction/"+this.route.snapshot.params.id;e=this.getUserQueryParams(),this.approvalService.sendGetRequest(a,e).subscribe(function(e){t.listData2=e},function(e){console.log(e)})}},{key:"tackAction",value:function(){var e=this;if(!this.myForm.invalid){var t,a=Object.assign(this.myForm.value,{referenceId:this.route.snapshot.params.id,referenceEntity:"ONTOUR_PROCESS",approvalStepAction:this.get.value?{id:this.get.value}:null});console.log(a),t=a,this.approvalService.sendPutRequest(this.baseUrl+"/approvalProcTnxHtry/edit",t).subscribe(function(t){console.log(t),e.getFormData(),e.getSelfListData(),e.getApprovalStepAction(),e.resetFormValues()},function(t){console.log(t),e.toastr.info(t.error.message)})}}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"getUserQueryParams",value:function(){return{approvalProcess:"ONTOUR_PROCESS"}}},{key:"get",get:function(){return this.myForm.get("approvalStepAction")}}]),t}()).\u0275fac=function(e){return new(e||K)(u.Ub(s.a),u.Ub(Z.c),u.Ub(A),u.Ub(Q.a),u.Ub(h.b),u.Ub(f.d),u.Ub(s.c))},K.\u0275cmp=u.Ob({type:K,selectors:[["app-view"]],decls:207,vars:29,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTour",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"col-8"],[1,"row","fieldsetBorder","logBox"],[1,"table"],[4,"ngFor","ngForOf"],[1,"col-4"],["novalidate","",3,"formGroup","ngSubmit"],[1,"col-form-label","col-md-4"],[1,"col-md-8"],["formControlName","approvalStepAction","bindLabel","activityStatusTitle","bindValue","id","placeholder","Select","appendTo","body",3,"items"],["formControlName","remarks",1,"form-control","mb-3"],[1,"col-md-9"],[1,"col-md-3"],["type","submit",1,"btn","btn-secondary","btn-sm","mb-2"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/sefl-service/onTour",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self-service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"OnTour"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Show"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"fieldset",16),u.Vb(25,"legend"),u.ac(26,"div",17),u.ac(27,"div",18),u.ac(28,"label",19),u.Lc(29,"Employee"),u.Zb(),u.ac(30,"div",20),u.ac(31,"span"),u.Lc(32,": \xa0"),u.Zb(),u.ac(33,"span"),u.Lc(34),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",18),u.ac(36,"label",19),u.Lc(37,"Mobile No"),u.Zb(),u.ac(38,"div",20),u.ac(39,"span"),u.Lc(40,": \xa0"),u.Zb(),u.ac(41,"span"),u.Lc(42),u.Zb(),u.Zb(),u.Zb(),u.ac(43,"div",18),u.ac(44,"label",19),u.Lc(45,"Responsible Employee"),u.Zb(),u.ac(46,"div",20),u.ac(47,"span"),u.Lc(48,": \xa0"),u.Zb(),u.ac(49,"span"),u.Lc(50),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",18),u.ac(52,"label",19),u.Lc(53,"Mobile No"),u.Zb(),u.ac(54,"div",20),u.ac(55,"span"),u.Lc(56,": \xa0"),u.Zb(),u.ac(57,"span"),u.Lc(58),u.Zb(),u.Zb(),u.Zb(),u.ac(59,"div",18),u.ac(60,"label",19),u.Lc(61,"Tour Type"),u.Zb(),u.ac(62,"div",20),u.ac(63,"span"),u.Lc(64,": \xa0"),u.Zb(),u.ac(65,"span"),u.Lc(66),u.Zb(),u.Zb(),u.Zb(),u.ac(67,"div",18),u.ac(68,"label",19),u.Lc(69,"Start Date"),u.Zb(),u.ac(70,"div",20),u.ac(71,"span"),u.Lc(72,": \xa0"),u.Zb(),u.ac(73,"span"),u.Lc(74),u.kc(75,"date"),u.Zb(),u.Zb(),u.Zb(),u.ac(76,"div",18),u.ac(77,"label",19),u.Lc(78,"End Date"),u.Zb(),u.ac(79,"div",20),u.ac(80,"span"),u.Lc(81,": \xa0"),u.Zb(),u.ac(82,"span"),u.Lc(83),u.kc(84,"date"),u.Zb(),u.Zb(),u.Zb(),u.ac(85,"div",18),u.ac(86,"label",19),u.Lc(87,"Total Days"),u.Zb(),u.ac(88,"div",20),u.ac(89,"span"),u.Lc(90,": \xa0"),u.Zb(),u.ac(91,"span"),u.Lc(92),u.Zb(),u.Zb(),u.Zb(),u.ac(93,"div",18),u.ac(94,"label",19),u.Lc(95,"Tour Location"),u.Zb(),u.ac(96,"div",20),u.ac(97,"span"),u.Lc(98,": \xa0"),u.Zb(),u.ac(99,"span"),u.Lc(100),u.Zb(),u.Zb(),u.Zb(),u.ac(101,"div",18),u.ac(102,"label",19),u.Lc(103,"Couse Of Tour"),u.Zb(),u.ac(104,"div",20),u.ac(105,"span"),u.Lc(106,": \xa0"),u.Zb(),u.ac(107,"span"),u.Lc(108),u.Zb(),u.Zb(),u.Zb(),u.ac(109,"div",18),u.ac(110,"label",19),u.Lc(111,"Remarks"),u.Zb(),u.ac(112,"div",20),u.ac(113,"span"),u.Lc(114,": \xa0"),u.Zb(),u.ac(115,"span"),u.Lc(116),u.Zb(),u.Zb(),u.Zb(),u.ac(117,"div",18),u.ac(118,"label",19),u.Lc(119,"Approval Status"),u.Zb(),u.ac(120,"div",20),u.ac(121,"span"),u.Lc(122,": \xa0"),u.Zb(),u.ac(123,"span"),u.Lc(124),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(125,"div",12),u.ac(126,"div",21),u.ac(127,"fieldset",22),u.ac(128,"legend"),u.Lc(129,"Approval Status"),u.Zb(),u.ac(130,"table",23),u.ac(131,"thead"),u.ac(132,"tr"),u.ac(133,"th"),u.Lc(134,"S/L"),u.Zb(),u.ac(135,"th"),u.Lc(136,"Approval Step"),u.Zb(),u.ac(137,"th"),u.Lc(138,"Sign By"),u.Zb(),u.ac(139,"th"),u.Lc(140,"Action"),u.Zb(),u.ac(141,"th"),u.Lc(142,"Date"),u.Zb(),u.ac(143,"th"),u.Lc(144,"Remarks"),u.Zb(),u.Zb(),u.Zb(),u.ac(145,"tbody"),u.Jc(146,J,13,6,"tr",24),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(147,"div",25),u.ac(148,"form",26),u.hc("ngSubmit",function(){return t.tackAction()}),u.ac(149,"fieldset",22),u.ac(150,"legend"),u.Lc(151,"Take Action"),u.Zb(),u.ac(152,"label",27),u.Lc(153,"Status"),u.Zb(),u.ac(154,"div",28),u.Vb(155,"ng-select",29),u.Zb(),u.Vb(156,"br"),u.Vb(157,"br"),u.ac(158,"label",27),u.Lc(159,"Remarks"),u.Zb(),u.ac(160,"div",28),u.Vb(161,"textarea",30),u.Zb(),u.Vb(162,"div",31),u.ac(163,"div",32),u.ac(164,"button",33),u.Lc(165," Save "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(166,"fieldset",22),u.ac(167,"legend"),u.Lc(168,"System Log Information"),u.Zb(),u.ac(169,"div",34),u.ac(170,"label",35),u.Lc(171,"ID"),u.Zb(),u.ac(172,"div",36),u.ac(173,"span"),u.Lc(174),u.Zb(),u.Zb(),u.Zb(),u.ac(175,"div",34),u.ac(176,"label",35),u.Lc(177,"Creation Time"),u.Zb(),u.ac(178,"div",36),u.ac(179,"span"),u.Lc(180),u.Zb(),u.Zb(),u.Zb(),u.ac(181,"div",34),u.ac(182,"label",35),u.Lc(183,"Creation User"),u.Zb(),u.ac(184,"div",36),u.ac(185,"span"),u.Lc(186),u.Zb(),u.Zb(),u.Zb(),u.ac(187,"div",34),u.ac(188,"label",35),u.Lc(189,"Last Update Time"),u.Zb(),u.ac(190,"div",36),u.ac(191,"span"),u.Lc(192),u.Zb(),u.Zb(),u.Zb(),u.ac(193,"div",34),u.ac(194,"label",35),u.Lc(195,"Last Update User"),u.Zb(),u.ac(196,"div",36),u.ac(197,"span"),u.Lc(198),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(199,"div",37),u.ac(200,"a",38),u.Vb(201,"i",11),u.Lc(202," Close"),u.Zb(),u.Lc(203," \xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(204,"ngx-spinner",39),u.ac(205,"p",40),u.Lc(206," Processing... "),u.Zb(),u.Zb()),2&e&&(u.Ib(34),u.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),u.Ib(8),u.Nc("",t.myData.hrCrEmp.mobCode," "),u.Ib(8),u.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),u.Ib(8),u.Nc("",t.myData.hrCrEmpResponsible.mobCode," "),u.Ib(8),u.Mc(t.myData.tourType),u.Ib(8),u.Mc(u.mc(75,23,t.myData.startDate,"yyyy-MM-dd")),u.Ib(9),u.Mc(u.mc(84,26,t.myData.endDate,"yyyy-MM-dd")),u.Ib(9),u.Mc(t.myData.tourDays),u.Ib(8),u.Mc(t.myData.addressDuringTour),u.Ib(8),u.Mc(t.myData.reasonForTour),u.Ib(8),u.Mc(t.myData.remarks),u.Ib(8),u.Mc(t.myData.tourApprovalStatus),u.Ib(22),u.pc("ngForOf",t.listData),u.Ib(2),u.pc("formGroup",t.myForm),u.Ib(7),u.pc("items",t.listData2),u.Ib(19),u.Mc(t.myData.id),u.Ib(6),u.Mc(t.myData.createDate),u.Ib(6),u.Mc(t.myData.createdByHrCrEmp.user.username),u.Ib(6),u.Mc(t.myData.updateDateTime),u.Ib(6),u.Mc(t.myData.lastUpdateUser),u.Ib(6),u.pc("fullScreen",!1))},directives:[s.e,l.l,f.x,f.p,f.h,v.a,f.o,f.f,f.b,Z.a],pipes:[l.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),K),ee=((W=function(){function t(a,i,c,o,n,r,l,s){e(this,t),this.formBuilder=a,this.datePipe=i,this.login=c,this.route=o,this.router=n,this.onTourService=r,this.toastr=l,this.commonService=s,this.baseUrl=d.a.baseUrl,this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({hrCrEmp:[{},[f.w.required]],contactNo:["",[f.w.required]],hrCrEmpResponsible:[{},[f.w.required]],tourType:["",[f.w.required]],startDate:["",[f.w.required]],endDate:["",[f.w.required]],addressDuringTour:["",[f.w.required]],reasonForTour:["",[f.w.required]],remarks:[""]})}},{key:"myFormSubmit",value:function(){var e=this;if(this.myForm.invalid)this.toastr.info("Please insert valid data");else if(!this.checkSomeCondition()){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),a=this.baseUrl+"/onTourTnx/save",i={};i=t,console.log(i),i.startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.onTourService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.router.navigate(["/sefl-service/onTourHrAdmin"],{relativeTo:e.route})},function(e){console.log(e)})}}},{key:"checkSomeCondition",value:function(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}},{key:"contactFind",value:function(e){alert(e)}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.listData=e.configDDL.append?e.configDDL.listData.concat(t.objectList):t.objectList,e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}}]),t}()).\u0275fac=function(e){return new(e||W)(u.Ub(f.d),u.Ub(l.e),u.Ub(C.a),u.Ub(s.a),u.Ub(s.c),u.Ub(A),u.Ub(h.b),u.Ub(b.a))},W.\u0275cmp=u.Ob({type:W,selectors:[["app-create"]],decls:90,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["oninput","javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);","type","number","maxlength","11","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour (Hr Admin)"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour (Hr Admin)"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Create"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"form",16),u.hc("ngSubmit",function(){return t.myFormSubmit()}),u.ac(25,"div",17),u.ac(26,"label",18),u.Lc(27,"Employee "),u.Zb(),u.ac(28,"div",19),u.ac(29,"ng-select",20),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(30,"div",17),u.ac(31,"label",18),u.Lc(32,"Contact No"),u.Zb(),u.ac(33,"div",19),u.Vb(34,"input",21),u.Zb(),u.Zb(),u.ac(35,"div",17),u.ac(36,"label",18),u.Lc(37,"Responsible Employee "),u.Zb(),u.ac(38,"div",19),u.ac(39,"ng-select",22),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(40,"div",17),u.ac(41,"label",18),u.Lc(42,"Tour Type"),u.Zb(),u.ac(43,"div",19),u.ac(44,"select",23),u.ac(45,"option",24),u.Lc(46,"Select"),u.Zb(),u.ac(47,"option",25),u.Lc(48,"Local"),u.Zb(),u.ac(49,"option",26),u.Lc(50,"Global"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",17),u.ac(52,"label",18),u.Lc(53,"Start Date"),u.Zb(),u.ac(54,"div",19),u.ac(55,"div",27),u.Vb(56,"input",28),u.Zb(),u.Zb(),u.Zb(),u.ac(57,"div",17),u.ac(58,"label",18),u.Lc(59,"End Date"),u.Zb(),u.ac(60,"div",19),u.ac(61,"div",27),u.Vb(62,"input",29),u.Zb(),u.Zb(),u.Zb(),u.ac(63,"div",17),u.ac(64,"label",18),u.Lc(65,"Address During Tour"),u.Zb(),u.ac(66,"div",19),u.Vb(67,"textarea",30),u.Zb(),u.Zb(),u.ac(68,"div",17),u.ac(69,"label",18),u.Lc(70,"Reason For Tour"),u.Zb(),u.ac(71,"div",19),u.Vb(72,"textarea",31),u.Zb(),u.Zb(),u.ac(73,"div",17),u.ac(74,"label",18),u.Lc(75,"Remarks"),u.Zb(),u.ac(76,"div",19),u.Vb(77,"textarea",32),u.Zb(),u.Zb(),u.ac(78,"div",33),u.ac(79,"a",34),u.Vb(80,"i",11),u.Lc(81," Cancel"),u.Zb(),u.Lc(82," \xa0 \xa0 "),u.ac(83,"button",35),u.hc("click",function(){return t.resetFormValues()}),u.Vb(84,"i",36),u.Lc(85," Reset "),u.Zb(),u.Lc(86," \xa0 \xa0 "),u.ac(87,"button",37),u.Vb(88,"i",38),u.Lc(89," Save \xa0\xa0\xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(24),u.pc("formGroup",t.myForm),u.Ib(5),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),u.Ib(10),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[s.e,f.x,f.p,f.h,v.a,f.o,f.f,f.t,f.b,f.k,f.v,f.s,f.y,D.b,D.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),W),te=((Y=function(){function t(a,i,c,o,n,r,l,s){e(this,t),this.formBuilder=a,this.datePipe=i,this.route=c,this.router=o,this.onTourService=n,this.toastr=r,this.commonService=l,this.spinnerService=s,this.baseUrl=d.a.baseUrl,this.myFormData={},this._initConfigDDL(),this._customInitLoadData()}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],hrCrEmp:["",[f.w.required]],contactNo:["",[f.w.required]],hrCrEmpResponsible:["",[f.w.required]],tourType:["",[f.w.required]],startDate:["",[f.w.required]],endDate:["",[f.w.required]],addressDuringTour:["",[f.w.required]],reasonForTour:["",[f.w.required]],remarks:[""]})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(t,{}).subscribe(function(t){e.myFormData=t,console.log(e.myFormData),e.spinnerService.hide(),e.configDDL.listData=[{ddlCode:t.hrCrEmp.id,ddlDescription:t.hrCrEmp.loginCode+"-"+t.hrCrEmp.displayName}],e.myFormData.hrCrEmp=t.hrCrEmp.id,e.configDDL.listData2=[{ddlCode:t.hrCrEmpResponsible.id,ddlDescription:t.hrCrEmpResponsible.loginCode+"-"+t.hrCrEmpResponsible.displayName}],e.myFormData.hrCrEmpResponsible=t.hrCrEmpResponsible.id,e.myFormData.startDate=e.datePipe.transform(t.startDate,"MM-dd-yyyy").toString().slice(0,10),e.myFormData.endDate=e.datePipe.transform(t.endDate,"MM-dd-yyyy").toString().slice(0,10),e.myForm.patchValue(e.myFormData)},function(e){console.log(e)})}},{key:"saveUpdatedFormData",value:function(){var e=this;if(this.myForm.invalid)this.toastr.info("Please insert valid data");else if(!this.checkSomeCondition()){var t=Object.assign(this.myForm.value,{hrCrEmp:this.getHrCrEmp.value?{id:this.getHrCrEmp.value}:null,hrCrEmpResponsible:this.getHrCrResponsibleEmp.value?{id:this.getHrCrResponsibleEmp.value}:null}),a=this.baseUrl+"/onTourTnx/save";console.log(a);var i={};(i=t).startDate=i.startDate?this.datePipe.transform(i.startDate,"yyyy-MM-dd").toString().slice(0,10):null,i.endDate=i.endDate?this.datePipe.transform(i.endDate,"yyyy-MM-dd").toString().slice(0,10):null,this.spinnerService.show(),this.onTourService.sendPostRequest(a,i).subscribe(function(t){console.log(t),e.spinnerService.hide(),e.router.navigate(["/sefl-service/onTour"],{relativeTo:e.route})},function(t){console.log(t),e.toastr.warning(t.error.message),e.spinnerService.hide()})}}},{key:"checkSomeCondition",value:function(){return this.myForm.value.startDate>this.myForm.value.endDate&&(this.toastr.error("End Date must be equal or greater"),!0)}},{key:"resetFormValues",value:function(){this.getFormData()}},{key:"searchDDL",value:function(e){this.configDDL.q=e.term,this.configDDL.pageNum=1,this.configDDL.append=!1,this.getListDataDDL()}},{key:"scrollToEndDDL",value:function(){this.configDDL.pageNum++,this.configDDL.append=!0,this.getListDataDDL()}},{key:"_customInitLoadData",value:function(){this.configDDL.activeFieldName="ddlDescription",this.configDDL.dataGetApiPath="/api/common/getEmp",this.configDDL.apiQueryFieldName="hrCrEmp"}},{key:"clearDDL",value:function(){this.configDDL.q=""}},{key:"getListDataDDL",value:function(){var e=this,t=this.baseUrl+this.configDDL.dataGetApiPath,a={};a.pageNum=this.configDDL.pageNum,a.pageSize=this.configDDL.pageSize,this.configDDL.q&&null!=this.configDDL.q&&(a[this.configDDL.apiQueryFieldName]=this.configDDL.q),this.commonService.sendGetRequest(t,a).subscribe(function(t){e.configDDL.append?(e.configDDL.listData=e.configDDL.listData.concat(t.objectList),e.configDDL.listData2=e.configDDL.listData2.concat(t.objectList)):(e.configDDL.listData=t.objectList,e.configDDL.listData2=t.objectList),e.configDDL.totalItem=t.totalItems},function(e){console.log(e)})}},{key:"setDefaultParamsDDL",value:function(){this._initConfigDDL()}},{key:"_initConfigDDL",value:function(){this.configDDL={pageNum:1,pageSize:10,totalItem:50,listData:[],listData2:[],append:!1,q:"",activeFieldName:"xxxFieldName",dataGetApiPath:"",apiQueryFieldName:"xxxFieldName"}}},{key:"initSysParamsDDL",value:function(e,t,a,i){console.log("..."),console.log("ddlActiveFieldName:"+t),console.log("dataGetApiPathDDL:"+a),console.log(e.target),this.configDDL.activeFieldName&&this.configDDL.activeFieldName!=t&&this.setDefaultParamsDDL(),this.configDDL.activeFieldName=t,this.configDDL.dataGetApiPath=a,this.configDDL.apiQueryFieldName=i,this.getListDataDDL()}},{key:"getHrCrEmp",get:function(){return this.myForm.get("hrCrEmp")}},{key:"getHrCrResponsibleEmp",get:function(){return this.myForm.get("hrCrEmpResponsible")}}]),t}()).\u0275fac=function(e){return new(e||Y)(u.Ub(f.d),u.Ub(l.e),u.Ub(s.a),u.Ub(s.c),u.Ub(A),u.Ub(h.b),u.Ub(b.a),u.Ub(Z.c))},Y.\u0275cmp=u.Ob({type:Y,selectors:[["app-edit"]],decls:90,vars:11,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],["novalidate","",3,"formGroup","ngSubmit"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],["formControlName","hrCrEmp","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["type","number","formControlName","contactNo",1,"form-control"],["formControlName","hrCrEmpResponsible","placeholder","Select employee","bindLabel","ddlDescription","bindValue","ddlCode","ddlActiveFieldName","ddlDescription",1,"custom-ng-select",3,"items","searchable","clearable","virtualScroll","clearOnBackspace","search","scrollToEnd","clear","click"],["formControlName","tourType",1,"form-control"],["value",""],["value","Local"],["value","Global"],[1,"cal-icon"],["type","text","formControlName","startDate","bsDatepicker","",1,"form-control","datetimepicker"],["type","text","formControlName","endDate","bsDatepicker","",1,"form-control"],["type","text","formControlName","addressDuringTour",1,"form-control"],["type","text","formControlName","reasonForTour",1,"form-control"],["type","text","formControlName","remarks",1,"form-control"],[1,"text-right"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","btn-warning","btn-ripple"],["type","button",1,"btn","btn-secondary","btn-ripple",3,"click"],["aria-hidden","true",1,"fa","fa-undo"],["type","submit",1,"btn","btn-primary","btn-ripple"],["aria-hidden","true",1,"fa","fa-check"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour (Hr Admin)"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour (Hr Admin)"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Edit"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"form",16),u.hc("ngSubmit",function(){return t.saveUpdatedFormData()}),u.ac(25,"div",17),u.ac(26,"label",18),u.Lc(27,"Employee "),u.Zb(),u.ac(28,"div",19),u.ac(29,"ng-select",20),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(30,"div",17),u.ac(31,"label",18),u.Lc(32,"Contact No"),u.Zb(),u.ac(33,"div",19),u.Vb(34,"input",21),u.Zb(),u.Zb(),u.ac(35,"div",17),u.ac(36,"label",18),u.Lc(37,"Responsible Employee "),u.Zb(),u.ac(38,"div",19),u.ac(39,"ng-select",22),u.hc("search",function(e){return t.searchDDL(e)})("scrollToEnd",function(){return t.scrollToEndDDL()})("clear",function(){return t.clearDDL()})("click",function(e){return t.initSysParamsDDL(e,"ddlDescription","/api/common/getEmp","hrCrEmp")}),u.Zb(),u.Zb(),u.Zb(),u.ac(40,"div",17),u.ac(41,"label",18),u.Lc(42,"Tour Type"),u.Zb(),u.ac(43,"div",19),u.ac(44,"select",23),u.ac(45,"option",24),u.Lc(46,"Select"),u.Zb(),u.ac(47,"option",25),u.Lc(48,"Local"),u.Zb(),u.ac(49,"option",26),u.Lc(50,"Global"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",17),u.ac(52,"label",18),u.Lc(53,"Start Date"),u.Zb(),u.ac(54,"div",19),u.ac(55,"div",27),u.Vb(56,"input",28),u.Zb(),u.Zb(),u.Zb(),u.ac(57,"div",17),u.ac(58,"label",18),u.Lc(59,"End Date"),u.Zb(),u.ac(60,"div",19),u.ac(61,"div",27),u.Vb(62,"input",29),u.Zb(),u.Zb(),u.Zb(),u.ac(63,"div",17),u.ac(64,"label",18),u.Lc(65,"Address During Tour"),u.Zb(),u.ac(66,"div",19),u.Vb(67,"textarea",30),u.Zb(),u.Zb(),u.ac(68,"div",17),u.ac(69,"label",18),u.Lc(70,"Reason For Tour"),u.Zb(),u.ac(71,"div",19),u.Vb(72,"textarea",31),u.Zb(),u.Zb(),u.ac(73,"div",17),u.ac(74,"label",18),u.Lc(75,"Remarks"),u.Zb(),u.ac(76,"div",19),u.Vb(77,"textarea",32),u.Zb(),u.Zb(),u.ac(78,"div",33),u.ac(79,"a",34),u.Vb(80,"i",11),u.Lc(81," Cancel"),u.Zb(),u.Lc(82," \xa0 \xa0 "),u.ac(83,"button",35),u.hc("click",function(){return t.resetFormValues()}),u.Vb(84,"i",36),u.Lc(85," Reset "),u.Zb(),u.Lc(86," \xa0 \xa0 "),u.ac(87,"button",37),u.Vb(88,"i",38),u.Lc(89," Save \xa0\xa0\xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(24),u.pc("formGroup",t.myForm),u.Ib(5),u.pc("items",t.configDDL.listData)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0),u.Ib(10),u.pc("items",t.configDDL.listData2)("searchable",!0)("clearable",!0)("virtualScroll",!0)("clearOnBackspace",!0))},directives:[s.e,f.x,f.p,f.h,v.a,f.o,f.f,f.t,f.b,f.v,f.s,f.y,D.b,D.a],styles:[".content[_ngcontent-%COMP%]{padding:30px}input.form-control[_ngcontent-%COMP%], select.form-control[_ngcontent-%COMP%], textarea.form-control[_ngcontent-%COMP%]{border-color:#667eea;border-left:3px solid #667eea;box-shadow:none}.form-control[_ngcontent-%COMP%]:focus{border-color:#705cba;outline:0;box-shadow:inset 0 1px 1px rgb(0 0 0/8%),0 0 8px rgb(102 175 233/60%)}"]}),Y);function ae(e,t){if(1&e){var a=u.bc();u.ac(0,"tr"),u.ac(1,"td"),u.Lc(2),u.Zb(),u.ac(3,"td"),u.Lc(4),u.Zb(),u.ac(5,"td"),u.Lc(6),u.Zb(),u.ac(7,"td"),u.Lc(8),u.Zb(),u.ac(9,"td"),u.Lc(10),u.kc(11,"date"),u.Zb(),u.ac(12,"td"),u.Lc(13),u.kc(14,"date"),u.Zb(),u.ac(15,"td"),u.Lc(16),u.kc(17,"date"),u.Zb(),u.ac(18,"td"),u.ac(19,"span",56),u.Lc(20),u.Zb(),u.Zb(),u.ac(21,"td"),u.ac(22,"a",57),u.Lc(23,"View"),u.Zb(),u.Lc(24," \xa0 "),u.ac(25,"a",58),u.Vb(26,"i",59),u.Zb(),u.Lc(27,"\xa0\xa0 "),u.ac(28,"a",60),u.hc("click",function(){u.Cc(a);var e=t.$implicit;return u.jc().tempId=e.id}),u.Vb(29,"i",61),u.Zb(),u.Zb(),u.Zb()}if(2&e){var i=t.$implicit,c=t.index,o=u.jc();u.Mb("active",c==o.currentIndex),u.Ib(2),u.Mc((o.configPgn.pageNum-1)*o.configPgn.pageSize+(c+1)),u.Ib(2),u.Mc(i.hrCrEmp.displayName),u.Ib(2),u.Mc(i.tourType),u.Ib(2),u.Mc(i.tourDays),u.Ib(2),u.Mc(u.mc(11,12,i.createDate,"yyyy-MM-dd")),u.Ib(3),u.Mc(u.mc(14,15,i.startDate,"yyyy-MM-dd")),u.Ib(3),u.Mc(u.mc(17,18,i.endDate,"yyyy-MM-dd")),u.Ib(4),u.Mc(i.tourApprovalStatus),u.Ib(2),u.rc("routerLink","./view/",i.id,""),u.Ib(3),u.rc("routerLink","./edit/",i.id,"")}}function ie(e,t){1&e&&(u.ac(0,"tr"),u.ac(1,"td",62),u.ac(2,"h5",63),u.Lc(3,"No data found"),u.Zb(),u.Zb(),u.Zb())}function ce(e,t){if(1&e&&(u.ac(0,"option",64),u.Lc(1),u.Zb()),2&e){var a=t.$implicit;u.pc("value",a),u.Ib(1),u.Nc(" ",a," ")}}var oe,ne=function(){return{dateInputFormat:"DD-MM-YYYY",returnFocusToInput:!0}},re=((oe=function(){function t(a,i,c,o){e(this,t),this.onTourService=a,this.login=i,this.spinnerService=c,this.toastr=o,this.baseUrl=d.a.baseUrl,this.pipe=new l.e("en-US"),this.listData=[],this.configPgn={pageNum:1,pageSize:5,totalItem:50,pageSizes:[5,10,25,50,100,200,500,1e3],pgnDiplayLastSeq:10,itemsPerPage:5,currentPage:1,totalItems:50}}return a(t,[{key:"ngOnInit",value:function(){this.loginUser(),this.getSelfListData()}},{key:"loginUser",value:function(){this.user=this.login.getUser(),console.log(this.user)}},{key:"getSelfListData",value:function(){var e,t=this,a=this.baseUrl+"/onTourTnx/getAll";e=this.getUserQueryParams(this.configPgn.pageNum,this.configPgn.pageSize),this.spinnerService.show(),this.onTourService.sendGetSelfRequest(a,e).subscribe(function(e){t.listData=e.objectList,console.log(t.listData),t.configPgn.totalItem=e.totalItems,t.configPgn.totalItems=e.totalItems,t.setDisplayLastSequence(),t.spinnerService.hide()},function(e){console.log(e)})}},{key:"deleteEnityData",value:function(e){var t=this,a=this.baseUrl+"/onTourTnx/delete/"+e;console.log(a),this.spinnerService.show(),this.onTourService.sendDeleteRequest(a,{}).subscribe(function(e){console.log(e),t.spinnerService.hide(),$("#delete_entity").modal("hide"),t.toastr.success("Successfully item is deleted","Success"),t.getSelfListData()},function(e){$("#delete_entity").modal("hide"),t.toastr.warning(e.error.message),t.spinnerService.hide()})}},{key:"getUserQueryParams",value:function(e,t){var a={};return e&&(a.pageNum=e-0),t&&(a.pageSize=t),a}},{key:"setDisplayLastSequence",value:function(){this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize,this.listData.length<this.configPgn.pageSize&&(this.configPgn.pngDiplayLastSeq=(this.configPgn.pageNum-1)*this.configPgn.pageSize+this.configPgn.pageSize),this.configPgn.totalItem<this.configPgn.pngDiplayLastSeq&&(this.configPgn.pngDiplayLastSeq=this.configPgn.totalItem)}},{key:"handlePageChange",value:function(e){this.configPgn.pageNum=e,this.configPgn.currentPage=this.configPgn.pageNum,this.getSelfListData()}},{key:"handlePageSizeChange",value:function(e){this.configPgn.pageSize=e.target.value,this.configPgn.pageNum=1,this.configPgn.itemsPerPage=this.configPgn.pageSize,this.getSelfListData()}}]),t}()).\u0275fac=function(e){return new(e||oe)(u.Ub(A),u.Ub(C.a),u.Ub(Z.c),u.Ub(h.b))},oe.\u0275cmp=u.Ob({type:oe,selectors:[["app-on-tour"]],decls:114,vars:14,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],[1,"btn-group","btn-group"],[1,"btn","btn-white"],[1,"fa","fa-print","fa-lg"],[1,"card","mb-2",2,"background-color","transparent"],[1,"card-body","p-3"],[1,"row","filter-row"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-xxl-2","col-12"],[1,"form-group","form-focus"],["type","text",1,"form-control","floating"],[1,"focus-label"],[1,"col-sm-6","col-md-3","col-lg-3","col-xl-3","col-12"],[1,"cal-icon"],["bsDatepicker","","type","text",1,"form-control","floating","datetimepicker",3,"bsConfig"],[1,"btn","btn-success","btn-block"],[1,"row"],[1,"col-md-12"],[1,"card"],[1,"card-header"],[1,"card-tools"],["routerLink","/sefl-service/onTourHrAdmin/create",1,"btn","btn-outline-primary"],[1,"fa","fa-plus"],[1,"card-body"],[1,"table-responsive"],[1,"d-flex","justify-content-start","pb-1"],[1,"pgn-displayDataInfo"],[1,"page-item","disabled"],["id","genListTable",1,"table","table-striped","custom-table"],[3,"active",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end"],[1,""],[1,"pgn-pageSizeOption",3,"change"],[3,"value",4,"ngFor","ngForOf"],[1,"pgn-pageSliceCt"],["responsive","true","previousLabel","Prev","nextLabel","Next",3,"pageChange"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"],["id","delete_entity","role","dialog",1,"modal","custom-modal","fade"],[1,"modal-dialog","modal-dialog-centered"],[1,"modal-content"],[1,"modal-body"],[1,"form-header"],[1,"modal-btn","delete-action"],[1,"col-6"],[1,"btn","btn-primary","continue-btn",3,"click"],["data-dismiss","modal",1,"btn","btn-primary","cancel-btn"],[1,"badge","badge-success"],[1,"btn","btn-sm","btn-primary",3,"routerLink"],[1,"btn","btn-sm","btn-info",3,"routerLink"],[1,"fa","fa-pencil","m-r-5"],["data-toggle","modal","data-target","#delete_entity",1,"btn","btn-sm","btn-danger",3,"click"],[1,"fa","fa-trash-o","m-r-5"],["colspan","10"],[2,"text-align","center"],[3,"value"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour (Hr Admin)"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self Service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour (Hr Admin)"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"List"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"div",10),u.ac(18,"button",11),u.Lc(19,"Excel"),u.Zb(),u.ac(20,"button",11),u.Lc(21,"PDF"),u.Zb(),u.ac(22,"button",11),u.Vb(23,"i",12),u.Lc(24," Print"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(25,"div",13),u.ac(26,"div",14),u.ac(27,"div",15),u.ac(28,"div",16),u.ac(29,"div",17),u.Vb(30,"input",18),u.ac(31,"label",19),u.Lc(32,"Employee Code"),u.Zb(),u.Zb(),u.Zb(),u.ac(33,"div",20),u.ac(34,"div",17),u.ac(35,"div",21),u.Vb(36,"input",22),u.Zb(),u.ac(37,"label",19),u.Lc(38,"From"),u.Zb(),u.Zb(),u.Zb(),u.ac(39,"div",20),u.ac(40,"div",17),u.ac(41,"div",21),u.Vb(42,"input",22),u.Zb(),u.ac(43,"label",19),u.Lc(44,"To"),u.Zb(),u.Zb(),u.Zb(),u.ac(45,"div",20),u.ac(46,"a",23),u.Lc(47," Search "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(48,"div",24),u.ac(49,"div",25),u.ac(50,"div",26),u.ac(51,"div",27),u.ac(52,"div",28),u.ac(53,"a",29),u.Vb(54,"i",30),u.Lc(55," New \xa0\xa0\xa0"),u.Zb(),u.Zb(),u.Zb(),u.ac(56,"div",31),u.ac(57,"div",32),u.ac(58,"div",33),u.ac(59,"div",34),u.ac(60,"span",35),u.Lc(61),u.Zb(),u.Zb(),u.Zb(),u.ac(62,"table",36),u.ac(63,"thead"),u.ac(64,"tr"),u.ac(65,"th"),u.Lc(66,"SL"),u.Zb(),u.ac(67,"th"),u.Lc(68,"Emp"),u.Zb(),u.ac(69,"th"),u.Lc(70,"Tour Type"),u.Zb(),u.ac(71,"th"),u.Lc(72,"Total Days"),u.Zb(),u.ac(73,"th"),u.Lc(74,"Apply Date"),u.Zb(),u.ac(75,"th"),u.Lc(76,"From Date"),u.Zb(),u.ac(77,"th"),u.Lc(78,"To Date"),u.Zb(),u.ac(79,"th"),u.Lc(80,"Approval Status"),u.Zb(),u.ac(81,"th"),u.Lc(82,"Action"),u.Zb(),u.Zb(),u.Zb(),u.ac(83,"tbody"),u.Jc(84,ae,30,21,"tr",37),u.kc(85,"paginate"),u.Jc(86,ie,4,0,"tr",38),u.Zb(),u.Zb(),u.ac(87,"div",39),u.ac(88,"div",40),u.Lc(89," Items per Page "),u.ac(90,"select",41),u.hc("change",function(e){return t.handlePageSizeChange(e)}),u.Jc(91,ce,2,2,"option",42),u.Zb(),u.Zb(),u.ac(92,"div",43),u.ac(93,"pagination-controls",44),u.hc("pageChange",function(e){return t.handlePageChange(e)}),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(94,"ngx-spinner",45),u.ac(95,"p",46),u.Lc(96," Processing... "),u.Zb(),u.Zb(),u.ac(97,"div",47),u.ac(98,"div",48),u.ac(99,"div",49),u.ac(100,"div",50),u.ac(101,"div",51),u.ac(102,"h3"),u.Lc(103,"Delete Item"),u.Zb(),u.ac(104,"p"),u.Lc(105,"Are you sure want to delete?"),u.Zb(),u.Zb(),u.ac(106,"div",52),u.ac(107,"div",24),u.ac(108,"div",53),u.ac(109,"a",54),u.hc("click",function(){return t.deleteEnityData(t.tempId)}),u.Lc(110,"Delete"),u.Zb(),u.Zb(),u.ac(111,"div",53),u.ac(112,"a",55),u.Lc(113,"Cancel"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb()),2&e&&(u.Ib(36),u.pc("bsConfig",u.sc(12,ne)),u.Ib(6),u.pc("bsConfig",u.sc(13,ne)),u.Ib(19),u.Pc("Displaying ( ",(t.configPgn.pageNum-1)*t.configPgn.pageSize+1," to ",t.configPgn.pngDiplayLastSeq," of ",t.configPgn.totalItem," ) entries"),u.Ib(23),u.pc("ngForOf",u.mc(85,9,t.listData,t.configPgn)),u.Ib(2),u.pc("ngIf",0===t.listData.length),u.Ib(5),u.pc("ngForOf",t.configPgn.pageSizes),u.Ib(3),u.pc("fullScreen",!1))},directives:[s.e,D.b,D.a,l.l,l.m,k.c,Z.a,f.s,f.y],pipes:[k.b,l.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}"]}),oe);function le(e,t){if(1&e&&(u.ac(0,"tr"),u.ac(1,"td"),u.Lc(2),u.Zb(),u.ac(3,"td"),u.Lc(4),u.Zb(),u.ac(5,"td"),u.Lc(6),u.Zb(),u.ac(7,"td"),u.Lc(8),u.Zb(),u.ac(9,"td"),u.Lc(10),u.Zb(),u.ac(11,"td"),u.Lc(12),u.Zb(),u.Zb()),2&e){var a=t.$implicit,i=t.index;u.Ib(2),u.Nc(" ",1+i," "),u.Ib(2),u.Mc(a.approvalStep.approvalGroupName?a.approvalStep.approvalGroupName:"null"),u.Ib(2),u.Mc(a.approvalStepApproverEmp?a.approvalStepApproverEmp.displayName:"null"),u.Ib(2),u.Mc(a.actionStatus?a.actionStatus:"null"),u.Ib(2),u.Mc(a.updateDateTime?a.updateDateTime:"null"),u.Ib(2),u.Mc(a.remarks?a.remarks:"null")}}var se,be,de,pe,ue=((se=function(){function t(a,i,c,o,n,r,l){e(this,t),this.route=a,this.spinnerService=i,this.onTourService=c,this.approvalService=o,this.toastr=n,this.formBuilder=r,this.router=l,this.baseUrl=d.a.baseUrl,this.myData={},this.listData=[],this.listData2=[]}return a(t,[{key:"ngOnInit",value:function(){this.initializeForm(),this.getFormData(),this.getSelfListData(),this.getApprovalStepAction()}},{key:"initializeForm",value:function(){this.myForm=this.formBuilder.group({id:[""],approvalStepAction:["",f.w.required],remarks:["",f.w.required]})}},{key:"getFormData",value:function(){var e=this,t=this.baseUrl+"/onTourTnx/get/"+this.route.snapshot.params.id;this.spinnerService.show(),this.onTourService.sendGetRequest(t,{}).subscribe(function(t){e.myData=t,console.log(e.myData),e.spinnerService.hide()},function(e){console.log(e)})}},{key:"getSelfListData",value:function(){var e,t=this,a=this.baseUrl+"/approvalProcTnxHtry/getSelfApprovalProcTnxList/"+this.route.snapshot.params.id;e=this.getUserQueryParams(),this.approvalService.sendGetRequest(a,e).subscribe(function(e){t.listData=e},function(e){console.log(e)})}},{key:"getApprovalStepAction",value:function(){var e,t=this,a=this.baseUrl+"/approvalStepAction/getApprovalStepAction/"+this.route.snapshot.params.id;e=this.getUserQueryParams(),this.approvalService.sendGetRequest(a,e).subscribe(function(e){t.listData2=e},function(e){console.log(e)})}},{key:"tackAction",value:function(){var e=this;if(!this.myForm.invalid){var t,a=Object.assign(this.myForm.value,{referenceId:this.route.snapshot.params.id,referenceEntity:"ONTOUR_PROCESS",approvalStepAction:this.get.value?{id:this.get.value}:null});console.log(a),t=a,this.approvalService.sendPutRequest(this.baseUrl+"/approvalProcTnxHtry/edit",t).subscribe(function(t){console.log(t),e.getFormData(),e.getSelfListData(),e.getApprovalStepAction(),e.resetFormValues()},function(t){console.log(t),e.toastr.info(t.error.message)})}}},{key:"resetFormValues",value:function(){this.myForm.reset()}},{key:"getUserQueryParams",value:function(){return{approvalProcess:"ONTOUR_PROCESS"}}},{key:"get",get:function(){return this.myForm.get("approvalStepAction")}}]),t}()).\u0275fac=function(e){return new(e||se)(u.Ub(s.a),u.Ub(Z.c),u.Ub(A),u.Ub(Q.a),u.Ub(h.b),u.Ub(f.d),u.Ub(s.c))},se.\u0275cmp=u.Ob({type:se,selectors:[["app-view"]],decls:207,vars:29,consts:[[1,"content","container-fluid"],[1,"page-header"],[1,"row","align-items-center"],[1,"col"],[1,"page-title"],[1,"breadcrumb"],[1,"breadcrumb-item"],["routerLink","/dashboard"],[1,"breadcrumb-item","active"],[1,"col-auto","float-right","ml-auto"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","add-btn"],[1,"fa","fa-share"],[1,"row"],[1,"col-lg-12"],[1,"card"],[1,"card-body"],[1,"row","fieldsetWithoutBorder"],[1,"col-12"],[1,"form-group","row"],[1,"col-form-label","col-md-2"],[1,"col-md-10"],[1,"col-8"],[1,"row","fieldsetBorder","logBox"],[1,"table"],[4,"ngFor","ngForOf"],[1,"col-4"],["novalidate","",3,"formGroup","ngSubmit"],[1,"col-form-label","col-md-4"],[1,"col-md-8"],["formControlName","approvalStepAction","bindLabel","activityStatusTitle","bindValue","id","placeholder","Select","appendTo","body",3,"items"],["formControlName","remarks",1,"form-control","mb-3"],[1,"col-md-9"],[1,"col-md-3"],["type","submit",1,"btn","btn-secondary","btn-sm","mb-2"],[1,"form-group"],[1,"col-form-label"],[1,""],[1,"text-right"],["routerLink","/sefl-service/onTourHrAdmin",1,"btn","btn-primary","btn-ripple"],["bdColor","rgba(255,255,255,0.5)","size","medium","color","#667eea","type","ball-clip-rotate",3,"fullScreen"],[2,"color","black"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.ac(1,"div",1),u.ac(2,"div",2),u.ac(3,"div",3),u.ac(4,"h3",4),u.Lc(5,"On Tour (Hr Admin)"),u.Zb(),u.ac(6,"ul",5),u.ac(7,"li",6),u.ac(8,"a",7),u.Lc(9,"Home"),u.Zb(),u.Zb(),u.ac(10,"li",8),u.Lc(11,"Self-service"),u.Zb(),u.ac(12,"li",8),u.Lc(13,"On Tour (Hr Admin)"),u.Zb(),u.ac(14,"li",8),u.Lc(15,"Show"),u.Zb(),u.Zb(),u.Zb(),u.ac(16,"div",9),u.ac(17,"a",10),u.Vb(18,"i",11),u.Lc(19," Back To List"),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(20,"div",12),u.ac(21,"div",13),u.ac(22,"div",14),u.ac(23,"div",15),u.ac(24,"fieldset",16),u.Vb(25,"legend"),u.ac(26,"div",17),u.ac(27,"div",18),u.ac(28,"label",19),u.Lc(29,"Employee"),u.Zb(),u.ac(30,"div",20),u.ac(31,"span"),u.Lc(32,": \xa0"),u.Zb(),u.ac(33,"span"),u.Lc(34),u.Zb(),u.Zb(),u.Zb(),u.ac(35,"div",18),u.ac(36,"label",19),u.Lc(37,"Mobile No"),u.Zb(),u.ac(38,"div",20),u.ac(39,"span"),u.Lc(40,": \xa0"),u.Zb(),u.ac(41,"span"),u.Lc(42),u.Zb(),u.Zb(),u.Zb(),u.ac(43,"div",18),u.ac(44,"label",19),u.Lc(45,"Responsible Employee"),u.Zb(),u.ac(46,"div",20),u.ac(47,"span"),u.Lc(48,": \xa0"),u.Zb(),u.ac(49,"span"),u.Lc(50),u.Zb(),u.Zb(),u.Zb(),u.ac(51,"div",18),u.ac(52,"label",19),u.Lc(53,"Mobile No"),u.Zb(),u.ac(54,"div",20),u.ac(55,"span"),u.Lc(56,": \xa0"),u.Zb(),u.ac(57,"span"),u.Lc(58),u.Zb(),u.Zb(),u.Zb(),u.ac(59,"div",18),u.ac(60,"label",19),u.Lc(61,"Tour Type"),u.Zb(),u.ac(62,"div",20),u.ac(63,"span"),u.Lc(64,": \xa0"),u.Zb(),u.ac(65,"span"),u.Lc(66),u.Zb(),u.Zb(),u.Zb(),u.ac(67,"div",18),u.ac(68,"label",19),u.Lc(69,"Start Date"),u.Zb(),u.ac(70,"div",20),u.ac(71,"span"),u.Lc(72,": \xa0"),u.Zb(),u.ac(73,"span"),u.Lc(74),u.kc(75,"date"),u.Zb(),u.Zb(),u.Zb(),u.ac(76,"div",18),u.ac(77,"label",19),u.Lc(78,"End Date"),u.Zb(),u.ac(79,"div",20),u.ac(80,"span"),u.Lc(81,": \xa0"),u.Zb(),u.ac(82,"span"),u.Lc(83),u.kc(84,"date"),u.Zb(),u.Zb(),u.Zb(),u.ac(85,"div",18),u.ac(86,"label",19),u.Lc(87,"Total Days"),u.Zb(),u.ac(88,"div",20),u.ac(89,"span"),u.Lc(90,": \xa0"),u.Zb(),u.ac(91,"span"),u.Lc(92),u.Zb(),u.Zb(),u.Zb(),u.ac(93,"div",18),u.ac(94,"label",19),u.Lc(95,"Tour Location"),u.Zb(),u.ac(96,"div",20),u.ac(97,"span"),u.Lc(98,": \xa0"),u.Zb(),u.ac(99,"span"),u.Lc(100),u.Zb(),u.Zb(),u.Zb(),u.ac(101,"div",18),u.ac(102,"label",19),u.Lc(103,"Couse Of Tour"),u.Zb(),u.ac(104,"div",20),u.ac(105,"span"),u.Lc(106,": \xa0"),u.Zb(),u.ac(107,"span"),u.Lc(108),u.Zb(),u.Zb(),u.Zb(),u.ac(109,"div",18),u.ac(110,"label",19),u.Lc(111,"Remarks"),u.Zb(),u.ac(112,"div",20),u.ac(113,"span"),u.Lc(114,": \xa0"),u.Zb(),u.ac(115,"span"),u.Lc(116),u.Zb(),u.Zb(),u.Zb(),u.ac(117,"div",18),u.ac(118,"label",19),u.Lc(119,"Approval Status"),u.Zb(),u.ac(120,"div",20),u.ac(121,"span"),u.Lc(122,": \xa0"),u.Zb(),u.ac(123,"span"),u.Lc(124),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(125,"div",12),u.ac(126,"div",21),u.ac(127,"fieldset",22),u.ac(128,"legend"),u.Lc(129,"Approval Status"),u.Zb(),u.ac(130,"table",23),u.ac(131,"thead"),u.ac(132,"tr"),u.ac(133,"th"),u.Lc(134,"S/L"),u.Zb(),u.ac(135,"th"),u.Lc(136,"Approval Step"),u.Zb(),u.ac(137,"th"),u.Lc(138,"Sign By"),u.Zb(),u.ac(139,"th"),u.Lc(140,"Action"),u.Zb(),u.ac(141,"th"),u.Lc(142,"Date"),u.Zb(),u.ac(143,"th"),u.Lc(144,"Remarks"),u.Zb(),u.Zb(),u.Zb(),u.ac(145,"tbody"),u.Jc(146,le,13,6,"tr",24),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(147,"div",25),u.ac(148,"form",26),u.hc("ngSubmit",function(){return t.tackAction()}),u.ac(149,"fieldset",22),u.ac(150,"legend"),u.Lc(151,"Take Action"),u.Zb(),u.ac(152,"label",27),u.Lc(153,"Status"),u.Zb(),u.ac(154,"div",28),u.Vb(155,"ng-select",29),u.Zb(),u.Vb(156,"br"),u.Vb(157,"br"),u.ac(158,"label",27),u.Lc(159,"Remarks"),u.Zb(),u.ac(160,"div",28),u.Vb(161,"textarea",30),u.Zb(),u.Vb(162,"div",31),u.ac(163,"div",32),u.ac(164,"button",33),u.Lc(165," Save "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(166,"fieldset",22),u.ac(167,"legend"),u.Lc(168,"System Log Information"),u.Zb(),u.ac(169,"div",34),u.ac(170,"label",35),u.Lc(171,"ID"),u.Zb(),u.ac(172,"div",36),u.ac(173,"span"),u.Lc(174),u.Zb(),u.Zb(),u.Zb(),u.ac(175,"div",34),u.ac(176,"label",35),u.Lc(177,"Creation Time"),u.Zb(),u.ac(178,"div",36),u.ac(179,"span"),u.Lc(180),u.Zb(),u.Zb(),u.Zb(),u.ac(181,"div",34),u.ac(182,"label",35),u.Lc(183,"Creation User"),u.Zb(),u.ac(184,"div",36),u.ac(185,"span"),u.Lc(186),u.Zb(),u.Zb(),u.Zb(),u.ac(187,"div",34),u.ac(188,"label",35),u.Lc(189,"Last Update Time"),u.Zb(),u.ac(190,"div",36),u.ac(191,"span"),u.Lc(192),u.Zb(),u.Zb(),u.Zb(),u.ac(193,"div",34),u.ac(194,"label",35),u.Lc(195,"Last Update User"),u.Zb(),u.ac(196,"div",36),u.ac(197,"span"),u.Lc(198),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(199,"div",37),u.ac(200,"a",38),u.Vb(201,"i",11),u.Lc(202," Close"),u.Zb(),u.Lc(203," \xa0 "),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.Zb(),u.ac(204,"ngx-spinner",39),u.ac(205,"p",40),u.Lc(206," Processing... "),u.Zb(),u.Zb()),2&e&&(u.Ib(34),u.Oc("",t.myData.hrCrEmp.displayName," (Code:",t.myData.hrCrEmp.loginCode,") "),u.Ib(8),u.Nc("",t.myData.hrCrEmp.mobCode," "),u.Ib(8),u.Oc("",t.myData.hrCrEmpResponsible.displayName," (Code:",t.myData.hrCrEmpResponsible.loginCode,") "),u.Ib(8),u.Nc("",t.myData.hrCrEmpResponsible.mobCode," "),u.Ib(8),u.Mc(t.myData.tourType),u.Ib(8),u.Mc(u.mc(75,23,t.myData.startDate,"yyyy-MM-dd")),u.Ib(9),u.Mc(u.mc(84,26,t.myData.endDate,"yyyy-MM-dd")),u.Ib(9),u.Mc(t.myData.tourDays),u.Ib(8),u.Mc(t.myData.addressDuringTour),u.Ib(8),u.Mc(t.myData.reasonForTour),u.Ib(8),u.Mc(t.myData.remarks),u.Ib(8),u.Mc(t.myData.tourApprovalStatus),u.Ib(22),u.pc("ngForOf",t.listData),u.Ib(2),u.pc("formGroup",t.myForm),u.Ib(7),u.pc("items",t.listData2),u.Ib(19),u.Mc(t.myData.id),u.Ib(6),u.Mc(t.myData.createDate),u.Ib(6),u.Mc(t.myData.createdByHrCrEmp.user.username),u.Ib(6),u.Mc(t.myData.updateDateTime),u.Ib(6),u.Mc(t.myData.lastUpdateUser),u.Ib(6),u.pc("fullScreen",!1))},directives:[s.e,l.l,f.x,f.p,f.h,v.a,f.o,f.f,f.b,Z.a],pipes:[l.e],styles:[".content[_ngcontent-%COMP%]{padding:30px}fieldset.fieldsetBorder[_ngcontent-%COMP%]{border:1px solid rgba(31,31,31,.25);border-radius:5px;margin:1px 1px 7px;padding-left:5px}fieldset.fieldsetWithoutBorder[_ngcontent-%COMP%]{margin-bottom:7px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{width:auto;border-radius:5px;font-size:15px;padding-left:5px;padding-right:5px;margin-left:7px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{float:left}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{min-width:170px;margin-right:5px;margin-left:5px}.logBox[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-radius:3px;border:1px solid rgba(31,31,31,.25);padding:1px 3px;margin-right:5px;margin-left:5px;min-height:25px}.logBox[_ngcontent-%COMP%]{font-size:13px}"]}),se),me=function(e){return{height:e}},ge=[{path:"",component:(be=function(){function t(a){var i=this;e(this,t),this.ngZone=a,window.onresize=function(e){i.ngZone.run(function(){i.innerHeight=window.innerHeight+"px"})},this.getScreenHeight()}return a(t,[{key:"getScreenHeight",value:function(){this.innerHeight=window.innerHeight+"px"}},{key:"ngOnInit",value:function(){}},{key:"onResize",value:function(e){this.innerHeight=e.target.innerHeight+"px"}}]),t}(),be.\u0275fac=function(e){return new(e||be)(u.Ub(u.G))},be.\u0275cmp=u.Ob({type:be,selectors:[["app-self-service"]],decls:2,vars:3,consts:[[1,"page-wrapper",3,"ngStyle","resized"]],template:function(e,t){1&e&&(u.ac(0,"div",0),u.hc("resized",function(e){return t.onResize(e)}),u.Vb(1,"router-outlet"),u.Zb()),2&e&&u.pc("ngStyle",u.tc(1,me,t.innerHeight))},directives:[l.n,s.g],styles:[""]}),be),children:[{path:"onTour",component:j},{path:"create",component:q},{path:"onTour/view/:id",component:X},{path:"onTour/edit/:id",component:V},{path:"employeeleaves",component:U},{path:"employeeleaves/create",component:L},{path:"employeeleaves/edit/:id",component:y},{path:"employeeleaves/view/:id",component:R},{path:"onTourHrAdmin",component:re},{path:"onTourHrAdmin/create",component:ee},{path:"onTourHrAdmin/view/:id",component:ue},{path:"onTourHrAdmin/edit/:id",component:te}]}],fe=((de=function t(){e(this,t)}).\u0275fac=function(e){return new(e||de)},de.\u0275mod=u.Sb({type:de}),de.\u0275inj=u.Rb({imports:[[s.f.forChild(ge)],s.f]}),de),he=c("iHf9"),ve=c("0jEk"),De=((pe=function t(){e(this,t)}).\u0275fac=function(e){return new(e||pe)},pe.\u0275mod=u.Sb({type:pe}),pe.\u0275inj=u.Rb({imports:[[l.c,fe,D.c.forRoot(),ve.a,f.u,he.b,k.a,Z.b,v.b]]}),pe)},rmPI:function(e,t,a){"use strict";t.a="http://localhost:9090/hrms_api"}}])}();