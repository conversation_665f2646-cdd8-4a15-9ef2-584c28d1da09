!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";var n=[],r=Object.getPrototypeOf,i=n.slice,o=n.flat?function(t){return n.flat.call(t)}:function(t){return n.concat.apply([],t)},a=n.push,s=n.indexOf,l={},u=l.toString,c=l.hasOwnProperty,h=c.toString,f=h.call(Object),d={},p=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType},g=function(t){return null!=t&&t===t.window},m=t.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(t,e,n){var r,i,o=(n=n||m).createElement("script");if(o.text=t,e)for(r in v)(i=e[r]||e.getAttribute&&e.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?l[u.call(t)]||"object":typeof t}var x="3.5.1",_=function(t,e){return new _.fn.init(t,e)};function w(t){var e=!!t&&"length"in t&&t.length,n=b(t);return!p(t)&&!g(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}_.fn=_.prototype={jquery:x,constructor:_,length:0,toArray:function(){return i.call(this)},get:function(t){return null==t?i.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=_.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return _.each(this,t)},map:function(t){return this.pushStack(_.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(i.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(_.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(_.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(0<=n&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:n.sort,splice:n.splice},_.extend=_.fn.extend=function(){var t,e,n,r,i,o,a=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[s]||{},s++),"object"==typeof a||p(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(t=arguments[s]))for(e in t)r=t[e],"__proto__"!==e&&a!==r&&(u&&r&&(_.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[e],o=i&&!Array.isArray(n)?[]:i||_.isPlainObject(n)?n:{},i=!1,a[e]=_.extend(u,o,r)):void 0!==r&&(a[e]=r));return a},_.extend({expando:"jQuery"+(x+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==u.call(t)||(e=r(t))&&("function"!=typeof(n=c.call(e,"constructor")&&e.constructor)||h.call(n)!==f))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){y(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,r=0;if(w(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},makeArray:function(t,e){var n=e||[];return null!=t&&(w(Object(t))?_.merge(n,"string"==typeof t?[t]:t):a.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:s.call(e,t,n)},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r=[],i=0,o=t.length,a=!n;i<o;i++)!e(t[i],i)!==a&&r.push(t[i]);return r},map:function(t,e,n){var r,i,a=0,s=[];if(w(t))for(r=t.length;a<r;a++)null!=(i=e(t[a],a,n))&&s.push(i);else for(a in t)null!=(i=e(t[a],a,n))&&s.push(i);return o(s)},guid:1,support:d}),"function"==typeof Symbol&&(_.fn[Symbol.iterator]=n[Symbol.iterator]),_.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){l["[object "+e+"]"]=e.toLowerCase()});var S=function(t){var e,n,r,i,o,a,s,l,u,c,h,f,d,p,g,m,v,y,b,x="sizzle"+1*new Date,_=t.document,w=0,S=0,C=lt(),T=lt(),D=lt(),k=lt(),E=function(t,e){return t===e&&(h=!0),0},A={}.hasOwnProperty,L=[],I=L.pop,N=L.push,F=L.push,j=L.slice,P=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},M="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",R="[\\x20\\t\\r\\n\\f]",O="(?:\\\\[\\da-fA-F]{1,6}"+R+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",B="\\["+R+"*("+O+")(?:"+R+"*([*^$|!~]?=)"+R+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+O+"))|)"+R+"*\\]",H=":("+O+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+B+")*)|.*)\\)|)",q=new RegExp(R+"+","g"),W=new RegExp("^"+R+"+|((?:^|[^\\\\])(?:\\\\.)*)"+R+"+$","g"),z=new RegExp("^"+R+"*,"+R+"*"),U=new RegExp("^"+R+"*([>+~]|"+R+")"+R+"*"),V=new RegExp(R+"|>"),X=new RegExp(H),Y=new RegExp("^"+O+"$"),G={ID:new RegExp("^#("+O+")"),CLASS:new RegExp("^\\.("+O+")"),TAG:new RegExp("^("+O+"|[*])"),ATTR:new RegExp("^"+B),PSEUDO:new RegExp("^"+H),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+R+"*(even|odd|(([+-]|)(\\d*)n|)"+R+"*(?:([+-]|)"+R+"*(\\d+)|))"+R+"*\\)|)","i"),bool:new RegExp("^(?:"+M+")$","i"),needsContext:new RegExp("^"+R+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+R+"*((?:-\\d)?\\d*)"+R+"*\\)|)(?=[^-]|$)","i")},$=/HTML$/i,Q=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=new RegExp("\\\\[\\da-fA-F]{1,6}"+R+"?|\\\\([^\\r\\n\\f])","g"),nt=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},rt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,it=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},ot=function(){f()},at=xt(function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{F.apply(L=j.call(_.childNodes),_.childNodes)}catch(e){F={apply:L.length?function(t,e){N.apply(t,j.call(e))}:function(t,e){for(var n=t.length,r=0;t[n++]=e[r++];);t.length=n-1}}}function st(t,e,r,i){var o,s,u,c,h,p,v,y=e&&e.ownerDocument,_=e?e.nodeType:9;if(r=r||[],"string"!=typeof t||!t||1!==_&&9!==_&&11!==_)return r;if(!i&&(f(e),e=e||d,g)){if(11!==_&&(h=Z.exec(t)))if(o=h[1]){if(9===_){if(!(u=e.getElementById(o)))return r;if(u.id===o)return r.push(u),r}else if(y&&(u=y.getElementById(o))&&b(e,u)&&u.id===o)return r.push(u),r}else{if(h[2])return F.apply(r,e.getElementsByTagName(t)),r;if((o=h[3])&&n.getElementsByClassName&&e.getElementsByClassName)return F.apply(r,e.getElementsByClassName(o)),r}if(n.qsa&&!k[t+" "]&&(!m||!m.test(t))&&(1!==_||"object"!==e.nodeName.toLowerCase())){if(v=t,y=e,1===_&&(V.test(t)||U.test(t))){for((y=tt.test(t)&&vt(e.parentNode)||e)===e&&n.scope||((c=e.getAttribute("id"))?c=c.replace(rt,it):e.setAttribute("id",c=x)),s=(p=a(t)).length;s--;)p[s]=(c?"#"+c:":scope")+" "+bt(p[s]);v=p.join(",")}try{return F.apply(r,y.querySelectorAll(v)),r}catch(e){k(t,!0)}finally{c===x&&e.removeAttribute("id")}}}return l(t.replace(W,"$1"),e,r,i)}function lt(){var t=[];return function e(n,i){return t.push(n+" ")>r.cacheLength&&delete e[t.shift()],e[n+" "]=i}}function ut(t){return t[x]=!0,t}function ct(t){var e=d.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function ht(t,e){for(var n=t.split("|"),i=n.length;i--;)r.attrHandle[n[i]]=e}function ft(t,e){var n=e&&t,r=n&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function dt(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function pt(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}function gt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&at(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function mt(t){return ut(function(e){return e=+e,ut(function(n,r){for(var i,o=t([],n.length,e),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function vt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in n=st.support={},o=st.isXML=function(t){var e=(t.ownerDocument||t).documentElement;return!$.test(t.namespaceURI||e&&e.nodeName||"HTML")},f=st.setDocument=function(t){var e,i,a=t?t.ownerDocument||t:_;return a!=d&&9===a.nodeType&&a.documentElement&&(p=(d=a).documentElement,g=!o(d),_!=d&&(i=d.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",ot,!1):i.attachEvent&&i.attachEvent("onunload",ot)),n.scope=ct(function(t){return p.appendChild(t).appendChild(d.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length}),n.attributes=ct(function(t){return t.className="i",!t.getAttribute("className")}),n.getElementsByTagName=ct(function(t){return t.appendChild(d.createComment("")),!t.getElementsByTagName("*").length}),n.getElementsByClassName=K.test(d.getElementsByClassName),n.getById=ct(function(t){return p.appendChild(t).id=x,!d.getElementsByName||!d.getElementsByName(x).length}),n.getById?(r.filter.ID=function(t){var e=t.replace(et,nt);return function(t){return t.getAttribute("id")===e}},r.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n=e.getElementById(t);return n?[n]:[]}}):(r.filter.ID=function(t){var e=t.replace(et,nt);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},r.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var n,r,i,o=e.getElementById(t);if(o){if((n=o.getAttributeNode("id"))&&n.value===t)return[o];for(i=e.getElementsByName(t),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===t)return[o]}return[]}}),r.find.TAG=n.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):n.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,r=[],i=0,o=e.getElementsByTagName(t);if("*"===t){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},r.find.CLASS=n.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&g)return e.getElementsByClassName(t)},v=[],m=[],(n.qsa=K.test(d.querySelectorAll))&&(ct(function(t){var e;p.appendChild(t).innerHTML="<a id='"+x+"'></a><select id='"+x+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+R+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||m.push("\\["+R+"*(?:value|"+M+")"),t.querySelectorAll("[id~="+x+"-]").length||m.push("~="),(e=d.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||m.push("\\["+R+"*name"+R+"*="+R+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||m.push(":checked"),t.querySelectorAll("a#"+x+"+*").length||m.push(".#.+[+~]"),t.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ct(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=d.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&m.push("name"+R+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),p.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),m.push(",.*:")})),(n.matchesSelector=K.test(y=p.matches||p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ct(function(t){n.disconnectedMatch=y.call(t,"*"),y.call(t,"[s!='']:x"),v.push("!=",H)}),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),e=K.test(p.compareDocumentPosition),b=e||K.test(p.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,r=e&&e.parentNode;return t===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):t.compareDocumentPosition&&16&t.compareDocumentPosition(r)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},E=e?function(t,e){if(t===e)return h=!0,0;var r=!t.compareDocumentPosition-!e.compareDocumentPosition;return r||(1&(r=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!n.sortDetached&&e.compareDocumentPosition(t)===r?t==d||t.ownerDocument==_&&b(_,t)?-1:e==d||e.ownerDocument==_&&b(_,e)?1:c?P(c,t)-P(c,e):0:4&r?-1:1)}:function(t,e){if(t===e)return h=!0,0;var n,r=0,i=t.parentNode,o=e.parentNode,a=[t],s=[e];if(!i||!o)return t==d?-1:e==d?1:i?-1:o?1:c?P(c,t)-P(c,e):0;if(i===o)return ft(t,e);for(n=t;n=n.parentNode;)a.unshift(n);for(n=e;n=n.parentNode;)s.unshift(n);for(;a[r]===s[r];)r++;return r?ft(a[r],s[r]):a[r]==_?-1:s[r]==_?1:0}),d},st.matches=function(t,e){return st(t,null,null,e)},st.matchesSelector=function(t,e){if(f(t),n.matchesSelector&&g&&!k[e+" "]&&(!v||!v.test(e))&&(!m||!m.test(e)))try{var r=y.call(t,e);if(r||n.disconnectedMatch||t.document&&11!==t.document.nodeType)return r}catch(t){k(e,!0)}return 0<st(e,d,null,[t]).length},st.contains=function(t,e){return(t.ownerDocument||t)!=d&&f(t),b(t,e)},st.attr=function(t,e){(t.ownerDocument||t)!=d&&f(t);var i=r.attrHandle[e.toLowerCase()],o=i&&A.call(r.attrHandle,e.toLowerCase())?i(t,e,!g):void 0;return void 0!==o?o:n.attributes||!g?t.getAttribute(e):(o=t.getAttributeNode(e))&&o.specified?o.value:null},st.escape=function(t){return(t+"").replace(rt,it)},st.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},st.uniqueSort=function(t){var e,r=[],i=0,o=0;if(h=!n.detectDuplicates,c=!n.sortStable&&t.slice(0),t.sort(E),h){for(;e=t[o++];)e===t[o]&&(i=r.push(o));for(;i--;)t.splice(r[i],1)}return c=null,t},i=st.getText=function(t){var e,n="",r=0,o=t.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=i(t)}else if(3===o||4===o)return t.nodeValue}else for(;e=t[r++];)n+=i(e);return n},(r=st.selectors={cacheLength:50,createPseudo:ut,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(et,nt),t[3]=(t[3]||t[4]||t[5]||"").replace(et,nt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||st.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&st.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return G.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&X.test(n)&&(e=a(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(et,nt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=C[t+" "];return e||(e=new RegExp("(^|"+R+")"+t+"("+R+"|$)"))&&C(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,n){return function(r){var i=st.attr(r,t);return null==i?"!="===e:!e||(i+="","="===e?i===n:"!="===e?i!==n:"^="===e?n&&0===i.indexOf(n):"*="===e?n&&-1<i.indexOf(n):"$="===e?n&&i.slice(-n.length)===n:"~="===e?-1<(" "+i.replace(q," ")+" ").indexOf(n):"|="===e&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,r,i){var o="nth"!==t.slice(0,3),a="last"!==t.slice(-4),s="of-type"===e;return 1===r&&0===i?function(t){return!!t.parentNode}:function(e,n,l){var u,c,h,f,d,p,g=o!==a?"nextSibling":"previousSibling",m=e.parentNode,v=s&&e.nodeName.toLowerCase(),y=!l&&!s,b=!1;if(m){if(o){for(;g;){for(f=e;f=f[g];)if(s?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;p=g="only"===t&&!p&&"nextSibling"}return!0}if(p=[a?m.firstChild:m.lastChild],a&&y){for(b=(d=(u=(c=(h=(f=m)[x]||(f[x]={}))[f.uniqueID]||(h[f.uniqueID]={}))[t]||[])[0]===w&&u[1])&&u[2],f=d&&m.childNodes[d];f=++d&&f&&f[g]||(b=d=0)||p.pop();)if(1===f.nodeType&&++b&&f===e){c[t]=[w,d,b];break}}else if(y&&(b=d=(u=(c=(h=(f=e)[x]||(f[x]={}))[f.uniqueID]||(h[f.uniqueID]={}))[t]||[])[0]===w&&u[1]),!1===b)for(;(f=++d&&f&&f[g]||(b=d=0)||p.pop())&&((s?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++b||(y&&((c=(h=f[x]||(f[x]={}))[f.uniqueID]||(h[f.uniqueID]={}))[t]=[w,b]),f!==e)););return(b-=i)===r||b%r==0&&0<=b/r}}},PSEUDO:function(t,e){var n,i=r.pseudos[t]||r.setFilters[t.toLowerCase()]||st.error("unsupported pseudo: "+t);return i[x]?i(e):1<i.length?(n=[t,t,"",e],r.setFilters.hasOwnProperty(t.toLowerCase())?ut(function(t,n){for(var r,o=i(t,e),a=o.length;a--;)t[r=P(t,o[a])]=!(n[r]=o[a])}):function(t){return i(t,0,n)}):i}},pseudos:{not:ut(function(t){var e=[],n=[],r=s(t.replace(W,"$1"));return r[x]?ut(function(t,e,n,i){for(var o,a=r(t,null,i,[]),s=t.length;s--;)(o=a[s])&&(t[s]=!(e[s]=o))}):function(t,i,o){return e[0]=t,r(e,null,o,n),e[0]=null,!n.pop()}}),has:ut(function(t){return function(e){return 0<st(t,e).length}}),contains:ut(function(t){return t=t.replace(et,nt),function(e){return-1<(e.textContent||i(e)).indexOf(t)}}),lang:ut(function(t){return Y.test(t||"")||st.error("unsupported lang: "+t),t=t.replace(et,nt).toLowerCase(),function(e){var n;do{if(n=g?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===p},focus:function(t){return t===d.activeElement&&(!d.hasFocus||d.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:gt(!1),disabled:gt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!r.pseudos.empty(t)},header:function(t){return J.test(t.nodeName)},input:function(t){return Q.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:mt(function(){return[0]}),last:mt(function(t,e){return[e-1]}),eq:mt(function(t,e,n){return[n<0?n+e:n]}),even:mt(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),odd:mt(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),lt:mt(function(t,e,n){for(var r=n<0?n+e:e<n?e:n;0<=--r;)t.push(r);return t}),gt:mt(function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t})}}).pseudos.nth=r.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[e]=dt(e);for(e in{submit:!0,reset:!0})r.pseudos[e]=pt(e);function yt(){}function bt(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function xt(t,e,n){var r=e.dir,i=e.next,o=i||r,a=n&&"parentNode"===o,s=S++;return e.first?function(e,n,i){for(;e=e[r];)if(1===e.nodeType||a)return t(e,n,i);return!1}:function(e,n,l){var u,c,h,f=[w,s];if(l){for(;e=e[r];)if((1===e.nodeType||a)&&t(e,n,l))return!0}else for(;e=e[r];)if(1===e.nodeType||a)if(c=(h=e[x]||(e[x]={}))[e.uniqueID]||(h[e.uniqueID]={}),i&&i===e.nodeName.toLowerCase())e=e[r]||e;else{if((u=c[o])&&u[0]===w&&u[1]===s)return f[2]=u[2];if((c[o]=f)[2]=t(e,n,l))return!0}return!1}}function _t(t){return 1<t.length?function(e,n,r){for(var i=t.length;i--;)if(!t[i](e,n,r))return!1;return!0}:t[0]}function wt(t,e,n,r,i){for(var o,a=[],s=0,l=t.length,u=null!=e;s<l;s++)(o=t[s])&&(n&&!n(o,r,i)||(a.push(o),u&&e.push(s)));return a}function St(t,e,n,r,i,o){return r&&!r[x]&&(r=St(r)),i&&!i[x]&&(i=St(i,o)),ut(function(o,a,s,l){var u,c,h,f=[],d=[],p=a.length,g=o||function(t,e,n){for(var r=0,i=e.length;r<i;r++)st(t,e[r],n);return n}(e||"*",s.nodeType?[s]:s,[]),m=!t||!o&&e?g:wt(g,f,t,s,l),v=n?i||(o?t:p||r)?[]:a:m;if(n&&n(m,v,s,l),r)for(u=wt(v,d),r(u,[],s,l),c=u.length;c--;)(h=u[c])&&(v[d[c]]=!(m[d[c]]=h));if(o){if(i||t){if(i){for(u=[],c=v.length;c--;)(h=v[c])&&u.push(m[c]=h);i(null,v=[],u,l)}for(c=v.length;c--;)(h=v[c])&&-1<(u=i?P(o,h):f[c])&&(o[u]=!(a[u]=h))}}else v=wt(v===a?v.splice(p,v.length):v),i?i(null,a,v,l):F.apply(a,v)})}function Ct(t){for(var e,n,i,o=t.length,a=r.relative[t[0].type],s=a||r.relative[" "],l=a?1:0,c=xt(function(t){return t===e},s,!0),h=xt(function(t){return-1<P(e,t)},s,!0),f=[function(t,n,r){var i=!a&&(r||n!==u)||((e=n).nodeType?c(t,n,r):h(t,n,r));return e=null,i}];l<o;l++)if(n=r.relative[t[l].type])f=[xt(_t(f),n)];else{if((n=r.filter[t[l].type].apply(null,t[l].matches))[x]){for(i=++l;i<o&&!r.relative[t[i].type];i++);return St(1<l&&_t(f),1<l&&bt(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(W,"$1"),n,l<i&&Ct(t.slice(l,i)),i<o&&Ct(t=t.slice(i)),i<o&&bt(t))}f.push(n)}return _t(f)}return yt.prototype=r.filters=r.pseudos,r.setFilters=new yt,a=st.tokenize=function(t,e){var n,i,o,a,s,l,u,c=T[t+" "];if(c)return e?0:c.slice(0);for(s=t,l=[],u=r.preFilter;s;){for(a in n&&!(i=z.exec(s))||(i&&(s=s.slice(i[0].length)||s),l.push(o=[])),n=!1,(i=U.exec(s))&&(n=i.shift(),o.push({value:n,type:i[0].replace(W," ")}),s=s.slice(n.length)),r.filter)!(i=G[a].exec(s))||u[a]&&!(i=u[a](i))||(n=i.shift(),o.push({value:n,type:a,matches:i}),s=s.slice(n.length));if(!n)break}return e?s.length:s?st.error(t):T(t,l).slice(0)},s=st.compile=function(t,e){var n,i,o,s,l,c,h=[],p=[],m=D[t+" "];if(!m){for(e||(e=a(t)),n=e.length;n--;)(m=Ct(e[n]))[x]?h.push(m):p.push(m);(m=D(t,(i=p,s=0<(o=h).length,l=0<i.length,c=function(t,e,n,a,c){var h,p,m,v=0,y="0",b=t&&[],x=[],_=u,S=t||l&&r.find.TAG("*",c),C=w+=null==_?1:Math.random()||.1,T=S.length;for(c&&(u=e==d||e||c);y!==T&&null!=(h=S[y]);y++){if(l&&h){for(p=0,e||h.ownerDocument==d||(f(h),n=!g);m=i[p++];)if(m(h,e||d,n)){a.push(h);break}c&&(w=C)}s&&((h=!m&&h)&&v--,t&&b.push(h))}if(v+=y,s&&y!==v){for(p=0;m=o[p++];)m(b,x,e,n);if(t){if(0<v)for(;y--;)b[y]||x[y]||(x[y]=I.call(a));x=wt(x)}F.apply(a,x),c&&!t&&0<x.length&&1<v+o.length&&st.uniqueSort(a)}return c&&(w=C,u=_),b},s?ut(c):c))).selector=t}return m},l=st.select=function(t,e,n,i){var o,l,u,c,h,f="function"==typeof t&&t,d=!i&&a(t=f.selector||t);if(n=n||[],1===d.length){if(2<(l=d[0]=d[0].slice(0)).length&&"ID"===(u=l[0]).type&&9===e.nodeType&&g&&r.relative[l[1].type]){if(!(e=(r.find.ID(u.matches[0].replace(et,nt),e)||[])[0]))return n;f&&(e=e.parentNode),t=t.slice(l.shift().value.length)}for(o=G.needsContext.test(t)?0:l.length;o--&&!r.relative[c=(u=l[o]).type];)if((h=r.find[c])&&(i=h(u.matches[0].replace(et,nt),tt.test(l[0].type)&&vt(e.parentNode)||e))){if(l.splice(o,1),!(t=i.length&&bt(l)))return F.apply(n,i),n;break}}return(f||s(t,d))(i,e,!g,n,!e||tt.test(t)&&vt(e.parentNode)||e),n},n.sortStable=x.split("").sort(E).join("")===x,n.detectDuplicates=!!h,f(),n.sortDetached=ct(function(t){return 1&t.compareDocumentPosition(d.createElement("fieldset"))}),ct(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||ht("type|href|height|width",function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),n.attributes&&ct(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||ht("value",function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),ct(function(t){return null==t.getAttribute("disabled")})||ht(M,function(t,e,n){var r;if(!n)return!0===t[e]?e.toLowerCase():(r=t.getAttributeNode(e))&&r.specified?r.value:null}),st}(t);_.find=S,_.expr=S.selectors,_.expr[":"]=_.expr.pseudos,_.uniqueSort=_.unique=S.uniqueSort,_.text=S.getText,_.isXMLDoc=S.isXML,_.contains=S.contains,_.escapeSelector=S.escape;var C=function(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&_(t).is(n))break;r.push(t)}return r},T=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},D=_.expr.match.needsContext;function k(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var E=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function A(t,e,n){return p(e)?_.grep(t,function(t,r){return!!e.call(t,r,t)!==n}):e.nodeType?_.grep(t,function(t){return t===e!==n}):"string"!=typeof e?_.grep(t,function(t){return-1<s.call(e,t)!==n}):_.filter(e,t,n)}_.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?_.find.matchesSelector(r,t)?[r]:[]:_.find.matches(t,_.grep(e,function(t){return 1===t.nodeType}))},_.fn.extend({find:function(t){var e,n,r=this.length,i=this;if("string"!=typeof t)return this.pushStack(_(t).filter(function(){for(e=0;e<r;e++)if(_.contains(i[e],this))return!0}));for(n=this.pushStack([]),e=0;e<r;e++)_.find(t,i[e],n);return 1<r?_.uniqueSort(n):n},filter:function(t){return this.pushStack(A(this,t||[],!1))},not:function(t){return this.pushStack(A(this,t||[],!0))},is:function(t){return!!A(this,"string"==typeof t&&D.test(t)?_(t):t||[],!1).length}});var L,I=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(_.fn.init=function(t,e,n){var r,i;if(!t)return this;if(n=n||L,"string"==typeof t){if(!(r="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:I.exec(t))||!r[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(r[1]){if(_.merge(this,_.parseHTML(r[1],(e=e instanceof _?e[0]:e)&&e.nodeType?e.ownerDocument||e:m,!0)),E.test(r[1])&&_.isPlainObject(e))for(r in e)p(this[r])?this[r](e[r]):this.attr(r,e[r]);return this}return(i=m.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):p(t)?void 0!==n.ready?n.ready(t):t(_):_.makeArray(t,this)}).prototype=_.fn,L=_(m);var N=/^(?:parents|prev(?:Until|All))/,F={children:!0,contents:!0,next:!0,prev:!0};function j(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}_.fn.extend({has:function(t){var e=_(t,this),n=e.length;return this.filter(function(){for(var t=0;t<n;t++)if(_.contains(this,e[t]))return!0})},closest:function(t,e){var n,r=0,i=this.length,o=[],a="string"!=typeof t&&_(t);if(!D.test(t))for(;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&_.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(1<o.length?_.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?s.call(_(t),this[0]):s.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(_.uniqueSort(_.merge(this.get(),_(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),_.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return C(t,"parentNode")},parentsUntil:function(t,e,n){return C(t,"parentNode",n)},next:function(t){return j(t,"nextSibling")},prev:function(t){return j(t,"previousSibling")},nextAll:function(t){return C(t,"nextSibling")},prevAll:function(t){return C(t,"previousSibling")},nextUntil:function(t,e,n){return C(t,"nextSibling",n)},prevUntil:function(t,e,n){return C(t,"previousSibling",n)},siblings:function(t){return T((t.parentNode||{}).firstChild,t)},children:function(t){return T(t.firstChild)},contents:function(t){return null!=t.contentDocument&&r(t.contentDocument)?t.contentDocument:(k(t,"template")&&(t=t.content||t),_.merge([],t.childNodes))}},function(t,e){_.fn[t]=function(n,r){var i=_.map(this,e,n);return"Until"!==t.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=_.filter(r,i)),1<this.length&&(F[t]||_.uniqueSort(i),N.test(t)&&i.reverse()),this.pushStack(i)}});var P=/[^\x20\t\r\n\f]+/g;function M(t){return t}function R(t){throw t}function O(t,e,n,r){var i;try{t&&p(i=t.promise)?i.call(t).done(e).fail(n):t&&p(i=t.then)?i.call(t,e,n):e.apply(void 0,[t].slice(r))}catch(t){n.apply(void 0,[t])}}_.Callbacks=function(t){var e;t="string"==typeof t?(e={},_.each(t.match(P)||[],function(t,n){e[n]=!0}),e):_.extend({},t);var n,r,i,o,a=[],s=[],l=-1,u=function(){for(o=o||t.once,i=n=!0;s.length;l=-1)for(r=s.shift();++l<a.length;)!1===a[l].apply(r[0],r[1])&&t.stopOnFalse&&(l=a.length,r=!1);t.memory||(r=!1),n=!1,o&&(a=r?[]:"")},c={add:function(){return a&&(r&&!n&&(l=a.length-1,s.push(r)),function e(n){_.each(n,function(n,r){p(r)?t.unique&&c.has(r)||a.push(r):r&&r.length&&"string"!==b(r)&&e(r)})}(arguments),r&&!n&&u()),this},remove:function(){return _.each(arguments,function(t,e){for(var n;-1<(n=_.inArray(e,a,n));)a.splice(n,1),n<=l&&l--}),this},has:function(t){return t?-1<_.inArray(t,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return o=s=[],a=r="",this},disabled:function(){return!a},lock:function(){return o=s=[],r||n||(a=r=""),this},locked:function(){return!!o},fireWith:function(t,e){return o||(e=[t,(e=e||[]).slice?e.slice():e],s.push(e),n||u()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!i}};return c},_.extend({Deferred:function(e){var n=[["notify","progress",_.Callbacks("memory"),_.Callbacks("memory"),2],["resolve","done",_.Callbacks("once memory"),_.Callbacks("once memory"),0,"resolved"],["reject","fail",_.Callbacks("once memory"),_.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return i.then(null,t)},pipe:function(){var t=arguments;return _.Deferred(function(e){_.each(n,function(n,r){var i=p(t[r[4]])&&t[r[4]];o[r[1]](function(){var t=i&&i.apply(this,arguments);t&&p(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[r[0]+"With"](this,i?[t]:arguments)})}),t=null}).promise()},then:function(e,r,i){var o=0;function a(e,n,r,i){return function(){var s=this,l=arguments,u=function(){var t,u;if(!(e<o)){if((t=r.apply(s,l))===n.promise())throw new TypeError("Thenable self-resolution");p(u=t&&("object"==typeof t||"function"==typeof t)&&t.then)?i?u.call(t,a(o,n,M,i),a(o,n,R,i)):(o++,u.call(t,a(o,n,M,i),a(o,n,R,i),a(o,n,M,n.notifyWith))):(r!==M&&(s=void 0,l=[t]),(i||n.resolveWith)(s,l))}},c=i?u:function(){try{u()}catch(u){_.Deferred.exceptionHook&&_.Deferred.exceptionHook(u,c.stackTrace),o<=e+1&&(r!==R&&(s=void 0,l=[u]),n.rejectWith(s,l))}};e?c():(_.Deferred.getStackHook&&(c.stackTrace=_.Deferred.getStackHook()),t.setTimeout(c))}}return _.Deferred(function(t){n[0][3].add(a(0,t,p(i)?i:M,t.notifyWith)),n[1][3].add(a(0,t,p(e)?e:M)),n[2][3].add(a(0,t,p(r)?r:R))}).promise()},promise:function(t){return null!=t?_.extend(t,i):i}},o={};return _.each(n,function(t,e){var a=e[2],s=e[5];i[e[1]]=a.add,s&&a.add(function(){r=s},n[3-t][2].disable,n[3-t][3].disable,n[0][2].lock,n[0][3].lock),a.add(e[3].fire),o[e[0]]=function(){return o[e[0]+"With"](this===o?void 0:this,arguments),this},o[e[0]+"With"]=a.fireWith}),i.promise(o),e&&e.call(o,o),o},when:function(t){var e=arguments.length,n=e,r=Array(n),o=i.call(arguments),a=_.Deferred(),s=function(t){return function(n){r[t]=this,o[t]=1<arguments.length?i.call(arguments):n,--e||a.resolveWith(r,o)}};if(e<=1&&(O(t,a.done(s(n)).resolve,a.reject,!e),"pending"===a.state()||p(o[n]&&o[n].then)))return a.then();for(;n--;)O(o[n],s(n),a.reject);return a.promise()}});var B=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;_.Deferred.exceptionHook=function(e,n){t.console&&t.console.warn&&e&&B.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,n)},_.readyException=function(e){t.setTimeout(function(){throw e})};var H=_.Deferred();function q(){m.removeEventListener("DOMContentLoaded",q),t.removeEventListener("load",q),_.ready()}_.fn.ready=function(t){return H.then(t).catch(function(t){_.readyException(t)}),this},_.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--_.readyWait:_.isReady)||(_.isReady=!0)!==t&&0<--_.readyWait||H.resolveWith(m,[_])}}),_.ready.then=H.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?t.setTimeout(_.ready):(m.addEventListener("DOMContentLoaded",q),t.addEventListener("load",q));var W=function(t,e,n,r,i,o,a){var s=0,l=t.length,u=null==n;if("object"===b(n))for(s in i=!0,n)W(t,e,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,p(r)||(a=!0),u&&(a?(e.call(t,r),e=null):(u=e,e=function(t,e,n){return u.call(_(t),n)})),e))for(;s<l;s++)e(t[s],n,a?r:r.call(t[s],s,e(t[s],n)));return i?t:u?e.call(t):l?e(t[0],n):o},z=/^-ms-/,U=/-([a-z])/g;function V(t,e){return e.toUpperCase()}function X(t){return t.replace(z,"ms-").replace(U,V)}var Y=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function G(){this.expando=_.expando+G.uid++}G.uid=1,G.prototype={cache:function(t){var e=t[this.expando];return e||(e={},Y(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var r,i=this.cache(t);if("string"==typeof e)i[X(e)]=n;else for(r in e)i[X(r)]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][X(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r=t[this.expando];if(void 0!==r){if(void 0!==e){n=(e=Array.isArray(e)?e.map(X):(e=X(e))in r?[e]:e.match(P)||[]).length;for(;n--;)delete r[e[n]]}(void 0===e||_.isEmptyObject(r))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!_.isEmptyObject(e)}};var $=new G,Q=new G,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function Z(t,e,n){var r,i;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(K,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(r))){try{n="true"===(i=n)||"false"!==i&&("null"===i?null:i===+i+""?+i:J.test(i)?JSON.parse(i):i)}catch(t){}Q.set(t,e,n)}else n=void 0;return n}_.extend({hasData:function(t){return Q.hasData(t)||$.hasData(t)},data:function(t,e,n){return Q.access(t,e,n)},removeData:function(t,e){Q.remove(t,e)},_data:function(t,e,n){return $.access(t,e,n)},_removeData:function(t,e){$.remove(t,e)}}),_.fn.extend({data:function(t,e){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===t){if(this.length&&(i=Q.get(o),1===o.nodeType&&!$.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(r=a[n].name).indexOf("data-")&&(r=X(r.slice(5)),Z(o,r,i[r]));$.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof t?this.each(function(){Q.set(this,t)}):W(this,function(e){var n;if(o&&void 0===e)return void 0!==(n=Q.get(o,t))||void 0!==(n=Z(o,t))?n:void 0;this.each(function(){Q.set(this,t,e)})},null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each(function(){Q.remove(this,t)})}}),_.extend({queue:function(t,e,n){var r;if(t)return r=$.get(t,e=(e||"fx")+"queue"),n&&(!r||Array.isArray(n)?r=$.access(t,e,_.makeArray(n)):r.push(n)),r||[]},dequeue:function(t,e){var n=_.queue(t,e=e||"fx"),r=n.length,i=n.shift(),o=_._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,function(){_.dequeue(t,e)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return $.get(t,n)||$.access(t,n,{empty:_.Callbacks("once memory").add(function(){$.remove(t,[e+"queue",n])})})}}),_.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?_.queue(this[0],t):void 0===e?this:this.each(function(){var n=_.queue(this,t,e);_._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&_.dequeue(this,t)})},dequeue:function(t){return this.each(function(){_.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,r=1,i=_.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(n=$.get(o[a],t+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(e)}});var tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+tt+")([a-z%]*)$","i"),nt=["Top","Right","Bottom","Left"],rt=m.documentElement,it=function(t){return _.contains(t.ownerDocument,t)},ot={composed:!0};rt.getRootNode&&(it=function(t){return _.contains(t.ownerDocument,t)||t.getRootNode(ot)===t.ownerDocument});var at=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&it(t)&&"none"===_.css(t,"display")};function st(t,e,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return _.css(t,e,"")},l=s(),u=n&&n[3]||(_.cssNumber[e]?"":"px"),c=t.nodeType&&(_.cssNumber[e]||"px"!==u&&+l)&&et.exec(_.css(t,e));if(c&&c[3]!==u){for(u=u||c[3],c=+(l/=2)||1;a--;)_.style(t,e,c+u),(1-o)*(1-(o=s()/l||.5))<=0&&(a=0),c/=o;_.style(t,e,(c*=2)+u),n=n||[]}return n&&(c=+c||+l||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=u,r.start=c,r.end=i)),i}var lt={};function ut(t,e){for(var n,r,i,o,a,s,l,u=[],c=0,h=t.length;c<h;c++)(r=t[c]).style&&(n=r.style.display,e?("none"===n&&(u[c]=$.get(r,"display")||null,u[c]||(r.style.display="")),""===r.style.display&&at(r)&&(u[c]=(l=a=o=void 0,a=(i=r).ownerDocument,(l=lt[s=i.nodeName])||(o=a.body.appendChild(a.createElement(s)),l=_.css(o,"display"),o.parentNode.removeChild(o),"none"===l&&(l="block"),lt[s]=l)))):"none"!==n&&(u[c]="none",$.set(r,"display",n)));for(c=0;c<h;c++)null!=u[c]&&(t[c].style.display=u[c]);return t}_.fn.extend({show:function(){return ut(this,!0)},hide:function(){return ut(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){at(this)?_(this).show():_(this).hide()})}});var ct,ht,ft=/^(?:checkbox|radio)$/i,dt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,pt=/^$|^module$|\/(?:java|ecma)script/i;ct=m.createDocumentFragment().appendChild(m.createElement("div")),(ht=m.createElement("input")).setAttribute("type","radio"),ht.setAttribute("checked","checked"),ht.setAttribute("name","t"),ct.appendChild(ht),d.checkClone=ct.cloneNode(!0).cloneNode(!0).lastChild.checked,ct.innerHTML="<textarea>x</textarea>",d.noCloneChecked=!!ct.cloneNode(!0).lastChild.defaultValue,ct.innerHTML="<option></option>",d.option=!!ct.lastChild;var gt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function mt(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&k(t,e)?_.merge([t],n):n}function vt(t,e){for(var n=0,r=t.length;n<r;n++)$.set(t[n],"globalEval",!e||$.get(e[n],"globalEval"))}gt.tbody=gt.tfoot=gt.colgroup=gt.caption=gt.thead,gt.th=gt.td,d.option||(gt.optgroup=gt.option=[1,"<select multiple='multiple'>","</select>"]);var yt=/<|&#?\w+;/;function bt(t,e,n,r,i){for(var o,a,s,l,u,c,h=e.createDocumentFragment(),f=[],d=0,p=t.length;d<p;d++)if((o=t[d])||0===o)if("object"===b(o))_.merge(f,o.nodeType?[o]:o);else if(yt.test(o)){for(a=a||h.appendChild(e.createElement("div")),s=(dt.exec(o)||["",""])[1].toLowerCase(),a.innerHTML=(l=gt[s]||gt._default)[1]+_.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;_.merge(f,a.childNodes),(a=h.firstChild).textContent=""}else f.push(e.createTextNode(o));for(h.textContent="",d=0;o=f[d++];)if(r&&-1<_.inArray(o,r))i&&i.push(o);else if(u=it(o),a=mt(h.appendChild(o),"script"),u&&vt(a),n)for(c=0;o=a[c++];)pt.test(o.type||"")&&n.push(o);return h}var xt=/^key/,_t=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,wt=/^([^.]*)(?:\.(.+)|)/;function St(){return!0}function Ct(){return!1}function Tt(t,e){return t===function(){try{return m.activeElement}catch(t){}}()==("focus"===e)}function Dt(t,e,n,r,i,o){var a,s;if("object"==typeof e){for(s in"string"!=typeof n&&(r=r||n,n=void 0),e)Dt(t,s,n,r,e[s],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Ct;else if(!i)return t;return 1===o&&(a=i,(i=function(t){return _().off(t),a.apply(this,arguments)}).guid=a.guid||(a.guid=_.guid++)),t.each(function(){_.event.add(this,e,i,r,n)})}function kt(t,e,n){n?($.set(t,e,!1),_.event.add(t,e,{namespace:!1,handler:function(t){var r,o,a=$.get(this,e);if(1&t.isTrigger&&this[e]){if(a.length)(_.event.special[e]||{}).delegateType&&t.stopPropagation();else if(a=i.call(arguments),$.set(this,e,a),r=n(this,e),this[e](),a!==(o=$.get(this,e))||r?$.set(this,e,!1):o={},a!==o)return t.stopImmediatePropagation(),t.preventDefault(),o.value}else a.length&&($.set(this,e,{value:_.event.trigger(_.extend(a[0],_.Event.prototype),a.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===$.get(t,e)&&_.event.add(t,e,St)}_.event={global:{},add:function(t,e,n,r,i){var o,a,s,l,u,c,h,f,d,p,g,m=$.get(t);if(Y(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&_.find.matchesSelector(rt,i),n.guid||(n.guid=_.guid++),(l=m.events)||(l=m.events=Object.create(null)),(a=m.handle)||(a=m.handle=function(e){return void 0!==_&&_.event.triggered!==e.type?_.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(P)||[""]).length;u--;)d=g=(s=wt.exec(e[u])||[])[1],p=(s[2]||"").split(".").sort(),d&&(h=_.event.special[d]||{},h=_.event.special[d=(i?h.delegateType:h.bindType)||d]||{},c=_.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&_.expr.match.needsContext.test(i),namespace:p.join(".")},o),(f=l[d])||((f=l[d]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(t,r,p,a)||t.addEventListener&&t.addEventListener(d,a)),h.add&&(h.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,c):f.push(c),_.event.global[d]=!0)},remove:function(t,e,n,r,i){var o,a,s,l,u,c,h,f,d,p,g,m=$.hasData(t)&&$.get(t);if(m&&(l=m.events)){for(u=(e=(e||"").match(P)||[""]).length;u--;)if(d=g=(s=wt.exec(e[u])||[])[1],p=(s[2]||"").split(".").sort(),d){for(h=_.event.special[d]||{},f=l[d=(r?h.delegateType:h.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=f.length;o--;)c=f[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(f.splice(o,1),c.selector&&f.delegateCount--,h.remove&&h.remove.call(t,c));a&&!f.length&&(h.teardown&&!1!==h.teardown.call(t,p,m.handle)||_.removeEvent(t,d,m.handle),delete l[d])}else for(d in l)_.event.remove(t,d+e[u],n,r,!0);_.isEmptyObject(l)&&$.remove(t,"handle events")}},dispatch:function(t){var e,n,r,i,o,a,s=new Array(arguments.length),l=_.event.fix(t),u=($.get(this,"events")||Object.create(null))[l.type]||[],c=_.event.special[l.type]||{};for(s[0]=l,e=1;e<arguments.length;e++)s[e]=arguments[e];if(l.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,l)){for(a=_.event.handlers.call(this,l,u),e=0;(i=a[e++])&&!l.isPropagationStopped();)for(l.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(r=((_.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(l.result=r)&&(l.preventDefault(),l.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,r,i,o,a,s=[],l=e.delegateCount,u=t.target;if(l&&u.nodeType&&!("click"===t.type&&1<=t.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==t.type||!0!==u.disabled)){for(o=[],a={},n=0;n<l;n++)void 0===a[i=(r=e[n]).selector+" "]&&(a[i]=r.needsContext?-1<_(i,this).index(u):_.find(i,this,null,[u]).length),a[i]&&o.push(r);o.length&&s.push({elem:u,handlers:o})}return u=this,l<e.length&&s.push({elem:u,handlers:e.slice(l)}),s},addProp:function(t,e){Object.defineProperty(_.Event.prototype,t,{enumerable:!0,configurable:!0,get:p(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[_.expando]?t:new _.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return ft.test(e.type)&&e.click&&k(e,"input")&&kt(e,"click",St),!1},trigger:function(t){var e=this||t;return ft.test(e.type)&&e.click&&k(e,"input")&&kt(e,"click"),!0},_default:function(t){var e=t.target;return ft.test(e.type)&&e.click&&k(e,"input")&&$.get(e,"click")||k(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},_.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},_.Event=function(t,e){if(!(this instanceof _.Event))return new _.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?St:Ct,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&_.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[_.expando]=!0},_.Event.prototype={constructor:_.Event,isDefaultPrevented:Ct,isPropagationStopped:Ct,isImmediatePropagationStopped:Ct,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=St,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=St,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=St,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},_.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(t){var e=t.button;return null==t.which&&xt.test(t.type)?null!=t.charCode?t.charCode:t.keyCode:!t.which&&void 0!==e&&_t.test(t.type)?1&e?1:2&e?3:4&e?2:0:t.which}},_.event.addProp),_.each({focus:"focusin",blur:"focusout"},function(t,e){_.event.special[t]={setup:function(){return kt(this,t,Tt),!1},trigger:function(){return kt(this,t),!0},delegateType:e}}),_.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){_.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,r=t.relatedTarget,i=t.handleObj;return r&&(r===this||_.contains(this,r))||(t.type=i.origType,n=i.handler.apply(this,arguments),t.type=e),n}}}),_.fn.extend({on:function(t,e,n,r){return Dt(this,t,e,n,r)},one:function(t,e,n,r){return Dt(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)return r=t.handleObj,_(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof t){for(i in t)this.off(i,e,t[i]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=Ct),this.each(function(){_.event.remove(this,t,n,e)})}});var Et=/<script|<style|<link/i,At=/checked\s*(?:[^=]|=\s*.checked.)/i,Lt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function It(t,e){return k(t,"table")&&k(11!==e.nodeType?e:e.firstChild,"tr")&&_(t).children("tbody")[0]||t}function Nt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Ft(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function jt(t,e){var n,r,i,o,a,s;if(1===e.nodeType){if($.hasData(t)&&(s=$.get(t).events))for(i in $.remove(e,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)_.event.add(e,i,s[i][n]);Q.hasData(t)&&(o=Q.access(t),a=_.extend({},o),Q.set(e,a))}}function Pt(t,e,n,r){e=o(e);var i,a,s,l,u,c,h=0,f=t.length,g=f-1,m=e[0],v=p(m);if(v||1<f&&"string"==typeof m&&!d.checkClone&&At.test(m))return t.each(function(i){var o=t.eq(i);v&&(e[0]=m.call(this,i,o.html())),Pt(o,e,n,r)});if(f&&(a=(i=bt(e,t[0].ownerDocument,!1,t,r)).firstChild,1===i.childNodes.length&&(i=a),a||r)){for(l=(s=_.map(mt(i,"script"),Nt)).length;h<f;h++)u=i,h!==g&&(u=_.clone(u,!0,!0),l&&_.merge(s,mt(u,"script"))),n.call(t[h],u,h);if(l)for(c=s[s.length-1].ownerDocument,_.map(s,Ft),h=0;h<l;h++)pt.test((u=s[h]).type||"")&&!$.access(u,"globalEval")&&_.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?_._evalUrl&&!u.noModule&&_._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},c):y(u.textContent.replace(Lt,""),u,c))}return t}function Mt(t,e,n){for(var r,i=e?_.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||_.cleanData(mt(r)),r.parentNode&&(n&&it(r)&&vt(mt(r,"script")),r.parentNode.removeChild(r));return t}_.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var r,i,o,a,s,l,u,c=t.cloneNode(!0),h=it(t);if(!(d.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||_.isXMLDoc(t)))for(a=mt(c),r=0,i=(o=mt(t)).length;r<i;r++)s=o[r],"input"===(u=(l=a[r]).nodeName.toLowerCase())&&ft.test(s.type)?l.checked=s.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=s.defaultValue);if(e)if(n)for(o=o||mt(t),a=a||mt(c),r=0,i=o.length;r<i;r++)jt(o[r],a[r]);else jt(t,c);return 0<(a=mt(c,"script")).length&&vt(a,!h&&mt(t,"script")),c},cleanData:function(t){for(var e,n,r,i=_.event.special,o=0;void 0!==(n=t[o]);o++)if(Y(n)){if(e=n[$.expando]){if(e.events)for(r in e.events)i[r]?_.event.remove(n,r):_.removeEvent(n,r,e.handle);n[$.expando]=void 0}n[Q.expando]&&(n[Q.expando]=void 0)}}}),_.fn.extend({detach:function(t){return Mt(this,t,!0)},remove:function(t){return Mt(this,t)},text:function(t){return W(this,function(t){return void 0===t?_.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Pt(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||It(this,t).appendChild(t)})},prepend:function(){return Pt(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=It(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return Pt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Pt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(_.cleanData(mt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return _.clone(this,t,e)})},html:function(t){return W(this,function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Et.test(t)&&!gt[(dt.exec(t)||["",""])[1].toLowerCase()]){t=_.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(_.cleanData(mt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return Pt(this,arguments,function(e){var n=this.parentNode;_.inArray(this,t)<0&&(_.cleanData(mt(this)),n&&n.replaceChild(e,this))},t)}}),_.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){_.fn[t]=function(t){for(var n,r=[],i=_(t),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),_(i[s])[e](n),a.apply(r,n.get());return this.pushStack(r)}});var Rt=new RegExp("^("+tt+")(?!px)[a-z%]+$","i"),Ot=function(e){var n=e.ownerDocument.defaultView;return n&&n.opener||(n=t),n.getComputedStyle(e)},Bt=function(t,e,n){var r,i,o={};for(i in e)o[i]=t.style[i],t.style[i]=e[i];for(i in r=n.call(t),e)t.style[i]=o[i];return r},Ht=new RegExp(nt.join("|"),"i");function qt(t,e,n){var r,i,o,a,s=t.style;return(n=n||Ot(t))&&(""!==(a=n.getPropertyValue(e)||n[e])||it(t)||(a=_.style(t,e)),!d.pixelBoxStyles()&&Rt.test(a)&&Ht.test(e)&&(r=s.width,i=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=r,s.minWidth=i,s.maxWidth=o)),void 0!==a?a+"":a}function Wt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(c){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",rt.appendChild(u).appendChild(c);var e=t.getComputedStyle(c);r="1%"!==e.top,l=12===n(e.marginLeft),c.style.right="60%",a=36===n(e.right),i=36===n(e.width),c.style.position="absolute",o=12===n(c.offsetWidth/3),rt.removeChild(u),c=null}}function n(t){return Math.round(parseFloat(t))}var r,i,o,a,s,l,u=m.createElement("div"),c=m.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",d.clearCloneStyle="content-box"===c.style.backgroundClip,_.extend(d,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),r},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,n,r,i;return null==s&&(e=m.createElement("table"),n=m.createElement("tr"),r=m.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",r.style.height="9px",rt.appendChild(e).appendChild(n).appendChild(r),i=t.getComputedStyle(n),s=3<parseInt(i.height),rt.removeChild(e)),s}}))}();var zt=["Webkit","Moz","ms"],Ut=m.createElement("div").style,Vt={};function Xt(t){return _.cssProps[t]||Vt[t]||(t in Ut?t:Vt[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=zt.length;n--;)if((t=zt[n]+e)in Ut)return t}(t)||t)}var Yt=/^(none|table(?!-c[ea]).+)/,Gt=/^--/,$t={position:"absolute",visibility:"hidden",display:"block"},Qt={letterSpacing:"0",fontWeight:"400"};function Jt(t,e,n){var r=et.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function Kt(t,e,n,r,i,o){var a="width"===e?1:0,s=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=_.css(t,n+nt[a],!0,i)),r?("content"===n&&(l-=_.css(t,"padding"+nt[a],!0,i)),"margin"!==n&&(l-=_.css(t,"border"+nt[a]+"Width",!0,i))):(l+=_.css(t,"padding"+nt[a],!0,i),"padding"!==n?l+=_.css(t,"border"+nt[a]+"Width",!0,i):s+=_.css(t,"border"+nt[a]+"Width",!0,i));return!r&&0<=o&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-s-.5))||0),l}function Zt(t,e,n){var r=Ot(t),i=(!d.boxSizingReliable()||n)&&"border-box"===_.css(t,"boxSizing",!1,r),o=i,a=qt(t,e,r),s="offset"+e[0].toUpperCase()+e.slice(1);if(Rt.test(a)){if(!n)return a;a="auto"}return(!d.boxSizingReliable()&&i||!d.reliableTrDimensions()&&k(t,"tr")||"auto"===a||!parseFloat(a)&&"inline"===_.css(t,"display",!1,r))&&t.getClientRects().length&&(i="border-box"===_.css(t,"boxSizing",!1,r),(o=s in t)&&(a=t[s])),(a=parseFloat(a)||0)+Kt(t,e,n||(i?"border":"content"),o,r,a)+"px"}function te(t,e,n,r,i){return new te.prototype.init(t,e,n,r,i)}_.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=qt(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,n,r){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var i,o,a,s=X(e),l=Gt.test(e),u=t.style;if(l||(e=Xt(s)),a=_.cssHooks[e]||_.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(t,!1,r))?i:u[e];"string"==(o=typeof n)&&(i=et.exec(n))&&i[1]&&(n=st(t,e,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(_.cssNumber[s]?"":"px")),d.clearCloneStyle||""!==n||0!==e.indexOf("background")||(u[e]="inherit"),a&&"set"in a&&void 0===(n=a.set(t,n,r))||(l?u.setProperty(e,n):u[e]=n))}},css:function(t,e,n,r){var i,o,a,s=X(e);return Gt.test(e)||(e=Xt(s)),(a=_.cssHooks[e]||_.cssHooks[s])&&"get"in a&&(i=a.get(t,!0,n)),void 0===i&&(i=qt(t,e,r)),"normal"===i&&e in Qt&&(i=Qt[e]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),_.each(["height","width"],function(t,e){_.cssHooks[e]={get:function(t,n,r){if(n)return!Yt.test(_.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?Zt(t,e,r):Bt(t,$t,function(){return Zt(t,e,r)})},set:function(t,n,r){var i,o=Ot(t),a=!d.scrollboxSize()&&"absolute"===o.position,s=(a||r)&&"border-box"===_.css(t,"boxSizing",!1,o),l=r?Kt(t,e,r,s,o):0;return s&&a&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-Kt(t,e,"border",!1,o)-.5)),l&&(i=et.exec(n))&&"px"!==(i[3]||"px")&&(t.style[e]=n,n=_.css(t,e)),Jt(0,n,l)}}}),_.cssHooks.marginLeft=Wt(d.reliableMarginLeft,function(t,e){if(e)return(parseFloat(qt(t,"marginLeft"))||t.getBoundingClientRect().left-Bt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),_.each({margin:"",padding:"",border:"Width"},function(t,e){_.cssHooks[t+e]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[t+nt[r]+e]=o[r]||o[r-2]||o[0];return i}},"margin"!==t&&(_.cssHooks[t+e].set=Jt)}),_.fn.extend({css:function(t,e){return W(this,function(t,e,n){var r,i,o={},a=0;if(Array.isArray(e)){for(r=Ot(t),i=e.length;a<i;a++)o[e[a]]=_.css(t,e[a],!1,r);return o}return void 0!==n?_.style(t,e,n):_.css(t,e)},t,e,1<arguments.length)}}),((_.Tween=te).prototype={constructor:te,init:function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||_.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(_.cssNumber[n]?"":"px")},cur:function(){var t=te.propHooks[this.prop];return t&&t.get?t.get(this):te.propHooks._default.get(this)},run:function(t){var e,n=te.propHooks[this.prop];return this.pos=e=this.options.duration?_.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):te.propHooks._default.set(this),this}}).init.prototype=te.prototype,(te.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=_.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){_.fx.step[t.prop]?_.fx.step[t.prop](t):1!==t.elem.nodeType||!_.cssHooks[t.prop]&&null==t.elem.style[Xt(t.prop)]?t.elem[t.prop]=t.now:_.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=te.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},_.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},_.fx=te.prototype.init,_.fx.step={};var ee,ne,re,ie,oe=/^(?:toggle|show|hide)$/,ae=/queueHooks$/;function se(){ne&&(!1===m.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(se):t.setTimeout(se,_.fx.interval),_.fx.tick())}function le(){return t.setTimeout(function(){ee=void 0}),ee=Date.now()}function ue(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)i["margin"+(n=nt[r])]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function ce(t,e,n){for(var r,i=(he.tweeners[e]||[]).concat(he.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,e,t))return r}function he(t,e,n){var r,i,o=0,a=he.prefilters.length,s=_.Deferred().always(function(){delete l.elem}),l=function(){if(i)return!1;for(var e=ee||le(),n=Math.max(0,u.startTime+u.duration-e),r=1-(n/u.duration||0),o=0,a=u.tweens.length;o<a;o++)u.tweens[o].run(r);return s.notifyWith(t,[u,r,n]),r<1&&a?n:(a||s.notifyWith(t,[u,1,0]),s.resolveWith(t,[u]),!1)},u=s.promise({elem:t,props:_.extend({},e),opts:_.extend(!0,{specialEasing:{},easing:_.easing._default},n),originalProperties:e,originalOptions:n,startTime:ee||le(),duration:n.duration,tweens:[],createTween:function(e,n){var r=_.Tween(t,u.opts,e,n,u.opts.specialEasing[e]||u.opts.easing);return u.tweens.push(r),r},stop:function(e){var n=0,r=e?u.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)u.tweens[n].run(1);return e?(s.notifyWith(t,[u,1,0]),s.resolveWith(t,[u,e])):s.rejectWith(t,[u,e]),this}}),c=u.props;for(function(t,e){var n,r,i,o,a;for(n in t)if(i=e[r=X(n)],o=t[n],Array.isArray(o)&&(i=o[1],o=t[n]=o[0]),n!==r&&(t[r]=o,delete t[n]),(a=_.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete t[r],o)n in t||(t[n]=o[n],e[n]=i);else e[r]=i}(c,u.opts.specialEasing);o<a;o++)if(r=he.prefilters[o].call(u,t,c,u.opts))return p(r.stop)&&(_._queueHooks(u.elem,u.opts.queue).stop=r.stop.bind(r)),r;return _.map(c,ce,u),p(u.opts.start)&&u.opts.start.call(t,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),_.fx.timer(_.extend(l,{elem:t,anim:u,queue:u.opts.queue})),u}_.Animation=_.extend(he,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return st(n.elem,t,et.exec(e),n),n}]},tweener:function(t,e){p(t)?(e=t,t=["*"]):t=t.match(P);for(var n,r=0,i=t.length;r<i;r++)(he.tweeners[n=t[r]]=he.tweeners[n]||[]).unshift(e)},prefilters:[function(t,e,n){var r,i,o,a,s,l,u,c,h="width"in e||"height"in e,f=this,d={},p=t.style,g=t.nodeType&&at(t),m=$.get(t,"fxshow");for(r in n.queue||(null==(a=_._queueHooks(t,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,f.always(function(){f.always(function(){a.unqueued--,_.queue(t,"fx").length||a.empty.fire()})})),e)if(oe.test(i=e[r])){if(delete e[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;g=!0}d[r]=m&&m[r]||_.style(t,r)}if((l=!_.isEmptyObject(e))||!_.isEmptyObject(d))for(r in h&&1===t.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=$.get(t,"display")),"none"===(c=_.css(t,"display"))&&(u?c=u:(ut([t],!0),u=t.style.display||u,c=_.css(t,"display"),ut([t]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===_.css(t,"float")&&(l||(f.done(function(){p.display=u}),null==u&&(u="none"===(c=p.display)?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",f.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,d)l||(m?"hidden"in m&&(g=m.hidden):m=$.access(t,"fxshow",{display:u}),o&&(m.hidden=!g),g&&ut([t],!0),f.done(function(){for(r in g||ut([t]),$.remove(t,"fxshow"),d)_.style(t,r,d[r])})),l=ce(g?m[r]:0,r,f),r in m||(m[r]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?he.prefilters.unshift(t):he.prefilters.push(t)}}),_.speed=function(t,e,n){var r=t&&"object"==typeof t?_.extend({},t):{complete:n||!n&&e||p(t)&&t,duration:t,easing:n&&e||e&&!p(e)&&e};return _.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration=r.duration in _.fx.speeds?_.fx.speeds[r.duration]:_.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){p(r.old)&&r.old.call(this),r.queue&&_.dequeue(this,r.queue)},r},_.fn.extend({fadeTo:function(t,e,n,r){return this.filter(at).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(t,e,n,r){var i=_.isEmptyObject(t),o=_.speed(e,n,r),a=function(){var e=he(this,_.extend({},t),o);(i||$.get(this,"finish"))&&e.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(t,e,n){var r=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,i=null!=t&&t+"queueHooks",o=_.timers,a=$.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&ae.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=t&&o[i].queue!==t||(o[i].anim.stop(n),e=!1,o.splice(i,1));!e&&n||_.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,n=$.get(this),r=n[t+"queue"],i=n[t+"queueHooks"],o=_.timers,a=r?r.length:0;for(n.finish=!0,_.queue(this,t,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<a;e++)r[e]&&r[e].finish&&r[e].finish.call(this);delete n.finish})}}),_.each(["toggle","show","hide"],function(t,e){var n=_.fn[e];_.fn[e]=function(t,r,i){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(ue(e,!0),t,r,i)}}),_.each({slideDown:ue("show"),slideUp:ue("hide"),slideToggle:ue("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){_.fn[t]=function(t,n,r){return this.animate(e,t,n,r)}}),_.timers=[],_.fx.tick=function(){var t,e=0,n=_.timers;for(ee=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||_.fx.stop(),ee=void 0},_.fx.timer=function(t){_.timers.push(t),_.fx.start()},_.fx.interval=13,_.fx.start=function(){ne||(ne=!0,se())},_.fx.stop=function(){ne=null},_.fx.speeds={slow:600,fast:200,_default:400},_.fn.delay=function(e,n){return e=_.fx&&_.fx.speeds[e]||e,this.queue(n=n||"fx",function(n,r){var i=t.setTimeout(n,e);r.stop=function(){t.clearTimeout(i)}})},re=m.createElement("input"),ie=m.createElement("select").appendChild(m.createElement("option")),re.type="checkbox",d.checkOn=""!==re.value,d.optSelected=ie.selected,(re=m.createElement("input")).value="t",re.type="radio",d.radioValue="t"===re.value;var fe,de=_.expr.attrHandle;_.fn.extend({attr:function(t,e){return W(this,_.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){_.removeAttr(this,t)})}}),_.extend({attr:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?_.prop(t,e,n):(1===o&&_.isXMLDoc(t)||(i=_.attrHooks[e.toLowerCase()]||(_.expr.match.bool.test(e)?fe:void 0)),void 0!==n?null===n?void _.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):i&&"get"in i&&null!==(r=i.get(t,e))?r:null==(r=_.find.attr(t,e))?void 0:r)},attrHooks:{type:{set:function(t,e){if(!d.radioValue&&"radio"===e&&k(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,r=0,i=e&&e.match(P);if(i&&1===t.nodeType)for(;n=i[r++];)t.removeAttribute(n)}}),fe={set:function(t,e,n){return!1===e?_.removeAttr(t,n):t.setAttribute(n,n),n}},_.each(_.expr.match.bool.source.match(/\w+/g),function(t,e){var n=de[e]||_.find.attr;de[e]=function(t,e,r){var i,o,a=e.toLowerCase();return r||(o=de[a],de[a]=i,i=null!=n(t,e,r)?a:null,de[a]=o),i}});var pe=/^(?:input|select|textarea|button)$/i,ge=/^(?:a|area)$/i;function me(t){return(t.match(P)||[]).join(" ")}function ve(t){return t.getAttribute&&t.getAttribute("class")||""}function ye(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(P)||[]}_.fn.extend({prop:function(t,e){return W(this,_.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[_.propFix[t]||t]})}}),_.extend({prop:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&_.isXMLDoc(t)||(i=_.propHooks[e=_.propFix[e]||e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=_.find.attr(t,"tabindex");return e?parseInt(e,10):pe.test(t.nodeName)||ge.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),d.optSelected||(_.propHooks.selected={get:function(t){return null},set:function(t){}}),_.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){_.propFix[this.toLowerCase()]=this}),_.fn.extend({addClass:function(t){var e,n,r,i,o,a,s,l=0;if(p(t))return this.each(function(e){_(this).addClass(t.call(this,e,ve(this)))});if((e=ye(t)).length)for(;n=this[l++];)if(i=ve(n),r=1===n.nodeType&&" "+me(i)+" "){for(a=0;o=e[a++];)r.indexOf(" "+o+" ")<0&&(r+=o+" ");i!==(s=me(r))&&n.setAttribute("class",s)}return this},removeClass:function(t){var e,n,r,i,o,a,s,l=0;if(p(t))return this.each(function(e){_(this).removeClass(t.call(this,e,ve(this)))});if(!arguments.length)return this.attr("class","");if((e=ye(t)).length)for(;n=this[l++];)if(i=ve(n),r=1===n.nodeType&&" "+me(i)+" "){for(a=0;o=e[a++];)for(;-1<r.indexOf(" "+o+" ");)r=r.replace(" "+o+" "," ");i!==(s=me(r))&&n.setAttribute("class",s)}return this},toggleClass:function(t,e){var n=typeof t,r="string"===n||Array.isArray(t);return"boolean"==typeof e&&r?e?this.addClass(t):this.removeClass(t):p(t)?this.each(function(n){_(this).toggleClass(t.call(this,n,ve(this),e),e)}):this.each(function(){var e,i,o,a;if(r)for(i=0,o=_(this),a=ye(t);e=a[i++];)o.hasClass(e)?o.removeClass(e):o.addClass(e);else void 0!==t&&"boolean"!==n||((e=ve(this))&&$.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===t?"":$.get(this,"__className__")||""))})},hasClass:function(t){var e,n,r=0;for(e=" "+t+" ";n=this[r++];)if(1===n.nodeType&&-1<(" "+me(ve(n))+" ").indexOf(e))return!0;return!1}});var be=/\r/g;_.fn.extend({val:function(t){var e,n,r,i=this[0];return arguments.length?(r=p(t),this.each(function(n){var i;1===this.nodeType&&(null==(i=r?t.call(this,n,_(this).val()):t)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=_.map(i,function(t){return null==t?"":t+""})),(e=_.valHooks[this.type]||_.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,i,"value")||(this.value=i))})):i?(e=_.valHooks[i.type]||_.valHooks[i.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(be,""):null==n?"":n:void 0}}),_.extend({valHooks:{option:{get:function(t){var e=_.find.attr(t,"value");return null!=e?e:me(_.text(t))}},select:{get:function(t){var e,n,r,i=t.options,o=t.selectedIndex,a="select-one"===t.type,s=a?null:[],l=a?o+1:i.length;for(r=o<0?l:a?o:0;r<l;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!k(n.parentNode,"optgroup"))){if(e=_(n).val(),a)return e;s.push(e)}return s},set:function(t,e){for(var n,r,i=t.options,o=_.makeArray(e),a=i.length;a--;)((r=i[a]).selected=-1<_.inArray(_.valHooks.option.get(r),o))&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),_.each(["radio","checkbox"],function(){_.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<_.inArray(_(t).val(),e)}},d.checkOn||(_.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),d.focusin="onfocusin"in t;var xe=/^(?:focusinfocus|focusoutblur)$/,_e=function(t){t.stopPropagation()};_.extend(_.event,{trigger:function(e,n,r,i){var o,a,s,l,u,h,f,d,v=[r||m],y=c.call(e,"type")?e.type:e,b=c.call(e,"namespace")?e.namespace.split("."):[];if(a=d=s=r=r||m,3!==r.nodeType&&8!==r.nodeType&&!xe.test(y+_.event.triggered)&&(-1<y.indexOf(".")&&(y=(b=y.split(".")).shift(),b.sort()),u=y.indexOf(":")<0&&"on"+y,(e=e[_.expando]?e:new _.Event(y,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=b.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),n=null==n?[e]:_.makeArray(n,[e]),f=_.event.special[y]||{},i||!f.trigger||!1!==f.trigger.apply(r,n))){if(!i&&!f.noBubble&&!g(r)){for(xe.test((l=f.delegateType||y)+y)||(a=a.parentNode);a;a=a.parentNode)v.push(a),s=a;s===(r.ownerDocument||m)&&v.push(s.defaultView||s.parentWindow||t)}for(o=0;(a=v[o++])&&!e.isPropagationStopped();)d=a,e.type=1<o?l:f.bindType||y,(h=($.get(a,"events")||Object.create(null))[e.type]&&$.get(a,"handle"))&&h.apply(a,n),(h=u&&a[u])&&h.apply&&Y(a)&&(e.result=h.apply(a,n),!1===e.result&&e.preventDefault());return e.type=y,i||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(v.pop(),n)||!Y(r)||u&&p(r[y])&&!g(r)&&((s=r[u])&&(r[u]=null),_.event.triggered=y,e.isPropagationStopped()&&d.addEventListener(y,_e),r[y](),e.isPropagationStopped()&&d.removeEventListener(y,_e),_.event.triggered=void 0,s&&(r[u]=s)),e.result}},simulate:function(t,e,n){var r=_.extend(new _.Event,n,{type:t,isSimulated:!0});_.event.trigger(r,null,e)}}),_.fn.extend({trigger:function(t,e){return this.each(function(){_.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];if(n)return _.event.trigger(t,e,n,!0)}}),d.focusin||_.each({focus:"focusin",blur:"focusout"},function(t,e){var n=function(t){_.event.simulate(e,t.target,_.event.fix(t))};_.event.special[e]={setup:function(){var r=this.ownerDocument||this.document||this,i=$.access(r,e);i||r.addEventListener(t,n,!0),$.access(r,e,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=$.access(r,e)-1;i?$.access(r,e,i):(r.removeEventListener(t,n,!0),$.remove(r,e))}}});var we=t.location,Se={guid:Date.now()},Ce=/\?/;_.parseXML=function(e){var n;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||_.error("Invalid XML: "+e),n};var Te=/\[\]$/,De=/\r?\n/g,ke=/^(?:submit|button|image|reset|file)$/i,Ee=/^(?:input|select|textarea|keygen)/i;function Ae(t,e,n,r){var i;if(Array.isArray(e))_.each(e,function(e,i){n||Te.test(t)?r(t,i):Ae(t+"["+("object"==typeof i&&null!=i?e:"")+"]",i,n,r)});else if(n||"object"!==b(e))r(t,e);else for(i in e)Ae(t+"["+i+"]",e[i],n,r)}_.param=function(t,e){var n,r=[],i=function(t,e){var n=p(e)?e():e;r[r.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!_.isPlainObject(t))_.each(t,function(){i(this.name,this.value)});else for(n in t)Ae(n,t[n],e,i);return r.join("&")},_.fn.extend({serialize:function(){return _.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=_.prop(this,"elements");return t?_.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!_(this).is(":disabled")&&Ee.test(this.nodeName)&&!ke.test(t)&&(this.checked||!ft.test(t))}).map(function(t,e){var n=_(this).val();return null==n?null:Array.isArray(n)?_.map(n,function(t){return{name:e.name,value:t.replace(De,"\r\n")}}):{name:e.name,value:n.replace(De,"\r\n")}}).get()}});var Le=/%20/g,Ie=/#.*$/,Ne=/([?&])_=[^&]*/,Fe=/^(.*?):[ \t]*([^\r\n]*)$/gm,je=/^(?:GET|HEAD)$/,Pe=/^\/\//,Me={},Re={},Oe="*/".concat("*"),Be=m.createElement("a");function He(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var r,i=0,o=e.toLowerCase().match(P)||[];if(p(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(t[r]=t[r]||[]).unshift(n)):(t[r]=t[r]||[]).push(n)}}function qe(t,e,n,r){var i={},o=t===Re;function a(s){var l;return i[s]=!0,_.each(t[s]||[],function(t,s){var u=s(e,n,r);return"string"!=typeof u||o||i[u]?o?!(l=u):void 0:(e.dataTypes.unshift(u),a(u),!1)}),l}return a(e.dataTypes[0])||!i["*"]&&a("*")}function We(t,e){var n,r,i=_.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r||(r={}))[n]=e[n]);return r&&_.extend(!0,t,r),t}Be.href=we.href,_.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:we.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(we.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Oe,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":_.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?We(We(t,_.ajaxSettings),e):We(_.ajaxSettings,t)},ajaxPrefilter:He(Me),ajaxTransport:He(Re),ajax:function(e,n){"object"==typeof e&&(n=e,e=void 0);var r,i,o,a,s,l,u,c,h,f,d=_.ajaxSetup({},n=n||{}),p=d.context||d,g=d.context&&(p.nodeType||p.jquery)?_(p):_.event,v=_.Deferred(),y=_.Callbacks("once memory"),b=d.statusCode||{},x={},w={},S="canceled",C={readyState:0,getResponseHeader:function(t){var e;if(u){if(!a)for(a={};e=Fe.exec(o);)a[e[1].toLowerCase()+" "]=(a[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=a[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return u?o:null},setRequestHeader:function(t,e){return null==u&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==u&&(d.mimeType=t),this},statusCode:function(t){var e;if(t)if(u)C.always(t[C.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||S;return r&&r.abort(e),T(0,e),this}};if(v.promise(C),d.url=((e||d.url||we.href)+"").replace(Pe,we.protocol+"//"),d.type=n.method||n.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(P)||[""],null==d.crossDomain){l=m.createElement("a");try{l.href=d.url,l.href=l.href,d.crossDomain=Be.protocol+"//"+Be.host!=l.protocol+"//"+l.host}catch(e){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=_.param(d.data,d.traditional)),qe(Me,d,n,C),u)return C;for(h in(c=_.event&&d.global)&&0==_.active++&&_.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!je.test(d.type),i=d.url.replace(Ie,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(Le,"+")):(f=d.url.slice(i.length),d.data&&(d.processData||"string"==typeof d.data)&&(i+=(Ce.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(i=i.replace(Ne,"$1"),f=(Ce.test(i)?"&":"?")+"_="+Se.guid+++f),d.url=i+f),d.ifModified&&(_.lastModified[i]&&C.setRequestHeader("If-Modified-Since",_.lastModified[i]),_.etag[i]&&C.setRequestHeader("If-None-Match",_.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||n.contentType)&&C.setRequestHeader("Content-Type",d.contentType),C.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Oe+"; q=0.01":""):d.accepts["*"]),d.headers)C.setRequestHeader(h,d.headers[h]);if(d.beforeSend&&(!1===d.beforeSend.call(p,C,d)||u))return C.abort();if(S="abort",y.add(d.complete),C.done(d.success),C.fail(d.error),r=qe(Re,d,n,C)){if(C.readyState=1,c&&g.trigger("ajaxSend",[C,d]),u)return C;d.async&&0<d.timeout&&(s=t.setTimeout(function(){C.abort("timeout")},d.timeout));try{u=!1,r.send(x,T)}catch(e){if(u)throw e;T(-1,e)}}else T(-1,"No Transport");function T(e,n,a,l){var h,f,m,x,w,S=n;u||(u=!0,s&&t.clearTimeout(s),r=void 0,o=l||"",C.readyState=0<e?4:0,h=200<=e&&e<300||304===e,a&&(x=function(t,e,n){for(var r,i,o,a,s=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||t.converters[i+" "+l[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==l[0]&&l.unshift(o),n[o]}(d,C,a)),!h&&-1<_.inArray("script",d.dataTypes)&&(d.converters["text script"]=function(){}),x=function(t,e,n,r){var i,o,a,s,l,u={},c=t.dataTypes.slice();if(c[1])for(a in t.converters)u[a.toLowerCase()]=t.converters[a];for(o=c.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!l&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=u[l+" "+o]||u["* "+o]))for(i in u)if((s=i.split(" "))[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[i]:!0!==u[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&t.throws)e=a(e);else try{e=a(e)}catch(t){return{state:"parsererror",error:a?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(d,x,C,h),h?(d.ifModified&&((w=C.getResponseHeader("Last-Modified"))&&(_.lastModified[i]=w),(w=C.getResponseHeader("etag"))&&(_.etag[i]=w)),204===e||"HEAD"===d.type?S="nocontent":304===e?S="notmodified":(S=x.state,f=x.data,h=!(m=x.error))):(m=S,!e&&S||(S="error",e<0&&(e=0))),C.status=e,C.statusText=(n||S)+"",h?v.resolveWith(p,[f,S,C]):v.rejectWith(p,[C,S,m]),C.statusCode(b),b=void 0,c&&g.trigger(h?"ajaxSuccess":"ajaxError",[C,d,h?f:m]),y.fireWith(p,[C,S]),c&&(g.trigger("ajaxComplete",[C,d]),--_.active||_.event.trigger("ajaxStop")))}return C},getJSON:function(t,e,n){return _.get(t,e,n,"json")},getScript:function(t,e){return _.get(t,void 0,e,"script")}}),_.each(["get","post"],function(t,e){_[e]=function(t,n,r,i){return p(n)&&(i=i||r,r=n,n=void 0),_.ajax(_.extend({url:t,type:e,dataType:i,data:n,success:r},_.isPlainObject(t)&&t))}}),_.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),_._evalUrl=function(t,e,n){return _.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){_.globalEval(t,e,n)}})},_.fn.extend({wrapAll:function(t){var e;return this[0]&&(p(t)&&(t=t.call(this[0])),e=_(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return p(t)?this.each(function(e){_(this).wrapInner(t.call(this,e))}):this.each(function(){var e=_(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=p(t);return this.each(function(n){_(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){_(this).replaceWith(this.childNodes)}),this}}),_.expr.pseudos.hidden=function(t){return!_.expr.pseudos.visible(t)},_.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},_.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(e){}};var ze={0:200,1223:204},Ue=_.ajaxSettings.xhr();d.cors=!!Ue&&"withCredentials"in Ue,d.ajax=Ue=!!Ue,_.ajaxTransport(function(e){var n,r;if(d.cors||Ue&&!e.crossDomain)return{send:function(i,o){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];for(a in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)s.setRequestHeader(a,i[a]);n=function(t){return function(){n&&(n=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===t?s.abort():"error"===t?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(ze[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),r=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&t.setTimeout(function(){n&&r()})},n=n("abort");try{s.send(e.hasContent&&e.data||null)}catch(i){if(n)throw i}},abort:function(){n&&n()}}}),_.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),_.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return _.globalEval(t),t}}}),_.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),_.ajaxTransport("script",function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(r,i){e=_("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&i("error"===t.type?404:200,t.type)}),m.head.appendChild(e[0])},abort:function(){n&&n()}}});var Ve,Xe=[],Ye=/(=)\?(?=&|$)|\?\?/;_.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Xe.pop()||_.expando+"_"+Se.guid++;return this[t]=!0,t}}),_.ajaxPrefilter("json jsonp",function(e,n,r){var i,o,a,s=!1!==e.jsonp&&(Ye.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ye.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=p(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Ye,"$1"+i):!1!==e.jsonp&&(e.url+=(Ce.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||_.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=t[i],t[i]=function(){a=arguments},r.always(function(){void 0===o?_(t).removeProp(i):t[i]=o,e[i]&&(e.jsonpCallback=n.jsonpCallback,Xe.push(i)),a&&p(o)&&o(a[0]),a=o=void 0}),"script"}),d.createHTMLDocument=((Ve=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ve.childNodes.length),_.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(d.createHTMLDocument?((r=(e=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,e.head.appendChild(r)):e=m),o=!n&&[],(i=E.exec(t))?[e.createElement(i[1])]:(i=bt([t],e,o),o&&o.length&&_(o).remove(),_.merge([],i.childNodes)));var r,i,o},_.fn.load=function(t,e,n){var r,i,o,a=this,s=t.indexOf(" ");return-1<s&&(r=me(t.slice(s)),t=t.slice(0,s)),p(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),0<a.length&&_.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done(function(t){o=arguments,a.html(r?_("<div>").append(_.parseHTML(t)).find(r):t)}).always(n&&function(t,e){a.each(function(){n.apply(this,o||[t.responseText,e,t])})}),this},_.expr.pseudos.animated=function(t){return _.grep(_.timers,function(e){return t===e.elem}).length},_.offset={setOffset:function(t,e,n){var r,i,o,a,s,l,u=_.css(t,"position"),c=_(t),h={};"static"===u&&(t.style.position="relative"),s=c.offset(),o=_.css(t,"top"),l=_.css(t,"left"),("absolute"===u||"fixed"===u)&&-1<(o+l).indexOf("auto")?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(l)||0),p(e)&&(e=e.call(t,n,_.extend({},s))),null!=e.top&&(h.top=e.top-s.top+a),null!=e.left&&(h.left=e.left-s.left+i),"using"in e?e.using.call(t,h):("number"==typeof h.top&&(h.top+="px"),"number"==typeof h.left&&(h.left+="px"),c.css(h))}},_.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){_.offset.setOffset(this,t,e)});var e,n,r=this[0];return r?r.getClientRects().length?{top:(e=r.getBoundingClientRect()).top+(n=r.ownerDocument.defaultView).pageYOffset,left:e.left+n.pageXOffset}:{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,r=this[0],i={top:0,left:0};if("fixed"===_.css(r,"position"))e=r.getBoundingClientRect();else{for(e=this.offset(),n=r.ownerDocument,t=r.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===_.css(t,"position");)t=t.parentNode;t&&t!==r&&1===t.nodeType&&((i=_(t).offset()).top+=_.css(t,"borderTopWidth",!0),i.left+=_.css(t,"borderLeftWidth",!0))}return{top:e.top-i.top-_.css(r,"marginTop",!0),left:e.left-i.left-_.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===_.css(t,"position");)t=t.offsetParent;return t||rt})}}),_.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n="pageYOffset"===e;_.fn[t]=function(r){return W(this,function(t,r,i){var o;if(g(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===i)return o?o[e]:t[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):t[r]=i},t,r,arguments.length)}}),_.each(["top","left"],function(t,e){_.cssHooks[e]=Wt(d.pixelPosition,function(t,n){if(n)return n=qt(t,e),Rt.test(n)?_(t).position()[e]+"px":n})}),_.each({Height:"height",Width:"width"},function(t,e){_.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,r){_.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===o?"margin":"border");return W(this,function(e,n,i){var o;return g(e)?0===r.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===i?_.css(e,n,s):_.style(e,n,i,s)},e,a?i:void 0,a)}})}),_.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){_.fn[e]=function(t){return this.on(e,t)}}),_.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),_.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){_.fn[e]=function(t,n){return 0<arguments.length?this.on(e,null,t,n):this.trigger(e)}});var Ge=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;_.proxy=function(t,e){var n,r,o;if("string"==typeof e&&(n=t[e],e=t,t=n),p(t))return r=i.call(arguments,2),(o=function(){return t.apply(e||this,r.concat(i.call(arguments)))}).guid=t.guid=t.guid||_.guid++,o},_.holdReady=function(t){t?_.readyWait++:_.ready(!0)},_.isArray=Array.isArray,_.parseJSON=JSON.parse,_.nodeName=k,_.isFunction=p,_.isWindow=g,_.camelCase=X,_.type=b,_.now=Date.now,_.isNumeric=function(t){var e=_.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},_.trim=function(t){return null==t?"":(t+"").replace(Ge,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return _});var $e=t.jQuery,Qe=t.$;return _.noConflict=function(e){return t.$===_&&(t.$=Qe),e&&t.jQuery===_&&(t.jQuery=$e),_},void 0===e&&(t.jQuery=t.$=_),_}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Popper=e()}(this,function(){"use strict";function t(t){return t&&"[object Function]"==={}.toString.call(t)}function e(t,e){if(1!==t.nodeType)return[];var n=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?n[e]:n}function n(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function r(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var i=e(t);return/(auto|scroll|overlay)/.test(i.overflow+i.overflowY+i.overflowX)?t:r(n(t))}function i(t){return t&&t.referenceNode?t.referenceNode:t}function o(t){return 11===t?$:10===t?Q:$||Q}function a(t){if(!t)return document.documentElement;for(var n=o(10)?document.body:null,r=t.offsetParent||null;r===n&&t.nextElementSibling;)r=(t=t.nextElementSibling).offsetParent;var i=r&&r.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(r.nodeName)&&"static"===e(r,"position")?a(r):r:t?t.ownerDocument.documentElement:document.documentElement}function s(t){return null===t.parentNode?t:s(t.parentNode)}function l(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,r=n?t:e,i=n?e:t,o=document.createRange();o.setStart(r,0),o.setEnd(i,0);var u=o.commonAncestorContainer;if(t!==u&&e!==u||r.contains(i))return function(t){var e=t.nodeName;return"BODY"!==e&&("HTML"===e||a(t.firstElementChild)===t)}(u)?u:a(u);var c=s(t);return c.host?l(c.host,e):l(t,s(e).host)}function u(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"top",n="top"===e?"scrollTop":"scrollLeft",r=t.nodeName;if("BODY"===r||"HTML"===r){var i=t.ownerDocument.documentElement,o=t.ownerDocument.scrollingElement||i;return o[n]}return t[n]}function c(t,e){var n=2<arguments.length&&void 0!==arguments[2]&&arguments[2],r=u(e,"top"),i=u(e,"left"),o=n?-1:1;return t.top+=r*o,t.bottom+=r*o,t.left+=i*o,t.right+=i*o,t}function h(t,e){var n="x"===e?"Left":"Top",r="Left"==n?"Right":"Bottom";return parseFloat(t["border"+n+"Width"])+parseFloat(t["border"+r+"Width"])}function f(t,e,n,r){return V(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],o(10)?parseInt(n["offset"+t])+parseInt(r["margin"+("Height"===t?"Top":"Left")])+parseInt(r["margin"+("Height"===t?"Bottom":"Right")]):0)}function d(t){var e=t.body,n=t.documentElement,r=o(10)&&getComputedStyle(n);return{height:f("Height",e,n,r),width:f("Width",e,n,r)}}function p(t){return tt({},t,{right:t.left+t.width,bottom:t.top+t.height})}function g(t){var n={};try{if(o(10)){n=t.getBoundingClientRect();var r=u(t,"top"),i=u(t,"left");n.top+=r,n.left+=i,n.bottom+=r,n.right+=i}else n=t.getBoundingClientRect()}catch(e){}var a={left:n.left,top:n.top,width:n.right-n.left,height:n.bottom-n.top},s="HTML"===t.nodeName?d(t.ownerDocument):{},l=t.offsetWidth-(s.width||t.clientWidth||a.width),c=t.offsetHeight-(s.height||t.clientHeight||a.height);if(l||c){var f=e(t);l-=h(f,"x"),c-=h(f,"y"),a.width-=l,a.height-=c}return p(a)}function m(t,n){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],a=o(10),s="HTML"===n.nodeName,l=g(t),u=g(n),h=r(t),f=e(n),d=parseFloat(f.borderTopWidth),m=parseFloat(f.borderLeftWidth);i&&s&&(u.top=V(u.top,0),u.left=V(u.left,0));var v=p({top:l.top-u.top-d,left:l.left-u.left-m,width:l.width,height:l.height});if(v.marginTop=0,v.marginLeft=0,!a&&s){var y=parseFloat(f.marginTop),b=parseFloat(f.marginLeft);v.top-=d-y,v.bottom-=d-y,v.left-=m-b,v.right-=m-b,v.marginTop=y,v.marginLeft=b}return(a&&!i?n.contains(h):n===h&&"BODY"!==h.nodeName)&&(v=c(v,n)),v}function v(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=t.ownerDocument.documentElement,r=m(t,n),i=V(n.clientWidth,window.innerWidth||0),o=V(n.clientHeight,window.innerHeight||0),a=e?0:u(n),s=e?0:u(n,"left"),l={top:a-r.top+r.marginTop,left:s-r.left+r.marginLeft,width:i,height:o};return p(l)}function y(t){var r=t.nodeName;if("BODY"===r||"HTML"===r)return!1;if("fixed"===e(t,"position"))return!0;var i=n(t);return!!i&&y(i)}function b(t){if(!t||!t.parentElement||o())return document.documentElement;for(var n=t.parentElement;n&&"none"===e(n,"transform");)n=n.parentElement;return n||document.documentElement}function x(t,e,o,a){var s=4<arguments.length&&void 0!==arguments[4]&&arguments[4],u={top:0,left:0},c=s?b(t):l(t,i(e));if("viewport"===a)u=v(c,s);else{var h;"scrollParent"===a?"BODY"===(h=r(n(e))).nodeName&&(h=t.ownerDocument.documentElement):h="window"===a?t.ownerDocument.documentElement:a;var f=m(h,c,s);if("HTML"!==h.nodeName||y(c))u=f;else{var p=d(t.ownerDocument),g=p.height,x=p.width;u.top+=f.top-f.marginTop,u.bottom=g+f.top,u.left+=f.left-f.marginLeft,u.right=x+f.left}}var _="number"==typeof(o=o||0);return u.left+=_?o:o.left||0,u.top+=_?o:o.top||0,u.right-=_?o:o.right||0,u.bottom-=_?o:o.bottom||0,u}function _(t){return t.width*t.height}function w(t,e,n,r,i){var o=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var a=x(n,r,o,i),s={top:{width:a.width,height:e.top-a.top},right:{width:a.right-e.right,height:a.height},bottom:{width:a.width,height:a.bottom-e.bottom},left:{width:e.left-a.left,height:a.height}},l=Object.keys(s).map(function(t){return tt({key:t},s[t],{area:_(s[t])})}).sort(function(t,e){return e.area-t.area}),u=l.filter(function(t){return t.width>=n.clientWidth&&t.height>=n.clientHeight}),c=0<u.length?u[0].key:l[0].key,h=t.split("-")[1];return c+(h?"-"+h:"")}function S(t,e,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,o=r?b(e):l(e,i(n));return m(n,o,r)}function C(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),n=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),r=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+r,height:t.offsetHeight+n}}function T(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function D(t,e,n){n=n.split("-")[0];var r=C(t),i={width:r.width,height:r.height},o=-1!==["right","left"].indexOf(n),a=o?"top":"left",s=o?"left":"top",l=o?"height":"width",u=o?"width":"height";return i[a]=e[a]+e[l]/2-r[l]/2,i[s]=n===s?e[s]-r[u]:e[T(s)],i}function k(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function E(e,n,r){return(void 0===r?e:e.slice(0,function(t,e,n){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===n});var r=k(t,function(t){return t[e]===n});return t.indexOf(r)}(e,"name",r))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var r=e.function||e.fn;e.enabled&&t(r)&&(n.offsets.popper=p(n.offsets.popper),n.offsets.reference=p(n.offsets.reference),n=r(n,e))}),n}function A(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=S(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=w(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=D(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=E(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}function L(t,e){return t.some(function(t){return t.enabled&&t.name===e})}function I(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),r=0;r<e.length;r++){var i=e[r],o=i?""+i+n:t;if(void 0!==document.body.style[o])return o}return null}function N(){return this.state.isDestroyed=!0,L(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[I("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function F(t){var e=t.ownerDocument;return e?e.defaultView:window}function j(t,e,n,i){var o="BODY"===t.nodeName,a=o?t.ownerDocument.defaultView:t;a.addEventListener(e,n,{passive:!0}),o||j(r(a.parentNode),e,n,i),i.push(a)}function P(t,e,n,i){n.updateBound=i,F(t).addEventListener("resize",n.updateBound,{passive:!0});var o=r(t);return j(o,"scroll",n.updateBound,n.scrollParents),n.scrollElement=o,n.eventsEnabled=!0,n}function M(){this.state.eventsEnabled||(this.state=P(this.reference,0,this.state,this.scheduleUpdate))}function R(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=function(t,e){return F(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e}(this.reference,this.state))}function O(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function B(t,e){Object.keys(e).forEach(function(n){var r="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&O(e[n])&&(r="px"),t.style[n]=e[n]+r})}function H(t,e,n){var r=k(t,function(t){return t.name===e}),i=!!r&&t.some(function(t){return t.name===n&&t.enabled&&t.order<r.order});if(!i){var o="`"+e+"`";console.warn("`"+n+"` modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return i}function q(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=rt.indexOf(t),r=rt.slice(n+1).concat(rt.slice(0,n));return e?r.reverse():r}var W=Math.min,z=Math.floor,U=Math.round,V=Math.max,X="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,Y=function(){for(var t=["Edge","Trident","Firefox"],e=0;e<t.length;e+=1)if(X&&0<=navigator.userAgent.indexOf(t[e]))return 1;return 0}(),G=X&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then(function(){e=!1,t()}))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},Y))}},$=X&&!(!window.MSInputMethodContext||!document.documentMode),Q=X&&/MSIE 10/.test(navigator.userAgent),J=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},K=function(){function t(t,e){for(var n,r=0;r<e.length;r++)(n=e[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Z=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},tt=Object.assign||function(t){for(var e,n=1;n<arguments.length;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},et=X&&/Firefox/i.test(navigator.userAgent),nt=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],rt=nt.slice(3),it=function(){function e(n,r){var i=this,o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};J(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=G(this.update.bind(this)),this.options=tt({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=n&&n.jquery?n[0]:n,this.popper=r&&r.jquery?r[0]:r,this.options.modifiers={},Object.keys(tt({},e.Defaults.modifiers,o.modifiers)).forEach(function(t){i.options.modifiers[t]=tt({},e.Defaults.modifiers[t]||{},o.modifiers?o.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return tt({name:t},i.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(e){e.enabled&&t(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();var a=this.options.eventsEnabled;a&&this.enableEventListeners(),this.state.eventsEnabled=a}return K(e,[{key:"update",value:function(){return A.call(this)}},{key:"destroy",value:function(){return N.call(this)}},{key:"enableEventListeners",value:function(){return M.call(this)}},{key:"disableEventListeners",value:function(){return R.call(this)}}]),e}();return it.Utils=("undefined"==typeof window?global:window).PopperUtils,it.placements=nt,it.Defaults={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,n=e.split("-")[0],r=e.split("-")[1];if(r){var i=t.offsets,o=i.reference,a=i.popper,s=-1!==["bottom","top"].indexOf(n),l=s?"left":"top",u=s?"width":"height",c={start:Z({},l,o[l]),end:Z({},l,o[l]+o[u]-a[u])};t.offsets.popper=tt({},a,c[r])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var n,r=e.offset,i=t.offsets,o=i.popper,a=i.reference,s=t.placement.split("-")[0];return n=O(+r)?[+r,0]:function(t,e,n,r){var i=[0,0],o=-1!==["right","left"].indexOf(r),a=t.split(/(\+|\-)/).map(function(t){return t.trim()}),s=a.indexOf(k(a,function(t){return-1!==t.search(/,|\s/)}));a[s]&&-1===a[s].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,u=-1===s?[a]:[a.slice(0,s).concat([a[s].split(l)[0]]),[a[s].split(l)[1]].concat(a.slice(s+1))];return(u=u.map(function(t,r){var i=(1===r?!o:o)?"height":"width",a=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,a=!0,t):a?(t[t.length-1]+=e,a=!1,t):t.concat(e)},[]).map(function(t){return function(t,e,n,r){var i=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+i[1],a=i[2];if(!o)return t;if(0===a.indexOf("%")){var s;switch(a){case"%p":s=n;break;case"%":case"%r":default:s=r}return p(s)[e]/100*o}return"vh"===a||"vw"===a?("vh"===a?V(document.documentElement.clientHeight,window.innerHeight||0):V(document.documentElement.clientWidth,window.innerWidth||0))/100*o:o}(t,i,e,n)})})).forEach(function(t,e){t.forEach(function(n,r){O(n)&&(i[e]+=n*("-"===t[r-1]?-1:1))})}),i}(r,o,a,s),"left"===s?(o.top+=n[0],o.left-=n[1]):"right"===s?(o.top+=n[0],o.left+=n[1]):"top"===s?(o.left+=n[0],o.top-=n[1]):"bottom"===s&&(o.left+=n[0],o.top+=n[1]),t.popper=o,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var n=e.boundariesElement||a(t.instance.popper);t.instance.reference===n&&(n=a(n));var r=I("transform"),i=t.instance.popper.style,o=i.top,s=i.left,l=i[r];i.top="",i.left="",i[r]="";var u=x(t.instance.popper,t.instance.reference,e.padding,n,t.positionFixed);i.top=o,i.left=s,i[r]=l,e.boundaries=u;var c=t.offsets.popper,h={primary:function(t){var n=c[t];return c[t]<u[t]&&!e.escapeWithReference&&(n=V(c[t],u[t])),Z({},t,n)},secondary:function(t){var n="right"===t?"left":"top",r=c[n];return c[t]>u[t]&&!e.escapeWithReference&&(r=W(c[n],u[t]-("right"===t?c.width:c.height))),Z({},n,r)}};return e.priority.forEach(function(t){var e=-1===["left","top"].indexOf(t)?"secondary":"primary";c=tt({},c,h[e](t))}),t.offsets.popper=c,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,r=e.reference,i=t.placement.split("-")[0],o=z,a=-1!==["top","bottom"].indexOf(i),s=a?"right":"bottom",l=a?"left":"top",u=a?"width":"height";return n[s]<o(r[l])&&(t.offsets.popper[l]=o(r[l])-n[u]),n[l]>o(r[s])&&(t.offsets.popper[l]=o(r[s])),t}},arrow:{order:500,enabled:!0,fn:function(t,n){var r;if(!H(t.instance.modifiers,"arrow","keepTogether"))return t;var i=n.element;if("string"==typeof i){if(!(i=t.instance.popper.querySelector(i)))return t}else if(!t.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var o=t.placement.split("-")[0],a=t.offsets,s=a.popper,l=a.reference,u=-1!==["left","right"].indexOf(o),c=u?"height":"width",h=u?"Top":"Left",f=h.toLowerCase(),d=u?"left":"top",g=u?"bottom":"right",m=C(i)[c];l[g]-m<s[f]&&(t.offsets.popper[f]-=s[f]-(l[g]-m)),l[f]+m>s[g]&&(t.offsets.popper[f]+=l[f]+m-s[g]),t.offsets.popper=p(t.offsets.popper);var v=l[f]+l[c]/2-m/2,y=e(t.instance.popper),b=parseFloat(y["margin"+h]),x=parseFloat(y["border"+h+"Width"]),_=v-t.offsets.popper[f]-b-x;return _=V(W(s[c]-m,_),0),t.arrowElement=i,t.offsets.arrow=(Z(r={},f,U(_)),Z(r,d,""),r),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(L(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var n=x(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement,t.positionFixed),r=t.placement.split("-")[0],i=T(r),o=t.placement.split("-")[1]||"",a=[];switch(e.behavior){case"flip":a=[r,i];break;case"clockwise":a=q(r);break;case"counterclockwise":a=q(r,!0);break;default:a=e.behavior}return a.forEach(function(s,l){if(r!==s||a.length===l+1)return t;r=t.placement.split("-")[0],i=T(r);var u=t.offsets.popper,c=t.offsets.reference,h=z,f="left"===r&&h(u.right)>h(c.left)||"right"===r&&h(u.left)<h(c.right)||"top"===r&&h(u.bottom)>h(c.top)||"bottom"===r&&h(u.top)<h(c.bottom),d=h(u.left)<h(n.left),p=h(u.right)>h(n.right),g=h(u.top)<h(n.top),m=h(u.bottom)>h(n.bottom),v="left"===r&&d||"right"===r&&p||"top"===r&&g||"bottom"===r&&m,y=-1!==["top","bottom"].indexOf(r),b=!!e.flipVariations&&(y&&"start"===o&&d||y&&"end"===o&&p||!y&&"start"===o&&g||!y&&"end"===o&&m)||!!e.flipVariationsByContent&&(y&&"start"===o&&p||y&&"end"===o&&d||!y&&"start"===o&&m||!y&&"end"===o&&g);(f||v||b)&&(t.flipped=!0,(f||v)&&(r=a[l+1]),b&&(o=function(t){return"end"===t?"start":"start"===t?"end":t}(o)),t.placement=r+(o?"-"+o:""),t.offsets.popper=tt({},t.offsets.popper,D(t.instance.popper,t.offsets.reference,t.placement)),t=E(t.instance.modifiers,t,"flip"))}),t},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],r=t.offsets,i=r.popper,o=r.reference,a=-1!==["left","right"].indexOf(n),s=-1===["top","left"].indexOf(n);return i[a?"left":"top"]=o[n]-(s?i[a?"width":"height"]:0),t.placement=T(e),t.offsets.popper=p(i),t}},hide:{order:800,enabled:!0,fn:function(t){if(!H(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=k(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,r=e.y,i=t.offsets.popper,o=k(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s,l,u=void 0===o?e.gpuAcceleration:o,c=a(t.instance.popper),h=g(c),f={position:i.position},d=function(t,e){var n=t.offsets,r=n.popper,i=U,o=function(t){return t},a=i(n.reference.width),s=i(r.width),l=-1!==["left","right"].indexOf(t.placement),u=-1!==t.placement.indexOf("-"),c=e?l||u||a%2==s%2?i:z:o,h=e?i:o;return{left:c(1==a%2&&1==s%2&&!u&&e?r.left-1:r.left),top:h(r.top),bottom:h(r.bottom),right:c(r.right)}}(t,2>window.devicePixelRatio||!et),p="bottom"===n?"top":"bottom",m="right"===r?"left":"right",v=I("transform");if(l="bottom"==p?"HTML"===c.nodeName?-c.clientHeight+d.bottom:-h.height+d.bottom:d.top,s="right"==m?"HTML"===c.nodeName?-c.clientWidth+d.right:-h.width+d.right:d.left,u&&v)f[v]="translate3d("+s+"px, "+l+"px, 0)",f[p]=0,f[m]=0,f.willChange="transform";else{var y="right"==m?-1:1;f[p]=l*("bottom"==p?-1:1),f[m]=s*y,f.willChange=p+", "+m}return t.attributes=tt({},{"x-placement":t.placement},t.attributes),t.styles=tt({},f,t.styles),t.arrowStyles=tt({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){return B(t.instance.popper,t.styles),function(t,e){Object.keys(e).forEach(function(n){!1===e[n]?t.removeAttribute(n):t.setAttribute(n,e[n])})}(t.instance.popper,t.attributes),t.arrowElement&&Object.keys(t.arrowStyles).length&&B(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,r,i){var o=S(i,e,t,n.positionFixed),a=w(n.placement,o,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",a),B(e,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},it}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap={},t.jQuery,t.Popper)}(this,function(t,e,n){"use strict";function r(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function i(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}function o(){return(o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e,n=n&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n;var a={TRANSITION_END:"bsTransitionEnd",getUID:function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},getSelectorFromElement:function(t){var e=t.getAttribute("data-target");if(!e||"#"===e){var n=t.getAttribute("href");e=n&&"#"!==n?n.trim():""}try{return document.querySelector(e)?e:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var n=e(t).css("transition-duration"),r=e(t).css("transition-delay"),i=parseFloat(n),o=parseFloat(r);return i||o?(n=n.split(",")[0],r=r.split(",")[0],1e3*(parseFloat(n)+parseFloat(r))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){e(t).trigger("transitionend")},supportsTransitionEnd:function(){return Boolean("transitionend")},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var r in n)if(Object.prototype.hasOwnProperty.call(n,r)){var i=n[r],o=e[r],s=o&&a.isElement(o)?"element":null===(l=o)||void 0===l?""+l:{}.toString.call(l).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(s))throw new Error(t.toUpperCase()+': Option "'+r+'" provided type "'+s+'" but expected type "'+i+'".')}var l},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){var e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?a.findShadowRoot(t.parentNode):null},jQueryDetection:function(){if(void 0===e)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=e.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||t[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};a.jQueryDetection(),e.fn.emulateTransitionEnd=function(t){var n=this,r=!1;return e(this).one(a.TRANSITION_END,function(){r=!0}),setTimeout(function(){r||a.triggerTransitionEnd(n)},t),this},e.event.special[a.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(t){if(e(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var s="alert",l=e.fn[s],u=function(){function t(t){this._element=t}var n=t.prototype;return n.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},n.dispose=function(){e.removeData(this._element,"bs.alert"),this._element=null},n._getRootElement=function(t){var n=a.getSelectorFromElement(t),r=!1;return n&&(r=document.querySelector(n)),r||(r=e(t).closest(".alert")[0]),r},n._triggerCloseEvent=function(t){var n=e.Event("close.bs.alert");return e(t).trigger(n),n},n._removeElement=function(t){var n=this;if(e(t).removeClass("show"),e(t).hasClass("fade")){var r=a.getTransitionDurationFromElement(t);e(t).one(a.TRANSITION_END,function(e){return n._destroyElement(t,e)}).emulateTransitionEnd(r)}else this._destroyElement(t)},n._destroyElement=function(t){e(t).detach().trigger("closed.bs.alert").remove()},t._jQueryInterface=function(n){return this.each(function(){var r=e(this),i=r.data("bs.alert");i||(i=new t(this),r.data("bs.alert",i)),"close"===n&&i[n](this)})},t._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}}]),t}();e(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',u._handleDismiss(new u)),e.fn[s]=u._jQueryInterface,e.fn[s].Constructor=u,e.fn[s].noConflict=function(){return e.fn[s]=l,u._jQueryInterface};var c=e.fn.button,h=function(){function t(t){this._element=t}var n=t.prototype;return n.toggle=function(){var t=!0,n=!0,r=e(this._element).closest('[data-toggle="buttons"]')[0];if(r){var i=this._element.querySelector('input:not([type="hidden"])');if(i){if("radio"===i.type)if(i.checked&&this._element.classList.contains("active"))t=!1;else{var o=r.querySelector(".active");o&&e(o).removeClass("active")}t&&("checkbox"!==i.type&&"radio"!==i.type||(i.checked=!this._element.classList.contains("active")),e(i).trigger("change")),i.focus(),n=!1}}this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),t&&e(this._element).toggleClass("active"))},n.dispose=function(){e.removeData(this._element,"bs.button"),this._element=null},t._jQueryInterface=function(n){return this.each(function(){var r=e(this).data("bs.button");r||(r=new t(this),e(this).data("bs.button",r)),"toggle"===n&&r[n]()})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}}]),t}();e(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(t){var n=t.target,r=n;if(e(n).hasClass("btn")||(n=e(n).closest(".btn")[0]),!n||n.hasAttribute("disabled")||n.classList.contains("disabled"))t.preventDefault();else{var i=n.querySelector('input:not([type="hidden"])');if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void t.preventDefault();("LABEL"!==r.tagName||i&&"checkbox"!==i.type)&&h._jQueryInterface.call(e(n),"toggle")}}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(t){var n=e(t.target).closest(".btn")[0];e(n).toggleClass("focus",/^focus(in)?$/.test(t.type))}),e(window).on("load.bs.button.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,n=t.length;e<n;e++){var r=t[e],i=r.querySelector('input:not([type="hidden"])');i.checked||i.hasAttribute("checked")?r.classList.add("active"):r.classList.remove("active")}for(var o=0,a=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var s=t[o];"true"===s.getAttribute("aria-pressed")?s.classList.add("active"):s.classList.remove("active")}}),e.fn.button=h._jQueryInterface,e.fn.button.Constructor=h,e.fn.button.noConflict=function(){return e.fn.button=c,h._jQueryInterface};var f="carousel",d=e.fn[f],p={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},g={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},m={TOUCH:"touch",PEN:"pen"},v=function(){function t(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var n=t.prototype;return n.next=function(){this._isSliding||this._slide("next")},n.nextWhenVisible=function(){!document.hidden&&e(this._element).is(":visible")&&"hidden"!==e(this._element).css("visibility")&&this.next()},n.prev=function(){this._isSliding||this._slide("prev")},n.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(a.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},n.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},n.to=function(t){var n=this;this._activeElement=this._element.querySelector(".active.carousel-item");var r=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)e(this._element).one("slid.bs.carousel",function(){return n.to(t)});else{if(r===t)return this.pause(),void this.cycle();this._slide(t>r?"next":"prev",this._items[t])}},n.dispose=function(){e(this._element).off(".bs.carousel"),e.removeData(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},n._getConfig=function(t){return t=o({},p,t),a.typeCheckConfig(f,t,g),t},n._handleSwipe=function(){var t=Math.abs(this.touchDeltaX);if(!(t<=40)){var e=t/this.touchDeltaX;this.touchDeltaX=0,e>0&&this.prev(),e<0&&this.next()}},n._addEventListeners=function(){var t=this;this._config.keyboard&&e(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&e(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},n._addTouchEventListeners=function(){var t=this;if(this._touchSupported){var n=function(e){t._pointerEvent&&m[e.originalEvent.pointerType.toUpperCase()]?t.touchStartX=e.originalEvent.clientX:t._pointerEvent||(t.touchStartX=e.originalEvent.touches[0].clientX)},r=function(e){t._pointerEvent&&m[e.originalEvent.pointerType.toUpperCase()]&&(t.touchDeltaX=e.originalEvent.clientX-t.touchStartX),t._handleSwipe(),"hover"===t._config.pause&&(t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout(function(e){return t.cycle(e)},500+t._config.interval))};e(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(t){return t.preventDefault()}),this._pointerEvent?(e(this._element).on("pointerdown.bs.carousel",function(t){return n(t)}),e(this._element).on("pointerup.bs.carousel",function(t){return r(t)}),this._element.classList.add("pointer-event")):(e(this._element).on("touchstart.bs.carousel",function(t){return n(t)}),e(this._element).on("touchmove.bs.carousel",function(e){return function(e){t.touchDeltaX=e.originalEvent.touches&&e.originalEvent.touches.length>1?0:e.originalEvent.touches[0].clientX-t.touchStartX}(e)}),e(this._element).on("touchend.bs.carousel",function(t){return r(t)}))}},n._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},n._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(t)},n._getItemByDirection=function(t,e){var n="next"===t,r="prev"===t,i=this._getItemIndex(e);if((r&&0===i||n&&i===this._items.length-1)&&!this._config.wrap)return e;var o=(i+("prev"===t?-1:1))%this._items.length;return-1===o?this._items[this._items.length-1]:this._items[o]},n._triggerSlideEvent=function(t,n){var r=this._getItemIndex(t),i=this._getItemIndex(this._element.querySelector(".active.carousel-item")),o=e.Event("slide.bs.carousel",{relatedTarget:t,direction:n,from:i,to:r});return e(this._element).trigger(o),o},n._setActiveIndicatorElement=function(t){if(this._indicatorsElement){var n=[].slice.call(this._indicatorsElement.querySelectorAll(".active"));e(n).removeClass("active");var r=this._indicatorsElement.children[this._getItemIndex(t)];r&&e(r).addClass("active")}},n._slide=function(t,n){var r,i,o,s=this,l=this._element.querySelector(".active.carousel-item"),u=this._getItemIndex(l),c=n||l&&this._getItemByDirection(t,l),h=this._getItemIndex(c),f=Boolean(this._interval);if("next"===t?(r="carousel-item-left",i="carousel-item-next",o="left"):(r="carousel-item-right",i="carousel-item-prev",o="right"),c&&e(c).hasClass("active"))this._isSliding=!1;else if(!this._triggerSlideEvent(c,o).isDefaultPrevented()&&l&&c){this._isSliding=!0,f&&this.pause(),this._setActiveIndicatorElement(c);var d=e.Event("slid.bs.carousel",{relatedTarget:c,direction:o,from:u,to:h});if(e(this._element).hasClass("slide")){e(c).addClass(i),a.reflow(c),e(l).addClass(r),e(c).addClass(r);var p=parseInt(c.getAttribute("data-interval"),10);p?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=p):this._config.interval=this._config.defaultInterval||this._config.interval;var g=a.getTransitionDurationFromElement(l);e(l).one(a.TRANSITION_END,function(){e(c).removeClass(r+" "+i).addClass("active"),e(l).removeClass("active "+i+" "+r),s._isSliding=!1,setTimeout(function(){return e(s._element).trigger(d)},0)}).emulateTransitionEnd(g)}else e(l).removeClass("active"),e(c).addClass("active"),this._isSliding=!1,e(this._element).trigger(d);f&&this.cycle()}},t._jQueryInterface=function(n){return this.each(function(){var r=e(this).data("bs.carousel"),i=o({},p,e(this).data());"object"==typeof n&&(i=o({},i,n));var a="string"==typeof n?n:i.slide;if(r||(r=new t(this,i),e(this).data("bs.carousel",r)),"number"==typeof n)r.to(n);else if("string"==typeof a){if(void 0===r[a])throw new TypeError('No method named "'+a+'"');r[a]()}else i.interval&&i.ride&&(r.pause(),r.cycle())})},t._dataApiClickHandler=function(n){var r=a.getSelectorFromElement(this);if(r){var i=e(r)[0];if(i&&e(i).hasClass("carousel")){var s=o({},e(i).data(),e(this).data()),l=this.getAttribute("data-slide-to");l&&(s.interval=!1),t._jQueryInterface.call(e(i),s),l&&e(i).data("bs.carousel").to(l),n.preventDefault()}}},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return p}}]),t}();e(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",v._dataApiClickHandler),e(window).on("load.bs.carousel.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),n=0,r=t.length;n<r;n++){var i=e(t[n]);v._jQueryInterface.call(i,i.data())}}),e.fn[f]=v._jQueryInterface,e.fn[f].Constructor=v,e.fn[f].noConflict=function(){return e.fn[f]=d,v._jQueryInterface};var y="collapse",b=e.fn[y],x={toggle:!0,parent:""},_={toggle:"boolean",parent:"(string|element)"},w=function(){function t(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]')),r=0,i=n.length;r<i;r++){var o=n[r],s=a.getSelectorFromElement(o),l=[].slice.call(document.querySelectorAll(s)).filter(function(e){return e===t});null!==s&&l.length>0&&(this._selector=s,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var n=t.prototype;return n.toggle=function(){e(this._element).hasClass("show")?this.hide():this.show()},n.show=function(){var n,r,i=this;if(!(this._isTransitioning||e(this._element).hasClass("show")||(this._parent&&0===(n=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(t){return"string"==typeof i._config.parent?t.getAttribute("data-parent")===i._config.parent:t.classList.contains("collapse")})).length&&(n=null),n&&(r=e(n).not(this._selector).data("bs.collapse"))&&r._isTransitioning))){var o=e.Event("show.bs.collapse");if(e(this._element).trigger(o),!o.isDefaultPrevented()){n&&(t._jQueryInterface.call(e(n).not(this._selector),"hide"),r||e(n).data("bs.collapse",null));var s=this._getDimension();e(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[s]=0,this._triggerArray.length&&e(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0);var l="scroll"+(s[0].toUpperCase()+s.slice(1)),u=a.getTransitionDurationFromElement(this._element);e(this._element).one(a.TRANSITION_END,function(){e(i._element).removeClass("collapsing").addClass("collapse show"),i._element.style[s]="",i.setTransitioning(!1),e(i._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(u),this._element.style[s]=this._element[l]+"px"}}},n.hide=function(){var t=this;if(!this._isTransitioning&&e(this._element).hasClass("show")){var n=e.Event("hide.bs.collapse");if(e(this._element).trigger(n),!n.isDefaultPrevented()){var r=this._getDimension();this._element.style[r]=this._element.getBoundingClientRect()[r]+"px",a.reflow(this._element),e(this._element).addClass("collapsing").removeClass("collapse show");var i=this._triggerArray.length;if(i>0)for(var o=0;o<i;o++){var s=this._triggerArray[o],l=a.getSelectorFromElement(s);null!==l&&(e([].slice.call(document.querySelectorAll(l))).hasClass("show")||e(s).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0),this._element.style[r]="";var u=a.getTransitionDurationFromElement(this._element);e(this._element).one(a.TRANSITION_END,function(){t.setTransitioning(!1),e(t._element).removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")}).emulateTransitionEnd(u)}}},n.setTransitioning=function(t){this._isTransitioning=t},n.dispose=function(){e.removeData(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},n._getConfig=function(t){return(t=o({},x,t)).toggle=Boolean(t.toggle),a.typeCheckConfig(y,t,_),t},n._getDimension=function(){return e(this._element).hasClass("width")?"width":"height"},n._getParent=function(){var n,r=this;a.isElement(this._config.parent)?(n=this._config.parent,void 0!==this._config.parent.jquery&&(n=this._config.parent[0])):n=document.querySelector(this._config.parent);var i=[].slice.call(n.querySelectorAll('[data-toggle="collapse"][data-parent="'+this._config.parent+'"]'));return e(i).each(function(e,n){r._addAriaAndCollapsedClass(t._getTargetFromElement(n),[n])}),n},n._addAriaAndCollapsedClass=function(t,n){var r=e(t).hasClass("show");n.length&&e(n).toggleClass("collapsed",!r).attr("aria-expanded",r)},t._getTargetFromElement=function(t){var e=a.getSelectorFromElement(t);return e?document.querySelector(e):null},t._jQueryInterface=function(n){return this.each(function(){var r=e(this),i=r.data("bs.collapse"),a=o({},x,r.data(),"object"==typeof n&&n?n:{});if(!i&&a.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(a.toggle=!1),i||(i=new t(this,a),r.data("bs.collapse",i)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return x}}]),t}();e(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var n=e(this),r=a.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(r));e(i).each(function(){var t=e(this),r=t.data("bs.collapse")?"toggle":n.data();w._jQueryInterface.call(t,r)})}),e.fn[y]=w._jQueryInterface,e.fn[y].Constructor=w,e.fn[y].noConflict=function(){return e.fn[y]=b,w._jQueryInterface};var S="dropdown",C=e.fn[S],T=new RegExp("38|40|27"),D={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},k={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},E=function(){function t(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var r=t.prototype;return r.toggle=function(){if(!this._element.disabled&&!e(this._element).hasClass("disabled")){var n=e(this._menu).hasClass("show");t._clearMenus(),n||this.show(!0)}},r.show=function(r){if(void 0===r&&(r=!1),!(this._element.disabled||e(this._element).hasClass("disabled")||e(this._menu).hasClass("show"))){var i={relatedTarget:this._element},o=e.Event("show.bs.dropdown",i),s=t._getParentFromElement(this._element);if(e(s).trigger(o),!o.isDefaultPrevented()){if(!this._inNavbar&&r){if(void 0===n)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var l=this._element;"parent"===this._config.reference?l=s:a.isElement(this._config.reference)&&(l=this._config.reference,void 0!==this._config.reference.jquery&&(l=this._config.reference[0])),"scrollParent"!==this._config.boundary&&e(s).addClass("position-static"),this._popper=new n(l,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===e(s).closest(".navbar-nav").length&&e(document.body).children().on("mouseover",null,e.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),e(this._menu).toggleClass("show"),e(s).toggleClass("show").trigger(e.Event("shown.bs.dropdown",i))}}},r.hide=function(){if(!this._element.disabled&&!e(this._element).hasClass("disabled")&&e(this._menu).hasClass("show")){var n={relatedTarget:this._element},r=e.Event("hide.bs.dropdown",n),i=t._getParentFromElement(this._element);e(i).trigger(r),r.isDefaultPrevented()||(this._popper&&this._popper.destroy(),e(this._menu).toggleClass("show"),e(i).toggleClass("show").trigger(e.Event("hidden.bs.dropdown",n)))}},r.dispose=function(){e.removeData(this._element,"bs.dropdown"),e(this._element).off(".bs.dropdown"),this._element=null,this._menu=null,null!==this._popper&&(this._popper.destroy(),this._popper=null)},r.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},r._addEventListeners=function(){var t=this;e(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},r._getConfig=function(t){return t=o({},this.constructor.Default,e(this._element).data(),t),a.typeCheckConfig(S,t,this.constructor.DefaultType),t},r._getMenuElement=function(){if(!this._menu){var e=t._getParentFromElement(this._element);e&&(this._menu=e.querySelector(".dropdown-menu"))}return this._menu},r._getPlacement=function(){var t=e(this._element.parentNode),n="bottom-start";return t.hasClass("dropup")?n=e(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":t.hasClass("dropright")?n="right-start":t.hasClass("dropleft")?n="left-start":e(this._menu).hasClass("dropdown-menu-right")&&(n="bottom-end"),n},r._detectNavbar=function(){return e(this._element).closest(".navbar").length>0},r._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},r._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),o({},t,this._config.popperConfig)},t._jQueryInterface=function(n){return this.each(function(){var r=e(this).data("bs.dropdown");if(r||(r=new t(this,"object"==typeof n?n:null),e(this).data("bs.dropdown",r)),"string"==typeof n){if(void 0===r[n])throw new TypeError('No method named "'+n+'"');r[n]()}})},t._clearMenus=function(n){if(!n||3!==n.which&&("keyup"!==n.type||9===n.which))for(var r=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]')),i=0,o=r.length;i<o;i++){var a=t._getParentFromElement(r[i]),s=e(r[i]).data("bs.dropdown"),l={relatedTarget:r[i]};if(n&&"click"===n.type&&(l.clickEvent=n),s){var u=s._menu;if(e(a).hasClass("show")&&!(n&&("click"===n.type&&/input|textarea/i.test(n.target.tagName)||"keyup"===n.type&&9===n.which)&&e.contains(a,n.target))){var c=e.Event("hide.bs.dropdown",l);e(a).trigger(c),c.isDefaultPrevented()||("ontouchstart"in document.documentElement&&e(document.body).children().off("mouseover",null,e.noop),r[i].setAttribute("aria-expanded","false"),s._popper&&s._popper.destroy(),e(u).removeClass("show"),e(a).removeClass("show").trigger(e.Event("hidden.bs.dropdown",l)))}}}},t._getParentFromElement=function(t){var e,n=a.getSelectorFromElement(t);return n&&(e=document.querySelector(n)),e||t.parentNode},t._dataApiKeydownHandler=function(n){if(!(/input|textarea/i.test(n.target.tagName)?32===n.which||27!==n.which&&(40!==n.which&&38!==n.which||e(n.target).closest(".dropdown-menu").length):!T.test(n.which))&&!this.disabled&&!e(this).hasClass("disabled")){var r=t._getParentFromElement(this),i=e(r).hasClass("show");if(i||27!==n.which){if(n.preventDefault(),n.stopPropagation(),!i||i&&(27===n.which||32===n.which))return 27===n.which&&e(r.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void e(this).trigger("click");var o=[].slice.call(r.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(t){return e(t).is(":visible")});if(0!==o.length){var a=o.indexOf(n.target);38===n.which&&a>0&&a--,40===n.which&&a<o.length-1&&a++,a<0&&(a=0),o[a].focus()}}}},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return D}},{key:"DefaultType",get:function(){return k}}]),t}();e(document).on("keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',E._dataApiKeydownHandler).on("keydown.bs.dropdown.data-api",".dropdown-menu",E._dataApiKeydownHandler).on("click.bs.dropdown.data-api keyup.bs.dropdown.data-api",E._clearMenus).on("click.bs.dropdown.data-api",'[data-toggle="dropdown"]',function(t){t.preventDefault(),t.stopPropagation(),E._jQueryInterface.call(e(this),"toggle")}).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}),e.fn[S]=E._jQueryInterface,e.fn[S].Constructor=E,e.fn[S].noConflict=function(){return e.fn[S]=C,E._jQueryInterface};var A=e.fn.modal,L={backdrop:!0,keyboard:!0,focus:!0,show:!0},I={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},N=function(){function t(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var n=t.prototype;return n.toggle=function(t){return this._isShown?this.hide():this.show(t)},n.show=function(t){var n=this;if(!this._isShown&&!this._isTransitioning){e(this._element).hasClass("fade")&&(this._isTransitioning=!0);var r=e.Event("show.bs.modal",{relatedTarget:t});e(this._element).trigger(r),this._isShown||r.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),e(this._element).on("click.dismiss.bs.modal",'[data-dismiss="modal"]',function(t){return n.hide(t)}),e(this._dialog).on("mousedown.dismiss.bs.modal",function(){e(n._element).one("mouseup.dismiss.bs.modal",function(t){e(t.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(t)}))}},n.hide=function(t){var n=this;if(t&&t.preventDefault(),this._isShown&&!this._isTransitioning){var r=e.Event("hide.bs.modal");if(e(this._element).trigger(r),this._isShown&&!r.isDefaultPrevented()){this._isShown=!1;var i=e(this._element).hasClass("fade");if(i&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),e(document).off("focusin.bs.modal"),e(this._element).removeClass("show"),e(this._element).off("click.dismiss.bs.modal"),e(this._dialog).off("mousedown.dismiss.bs.modal"),i){var o=a.getTransitionDurationFromElement(this._element);e(this._element).one(a.TRANSITION_END,function(t){return n._hideModal(t)}).emulateTransitionEnd(o)}else this._hideModal()}}},n.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return e(t).off(".bs.modal")}),e(document).off("focusin.bs.modal"),e.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},n.handleUpdate=function(){this._adjustDialog()},n._getConfig=function(t){return t=o({},L,t),a.typeCheckConfig("modal",t,I),t},n._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var n=e.Event("hidePrevented.bs.modal");if(e(this._element).trigger(n),n.defaultPrevented)return;var r=this._element.scrollHeight>document.documentElement.clientHeight;r||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static");var i=a.getTransitionDurationFromElement(this._dialog);e(this._element).off(a.TRANSITION_END),e(this._element).one(a.TRANSITION_END,function(){t._element.classList.remove("modal-static"),r||e(t._element).one(a.TRANSITION_END,function(){t._element.style.overflowY=""}).emulateTransitionEnd(t._element,i)}).emulateTransitionEnd(i),this._element.focus()}else this.hide()},n._showElement=function(t){var n=this,r=e(this._element).hasClass("fade"),i=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),e(this._dialog).hasClass("modal-dialog-scrollable")&&i?i.scrollTop=0:this._element.scrollTop=0,r&&a.reflow(this._element),e(this._element).addClass("show"),this._config.focus&&this._enforceFocus();var o=e.Event("shown.bs.modal",{relatedTarget:t}),s=function(){n._config.focus&&n._element.focus(),n._isTransitioning=!1,e(n._element).trigger(o)};if(r){var l=a.getTransitionDurationFromElement(this._dialog);e(this._dialog).one(a.TRANSITION_END,s).emulateTransitionEnd(l)}else s()},n._enforceFocus=function(){var t=this;e(document).off("focusin.bs.modal").on("focusin.bs.modal",function(n){document!==n.target&&t._element!==n.target&&0===e(t._element).has(n.target).length&&t._element.focus()})},n._setEscapeEvent=function(){var t=this;this._isShown?e(this._element).on("keydown.dismiss.bs.modal",function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||e(this._element).off("keydown.dismiss.bs.modal")},n._setResizeEvent=function(){var t=this;this._isShown?e(window).on("resize.bs.modal",function(e){return t.handleUpdate(e)}):e(window).off("resize.bs.modal")},n._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){e(document.body).removeClass("modal-open"),t._resetAdjustments(),t._resetScrollbar(),e(t._element).trigger("hidden.bs.modal")})},n._removeBackdrop=function(){this._backdrop&&(e(this._backdrop).remove(),this._backdrop=null)},n._showBackdrop=function(t){var n=this,r=e(this._element).hasClass("fade")?"fade":"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",r&&this._backdrop.classList.add(r),e(this._backdrop).appendTo(document.body),e(this._element).on("click.dismiss.bs.modal",function(t){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:t.target===t.currentTarget&&n._triggerBackdropTransition()}),r&&a.reflow(this._backdrop),e(this._backdrop).addClass("show"),!t)return;if(!r)return void t();var i=a.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(a.TRANSITION_END,t).emulateTransitionEnd(i)}else if(!this._isShown&&this._backdrop){e(this._backdrop).removeClass("show");var o=function(){n._removeBackdrop(),t&&t()};if(e(this._element).hasClass("fade")){var s=a.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(a.TRANSITION_END,o).emulateTransitionEnd(s)}else o()}else t&&t()},n._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},n._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},n._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},n._setScrollbar=function(){var t=this;if(this._isBodyOverflowing){var n=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),r=[].slice.call(document.querySelectorAll(".sticky-top"));e(n).each(function(n,r){var i=r.style.paddingRight,o=e(r).css("padding-right");e(r).data("padding-right",i).css("padding-right",parseFloat(o)+t._scrollbarWidth+"px")}),e(r).each(function(n,r){var i=r.style.marginRight,o=e(r).css("margin-right");e(r).data("margin-right",i).css("margin-right",parseFloat(o)-t._scrollbarWidth+"px")});var i=document.body.style.paddingRight,o=e(document.body).css("padding-right");e(document.body).data("padding-right",i).css("padding-right",parseFloat(o)+this._scrollbarWidth+"px")}e(document.body).addClass("modal-open")},n._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));e(t).each(function(t,n){var r=e(n).data("padding-right");e(n).removeData("padding-right"),n.style.paddingRight=r||""});var n=[].slice.call(document.querySelectorAll(".sticky-top"));e(n).each(function(t,n){var r=e(n).data("margin-right");void 0!==r&&e(n).css("margin-right",r).removeData("margin-right")});var r=e(document.body).data("padding-right");e(document.body).removeData("padding-right"),document.body.style.paddingRight=r||""},n._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},t._jQueryInterface=function(n,r){return this.each(function(){var i=e(this).data("bs.modal"),a=o({},L,e(this).data(),"object"==typeof n&&n?n:{});if(i||(i=new t(this,a),e(this).data("bs.modal",i)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n](r)}else a.show&&i.show(r)})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return L}}]),t}();e(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var n,r=this,i=a.getSelectorFromElement(this);i&&(n=document.querySelector(i));var s=e(n).data("bs.modal")?"toggle":o({},e(n).data(),e(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var l=e(n).one("show.bs.modal",function(t){t.isDefaultPrevented()||l.one("hidden.bs.modal",function(){e(r).is(":visible")&&r.focus()})});N._jQueryInterface.call(e(n),s,this)}),e.fn.modal=N._jQueryInterface,e.fn.modal.Constructor=N,e.fn.modal.noConflict=function(){return e.fn.modal=A,N._jQueryInterface};var F=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],j=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,P=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function M(t,e,n){if(0===t.length)return t;if(n&&"function"==typeof n)return n(t);for(var r=(new window.DOMParser).parseFromString(t,"text/html"),i=Object.keys(e),o=[].slice.call(r.body.querySelectorAll("*")),a=function(t,n){var r=o[t],a=r.nodeName.toLowerCase();if(-1===i.indexOf(r.nodeName.toLowerCase()))return r.parentNode.removeChild(r),"continue";var s=[].slice.call(r.attributes),l=[].concat(e["*"]||[],e[a]||[]);s.forEach(function(t){(function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===F.indexOf(n)||Boolean(t.nodeValue.match(j)||t.nodeValue.match(P));for(var r=e.filter(function(t){return t instanceof RegExp}),i=0,o=r.length;i<o;i++)if(n.match(r[i]))return!0;return!1})(t,l)||r.removeAttribute(t.nodeName)})},s=0,l=o.length;s<l;s++)a(s);return r.body.innerHTML}var R="tooltip",O=e.fn[R],B=new RegExp("(^|\\s)bs-tooltip\\S+","g"),H=["sanitize","whiteList","sanitizeFn"],q={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},W={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},z={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},popperConfig:null},U={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},V=function(){function t(t,e){if(void 0===n)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var r=t.prototype;return r.enable=function(){this._isEnabled=!0},r.disable=function(){this._isEnabled=!1},r.toggleEnabled=function(){this._isEnabled=!this._isEnabled},r.toggle=function(t){if(this._isEnabled)if(t){var n=this.constructor.DATA_KEY,r=e(t.currentTarget).data(n);r||(r=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(n,r)),r._activeTrigger.click=!r._activeTrigger.click,r._isWithActiveTrigger()?r._enter(null,r):r._leave(null,r)}else{if(e(this.getTipElement()).hasClass("show"))return void this._leave(null,this);this._enter(null,this)}},r.dispose=function(){clearTimeout(this._timeout),e.removeData(this.element,this.constructor.DATA_KEY),e(this.element).off(this.constructor.EVENT_KEY),e(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&e(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},r.show=function(){var t=this;if("none"===e(this.element).css("display"))throw new Error("Please use show on visible elements");var r=e.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){e(this.element).trigger(r);var i=a.findShadowRoot(this.element),o=e.contains(null!==i?i:this.element.ownerDocument.documentElement,this.element);if(r.isDefaultPrevented()||!o)return;var s=this.getTipElement(),l=a.getUID(this.constructor.NAME);s.setAttribute("id",l),this.element.setAttribute("aria-describedby",l),this.setContent(),this.config.animation&&e(s).addClass("fade");var u="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,c=this._getAttachment(u);this.addAttachmentClass(c);var h=this._getContainer();e(s).data(this.constructor.DATA_KEY,this),e.contains(this.element.ownerDocument.documentElement,this.tip)||e(s).appendTo(h),e(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new n(this.element,s,this._getPopperConfig(c)),e(s).addClass("show"),"ontouchstart"in document.documentElement&&e(document.body).children().on("mouseover",null,e.noop);var f=function(){t.config.animation&&t._fixTransition();var n=t._hoverState;t._hoverState=null,e(t.element).trigger(t.constructor.Event.SHOWN),"out"===n&&t._leave(null,t)};if(e(this.tip).hasClass("fade")){var d=a.getTransitionDurationFromElement(this.tip);e(this.tip).one(a.TRANSITION_END,f).emulateTransitionEnd(d)}else f()}},r.hide=function(t){var n=this,r=this.getTipElement(),i=e.Event(this.constructor.Event.HIDE),o=function(){"show"!==n._hoverState&&r.parentNode&&r.parentNode.removeChild(r),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),e(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),t&&t()};if(e(this.element).trigger(i),!i.isDefaultPrevented()){if(e(r).removeClass("show"),"ontouchstart"in document.documentElement&&e(document.body).children().off("mouseover",null,e.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,e(this.tip).hasClass("fade")){var s=a.getTransitionDurationFromElement(r);e(r).one(a.TRANSITION_END,o).emulateTransitionEnd(s)}else o();this._hoverState=""}},r.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},r.isWithContent=function(){return Boolean(this.getTitle())},r.addAttachmentClass=function(t){e(this.getTipElement()).addClass("bs-tooltip-"+t)},r.getTipElement=function(){return this.tip=this.tip||e(this.config.template)[0],this.tip},r.setContent=function(){var t=this.getTipElement();this.setElementContent(e(t.querySelectorAll(".tooltip-inner")),this.getTitle()),e(t).removeClass("fade show")},r.setElementContent=function(t,n){"object"!=typeof n||!n.nodeType&&!n.jquery?this.config.html?(this.config.sanitize&&(n=M(n,this.config.whiteList,this.config.sanitizeFn)),t.html(n)):t.text(n):this.config.html?e(n).parent().is(t)||t.empty().append(n):t.text(e(n).text())},r.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||(t="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),t},r._getPopperConfig=function(t){var e=this;return o({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}},this.config.popperConfig)},r._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=o({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},r._getContainer=function(){return!1===this.config.container?document.body:a.isElement(this.config.container)?e(this.config.container):e(document).find(this.config.container)},r._getAttachment=function(t){return W[t.toUpperCase()]},r._setListeners=function(){var t=this;this.config.trigger.split(" ").forEach(function(n){if("click"===n)e(t.element).on(t.constructor.Event.CLICK,t.config.selector,function(e){return t.toggle(e)});else if("manual"!==n){var r="hover"===n?t.constructor.Event.MOUSEENTER:t.constructor.Event.FOCUSIN,i="hover"===n?t.constructor.Event.MOUSELEAVE:t.constructor.Event.FOCUSOUT;e(t.element).on(r,t.config.selector,function(e){return t._enter(e)}).on(i,t.config.selector,function(e){return t._leave(e)})}}),this._hideModalHandler=function(){t.element&&t.hide()},e(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=o({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},r._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==t)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},r._enter=function(t,n){var r=this.constructor.DATA_KEY;(n=n||e(t.currentTarget).data(r))||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(r,n)),t&&(n._activeTrigger["focusin"===t.type?"focus":"hover"]=!0),e(n.getTipElement()).hasClass("show")||"show"===n._hoverState?n._hoverState="show":(clearTimeout(n._timeout),n._hoverState="show",n.config.delay&&n.config.delay.show?n._timeout=setTimeout(function(){"show"===n._hoverState&&n.show()},n.config.delay.show):n.show())},r._leave=function(t,n){var r=this.constructor.DATA_KEY;(n=n||e(t.currentTarget).data(r))||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(r,n)),t&&(n._activeTrigger["focusout"===t.type?"focus":"hover"]=!1),n._isWithActiveTrigger()||(clearTimeout(n._timeout),n._hoverState="out",n.config.delay&&n.config.delay.hide?n._timeout=setTimeout(function(){"out"===n._hoverState&&n.hide()},n.config.delay.hide):n.hide())},r._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},r._getConfig=function(t){var n=e(this.element).data();return Object.keys(n).forEach(function(t){-1!==H.indexOf(t)&&delete n[t]}),"number"==typeof(t=o({},this.constructor.Default,n,"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),a.typeCheckConfig(R,t,this.constructor.DefaultType),t.sanitize&&(t.template=M(t.template,t.whiteList,t.sanitizeFn)),t},r._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},r._cleanTipClass=function(){var t=e(this.getTipElement()),n=t.attr("class").match(B);null!==n&&n.length&&t.removeClass(n.join(""))},r._handlePopperPlacementChange=function(t){this.tip=t.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},r._fixTransition=function(){var t=this.getTipElement(),n=this.config.animation;null===t.getAttribute("x-placement")&&(e(t).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=n)},t._jQueryInterface=function(n){return this.each(function(){var r=e(this).data("bs.tooltip"),i="object"==typeof n&&n;if((r||!/dispose|hide/.test(n))&&(r||(r=new t(this,i),e(this).data("bs.tooltip",r)),"string"==typeof n)){if(void 0===r[n])throw new TypeError('No method named "'+n+'"');r[n]()}})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return z}},{key:"NAME",get:function(){return R}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return U}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return q}}]),t}();e.fn[R]=V._jQueryInterface,e.fn[R].Constructor=V,e.fn[R].noConflict=function(){return e.fn[R]=O,V._jQueryInterface};var X="popover",Y=e.fn[X],G=new RegExp("(^|\\s)bs-popover\\S+","g"),$=o({},V.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),Q=o({},V.DefaultType,{content:"(string|element|function)"}),J={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},K=function(t){var n,r;function o(){return t.apply(this,arguments)||this}r=t,(n=o).prototype=Object.create(r.prototype),n.prototype.constructor=n,n.__proto__=r;var a=o.prototype;return a.isWithContent=function(){return this.getTitle()||this._getContent()},a.addAttachmentClass=function(t){e(this.getTipElement()).addClass("bs-popover-"+t)},a.getTipElement=function(){return this.tip=this.tip||e(this.config.template)[0],this.tip},a.setContent=function(){var t=e(this.getTipElement());this.setElementContent(t.find(".popover-header"),this.getTitle());var n=this._getContent();"function"==typeof n&&(n=n.call(this.element)),this.setElementContent(t.find(".popover-body"),n),t.removeClass("fade show")},a._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},a._cleanTipClass=function(){var t=e(this.getTipElement()),n=t.attr("class").match(G);null!==n&&n.length>0&&t.removeClass(n.join(""))},o._jQueryInterface=function(t){return this.each(function(){var n=e(this).data("bs.popover"),r="object"==typeof t?t:null;if((n||!/dispose|hide/.test(t))&&(n||(n=new o(this,r),e(this).data("bs.popover",n)),"string"==typeof t)){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}})},i(o,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return $}},{key:"NAME",get:function(){return X}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return J}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return Q}}]),o}(V);e.fn[X]=K._jQueryInterface,e.fn[X].Constructor=K,e.fn[X].noConflict=function(){return e.fn[X]=Y,K._jQueryInterface};var Z="scrollspy",tt=e.fn[Z],et={offset:10,method:"auto",target:""},nt={offset:"number",method:"string",target:"(string|element)"},rt=function(){function t(t,n){var r=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(n),this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,e(this._scrollElement).on("scroll.bs.scrollspy",function(t){return r._process(t)}),this.refresh(),this._process()}var n=t.prototype;return n.refresh=function(){var t=this,n="auto"===this._config.method?this._scrollElement===this._scrollElement.window?"offset":"position":this._config.method,r="position"===n?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var i,o=a.getSelectorFromElement(t);if(o&&(i=document.querySelector(o)),i){var s=i.getBoundingClientRect();if(s.width||s.height)return[e(i)[n]().top+r,o]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},n.dispose=function(){e.removeData(this._element,"bs.scrollspy"),e(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},n._getConfig=function(t){if("string"!=typeof(t=o({},et,"object"==typeof t&&t?t:{})).target&&a.isElement(t.target)){var n=e(t.target).attr("id");n||(n=a.getUID(Z),e(t.target).attr("id",n)),t.target="#"+n}return a.typeCheckConfig(Z,t,nt),t},n._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},n._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},n._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},n._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),t>=n){var r=this._targets[this._targets.length-1];this._activeTarget!==r&&this._activate(r)}else{if(this._activeTarget&&t<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var i=this._offsets.length;i--;)this._activeTarget!==this._targets[i]&&t>=this._offsets[i]&&(void 0===this._offsets[i+1]||t<this._offsets[i+1])&&this._activate(this._targets[i])}},n._activate=function(t){this._activeTarget=t,this._clear();var n=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),r=e([].slice.call(document.querySelectorAll(n.join(","))));r.hasClass("dropdown-item")?(r.closest(".dropdown").find(".dropdown-toggle").addClass("active"),r.addClass("active")):(r.addClass("active"),r.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),r.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),e(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},n._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains("active")}).forEach(function(t){return t.classList.remove("active")})},t._jQueryInterface=function(n){return this.each(function(){var r=e(this).data("bs.scrollspy");if(r||(r=new t(this,"object"==typeof n&&n),e(this).data("bs.scrollspy",r)),"string"==typeof n){if(void 0===r[n])throw new TypeError('No method named "'+n+'"');r[n]()}})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return et}}]),t}();e(window).on("load.bs.scrollspy.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),n=t.length;n--;){var r=e(t[n]);rt._jQueryInterface.call(r,r.data())}}),e.fn[Z]=rt._jQueryInterface,e.fn[Z].Constructor=rt,e.fn[Z].noConflict=function(){return e.fn[Z]=tt,rt._jQueryInterface};var it=e.fn.tab,ot=function(){function t(t){this._element=t}var n=t.prototype;return n.show=function(){var t=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&e(this._element).hasClass("active")||e(this._element).hasClass("disabled"))){var n,r,i=e(this._element).closest(".nav, .list-group")[0],o=a.getSelectorFromElement(this._element);if(i){var s="UL"===i.nodeName||"OL"===i.nodeName?"> li > .active":".active";r=(r=e.makeArray(e(i).find(s)))[r.length-1]}var l=e.Event("hide.bs.tab",{relatedTarget:this._element}),u=e.Event("show.bs.tab",{relatedTarget:r});if(r&&e(r).trigger(l),e(this._element).trigger(u),!u.isDefaultPrevented()&&!l.isDefaultPrevented()){o&&(n=document.querySelector(o)),this._activate(this._element,i);var c=function(){var n=e.Event("hidden.bs.tab",{relatedTarget:t._element}),i=e.Event("shown.bs.tab",{relatedTarget:r});e(r).trigger(n),e(t._element).trigger(i)};n?this._activate(n,n.parentNode,c):c()}}},n.dispose=function(){e.removeData(this._element,"bs.tab"),this._element=null},n._activate=function(t,n,r){var i=this,o=(!n||"UL"!==n.nodeName&&"OL"!==n.nodeName?e(n).children(".active"):e(n).find("> li > .active"))[0],s=r&&o&&e(o).hasClass("fade"),l=function(){return i._transitionComplete(t,o,r)};if(o&&s){var u=a.getTransitionDurationFromElement(o);e(o).removeClass("show").one(a.TRANSITION_END,l).emulateTransitionEnd(u)}else l()},n._transitionComplete=function(t,n,r){if(n){e(n).removeClass("active");var i=e(n.parentNode).find("> .dropdown-menu .active")[0];i&&e(i).removeClass("active"),"tab"===n.getAttribute("role")&&n.setAttribute("aria-selected",!1)}if(e(t).addClass("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),a.reflow(t),t.classList.contains("fade")&&t.classList.add("show"),t.parentNode&&e(t.parentNode).hasClass("dropdown-menu")){var o=e(t).closest(".dropdown")[0];if(o){var s=[].slice.call(o.querySelectorAll(".dropdown-toggle"));e(s).addClass("active")}t.setAttribute("aria-expanded",!0)}r&&r()},t._jQueryInterface=function(n){return this.each(function(){var r=e(this),i=r.data("bs.tab");if(i||(i=new t(this),r.data("bs.tab",i)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}}]),t}();e(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(t){t.preventDefault(),ot._jQueryInterface.call(e(this),"show")}),e.fn.tab=ot._jQueryInterface,e.fn.tab.Constructor=ot,e.fn.tab.noConflict=function(){return e.fn.tab=it,ot._jQueryInterface};var at=e.fn.toast,st={animation:"boolean",autohide:"boolean",delay:"number"},lt={animation:!0,autohide:!0,delay:500},ut=function(){function t(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}var n=t.prototype;return n.show=function(){var t=this,n=e.Event("show.bs.toast");if(e(this._element).trigger(n),!n.isDefaultPrevented()){this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");var r=function(){t._element.classList.remove("showing"),t._element.classList.add("show"),e(t._element).trigger("shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout(function(){t.hide()},t._config.delay))};if(this._element.classList.remove("hide"),a.reflow(this._element),this._element.classList.add("showing"),this._config.animation){var i=a.getTransitionDurationFromElement(this._element);e(this._element).one(a.TRANSITION_END,r).emulateTransitionEnd(i)}else r()}},n.hide=function(){if(this._element.classList.contains("show")){var t=e.Event("hide.bs.toast");e(this._element).trigger(t),t.isDefaultPrevented()||this._close()}},n.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),e(this._element).off("click.dismiss.bs.toast"),e.removeData(this._element,"bs.toast"),this._element=null,this._config=null},n._getConfig=function(t){return t=o({},lt,e(this._element).data(),"object"==typeof t&&t?t:{}),a.typeCheckConfig("toast",t,this.constructor.DefaultType),t},n._setListeners=function(){var t=this;e(this._element).on("click.dismiss.bs.toast",'[data-dismiss="toast"]',function(){return t.hide()})},n._close=function(){var t=this,n=function(){t._element.classList.add("hide"),e(t._element).trigger("hidden.bs.toast")};if(this._element.classList.remove("show"),this._config.animation){var r=a.getTransitionDurationFromElement(this._element);e(this._element).one(a.TRANSITION_END,n).emulateTransitionEnd(r)}else n()},n._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},t._jQueryInterface=function(n){return this.each(function(){var r=e(this),i=r.data("bs.toast");if(i||(i=new t(this,"object"==typeof n&&n),r.data("bs.toast",i)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n](this)}})},i(t,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"DefaultType",get:function(){return st}},{key:"Default",get:function(){return lt}}]),t}();e.fn.toast=ut._jQueryInterface,e.fn.toast.Constructor=ut,e.fn.toast.noConflict=function(){return e.fn.toast=at,ut._jQueryInterface},t.Alert=u,t.Button=h,t.Carousel=v,t.Collapse=w,t.Dropdown=E,t.Modal=N,t.Popover=K,t.Scrollspy=rt,t.Tab=ot,t.Toast=ut,t.Tooltip=V,t.Util=a,Object.defineProperty(t,"__esModule",{value:!0})}),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e,window,document)}):"object"==typeof exports?module.exports=function(e,n){return e||(e=window),n||(n="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(n,e,e.document)}:t(jQuery,window,document)}(function(t,e,n,r){"use strict";var i,o,a,s,l=function(e){this.$=function(t,e){return this.api(!0).$(t,e)},this._=function(t,e){return this.api(!0).rows(t,e).data()},this.api=function(t){return new o(t?oe(this[i.iApiIndex]):this)},this.fnAddData=function(e,n){var i=this.api(!0),o=t.isArray(e)&&(t.isArray(e[0])||t.isPlainObject(e[0]))?i.rows.add(e):i.row.add(e);return(n===r||n)&&i.draw(),o.flatten().toArray()},this.fnAdjustColumnSizing=function(t){var e=this.api(!0).columns.adjust(),n=e.settings()[0],i=n.oScroll;t===r||t?e.draw(!1):""===i.sX&&""===i.sY||qt(n)},this.fnClearTable=function(t){var e=this.api(!0).clear();(t===r||t)&&e.draw()},this.fnClose=function(t){this.api(!0).row(t).child.hide()},this.fnDeleteRow=function(t,e,n){var i=this.api(!0),o=i.rows(t),a=o.settings()[0],s=a.aoData[o[0][0]];return o.remove(),e&&e.call(this,a,s),(n===r||n)&&i.draw(),s},this.fnDestroy=function(t){this.api(!0).destroy(t)},this.fnDraw=function(t){this.api(!0).draw(t)},this.fnFilter=function(t,e,n,i,o,a){var s=this.api(!0);null===e||e===r?s.search(t,n,i,a):s.column(e).search(t,n,i,a),s.draw()},this.fnGetData=function(t,e){var n=this.api(!0);if(t!==r){var i=t.nodeName?t.nodeName.toLowerCase():"";return e!==r||"td"==i||"th"==i?n.cell(t,e).data():n.row(t).data()||null}return n.data().toArray()},this.fnGetNodes=function(t){var e=this.api(!0);return t!==r?e.row(t).node():e.rows().nodes().flatten().toArray()},this.fnGetPosition=function(t){var e=this.api(!0),n=t.nodeName.toUpperCase();if("TR"==n)return e.row(t).index();if("TD"==n||"TH"==n){var r=e.cell(t).index();return[r.row,r.columnVisible,r.column]}return null},this.fnIsOpen=function(t){return this.api(!0).row(t).child.isShown()},this.fnOpen=function(t,e,n){return this.api(!0).row(t).child(e,n).show().child()[0]},this.fnPageChange=function(t,e){var n=this.api(!0).page(t);(e===r||e)&&n.draw(!1)},this.fnSetColumnVis=function(t,e,n){var i=this.api(!0).column(t).visible(e);(n===r||n)&&i.columns.adjust().draw()},this.fnSettings=function(){return oe(this[i.iApiIndex])},this.fnSort=function(t){this.api(!0).order(t).draw()},this.fnSortListener=function(t,e,n){this.api(!0).order.listener(t,e,n)},this.fnUpdate=function(t,e,n,i,o){var a=this.api(!0);return n===r||null===n?a.row(e).data(t):a.cell(e,n).data(t),(o===r||o)&&a.columns.adjust(),(i===r||i)&&a.draw(),0},this.fnVersionCheck=i.fnVersionCheck;var n=this,a=e===r,s=this.length;for(var u in a&&(e={}),this.oApi=this.internal=i.internal,l.ext.internal)u&&(this[u]=Fe(u));return this.each(function(){var i,o=s>1?le({},e,!0):e,u=0,c=this.getAttribute("id"),h=!1,f=l.defaults,d=t(this);if("table"==this.nodeName.toLowerCase()){L(f),I(f.column),k(f,f,!0),k(f.column,f.column,!0),k(f,t.extend(o,d.data()),!0);var p=l.settings;for(u=0,i=p.length;u<i;u++){var g=p[u];if(g.nTable==this||g.nTHead&&g.nTHead.parentNode==this||g.nTFoot&&g.nTFoot.parentNode==this){if(a||(o.bRetrieve!==r?o.bRetrieve:f.bRetrieve))return g.oInstance;if(o.bDestroy!==r?o.bDestroy:f.bDestroy){g.oInstance.fnDestroy();break}return void ae(g,0,"Cannot reinitialise DataTable",3)}if(g.sTableId==this.id){p.splice(u,1);break}}null!==c&&""!==c||(c="DataTables_Table_"+l.ext._unique++,this.id=c);var m=t.extend(!0,{},l.models.oSettings,{sDestroyWidth:d[0].style.width,sInstance:c,sTableId:c});m.nTable=this,m.oApi=n.internal,m.oInit=o,p.push(m),m.oInstance=1===n.length?n:d.dataTable(),L(o),E(o.oLanguage),o.aLengthMenu&&!o.iDisplayLength&&(o.iDisplayLength=t.isArray(o.aLengthMenu[0])?o.aLengthMenu[0][0]:o.aLengthMenu[0]),o=le(t.extend(!0,{},f),o),se(m.oFeatures,o,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),se(m,o,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),se(m.oScroll,o,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),se(m.oLanguage,o,"fnInfoCallback"),ce(m,"aoDrawCallback",o.fnDrawCallback,"user"),ce(m,"aoServerParams",o.fnServerParams,"user"),ce(m,"aoStateSaveParams",o.fnStateSaveParams,"user"),ce(m,"aoStateLoadParams",o.fnStateLoadParams,"user"),ce(m,"aoStateLoaded",o.fnStateLoaded,"user"),ce(m,"aoRowCallback",o.fnRowCallback,"user"),ce(m,"aoRowCreatedCallback",o.fnCreatedRow,"user"),ce(m,"aoHeaderCallback",o.fnHeaderCallback,"user"),ce(m,"aoFooterCallback",o.fnFooterCallback,"user"),ce(m,"aoInitComplete",o.fnInitComplete,"user"),ce(m,"aoPreDrawCallback",o.fnPreDrawCallback,"user"),m.rowIdFn=Q(o.rowId),N(m);var v=m.oClasses;if(t.extend(v,l.ext.classes,o.oClasses),d.addClass(v.sTable),m.iInitDisplayStart===r&&(m.iInitDisplayStart=o.iDisplayStart,m._iDisplayStart=o.iDisplayStart),null!==o.iDeferLoading){m.bDeferLoading=!0;var y=t.isArray(o.iDeferLoading);m._iRecordsDisplay=y?o.iDeferLoading[0]:o.iDeferLoading,m._iRecordsTotal=y?o.iDeferLoading[1]:o.iDeferLoading}var b=m.oLanguage;t.extend(!0,b,o.oLanguage),b.sUrl&&(t.ajax({dataType:"json",url:b.sUrl,success:function(e){E(e),k(f.oLanguage,e),t.extend(!0,b,e),Nt(m)},error:function(){Nt(m)}}),h=!0),null===o.asStripeClasses&&(m.asStripeClasses=[v.sStripeOdd,v.sStripeEven]);var x=m.asStripeClasses,_=d.children("tbody").find("tr").eq(0);-1!==t.inArray(!0,t.map(x,function(t,e){return _.hasClass(t)}))&&(t("tbody tr",this).removeClass(x.join(" ")),m.asDestroyStripes=x.slice());var w,S=[],C=this.getElementsByTagName("thead");if(0!==C.length&&(ct(m.aoHeader,C[0]),S=ht(m)),null===o.aoColumns)for(w=[],u=0,i=S.length;u<i;u++)w.push(null);else w=o.aoColumns;for(u=0,i=w.length;u<i;u++)j(m,S?S[u]:null);if(W(m,o.aoColumnDefs,w,function(t,e){P(m,t,e)}),_.length){var T=function(t,e){return null!==t.getAttribute("data-"+e)?e:null};t(_[0]).children("th, td").each(function(t,e){var n=m.aoColumns[t];if(n.mData===t){var i=T(e,"sort")||T(e,"order"),o=T(e,"filter")||T(e,"search");null===i&&null===o||(n.mData={_:t+".display",sort:null!==i?t+".@data-"+i:r,type:null!==i?t+".@data-"+i:r,filter:null!==o?t+".@data-"+o:r},P(m,t))}})}var D=m.oFeatures,A=function(){if(o.aaSorting===r){var e=m.aaSorting;for(u=0,i=e.length;u<i;u++)e[u][1]=m.aoColumns[u].asSorting[0]}ee(m),D.bSort&&ce(m,"aoDrawCallback",function(){if(m.bSorted){var e=Qt(m),n={};t.each(e,function(t,e){n[e.src]=e.dir}),he(m,null,"order",[m,e,n]),Kt(m)}}),ce(m,"aoDrawCallback",function(){(m.bSorted||"ssp"===pe(m)||D.bDeferRender)&&ee(m)},"sc");var n=d.children("caption").each(function(){this._captionSide=t(this).css("caption-side")}),a=d.children("thead");0===a.length&&(a=t("<thead/>").appendTo(d)),m.nTHead=a[0];var s=d.children("tbody");0===s.length&&(s=t("<tbody/>").appendTo(d)),m.nTBody=s[0];var l=d.children("tfoot");if(0===l.length&&n.length>0&&(""!==m.oScroll.sX||""!==m.oScroll.sY)&&(l=t("<tfoot/>").appendTo(d)),0===l.length||0===l.children().length?d.addClass(v.sNoFooter):l.length>0&&(m.nTFoot=l[0],ct(m.aoFooter,m.nTFoot)),o.aaData)for(u=0;u<o.aaData.length;u++)z(m,o.aaData[u]);else(m.bDeferLoading||"dom"==pe(m))&&U(m,t(m.nTBody).children("tr"));m.aiDisplay=m.aiDisplayMaster.slice(),m.bInitialised=!0,!1===h&&Nt(m)};o.bStateSave?(D.bStateSave=!0,ce(m,"aoDrawCallback",re,"state_save"),ie(m,0,A)):A()}else ae(null,0,"Non-table node initialisation ("+this.nodeName+")",2)}),n=null,this},u={},c=/[\r\n\u2028]/g,h=/<.*?>/g,f=/^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,d=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^","-"].join("|\\")+")","g"),p=/[',$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,g=function(t){return!t||!0===t||"-"===t},m=function(t){var e=parseInt(t,10);return!isNaN(e)&&isFinite(t)?e:null},v=function(t,e){return u[e]||(u[e]=new RegExp(St(e),"g")),"string"==typeof t&&"."!==e?t.replace(/\./g,"").replace(u[e],"."):t},y=function(t,e,n){var r="string"==typeof t;return!!g(t)||(e&&r&&(t=v(t,e)),n&&r&&(t=t.replace(p,"")),!isNaN(parseFloat(t))&&isFinite(t))},b=function(t,e,n){return!!g(t)||function(t){return g(t)||"string"==typeof t}(t)&&!!y(C(t),e,n)||null},x=function(t,e,n){var i=[],o=0,a=t.length;if(n!==r)for(;o<a;o++)t[o]&&t[o][e]&&i.push(t[o][e][n]);else for(;o<a;o++)t[o]&&i.push(t[o][e]);return i},_=function(t,e,n,i){var o=[],a=0,s=e.length;if(i!==r)for(;a<s;a++)t[e[a]][n]&&o.push(t[e[a]][n][i]);else for(;a<s;a++)o.push(t[e[a]][n]);return o},w=function(t,e){var n,i=[];e===r?(e=0,n=t):(n=e,e=t);for(var o=e;o<n;o++)i.push(o);return i},S=function(t){for(var e=[],n=0,r=t.length;n<r;n++)t[n]&&e.push(t[n]);return e},C=function(t){return t.replace(h,"")},T=function(t){if(function(t){if(t.length<2)return!0;for(var e=t.slice().sort(),n=e[0],r=1,i=e.length;r<i;r++){if(e[r]===n)return!1;n=e[r]}return!0}(t))return t.slice();var e,n,r,i=[],o=t.length,a=0;t:for(n=0;n<o;n++){for(e=t[n],r=0;r<a;r++)if(i[r]===e)continue t;i.push(e),a++}return i};function D(e){var n,r,i={};t.each(e,function(t,o){(n=t.match(/^([^A-Z]+?)([A-Z])/))&&-1!=="a aa ai ao as b fn i m o s ".indexOf(n[1]+" ")&&(r=t.replace(n[0],n[2].toLowerCase()),i[r]=t,"o"===n[1]&&D(e[t]))}),e._hungarianMap=i}function k(e,n,i){var o;e._hungarianMap||D(e),t.each(n,function(a,s){(o=e._hungarianMap[a])===r||!i&&n[o]!==r||("o"===o.charAt(0)?(n[o]||(n[o]={}),t.extend(!0,n[o],n[a]),k(e[o],n[o],i)):n[o]=n[a])})}function E(t){var e=l.defaults.oLanguage,n=e.sDecimal;if(n&&Ie(n),t){var r=t.sZeroRecords;!t.sEmptyTable&&r&&"No data available in table"===e.sEmptyTable&&se(t,t,"sZeroRecords","sEmptyTable"),!t.sLoadingRecords&&r&&"Loading..."===e.sLoadingRecords&&se(t,t,"sZeroRecords","sLoadingRecords"),t.sInfoThousands&&(t.sThousands=t.sInfoThousands);var i=t.sDecimal;i&&n!==i&&Ie(i)}}l.util={throttle:function(t,e){var n,i,o=e!==r?e:200;return function(){var e=this,a=+new Date,s=arguments;n&&a<n+o?(clearTimeout(i),i=setTimeout(function(){n=r,t.apply(e,s)},o)):(n=a,t.apply(e,s))}},escapeRegex:function(t){return t.replace(d,"\\$1")}};var A=function(t,e,n){t[e]!==r&&(t[n]=t[e])};function L(t){A(t,"ordering","bSort"),A(t,"orderMulti","bSortMulti"),A(t,"orderClasses","bSortClasses"),A(t,"orderCellsTop","bSortCellsTop"),A(t,"order","aaSorting"),A(t,"orderFixed","aaSortingFixed"),A(t,"paging","bPaginate"),A(t,"pagingType","sPaginationType"),A(t,"pageLength","iDisplayLength"),A(t,"searching","bFilter"),"boolean"==typeof t.sScrollX&&(t.sScrollX=t.sScrollX?"100%":""),"boolean"==typeof t.scrollX&&(t.scrollX=t.scrollX?"100%":"");var e=t.aoSearchCols;if(e)for(var n=0,r=e.length;n<r;n++)e[n]&&k(l.models.oSearch,e[n])}function I(e){A(e,"orderable","bSortable"),A(e,"orderData","aDataSort"),A(e,"orderSequence","asSorting"),A(e,"orderDataType","sortDataType");var n=e.aDataSort;"number"!=typeof n||t.isArray(n)||(e.aDataSort=[n])}function N(n){if(!l.__browser){var r={};l.__browser=r;var i=t("<div/>").css({position:"fixed",top:0,left:-1*t(e).scrollLeft(),height:1,width:1,overflow:"hidden"}).append(t("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(t("<div/>").css({width:"100%",height:10}))).appendTo("body"),o=i.children(),a=o.children();r.barWidth=o[0].offsetWidth-o[0].clientWidth,r.bScrollOversize=100===a[0].offsetWidth&&100!==o[0].clientWidth,r.bScrollbarLeft=1!==Math.round(a.offset().left),r.bBounding=!!i[0].getBoundingClientRect().width,i.remove()}t.extend(n.oBrowser,l.__browser),n.oScroll.iBarWidth=l.__browser.barWidth}function F(t,e,n,i,o,a){var s,l=i,u=!1;for(n!==r&&(s=n,u=!0);l!==o;)t.hasOwnProperty(l)&&(s=u?e(s,t[l],l,t):t[l],u=!0,l+=a);return s}function j(e,r){var i=l.defaults.column,o=e.aoColumns.length,a=t.extend({},l.models.oColumn,i,{nTh:r||n.createElement("th"),sTitle:i.sTitle?i.sTitle:r?r.innerHTML:"",aDataSort:i.aDataSort?i.aDataSort:[o],mData:i.mData?i.mData:o,idx:o});e.aoColumns.push(a);var s=e.aoPreSearchCols;s[o]=t.extend({},l.models.oSearch,s[o]),P(e,o,t(r).data())}function P(e,n,i){var o=e.aoColumns[n],a=e.oClasses,s=t(o.nTh);if(!o.sWidthOrig){o.sWidthOrig=s.attr("width")||null;var u=(s.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/);u&&(o.sWidthOrig=u[1])}i!==r&&null!==i&&(I(i),k(l.defaults.column,i,!0),i.mDataProp===r||i.mData||(i.mData=i.mDataProp),i.sType&&(o._sManualType=i.sType),i.className&&!i.sClass&&(i.sClass=i.className),i.sClass&&s.addClass(i.sClass),t.extend(o,i),se(o,i,"sWidth","sWidthOrig"),i.iDataSort!==r&&(o.aDataSort=[i.iDataSort]),se(o,i,"aDataSort"));var c=o.mData,h=Q(c),f=o.mRender?Q(o.mRender):null,d=function(t){return"string"==typeof t&&-1!==t.indexOf("@")};o._bAttrSrc=t.isPlainObject(c)&&(d(c.sort)||d(c.type)||d(c.filter)),o._setter=null,o.fnGetData=function(t,e,n){var i=h(t,e,r,n);return f&&e?f(i,e,t,n):i},o.fnSetData=function(t,e,n){return J(c)(t,e,n)},"number"!=typeof c&&(e._rowReadObject=!0),e.oFeatures.bSort||(o.bSortable=!1,s.addClass(a.sSortableNone));var p=-1!==t.inArray("asc",o.asSorting),g=-1!==t.inArray("desc",o.asSorting);o.bSortable&&(p||g)?p&&!g?(o.sSortingClass=a.sSortableAsc,o.sSortingClassJUI=a.sSortJUIAscAllowed):!p&&g?(o.sSortingClass=a.sSortableDesc,o.sSortingClassJUI=a.sSortJUIDescAllowed):(o.sSortingClass=a.sSortable,o.sSortingClassJUI=a.sSortJUI):(o.sSortingClass=a.sSortableNone,o.sSortingClassJUI="")}function M(t){if(!1!==t.oFeatures.bAutoWidth){var e=t.aoColumns;Ut(t);for(var n=0,r=e.length;n<r;n++)e[n].nTh.style.width=e[n].sWidth}var i=t.oScroll;""===i.sY&&""===i.sX||qt(t),he(t,null,"column-sizing",[t])}function R(t,e){var n=H(t,"bVisible");return"number"==typeof n[e]?n[e]:null}function O(e,n){var r=H(e,"bVisible"),i=t.inArray(n,r);return-1!==i?i:null}function B(e){var n=0;return t.each(e.aoColumns,function(e,r){r.bVisible&&"none"!==t(r.nTh).css("display")&&n++}),n}function H(e,n){var r=[];return t.map(e.aoColumns,function(t,e){t[n]&&r.push(e)}),r}function q(t){var e,n,i,o,a,s,u,c,h,f=t.aoColumns,d=t.aoData,p=l.ext.type.detect;for(e=0,n=f.length;e<n;e++)if(h=[],!(u=f[e]).sType&&u._sManualType)u.sType=u._sManualType;else if(!u.sType){for(i=0,o=p.length;i<o;i++){for(a=0,s=d.length;a<s&&(h[a]===r&&(h[a]=V(t,a,e,"type")),(c=p[i](h[a],t))||i===p.length-1)&&"html"!==c;a++);if(c){u.sType=c;break}}u.sType||(u.sType="string")}}function W(e,n,i,o){var a,s,l,u,c,h,f,d=e.aoColumns;if(n)for(a=n.length-1;a>=0;a--){var p=(f=n[a]).targets!==r?f.targets:f.aTargets;for(t.isArray(p)||(p=[p]),l=0,u=p.length;l<u;l++)if("number"==typeof p[l]&&p[l]>=0){for(;d.length<=p[l];)j(e);o(p[l],f)}else if("number"==typeof p[l]&&p[l]<0)o(d.length+p[l],f);else if("string"==typeof p[l])for(c=0,h=d.length;c<h;c++)("_all"==p[l]||t(d[c].nTh).hasClass(p[l]))&&o(c,f)}if(i)for(a=0,s=i.length;a<s;a++)o(a,i[a])}function z(e,n,i,o){var a=e.aoData.length,s=t.extend(!0,{},l.models.oRow,{src:i?"dom":"data",idx:a});s._aData=n,e.aoData.push(s);for(var u=e.aoColumns,c=0,h=u.length;c<h;c++)u[c].sType=null;e.aiDisplayMaster.push(a);var f=e.rowIdFn(n);return f!==r&&(e.aIds[f]=s),!i&&e.oFeatures.bDeferRender||rt(e,a,i,o),a}function U(e,n){var r;return n instanceof t||(n=t(n)),n.map(function(t,n){return r=nt(e,n),z(e,r.data,n,r.cells)})}function V(t,e,n,i){var o=t.iDraw,a=t.aoColumns[n],s=t.aoData[e]._aData,l=a.sDefaultContent,u=a.fnGetData(s,i,{settings:t,row:e,col:n});if(u===r)return t.iDrawError!=o&&null===l&&(ae(t,0,"Requested unknown parameter "+("function"==typeof a.mData?"{function}":"'"+a.mData+"'")+" for row "+e+", column "+n,4),t.iDrawError=o),l;if(u!==s&&null!==u||null===l||i===r){if("function"==typeof u)return u.call(s)}else u=l;return null===u&&"display"==i?"":u}function X(t,e,n,r){t.aoColumns[n].fnSetData(t.aoData[e]._aData,r,{settings:t,row:e,col:n})}var Y=/\[.*?\]$/,G=/\(\)$/;function $(e){return t.map(e.match(/(\\.|[^\.])+/g)||[""],function(t){return t.replace(/\\\./g,".")})}function Q(e){if(t.isPlainObject(e)){var n={};return t.each(e,function(t,e){e&&(n[t]=Q(e))}),function(t,e,i,o){var a=n[e]||n._;return a!==r?a(t,e,i,o):t}}if(null===e)return function(t){return t};if("function"==typeof e)return function(t,n,r,i){return e(t,n,r,i)};if("string"!=typeof e||-1===e.indexOf(".")&&-1===e.indexOf("[")&&-1===e.indexOf("("))return function(t,n){return t[e]};var i=function(e,n,o){var a,s,l,u;if(""!==o)for(var c=$(o),h=0,f=c.length;h<f;h++){if(a=c[h].match(Y),s=c[h].match(G),a){if(c[h]=c[h].replace(Y,""),""!==c[h]&&(e=e[c[h]]),l=[],c.splice(0,h+1),u=c.join("."),t.isArray(e))for(var d=0,p=e.length;d<p;d++)l.push(i(e[d],n,u));var g=a[0].substring(1,a[0].length-1);e=""===g?l:l.join(g);break}if(s)c[h]=c[h].replace(G,""),e=e[c[h]]();else{if(null===e||e[c[h]]===r)return r;e=e[c[h]]}}return e};return function(t,n){return i(t,n,e)}}function J(e){if(t.isPlainObject(e))return J(e._);if(null===e)return function(){};if("function"==typeof e)return function(t,n,r){e(t,"set",n,r)};if("string"!=typeof e||-1===e.indexOf(".")&&-1===e.indexOf("[")&&-1===e.indexOf("("))return function(t,n){t[e]=n};var n=function(e,i,o){for(var a,s,l,u,c,h=$(o),f=h[h.length-1],d=0,p=h.length-1;d<p;d++){if(s=h[d].match(Y),l=h[d].match(G),s){if(h[d]=h[d].replace(Y,""),e[h[d]]=[],(a=h.slice()).splice(0,d+1),c=a.join("."),t.isArray(i))for(var g=0,m=i.length;g<m;g++)n(u={},i[g],c),e[h[d]].push(u);else e[h[d]]=i;return}l&&(h[d]=h[d].replace(G,""),e=e[h[d]](i)),null!==e[h[d]]&&e[h[d]]!==r||(e[h[d]]={}),e=e[h[d]]}f.match(G)?e=e[f.replace(G,"")](i):e[f.replace(Y,"")]=i};return function(t,r){return n(t,r,e)}}function K(t){return x(t.aoData,"_aData")}function Z(t){t.aoData.length=0,t.aiDisplayMaster.length=0,t.aiDisplay.length=0,t.aIds={}}function tt(t,e,n){for(var i=-1,o=0,a=t.length;o<a;o++)t[o]==e?i=o:t[o]>e&&t[o]--;-1!=i&&n===r&&t.splice(i,1)}function et(t,e,n,i){var o,a,s=t.aoData[e],l=function(n,r){for(;n.childNodes.length;)n.removeChild(n.firstChild);n.innerHTML=V(t,e,r,"display")};if("dom"!==n&&(n&&"auto"!==n||"dom"!==s.src)){var u=s.anCells;if(u)if(i!==r)l(u[i],i);else for(o=0,a=u.length;o<a;o++)l(u[o],o)}else s._aData=nt(t,s,i,i===r?r:s._aData).data;s._aSortData=null,s._aFilterData=null;var c=t.aoColumns;if(i!==r)c[i].sType=null;else{for(o=0,a=c.length;o<a;o++)c[o].sType=null;it(t,s)}}function nt(e,n,i,o){var a,s,l,u=[],c=n.firstChild,h=0,f=e.aoColumns,d=e._rowReadObject;o=o!==r?o:d?{}:[];var p=function(t,e){if("string"==typeof t){var n=t.indexOf("@");if(-1!==n){var r=t.substring(n+1);J(t)(o,e.getAttribute(r))}}},g=function(e){i!==r&&i!==h||(s=f[h],l=t.trim(e.innerHTML),s&&s._bAttrSrc?(J(s.mData._)(o,l),p(s.mData.sort,e),p(s.mData.type,e),p(s.mData.filter,e)):d?(s._setter||(s._setter=J(s.mData)),s._setter(o,l)):o[h]=l),h++};if(c)for(;c;)"TD"!=(a=c.nodeName.toUpperCase())&&"TH"!=a||(g(c),u.push(c)),c=c.nextSibling;else for(var m=0,v=(u=n.anCells).length;m<v;m++)g(u[m]);var y=n.firstChild?n:n.nTr;if(y){var b=y.getAttribute("id");b&&J(e.rowId)(o,b)}return{data:o,cells:u}}function rt(e,r,i,o){var a,s,l,u,c,h,f=e.aoData[r],d=f._aData,p=[];if(null===f.nTr){for(a=i||n.createElement("tr"),f.nTr=a,f.anCells=p,a._DT_RowIndex=r,it(e,f),u=0,c=e.aoColumns.length;u<c;u++)l=e.aoColumns[u],(s=(h=!i)?n.createElement(l.sCellType):o[u])._DT_CellIndex={row:r,column:u},p.push(s),!h&&(i&&!l.mRender&&l.mData===u||t.isPlainObject(l.mData)&&l.mData._===u+".display")||(s.innerHTML=V(e,r,u,"display")),l.sClass&&(s.className+=" "+l.sClass),l.bVisible&&!i?a.appendChild(s):!l.bVisible&&i&&s.parentNode.removeChild(s),l.fnCreatedCell&&l.fnCreatedCell.call(e.oInstance,s,V(e,r,u),d,r,u);he(e,"aoRowCreatedCallback",null,[a,d,r,p])}f.nTr.setAttribute("role","row")}function it(e,n){var r=n.nTr,i=n._aData;if(r){var o=e.rowIdFn(i);if(o&&(r.id=o),i.DT_RowClass){var a=i.DT_RowClass.split(" ");n.__rowc=n.__rowc?T(n.__rowc.concat(a)):a,t(r).removeClass(n.__rowc.join(" ")).addClass(i.DT_RowClass)}i.DT_RowAttr&&t(r).attr(i.DT_RowAttr),i.DT_RowData&&t(r).data(i.DT_RowData)}}function ot(e){var n,r,i,o,a,s=e.nTHead,l=e.nTFoot,u=0===t("th, td",s).length,c=e.oClasses,h=e.aoColumns;for(u&&(o=t("<tr/>").appendTo(s)),n=0,r=h.length;n<r;n++)i=t((a=h[n]).nTh).addClass(a.sClass),u&&i.appendTo(o),e.oFeatures.bSort&&(i.addClass(a.sSortingClass),!1!==a.bSortable&&(i.attr("tabindex",e.iTabIndex).attr("aria-controls",e.sTableId),te(e,a.nTh,n))),a.sTitle!=i[0].innerHTML&&i.html(a.sTitle),de(e,"header")(e,i,a,c);if(u&&ct(e.aoHeader,s),t(s).find(">tr").attr("role","row"),t(s).find(">tr>th, >tr>td").addClass(c.sHeaderTH),t(l).find(">tr>th, >tr>td").addClass(c.sFooterTH),null!==l){var f=e.aoFooter[0];for(n=0,r=f.length;n<r;n++)(a=h[n]).nTf=f[n].cell,a.sClass&&t(a.nTf).addClass(a.sClass)}}function at(e,n,i){var o,a,s,l,u,c,h,f,d,p=[],g=[],m=e.aoColumns.length;if(n){for(i===r&&(i=!1),o=0,a=n.length;o<a;o++){for(p[o]=n[o].slice(),p[o].nTr=n[o].nTr,s=m-1;s>=0;s--)e.aoColumns[s].bVisible||i||p[o].splice(s,1);g.push([])}for(o=0,a=p.length;o<a;o++){if(h=p[o].nTr)for(;c=h.firstChild;)h.removeChild(c);for(s=0,l=p[o].length;s<l;s++)if(f=1,d=1,g[o][s]===r){for(h.appendChild(p[o][s].cell),g[o][s]=1;p[o+f]!==r&&p[o][s].cell==p[o+f][s].cell;)g[o+f][s]=1,f++;for(;p[o][s+d]!==r&&p[o][s].cell==p[o][s+d].cell;){for(u=0;u<f;u++)g[o+u][s+d]=1;d++}t(p[o][s].cell).attr("rowspan",f).attr("colspan",d)}}}}function st(e){var n=he(e,"aoPreDrawCallback","preDraw",[e]);if(-1===t.inArray(!1,n)){var i=[],o=0,a=e.asStripeClasses,s=a.length,l=e.oLanguage,u=e.iInitDisplayStart,c="ssp"==pe(e),h=e.aiDisplay;e.bDrawing=!0,u!==r&&-1!==u&&(e._iDisplayStart=c?u:u>=e.fnRecordsDisplay()?0:u,e.iInitDisplayStart=-1);var f=e._iDisplayStart,d=e.fnDisplayEnd();if(e.bDeferLoading)e.bDeferLoading=!1,e.iDraw++,Bt(e,!1);else if(c){if(!e.bDestroying&&!dt(e))return}else e.iDraw++;if(0!==h.length)for(var p=c?e.aoData.length:d,g=c?0:f;g<p;g++){var m=h[g],v=e.aoData[m];null===v.nTr&&rt(e,m);var y=v.nTr;if(0!==s){var b=a[o%s];v._sRowStripe!=b&&(t(y).removeClass(v._sRowStripe).addClass(b),v._sRowStripe=b)}he(e,"aoRowCallback",null,[y,v._aData,o,g,m]),i.push(y),o++}else{var x=l.sZeroRecords;1==e.iDraw&&"ajax"==pe(e)?x=l.sLoadingRecords:l.sEmptyTable&&0===e.fnRecordsTotal()&&(x=l.sEmptyTable),i[0]=t("<tr/>",{class:s?a[0]:""}).append(t("<td />",{valign:"top",colSpan:B(e),class:e.oClasses.sRowEmpty}).html(x))[0]}he(e,"aoHeaderCallback","header",[t(e.nTHead).children("tr")[0],K(e),f,d,h]),he(e,"aoFooterCallback","footer",[t(e.nTFoot).children("tr")[0],K(e),f,d,h]);var _=t(e.nTBody);_.children().detach(),_.append(t(i)),he(e,"aoDrawCallback","draw",[e]),e.bSorted=!1,e.bFiltered=!1,e.bDrawing=!1}else Bt(e,!1)}function lt(t,e){var n=t.oFeatures,r=n.bFilter;n.bSort&&Jt(t),r?yt(t,t.oPreviousSearch):t.aiDisplay=t.aiDisplayMaster.slice(),!0!==e&&(t._iDisplayStart=0),t._drawHold=e,st(t),t._drawHold=!1}function ut(e){var n=e.oClasses,r=t(e.nTable),i=t("<div/>").insertBefore(r),o=e.oFeatures,a=t("<div/>",{id:e.sTableId+"_wrapper",class:n.sWrapper+(e.nTFoot?"":" "+n.sNoFooter)});e.nHolding=i[0],e.nTableWrapper=a[0],e.nTableReinsertBefore=e.nTable.nextSibling;for(var s,u,c,h,f,d,p=e.sDom.split(""),g=0;g<p.length;g++){if(s=null,"<"==(u=p[g])){if(c=t("<div/>")[0],"'"==(h=p[g+1])||'"'==h){for(f="",d=2;p[g+d]!=h;)f+=p[g+d],d++;if("H"==f?f=n.sJUIHeader:"F"==f&&(f=n.sJUIFooter),-1!=f.indexOf(".")){var m=f.split(".");c.id=m[0].substr(1,m[0].length-1),c.className=m[1]}else"#"==f.charAt(0)?c.id=f.substr(1,f.length-1):c.className=f;g+=d}a.append(c),a=t(c)}else if(">"==u)a=a.parent();else if("l"==u&&o.bPaginate&&o.bLengthChange)s=Pt(e);else if("f"==u&&o.bFilter)s=vt(e);else if("r"==u&&o.bProcessing)s=Ot(e);else if("t"==u)s=Ht(e);else if("i"==u&&o.bInfo)s=At(e);else if("p"==u&&o.bPaginate)s=Mt(e);else if(0!==l.ext.feature.length)for(var v=l.ext.feature,y=0,b=v.length;y<b;y++)if(u==v[y].cFeature){s=v[y].fnInit(e);break}if(s){var x=e.aanFeatures;x[u]||(x[u]=[]),x[u].push(s),a.append(s)}}i.replaceWith(a),e.nHolding=null}function ct(e,n){var r,i,o,a,s,l,u,c,h,f,d=t(n).children("tr"),p=function(t,e,n){for(var r=t[e];r[n];)n++;return n};for(e.splice(0,e.length),o=0,l=d.length;o<l;o++)e.push([]);for(o=0,l=d.length;o<l;o++)for(i=(r=d[o]).firstChild;i;){if("TD"==i.nodeName.toUpperCase()||"TH"==i.nodeName.toUpperCase())for(c=(c=1*i.getAttribute("colspan"))&&0!==c&&1!==c?c:1,h=(h=1*i.getAttribute("rowspan"))&&0!==h&&1!==h?h:1,u=p(e,o,0),f=1===c,s=0;s<c;s++)for(a=0;a<h;a++)e[o+a][u+s]={cell:i,unique:f},e[o+a].nTr=r;i=i.nextSibling}}function ht(t,e,n){var r=[];n||(n=t.aoHeader,e&&ct(n=[],e));for(var i=0,o=n.length;i<o;i++)for(var a=0,s=n[i].length;a<s;a++)!n[i][a].unique||r[a]&&t.bSortCellsTop||(r[a]=n[i][a].cell);return r}function ft(e,n,r){if(he(e,"aoServerParams","serverParams",[n]),n&&t.isArray(n)){var i={},o=/(.*?)\[\]$/;t.each(n,function(t,e){var n=e.name.match(o);if(n){var r=n[0];i[r]||(i[r]=[]),i[r].push(e.value)}else i[e.name]=e.value}),n=i}var a,s=e.ajax,l=e.oInstance,u=function(t){he(e,null,"xhr",[e,t,e.jqXHR]),r(t)};if(t.isPlainObject(s)&&s.data){var c="function"==typeof(a=s.data)?a(n,e):a;n="function"==typeof a&&c?c:t.extend(!0,n,c),delete s.data}var h={data:n,success:function(t){var n=t.error||t.sError;n&&ae(e,0,n),e.json=t,u(t)},dataType:"json",cache:!1,type:e.sServerMethod,error:function(n,r,i){var o=he(e,null,"xhr",[e,null,e.jqXHR]);-1===t.inArray(!0,o)&&("parsererror"==r?ae(e,0,"Invalid JSON response",1):4===n.readyState&&ae(e,0,"Ajax error",7)),Bt(e,!1)}};e.oAjaxData=n,he(e,null,"preXhr",[e,n]),e.fnServerData?e.fnServerData.call(l,e.sAjaxSource,t.map(n,function(t,e){return{name:e,value:t}}),u,e):e.sAjaxSource||"string"==typeof s?e.jqXHR=t.ajax(t.extend(h,{url:s||e.sAjaxSource})):"function"==typeof s?e.jqXHR=s.call(l,n,u,e):(e.jqXHR=t.ajax(t.extend(h,s)),s.data=a)}function dt(t){return!t.bAjaxDataGet||(t.iDraw++,Bt(t,!0),ft(t,pt(t),function(e){gt(t,e)}),!1)}function pt(e){var n,r,i,o,a=e.aoColumns,s=a.length,u=e.oFeatures,c=e.oPreviousSearch,h=e.aoPreSearchCols,f=[],d=Qt(e),p=e._iDisplayStart,g=!1!==u.bPaginate?e._iDisplayLength:-1,m=function(t,e){f.push({name:t,value:e})};m("sEcho",e.iDraw),m("iColumns",s),m("sColumns",x(a,"sName").join(",")),m("iDisplayStart",p),m("iDisplayLength",g);var v={draw:e.iDraw,columns:[],order:[],start:p,length:g,search:{value:c.sSearch,regex:c.bRegex}};for(n=0;n<s;n++)v.columns.push({data:r="function"==typeof(i=a[n]).mData?"function":i.mData,name:i.sName,searchable:i.bSearchable,orderable:i.bSortable,search:{value:(o=h[n]).sSearch,regex:o.bRegex}}),m("mDataProp_"+n,r),u.bFilter&&(m("sSearch_"+n,o.sSearch),m("bRegex_"+n,o.bRegex),m("bSearchable_"+n,i.bSearchable)),u.bSort&&m("bSortable_"+n,i.bSortable);u.bFilter&&(m("sSearch",c.sSearch),m("bRegex",c.bRegex)),u.bSort&&(t.each(d,function(t,e){v.order.push({column:e.col,dir:e.dir}),m("iSortCol_"+t,e.col),m("sSortDir_"+t,e.dir)}),m("iSortingCols",d.length));var y=l.ext.legacy.ajax;return null===y?e.sAjaxSource?f:v:y?f:v}function gt(t,e){var n=function(t,n){return e[t]!==r?e[t]:e[n]},i=mt(t,e),o=n("sEcho","draw"),a=n("iTotalRecords","recordsTotal"),s=n("iTotalDisplayRecords","recordsFiltered");if(o){if(1*o<t.iDraw)return;t.iDraw=1*o}Z(t),t._iRecordsTotal=parseInt(a,10),t._iRecordsDisplay=parseInt(s,10);for(var l=0,u=i.length;l<u;l++)z(t,i[l]);t.aiDisplay=t.aiDisplayMaster.slice(),t.bAjaxDataGet=!1,st(t),t._bInitComplete||Ft(t,e),t.bAjaxDataGet=!0,Bt(t,!1)}function mt(e,n){var i=t.isPlainObject(e.ajax)&&e.ajax.dataSrc!==r?e.ajax.dataSrc:e.sAjaxDataProp;return"data"===i?n.aaData||n[i]:""!==i?Q(i)(n):n}function vt(e){var r=e.oClasses,i=e.sTableId,o=e.oLanguage,a=e.oPreviousSearch,s=e.aanFeatures,l='<input type="search" class="'+r.sFilterInput+'"/>',u=o.sSearch;u=u.match(/_INPUT_/)?u.replace("_INPUT_",l):u+l;var c=t("<div/>",{id:s.f?null:i+"_filter",class:r.sFilter}).append(t("<label/>").append(u)),h=function(){var t=this.value?this.value:"";t!=a.sSearch&&(yt(e,{sSearch:t,bRegex:a.bRegex,bSmart:a.bSmart,bCaseInsensitive:a.bCaseInsensitive}),e._iDisplayStart=0,st(e))},f=null!==e.searchDelay?e.searchDelay:"ssp"===pe(e)?400:0,d=t("input",c).val(a.sSearch).attr("placeholder",o.sSearchPlaceholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",f?Vt(h,f):h).on("keypress.DT",function(t){if(13==t.keyCode)return!1}).attr("aria-controls",i);return t(e.nTable).on("search.dt.DT",function(t,r){if(e===r)try{d[0]!==n.activeElement&&d.val(a.sSearch)}catch(i){}}),c[0]}function yt(t,e,n){var i=t.oPreviousSearch,o=t.aoPreSearchCols,a=function(t){i.sSearch=t.sSearch,i.bRegex=t.bRegex,i.bSmart=t.bSmart,i.bCaseInsensitive=t.bCaseInsensitive},s=function(t){return t.bEscapeRegex!==r?!t.bEscapeRegex:t.bRegex};if(q(t),"ssp"!=pe(t)){_t(t,e.sSearch,n,s(e),e.bSmart,e.bCaseInsensitive),a(e);for(var l=0;l<o.length;l++)xt(t,o[l].sSearch,l,s(o[l]),o[l].bSmart,o[l].bCaseInsensitive);bt(t)}else a(e);t.bFiltered=!0,he(t,null,"search",[t])}function bt(e){for(var n,r,i=l.ext.search,o=e.aiDisplay,a=0,s=i.length;a<s;a++){for(var u=[],c=0,h=o.length;c<h;c++)i[a](e,(n=e.aoData[r=o[c]])._aFilterData,r,n._aData,c)&&u.push(r);o.length=0,t.merge(o,u)}}function xt(t,e,n,r,i,o){if(""!==e){for(var a=[],s=t.aiDisplay,l=wt(e,r,i,o),u=0;u<s.length;u++)l.test(t.aoData[s[u]]._aFilterData[n])&&a.push(s[u]);t.aiDisplay=a}}function _t(t,e,n,r,i,o){var a,s,u,c=wt(e,r,i,o),h=t.oPreviousSearch.sSearch,f=t.aiDisplayMaster,d=[];if(0!==l.ext.search.length&&(n=!0),s=Dt(t),e.length<=0)t.aiDisplay=f.slice();else{for((s||n||r||h.length>e.length||0!==e.indexOf(h)||t.bSorted)&&(t.aiDisplay=f.slice()),a=t.aiDisplay,u=0;u<a.length;u++)c.test(t.aoData[a[u]]._sFilterRow)&&d.push(a[u]);t.aiDisplay=d}}function wt(e,n,r,i){if(e=n?e:St(e),r){var o=t.map(e.match(/"[^"]+"|[^ ]+/g)||[""],function(t){if('"'===t.charAt(0)){var e=t.match(/^"(.*)"$/);t=e?e[1]:t}return t.replace('"',"")});e="^(?=.*?"+o.join(")(?=.*?")+").*$"}return new RegExp(e,i?"i":"")}var St=l.util.escapeRegex,Ct=t("<div>")[0],Tt=Ct.textContent!==r;function Dt(t){var e,n,r,i,o,a,s,u,c=t.aoColumns,h=l.ext.type.search,f=!1;for(n=0,i=t.aoData.length;n<i;n++)if(!(u=t.aoData[n])._aFilterData){for(a=[],r=0,o=c.length;r<o;r++)(e=c[r]).bSearchable?(s=V(t,n,r,"filter"),h[e.sType]&&(s=h[e.sType](s)),null===s&&(s=""),"string"!=typeof s&&s.toString&&(s=s.toString())):s="",s.indexOf&&-1!==s.indexOf("&")&&(Ct.innerHTML=s,s=Tt?Ct.textContent:Ct.innerText),s.replace&&(s=s.replace(/[\r\n\u2028]/g,"")),a.push(s);u._aFilterData=a,u._sFilterRow=a.join("  "),f=!0}return f}function kt(t){return{search:t.sSearch,smart:t.bSmart,regex:t.bRegex,caseInsensitive:t.bCaseInsensitive}}function Et(t){return{sSearch:t.search,bSmart:t.smart,bRegex:t.regex,bCaseInsensitive:t.caseInsensitive}}function At(e){var n=e.sTableId,r=e.aanFeatures.i,i=t("<div/>",{class:e.oClasses.sInfo,id:r?null:n+"_info"});return r||(e.aoDrawCallback.push({fn:Lt,sName:"information"}),i.attr("role","status").attr("aria-live","polite"),t(e.nTable).attr("aria-describedby",n+"_info")),i[0]}function Lt(e){var n=e.aanFeatures.i;if(0!==n.length){var r=e.oLanguage,i=e._iDisplayStart+1,o=e.fnDisplayEnd(),a=e.fnRecordsTotal(),s=e.fnRecordsDisplay(),l=s?r.sInfo:r.sInfoEmpty;s!==a&&(l+=" "+r.sInfoFiltered),l=It(e,l+=r.sInfoPostFix);var u=r.fnInfoCallback;null!==u&&(l=u.call(e.oInstance,e,i,o,a,s,l)),t(n).html(l)}}function It(t,e){var n=t.fnFormatNumber,r=t._iDisplayStart+1,i=t._iDisplayLength,o=t.fnRecordsDisplay(),a=-1===i;return e.replace(/_START_/g,n.call(t,r)).replace(/_END_/g,n.call(t,t.fnDisplayEnd())).replace(/_MAX_/g,n.call(t,t.fnRecordsTotal())).replace(/_TOTAL_/g,n.call(t,o)).replace(/_PAGE_/g,n.call(t,a?1:Math.ceil(r/i))).replace(/_PAGES_/g,n.call(t,a?1:Math.ceil(o/i)))}function Nt(t){var e,n,r,i=t.iInitDisplayStart,o=t.aoColumns,a=t.oFeatures,s=t.bDeferLoading;if(t.bInitialised){for(ut(t),ot(t),at(t,t.aoHeader),at(t,t.aoFooter),Bt(t,!0),a.bAutoWidth&&Ut(t),e=0,n=o.length;e<n;e++)(r=o[e]).sWidth&&(r.nTh.style.width=$t(r.sWidth));he(t,null,"preInit",[t]),lt(t);var l=pe(t);("ssp"!=l||s)&&("ajax"==l?ft(t,[],function(n){var r=mt(t,n);for(e=0;e<r.length;e++)z(t,r[e]);t.iInitDisplayStart=i,lt(t),Bt(t,!1),Ft(t,n)}):(Bt(t,!1),Ft(t)))}else setTimeout(function(){Nt(t)},200)}function Ft(t,e){t._bInitComplete=!0,(e||t.oInit.aaData)&&M(t),he(t,null,"plugin-init",[t,e]),he(t,"aoInitComplete","init",[t,e])}function jt(t,e){var n=parseInt(e,10);t._iDisplayLength=n,fe(t),he(t,null,"length",[t,n])}function Pt(e){for(var n=e.oClasses,r=e.sTableId,i=e.aLengthMenu,o=t.isArray(i[0]),a=o?i[0]:i,s=o?i[1]:i,l=t("<select/>",{name:r+"_length","aria-controls":r,class:n.sLengthSelect}),u=0,c=a.length;u<c;u++)l[0][u]=new Option("number"==typeof s[u]?e.fnFormatNumber(s[u]):s[u],a[u]);var h=t("<div><label/></div>").addClass(n.sLength);return e.aanFeatures.l||(h[0].id=r+"_length"),h.children().append(e.oLanguage.sLengthMenu.replace("_MENU_",l[0].outerHTML)),t("select",h).val(e._iDisplayLength).on("change.DT",function(n){jt(e,t(this).val()),st(e)}),t(e.nTable).on("length.dt.DT",function(n,r,i){e===r&&t("select",h).val(i)}),h[0]}function Mt(e){var n=e.sPaginationType,r=l.ext.pager[n],i="function"==typeof r,o=function(t){st(t)},a=t("<div/>").addClass(e.oClasses.sPaging+n)[0],s=e.aanFeatures;return i||r.fnInit(e,a,o),s.p||(a.id=e.sTableId+"_paginate",e.aoDrawCallback.push({fn:function(t){if(i){var e,n,a=t._iDisplayStart,l=t._iDisplayLength,u=t.fnRecordsDisplay(),c=-1===l,h=c?0:Math.ceil(a/l),f=c?1:Math.ceil(u/l),d=r(h,f);for(e=0,n=s.p.length;e<n;e++)de(t,"pageButton")(t,s.p[e],e,d,h,f)}else r.fnUpdate(t,o)},sName:"pagination"})),a}function Rt(t,e,n){var r=t._iDisplayStart,i=t._iDisplayLength,o=t.fnRecordsDisplay();0===o||-1===i?r=0:"number"==typeof e?(r=e*i)>o&&(r=0):"first"==e?r=0:"previous"==e?(r=i>=0?r-i:0)<0&&(r=0):"next"==e?r+i<o&&(r+=i):"last"==e?r=Math.floor((o-1)/i)*i:ae(t,0,"Unknown paging action: "+e,5);var a=t._iDisplayStart!==r;return t._iDisplayStart=r,a&&(he(t,null,"page",[t]),n&&st(t)),a}function Ot(e){return t("<div/>",{id:e.aanFeatures.r?null:e.sTableId+"_processing",class:e.oClasses.sProcessing}).html(e.oLanguage.sProcessing).insertBefore(e.nTable)[0]}function Bt(e,n){e.oFeatures.bProcessing&&t(e.aanFeatures.r).css("display",n?"block":"none"),he(e,null,"processing",[e,n])}function Ht(e){var n=t(e.nTable);n.attr("role","grid");var r=e.oScroll;if(""===r.sX&&""===r.sY)return e.nTable;var i=r.sX,o=r.sY,a=e.oClasses,s=n.children("caption"),l=s.length?s[0]._captionSide:null,u=t(n[0].cloneNode(!1)),c=t(n[0].cloneNode(!1)),h=n.children("tfoot"),f="<div/>",d=function(t){return t?$t(t):null};h.length||(h=null);var p=t(f,{class:a.sScrollWrapper}).append(t(f,{class:a.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:i?d(i):"100%"}).append(t(f,{class:a.sScrollHeadInner}).css({"box-sizing":"content-box",width:r.sXInner||"100%"}).append(u.removeAttr("id").css("margin-left",0).append("top"===l?s:null).append(n.children("thead"))))).append(t(f,{class:a.sScrollBody}).css({position:"relative",overflow:"auto",width:d(i)}).append(n));h&&p.append(t(f,{class:a.sScrollFoot}).css({overflow:"hidden",border:0,width:i?d(i):"100%"}).append(t(f,{class:a.sScrollFootInner}).append(c.removeAttr("id").css("margin-left",0).append("bottom"===l?s:null).append(n.children("tfoot")))));var g=p.children(),m=g[0],v=g[1],y=h?g[2]:null;return i&&t(v).on("scroll.DT",function(t){var e=this.scrollLeft;m.scrollLeft=e,h&&(y.scrollLeft=e)}),t(v).css(o&&r.bCollapse?"max-height":"height",o),e.nScrollHead=m,e.nScrollBody=v,e.nScrollFoot=y,e.aoDrawCallback.push({fn:qt,sName:"scrolling"}),p[0]}function qt(e){var n,i,o,a,s,l,u,c,h,f=e.oScroll,d=f.sX,p=f.sXInner,g=f.sY,m=f.iBarWidth,v=t(e.nScrollHead),y=v[0].style,b=v.children("div"),_=b[0].style,w=b.children("table"),S=e.nScrollBody,C=t(S),T=S.style,D=t(e.nScrollFoot).children("div"),k=D.children("table"),E=t(e.nTHead),A=t(e.nTable),L=A[0],I=L.style,N=e.nTFoot?t(e.nTFoot):null,F=e.oBrowser,j=F.bScrollOversize,P=x(e.aoColumns,"nTh"),O=[],B=[],H=[],q=[],W=function(t){var e=t.style;e.paddingTop="0",e.paddingBottom="0",e.borderTopWidth="0",e.borderBottomWidth="0",e.height=0},z=S.scrollHeight>S.clientHeight;if(e.scrollBarVis!==z&&e.scrollBarVis!==r)return e.scrollBarVis=z,void M(e);e.scrollBarVis=z,A.children("thead, tfoot").remove(),N&&(l=N.clone().prependTo(A),i=N.find("tr"),a=l.find("tr")),s=E.clone().prependTo(A),n=E.find("tr"),o=s.find("tr"),s.find("th, td").removeAttr("tabindex"),d||(T.width="100%",v[0].style.width="100%"),t.each(ht(e,s),function(t,n){u=R(e,t),n.style.width=e.aoColumns[u].sWidth}),N&&Wt(function(t){t.style.width=""},a),h=A.outerWidth(),""===d?(I.width="100%",j&&(A.find("tbody").height()>S.offsetHeight||"scroll"==C.css("overflow-y"))&&(I.width=$t(A.outerWidth()-m)),h=A.outerWidth()):""!==p&&(I.width=$t(p),h=A.outerWidth()),Wt(W,o),Wt(function(e){H.push(e.innerHTML),O.push($t(t(e).css("width")))},o),Wt(function(e,n){-1!==t.inArray(e,P)&&(e.style.width=O[n])},n),t(o).height(0),N&&(Wt(W,a),Wt(function(e){q.push(e.innerHTML),B.push($t(t(e).css("width")))},a),Wt(function(t,e){t.style.width=B[e]},i),t(a).height(0)),Wt(function(t,e){t.innerHTML='<div class="dataTables_sizing">'+H[e]+"</div>",t.childNodes[0].style.height="0",t.childNodes[0].style.overflow="hidden",t.style.width=O[e]},o),N&&Wt(function(t,e){t.innerHTML='<div class="dataTables_sizing">'+q[e]+"</div>",t.childNodes[0].style.height="0",t.childNodes[0].style.overflow="hidden",t.style.width=B[e]},a),A.outerWidth()<h?(c=S.scrollHeight>S.offsetHeight||"scroll"==C.css("overflow-y")?h+m:h,j&&(S.scrollHeight>S.offsetHeight||"scroll"==C.css("overflow-y"))&&(I.width=$t(c-m)),""!==d&&""===p||ae(e,1,"Possible column misalignment",6)):c="100%",T.width=$t(c),y.width=$t(c),N&&(e.nScrollFoot.style.width=$t(c)),g||j&&(T.height=$t(L.offsetHeight+m));var U=A.outerWidth();w[0].style.width=$t(U),_.width=$t(U);var V=A.height()>S.clientHeight||"scroll"==C.css("overflow-y"),X="padding"+(F.bScrollbarLeft?"Left":"Right");_[X]=V?m+"px":"0px",N&&(k[0].style.width=$t(U),D[0].style.width=$t(U),D[0].style[X]=V?m+"px":"0px"),A.children("colgroup").insertBefore(A.children("thead")),C.trigger("scroll"),!e.bSorted&&!e.bFiltered||e._drawHold||(S.scrollTop=0)}function Wt(t,e,n){for(var r,i,o=0,a=0,s=e.length;a<s;){for(r=e[a].firstChild,i=n?n[a].firstChild:null;r;)1===r.nodeType&&(n?t(r,i,o):t(r,o),o++),r=r.nextSibling,i=n?i.nextSibling:null;a++}}var zt=/<.*?>/g;function Ut(n){var r,i,o,a=n.nTable,s=n.aoColumns,l=n.oScroll,u=l.sY,c=l.sX,h=l.sXInner,f=s.length,d=H(n,"bVisible"),p=t("th",n.nTHead),g=a.getAttribute("width"),m=a.parentNode,v=!1,y=n.oBrowser,b=y.bScrollOversize,x=a.style.width;for(x&&-1!==x.indexOf("%")&&(g=x),r=0;r<d.length;r++)null!==(i=s[d[r]]).sWidth&&(i.sWidth=Xt(i.sWidthOrig,m),v=!0);if(b||!v&&!c&&!u&&f==B(n)&&f==p.length)for(r=0;r<f;r++){var _=R(n,r);null!==_&&(s[_].sWidth=$t(p.eq(r).width()))}else{var w=t(a).clone().css("visibility","hidden").removeAttr("id");w.find("tbody tr").remove();var S=t("<tr/>").appendTo(w.find("tbody"));for(w.find("thead, tfoot").remove(),w.append(t(n.nTHead).clone()).append(t(n.nTFoot).clone()),w.find("tfoot th, tfoot td").css("width",""),p=ht(n,w.find("thead")[0]),r=0;r<d.length;r++)p[r].style.width=null!==(i=s[d[r]]).sWidthOrig&&""!==i.sWidthOrig?$t(i.sWidthOrig):"",i.sWidthOrig&&c&&t(p[r]).append(t("<div/>").css({width:i.sWidthOrig,margin:0,padding:0,border:0,height:1}));if(n.aoData.length)for(r=0;r<d.length;r++)i=s[o=d[r]],t(Yt(n,o)).clone(!1).append(i.sContentPadding).appendTo(S);t("[name]",w).removeAttr("name");var C=t("<div/>").css(c||u?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(w).appendTo(m);c&&h?w.width(h):c?(w.css("width","auto"),w.removeAttr("width"),w.width()<m.clientWidth&&g&&w.width(m.clientWidth)):u?w.width(m.clientWidth):g&&w.width(g);var T=0;for(r=0;r<d.length;r++){var D=t(p[r]),k=D.outerWidth()-D.width(),E=y.bBounding?Math.ceil(p[r].getBoundingClientRect().width):D.outerWidth();T+=E,s[d[r]].sWidth=$t(E-k)}a.style.width=$t(T),C.remove()}if(g&&(a.style.width=$t(g)),(g||c)&&!n._reszEvt){var A=function(){t(e).on("resize.DT-"+n.sInstance,Vt(function(){M(n)}))};b?setTimeout(A,1e3):A(),n._reszEvt=!0}}var Vt=l.util.throttle;function Xt(e,r){if(!e)return 0;var i=t("<div/>").css("width",$t(e)).appendTo(r||n.body),o=i[0].offsetWidth;return i.remove(),o}function Yt(e,n){var r=Gt(e,n);if(r<0)return null;var i=e.aoData[r];return i.nTr?i.anCells[n]:t("<td/>").html(V(e,r,n,"display"))[0]}function Gt(t,e){for(var n,r=-1,i=-1,o=0,a=t.aoData.length;o<a;o++)(n=(n=(n=V(t,o,e,"display")+"").replace(zt,"")).replace(/&nbsp;/g," ")).length>r&&(r=n.length,i=o);return i}function $t(t){return null===t?"0px":"number"==typeof t?t<0?"0px":t+"px":t.match(/\d$/)?t+"px":t}function Qt(e){var n,i,o,a,s,u,c,h=[],f=e.aoColumns,d=e.aaSortingFixed,p=t.isPlainObject(d),g=[],m=function(e){e.length&&!t.isArray(e[0])?g.push(e):t.merge(g,e)};for(t.isArray(d)&&m(d),p&&d.pre&&m(d.pre),m(e.aaSorting),p&&d.post&&m(d.post),n=0;n<g.length;n++)for(i=0,o=(a=f[c=g[n][0]].aDataSort).length;i<o;i++)u=f[s=a[i]].sType||"string",g[n]._idx===r&&(g[n]._idx=t.inArray(g[n][1],f[s].asSorting)),h.push({src:c,col:s,dir:g[n][1],index:g[n]._idx,type:u,formatter:l.ext.type.order[u+"-pre"]});return h}function Jt(t){var e,n,r,i,o,a=[],s=l.ext.type.order,u=t.aoData,c=0,h=t.aiDisplayMaster;for(q(t),e=0,n=(o=Qt(t)).length;e<n;e++)(i=o[e]).formatter&&c++,ne(t,i.col);if("ssp"!=pe(t)&&0!==o.length){for(e=0,r=h.length;e<r;e++)a[h[e]]=e;h.sort(c===o.length?function(t,e){var n,r,i,s,l,c=o.length,h=u[t]._aSortData,f=u[e]._aSortData;for(i=0;i<c;i++)if(0!=(s=(n=h[(l=o[i]).col])<(r=f[l.col])?-1:n>r?1:0))return"asc"===l.dir?s:-s;return(n=a[t])<(r=a[e])?-1:n>r?1:0}:function(t,e){var n,r,i,l,c,h=o.length,f=u[t]._aSortData,d=u[e]._aSortData;for(i=0;i<h;i++)if(0!==(l=(s[(c=o[i]).type+"-"+c.dir]||s["string-"+c.dir])(n=f[c.col],r=d[c.col])))return l;return(n=a[t])<(r=a[e])?-1:n>r?1:0})}t.bSorted=!0}function Kt(t){for(var e,n,r=t.aoColumns,i=Qt(t),o=t.oLanguage.oAria,a=0,s=r.length;a<s;a++){var l=r[a],u=l.asSorting,c=l.sTitle.replace(/<.*?>/g,""),h=l.nTh;h.removeAttribute("aria-sort"),l.bSortable?(i.length>0&&i[0].col==a?(h.setAttribute("aria-sort","asc"==i[0].dir?"ascending":"descending"),n=u[i[0].index+1]||u[0]):n=u[0],e=c+("asc"===n?o.sSortAscending:o.sSortDescending)):e=c,h.setAttribute("aria-label",e)}}function Zt(e,n,i,o){var a,s=e.aaSorting,l=e.aoColumns[n].asSorting,u=function(e,n){var i=e._idx;return i===r&&(i=t.inArray(e[1],l)),i+1<l.length?i+1:n?null:0};if("number"==typeof s[0]&&(s=e.aaSorting=[s]),i&&e.oFeatures.bSortMulti){var c=t.inArray(n,x(s,"0"));-1!==c?(null===(a=u(s[c],!0))&&1===s.length&&(a=0),null===a?s.splice(c,1):(s[c][1]=l[a],s[c]._idx=a)):(s.push([n,l[0],0]),s[s.length-1]._idx=0)}else s.length&&s[0][0]==n?(a=u(s[0]),s.length=1,s[0][1]=l[a],s[0]._idx=a):(s.length=0,s.push([n,l[0]]),s[0]._idx=0);lt(e),"function"==typeof o&&o(e)}function te(t,e,n,r){var i=t.aoColumns[n];ue(e,{},function(e){!1!==i.bSortable&&(t.oFeatures.bProcessing?(Bt(t,!0),setTimeout(function(){Zt(t,n,e.shiftKey,r),"ssp"!==pe(t)&&Bt(t,!1)},0)):Zt(t,n,e.shiftKey,r))})}function ee(e){var n,r,i=e.aLastSort,o=e.oClasses.sSortColumn,a=Qt(e),s=e.oFeatures;if(s.bSort&&s.bSortClasses){for(n=0,r=i.length;n<r;n++)t(x(e.aoData,"anCells",i[n].src)).removeClass(o+(n<2?n+1:3));for(n=0,r=a.length;n<r;n++)t(x(e.aoData,"anCells",a[n].src)).addClass(o+(n<2?n+1:3))}e.aLastSort=a}function ne(t,e){var n,r,i,o=t.aoColumns[e],a=l.ext.order[o.sSortDataType];a&&(n=a.call(t.oInstance,t,e,O(t,e)));for(var s=l.ext.type.order[o.sType+"-pre"],u=0,c=t.aoData.length;u<c;u++)(r=t.aoData[u])._aSortData||(r._aSortData=[]),r._aSortData[e]&&!a||(i=a?n[u]:V(t,u,e,"sort"),r._aSortData[e]=s?s(i):i)}function re(e){if(e.oFeatures.bStateSave&&!e.bDestroying){var n={time:+new Date,start:e._iDisplayStart,length:e._iDisplayLength,order:t.extend(!0,[],e.aaSorting),search:kt(e.oPreviousSearch),columns:t.map(e.aoColumns,function(t,n){return{visible:t.bVisible,search:kt(e.aoPreSearchCols[n])}})};he(e,"aoStateSaveParams","stateSaveParams",[e,n]),e.oSavedState=n,e.fnStateSaveCallback.call(e.oInstance,e,n)}}function ie(e,n,i){var o,a,s=e.aoColumns,l=function(n){if(n&&n.time){var l=he(e,"aoStateLoadParams","stateLoadParams",[e,n]);if(-1===t.inArray(!1,l)){var u=e.iStateDuration;if(u>0&&n.time<+new Date-1e3*u)i();else if(n.columns&&s.length!==n.columns.length)i();else{if(e.oLoadedState=t.extend(!0,{},n),n.start!==r&&(e._iDisplayStart=n.start,e.iInitDisplayStart=n.start),n.length!==r&&(e._iDisplayLength=n.length),n.order!==r&&(e.aaSorting=[],t.each(n.order,function(t,n){e.aaSorting.push(n[0]>=s.length?[0,n[1]]:n)})),n.search!==r&&t.extend(e.oPreviousSearch,Et(n.search)),n.columns)for(o=0,a=n.columns.length;o<a;o++){var c=n.columns[o];c.visible!==r&&(s[o].bVisible=c.visible),c.search!==r&&t.extend(e.aoPreSearchCols[o],Et(c.search))}he(e,"aoStateLoaded","stateLoaded",[e,n]),i()}}else i()}else i()};if(e.oFeatures.bStateSave){var u=e.fnStateLoadCallback.call(e.oInstance,e,l);u!==r&&l(u)}else i()}function oe(e){var n=l.settings,r=t.inArray(e,x(n,"nTable"));return-1!==r?n[r]:null}function ae(t,n,r,i){if(r="DataTables warning: "+(t?"table id="+t.sTableId+" - ":"")+r,i&&(r+=". For more information about this error, please see http://datatables.net/tn/"+i),n)e.console&&console.log&&console.log(r);else{var o=l.ext,a=o.sErrMode||o.errMode;if(t&&he(t,null,"error",[t,i,r]),"alert"==a)alert(r);else{if("throw"==a)throw new Error(r);"function"==typeof a&&a(t,i,r)}}}function se(e,n,i,o){t.isArray(i)?t.each(i,function(r,i){t.isArray(i)?se(e,n,i[0],i[1]):se(e,n,i)}):(o===r&&(o=i),n[i]!==r&&(e[o]=n[i]))}function le(e,n,r){var i;for(var o in n)n.hasOwnProperty(o)&&(t.isPlainObject(i=n[o])?(t.isPlainObject(e[o])||(e[o]={}),t.extend(!0,e[o],i)):e[o]=r&&"data"!==o&&"aaData"!==o&&t.isArray(i)?i.slice():i);return e}function ue(e,n,r){t(e).on("click.DT",n,function(n){t(e).blur(),r(n)}).on("keypress.DT",n,function(t){13===t.which&&(t.preventDefault(),r(t))}).on("selectstart.DT",function(){return!1})}function ce(t,e,n,r){n&&t[e].push({fn:n,sName:r})}function he(e,n,r,i){var o=[];if(n&&(o=t.map(e[n].slice().reverse(),function(t,n){return t.fn.apply(e.oInstance,i)})),null!==r){var a=t.Event(r+".dt");t(e.nTable).trigger(a,i),o.push(a.result)}return o}function fe(t){var e=t._iDisplayStart,n=t.fnDisplayEnd(),r=t._iDisplayLength;e>=n&&(e=n-r),e-=e%r,(-1===r||e<0)&&(e=0),t._iDisplayStart=e}function de(e,n){var r=e.renderer,i=l.ext.renderer[n];return t.isPlainObject(r)&&r[n]?i[r[n]]||i._:"string"==typeof r&&i[r]||i._}function pe(t){return t.oFeatures.bServerSide?"ssp":t.ajax||t.sAjaxSource?"ajax":"dom"}var ge=[],me=Array.prototype;l.Api=o=function(e,n){if(!(this instanceof o))return new o(e,n);var r=[],i=function(e){var n=function(e){var n,r,i=l.settings,o=t.map(i,function(t,e){return t.nTable});return e?e.nTable&&e.oApi?[e]:e.nodeName&&"table"===e.nodeName.toLowerCase()?-1!==(n=t.inArray(e,o))?[i[n]]:null:e&&"function"==typeof e.settings?e.settings().toArray():("string"==typeof e?r=t(e):e instanceof t&&(r=e),r?r.map(function(e){return-1!==(n=t.inArray(this,o))?i[n]:null}).toArray():void 0):[]}(e);n&&r.push.apply(r,n)};if(t.isArray(e))for(var a=0,s=e.length;a<s;a++)i(e[a]);else i(e);this.context=T(r),n&&t.merge(this,n),this.selector={rows:null,cols:null,opts:null},o.extend(this,this,ge)},t.extend(o.prototype,{any:function(){return 0!==this.count()},concat:me.concat,context:[],count:function(){return this.flatten().length},each:function(t){for(var e=0,n=this.length;e<n;e++)t.call(this,this[e],e,this);return this},eq:function(t){var e=this.context;return e.length>t?new o(e[t],this[t]):null},filter:function(t){var e=[];if(me.filter)e=me.filter.call(this,t,this);else for(var n=0,r=this.length;n<r;n++)t.call(this,this[n],n,this)&&e.push(this[n]);return new o(this.context,e)},flatten:function(){var t=[];return new o(this.context,t.concat.apply(t,this.toArray()))},join:me.join,indexOf:me.indexOf||function(t,e){for(var n=e||0,r=this.length;n<r;n++)if(this[n]===t)return n;return-1},iterator:function(t,e,n,i){var a,s,l,u,c,h,f,d,p=[],g=this.context,m=this.selector;for("string"==typeof t&&(i=n,n=e,e=t,t=!1),s=0,l=g.length;s<l;s++){var v=new o(g[s]);if("table"===e)(a=n.call(v,g[s],s))!==r&&p.push(a);else if("columns"===e||"rows"===e)(a=n.call(v,g[s],this[s],s))!==r&&p.push(a);else if("column"===e||"column-rows"===e||"row"===e||"cell"===e)for(f=this[s],"column-rows"===e&&(h=_e(g[s],m.opts)),u=0,c=f.length;u<c;u++)d=f[u],(a="cell"===e?n.call(v,g[s],d.row,d.column,s,u):n.call(v,g[s],d,s,u,h))!==r&&p.push(a)}if(p.length||i){var y=new o(g,t?p.concat.apply([],p):p),b=y.selector;return b.rows=m.rows,b.cols=m.cols,b.opts=m.opts,y}return this},lastIndexOf:me.lastIndexOf||function(t,e){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(t){var e=[];if(me.map)e=me.map.call(this,t,this);else for(var n=0,r=this.length;n<r;n++)e.push(t.call(this,this[n],n));return new o(this.context,e)},pluck:function(t){return this.map(function(e){return e[t]})},pop:me.pop,push:me.push,reduce:me.reduce||function(t,e){return F(this,t,e,0,this.length,1)},reduceRight:me.reduceRight||function(t,e){return F(this,t,e,this.length-1,-1,-1)},reverse:me.reverse,selector:null,shift:me.shift,slice:function(){return new o(this.context,this)},sort:me.sort,splice:me.splice,toArray:function(){return me.slice.call(this)},to$:function(){return t(this)},toJQuery:function(){return t(this)},unique:function(){return new o(this.context,T(this))},unshift:me.unshift}),o.extend=function(t,e,n){if(n.length&&e&&(e instanceof o||e.__dt_wrapper)){var r,i,a,s=function(t,e,n){return function(){var r=e.apply(t,arguments);return o.extend(r,r,n.methodExt),r}};for(r=0,i=n.length;r<i;r++)e[(a=n[r]).name]="function"===a.type?s(t,a.val,a):"object"===a.type?{}:a.val,e[a.name].__dt_wrapper=!0,o.extend(t,e[a.name],a.propExt)}},o.register=a=function(e,n){if(t.isArray(e))for(var r=0,i=e.length;r<i;r++)o.register(e[r],n);else{var a,s,l,u,c=e.split("."),h=ge,f=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n].name===e)return t[n];return null};for(a=0,s=c.length;a<s;a++){var d=f(h,l=(u=-1!==c[a].indexOf("()"))?c[a].replace("()",""):c[a]);d||h.push(d={name:l,val:{},methodExt:[],propExt:[],type:"object"}),a===s-1?(d.val=n,d.type="function"==typeof n?"function":t.isPlainObject(n)?"object":"other"):h=u?d.methodExt:d.propExt}}},o.registerPlural=s=function(e,n,i){o.register(e,i),o.register(n,function(){var e=i.apply(this,arguments);return e===this?this:e instanceof o?e.length?t.isArray(e[0])?new o(e.context,e[0]):e[0]:r:e})},a("tables()",function(e){return e?new o(function(e,n){if("number"==typeof e)return[n[e]];var r=t.map(n,function(t,e){return t.nTable});return t(r).filter(e).map(function(e){var i=t.inArray(this,r);return n[i]}).toArray()}(e,this.context)):this}),a("table()",function(t){var e=this.tables(t),n=e.context;return n.length?new o(n[0]):e}),s("tables().nodes()","table().node()",function(){return this.iterator("table",function(t){return t.nTable},1)}),s("tables().body()","table().body()",function(){return this.iterator("table",function(t){return t.nTBody},1)}),s("tables().header()","table().header()",function(){return this.iterator("table",function(t){return t.nTHead},1)}),s("tables().footer()","table().footer()",function(){return this.iterator("table",function(t){return t.nTFoot},1)}),s("tables().containers()","table().container()",function(){return this.iterator("table",function(t){return t.nTableWrapper},1)}),a("draw()",function(t){return this.iterator("table",function(e){"page"===t?st(e):("string"==typeof t&&(t="full-hold"!==t),lt(e,!1===t))})}),a("page()",function(t){return t===r?this.page.info().page:this.iterator("table",function(e){Rt(e,t)})}),a("page.info()",function(t){if(0===this.context.length)return r;var e=this.context[0],n=e._iDisplayStart,i=e.oFeatures.bPaginate?e._iDisplayLength:-1,o=e.fnRecordsDisplay(),a=-1===i;return{page:a?0:Math.floor(n/i),pages:a?1:Math.ceil(o/i),start:n,end:e.fnDisplayEnd(),length:i,recordsTotal:e.fnRecordsTotal(),recordsDisplay:o,serverSide:"ssp"===pe(e)}}),a("page.len()",function(t){return t===r?0!==this.context.length?this.context[0]._iDisplayLength:r:this.iterator("table",function(e){jt(e,t)})});var ve=function(t,e,n){if(n){var r=new o(t);r.one("draw",function(){n(r.ajax.json())})}if("ssp"==pe(t))lt(t,e);else{Bt(t,!0);var i=t.jqXHR;i&&4!==i.readyState&&i.abort(),ft(t,[],function(n){Z(t);for(var r=mt(t,n),i=0,o=r.length;i<o;i++)z(t,r[i]);lt(t,e),Bt(t,!1)})}};a("ajax.json()",function(){var t=this.context;if(t.length>0)return t[0].json}),a("ajax.params()",function(){var t=this.context;if(t.length>0)return t[0].oAjaxData}),a("ajax.reload()",function(t,e){return this.iterator("table",function(n){ve(n,!1===e,t)})}),a("ajax.url()",function(e){var n=this.context;return e===r?0===n.length?r:(n=n[0]).ajax?t.isPlainObject(n.ajax)?n.ajax.url:n.ajax:n.sAjaxSource:this.iterator("table",function(n){t.isPlainObject(n.ajax)?n.ajax.url=e:n.ajax=e})}),a("ajax.url().load()",function(t,e){return this.iterator("table",function(n){ve(n,!1===e,t)})});var ye=function(e,n,o,a,s){var l,u,c,h,f,d,p=[],g=typeof n;for(n&&"string"!==g&&"function"!==g&&n.length!==r||(n=[n]),c=0,h=n.length;c<h;c++)for(f=0,d=(u=n[c]&&n[c].split&&!n[c].match(/[\[\(:]/)?n[c].split(","):[n[c]]).length;f<d;f++)(l=o("string"==typeof u[f]?t.trim(u[f]):u[f]))&&l.length&&(p=p.concat(l));var m=i.selector[e];if(m.length)for(c=0,h=m.length;c<h;c++)p=m[c](a,s,p);return T(p)},be=function(e){return e||(e={}),e.filter&&e.search===r&&(e.search=e.filter),t.extend({search:"none",order:"current",page:"all"},e)},xe=function(t){for(var e=0,n=t.length;e<n;e++)if(t[e].length>0)return t[0]=t[e],t[0].length=1,t.length=1,t.context=[t.context[e]],t;return t.length=0,t},_e=function(e,n){var r,i=[],o=e.aiDisplay,a=e.aiDisplayMaster,s=n.search,l=n.order,u=n.page;if("ssp"==pe(e))return"removed"===s?[]:w(0,a.length);if("current"==u)for(h=e._iDisplayStart,f=e.fnDisplayEnd();h<f;h++)i.push(o[h]);else if("current"==l||"applied"==l){if("none"==s)i=a.slice();else if("applied"==s)i=o.slice();else if("removed"==s){for(var c={},h=0,f=o.length;h<f;h++)c[o[h]]=null;i=t.map(a,function(t){return c.hasOwnProperty(t)?null:t})}}else if("index"==l||"original"==l)for(h=0,f=e.aoData.length;h<f;h++)("none"==s||-1===(r=t.inArray(h,o))&&"removed"==s||r>=0&&"applied"==s)&&i.push(h);return i};a("rows()",function(e,n){e===r?e="":t.isPlainObject(e)&&(n=e,e=""),n=be(n);var i=this.iterator("table",function(i){return function(e,n,i){var o;return ye("row",n,function(n){var a=m(n),s=e.aoData;if(null!==a&&!i)return[a];if(o||(o=_e(e,i)),null!==a&&-1!==t.inArray(a,o))return[a];if(null===n||n===r||""===n)return o;if("function"==typeof n)return t.map(o,function(t){var e=s[t];return n(t,e._aData,e.nTr)?t:null});if(n.nodeName){var l=n._DT_RowIndex,u=n._DT_CellIndex;if(l!==r)return s[l]&&s[l].nTr===n?[l]:[];if(u)return s[u.row]&&s[u.row].nTr===n.parentNode?[u.row]:[];var c=t(n).closest("*[data-dt-row]");return c.length?[c.data("dt-row")]:[]}if("string"==typeof n&&"#"===n.charAt(0)){var h=e.aIds[n.replace(/^#/,"")];if(h!==r)return[h.idx]}var f=S(_(e.aoData,o,"nTr"));return t(f).filter(n).map(function(){return this._DT_RowIndex}).toArray()},e,i)}(i,e,n)},1);return i.selector.rows=e,i.selector.opts=n,i}),a("rows().nodes()",function(){return this.iterator("row",function(t,e){return t.aoData[e].nTr||r},1)}),a("rows().data()",function(){return this.iterator(!0,"rows",function(t,e){return _(t.aoData,e,"_aData")},1)}),s("rows().cache()","row().cache()",function(t){return this.iterator("row",function(e,n){var r=e.aoData[n];return"search"===t?r._aFilterData:r._aSortData},1)}),s("rows().invalidate()","row().invalidate()",function(t){return this.iterator("row",function(e,n){et(e,n,t)})}),s("rows().indexes()","row().index()",function(){return this.iterator("row",function(t,e){return e},1)}),s("rows().ids()","row().id()",function(t){for(var e=[],n=this.context,r=0,i=n.length;r<i;r++)for(var a=0,s=this[r].length;a<s;a++){var l=n[r].rowIdFn(n[r].aoData[this[r][a]]._aData);e.push((!0===t?"#":"")+l)}return new o(n,e)}),s("rows().remove()","row().remove()",function(){var t=this;return this.iterator("row",function(e,n,i){var o,a,s,l,u,c,h=e.aoData,f=h[n];for(h.splice(n,1),o=0,a=h.length;o<a;o++)if(c=(u=h[o]).anCells,null!==u.nTr&&(u.nTr._DT_RowIndex=o),null!==c)for(s=0,l=c.length;s<l;s++)c[s]._DT_CellIndex.row=o;tt(e.aiDisplayMaster,n),tt(e.aiDisplay,n),tt(t[i],n,!1),e._iRecordsDisplay>0&&e._iRecordsDisplay--,fe(e);var d=e.rowIdFn(f._aData);d!==r&&delete e.aIds[d]}),this.iterator("table",function(t){for(var e=0,n=t.aoData.length;e<n;e++)t.aoData[e].idx=e}),this}),a("rows.add()",function(e){var n=this.iterator("table",function(t){var n,r,i,o=[];for(r=0,i=e.length;r<i;r++)(n=e[r]).nodeName&&"TR"===n.nodeName.toUpperCase()?o.push(U(t,n)[0]):o.push(z(t,n));return o},1),r=this.rows(-1);return r.pop(),t.merge(r,n),r}),a("row()",function(t,e){return xe(this.rows(t,e))}),a("row().data()",function(e){var n=this.context;if(e===r)return n.length&&this.length?n[0].aoData[this[0]]._aData:r;var i=n[0].aoData[this[0]];return i._aData=e,t.isArray(e)&&i.nTr.id&&J(n[0].rowId)(e,i.nTr.id),et(n[0],this[0],"data"),this}),a("row().node()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]].nTr||null}),a("row.add()",function(e){e instanceof t&&e.length&&(e=e[0]);var n=this.iterator("table",function(t){return e.nodeName&&"TR"===e.nodeName.toUpperCase()?U(t,e)[0]:z(t,e)});return this.row(n[0])});var we=function(t,e){var n=t.context;if(n.length){var i=n[0].aoData[e!==r?e:t[0]];i&&i._details&&(i._details.remove(),i._detailsShow=r,i._details=r)}},Se=function(t,e){var n=t.context;if(n.length&&t.length){var r=n[0].aoData[t[0]];r._details&&(r._detailsShow=e,e?r._details.insertAfter(r.nTr):r._details.detach(),Ce(n[0]))}},Ce=function(t){var e=new o(t),n=".dt.DT_details",r="draw"+n,i="column-visibility"+n,a="destroy"+n,s=t.aoData;e.off(r+" "+i+" "+a),x(s,"_details").length>0&&(e.on(r,function(n,r){t===r&&e.rows({page:"current"}).eq(0).each(function(t){var e=s[t];e._detailsShow&&e._details.insertAfter(e.nTr)})}),e.on(i,function(e,n,r,i){if(t===n)for(var o,a=B(n),l=0,u=s.length;l<u;l++)(o=s[l])._details&&o._details.children("td[colspan]").attr("colspan",a)}),e.on(a,function(n,r){if(t===r)for(var i=0,o=s.length;i<o;i++)s[i]._details&&we(e,i)}))},Te="row().child()";a(Te,function(e,n){var i=this.context;return e===r?i.length&&this.length?i[0].aoData[this[0]]._details:r:(!0===e?this.child.show():!1===e?we(this):i.length&&this.length&&function(e,n,r,i){var o=[],a=function(n,r){if(t.isArray(n)||n instanceof t)for(var i=0,s=n.length;i<s;i++)a(n[i],r);else if(n.nodeName&&"tr"===n.nodeName.toLowerCase())o.push(n);else{var l=t("<tr><td/></tr>").addClass(r);t("td",l).addClass(r).html(n)[0].colSpan=B(e),o.push(l[0])}};a(r,i),n._details&&n._details.detach(),n._details=t(o),n._detailsShow&&n._details.insertAfter(n.nTr)}(i[0],i[0].aoData[this[0]],e,n),this)}),a(["row().child.show()",Te+".show()"],function(t){return Se(this,!0),this}),a(["row().child.hide()",Te+".hide()"],function(){return Se(this,!1),this}),a(["row().child.remove()",Te+".remove()"],function(){return we(this),this}),a("row().child.isShown()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]]._detailsShow||!1});var De=/^([^:]+):(name|visIdx|visible)$/,ke=function(t,e,n,r,i){for(var o=[],a=0,s=i.length;a<s;a++)o.push(V(t,i[a],e));return o};a("columns()",function(e,n){e===r?e="":t.isPlainObject(e)&&(n=e,e=""),n=be(n);var i=this.iterator("table",function(r){return function(e,n,r){var i=e.aoColumns,o=x(i,"sName"),a=x(i,"nTh");return ye("column",n,function(n){var s=m(n);if(""===n)return w(i.length);if(null!==s)return[s>=0?s:i.length+s];if("function"==typeof n){var l=_e(e,r);return t.map(i,function(t,r){return n(r,ke(e,r,0,0,l),a[r])?r:null})}var u="string"==typeof n?n.match(De):"";if(u)switch(u[2]){case"visIdx":case"visible":var c=parseInt(u[1],10);if(c<0){var h=t.map(i,function(t,e){return t.bVisible?e:null});return[h[h.length+c]]}return[R(e,c)];case"name":return t.map(o,function(t,e){return t===u[1]?e:null});default:return[]}if(n.nodeName&&n._DT_CellIndex)return[n._DT_CellIndex.column];var f=t(a).filter(n).map(function(){return t.inArray(this,a)}).toArray();if(f.length||!n.nodeName)return f;var d=t(n).closest("*[data-dt-column]");return d.length?[d.data("dt-column")]:[]},e,r)}(r,e,n)},1);return i.selector.cols=e,i.selector.opts=n,i}),s("columns().header()","column().header()",function(t,e){return this.iterator("column",function(t,e){return t.aoColumns[e].nTh},1)}),s("columns().footer()","column().footer()",function(t,e){return this.iterator("column",function(t,e){return t.aoColumns[e].nTf},1)}),s("columns().data()","column().data()",function(){return this.iterator("column-rows",ke,1)}),s("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e].mData},1)}),s("columns().cache()","column().cache()",function(t){return this.iterator("column-rows",function(e,n,r,i,o){return _(e.aoData,o,"search"===t?"_aFilterData":"_aSortData",n)},1)}),s("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(t,e,n,r,i){return _(t.aoData,i,"anCells",e)},1)}),s("columns().visible()","column().visible()",function(e,n){var i=this,o=this.iterator("column",function(n,i){if(e===r)return n.aoColumns[i].bVisible;!function(e,n,i){var o,a,s,l,u=e.aoColumns,c=u[n],h=e.aoData;if(i===r)return c.bVisible;if(c.bVisible!==i){if(i){var f=t.inArray(!0,x(u,"bVisible"),n+1);for(a=0,s=h.length;a<s;a++)o=h[a].anCells,(l=h[a].nTr)&&l.insertBefore(o[n],o[f]||null)}else t(x(e.aoData,"anCells",n)).detach();c.bVisible=i}}(n,i,e)});return e!==r&&this.iterator("table",function(o){at(o,o.aoHeader),at(o,o.aoFooter),o.aiDisplay.length||t(o.nTBody).find("td[colspan]").attr("colspan",B(o)),re(o),i.iterator("column",function(t,r){he(t,null,"column-visibility",[t,r,e,n])}),(n===r||n)&&i.columns.adjust()}),o}),s("columns().indexes()","column().index()",function(t){return this.iterator("column",function(e,n){return"visible"===t?O(e,n):n},1)}),a("columns.adjust()",function(){return this.iterator("table",function(t){M(t)},1)}),a("column.index()",function(t,e){if(0!==this.context.length){var n=this.context[0];if("fromVisible"===t||"toData"===t)return R(n,e);if("fromData"===t||"toVisible"===t)return O(n,e)}}),a("column()",function(t,e){return xe(this.columns(t,e))}),a("cells()",function(e,n,i){if(t.isPlainObject(e)&&(e.row===r?(i=e,e=null):(i=n,n=null)),t.isPlainObject(n)&&(i=n,n=null),null===n||n===r)return this.iterator("table",function(n){return function(e,n,i){var o,a,s,l,u,c,h,f=e.aoData,d=_e(e,i),p=S(_(f,d,"anCells")),g=t([].concat.apply([],p)),m=e.aoColumns.length;return ye("cell",n,function(n){var i="function"==typeof n;if(null===n||n===r||i){for(a=[],s=0,l=d.length;s<l;s++)for(o=d[s],u=0;u<m;u++)c={row:o,column:u},i?(h=f[o],n(c,V(e,o,u),h.anCells?h.anCells[u]:null)&&a.push(c)):a.push(c);return a}if(t.isPlainObject(n))return n.column!==r&&n.row!==r&&-1!==t.inArray(n.row,d)?[n]:[];var p=g.filter(n).map(function(t,e){return{row:e._DT_CellIndex.row,column:e._DT_CellIndex.column}}).toArray();return p.length||!n.nodeName?p:(h=t(n).closest("*[data-dt-row]")).length?[{row:h.data("dt-row"),column:h.data("dt-column")}]:[]},e,i)}(n,e,be(i))});var o,a,s,l,u=i?{page:i.page,order:i.order,search:i.search}:{},c=this.columns(n,u),h=this.rows(e,u),f=this.iterator("table",function(t,e){var n=[];for(o=0,a=h[e].length;o<a;o++)for(s=0,l=c[e].length;s<l;s++)n.push({row:h[e][o],column:c[e][s]});return n},1),d=i&&i.selected?this.cells(f,i):f;return t.extend(d.selector,{cols:n,rows:e,opts:i}),d}),s("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(t,e,n){var i=t.aoData[e];return i&&i.anCells?i.anCells[n]:r},1)}),a("cells().data()",function(){return this.iterator("cell",function(t,e,n){return V(t,e,n)},1)}),s("cells().cache()","cell().cache()",function(t){return t="search"===t?"_aFilterData":"_aSortData",this.iterator("cell",function(e,n,r){return e.aoData[n][t][r]},1)}),s("cells().render()","cell().render()",function(t){return this.iterator("cell",function(e,n,r){return V(e,n,r,t)},1)}),s("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(t,e,n){return{row:e,column:n,columnVisible:O(t,n)}},1)}),s("cells().invalidate()","cell().invalidate()",function(t){return this.iterator("cell",function(e,n,r){et(e,n,t,r)})}),a("cell()",function(t,e,n){return xe(this.cells(t,e,n))}),a("cell().data()",function(t){var e=this.context,n=this[0];return t===r?e.length&&n.length?V(e[0],n[0].row,n[0].column):r:(X(e[0],n[0].row,n[0].column,t),et(e[0],n[0].row,"data",n[0].column),this)}),a("order()",function(e,n){var i=this.context;return e===r?0!==i.length?i[0].aaSorting:r:("number"==typeof e?e=[[e,n]]:e.length&&!t.isArray(e[0])&&(e=Array.prototype.slice.call(arguments)),this.iterator("table",function(t){t.aaSorting=e.slice()}))}),a("order.listener()",function(t,e,n){return this.iterator("table",function(r){te(r,t,e,n)})}),a("order.fixed()",function(e){if(!e){var n=this.context,i=n.length?n[0].aaSortingFixed:r;return t.isArray(i)?{pre:i}:i}return this.iterator("table",function(n){n.aaSortingFixed=t.extend(!0,{},e)})}),a(["columns().order()","column().order()"],function(e){var n=this;return this.iterator("table",function(r,i){var o=[];t.each(n[i],function(t,n){o.push([n,e])}),r.aaSorting=o})}),a("search()",function(e,n,i,o){var a=this.context;return e===r?0!==a.length?a[0].oPreviousSearch.sSearch:r:this.iterator("table",function(r){r.oFeatures.bFilter&&yt(r,t.extend({},r.oPreviousSearch,{sSearch:e+"",bRegex:null!==n&&n,bSmart:null===i||i,bCaseInsensitive:null===o||o}),1)})}),s("columns().search()","column().search()",function(e,n,i,o){return this.iterator("column",function(a,s){var l=a.aoPreSearchCols;if(e===r)return l[s].sSearch;a.oFeatures.bFilter&&(t.extend(l[s],{sSearch:e+"",bRegex:null!==n&&n,bSmart:null===i||i,bCaseInsensitive:null===o||o}),yt(a,a.oPreviousSearch,1))})}),a("state()",function(){return this.context.length?this.context[0].oSavedState:null}),a("state.clear()",function(){return this.iterator("table",function(t){t.fnStateSaveCallback.call(t.oInstance,t,{})})}),a("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null}),a("state.save()",function(){return this.iterator("table",function(t){re(t)})}),l.versionCheck=l.fnVersionCheck=function(t){for(var e,n,r=l.version.split("."),i=t.split("."),o=0,a=i.length;o<a;o++)if((e=parseInt(r[o],10)||0)!==(n=parseInt(i[o],10)||0))return e>n;return!0},l.isDataTable=l.fnIsDataTable=function(e){var n=t(e).get(0),r=!1;return e instanceof l.Api||(t.each(l.settings,function(e,i){var o=i.nScrollHead?t("table",i.nScrollHead)[0]:null,a=i.nScrollFoot?t("table",i.nScrollFoot)[0]:null;i.nTable!==n&&o!==n&&a!==n||(r=!0)}),r)},l.tables=l.fnTables=function(e){var n=!1;t.isPlainObject(e)&&(n=e.api,e=e.visible);var r=t.map(l.settings,function(n){if(!e||e&&t(n.nTable).is(":visible"))return n.nTable});return n?new o(r):r},l.camelToHungarian=k,a("$()",function(e,n){var r=this.rows(n).nodes(),i=t(r);return t([].concat(i.filter(e).toArray(),i.find(e).toArray()))}),t.each(["on","one","off"],function(e,n){a(n+"()",function(){var e=Array.prototype.slice.call(arguments);e[0]=t.map(e[0].split(/\s/),function(t){return t.match(/\.dt\b/)?t:t+".dt"}).join(" ");var r=t(this.tables().nodes());return r[n].apply(r,e),this})}),a("clear()",function(){return this.iterator("table",function(t){Z(t)})}),a("settings()",function(){return new o(this.context,this.context)}),a("init()",function(){var t=this.context;return t.length?t[0].oInit:null}),a("data()",function(){return this.iterator("table",function(t){return x(t.aoData,"_aData")}).flatten()}),a("destroy()",function(n){return n=n||!1,this.iterator("table",function(r){var i,a=r.nTableWrapper.parentNode,s=r.oClasses,u=r.nTable,c=r.nTBody,h=r.nTHead,f=r.nTFoot,d=t(u),p=t(c),g=t(r.nTableWrapper),m=t.map(r.aoData,function(t){return t.nTr});r.bDestroying=!0,he(r,"aoDestroyCallback","destroy",[r]),n||new o(r).columns().visible(!0),g.off(".DT").find(":not(tbody *)").off(".DT"),t(e).off(".DT-"+r.sInstance),u!=h.parentNode&&(d.children("thead").detach(),d.append(h)),f&&u!=f.parentNode&&(d.children("tfoot").detach(),d.append(f)),r.aaSorting=[],r.aaSortingFixed=[],ee(r),t(m).removeClass(r.asStripeClasses.join(" ")),t("th, td",h).removeClass(s.sSortable+" "+s.sSortableAsc+" "+s.sSortableDesc+" "+s.sSortableNone),p.children().detach(),p.append(m);var v=n?"remove":"detach";d[v](),g[v](),!n&&a&&(a.insertBefore(u,r.nTableReinsertBefore),d.css("width",r.sDestroyWidth).removeClass(s.sTable),(i=r.asDestroyStripes.length)&&p.children().each(function(e){t(this).addClass(r.asDestroyStripes[e%i])}));var y=t.inArray(r,l.settings);-1!==y&&l.settings.splice(y,1)})}),t.each(["column","row","cell"],function(t,e){a(e+"s().every()",function(t){var n=this.selector.opts,i=this;return this.iterator(e,function(o,a,s,l,u){t.call(i[e](a,"cell"===e?s:n,"cell"===e?n:r),a,s,l,u)})})}),a("i18n()",function(e,n,i){var o=this.context[0],a=Q(e)(o.oLanguage);return a===r&&(a=n),i!==r&&t.isPlainObject(a)&&(a=a[i]!==r?a[i]:a._),a.replace("%d",i)}),l.version="1.10.20",l.settings=[],l.models={},l.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0},l.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null,idx:-1},l.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null},l.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(t){try{return JSON.parse((-1===t.iStateDuration?sessionStorage:localStorage).getItem("DataTables_"+t.sInstance+"_"+location.pathname))}catch(e){}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(t,e){try{(-1===t.iStateDuration?sessionStorage:localStorage).setItem("DataTables_"+t.sInstance+"_"+location.pathname,JSON.stringify(e))}catch(n){}},fnStateSaveParams:null,iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"Processing...",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:t.extend({},l.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId"},D(l.defaults),l.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc","desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null},D(l.defaults.column),l.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1,bBounding:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,bAjaxDataGet:!0,jqXHR:null,json:r,oAjaxData:r,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return"ssp"==pe(this)?1*this._iRecordsTotal:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return"ssp"==pe(this)?1*this._iRecordsDisplay:this.aiDisplay.length},fnDisplayEnd:function(){var t=this._iDisplayLength,e=this._iDisplayStart,n=e+t,r=this.aiDisplay.length,i=this.oFeatures,o=i.bPaginate;return i.bServerSide?!1===o||-1===t?e+r:Math.min(e+t,this._iRecordsDisplay):!o||n>r||-1===t?r:n},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null},l.ext=i={buttons:{},classes:{},builder:"-source-",errMode:"alert",feature:[],search:[],selector:{cell:[],column:[],row:[]},internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:l.fnVersionCheck,iApiIndex:0,oJUIClasses:{},sVersion:l.version},t.extend(i,{afnFiltering:i.search,aTypes:i.type.detect,ofnSearch:i.type.search,oSort:i.type.order,afnSortData:i.order,aoFeatures:i.feature,oApi:i.internal,oStdClasses:i.classes,oPagination:i.pager}),t.extend(l.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_asc_disabled",sSortableDesc:"sorting_desc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""});var Ee=l.ext.pager;function Ae(t,e){var n=[],r=Ee.numbers_length,i=Math.floor(r/2);return e<=r?n=w(0,e):t<=i?((n=w(0,r-2)).push("ellipsis"),n.push(e-1)):t>=e-1-i?((n=w(e-(r-2),e)).splice(0,0,"ellipsis"),n.splice(0,0,0)):((n=w(t-i+2,t+i-1)).push("ellipsis"),n.push(e-1),n.splice(0,0,"ellipsis"),n.splice(0,0,0)),n.DT_el="span",n}t.extend(Ee,{simple:function(t,e){return["previous","next"]},full:function(t,e){return["first","previous","next","last"]},numbers:function(t,e){return[Ae(t,e)]},simple_numbers:function(t,e){return["previous",Ae(t,e),"next"]},full_numbers:function(t,e){return["first","previous",Ae(t,e),"next","last"]},first_last_numbers:function(t,e){return["first",Ae(t,e),"last"]},_numbers:Ae,numbers_length:7}),t.extend(!0,l.ext.renderer,{pageButton:{_:function(e,i,o,a,s,l){var u,c,h,f=e.oClasses,d=e.oLanguage.oPaginate,p=e.oLanguage.oAria.paginate||{},g=0,m=function(n,r){var i,a,h,v,y=f.sPageButtonDisabled,b=function(t){Rt(e,t.data.action,!0)};for(i=0,a=r.length;i<a;i++)if(t.isArray(h=r[i])){var x=t("<"+(h.DT_el||"div")+"/>").appendTo(n);m(x,h)}else{switch(u=null,c=h,v=e.iTabIndex,h){case"ellipsis":n.append('<span class="ellipsis">&#x2026;</span>');break;case"first":u=d.sFirst,0===s&&(v=-1,c+=" "+y);break;case"previous":u=d.sPrevious,0===s&&(v=-1,c+=" "+y);break;case"next":u=d.sNext,s===l-1&&(v=-1,c+=" "+y);break;case"last":u=d.sLast,s===l-1&&(v=-1,c+=" "+y);break;default:u=h+1,c=s===h?f.sPageButtonActive:""}null!==u&&(ue(t("<a>",{class:f.sPageButton+" "+c,"aria-controls":e.sTableId,"aria-label":p[h],"data-dt-idx":g,tabindex:v,id:0===o&&"string"==typeof h?e.sTableId+"_"+h:null}).html(u).appendTo(n),{action:h},b),g++)}};try{h=t(i).find(n.activeElement).data("dt-idx")}catch(v){}m(t(i).empty(),a),h!==r&&t(i).find("[data-dt-idx="+h+"]").focus()}}}),t.extend(l.ext.type.detect,[function(t,e){var n=e.oLanguage.sDecimal;return y(t,n)?"num"+n:null},function(t,e){if(t&&!(t instanceof Date)&&!f.test(t))return null;var n=Date.parse(t);return null!==n&&!isNaN(n)||g(t)?"date":null},function(t,e){var n=e.oLanguage.sDecimal;return y(t,n,!0)?"num-fmt"+n:null},function(t,e){var n=e.oLanguage.sDecimal;return b(t,n)?"html-num"+n:null},function(t,e){var n=e.oLanguage.sDecimal;return b(t,n,!0)?"html-num-fmt"+n:null},function(t,e){return g(t)||"string"==typeof t&&-1!==t.indexOf("<")?"html":null}]),t.extend(l.ext.type.search,{html:function(t){return g(t)?t:"string"==typeof t?t.replace(c," ").replace(h,""):""},string:function(t){return g(t)?t:"string"==typeof t?t.replace(c," "):t}});var Le=function(t,e,n,r){return 0===t||t&&"-"!==t?(e&&(t=v(t,e)),t.replace&&(n&&(t=t.replace(n,"")),r&&(t=t.replace(r,""))),1*t):-1/0};function Ie(e){t.each({num:function(t){return Le(t,e)},"num-fmt":function(t){return Le(t,e,p)},"html-num":function(t){return Le(t,e,h)},"html-num-fmt":function(t){return Le(t,e,h,p)}},function(t,n){i.type.order[t+e+"-pre"]=n,t.match(/^html\-/)&&(i.type.search[t+e]=i.type.search.html)})}t.extend(i.type.order,{"date-pre":function(t){var e=Date.parse(t);return isNaN(e)?-1/0:e},"html-pre":function(t){return g(t)?"":t.replace?t.replace(/<.*?>/g,"").toLowerCase():t+""},"string-pre":function(t){return g(t)?"":"string"==typeof t?t.toLowerCase():t.toString?t.toString():""},"string-asc":function(t,e){return t<e?-1:t>e?1:0},"string-desc":function(t,e){return t<e?1:t>e?-1:0}}),Ie(""),t.extend(!0,l.ext.renderer,{header:{_:function(e,n,r,i){t(e.nTable).on("order.dt.DT",function(t,o,a,s){if(e===o){var l=r.idx;n.removeClass(r.sSortingClass+" "+i.sSortAsc+" "+i.sSortDesc).addClass("asc"==s[l]?i.sSortAsc:"desc"==s[l]?i.sSortDesc:r.sSortingClass)}})},jqueryui:function(e,n,r,i){t("<div/>").addClass(i.sSortJUIWrapper).append(n.contents()).append(t("<span/>").addClass(i.sSortIcon+" "+r.sSortingClassJUI)).appendTo(n),t(e.nTable).on("order.dt.DT",function(t,o,a,s){if(e===o){var l=r.idx;n.removeClass(i.sSortAsc+" "+i.sSortDesc).addClass("asc"==s[l]?i.sSortAsc:"desc"==s[l]?i.sSortDesc:r.sSortingClass),n.find("span."+i.sSortIcon).removeClass(i.sSortJUIAsc+" "+i.sSortJUIDesc+" "+i.sSortJUI+" "+i.sSortJUIAscAllowed+" "+i.sSortJUIDescAllowed).addClass("asc"==s[l]?i.sSortJUIAsc:"desc"==s[l]?i.sSortJUIDesc:r.sSortingClassJUI)}})}}});var Ne=function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):t};function Fe(t){return function(){var e=[oe(this[l.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return l.ext.internal[t].apply(this,e)}}return l.render={number:function(t,e,n,r,i){return{display:function(o){if("number"!=typeof o&&"string"!=typeof o)return o;var a=o<0?"-":"",s=parseFloat(o);if(isNaN(s))return Ne(o);s=s.toFixed(n),o=Math.abs(s);var l=parseInt(o,10),u=n?e+(o-l).toFixed(n).substring(2):"";return a+(r||"")+l.toString().replace(/\B(?=(\d{3})+(?!\d))/g,t)+u+(i||"")}}},text:function(){return{display:Ne,filter:Ne}}},t.extend(l.ext.internal,{_fnExternApiFunc:Fe,_fnBuildAjax:ft,_fnAjaxUpdate:dt,_fnAjaxParameters:pt,_fnAjaxUpdateDraw:gt,_fnAjaxDataSrc:mt,_fnAddColumn:j,_fnColumnOptions:P,_fnAdjustColumnSizing:M,_fnVisibleToColumnIndex:R,_fnColumnIndexToVisible:O,_fnVisbleColumns:B,_fnGetColumns:H,_fnColumnTypes:q,_fnApplyColumnDefs:W,_fnHungarianMap:D,_fnCamelToHungarian:k,_fnLanguageCompat:E,_fnBrowserDetect:N,_fnAddData:z,_fnAddTr:U,_fnNodeToDataIndex:function(t,e){return e._DT_RowIndex!==r?e._DT_RowIndex:null},_fnNodeToColumnIndex:function(e,n,r){return t.inArray(r,e.aoData[n].anCells)},_fnGetCellData:V,_fnSetCellData:X,_fnSplitObjNotation:$,_fnGetObjectDataFn:Q,_fnSetObjectDataFn:J,_fnGetDataMaster:K,_fnClearTable:Z,_fnDeleteIndex:tt,_fnInvalidate:et,_fnGetRowElements:nt,_fnCreateTr:rt,_fnBuildHead:ot,_fnDrawHead:at,_fnDraw:st,_fnReDraw:lt,_fnAddOptionsHtml:ut,_fnDetectHeader:ct,_fnGetUniqueThs:ht,_fnFeatureHtmlFilter:vt,_fnFilterComplete:yt,_fnFilterCustom:bt,_fnFilterColumn:xt,_fnFilter:_t,_fnFilterCreateSearch:wt,_fnEscapeRegex:St,_fnFilterData:Dt,_fnFeatureHtmlInfo:At,_fnUpdateInfo:Lt,_fnInfoMacros:It,_fnInitialise:Nt,_fnInitComplete:Ft,_fnLengthChange:jt,_fnFeatureHtmlLength:Pt,_fnFeatureHtmlPaginate:Mt,_fnPageChange:Rt,_fnFeatureHtmlProcessing:Ot,_fnProcessingDisplay:Bt,_fnFeatureHtmlTable:Ht,_fnScrollDraw:qt,_fnApplyToChildren:Wt,_fnCalculateColumnWidths:Ut,_fnThrottle:Vt,_fnConvertToWidth:Xt,_fnGetWidestNode:Yt,_fnGetMaxLenString:Gt,_fnStringToCss:$t,_fnSortFlatten:Qt,_fnSort:Jt,_fnSortAria:Kt,_fnSortListener:Zt,_fnSortAttachListener:te,_fnSortingClasses:ee,_fnSortData:ne,_fnSaveState:re,_fnLoadState:ie,_fnSettingsFromNode:oe,_fnLog:ae,_fnMap:se,_fnBindAction:ue,_fnCallbackReg:ce,_fnCallbackFire:he,_fnLengthOverflow:fe,_fnRenderer:de,_fnDataSource:pe,_fnRowAttributes:it,_fnExtend:le,_fnCalculateEnd:function(){}}),t.fn.dataTable=l,l.$=t,t.fn.dataTableSettings=l.settings,t.fn.dataTableExt=l.ext,t.fn.DataTable=function(e){return t(this).dataTable(e).api()},t.each(l,function(e,n){t.fn.DataTable[e]=n}),t.fn.dataTable}),function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Raphael=e():t.Raphael=e()}(window,function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,(function(e){return t[e]}).bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="./dev/raphael.amd.js")}({"./dev/raphael.amd.js":function(t,e,n){var r,i;r=[n("./dev/raphael.core.js"),n("./dev/raphael.svg.js"),n("./dev/raphael.vml.js")],void 0===(i=(function(t){return t}).apply(e,r))||(t.exports=i)},"./dev/raphael.core.js":function(t,e,n){var r,i;r=[n("./node_modules/eve-raphael/eve.js")],void 0===(i=(function(t){function e(r){if(e.is(r,"function"))return n?r():t.on("raphael.DOMload",r);if(e.is(r,A))return e._engine.create[d](e,r.splice(0,3+e.is(r[0],k))).add(r);var i=Array.prototype.slice.call(arguments,0);if(e.is(i[i.length-1],"function")){var o=i.pop();return n?o.call(e._engine.create[d](e,i)):t.on("raphael.DOMload",function(){o.call(e._engine.create[d](e,i))})}return e._engine.create[d](e,arguments)}e.version="2.3.0",e.eve=t;var n,r,i,o,a=/[, ]+/,s={circle:1,rect:1,path:1,ellipse:1,text:1,image:1},l=/\{(\d+)\}/g,u="hasOwnProperty",c={doc:document,win:window},h={was:Object.prototype[u].call(c.win,"Raphael"),is:c.win.Raphael},f=function(){this.ca=this.customAttributes={}},d="apply",p="ontouchstart"in window||window.TouchEvent||window.DocumentTouch&&document instanceof DocumentTouch,g="",m=" ",v=String,y="click dblclick mousedown mousemove mouseout mouseover mouseup touchstart touchmove touchend touchcancel".split(m),b={mousedown:"touchstart",mousemove:"touchmove",mouseup:"touchend"},x=v.prototype.toLowerCase,_=Math,w=_.max,S=_.min,C=_.abs,T=_.pow,D=_.PI,k="number",E="string",A="array",L=Object.prototype.toString,I=(e._ISURL=/^url\(['"]?(.+?)['"]?\)$/i,/^\s*((#[a-f\d]{6})|(#[a-f\d]{3})|rgba?\(\s*([\d\.]+%?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+%?(?:\s*,\s*[\d\.]+%?)?)\s*\)|hsba?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\)|hsla?\(\s*([\d\.]+(?:deg|\xb0|%)?\s*,\s*[\d\.]+%?\s*,\s*[\d\.]+(?:%?\s*,\s*[\d\.]+)?)%?\s*\))\s*$/i),N={NaN:1,Infinity:1,"-Infinity":1},F=/^(?:cubic-)?bezier\(([^,]+),([^,]+),([^,]+),([^\)]+)\)/,j=_.round,P=parseFloat,M=parseInt,R=v.prototype.toUpperCase,O=e._availableAttrs={"arrow-end":"none","arrow-start":"none",blur:0,"clip-rect":"0 0 1e9 1e9",cursor:"default",cx:0,cy:0,fill:"#fff","fill-opacity":1,font:'10px "Arial"',"font-family":'"Arial"',"font-size":"10","font-style":"normal","font-weight":400,gradient:0,height:0,href:"http://raphaeljs.com/","letter-spacing":0,opacity:1,path:"M0,0",r:0,rx:0,ry:0,src:"",stroke:"#000","stroke-dasharray":"","stroke-linecap":"butt","stroke-linejoin":"butt","stroke-miterlimit":0,"stroke-opacity":1,"stroke-width":1,target:"_blank","text-anchor":"middle",title:"Raphael",transform:"",width:0,x:0,y:0,class:""},B=e._availableAnimAttrs={blur:k,"clip-rect":"csv",cx:k,cy:k,fill:"colour","fill-opacity":k,"font-size":k,height:k,opacity:k,path:"path",r:k,rx:k,ry:k,stroke:"colour","stroke-opacity":k,"stroke-width":k,transform:"transform",width:k,x:k,y:k},H=/[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/,q={hs:1,rg:1},W=/,?([achlmqrstvxz]),?/gi,z=/([achlmrqstvz])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/gi,U=/([rstm])[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029,]*((-?\d*\.?\d*(?:e[\-+]?\d+)?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*)+)/gi,V=/(-?\d*\.?\d*(?:e[\-+]?\d+)?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,?[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*/gi,X=(e._radial_gradient=/^r(?:\(([^,]+?)[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*,[\x09\x0a\x0b\x0c\x0d\x20\xa0\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\u2028\u2029]*([^\)]+?)\))?/,{}),Y=function(t,e){return P(t)-P(e)},G=function(t){return t},$=e._rectPath=function(t,e,n,r,i){return i?[["M",t+i,e],["l",n-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-n,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]]:[["M",t,e],["l",n,0],["l",0,r],["l",-n,0],["z"]]},Q=function(t,e,n,r){return null==r&&(r=n),[["M",t,e],["m",0,-r],["a",n,r,0,1,1,0,2*r],["a",n,r,0,1,1,0,-2*r],["z"]]},J=e._getPath={path:function(t){return t.attr("path")},circle:function(t){var e=t.attrs;return Q(e.cx,e.cy,e.r)},ellipse:function(t){var e=t.attrs;return Q(e.cx,e.cy,e.rx,e.ry)},rect:function(t){var e=t.attrs;return $(e.x,e.y,e.width,e.height,e.r)},image:function(t){var e=t.attrs;return $(e.x,e.y,e.width,e.height)},text:function(t){var e=t._getBBox();return $(e.x,e.y,e.width,e.height)},set:function(t){var e=t._getBBox();return $(e.x,e.y,e.width,e.height)}},K=e.mapPath=function(t,e){if(!e)return t;var n,r,i,o,a,s,l;for(i=0,a=(t=kt(t)).length;i<a;i++)for(o=1,s=(l=t[i]).length;o<s;o+=2)n=e.x(l[o],l[o+1]),r=e.y(l[o],l[o+1]),l[o]=n,l[o+1]=r;return t};if(e._g=c,"VML"==(e.type=c.win.SVGAngle||c.doc.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")?"SVG":"VML")){var Z,tt=c.doc.createElement("div");if(tt.innerHTML='<v:shape adj="1"/>',(Z=tt.firstChild).style.behavior="url(#default#VML)",!Z||"object"!=typeof Z.adj)return e.type=g;tt=null}function et(t){if("function"==typeof t||Object(t)!==t)return t;var e=new t.constructor;for(var n in t)t[u](n)&&(e[n]=et(t[n]));return e}e.svg=!(e.vml="VML"==e.type),e._Paper=f,e.fn=r=f.prototype=e.prototype,e._id=0,e.is=function(t,e){return"finite"==(e=x.call(e))?!N[u](+t):"array"==e?t instanceof Array:"null"==e&&null===t||e==typeof t&&null!==t||"object"==e&&t===Object(t)||"array"==e&&Array.isArray&&Array.isArray(t)||L.call(t).slice(8,-1).toLowerCase()==e},e.angle=function(t,n,r,i,o,a){if(null==o){var s=t-r,l=n-i;return s||l?(180+180*_.atan2(-l,-s)/D+360)%360:0}return e.angle(t,n,o,a)-e.angle(r,i,o,a)},e.rad=function(t){return t%360*D/180},e.deg=function(t){return Math.round(180*t/D%360*1e3)/1e3},e.snapTo=function(t,n,r){if(r=e.is(r,"finite")?r:10,e.is(t,A)){for(var i=t.length;i--;)if(C(t[i]-n)<=r)return t[i]}else{var o=n%(t=+t);if(o<r)return n-o;if(o>t-r)return n-o+t}return n},e.createUUID=(i=/[xy]/g,o=function(t){var e=16*_.random()|0;return("x"==t?e:3&e|8).toString(16)},function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(i,o).toUpperCase()}),e.setWindow=function(n){t("raphael.setWindow",e,c.win,n),c.win=n,c.doc=c.win.document,e._engine.initWin&&e._engine.initWin(c.win)};var nt=function(t){if(e.vml){var n,r=/^\s+|\s+$/g;try{var i=new ActiveXObject("htmlfile");i.write("<body>"),i.close(),n=i.body}catch(s){n=createPopup().document.body}var o=n.createTextRange();nt=ut(function(t){try{n.style.color=v(t).replace(r,g);var e=o.queryCommandValue("ForeColor");return"#"+("000000"+(e=(255&e)<<16|65280&e|(16711680&e)>>>16).toString(16)).slice(-6)}catch(s){return"none"}})}else{var a=c.doc.createElement("i");a.title="Raphaël Colour Picker",a.style.display="none",c.doc.body.appendChild(a),nt=ut(function(t){return a.style.color=t,c.doc.defaultView.getComputedStyle(a,g).getPropertyValue("color")})}return nt(t)},rt=function(){return"hsb("+[this.h,this.s,this.b]+")"},it=function(){return"hsl("+[this.h,this.s,this.l]+")"},ot=function(){return this.hex},at=function(t,n,r){if(null==n&&e.is(t,"object")&&"r"in t&&"g"in t&&"b"in t&&(r=t.b,n=t.g,t=t.r),null==n&&e.is(t,E)){var i=e.getRGB(t);t=i.r,n=i.g,r=i.b}return(t>1||n>1||r>1)&&(t/=255,n/=255,r/=255),[t,n,r]},st=function(t,n,r,i){var o={r:t*=255,g:n*=255,b:r*=255,hex:e.rgb(t,n,r),toString:ot};return e.is(i,"finite")&&(o.opacity=i),o};function lt(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return t.push(t.splice(n,1)[0])}function ut(t,e,n){return function r(){var i=Array.prototype.slice.call(arguments,0),o=i.join("␀"),a=r.cache=r.cache||{},s=r.count=r.count||[];return a[u](o)?(lt(s,o),n?n(a[o]):a[o]):(s.length>=1e3&&delete a[s.shift()],s.push(o),a[o]=t[d](e,i),n?n(a[o]):a[o])}}function ct(){return this.hex}function ht(t,e){for(var n=[],r=0,i=t.length;i-2*!e>r;r+=2){var o=[{x:+t[r-2],y:+t[r-1]},{x:+t[r],y:+t[r+1]},{x:+t[r+2],y:+t[r+3]},{x:+t[r+4],y:+t[r+5]}];e?r?i-4==r?o[3]={x:+t[0],y:+t[1]}:i-2==r&&(o[2]={x:+t[0],y:+t[1]},o[3]={x:+t[2],y:+t[3]}):o[0]={x:+t[i-2],y:+t[i-1]}:i-4==r?o[3]=o[2]:r||(o[0]={x:+t[r],y:+t[r+1]}),n.push(["C",(6*o[1].x-o[0].x+o[2].x)/6,(6*o[1].y-o[0].y+o[2].y)/6,(o[1].x+6*o[2].x-o[3].x)/6,(o[1].y+6*o[2].y-o[3].y)/6,o[2].x,o[2].y])}return n}e.color=function(t){var n;return e.is(t,"object")&&"h"in t&&"s"in t&&"b"in t?(n=e.hsb2rgb(t),t.r=n.r,t.g=n.g,t.b=n.b,t.hex=n.hex):e.is(t,"object")&&"h"in t&&"s"in t&&"l"in t?(n=e.hsl2rgb(t),t.r=n.r,t.g=n.g,t.b=n.b,t.hex=n.hex):(e.is(t,"string")&&(t=e.getRGB(t)),e.is(t,"object")&&"r"in t&&"g"in t&&"b"in t?(n=e.rgb2hsl(t),t.h=n.h,t.s=n.s,t.l=n.l,n=e.rgb2hsb(t),t.v=n.b):(t={hex:"none"}).r=t.g=t.b=t.h=t.s=t.v=t.l=-1),t.toString=ot,t},e.hsb2rgb=function(t,e,n,r){var i,o,a,s,l;return this.is(t,"object")&&"h"in t&&"s"in t&&"b"in t&&(n=t.b,e=t.s,r=t.o,t=t.h),s=(l=n*e)*(1-C((t=(t*=360)%360/60)%2-1)),i=o=a=n-l,st(i+=[l,s,0,0,s,l][t=~~t],o+=[s,l,l,s,0,0][t],a+=[0,0,s,l,l,s][t],r)},e.hsl2rgb=function(t,e,n,r){var i,o,a,s,l;return this.is(t,"object")&&"h"in t&&"s"in t&&"l"in t&&(n=t.l,e=t.s,t=t.h),(t>1||e>1||n>1)&&(t/=360,e/=100,n/=100),s=(l=2*e*(n<.5?n:1-n))*(1-C((t=(t*=360)%360/60)%2-1)),i=o=a=n-l/2,st(i+=[l,s,0,0,s,l][t=~~t],o+=[s,l,l,s,0,0][t],a+=[0,0,s,l,l,s][t],r)},e.rgb2hsb=function(t,e,n){var r,i;return n=at(t,e,n),{h:((0==(i=(r=w(t=n[0],e=n[1],n=n[2]))-S(t,e,n))?null:r==t?(e-n)/i:r==e?(n-t)/i+2:(t-e)/i+4)+360)%6*60/360,s:0==i?0:i/r,b:r,toString:rt}},e.rgb2hsl=function(t,e,n){var r,i,o,a;return n=at(t,e,n),r=((i=w(t=n[0],e=n[1],n=n[2]))+(o=S(t,e,n)))/2,{h:((0==(a=i-o)?null:i==t?(e-n)/a:i==e?(n-t)/a+2:(t-e)/a+4)+360)%6*60/360,s:0==a?0:r<.5?a/(2*r):a/(2-2*r),l:r,toString:it}},e._path2string=function(){return this.join(",").replace(W,"$1")},e._preload=function(t,e){var n=c.doc.createElement("img");n.style.cssText="position:absolute;left:-9999em;top:-9999em",n.onload=function(){e.call(this),this.onload=null,c.doc.body.removeChild(this)},n.onerror=function(){c.doc.body.removeChild(this)},c.doc.body.appendChild(n),n.src=t},e.getRGB=ut(function(t){if(!t||(t=v(t)).indexOf("-")+1)return{r:-1,g:-1,b:-1,hex:"none",error:1,toString:ct};if("none"==t)return{r:-1,g:-1,b:-1,hex:"none",toString:ct};!q[u](t.toLowerCase().substring(0,2))&&"#"!=t.charAt()&&(t=nt(t));var n,r,i,o,a,s,l=t.match(I);return l?(l[2]&&(i=M(l[2].substring(5),16),r=M(l[2].substring(3,5),16),n=M(l[2].substring(1,3),16)),l[3]&&(i=M((a=l[3].charAt(3))+a,16),r=M((a=l[3].charAt(2))+a,16),n=M((a=l[3].charAt(1))+a,16)),l[4]&&(s=l[4].split(H),n=P(s[0]),"%"==s[0].slice(-1)&&(n*=2.55),r=P(s[1]),"%"==s[1].slice(-1)&&(r*=2.55),i=P(s[2]),"%"==s[2].slice(-1)&&(i*=2.55),"rgba"==l[1].toLowerCase().slice(0,4)&&(o=P(s[3])),s[3]&&"%"==s[3].slice(-1)&&(o/=100)),l[5]?(s=l[5].split(H),n=P(s[0]),"%"==s[0].slice(-1)&&(n*=2.55),r=P(s[1]),"%"==s[1].slice(-1)&&(r*=2.55),i=P(s[2]),"%"==s[2].slice(-1)&&(i*=2.55),("deg"==s[0].slice(-3)||"°"==s[0].slice(-1))&&(n/=360),"hsba"==l[1].toLowerCase().slice(0,4)&&(o=P(s[3])),s[3]&&"%"==s[3].slice(-1)&&(o/=100),e.hsb2rgb(n,r,i,o)):l[6]?(s=l[6].split(H),n=P(s[0]),"%"==s[0].slice(-1)&&(n*=2.55),r=P(s[1]),"%"==s[1].slice(-1)&&(r*=2.55),i=P(s[2]),"%"==s[2].slice(-1)&&(i*=2.55),("deg"==s[0].slice(-3)||"°"==s[0].slice(-1))&&(n/=360),"hsla"==l[1].toLowerCase().slice(0,4)&&(o=P(s[3])),s[3]&&"%"==s[3].slice(-1)&&(o/=100),e.hsl2rgb(n,r,i,o)):((l={r:n,g:r,b:i,toString:ct}).hex="#"+(16777216|i|r<<8|n<<16).toString(16).slice(1),e.is(o,"finite")&&(l.opacity=o),l)):{r:-1,g:-1,b:-1,hex:"none",error:1,toString:ct}},e),e.hsb=ut(function(t,n,r){return e.hsb2rgb(t,n,r).hex}),e.hsl=ut(function(t,n,r){return e.hsl2rgb(t,n,r).hex}),e.rgb=ut(function(t,e,n){function r(t){return t+.5|0}return"#"+(16777216|r(n)|r(e)<<8|r(t)<<16).toString(16).slice(1)}),(e.getColor=function(t){var e=this.getColor.start=this.getColor.start||{h:0,s:1,b:t||.75},n=this.hsb2rgb(e.h,e.s,e.b);return e.h+=.075,e.h>1&&(e.h=0,e.s-=.2,e.s<=0&&(this.getColor.start={h:0,s:1,b:e.b})),n.hex}).reset=function(){delete this.start},e.parsePathString=function(t){if(!t)return null;var n=ft(t);if(n.arr)return bt(n.arr);var r={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0},i=[];return e.is(t,A)&&e.is(t[0],A)&&(i=bt(t)),i.length||v(t).replace(z,function(t,e,n){var o=[],a=e.toLowerCase();if(n.replace(V,function(t,e){e&&o.push(+e)}),"m"==a&&o.length>2&&(i.push([e].concat(o.splice(0,2))),a="l",e="m"==e?"l":"L"),"r"==a)i.push([e].concat(o));else for(;o.length>=r[a]&&(i.push([e].concat(o.splice(0,r[a]))),r[a]););}),i.toString=e._path2string,n.arr=bt(i),i},e.parseTransformString=ut(function(t){if(!t)return null;var n=[];return e.is(t,A)&&e.is(t[0],A)&&(n=bt(t)),n.length||v(t).replace(U,function(t,e,r){var i=[];x.call(e),r.replace(V,function(t,e){e&&i.push(+e)}),n.push([e].concat(i))}),n.toString=e._path2string,n},this,function(t){if(!t)return t;for(var e=[],n=0;n<t.length;n++){for(var r=[],i=0;i<t[n].length;i++)r.push(t[n][i]);e.push(r)}return e});var ft=function(t){var e=ft.ps=ft.ps||{};return e[t]?e[t].sleep=100:e[t]={sleep:100},setTimeout(function(){for(var n in e)e[u](n)&&n!=t&&(e[n].sleep--,!e[n].sleep&&delete e[n])}),e[t]};function dt(t,e,n,r,i){return t*(t*(-3*e+9*n-9*r+3*i)+6*e-12*n+6*r)-3*e+3*n}function pt(t,e,n,r,i,o,a,s,l){null==l&&(l=1);for(var u=(l=l>1?1:l<0?0:l)/2,c=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],h=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],f=0,d=0;d<12;d++){var p=u*c[d]+u,g=dt(p,t,n,i,a),m=dt(p,e,r,o,s);f+=h[d]*_.sqrt(g*g+m*m)}return u*f}function gt(t,e,n,r,i,o,a,s){if(!(w(t,n)<S(i,a)||S(t,n)>w(i,a)||w(e,r)<S(o,s)||S(e,r)>w(o,s))){var l=(t-n)*(o-s)-(e-r)*(i-a);if(l){var u=((t*r-e*n)*(i-a)-(t-n)*(i*s-o*a))/l,c=((t*r-e*n)*(o-s)-(e-r)*(i*s-o*a))/l,h=+u.toFixed(2),f=+c.toFixed(2);if(!(h<+S(t,n).toFixed(2)||h>+w(t,n).toFixed(2)||h<+S(i,a).toFixed(2)||h>+w(i,a).toFixed(2)||f<+S(e,r).toFixed(2)||f>+w(e,r).toFixed(2)||f<+S(o,s).toFixed(2)||f>+w(o,s).toFixed(2)))return{x:u,y:c}}}}function mt(t,n,r){var i=e.bezierBBox(t),o=e.bezierBBox(n);if(!e.isBBoxIntersect(i,o))return r?0:[];for(var a=pt.apply(0,t),s=pt.apply(0,n),l=w(~~(a/5),1),u=w(~~(s/5),1),c=[],h=[],f={},d=r?0:[],p=0;p<l+1;p++){var g=e.findDotsAtSegment.apply(e,t.concat(p/l));c.push({x:g.x,y:g.y,t:p/l})}for(p=0;p<u+1;p++)g=e.findDotsAtSegment.apply(e,n.concat(p/u)),h.push({x:g.x,y:g.y,t:p/u});for(p=0;p<l;p++)for(var m=0;m<u;m++){var v=c[p],y=c[p+1],b=h[m],x=h[m+1],_=C(y.x-v.x)<.001?"y":"x",T=C(x.x-b.x)<.001?"y":"x",D=gt(v.x,v.y,y.x,y.y,b.x,b.y,x.x,x.y);if(D){if(f[D.x.toFixed(4)]==D.y.toFixed(4))continue;f[D.x.toFixed(4)]=D.y.toFixed(4);var k=v.t+C((D[_]-v[_])/(y[_]-v[_]))*(y.t-v.t),E=b.t+C((D[T]-b[T])/(x[T]-b[T]))*(x.t-b.t);k>=0&&k<=1.001&&E>=0&&E<=1.001&&(r?d++:d.push({x:D.x,y:D.y,t1:S(k,1),t2:S(E,1)}))}}return d}function vt(t,n,r){t=e._path2curve(t),n=e._path2curve(n);for(var i,o,a,s,l,u,c,h,f,d,p=r?0:[],g=0,m=t.length;g<m;g++){var v=t[g];if("M"==v[0])i=l=v[1],o=u=v[2];else{"C"==v[0]?(f=[i,o].concat(v.slice(1)),i=f[6],o=f[7]):(f=[i,o,i,o,l,u,l,u],i=l,o=u);for(var y=0,b=n.length;y<b;y++){var x=n[y];if("M"==x[0])a=c=x[1],s=h=x[2];else{"C"==x[0]?(d=[a,s].concat(x.slice(1)),a=d[6],s=d[7]):(d=[a,s,a,s,c,h,c,h],a=c,s=h);var _=mt(f,d,r);if(r)p+=_;else{for(var w=0,S=_.length;w<S;w++)_[w].segment1=g,_[w].segment2=y,_[w].bez1=f,_[w].bez2=d;p=p.concat(_)}}}}}return p}e.findDotsAtSegment=function(t,e,n,r,i,o,a,s,l){var u=1-l,c=T(u,3),h=T(u,2),f=l*l,d=f*l,p=c*t+3*h*l*n+3*u*l*l*i+d*a,g=c*e+3*h*l*r+3*u*l*l*o+d*s,m=t+2*l*(n-t)+f*(i-2*n+t),v=e+2*l*(r-e)+f*(o-2*r+e),y=n+2*l*(i-n)+f*(a-2*i+n),b=r+2*l*(o-r)+f*(s-2*o+r),x=u*t+l*n,w=u*e+l*r,S=u*i+l*a,C=u*o+l*s,k=90-180*_.atan2(m-y,v-b)/D;return(m>y||v<b)&&(k+=180),{x:p,y:g,m:{x:m,y:v},n:{x:y,y:b},start:{x:x,y:w},end:{x:S,y:C},alpha:k}},e.bezierBBox=function(t,n,r,i,o,a,s,l){e.is(t,"array")||(t=[t,n,r,i,o,a,s,l]);var u=Dt.apply(null,t);return{x:u.min.x,y:u.min.y,x2:u.max.x,y2:u.max.y,width:u.max.x-u.min.x,height:u.max.y-u.min.y}},e.isPointInsideBBox=function(t,e,n){return e>=t.x&&e<=t.x2&&n>=t.y&&n<=t.y2},e.isBBoxIntersect=function(t,n){var r=e.isPointInsideBBox;return r(n,t.x,t.y)||r(n,t.x2,t.y)||r(n,t.x,t.y2)||r(n,t.x2,t.y2)||r(t,n.x,n.y)||r(t,n.x2,n.y)||r(t,n.x,n.y2)||r(t,n.x2,n.y2)||(t.x<n.x2&&t.x>n.x||n.x<t.x2&&n.x>t.x)&&(t.y<n.y2&&t.y>n.y||n.y<t.y2&&n.y>t.y)},e.pathIntersection=function(t,e){return vt(t,e)},e.pathIntersectionNumber=function(t,e){return vt(t,e,1)},e.isPointInsidePath=function(t,n,r){var i=e.pathBBox(t);return e.isPointInsideBBox(i,n,r)&&vt(t,[["M",n,r],["H",i.x2+10]],1)%2==1},e._removedFactory=function(e){return function(){t("raphael.log",null,"Raphaël: you are calling to method “"+e+"” of removed object",e)}};var yt=e.pathBBox=function(t){var e=ft(t);if(e.bbox)return et(e.bbox);if(!t)return{x:0,y:0,width:0,height:0,x2:0,y2:0};for(var n,r=0,i=0,o=[],a=[],s=0,l=(t=kt(t)).length;s<l;s++)if("M"==(n=t[s])[0])i=n[2],o.push(r=n[1]),a.push(i);else{var u=Dt(r,i,n[1],n[2],n[3],n[4],n[5],n[6]);o=o.concat(u.min.x,u.max.x),a=a.concat(u.min.y,u.max.y),r=n[5],i=n[6]}var c=S[d](0,o),h=S[d](0,a),f=w[d](0,o),p=w[d](0,a),g=f-c,m=p-h,v={x:c,y:h,x2:f,y2:p,width:g,height:m,cx:c+g/2,cy:h+m/2};return e.bbox=et(v),v},bt=function(t){var n=et(t);return n.toString=e._path2string,n},xt=e._pathToRelative=function(t){var n=ft(t);if(n.rel)return bt(n.rel);e.is(t,A)&&e.is(t&&t[0],A)||(t=e.parsePathString(t));var r=[],i=0,o=0,a=0,s=0,l=0;"M"==t[0][0]&&(a=i=t[0][1],s=o=t[0][2],l++,r.push(["M",i,o]));for(var u=l,c=t.length;u<c;u++){var h=r[u]=[],f=t[u];if(f[0]!=x.call(f[0]))switch(h[0]=x.call(f[0]),h[0]){case"a":h[1]=f[1],h[2]=f[2],h[3]=f[3],h[4]=f[4],h[5]=f[5],h[6]=+(f[6]-i).toFixed(3),h[7]=+(f[7]-o).toFixed(3);break;case"v":h[1]=+(f[1]-o).toFixed(3);break;case"m":a=f[1],s=f[2];default:for(var d=1,p=f.length;d<p;d++)h[d]=+(f[d]-(d%2?i:o)).toFixed(3)}else{h=r[u]=[],"m"==f[0]&&(a=f[1]+i,s=f[2]+o);for(var g=0,m=f.length;g<m;g++)r[u][g]=f[g]}var v=r[u].length;switch(r[u][0]){case"z":i=a,o=s;break;case"h":i+=+r[u][v-1];break;case"v":o+=+r[u][v-1];break;default:i+=+r[u][v-2],o+=+r[u][v-1]}}return r.toString=e._path2string,n.rel=bt(r),r},_t=e._pathToAbsolute=function(t){var n=ft(t);if(n.abs)return bt(n.abs);if(e.is(t,A)&&e.is(t&&t[0],A)||(t=e.parsePathString(t)),!t||!t.length)return[["M",0,0]];var r=[],i=0,o=0,a=0,s=0,l=0;"M"==t[0][0]&&(a=i=+t[0][1],s=o=+t[0][2],l++,r[0]=["M",i,o]);for(var u,c,h=3==t.length&&"M"==t[0][0]&&"R"==t[1][0].toUpperCase()&&"Z"==t[2][0].toUpperCase(),f=l,d=t.length;f<d;f++){if(r.push(u=[]),(c=t[f])[0]!=R.call(c[0]))switch(u[0]=R.call(c[0]),u[0]){case"A":u[1]=c[1],u[2]=c[2],u[3]=c[3],u[4]=c[4],u[5]=c[5],u[6]=+(c[6]+i),u[7]=+(c[7]+o);break;case"V":u[1]=+c[1]+o;break;case"H":u[1]=+c[1]+i;break;case"R":for(var p=[i,o].concat(c.slice(1)),g=2,m=p.length;g<m;g++)p[g]=+p[g]+i,p[++g]=+p[g]+o;r.pop(),r=r.concat(ht(p,h));break;case"M":a=+c[1]+i,s=+c[2]+o;default:for(g=1,m=c.length;g<m;g++)u[g]=+c[g]+(g%2?i:o)}else if("R"==c[0])p=[i,o].concat(c.slice(1)),r.pop(),r=r.concat(ht(p,h)),u=["R"].concat(c.slice(-2));else for(var v=0,y=c.length;v<y;v++)u[v]=c[v];switch(u[0]){case"Z":i=a,o=s;break;case"H":i=u[1];break;case"V":o=u[1];break;case"M":a=u[u.length-2],s=u[u.length-1];default:i=u[u.length-2],o=u[u.length-1]}}return r.toString=e._path2string,n.abs=bt(r),r},wt=function(t,e,n,r){return[t,e,n,r,n,r]},St=function(t,e,n,r,i,o){var a=1/3,s=2/3;return[a*t+s*n,a*e+s*r,a*i+s*n,a*o+s*r,i,o]},Ct=function(t,e,n,r,i,o,a,s,l,u){var c,h=120*D/180,f=D/180*(+i||0),d=[],p=ut(function(t,e,n){return{x:t*_.cos(n)-e*_.sin(n),y:t*_.sin(n)+e*_.cos(n)}});if(u)T=u[0],k=u[1],w=u[2],S=u[3];else{t=(c=p(t,e,-f)).x,e=c.y,s=(c=p(s,l,-f)).x,l=c.y,_.cos(D/180*i),_.sin(D/180*i);var g=(t-s)/2,m=(e-l)/2,v=g*g/(n*n)+m*m/(r*r);v>1&&(n*=v=_.sqrt(v),r*=v);var y=n*n,b=r*r,x=(o==a?-1:1)*_.sqrt(C((y*b-y*m*m-b*g*g)/(y*m*m+b*g*g))),w=x*n*m/r+(t+s)/2,S=x*-r*g/n+(e+l)/2,T=_.asin(((e-S)/r).toFixed(9)),k=_.asin(((l-S)/r).toFixed(9));(T=t<w?D-T:T)<0&&(T=2*D+T),(k=s<w?D-k:k)<0&&(k=2*D+k),a&&T>k&&(T-=2*D),!a&&k>T&&(k-=2*D)}var E=k-T;if(C(E)>h){var A=k,L=s,I=l;s=w+n*_.cos(k=T+h*(a&&k>T?1:-1)),l=S+r*_.sin(k),d=Ct(s,l,n,r,i,0,a,L,I,[k,A,w,S])}E=k-T;var N=_.cos(T),F=_.sin(T),j=_.cos(k),P=_.sin(k),M=_.tan(E/4),R=4/3*n*M,O=4/3*r*M,B=[t,e],H=[t+R*F,e-O*N],q=[s+R*P,l-O*j],W=[s,l];if(H[0]=2*B[0]-H[0],H[1]=2*B[1]-H[1],u)return[H,q,W].concat(d);for(var z=[],U=0,V=(d=[H,q,W].concat(d).join().split(",")).length;U<V;U++)z[U]=U%2?p(d[U-1],d[U],f).y:p(d[U],d[U+1],f).x;return z},Tt=function(t,e,n,r,i,o,a,s,l){var u=1-l;return{x:T(u,3)*t+3*T(u,2)*l*n+3*u*l*l*i+T(l,3)*a,y:T(u,3)*e+3*T(u,2)*l*r+3*u*l*l*o+T(l,3)*s}},Dt=ut(function(t,e,n,r,i,o,a,s){var l,u=i-2*n+t-(a-2*i+n),c=2*(n-t)-2*(i-n),h=t-n,f=(-c+_.sqrt(c*c-4*u*h))/2/u,p=(-c-_.sqrt(c*c-4*u*h))/2/u,g=[e,s],m=[t,a];return C(f)>"1e12"&&(f=.5),C(p)>"1e12"&&(p=.5),f>0&&f<1&&(l=Tt(t,e,n,r,i,o,a,s,f),m.push(l.x),g.push(l.y)),p>0&&p<1&&(l=Tt(t,e,n,r,i,o,a,s,p),m.push(l.x),g.push(l.y)),f=(-(c=2*(r-e)-2*(o-r))+_.sqrt(c*c-4*(u=o-2*r+e-(s-2*o+r))*(h=e-r)))/2/u,p=(-c-_.sqrt(c*c-4*u*h))/2/u,C(f)>"1e12"&&(f=.5),C(p)>"1e12"&&(p=.5),f>0&&f<1&&(l=Tt(t,e,n,r,i,o,a,s,f),m.push(l.x),g.push(l.y)),p>0&&p<1&&(l=Tt(t,e,n,r,i,o,a,s,p),m.push(l.x),g.push(l.y)),{min:{x:S[d](0,m),y:S[d](0,g)},max:{x:w[d](0,m),y:w[d](0,g)}}}),kt=e._path2curve=ut(function(t,e){var n=!e&&ft(t);if(!e&&n.curve)return bt(n.curve);for(var r=_t(t),i=e&&_t(e),o={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},a={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},s=function(t,e,n){var r,i;if(!t)return["C",e.x,e.y,e.x,e.y,e.x,e.y];switch(!(t[0]in{T:1,Q:1})&&(e.qx=e.qy=null),t[0]){case"M":e.X=t[1],e.Y=t[2];break;case"A":t=["C"].concat(Ct[d](0,[e.x,e.y].concat(t.slice(1))));break;case"S":"C"==n||"S"==n?(r=2*e.x-e.bx,i=2*e.y-e.by):(r=e.x,i=e.y),t=["C",r,i].concat(t.slice(1));break;case"T":"Q"==n||"T"==n?(e.qx=2*e.x-e.qx,e.qy=2*e.y-e.qy):(e.qx=e.x,e.qy=e.y),t=["C"].concat(St(e.x,e.y,e.qx,e.qy,t[1],t[2]));break;case"Q":e.qx=t[1],e.qy=t[2],t=["C"].concat(St(e.x,e.y,t[1],t[2],t[3],t[4]));break;case"L":t=["C"].concat(wt(e.x,e.y,t[1],t[2]));break;case"H":t=["C"].concat(wt(e.x,e.y,t[1],e.y));break;case"V":t=["C"].concat(wt(e.x,e.y,e.x,t[1]));break;case"Z":t=["C"].concat(wt(e.x,e.y,e.X,e.Y))}return t},l=function(t,e){if(t[e].length>7){t[e].shift();for(var n=t[e];n.length;)c[e]="A",i&&(h[e]="A"),t.splice(e++,0,["C"].concat(n.splice(0,6)));t.splice(e,1),m=w(r.length,i&&i.length||0)}},u=function(t,e,n,o,a){t&&e&&"M"==t[a][0]&&"M"!=e[a][0]&&(e.splice(a,0,["M",o.x,o.y]),n.bx=0,n.by=0,n.x=t[a][1],n.y=t[a][2],m=w(r.length,i&&i.length||0))},c=[],h=[],f="",p="",g=0,m=w(r.length,i&&i.length||0);g<m;g++){r[g]&&(f=r[g][0]),"C"!=f&&(c[g]=f,g&&(p=c[g-1])),r[g]=s(r[g],o,p),"A"!=c[g]&&"C"==f&&(c[g]="C"),l(r,g),i&&(i[g]&&(f=i[g][0]),"C"!=f&&(h[g]=f,g&&(p=h[g-1])),i[g]=s(i[g],a,p),"A"!=h[g]&&"C"==f&&(h[g]="C"),l(i,g)),u(r,i,o,a,g),u(i,r,a,o,g);var v=r[g],y=i&&i[g],b=v.length,x=i&&y.length;o.x=v[b-2],o.y=v[b-1],o.bx=P(v[b-4])||o.x,o.by=P(v[b-3])||o.y,a.bx=i&&(P(y[x-4])||a.x),a.by=i&&(P(y[x-3])||a.y),a.x=i&&y[x-2],a.y=i&&y[x-1]}return i||(n.curve=bt(r)),i?[r,i]:r},null,bt),Et=(e._parseDots=ut(function(t){for(var n=[],r=0,i=t.length;r<i;r++){var o={},a=t[r].match(/^([^:]*):?([\d\.]*)/);if(o.color=e.getRGB(a[1]),o.color.error)return null;o.opacity=o.color.opacity,o.color=o.color.hex,a[2]&&(o.offset=a[2]+"%"),n.push(o)}for(r=1,i=n.length-1;r<i;r++)if(!n[r].offset){for(var s=P(n[r-1].offset||0),l=0,u=r+1;u<i;u++)if(n[u].offset){l=n[u].offset;break}l||(l=100,u=i);for(var c=((l=P(l))-s)/(u-r+1);r<u;r++)n[r].offset=(s+=c)+"%"}return n}),e._tear=function(t,e){t==e.top&&(e.top=t.prev),t==e.bottom&&(e.bottom=t.next),t.next&&(t.next.prev=t.prev),t.prev&&(t.prev.next=t.next)}),At=(e._tofront=function(t,e){e.top!==t&&(Et(t,e),t.next=null,t.prev=e.top,e.top.next=t,e.top=t)},e._toback=function(t,e){e.bottom!==t&&(Et(t,e),t.next=e.bottom,t.prev=null,e.bottom.prev=t,e.bottom=t)},e._insertafter=function(t,e,n){Et(t,n),e==n.top&&(n.top=t),e.next&&(e.next.prev=t),t.next=e.next,t.prev=e,e.next=t},e._insertbefore=function(t,e,n){Et(t,n),e==n.bottom&&(n.bottom=t),e.prev&&(e.prev.next=t),t.prev=e.prev,e.prev=t,t.next=e},e.toMatrix=function(t,e){var n=yt(t),r={_:{transform:g},getBBox:function(){return n}};return Lt(r,e),r.matrix}),Lt=(e.transformPath=function(t,e){return K(t,At(t,e))},e._extractTransform=function(t,n){if(null==n)return t._.transform;n=v(n).replace(/\.{3}|\u2026/g,t._.transform||g);var r,i,o=e.parseTransformString(n),a=0,s=1,l=1,u=t._,c=new Ft;if(u.transform=o||[],o)for(var h=0,f=o.length;h<f;h++){var d,p,m,y,b,x=o[h],_=x.length,w=v(x[0]).toLowerCase(),S=x[0]!=w,C=S?c.invert():0;"t"==w&&3==_?S?(d=C.x(0,0),p=C.y(0,0),m=C.x(x[1],x[2]),y=C.y(x[1],x[2]),c.translate(m-d,y-p)):c.translate(x[1],x[2]):"r"==w?2==_?(b=b||t.getBBox(1),c.rotate(x[1],b.x+b.width/2,b.y+b.height/2),a+=x[1]):4==_&&(S?(m=C.x(x[2],x[3]),y=C.y(x[2],x[3]),c.rotate(x[1],m,y)):c.rotate(x[1],x[2],x[3]),a+=x[1]):"s"==w?2==_||3==_?(b=b||t.getBBox(1),c.scale(x[1],x[_-1],b.x+b.width/2,b.y+b.height/2),s*=x[1],l*=x[_-1]):5==_&&(S?(m=C.x(x[3],x[4]),y=C.y(x[3],x[4]),c.scale(x[1],x[2],m,y)):c.scale(x[1],x[2],x[3],x[4]),s*=x[1],l*=x[2]):"m"==w&&7==_&&c.add(x[1],x[2],x[3],x[4],x[5],x[6]),u.dirtyT=1,t.matrix=c}t.matrix=c,u.sx=s,u.sy=l,u.deg=a,u.dx=r=c.e,u.dy=i=c.f,1==s&&1==l&&!a&&u.bbox?(u.bbox.x+=+r,u.bbox.y+=+i):u.dirtyT=1}),It=function(t){var e=t[0];switch(e.toLowerCase()){case"t":return[e,0,0];case"m":return[e,1,0,0,1,0,0];case"r":return 4==t.length?[e,0,t[2],t[3]]:[e,0];case"s":return 5==t.length?[e,1,1,t[3],t[4]]:3==t.length?[e,1,1]:[e,1]}},Nt=e._equaliseTransform=function(t,n){n=v(n).replace(/\.{3}|\u2026/g,t),t=e.parseTransformString(t)||[],n=e.parseTransformString(n)||[];for(var r,i,o,a,s=w(t.length,n.length),l=[],u=[],c=0;c<s;c++){if(o=t[c]||It(n[c]),a=n[c]||It(o),o[0]!=a[0]||"r"==o[0].toLowerCase()&&(o[2]!=a[2]||o[3]!=a[3])||"s"==o[0].toLowerCase()&&(o[3]!=a[3]||o[4]!=a[4]))return;for(l[c]=[],u[c]=[],r=0,i=w(o.length,a.length);r<i;r++)r in o&&(l[c][r]=o[r]),r in a&&(u[c][r]=a[r])}return{from:l,to:u}};function Ft(t,e,n,r,i,o){null!=t?(this.a=+t,this.b=+e,this.c=+n,this.d=+r,this.e=+i,this.f=+o):(this.a=1,this.b=0,this.c=0,this.d=1,this.e=0,this.f=0)}e._getContainer=function(t,n,r,i){var o;if(null!=(o=null!=i||e.is(t,"object")?t:c.doc.getElementById(t)))return o.tagName?null==n?{container:o,width:o.style.pixelWidth||o.offsetWidth,height:o.style.pixelHeight||o.offsetHeight}:{container:o,width:n,height:r}:{container:1,x:t,y:n,width:r,height:i}},e.pathToRelative=xt,e._engine={},e.path2curve=kt,e.matrix=function(t,e,n,r,i,o){return new Ft(t,e,n,r,i,o)},function(t){function n(t){return t[0]*t[0]+t[1]*t[1]}function r(t){var e=_.sqrt(n(t));t[0]&&(t[0]/=e),t[1]&&(t[1]/=e)}t.add=function(t,e,n,r,i,o){var a,s,l,u,c=[[],[],[]],h=[[this.a,this.c,this.e],[this.b,this.d,this.f],[0,0,1]],f=[[t,n,i],[e,r,o],[0,0,1]];for(t&&t instanceof Ft&&(f=[[t.a,t.c,t.e],[t.b,t.d,t.f],[0,0,1]]),a=0;a<3;a++)for(s=0;s<3;s++){for(u=0,l=0;l<3;l++)u+=h[a][l]*f[l][s];c[a][s]=u}this.a=c[0][0],this.b=c[1][0],this.c=c[0][1],this.d=c[1][1],this.e=c[0][2],this.f=c[1][2]},t.invert=function(){var t=this,e=t.a*t.d-t.b*t.c;return new Ft(t.d/e,-t.b/e,-t.c/e,t.a/e,(t.c*t.f-t.d*t.e)/e,(t.b*t.e-t.a*t.f)/e)},t.clone=function(){return new Ft(this.a,this.b,this.c,this.d,this.e,this.f)},t.translate=function(t,e){this.add(1,0,0,1,t,e)},t.scale=function(t,e,n,r){null==e&&(e=t),(n||r)&&this.add(1,0,0,1,n,r),this.add(t,0,0,e,0,0),(n||r)&&this.add(1,0,0,1,-n,-r)},t.rotate=function(t,n,r){t=e.rad(t),n=n||0,r=r||0;var i=+_.cos(t).toFixed(9),o=+_.sin(t).toFixed(9);this.add(i,o,-o,i,n,r),this.add(1,0,0,1,-n,-r)},t.x=function(t,e){return t*this.a+e*this.c+this.e},t.y=function(t,e){return t*this.b+e*this.d+this.f},t.get=function(t){return+this[v.fromCharCode(97+t)].toFixed(4)},t.toString=function(){return e.svg?"matrix("+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)].join()+")":[this.get(0),this.get(2),this.get(1),this.get(3),0,0].join()},t.toFilter=function(){return"progid:DXImageTransform.Microsoft.Matrix(M11="+this.get(0)+", M12="+this.get(2)+", M21="+this.get(1)+", M22="+this.get(3)+", Dx="+this.get(4)+", Dy="+this.get(5)+", sizingmethod='auto expand')"},t.offset=function(){return[this.e.toFixed(4),this.f.toFixed(4)]},t.split=function(){var t={};t.dx=this.e,t.dy=this.f;var i=[[this.a,this.c],[this.b,this.d]];t.scalex=_.sqrt(n(i[0])),r(i[0]),t.shear=i[0][0]*i[1][0]+i[0][1]*i[1][1],i[1]=[i[1][0]-i[0][0]*t.shear,i[1][1]-i[0][1]*t.shear],t.scaley=_.sqrt(n(i[1])),r(i[1]),t.shear/=t.scaley;var o=-i[0][1],a=i[1][1];return a<0?(t.rotate=e.deg(_.acos(a)),o<0&&(t.rotate=360-t.rotate)):t.rotate=e.deg(_.asin(o)),t.isSimple=!(+t.shear.toFixed(9)||t.scalex.toFixed(9)!=t.scaley.toFixed(9)&&t.rotate),t.isSuperSimple=!+t.shear.toFixed(9)&&t.scalex.toFixed(9)==t.scaley.toFixed(9)&&!t.rotate,t.noRotation=!+t.shear.toFixed(9)&&!t.rotate,t},t.toTransformString=function(t){var e=t||this.split();return e.isSimple?(e.scalex=+e.scalex.toFixed(4),e.scaley=+e.scaley.toFixed(4),e.rotate=+e.rotate.toFixed(4),(e.dx||e.dy?"t"+[e.dx,e.dy]:g)+(1!=e.scalex||1!=e.scaley?"s"+[e.scalex,e.scaley,0,0]:g)+(e.rotate?"r"+[e.rotate,0,0]:g)):"m"+[this.get(0),this.get(1),this.get(2),this.get(3),this.get(4),this.get(5)]}}(Ft.prototype);for(var jt=function(){this.returnValue=!1},Pt=function(){return this.originalEvent.preventDefault()},Mt=function(){this.cancelBubble=!0},Rt=function(){return this.originalEvent.stopPropagation()},Ot=function(t){return{x:t.clientX+(c.doc.documentElement.scrollLeft||c.doc.body.scrollLeft),y:t.clientY+(c.doc.documentElement.scrollTop||c.doc.body.scrollTop)}},Bt=c.doc.addEventListener?function(t,e,n,r){var i=function(t){var e=Ot(t);return n.call(r,t,e.x,e.y)};if(t.addEventListener(e,i,!1),p&&b[e]){var o=function(e){for(var i=Ot(e),o=e,a=0,s=e.targetTouches&&e.targetTouches.length;a<s;a++)if(e.targetTouches[a].target==t){(e=e.targetTouches[a]).originalEvent=o,e.preventDefault=Pt,e.stopPropagation=Rt;break}return n.call(r,e,i.x,i.y)};t.addEventListener(b[e],o,!1)}return function(){return t.removeEventListener(e,i,!1),p&&b[e]&&t.removeEventListener(b[e],o,!1),!0}}:c.doc.attachEvent?function(t,e,n,r){var i=function(t){var e=(t=t||c.win.event).clientX+(c.doc.documentElement.scrollLeft||c.doc.body.scrollLeft),i=t.clientY+(c.doc.documentElement.scrollTop||c.doc.body.scrollTop);return t.preventDefault=t.preventDefault||jt,t.stopPropagation=t.stopPropagation||Mt,n.call(r,t,e,i)};return t.attachEvent("on"+e,i),function(){return t.detachEvent("on"+e,i),!0}}:void 0,Ht=[],qt=function(e){for(var n,r=e.clientX,i=e.clientY,o=c.doc.documentElement.scrollTop||c.doc.body.scrollTop,a=c.doc.documentElement.scrollLeft||c.doc.body.scrollLeft,s=Ht.length;s--;){if(n=Ht[s],p&&e.touches){for(var l,u=e.touches.length;u--;)if((l=e.touches[u]).identifier==n.el._drag.id){r=l.clientX,i=l.clientY,(e.originalEvent?e.originalEvent:e).preventDefault();break}}else e.preventDefault();var h,f=n.el.node,d=f.nextSibling,g=f.parentNode,m=f.style.display;c.win.opera&&g.removeChild(f),f.style.display="none",h=n.el.paper.getElementByPoint(r,i),f.style.display=m,c.win.opera&&(d?g.insertBefore(f,d):g.appendChild(f)),h&&t("raphael.drag.over."+n.el.id,n.el,h),t("raphael.drag.move."+n.el.id,n.move_scope||n.el,(r+=a)-n.el._drag.x,(i+=o)-n.el._drag.y,r,i,e)}},Wt=function(n){e.unmousemove(qt).unmouseup(Wt);for(var r,i=Ht.length;i--;)(r=Ht[i]).el._drag={},t("raphael.drag.end."+r.el.id,r.end_scope||r.start_scope||r.move_scope||r.el,n);Ht=[]},zt=e.el={},Ut=y.length;Ut--;)!function(t){e[t]=zt[t]=function(n,r){return e.is(n,"function")&&(this.events=this.events||[],this.events.push({name:t,f:n,unbind:Bt(this.shape||this.node||c.doc,t,n,r||this)})),this},e["un"+t]=zt["un"+t]=function(n){for(var r=this.events||[],i=r.length;i--;)r[i].name!=t||!e.is(n,"undefined")&&r[i].f!=n||(r[i].unbind(),r.splice(i,1),!r.length&&delete this.events);return this}}(y[Ut]);zt.data=function(n,r){var i=X[this.id]=X[this.id]||{};if(0==arguments.length)return i;if(1==arguments.length){if(e.is(n,"object")){for(var o in n)n[u](o)&&this.data(o,n[o]);return this}return t("raphael.data.get."+this.id,this,i[n],n),i[n]}return i[n]=r,t("raphael.data.set."+this.id,this,r,n),this},zt.removeData=function(t){return null==t?delete X[this.id]:X[this.id]&&delete X[this.id][t],this},zt.getData=function(){return et(X[this.id]||{})},zt.hover=function(t,e,n,r){return this.mouseover(t,n).mouseout(e,r||n)},zt.unhover=function(t,e){return this.unmouseover(t).unmouseout(e)};var Vt=[];function Xt(){return this.x+m+this.y+m+this.width+" × "+this.height}zt.drag=function(n,r,i,o,a,s){function l(l){(l.originalEvent||l).preventDefault();var u=l.clientX,h=l.clientY,f=c.doc.documentElement.scrollTop||c.doc.body.scrollTop,d=c.doc.documentElement.scrollLeft||c.doc.body.scrollLeft;if(this._drag.id=l.identifier,p&&l.touches)for(var g,m=l.touches.length;m--;)if(this._drag.id=(g=l.touches[m]).identifier,g.identifier==this._drag.id){u=g.clientX,h=g.clientY;break}this._drag.x=u+d,this._drag.y=h+f,!Ht.length&&e.mousemove(qt).mouseup(Wt),Ht.push({el:this,move_scope:o,start_scope:a,end_scope:s}),r&&t.on("raphael.drag.start."+this.id,r),n&&t.on("raphael.drag.move."+this.id,n),i&&t.on("raphael.drag.end."+this.id,i),t("raphael.drag.start."+this.id,a||o||this,this._drag.x,this._drag.y,l)}return this._drag={},Vt.push({el:this,start:l}),this.mousedown(l),this},zt.onDragOver=function(e){e?t.on("raphael.drag.over."+this.id,e):t.unbind("raphael.drag.over."+this.id)},zt.undrag=function(){for(var n=Vt.length;n--;)Vt[n].el==this&&(this.unmousedown(Vt[n].start),Vt.splice(n,1),t.unbind("raphael.drag.*."+this.id));!Vt.length&&e.unmousemove(qt).unmouseup(Wt),Ht=[]},r.circle=function(t,n,r){var i=e._engine.circle(this,t||0,n||0,r||0);return this.__set__&&this.__set__.push(i),i},r.rect=function(t,n,r,i,o){var a=e._engine.rect(this,t||0,n||0,r||0,i||0,o||0);return this.__set__&&this.__set__.push(a),a},r.ellipse=function(t,n,r,i){var o=e._engine.ellipse(this,t||0,n||0,r||0,i||0);return this.__set__&&this.__set__.push(o),o},r.path=function(t){t&&!e.is(t,E)&&!e.is(t[0],A)&&(t+=g);var n=e._engine.path(e.format[d](e,arguments),this);return this.__set__&&this.__set__.push(n),n},r.image=function(t,n,r,i,o){var a=e._engine.image(this,t||"about:blank",n||0,r||0,i||0,o||0);return this.__set__&&this.__set__.push(a),a},r.text=function(t,n,r){var i=e._engine.text(this,t||0,n||0,v(r));return this.__set__&&this.__set__.push(i),i},r.set=function(t){!e.is(t,"array")&&(t=Array.prototype.splice.call(arguments,0,arguments.length));var n=new le(t);return this.__set__&&this.__set__.push(n),n.paper=this,n.type="set",n},r.setStart=function(t){this.__set__=t||this.set()},r.setFinish=function(t){var e=this.__set__;return delete this.__set__,e},r.getSize=function(){var t=this.canvas.parentNode;return{width:t.offsetWidth,height:t.offsetHeight}},r.setSize=function(t,n){return e._engine.setSize.call(this,t,n)},r.setViewBox=function(t,n,r,i,o){return e._engine.setViewBox.call(this,t,n,r,i,o)},r.top=r.bottom=null,r.raphael=e,r.getElementByPoint=function(t,e){var n,r,i,o,a,s=this,l=s.canvas,u=c.doc.elementFromPoint(t,e);if(c.win.opera&&"svg"==u.tagName){var h=(r=(n=l).getBoundingClientRect(),o=(i=n.ownerDocument).body,a=i.documentElement,{y:r.top+(c.win.pageYOffset||a.scrollTop||o.scrollTop)-(a.clientTop||o.clientTop||0),x:r.left+(c.win.pageXOffset||a.scrollLeft||o.scrollLeft)-(a.clientLeft||o.clientLeft||0)}),f=l.createSVGRect();f.x=t-h.x,f.y=e-h.y,f.width=f.height=1;var d=l.getIntersectionList(f,null);d.length&&(u=d[d.length-1])}if(!u)return null;for(;u.parentNode&&u!=l.parentNode&&!u.raphael;)u=u.parentNode;return u==s.canvas.parentNode&&(u=l),u&&u.raphael?s.getById(u.raphaelid):null},r.getElementsByBBox=function(t){var n=this.set();return this.forEach(function(r){e.isBBoxIntersect(r.getBBox(),t)&&n.push(r)}),n},r.getById=function(t){for(var e=this.bottom;e;){if(e.id==t)return e;e=e.next}return null},r.forEach=function(t,e){for(var n=this.bottom;n;){if(!1===t.call(e,n))return this;n=n.next}return this},r.getElementsByPoint=function(t,e){var n=this.set();return this.forEach(function(r){r.isPointInside(t,e)&&n.push(r)}),n},zt.isPointInside=function(t,n){var r=this.realPath=J[this.type](this);return this.attr("transform")&&this.attr("transform").length&&(r=e.transformPath(r,this.attr("transform"))),e.isPointInsidePath(r,t,n)},zt.getBBox=function(t){if(this.removed)return{};var e=this._;return t?(!e.dirty&&e.bboxwt||(this.realPath=J[this.type](this),e.bboxwt=yt(this.realPath),e.bboxwt.toString=Xt,e.dirty=0),e.bboxwt):((e.dirty||e.dirtyT||!e.bbox)&&(!e.dirty&&this.realPath||(e.bboxwt=0,this.realPath=J[this.type](this)),e.bbox=yt(K(this.realPath,this.matrix)),e.bbox.toString=Xt,e.dirty=e.dirtyT=0),e.bbox)},zt.clone=function(){if(this.removed)return null;var t=this.paper[this.type]().attr(this.attr());return this.__set__&&this.__set__.push(t),t},zt.glow=function(t){if("text"==this.type)return null;var e={width:((t=t||{}).width||10)+(+this.attr("stroke-width")||1),fill:t.fill||!1,opacity:null==t.opacity?.5:t.opacity,offsetx:t.offsetx||0,offsety:t.offsety||0,color:t.color||"#000"},n=e.width/2,r=this.paper,i=r.set(),o=this.realPath||J[this.type](this);o=this.matrix?K(o,this.matrix):o;for(var a=1;a<n+1;a++)i.push(r.path(o).attr({stroke:e.color,fill:e.fill?e.color:"none","stroke-linejoin":"round","stroke-linecap":"round","stroke-width":+(e.width/n*a).toFixed(3),opacity:+(e.opacity/n).toFixed(3)}));return i.insertBefore(this).translate(e.offsetx,e.offsety)};var Yt=function(t,n,r,i,o,a,s,l,u){return null==u?pt(t,n,r,i,o,a,s,l):e.findDotsAtSegment(t,n,r,i,o,a,s,l,function(t,e,n,r,i,o,a,s,l){if(!(l<0||pt(t,e,n,r,i,o,a,s)<l)){var u,c=.5,h=1-c;for(u=pt(t,e,n,r,i,o,a,s,h);C(u-l)>.01;)u=pt(t,e,n,r,i,o,a,s,h+=(u<l?1:-1)*(c/=2));return h}}(t,n,r,i,o,a,s,l,u))},Gt=function(t,n){return function(r,i,o){for(var a,s,l,u,c,h="",f={},d=0,p=0,g=(r=kt(r)).length;p<g;p++){if("M"==(l=r[p])[0])a=+l[1],s=+l[2];else{if(d+(u=Yt(a,s,l[1],l[2],l[3],l[4],l[5],l[6]))>i){if(n&&!f.start){if(h+=["C"+(c=Yt(a,s,l[1],l[2],l[3],l[4],l[5],l[6],i-d)).start.x,c.start.y,c.m.x,c.m.y,c.x,c.y],o)return h;f.start=h,h=["M"+c.x,c.y+"C"+c.n.x,c.n.y,c.end.x,c.end.y,l[5],l[6]].join(),d+=u,a=+l[5],s=+l[6];continue}if(!t&&!n)return{x:(c=Yt(a,s,l[1],l[2],l[3],l[4],l[5],l[6],i-d)).x,y:c.y,alpha:c.alpha}}d+=u,a=+l[5],s=+l[6]}h+=l.shift()+l}return f.end=h,(c=t?d:n?f:e.findDotsAtSegment(a,s,l[0],l[1],l[2],l[3],l[4],l[5],1)).alpha&&(c={x:c.x,y:c.y,alpha:c.alpha}),c}},$t=Gt(1),Qt=Gt(),Jt=Gt(0,1);e.getTotalLength=$t,e.getPointAtLength=Qt,e.getSubpath=function(t,e,n){if(this.getTotalLength(t)-n<1e-6)return Jt(t,e).end;var r=Jt(t,n,1);return e?Jt(r,e).end:r},zt.getTotalLength=function(){var t=this.getPath();if(t)return this.node.getTotalLength?this.node.getTotalLength():$t(t)},zt.getPointAtLength=function(t){var e=this.getPath();if(e)return Qt(e,t)},zt.getPath=function(){var t,n=e._getPath[this.type];if("text"!=this.type&&"set"!=this.type)return n&&(t=n(this)),t},zt.getSubpath=function(t,n){var r=this.getPath();if(r)return e.getSubpath(r,t,n)};var Kt=e.easing_formulas={linear:function(t){return t},"<":function(t){return T(t,1.7)},">":function(t){return T(t,.48)},"<>":function(t){var e=.48-t/1.04,n=_.sqrt(.1734+e*e),r=n-e,i=-n-e,o=T(C(r),1/3)*(r<0?-1:1)+T(C(i),1/3)*(i<0?-1:1)+.5;return 3*(1-o)*o*o+o*o*o},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},elastic:function(t){return t==!!t?t:T(2,-10*t)*_.sin(2*D*(t-.075)/.3)+1},bounce:function(t){var e=7.5625,n=2.75;return t<1/n?e*t*t:t<2/n?e*(t-=1.5/n)*t+.75:t<2.5/n?e*(t-=2.25/n)*t+.9375:e*(t-=2.625/n)*t+.984375}};Kt.easeIn=Kt["ease-in"]=Kt["<"],Kt.easeOut=Kt["ease-out"]=Kt[">"],Kt.easeInOut=Kt["ease-in-out"]=Kt["<>"],Kt["back-in"]=Kt.backIn,Kt["back-out"]=Kt.backOut;var Zt=[],te=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){setTimeout(t,16)},ee=function(){for(var n=+new Date,r=0;r<Zt.length;r++){var i=Zt[r];if(!i.el.removed&&!i.paused){var o,a,s=n-i.start,l=i.ms,c=i.easing,h=i.from,f=i.diff,d=i.to,p=i.el,g={},v={};if(i.initstatus?(s=(i.initstatus*i.anim.top-i.prev)/(i.percent-i.prev)*l,i.status=i.initstatus,delete i.initstatus,i.stop&&Zt.splice(r--,1)):i.status=(i.prev+s/l*(i.percent-i.prev))/i.anim.top,!(s<0))if(s<l){var y=c(s/l);for(var b in h)if(h[u](b)){switch(B[b]){case k:o=+h[b]+y*l*f[b];break;case"colour":o="rgb("+[ne(j(h[b].r+y*l*f[b].r)),ne(j(h[b].g+y*l*f[b].g)),ne(j(h[b].b+y*l*f[b].b))].join(",")+")";break;case"path":o=[];for(var x=0,_=h[b].length;x<_;x++){o[x]=[h[b][x][0]];for(var w=1,S=h[b][x].length;w<S;w++)o[x][w]=+h[b][x][w]+y*l*f[b][x][w];o[x]=o[x].join(m)}o=o.join(m);break;case"transform":if(f[b].real)for(o=[],x=0,_=h[b].length;x<_;x++)for(o[x]=[h[b][x][0]],w=1,S=h[b][x].length;w<S;w++)o[x][w]=h[b][x][w]+y*l*f[b][x][w];else{var C=function(t){return+h[b][t]+y*l*f[b][t]};o=[["m",C(0),C(1),C(2),C(3),C(4),C(5)]]}break;case"csv":if("clip-rect"==b)for(o=[],x=4;x--;)o[x]=+h[b][x]+y*l*f[b][x];break;default:var T=[].concat(h[b]);for(o=[],x=p.paper.customAttributes[b].length;x--;)o[x]=+T[x]+y*l*f[b][x]}g[b]=o}p.attr(g),function(e,n,r){setTimeout(function(){t("raphael.anim.frame."+e,n,r)})}(p.id,p,i.anim)}else{if(function(n,r,i){setTimeout(function(){t("raphael.anim.frame."+r.id,r,i),t("raphael.anim.finish."+r.id,r,i),e.is(n,"function")&&n.call(r)})}(i.callback,p,i.anim),p.attr(d),Zt.splice(r--,1),i.repeat>1&&!i.next){for(a in d)d[u](a)&&(v[a]=i.totalOrigin[a]);i.el.attr(v),ie(i.anim,i.el,i.anim.percents[0],null,i.totalOrigin,i.repeat-1)}i.next&&!i.stop&&ie(i.anim,i.el,i.next,null,i.totalOrigin,i.repeat)}}}Zt.length&&te(ee)},ne=function(t){return t>255?255:t<0?0:t};function re(t,e){var n=[],r={};if(this.ms=e,this.times=1,t){for(var i in t)t[u](i)&&(r[P(i)]=t[i],n.push(P(i)));n.sort(Y)}this.anim=r,this.top=n[n.length-1],this.percents=n}function ie(n,r,i,o,s,l){i=P(i);var c,h,f,d,p,g,m=n.ms,y={},b={},x={};if(o)for(w=0,S=Zt.length;w<S;w++){var _=Zt[w];if(_.el.id==r.id&&_.anim==n){_.percent!=i?(Zt.splice(w,1),f=1):h=_,r.attr(_.totalOrigin);break}}else o=+b;for(var w=0,S=n.percents.length;w<S;w++){if(n.percents[w]==i||n.percents[w]>o*n.top){m=m/n.top*((i=n.percents[w])-(p=n.percents[w-1]||0)),d=n.percents[w+1],c=n.anim[i];break}o&&r.attr(n.anim[n.percents[w]])}if(c){if(h)h.initstatus=o,h.start=new Date-h.ms*o;else{for(var T in c)if(c[u](T)&&(B[u](T)||r.paper.customAttributes[u](T)))switch(y[T]=r.attr(T),null==y[T]&&(y[T]=O[T]),b[T]=c[T],B[T]){case k:x[T]=(b[T]-y[T])/m;break;case"colour":y[T]=e.getRGB(y[T]);var D=e.getRGB(b[T]);x[T]={r:(D.r-y[T].r)/m,g:(D.g-y[T].g)/m,b:(D.b-y[T].b)/m};break;case"path":var E=kt(y[T],b[T]),A=E[1];for(y[T]=E[0],x[T]=[],w=0,S=y[T].length;w<S;w++){x[T][w]=[0];for(var L=1,I=y[T][w].length;L<I;L++)x[T][w][L]=(A[w][L]-y[T][w][L])/m}break;case"transform":var N=r._,j=Nt(N[T],b[T]);if(j)for(y[T]=j.from,b[T]=j.to,x[T]=[],x[T].real=!0,w=0,S=y[T].length;w<S;w++)for(x[T][w]=[y[T][w][0]],L=1,I=y[T][w].length;L<I;L++)x[T][w][L]=(b[T][w][L]-y[T][w][L])/m;else{var M=r.matrix||new Ft,R={_:{transform:N.transform},getBBox:function(){return r.getBBox(1)}};y[T]=[M.a,M.b,M.c,M.d,M.e,M.f],Lt(R,b[T]),b[T]=R._.transform,x[T]=[(R.matrix.a-M.a)/m,(R.matrix.b-M.b)/m,(R.matrix.c-M.c)/m,(R.matrix.d-M.d)/m,(R.matrix.e-M.e)/m,(R.matrix.f-M.f)/m]}break;case"csv":var H=v(c[T]).split(a),q=v(y[T]).split(a);if("clip-rect"==T)for(y[T]=q,x[T]=[],w=q.length;w--;)x[T][w]=(H[w]-y[T][w])/m;b[T]=H;break;default:for(H=[].concat(c[T]),q=[].concat(y[T]),x[T]=[],w=r.paper.customAttributes[T].length;w--;)x[T][w]=((H[w]||0)-(q[w]||0))/m}var W=c.easing,z=e.easing_formulas[W];if(!z)if((z=v(W).match(F))&&5==z.length){var U=z;z=function(t){return function(t,e,n,r,i,o){var a=3*e,s=3*(r-e)-a,l=1-a-s,u=3*n,c=3*(i-n)-u,h=1-u-c;function f(t){return((l*t+s)*t+a)*t}return function(t,e){var n=function(t,e){var n,r,i,o,u,c;for(i=t,c=0;c<8;c++){if(o=f(i)-t,C(o)<e)return i;if(C(u=(3*l*i+2*s)*i+a)<1e-6)break;i-=o/u}if((i=t)<(n=0))return n;if(i>(r=1))return r;for(;n<r;){if(o=f(i),C(o-t)<e)return i;t>o?n=i:r=i,i=(r-n)/2+n}return i}(t,e);return((h*n+c)*n+u)*n}(t,1/(200*o))}(t,+U[1],+U[2],+U[3],+U[4],m)}}else z=G;if(_={anim:n,percent:i,timestamp:g=c.start||n.start||+new Date,start:g+(n.del||0),status:0,initstatus:o||0,stop:!1,ms:m,easing:z,from:y,diff:x,to:b,el:r,callback:c.callback,prev:p,next:d,repeat:l||n.times,origin:r.attr(),totalOrigin:s},Zt.push(_),o&&!h&&!f&&(_.stop=!0,_.start=new Date-m*o,1==Zt.length))return ee();f&&(_.start=new Date-_.ms*o),1==Zt.length&&te(ee)}t("raphael.anim.start."+r.id,r,n)}}function oe(t){for(var e=0;e<Zt.length;e++)Zt[e].el.paper==t&&Zt.splice(e--,1)}zt.animateWith=function(t,n,r,i,o,a){var s=this;if(s.removed)return a&&a.call(s),s;var l=r instanceof re?r:e.animation(r,i,o,a);ie(l,s,l.percents[0],null,s.attr());for(var u=0,c=Zt.length;u<c;u++)if(Zt[u].anim==n&&Zt[u].el==t){Zt[c-1].start=Zt[u].start;break}return s},zt.onAnimation=function(e){return e?t.on("raphael.anim.frame."+this.id,e):t.unbind("raphael.anim.frame."+this.id),this},re.prototype.delay=function(t){var e=new re(this.anim,this.ms);return e.times=this.times,e.del=+t||0,e},re.prototype.repeat=function(t){var e=new re(this.anim,this.ms);return e.del=this.del,e.times=_.floor(w(t,0))||1,e},e.animation=function(t,n,r,i){if(t instanceof re)return t;!e.is(r,"function")&&r||(i=i||r||null,r=null),t=Object(t),n=+n||0;var o,a,s={};for(a in t)t[u](a)&&P(a)!=a&&P(a)+"%"!=a&&(o=!0,s[a]=t[a]);if(o)return r&&(s.easing=r),i&&(s.callback=i),new re({100:s},n);if(i){var l=0;for(var c in t){var h=M(c);t[u](c)&&h>l&&(l=h)}!t[l+="%"].callback&&(t[l].callback=i)}return new re(t,n)},zt.animate=function(t,n,r,i){var o=this;if(o.removed)return i&&i.call(o),o;var a=t instanceof re?t:e.animation(t,n,r,i);return ie(a,o,a.percents[0],null,o.attr()),o},zt.setTime=function(t,e){return t&&null!=e&&this.status(t,S(e,t.ms)/t.ms),this},zt.status=function(t,e){var n,r,i=[],o=0;if(null!=e)return ie(t,this,-1,S(e,1)),this;for(n=Zt.length;o<n;o++)if((r=Zt[o]).el.id==this.id&&(!t||r.anim==t)){if(t)return r.status;i.push({anim:r.anim,status:r.status})}return t?0:i},zt.pause=function(e){for(var n=0;n<Zt.length;n++)Zt[n].el.id!=this.id||e&&Zt[n].anim!=e||!1!==t("raphael.anim.pause."+this.id,this,Zt[n].anim)&&(Zt[n].paused=!0);return this},zt.resume=function(e){for(var n=0;n<Zt.length;n++)if(Zt[n].el.id==this.id&&(!e||Zt[n].anim==e)){var r=Zt[n];!1!==t("raphael.anim.resume."+this.id,this,r.anim)&&(delete r.paused,this.status(r.anim,r.status))}return this},zt.stop=function(e){for(var n=0;n<Zt.length;n++)Zt[n].el.id!=this.id||e&&Zt[n].anim!=e||!1!==t("raphael.anim.stop."+this.id,this,Zt[n].anim)&&Zt.splice(n--,1);return this},t.on("raphael.remove",oe),t.on("raphael.clear",oe),zt.toString=function(){return"Raphaël’s object"};var ae,se,le=function(t){if(this.items=[],this.length=0,this.type="set",t)for(var e=0,n=t.length;e<n;e++)!t[e]||t[e].constructor!=zt.constructor&&t[e].constructor!=le||(this[this.items.length]=this.items[this.items.length]=t[e],this.length++)},ue=le.prototype;for(var ce in ue.push=function(){for(var t,e,n=0,r=arguments.length;n<r;n++)!(t=arguments[n])||t.constructor!=zt.constructor&&t.constructor!=le||(this[e=this.items.length]=this.items[e]=t,this.length++);return this},ue.pop=function(){return this.length&&delete this[this.length--],this.items.pop()},ue.forEach=function(t,e){for(var n=0,r=this.items.length;n<r;n++)if(!1===t.call(e,this.items[n],n))return this;return this},zt)zt[u](ce)&&(ue[ce]=function(t){return function(){var e=arguments;return this.forEach(function(n){n[t][d](n,e)})}}(ce));return ue.attr=function(t,n){if(t&&e.is(t,A)&&e.is(t[0],"object"))for(var r=0,i=t.length;r<i;r++)this.items[r].attr(t[r]);else for(var o=0,a=this.items.length;o<a;o++)this.items[o].attr(t,n);return this},ue.clear=function(){for(;this.length;)this.pop()},ue.splice=function(t,e,n){t=t<0?w(this.length+t,0):t,e=w(0,S(this.length-t,e));var r,i=[],o=[],a=[];for(r=2;r<arguments.length;r++)a.push(arguments[r]);for(r=0;r<e;r++)o.push(this[t+r]);for(;r<this.length-t;r++)i.push(this[t+r]);var s=a.length;for(r=0;r<s+i.length;r++)this.items[t+r]=this[t+r]=r<s?a[r]:i[r-s];for(r=this.items.length=this.length-=e-s;this[r];)delete this[r++];return new le(o)},ue.exclude=function(t){for(var e=0,n=this.length;e<n;e++)if(this[e]==t)return this.splice(e,1),!0},ue.animate=function(t,n,r,i){(e.is(r,"function")||!r)&&(i=r||null);var o,a,s=this.items.length,l=s,u=this;if(!s)return this;i&&(a=function(){!--s&&i.call(u)}),r=e.is(r,E)?r:a;var c=e.animation(t,n,r,a);for(o=this.items[--l].animate(c);l--;)this.items[l]&&!this.items[l].removed&&this.items[l].animateWith(o,c,c),this.items[l]&&!this.items[l].removed||s--;return this},ue.insertAfter=function(t){for(var e=this.items.length;e--;)this.items[e].insertAfter(t);return this},ue.getBBox=function(){for(var t=[],e=[],n=[],r=[],i=this.items.length;i--;)if(!this.items[i].removed){var o=this.items[i].getBBox();t.push(o.x),e.push(o.y),n.push(o.x+o.width),r.push(o.y+o.height)}return{x:t=S[d](0,t),y:e=S[d](0,e),x2:n=w[d](0,n),y2:r=w[d](0,r),width:n-t,height:r-e}},ue.clone=function(t){t=this.paper.set();for(var e=0,n=this.items.length;e<n;e++)t.push(this.items[e].clone());return t},ue.toString=function(){return"Raphaël‘s set"},ue.glow=function(t){var e=this.paper.set();return this.forEach(function(n,r){var i=n.glow(t);null!=i&&i.forEach(function(t,n){e.push(t)})}),e},ue.isPointInside=function(t,e){var n=!1;return this.forEach(function(r){if(r.isPointInside(t,e))return n=!0,!1}),n},e.registerFont=function(t){if(!t.face)return t;this.fonts=this.fonts||{};var e={w:t.w,face:{},glyphs:{}},n=t.face["font-family"];for(var r in t.face)t.face[u](r)&&(e.face[r]=t.face[r]);if(this.fonts[n]?this.fonts[n].push(e):this.fonts[n]=[e],!t.svg)for(var i in e.face["units-per-em"]=M(t.face["units-per-em"],10),t.glyphs)if(t.glyphs[u](i)){var o=t.glyphs[i];if(e.glyphs[i]={w:o.w,k:{},d:o.d&&"M"+o.d.replace(/[mlcxtrv]/g,function(t){return{l:"L",c:"C",x:"z",t:"m",r:"l",v:"c"}[t]||"M"})+"z"},o.k)for(var a in o.k)o[u](a)&&(e.glyphs[i].k[a]=o.k[a])}return t},r.getFont=function(t,n,r,i){if(i=i||"normal",r=r||"normal",n=+n||{normal:400,bold:700,lighter:300,bolder:800}[n]||400,e.fonts){var o,a=e.fonts[t];if(!a){var s=new RegExp("(^|\\s)"+t.replace(/[^\w\d\s+!~.:_-]/g,g)+"(\\s|$)","i");for(var l in e.fonts)if(e.fonts[u](l)&&s.test(l)){a=e.fonts[l];break}}if(a)for(var c=0,h=a.length;c<h&&((o=a[c]).face["font-weight"]!=n||o.face["font-style"]!=r&&o.face["font-style"]||o.face["font-stretch"]!=i);c++);return o}},r.print=function(t,n,r,i,o,s,l,u){s=s||"middle",l=w(S(l||0,1),-1),u=w(S(u||1,3),1);var c,h=v(r).split(g),f=0,d=0,p=g;if(e.is(i,"string")&&(i=this.getFont(i)),i){c=(o||16)/i.face["units-per-em"];for(var m=i.face.bbox.split(a),y=+m[0],b=m[3]-m[1],x=0,_=+m[1]+("baseline"==s?b+ +i.face.descent:b/2),C=0,T=h.length;C<T;C++){if("\n"==h[C])f=0,k=0,d=0,x+=b*u;else{var D=d&&i.glyphs[h[C-1]]||{},k=i.glyphs[h[C]];f+=d?(D.w||i.w)+(D.k&&D.k[h[C]]||0)+i.w*l:0,d=1}k&&k.d&&(p+=e.transformPath(k.d,["t",f*c,x*c,"s",c,c,y,_,"t",(t-y)/c,(n-_)/c]))}}return this.path(p).attr({fill:"#000",stroke:"none"})},r.add=function(t){if(e.is(t,"array"))for(var n,r=this.set(),i=0,o=t.length;i<o;i++)s[u]((n=t[i]||{}).type)&&r.push(this[n.type]().attr(n));return r},e.format=function(t,n){var r=e.is(n,A)?[0].concat(n):arguments;return t&&e.is(t,E)&&r.length-1&&(t=t.replace(l,function(t,e){return null==r[++e]?g:r[e]})),t||g},e.fullfill=(ae=/\{([^\}]+)\}/g,se=/(?:(?:^|\.)(.+?)(?=\[|\.|$|\()|\[('|")(.+?)\2\])(\(\))?/g,function(t,e){return String(t).replace(ae,function(t,n){return function(t,e,n){var r=n;return e.replace(se,function(t,e,n,i,o){e=e||i,r&&(e in r&&(r=r[e]),"function"==typeof r&&o&&(r=r()))}),r=(null==r||r==n?t:r)+""}(t,n,e)})}),e.ninja=function(){if(h.was)c.win.Raphael=h.is;else{window.Raphael=void 0;try{delete window.Raphael}catch(t){}}return e},e.st=ue,t.on("raphael.DOMload",function(){n=!0}),function(t,n,r){null==t.readyState&&t.addEventListener&&(t.addEventListener(n,r=function(){t.removeEventListener(n,r,!1),t.readyState="complete"},!1),t.readyState="loading"),function n(){/in/.test(t.readyState)?setTimeout(n,9):e.eve("raphael.DOMload")}()}(document,"DOMContentLoaded"),e}).apply(e,r))||(t.exports=i)},"./dev/raphael.svg.js":function(t,e,n){var r,i;r=[n("./dev/raphael.core.js")],void 0===(i=(function(t){if(!t||t.svg){var e="hasOwnProperty",n=String,r=parseFloat,i=parseInt,o=Math,a=o.max,s=o.abs,l=o.pow,u=/[, ]+/,c=t.eve,h="",f=" ",d="http://www.w3.org/1999/xlink",p={block:"M5,0 0,2.5 5,5z",classic:"M5,0 0,2.5 5,5 3.5,3 3.5,2z",diamond:"M2.5,0 5,2.5 2.5,5 0,2.5z",open:"M6,1 1,3.5 6,6",oval:"M2.5,0A2.5,2.5,0,0,1,2.5,5 2.5,2.5,0,0,1,2.5,0z"},g={};t.toString=function(){return"Your browser supports SVG.\nYou are running Raphaël "+this.version};var m=function(r,i){if(i)for(var o in"string"==typeof r&&(r=m(r)),i)i[e](o)&&("xlink:"==o.substring(0,6)?r.setAttributeNS(d,o.substring(6),n(i[o])):r.setAttribute(o,n(i[o])));else(r=t._g.doc.createElementNS("http://www.w3.org/2000/svg",r)).style&&(r.style.webkitTapHighlightColor="rgba(0,0,0,0)");return r},v=function(e,i){var u="linear",c=e.id+i,f=.5,d=.5,p=e.node,g=e.paper,v=p.style,b=t._g.doc.getElementById(c);if(!b){if(i=(i=n(i).replace(t._radial_gradient,function(t,e,n){if(u="radial",e&&n){f=r(e);var i=2*((d=r(n))>.5)-1;l(f-.5,2)+l(d-.5,2)>.25&&(d=o.sqrt(.25-l(f-.5,2))*i+.5)&&.5!=d&&(d=d.toFixed(5)-1e-5*i)}return h})).split(/\s*\-\s*/),"linear"==u){var x=i.shift();if(x=-r(x),isNaN(x))return null;var _=[0,0,o.cos(t.rad(x)),o.sin(t.rad(x))],w=1/(a(s(_[2]),s(_[3]))||1);_[2]*=w,_[3]*=w,_[2]<0&&(_[0]=-_[2],_[2]=0),_[3]<0&&(_[1]=-_[3],_[3]=0)}var S=t._parseDots(i);if(!S)return null;if(c=c.replace(/[\(\)\s,\xb0#]/g,"_"),e.gradient&&c!=e.gradient.id&&(g.defs.removeChild(e.gradient),delete e.gradient),!e.gradient){b=m(u+"Gradient",{id:c}),e.gradient=b,m(b,"radial"==u?{fx:f,fy:d}:{x1:_[0],y1:_[1],x2:_[2],y2:_[3],gradientTransform:e.matrix.invert()}),g.defs.appendChild(b);for(var C=0,T=S.length;C<T;C++)b.appendChild(m("stop",{offset:S[C].offset?S[C].offset:C?"100%":"0%","stop-color":S[C].color||"#fff","stop-opacity":isFinite(S[C].opacity)?S[C].opacity:1}))}}return m(p,{fill:y(c),opacity:1,"fill-opacity":1}),v.fill=h,v.opacity=1,v.fillOpacity=1,1},y=function(t){if((e=document.documentMode)&&(9===e||10===e))return"url('#"+t+"')";var e,n=document.location;return"url('"+n.protocol+"//"+n.host+n.pathname+n.search+"#"+t+"')"},b=function(t){var e=t.getBBox(1);m(t.pattern,{patternTransform:t.matrix.invert()+" translate("+e.x+","+e.y+")"})},x=function(r,i,o){if("path"==r.type){for(var a,s,l,u,c,f=n(i).toLowerCase().split("-"),d=r.paper,v=o?"end":"start",y=r.node,b=r.attrs,x=b["stroke-width"],_=f.length,w="classic",S=3,C=3,T=5;_--;)switch(f[_]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":w=f[_];break;case"wide":C=5;break;case"narrow":C=2;break;case"long":S=5;break;case"short":S=2}if("open"==w?(S+=2,C+=2,T+=2,l=1,u=o?4:1,c={fill:"none",stroke:b.stroke}):(u=l=S/2,c={fill:b.stroke,stroke:"none"}),r._.arrows?o?(r._.arrows.endPath&&g[r._.arrows.endPath]--,r._.arrows.endMarker&&g[r._.arrows.endMarker]--):(r._.arrows.startPath&&g[r._.arrows.startPath]--,r._.arrows.startMarker&&g[r._.arrows.startMarker]--):r._.arrows={},"none"!=w){var D="raphael-marker-"+w,k="raphael-marker-"+v+w+S+C+"-obj"+r.id;t._g.doc.getElementById(D)?g[D]++:(d.defs.appendChild(m(m("path"),{"stroke-linecap":"round",d:p[w],id:D})),g[D]=1);var E,A=t._g.doc.getElementById(k);A?(g[k]++,E=A.getElementsByTagName("use")[0]):(A=m(m("marker"),{id:k,markerHeight:C,markerWidth:S,orient:"auto",refX:u,refY:C/2}),E=m(m("use"),{"xlink:href":"#"+D,transform:(o?"rotate(180 "+S/2+" "+C/2+") ":h)+"scale("+S/T+","+C/T+")","stroke-width":(1/((S/T+C/T)/2)).toFixed(4)}),A.appendChild(E),d.defs.appendChild(A),g[k]=1),m(E,c);var L=l*("diamond"!=w&&"oval"!=w);o?(a=r._.arrows.startdx*x||0,s=t.getTotalLength(b.path)-L*x):(a=L*x,s=t.getTotalLength(b.path)-(r._.arrows.enddx*x||0)),(c={})["marker-"+v]="url(#"+k+")",(s||a)&&(c.d=t.getSubpath(b.path,a,s)),m(y,c),r._.arrows[v+"Path"]=D,r._.arrows[v+"Marker"]=k,r._.arrows[v+"dx"]=L,r._.arrows[v+"Type"]=w,r._.arrows[v+"String"]=i}else o?(a=r._.arrows.startdx*x||0,s=t.getTotalLength(b.path)-a):(a=0,s=t.getTotalLength(b.path)-(r._.arrows.enddx*x||0)),r._.arrows[v+"Path"]&&m(y,{d:t.getSubpath(b.path,a,s)}),delete r._.arrows[v+"Path"],delete r._.arrows[v+"Marker"],delete r._.arrows[v+"dx"],delete r._.arrows[v+"Type"],delete r._.arrows[v+"String"];for(c in g)if(g[e](c)&&!g[c]){var I=t._g.doc.getElementById(c);I&&I.parentNode.removeChild(I)}}},_={"-":[3,1],".":[1,1],"-.":[3,1,1,1],"-..":[3,1,1,1,1,1],". ":[1,3],"- ":[4,3],"--":[8,3],"- .":[4,3,1,3],"--.":[8,3,1,3],"--..":[8,3,1,3,1,3]},w=function(t,e,r){if(e=_[n(e).toLowerCase()]){for(var i=t.attrs["stroke-width"]||"1",o={round:i,square:i,butt:0}[t.attrs["stroke-linecap"]||r["stroke-linecap"]]||0,a=[],s=e.length;s--;)a[s]=e[s]*i+(s%2?1:-1)*o;m(t.node,{"stroke-dasharray":a.join(",")})}else m(t.node,{"stroke-dasharray":"none"})},S=function(r,o){var l=r.node,c=r.attrs,f=l.style.visibility;for(var p in l.style.visibility="hidden",o)if(o[e](p)){if(!t._availableAttrs[e](p))continue;var g=o[p];switch(c[p]=g,p){case"blur":r.blur(g);break;case"title":var y=l.getElementsByTagName("title");if(y.length&&(y=y[0]))y.firstChild.nodeValue=g;else{y=m("title");var _=t._g.doc.createTextNode(g);y.appendChild(_),l.appendChild(y)}break;case"href":case"target":var S=l.parentNode;if("a"!=S.tagName.toLowerCase()){var T=m("a");S.insertBefore(T,l),T.appendChild(l),S=T}"target"==p?S.setAttributeNS(d,"show","blank"==g?"new":g):S.setAttributeNS(d,p,g);break;case"cursor":l.style.cursor=g;break;case"transform":r.transform(g);break;case"arrow-start":x(r,g);break;case"arrow-end":x(r,g,1);break;case"clip-rect":var D=n(g).split(u);if(4==D.length){r.clip&&r.clip.parentNode.parentNode.removeChild(r.clip.parentNode);var k=m("clipPath"),E=m("rect");k.id=t.createUUID(),m(E,{x:D[0],y:D[1],width:D[2],height:D[3]}),k.appendChild(E),r.paper.defs.appendChild(k),m(l,{"clip-path":"url(#"+k.id+")"}),r.clip=E}if(!g){var A=l.getAttribute("clip-path");if(A){var L=t._g.doc.getElementById(A.replace(/(^url\(#|\)$)/g,h));L&&L.parentNode.removeChild(L),m(l,{"clip-path":h}),delete r.clip}}break;case"path":"path"==r.type&&(m(l,{d:g?c.path=t._pathToAbsolute(g):"M0,0"}),r._.dirty=1,r._.arrows&&("startString"in r._.arrows&&x(r,r._.arrows.startString),"endString"in r._.arrows&&x(r,r._.arrows.endString,1)));break;case"width":if(l.setAttribute(p,g),r._.dirty=1,!c.fx)break;p="x",g=c.x;case"x":c.fx&&(g=-c.x-(c.width||0));case"rx":if("rx"==p&&"rect"==r.type)break;case"cx":l.setAttribute(p,g),r.pattern&&b(r),r._.dirty=1;break;case"height":if(l.setAttribute(p,g),r._.dirty=1,!c.fy)break;p="y",g=c.y;case"y":c.fy&&(g=-c.y-(c.height||0));case"ry":if("ry"==p&&"rect"==r.type)break;case"cy":l.setAttribute(p,g),r.pattern&&b(r),r._.dirty=1;break;case"r":"rect"==r.type?m(l,{rx:g,ry:g}):l.setAttribute(p,g),r._.dirty=1;break;case"src":"image"==r.type&&l.setAttributeNS(d,"href",g);break;case"stroke-width":1==r._.sx&&1==r._.sy||(g/=a(s(r._.sx),s(r._.sy))||1),l.setAttribute(p,g),c["stroke-dasharray"]&&w(r,c["stroke-dasharray"],o),r._.arrows&&("startString"in r._.arrows&&x(r,r._.arrows.startString),"endString"in r._.arrows&&x(r,r._.arrows.endString,1));break;case"stroke-dasharray":w(r,g,o);break;case"fill":var I=n(g).match(t._ISURL);if(I){k=m("pattern");var N=m("image");k.id=t.createUUID(),m(k,{x:0,y:0,patternUnits:"userSpaceOnUse",height:1,width:1}),m(N,{x:0,y:0,"xlink:href":I[1]}),k.appendChild(N),function(e){t._preload(I[1],function(){var t=this.offsetWidth,n=this.offsetHeight;m(e,{width:t,height:n}),m(N,{width:t,height:n})})}(k),r.paper.defs.appendChild(k),m(l,{fill:"url(#"+k.id+")"}),r.pattern=k,r.pattern&&b(r);break}var F=t.getRGB(g);if(F.error){if(("circle"==r.type||"ellipse"==r.type||"r"!=n(g).charAt())&&v(r,g)){if("opacity"in c||"fill-opacity"in c){var j=t._g.doc.getElementById(l.getAttribute("fill").replace(/^url\(#|\)$/g,h));if(j){var P=j.getElementsByTagName("stop");m(P[P.length-1],{"stop-opacity":("opacity"in c?c.opacity:1)*("fill-opacity"in c?c["fill-opacity"]:1)})}}c.gradient=g,c.fill="none";break}}else delete o.gradient,delete c.gradient,!t.is(c.opacity,"undefined")&&t.is(o.opacity,"undefined")&&m(l,{opacity:c.opacity}),!t.is(c["fill-opacity"],"undefined")&&t.is(o["fill-opacity"],"undefined")&&m(l,{"fill-opacity":c["fill-opacity"]});F[e]("opacity")&&m(l,{"fill-opacity":F.opacity>1?F.opacity/100:F.opacity});case"stroke":F=t.getRGB(g),l.setAttribute(p,F.hex),"stroke"==p&&F[e]("opacity")&&m(l,{"stroke-opacity":F.opacity>1?F.opacity/100:F.opacity}),"stroke"==p&&r._.arrows&&("startString"in r._.arrows&&x(r,r._.arrows.startString),"endString"in r._.arrows&&x(r,r._.arrows.endString,1));break;case"gradient":("circle"==r.type||"ellipse"==r.type||"r"!=n(g).charAt())&&v(r,g);break;case"opacity":c.gradient&&!c[e]("stroke-opacity")&&m(l,{"stroke-opacity":g>1?g/100:g});case"fill-opacity":if(c.gradient){(j=t._g.doc.getElementById(l.getAttribute("fill").replace(/^url\(#|\)$/g,h)))&&(P=j.getElementsByTagName("stop"),m(P[P.length-1],{"stop-opacity":g}));break}default:"font-size"==p&&(g=i(g,10)+"px");var M=p.replace(/(\-.)/g,function(t){return t.substring(1).toUpperCase()});l.style[M]=g,r._.dirty=1,l.setAttribute(p,g)}}C(r,o),l.style.visibility=f},C=function(r,o){if("text"==r.type&&(o[e]("text")||o[e]("font")||o[e]("font-size")||o[e]("x")||o[e]("y"))){var a=r.attrs,s=r.node,l=s.firstChild?i(t._g.doc.defaultView.getComputedStyle(s.firstChild,h).getPropertyValue("font-size"),10):10;if(o[e]("text")){for(a.text=o.text;s.firstChild;)s.removeChild(s.firstChild);for(var u,c=n(o.text).split("\n"),f=[],d=0,p=c.length;d<p;d++)u=m("tspan"),d&&m(u,{dy:1.2*l,x:a.x}),u.appendChild(t._g.doc.createTextNode(c[d])),s.appendChild(u),f[d]=u}else for(d=0,p=(f=s.getElementsByTagName("tspan")).length;d<p;d++)d?m(f[d],{dy:1.2*l,x:a.x}):m(f[0],{dy:0});m(s,{x:a.x,y:a.y}),r._.dirty=1;var g=r._getBBox(),v=a.y-(g.y+g.height/2);v&&t.is(v,"finite")&&m(f[0],{dy:v})}},T=function(t){return t.parentNode&&"a"===t.parentNode.tagName.toLowerCase()?t.parentNode:t},D=function(e,n){this[0]=this.node=e,e.raphael=!0,this.id=("0000"+(Math.random()*Math.pow(36,5)<<0).toString(36)).slice(-5),e.raphaelid=this.id,this.matrix=t.matrix(),this.realPath=null,this.paper=n,this.attrs=this.attrs||{},this._={transform:[],sx:1,sy:1,deg:0,dx:0,dy:0,dirty:1},!n.bottom&&(n.bottom=this),this.prev=n.top,n.top&&(n.top.next=this),n.top=this,this.next=null},k=t.el;D.prototype=k,k.constructor=D,t._engine.path=function(t,e){var n=m("path");e.canvas&&e.canvas.appendChild(n);var r=new D(n,e);return r.type="path",S(r,{fill:"none",stroke:"#000",path:t}),r},k.rotate=function(t,e,i){if(this.removed)return this;if((t=n(t).split(u)).length-1&&(e=r(t[1]),i=r(t[2])),t=r(t[0]),null==i&&(e=i),null==e||null==i){var o=this.getBBox(1);e=o.x+o.width/2,i=o.y+o.height/2}return this.transform(this._.transform.concat([["r",t,e,i]])),this},k.scale=function(t,e,i,o){if(this.removed)return this;if((t=n(t).split(u)).length-1&&(e=r(t[1]),i=r(t[2]),o=r(t[3])),t=r(t[0]),null==e&&(e=t),null==o&&(i=o),null==i||null==o)var a=this.getBBox(1);return this.transform(this._.transform.concat([["s",t,e,i=null==i?a.x+a.width/2:i,o=null==o?a.y+a.height/2:o]])),this},k.translate=function(t,e){return this.removed||((t=n(t).split(u)).length-1&&(e=r(t[1])),t=r(t[0])||0,this.transform(this._.transform.concat([["t",t,e=+e||0]]))),this},k.transform=function(n){var r=this._;if(null==n)return r.transform;if(t._extractTransform(this,n),this.clip&&m(this.clip,{transform:this.matrix.invert()}),this.pattern&&b(this),this.node&&m(this.node,{transform:this.matrix}),1!=r.sx||1!=r.sy){var i=this.attrs[e]("stroke-width")?this.attrs["stroke-width"]:1;this.attr({"stroke-width":i})}return this},k.hide=function(){return this.removed||(this.node.style.display="none"),this},k.show=function(){return this.removed||(this.node.style.display=""),this},k.remove=function(){var e=T(this.node);if(!this.removed&&e.parentNode){var n=this.paper;for(var r in n.__set__&&n.__set__.exclude(this),c.unbind("raphael.*.*."+this.id),this.gradient&&n.defs.removeChild(this.gradient),t._tear(this,n),e.parentNode.removeChild(e),this.removeData(),this)this[r]="function"==typeof this[r]?t._removedFactory(r):null;this.removed=!0}},k._getBBox=function(){if("none"==this.node.style.display){this.show();var t=!0}var e,n=!1;this.paper.canvas.parentElement?e=this.paper.canvas.parentElement.style:this.paper.canvas.parentNode&&(e=this.paper.canvas.parentNode.style),e&&"none"==e.display&&(n=!0,e.display="");var r={};try{r=this.node.getBBox()}catch(i){r={x:this.node.clientLeft,y:this.node.clientTop,width:this.node.clientWidth,height:this.node.clientHeight}}finally{r=r||{},n&&(e.display="none")}return t&&this.hide(),r},k.attr=function(n,r){if(this.removed)return this;if(null==n){var i={};for(var o in this.attrs)this.attrs[e](o)&&(i[o]=this.attrs[o]);return i.gradient&&"none"==i.fill&&(i.fill=i.gradient)&&delete i.gradient,i.transform=this._.transform,i}if(null==r&&t.is(n,"string")){if("fill"==n&&"none"==this.attrs.fill&&this.attrs.gradient)return this.attrs.gradient;if("transform"==n)return this._.transform;for(var a=n.split(u),s={},l=0,h=a.length;l<h;l++)s[n=a[l]]=n in this.attrs?this.attrs[n]:t.is(this.paper.customAttributes[n],"function")?this.paper.customAttributes[n].def:t._availableAttrs[n];return h-1?s:s[a[0]]}if(null==r&&t.is(n,"array")){for(s={},l=0,h=n.length;l<h;l++)s[n[l]]=this.attr(n[l]);return s}if(null!=r){var f={};f[n]=r}else null!=n&&t.is(n,"object")&&(f=n);for(var d in f)c("raphael.attr."+d+"."+this.id,this,f[d]);for(d in this.paper.customAttributes)if(this.paper.customAttributes[e](d)&&f[e](d)&&t.is(this.paper.customAttributes[d],"function")){var p=this.paper.customAttributes[d].apply(this,[].concat(f[d]));for(var g in this.attrs[d]=f[d],p)p[e](g)&&(f[g]=p[g])}return S(this,f),this},k.toFront=function(){if(this.removed)return this;var e=T(this.node);e.parentNode.appendChild(e);var n=this.paper;return n.top!=this&&t._tofront(this,n),this},k.toBack=function(){if(this.removed)return this;var e=T(this.node),n=e.parentNode;return n.insertBefore(e,n.firstChild),t._toback(this,this.paper),this},k.insertAfter=function(e){if(this.removed||!e)return this;var n=T(this.node),r=T(e.node||e[e.length-1].node);return r.nextSibling?r.parentNode.insertBefore(n,r.nextSibling):r.parentNode.appendChild(n),t._insertafter(this,e,this.paper),this},k.insertBefore=function(e){if(this.removed||!e)return this;var n=T(this.node),r=T(e.node||e[0].node);return r.parentNode.insertBefore(n,r),t._insertbefore(this,e,this.paper),this},k.blur=function(e){var n=this;if(0!=+e){var r=m("filter"),i=m("feGaussianBlur");n.attrs.blur=e,r.id=t.createUUID(),m(i,{stdDeviation:+e||1.5}),r.appendChild(i),n.paper.defs.appendChild(r),n._blur=r,m(n.node,{filter:"url(#"+r.id+")"})}else n._blur&&(n._blur.parentNode.removeChild(n._blur),delete n._blur,delete n.attrs.blur),n.node.removeAttribute("filter");return n},t._engine.circle=function(t,e,n,r){var i=m("circle");t.canvas&&t.canvas.appendChild(i);var o=new D(i,t);return o.attrs={cx:e,cy:n,r:r,fill:"none",stroke:"#000"},o.type="circle",m(i,o.attrs),o},t._engine.rect=function(t,e,n,r,i,o){var a=m("rect");t.canvas&&t.canvas.appendChild(a);var s=new D(a,t);return s.attrs={x:e,y:n,width:r,height:i,rx:o||0,ry:o||0,fill:"none",stroke:"#000"},s.type="rect",m(a,s.attrs),s},t._engine.ellipse=function(t,e,n,r,i){var o=m("ellipse");t.canvas&&t.canvas.appendChild(o);var a=new D(o,t);return a.attrs={cx:e,cy:n,rx:r,ry:i,fill:"none",stroke:"#000"},a.type="ellipse",m(o,a.attrs),a},t._engine.image=function(t,e,n,r,i,o){var a=m("image");m(a,{x:n,y:r,width:i,height:o,preserveAspectRatio:"none"}),a.setAttributeNS(d,"href",e),t.canvas&&t.canvas.appendChild(a);var s=new D(a,t);return s.attrs={x:n,y:r,width:i,height:o,src:e},s.type="image",s},t._engine.text=function(e,n,r,i){var o=m("text");e.canvas&&e.canvas.appendChild(o);var a=new D(o,e);return a.attrs={x:n,y:r,"text-anchor":"middle",text:i,"font-family":t._availableAttrs["font-family"],"font-size":t._availableAttrs["font-size"],stroke:"none",fill:"#000"},a.type="text",S(a,a.attrs),a},t._engine.setSize=function(t,e){return this.width=t||this.width,this.height=e||this.height,this.canvas.setAttribute("width",this.width),this.canvas.setAttribute("height",this.height),this._viewBox&&this.setViewBox.apply(this,this._viewBox),this},t._engine.create=function(){var e=t._getContainer.apply(0,arguments),n=e&&e.container;if(!n)throw new Error("SVG container not found.");var r,i=e.x,o=e.y,a=e.width,s=e.height,l=m("svg"),u="overflow:hidden;";return i=i||0,o=o||0,m(l,{height:s=s||342,version:1.1,width:a=a||512,xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"}),1==n?(l.style.cssText=u+"position:absolute;left:"+i+"px;top:"+o+"px",t._g.doc.body.appendChild(l),r=1):(l.style.cssText=u+"position:relative",n.firstChild?n.insertBefore(l,n.firstChild):n.appendChild(l)),(n=new t._Paper).width=a,n.height=s,n.canvas=l,n.clear(),n._left=n._top=0,r&&(n.renderfix=function(){}),n.renderfix(),n},t._engine.setViewBox=function(t,e,n,r,i){c("raphael.setViewBox",this,this._viewBox,[t,e,n,r,i]);var o,s=this.getSize(),l=a(n/s.width,r/s.height),u=this.top,h=i?"xMidYMid meet":"xMinYMin";for(null==t?(this._vbSize&&(l=1),delete this._vbSize,o="0 0 "+this.width+f+this.height):(this._vbSize=l,o=t+f+e+f+n+f+r),m(this.canvas,{viewBox:o,preserveAspectRatio:h});l&&u;)u.attr({"stroke-width":"stroke-width"in u.attrs?u.attrs["stroke-width"]:1}),u._.dirty=1,u._.dirtyT=1,u=u.prev;return this._viewBox=[t,e,n,r,!!i],this},t.prototype.renderfix=function(){var t,e=this.canvas,n=e.style;try{t=e.getScreenCTM()||e.createSVGMatrix()}catch(o){t=e.createSVGMatrix()}var r=-t.e%1,i=-t.f%1;(r||i)&&(r&&(this._left=(this._left+r)%1,n.left=this._left+"px"),i&&(this._top=(this._top+i)%1,n.top=this._top+"px"))},t.prototype.clear=function(){t.eve("raphael.clear",this);for(var e=this.canvas;e.firstChild;)e.removeChild(e.firstChild);this.bottom=this.top=null,(this.desc=m("desc")).appendChild(t._g.doc.createTextNode("Created with Raphaël "+t.version)),e.appendChild(this.desc),e.appendChild(this.defs=m("defs"))},t.prototype.remove=function(){for(var e in c("raphael.remove",this),this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this)this[e]="function"==typeof this[e]?t._removedFactory(e):null};var E=t.st;for(var A in k)k[e](A)&&!E[e](A)&&(E[A]=function(t){return function(){var e=arguments;return this.forEach(function(n){n[t].apply(n,e)})}}(A))}}).apply(e,r))||(t.exports=i)},"./dev/raphael.vml.js":function(t,e,n){var r,i;r=[n("./dev/raphael.core.js")],void 0===(i=(function(t){if(!t||t.vml){var e="hasOwnProperty",n=String,r=parseFloat,i=Math,o=i.round,a=i.max,s=i.min,l=i.abs,u="fill",c=/[, ]+/,h=t.eve,f=" ",d="",p={M:"m",L:"l",C:"c",Z:"x",m:"t",l:"r",c:"v",z:"x"},g=/([clmz]),?([^clmz]*)/gi,m=/ progid:\S+Blur\([^\)]+\)/g,v=/-?[^,\s-]+/g,y="position:absolute;left:0;top:0;width:1px;height:1px;behavior:url(#default#VML)",b=21600,x={path:1,rect:1,image:1},_={circle:1,ellipse:1},w=function(e,n,r){var i=t.matrix();return i.rotate(-e,.5,.5),{dx:i.x(n,r),dy:i.y(n,r)}},S=function(t,e,n,r,i,o){var a=t._,s=t.matrix,c=a.fillpos,h=t.node,d=h.style,p=1,g="",m=b/e,v=b/n;if(d.visibility="hidden",e&&n){if(h.coordsize=l(m)+f+l(v),d.rotation=o*(e*n<0?-1:1),o){var y=w(o,r,i);r=y.dx,i=y.dy}if(e<0&&(g+="x"),n<0&&(g+=" y")&&(p=-1),d.flip=g,h.coordorigin=r*-m+f+i*-v,c||a.fillsize){var x=h.getElementsByTagName(u);h.removeChild(x=x&&x[0]),c&&(y=w(o,s.x(c[0],c[1]),s.y(c[0],c[1])),x.position=y.dx*p+f+y.dy*p),a.fillsize&&(x.size=a.fillsize[0]*l(e)+f+a.fillsize[1]*l(n)),h.appendChild(x)}d.visibility="visible"}};t.toString=function(){return"Your browser doesn’t support SVG. Falling down to VML.\nYou are running Raphaël "+this.version};var C,T=function(t,e,r){for(var i=n(e).toLowerCase().split("-"),o=r?"end":"start",a=i.length,s="classic",l="medium",u="medium";a--;)switch(i[a]){case"block":case"classic":case"oval":case"diamond":case"open":case"none":s=i[a];break;case"wide":case"narrow":u=i[a];break;case"long":case"short":l=i[a]}var c=t.node.getElementsByTagName("stroke")[0];c[o+"arrow"]=s,c[o+"arrowlength"]=l,c[o+"arrowwidth"]=u},D=function(i,l){i.attrs=i.attrs||{};var h=i.node,m=i.attrs,y=h.style,w=x[i.type]&&(l.x!=m.x||l.y!=m.y||l.width!=m.width||l.height!=m.height||l.cx!=m.cx||l.cy!=m.cy||l.rx!=m.rx||l.ry!=m.ry||l.r!=m.r),D=_[i.type]&&(m.cx!=l.cx||m.cy!=l.cy||m.r!=l.r||m.rx!=l.rx||m.ry!=l.ry),E=i;for(var A in l)l[e](A)&&(m[A]=l[A]);if(w&&(m.path=t._getPath[i.type](i),i._.dirty=1),l.href&&(h.href=l.href),l.title&&(h.title=l.title),l.target&&(h.target=l.target),l.cursor&&(y.cursor=l.cursor),"blur"in l&&i.blur(l.blur),(l.path&&"path"==i.type||w)&&(h.path=function(e){var r=/[ahqstv]/gi,i=t._pathToAbsolute;if(n(e).match(r)&&(i=t._path2curve),r=/[clmz]/g,i==t._pathToAbsolute&&!n(e).match(r)){var a=n(e).replace(g,function(t,e,n){var r=[],i="m"==e.toLowerCase(),a=p[e];return n.replace(v,function(t){i&&2==r.length&&(a+=r+p["m"==e?"l":"L"],r=[]),r.push(o(t*b))}),a+r});return a}var s,l,u=i(e);a=[];for(var c=0,h=u.length;c<h;c++){s=u[c],"z"==(l=u[c][0].toLowerCase())&&(l="x");for(var m=1,y=s.length;m<y;m++)l+=o(s[m]*b)+(m!=y-1?",":d);a.push(l)}return a.join(f)}(~n(m.path).toLowerCase().indexOf("r")?t._pathToAbsolute(m.path):m.path),i._.dirty=1,"image"==i.type&&(i._.fillpos=[m.x,m.y],i._.fillsize=[m.width,m.height],S(i,1,1,0,0,0))),"transform"in l&&i.transform(l.transform),D){var L=+m.cx,I=+m.cy,N=+m.rx||+m.r||0,F=+m.ry||+m.r||0;h.path=t.format("ar{0},{1},{2},{3},{4},{1},{4},{1}x",o((L-N)*b),o((I-F)*b),o((L+N)*b),o((I+F)*b),o(L*b)),i._.dirty=1}if("clip-rect"in l){var j=n(l["clip-rect"]).split(c);if(4==j.length){j[2]=+j[2]+ +j[0],j[3]=+j[3]+ +j[1];var P=h.clipRect||t._g.doc.createElement("div"),M=P.style;M.clip=t.format("rect({1}px {2}px {3}px {0}px)",j),h.clipRect||(M.position="absolute",M.top=0,M.left=0,M.width=i.paper.width+"px",M.height=i.paper.height+"px",h.parentNode.insertBefore(P,h),P.appendChild(h),h.clipRect=P)}l["clip-rect"]||h.clipRect&&(h.clipRect.style.clip="auto")}if(i.textpath){var R=i.textpath.style;l.font&&(R.font=l.font),l["font-family"]&&(R.fontFamily='"'+l["font-family"].split(",")[0].replace(/^['"]+|['"]+$/g,d)+'"'),l["font-size"]&&(R.fontSize=l["font-size"]),l["font-weight"]&&(R.fontWeight=l["font-weight"]),l["font-style"]&&(R.fontStyle=l["font-style"])}if("arrow-start"in l&&T(E,l["arrow-start"]),"arrow-end"in l&&T(E,l["arrow-end"],1),null!=l.opacity||null!=l.fill||null!=l.src||null!=l.stroke||null!=l["stroke-width"]||null!=l["stroke-opacity"]||null!=l["fill-opacity"]||null!=l["stroke-dasharray"]||null!=l["stroke-miterlimit"]||null!=l["stroke-linejoin"]||null!=l["stroke-linecap"]){var O=h.getElementsByTagName(u);if(!(O=O&&O[0])&&(O=C(u)),"image"==i.type&&l.src&&(O.src=l.src),l.fill&&(O.on=!0),null!=O.on&&"none"!=l.fill&&null!==l.fill||(O.on=!1),O.on&&l.fill){var B=n(l.fill).match(t._ISURL);if(B){O.parentNode==h&&h.removeChild(O),O.rotate=!0,O.src=B[1],O.type="tile";var H=i.getBBox(1);O.position=H.x+f+H.y,i._.fillpos=[H.x,H.y],t._preload(B[1],function(){i._.fillsize=[this.offsetWidth,this.offsetHeight]})}else O.color=t.getRGB(l.fill).hex,O.src=d,O.type="solid",t.getRGB(l.fill).error&&(E.type in{circle:1,ellipse:1}||"r"!=n(l.fill).charAt())&&k(E,l.fill,O)&&(m.fill="none",m.gradient=l.fill,O.rotate=!1)}if("fill-opacity"in l||"opacity"in l){var q=((+m["fill-opacity"]+1||2)-1)*((+m.opacity+1||2)-1)*((+t.getRGB(l.fill).o+1||2)-1);q=s(a(q,0),1),O.opacity=q,O.src&&(O.color="none")}h.appendChild(O);var W=h.getElementsByTagName("stroke")&&h.getElementsByTagName("stroke")[0],z=!1;!W&&(z=W=C("stroke")),(l.stroke&&"none"!=l.stroke||l["stroke-width"]||null!=l["stroke-opacity"]||l["stroke-dasharray"]||l["stroke-miterlimit"]||l["stroke-linejoin"]||l["stroke-linecap"])&&(W.on=!0),("none"==l.stroke||null===l.stroke||null==W.on||0==l.stroke||0==l["stroke-width"])&&(W.on=!1);var U=t.getRGB(l.stroke);W.on&&l.stroke&&(W.color=U.hex),q=((+m["stroke-opacity"]+1||2)-1)*((+m.opacity+1||2)-1)*((+U.o+1||2)-1);var V=.75*(r(l["stroke-width"])||1);if(q=s(a(q,0),1),null==l["stroke-width"]&&(V=m["stroke-width"]),l["stroke-width"]&&(W.weight=V),V&&V<1&&(q*=V)&&(W.weight=1),W.opacity=q,l["stroke-linejoin"]&&(W.joinstyle=l["stroke-linejoin"]||"miter"),W.miterlimit=l["stroke-miterlimit"]||8,l["stroke-linecap"]&&(W.endcap="butt"==l["stroke-linecap"]?"flat":"square"==l["stroke-linecap"]?"square":"round"),"stroke-dasharray"in l){var X={"-":"shortdash",".":"shortdot","-.":"shortdashdot","-..":"shortdashdotdot",". ":"dot","- ":"dash","--":"longdash","- .":"dashdot","--.":"longdashdot","--..":"longdashdotdot"};W.dashstyle=X[e](l["stroke-dasharray"])?X[l["stroke-dasharray"]]:d}z&&h.appendChild(W)}if("text"==E.type){E.paper.canvas.style.display=d;var Y=E.paper.span,G=m.font&&m.font.match(/\d+(?:\.\d*)?(?=px)/);y=Y.style,m.font&&(y.font=m.font),m["font-family"]&&(y.fontFamily=m["font-family"]),m["font-weight"]&&(y.fontWeight=m["font-weight"]),m["font-style"]&&(y.fontStyle=m["font-style"]),G=r(m["font-size"]||G&&G[0])||10,y.fontSize=100*G+"px",E.textpath.string&&(Y.innerHTML=n(E.textpath.string).replace(/</g,"&#60;").replace(/&/g,"&#38;").replace(/\n/g,"<br>"));var $=Y.getBoundingClientRect();E.W=m.w=($.right-$.left)/100,E.H=m.h=($.bottom-$.top)/100,E.X=m.x,E.Y=m.y+E.H/2,("x"in l||"y"in l)&&(E.path.v=t.format("m{0},{1}l{2},{1}",o(m.x*b),o(m.y*b),o(m.x*b)+1));for(var Q=["x","y","text","font","font-family","font-weight","font-style","font-size"],J=0,K=Q.length;J<K;J++)if(Q[J]in l){E._.dirty=1;break}switch(m["text-anchor"]){case"start":E.textpath.style["v-text-align"]="left",E.bbx=E.W/2;break;case"end":E.textpath.style["v-text-align"]="right",E.bbx=-E.W/2;break;default:E.textpath.style["v-text-align"]="center",E.bbx=0}E.textpath.style["v-text-kern"]=!0}},k=function(e,o,a){e.attrs=e.attrs||{};var s=Math.pow,l="linear",u=".5 .5";if(e.attrs.gradient=o,o=(o=n(o).replace(t._radial_gradient,function(t,e,n){return l="radial",e&&n&&(e=r(e),n=r(n),s(e-.5,2)+s(n-.5,2)>.25&&(n=i.sqrt(.25-s(e-.5,2))*(2*(n>.5)-1)+.5),u=e+f+n),d})).split(/\s*\-\s*/),"linear"==l){var c=o.shift();if(c=-r(c),isNaN(c))return null}var h=t._parseDots(o);if(!h)return null;if(e=e.shape||e.node,h.length){e.removeChild(a),a.on=!0,a.method="none",a.color=h[0].color,a.color2=h[h.length-1].color;for(var p=[],g=0,m=h.length;g<m;g++)h[g].offset&&p.push(h[g].offset+f+h[g].color);a.colors=p.length?p.join():"0% "+a.color,"radial"==l?(a.type="gradientTitle",a.focus="100%",a.focussize="0 0",a.focusposition=u,a.angle=0):(a.type="gradient",a.angle=(270-c)%360),e.appendChild(a)}return 1},E=function(e,n){this[0]=this.node=e,e.raphael=!0,this.id=t._oid++,e.raphaelid=this.id,this.X=0,this.Y=0,this.attrs={},this.paper=n,this.matrix=t.matrix(),this._={transform:[],sx:1,sy:1,dx:0,dy:0,deg:0,dirty:1,dirtyT:1},!n.bottom&&(n.bottom=this),this.prev=n.top,n.top&&(n.top.next=this),n.top=this,this.next=null},A=t.el;E.prototype=A,A.constructor=E,A.transform=function(e){if(null==e)return this._.transform;var r,i=this.paper._viewBoxShift,o=i?"s"+[i.scale,i.scale]+"-1-1t"+[i.dx,i.dy]:d;i&&(r=e=n(e).replace(/\.{3}|\u2026/g,this._.transform||d)),t._extractTransform(this,o+e);var a,s=this.matrix.clone(),l=this.skew,u=this.node,c=~n(this.attrs.fill).indexOf("-"),h=!n(this.attrs.fill).indexOf("url(");if(s.translate(1,1),h||c||"image"==this.type)if(l.matrix="1 0 0 1",l.offset="0 0",a=s.split(),c&&a.noRotation||!a.isSimple){u.style.filter=s.toFilter();var p=this.getBBox(),g=this.getBBox(1),m=p.x-g.x,v=p.y-g.y;u.coordorigin=m*-b+f+v*-b,S(this,1,1,m,v,0)}else u.style.filter=d,S(this,a.scalex,a.scaley,a.dx,a.dy,a.rotate);else u.style.filter=d,l.matrix=n(s),l.offset=s.offset();return null!==r&&(this._.transform=r,t._extractTransform(this,r)),this},A.rotate=function(t,e,i){if(this.removed)return this;if(null!=t){if((t=n(t).split(c)).length-1&&(e=r(t[1]),i=r(t[2])),t=r(t[0]),null==i&&(e=i),null==e||null==i){var o=this.getBBox(1);e=o.x+o.width/2,i=o.y+o.height/2}return this._.dirtyT=1,this.transform(this._.transform.concat([["r",t,e,i]])),this}},A.translate=function(t,e){return this.removed||((t=n(t).split(c)).length-1&&(e=r(t[1])),t=r(t[0])||0,e=+e||0,this._.bbox&&(this._.bbox.x+=t,this._.bbox.y+=e),this.transform(this._.transform.concat([["t",t,e]]))),this},A.scale=function(t,e,i,o){if(this.removed)return this;if((t=n(t).split(c)).length-1&&(e=r(t[1]),i=r(t[2]),o=r(t[3]),isNaN(i)&&(i=null),isNaN(o)&&(o=null)),t=r(t[0]),null==e&&(e=t),null==o&&(i=o),null==i||null==o)var a=this.getBBox(1);return this.transform(this._.transform.concat([["s",t,e,i=null==i?a.x+a.width/2:i,o=null==o?a.y+a.height/2:o]])),this._.dirtyT=1,this},A.hide=function(){return!this.removed&&(this.node.style.display="none"),this},A.show=function(){return!this.removed&&(this.node.style.display=d),this},A.auxGetBBox=t.el.getBBox,A.getBBox=function(){var t=this.auxGetBBox();if(this.paper&&this.paper._viewBoxShift){var e={},n=1/this.paper._viewBoxShift.scale;return e.x=t.x-this.paper._viewBoxShift.dx,e.x*=n,e.y=t.y-this.paper._viewBoxShift.dy,e.y*=n,e.width=t.width*n,e.height=t.height*n,e.x2=e.x+e.width,e.y2=e.y+e.height,e}return t},A._getBBox=function(){return this.removed?{}:{x:this.X+(this.bbx||0)-this.W/2,y:this.Y-this.H,width:this.W,height:this.H}},A.remove=function(){if(!this.removed&&this.node.parentNode){for(var e in this.paper.__set__&&this.paper.__set__.exclude(this),t.eve.unbind("raphael.*.*."+this.id),t._tear(this,this.paper),this.node.parentNode.removeChild(this.node),this.shape&&this.shape.parentNode.removeChild(this.shape),this)this[e]="function"==typeof this[e]?t._removedFactory(e):null;this.removed=!0}},A.attr=function(n,r){if(this.removed)return this;if(null==n){var i={};for(var o in this.attrs)this.attrs[e](o)&&(i[o]=this.attrs[o]);return i.gradient&&"none"==i.fill&&(i.fill=i.gradient)&&delete i.gradient,i.transform=this._.transform,i}if(null==r&&t.is(n,"string")){if(n==u&&"none"==this.attrs.fill&&this.attrs.gradient)return this.attrs.gradient;for(var a=n.split(c),s={},l=0,f=a.length;l<f;l++)s[n=a[l]]=n in this.attrs?this.attrs[n]:t.is(this.paper.customAttributes[n],"function")?this.paper.customAttributes[n].def:t._availableAttrs[n];return f-1?s:s[a[0]]}if(this.attrs&&null==r&&t.is(n,"array")){for(s={},l=0,f=n.length;l<f;l++)s[n[l]]=this.attr(n[l]);return s}var d;for(var p in null!=r&&((d={})[n]=r),null==r&&t.is(n,"object")&&(d=n),d)h("raphael.attr."+p+"."+this.id,this,d[p]);if(d){for(p in this.paper.customAttributes)if(this.paper.customAttributes[e](p)&&d[e](p)&&t.is(this.paper.customAttributes[p],"function")){var g=this.paper.customAttributes[p].apply(this,[].concat(d[p]));for(var m in this.attrs[p]=d[p],g)g[e](m)&&(d[m]=g[m])}d.text&&"text"==this.type&&(this.textpath.string=d.text),D(this,d)}return this},A.toFront=function(){return!this.removed&&this.node.parentNode.appendChild(this.node),this.paper&&this.paper.top!=this&&t._tofront(this,this.paper),this},A.toBack=function(){return this.removed||this.node.parentNode.firstChild!=this.node&&(this.node.parentNode.insertBefore(this.node,this.node.parentNode.firstChild),t._toback(this,this.paper)),this},A.insertAfter=function(e){return this.removed||(e.constructor==t.st.constructor&&(e=e[e.length-1]),e.node.nextSibling?e.node.parentNode.insertBefore(this.node,e.node.nextSibling):e.node.parentNode.appendChild(this.node),t._insertafter(this,e,this.paper)),this},A.insertBefore=function(e){return this.removed||(e.constructor==t.st.constructor&&(e=e[0]),e.node.parentNode.insertBefore(this.node,e.node),t._insertbefore(this,e,this.paper)),this},A.blur=function(e){var n=this.node.runtimeStyle,r=n.filter;return r=r.replace(m,d),0!=+e?(this.attrs.blur=e,n.filter=r+f+" progid:DXImageTransform.Microsoft.Blur(pixelradius="+(+e||1.5)+")",n.margin=t.format("-{0}px 0 0 -{0}px",o(+e||1.5))):(n.filter=r,n.margin=0,delete this.attrs.blur),this},t._engine.path=function(t,e){var n=C("shape");n.style.cssText=y,n.coordsize=b+f+b,n.coordorigin=e.coordorigin;var r=new E(n,e),i={fill:"none",stroke:"#000"};t&&(i.path=t),r.type="path",r.path=[],r.Path=d,D(r,i),e.canvas&&e.canvas.appendChild(n);var o=C("skew");return o.on=!0,n.appendChild(o),r.skew=o,r.transform(d),r},t._engine.rect=function(e,n,r,i,o,a){var s=t._rectPath(n,r,i,o,a),l=e.path(s),u=l.attrs;return l.X=u.x=n,l.Y=u.y=r,l.W=u.width=i,l.H=u.height=o,u.r=a,u.path=s,l.type="rect",l},t._engine.ellipse=function(t,e,n,r,i){var o=t.path();return o.X=e-r,o.Y=n-i,o.W=2*r,o.H=2*i,o.type="ellipse",D(o,{cx:e,cy:n,rx:r,ry:i}),o},t._engine.circle=function(t,e,n,r){var i=t.path();return i.X=e-r,i.Y=n-r,i.W=i.H=2*r,i.type="circle",D(i,{cx:e,cy:n,r:r}),i},t._engine.image=function(e,n,r,i,o,a){var s=t._rectPath(r,i,o,a),l=e.path(s).attr({stroke:"none"}),c=l.attrs,h=l.node,f=h.getElementsByTagName(u)[0];return c.src=n,l.X=c.x=r,l.Y=c.y=i,l.W=c.width=o,l.H=c.height=a,c.path=s,l.type="image",f.parentNode==h&&h.removeChild(f),f.rotate=!0,f.src=n,f.type="tile",l._.fillpos=[r,i],l._.fillsize=[o,a],h.appendChild(f),S(l,1,1,0,0,0),l},t._engine.text=function(e,r,i,a){var s=C("shape"),l=C("path"),u=C("textpath");i=i||0,a=a||"",l.v=t.format("m{0},{1}l{2},{1}",o((r=r||0)*b),o(i*b),o(r*b)+1),l.textpathok=!0,u.string=n(a),u.on=!0,s.style.cssText=y,s.coordsize=b+f+b,s.coordorigin="0 0";var c=new E(s,e),h={fill:"#000",stroke:"none",font:t._availableAttrs.font,text:a};c.shape=s,c.path=l,c.textpath=u,c.type="text",c.attrs.text=n(a),c.attrs.x=r,c.attrs.y=i,c.attrs.w=1,c.attrs.h=1,D(c,h),s.appendChild(u),s.appendChild(l),e.canvas.appendChild(s);var p=C("skew");return p.on=!0,s.appendChild(p),c.skew=p,c.transform(d),c},t._engine.setSize=function(e,n){var r=this.canvas.style;return this.width=e,this.height=n,e==+e&&(e+="px"),n==+n&&(n+="px"),r.width=e,r.height=n,r.clip="rect(0 "+e+" "+n+" 0)",this._viewBox&&t._engine.setViewBox.apply(this,this._viewBox),this},t._engine.setViewBox=function(e,n,r,i,o){t.eve("raphael.setViewBox",this,this._viewBox,[e,n,r,i,o]);var a,s,l=this.getSize(),u=l.width,c=l.height;return o&&(r*(a=c/i)<u&&(e-=(u-r*a)/2/a),i*(s=u/r)<c&&(n-=(c-i*s)/2/s)),this._viewBox=[e,n,r,i,!!o],this._viewBoxShift={dx:-e,dy:-n,scale:l},this.forEach(function(t){t.transform("...")}),this},t._engine.initWin=function(t){var e=t.document;e.styleSheets.length<31?e.createStyleSheet().addRule(".rvml","behavior:url(#default#VML)"):e.styleSheets[0].addRule(".rvml","behavior:url(#default#VML)");try{!e.namespaces.rvml&&e.namespaces.add("rvml","urn:schemas-microsoft-com:vml"),C=function(t){return e.createElement("<rvml:"+t+' class="rvml">')}}catch(n){C=function(t){return e.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="rvml">')}}},t._engine.initWin(t._g.win),t._engine.create=function(){var e=t._getContainer.apply(0,arguments),n=e.container,r=e.height,i=e.width,o=e.x,a=e.y;if(!n)throw new Error("VML container not found.");var s=new t._Paper,l=s.canvas=t._g.doc.createElement("div"),u=l.style;return o=o||0,a=a||0,r=r||342,s.width=i=i||512,s.height=r,i==+i&&(i+="px"),r==+r&&(r+="px"),s.coordsize="21600000 21600000",s.coordorigin="0 0",s.span=t._g.doc.createElement("span"),s.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;",l.appendChild(s.span),u.cssText=t.format("top:0;left:0;width:{0};height:{1};display:inline-block;position:relative;clip:rect(0 {0} {1} 0);overflow:hidden",i,r),1==n?(t._g.doc.body.appendChild(l),u.left=o+"px",u.top=a+"px",u.position="absolute"):n.firstChild?n.insertBefore(l,n.firstChild):n.appendChild(l),s.renderfix=function(){},s},t.prototype.clear=function(){t.eve("raphael.clear",this),this.canvas.innerHTML=d,this.span=t._g.doc.createElement("span"),this.span.style.cssText="position:absolute;left:-9999em;top:-9999em;padding:0;margin:0;line-height:1;display:inline;",this.canvas.appendChild(this.span),this.bottom=this.top=null},t.prototype.remove=function(){for(var e in t.eve("raphael.remove",this),this.canvas.parentNode.removeChild(this.canvas),this)this[e]="function"==typeof this[e]?t._removedFactory(e):null;return!0};var L=t.st;for(var I in A)A[e](I)&&!L[e](I)&&(L[I]=function(t){return function(){var e=arguments;return this.forEach(function(n){n[t].apply(n,e)})}}(I))}}).apply(e,r))||(t.exports=i)},"./node_modules/eve-raphael/eve.js":function(t,e,n){var r,i,o,a,s,l,u,c,h,f,d,p,g,m;a="hasOwnProperty",s=/[\.\/]/,l=/\s*,\s*/,u=function(t,e){return t-e},c={n:{}},h=function(){for(var t=0,e=this.length;t<e;t++)if(void 0!==this[t])return this[t]},f=function(){for(var t=this.length;--t;)if(void 0!==this[t])return this[t]},d=Object.prototype.toString,p=String,g=Array.isArray||function(t){return t instanceof Array||"[object Array]"==d.call(t)},(m=function(t,e){var n,r=o,a=Array.prototype.slice.call(arguments,2),s=m.listeners(t),l=0,c=[],d={},p=[],g=i;p.firstDefined=h,p.lastDefined=f,i=t,o=0;for(var v=0,y=s.length;v<y;v++)"zIndex"in s[v]&&(c.push(s[v].zIndex),s[v].zIndex<0&&(d[s[v].zIndex]=s[v]));for(c.sort(u);c[l]<0;)if(n=d[c[l++]],p.push(n.apply(e,a)),o)return o=r,p;for(v=0;v<y;v++)if("zIndex"in(n=s[v]))if(n.zIndex==c[l]){if(p.push(n.apply(e,a)),o)break;do{if((n=d[c[++l]])&&p.push(n.apply(e,a)),o)break}while(n)}else d[n.zIndex]=n;else if(p.push(n.apply(e,a)),o)break;return o=r,i=g,p})._events=c,m.listeners=function(t){var e,n,r,i,o,a,l,u,h=g(t)?t:t.split(s),f=c,d=[f],p=[];for(i=0,o=h.length;i<o;i++){for(u=[],a=0,l=d.length;a<l;a++)for(n=[(f=d[a].n)[h[i]],f["*"]],r=2;r--;)(e=n[r])&&(u.push(e),p=p.concat(e.f||[]));d=u}return p},m.separator=function(t){t?(t="["+(t=p(t).replace(/(?=[\.\^\]\[\-])/g,"\\"))+"]",s=new RegExp(t)):s=/[\.\/]/},m.on=function(t,e){if("function"!=typeof e)return function(){};for(var n=g(t)?g(t[0])?t:[t]:p(t).split(l),r=0,i=n.length;r<i;r++)!function(t){for(var n,r=g(t)?t:p(t).split(s),i=c,o=0,a=r.length;o<a;o++)i=(i=i.n).hasOwnProperty(r[o])&&i[r[o]]||(i[r[o]]={n:{}});for(i.f=i.f||[],o=0,a=i.f.length;o<a;o++)if(i.f[o]==e){n=!0;break}!n&&i.f.push(e)}(n[r]);return function(t){+t==+t&&(e.zIndex=+t)}},m.f=function(t){var e=[].slice.call(arguments,1);return function(){m.apply(null,[t,null].concat(e).concat([].slice.call(arguments,0)))}},m.stop=function(){o=1},m.nt=function(t){var e=g(i)?i.join("."):i;return t?new RegExp("(?:\\.|\\/|^)"+t+"(?:\\.|\\/|$)").test(e):e},m.nts=function(){return g(i)?i:i.split(s)},m.off=m.unbind=function(t,e){if(t){var n=g(t)?g(t[0])?t:[t]:p(t).split(l);if(n.length>1)for(var r=0,i=n.length;r<i;r++)m.off(n[r],e);else{n=g(t)?t:p(t).split(s);var o,u,h,f,d,v=[c];for(r=0,i=n.length;r<i;r++)for(f=0;f<v.length;f+=h.length-2){if(h=[f,1],o=v[f].n,"*"!=n[r])o[n[r]]&&h.push(o[n[r]]);else for(u in o)o[a](u)&&h.push(o[u]);v.splice.apply(v,h)}for(r=0,i=v.length;r<i;r++)for(o=v[r];o.n;){if(e){if(o.f){for(f=0,d=o.f.length;f<d;f++)if(o.f[f]==e){o.f.splice(f,1);break}!o.f.length&&delete o.f}for(u in o.n)if(o.n[a](u)&&o.n[u].f){var y=o.n[u].f;for(f=0,d=y.length;f<d;f++)if(y[f]==e){y.splice(f,1);break}!y.length&&delete o.n[u].f}}else for(u in delete o.f,o.n)o.n[a](u)&&o.n[u].f&&delete o.n[u].f;o=o.n}}}else m._events=c={n:{}}},m.once=function(t,e){var n=function(){return m.off(t,n),e.apply(this,arguments)};return m.on(t,n)},m.version="0.5.0",m.toString=function(){return"You are running Eve 0.5.0"},t.exports?t.exports=m:void 0===(r=(function(){return m}).apply(e,[]))||(t.exports=r)}})}),(function(){var t,e,n,r,i=[].slice,o=function(t,e){return function(){return t.apply(e,arguments)}},a={}.hasOwnProperty,s=function(t,e){for(var n in e)a.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t},l=[].indexOf||function(t){for(var e=0,n=this.length;e<n;e++)if(e in this&&this[e]===t)return e;return-1};e=window.Morris={},t=jQuery,e.EventEmitter=function(){function t(){}return t.prototype.on=function(t,e){return null==this.handlers&&(this.handlers={}),null==this.handlers[t]&&(this.handlers[t]=[]),this.handlers[t].push(e),this},t.prototype.fire=function(){var t,e,n,r,o,a;if(e=arguments[0],t=2<=arguments.length?i.call(arguments,1):[],null!=this.handlers&&null!=this.handlers[e]){for(a=[],n=0,r=(o=this.handlers[e]).length;n<r;n++)a.push(o[n].apply(null,t));return a}},t}(),e.commas=function(t){var e,n,r,i;return null!=t?(r=t<0?"-":"",e=Math.abs(t),r+=(n=Math.floor(e).toFixed(0)).replace(/(?=(?:\d{3})+$)(?!^)/g,","),(i=e.toString()).length>n.length&&(r+=i.slice(n.length)),r):"-"},e.pad2=function(t){return(t<10?"0":"")+t},e.Grid=function(n){function r(e){this.resizeHandler=o(this.resizeHandler,this);var n=this;if(this.el=t("string"==typeof e.element?document.getElementById(e.element):e.element),null==this.el||0===this.el.length)throw new Error("Graph container element not found");"static"===this.el.css("position")&&this.el.css("position","relative"),this.options=t.extend({},this.gridDefaults,this.defaults||{},e),"string"==typeof this.options.units&&(this.options.postUnits=e.units),this.raphael=new Raphael(this.el[0]),this.elementWidth=null,this.elementHeight=null,this.dirty=!1,this.selectFrom=null,this.init&&this.init(),this.setData(this.options.data),this.el.bind("mousemove",function(t){var e,r,i,o;return r=n.el.offset(),o=t.pageX-r.left,n.selectFrom?(e=n.data[n.hitTest(Math.min(o,n.selectFrom))]._x,i=n.data[n.hitTest(Math.max(o,n.selectFrom))]._x,n.selectionRect.attr({x:e,width:i-e})):n.fire("hovermove",o,t.pageY-r.top)}),this.el.bind("mouseleave",function(t){return n.selectFrom&&(n.selectionRect.hide(),n.selectFrom=null),n.fire("hoverout")}),this.el.bind("touchstart touchmove touchend",function(t){var e,r;return r=t.originalEvent.touches[0]||t.originalEvent.changedTouches[0],e=n.el.offset(),n.fire("hovermove",r.pageX-e.left,r.pageY-e.top)}),this.el.bind("click",function(t){var e;return e=n.el.offset(),n.fire("gridclick",t.pageX-e.left,t.pageY-e.top)}),this.options.rangeSelect&&(this.selectionRect=this.raphael.rect(0,0,0,this.el.innerHeight()).attr({fill:this.options.rangeSelectColor,stroke:!1}).toBack().hide(),this.el.bind("mousedown",function(t){var e;return e=n.el.offset(),n.startRange(t.pageX-e.left)}),this.el.bind("mouseup",function(t){var e;return e=n.el.offset(),n.endRange(t.pageX-e.left),n.fire("hovermove",t.pageX-e.left,t.pageY-e.top)})),this.options.resize&&t(window).bind("resize",function(t){return null!=n.timeoutId&&window.clearTimeout(n.timeoutId),n.timeoutId=window.setTimeout(n.resizeHandler,100)}),this.el.css("-webkit-tap-highlight-color","rgba(0,0,0,0)"),this.postInit&&this.postInit()}return s(r,n),r.prototype.gridDefaults={dateFormat:null,axes:!0,grid:!0,gridLineColor:"#aaa",gridStrokeWidth:.5,gridTextColor:"#888",gridTextSize:12,gridTextFamily:"sans-serif",gridTextWeight:"normal",hideHover:!1,yLabelFormat:null,xLabelAngle:0,numLines:5,padding:25,parseTime:!0,postUnits:"",preUnits:"",ymax:"auto",ymin:"auto 0",goals:[],goalStrokeWidth:1,goalLineColors:["#666633","#999966","#cc6666","#663333"],events:[],eventStrokeWidth:1,eventLineColors:["#005a04","#ccffbb","#3a5f0b","#005502"],rangeSelect:null,rangeSelectColor:"#eef",resize:!1},r.prototype.setData=function(t,n){var r,i,o,a,s,l,u,c,h,f,d,p,g;return null==n&&(n=!0),this.options.data=t,null==t||0===t.length?(this.data=[],this.raphael.clear(),void(null!=this.hover&&this.hover.hide())):(f=this.cumulative?0:null,d=this.cumulative?0:null,this.options.goals.length>0&&(a=Math.min.apply(Math,this.options.goals),o=Math.max.apply(Math,this.options.goals),d=null!=d?Math.min(d,a):a,f=null!=f?Math.max(f,o):o),this.data=(function(){var n,o,a;for(a=[],i=n=0,o=t.length;n<o;i=++n)(s={src:l=t[i]}).label=l[this.options.xkey],this.options.parseTime?(s.x=e.parseDate(s.label),this.options.dateFormat?s.label=this.options.dateFormat(s.x):"number"==typeof s.label&&(s.label=new Date(s.label).toString())):(s.x=i,this.options.xLabelFormat&&(s.label=this.options.xLabelFormat(s))),c=0,s.y=(function(){var t,e,n,i;for(i=[],r=t=0,e=(n=this.options.ykeys).length;t<e;r=++t)"string"==typeof(p=l[n[r]])&&(p=parseFloat(p)),null!=p&&"number"!=typeof p&&(p=null),null!=p&&(this.cumulative?c+=p:null!=f?(f=Math.max(p,f),d=Math.min(p,d)):f=d=p),this.cumulative&&null!=c&&(f=Math.max(c,f),d=Math.min(c,d)),i.push(p);return i}).call(this),a.push(s);return a}).call(this),this.options.parseTime&&(this.data=this.data.sort(function(t,e){return(t.x>e.x)-(e.x>t.x)})),this.xmin=this.data[0].x,this.xmax=this.data[this.data.length-1].x,this.events=[],this.options.events.length>0&&(this.events=this.options.parseTime?(function(){var t,n,r,i;for(i=[],t=0,n=(r=this.options.events).length;t<n;t++)i.push(e.parseDate(r[t]));return i}).call(this):this.options.events,this.xmax=Math.max(this.xmax,Math.max.apply(Math,this.events)),this.xmin=Math.min(this.xmin,Math.min.apply(Math,this.events))),this.xmin===this.xmax&&(this.xmin-=1,this.xmax+=1),this.ymin=this.yboundary("min",d),this.ymax=this.yboundary("max",f),this.ymin===this.ymax&&(d&&(this.ymin-=1),this.ymax+=1),!0!==(g=this.options.axes)&&"both"!==g&&"y"!==g&&!0!==this.options.grid||(this.options.ymax===this.gridDefaults.ymax&&this.options.ymin===this.gridDefaults.ymin?(this.grid=this.autoGridLines(this.ymin,this.ymax,this.options.numLines),this.ymin=Math.min(this.ymin,this.grid[0]),this.ymax=Math.max(this.ymax,this.grid[this.grid.length-1])):(u=(this.ymax-this.ymin)/(this.options.numLines-1),this.grid=(function(){var t,e,n;for(n=[],h=t=this.ymin,e=this.ymax;u>0?t<=e:t>=e;h=t+=u)n.push(h);return n}).call(this))),this.dirty=!0,n?this.redraw():void 0)},r.prototype.yboundary=function(t,e){var n,r;return"string"==typeof(n=this.options["y"+t])?"auto"===n.slice(0,4)?n.length>5?(r=parseInt(n.slice(5),10),null==e?r:Math[t](e,r)):null!=e?e:0:parseInt(n,10):n},r.prototype.autoGridLines=function(t,e,n){var r,i,o,a,s,l,u,c;return c=Math.floor(Math.log(e-t)/Math.log(10)),l=Math.pow(10,c),i=Math.floor(t/l)*l,r=Math.ceil(e/l)*l,s=(r-i)/(n-1),1===l&&s>1&&Math.ceil(s)!==s&&(s=Math.ceil(s),r=i+s*(n-1)),i<0&&r>0&&(i=Math.floor(t/s)*s,r=Math.ceil(e/s)*s),s<1?(a=Math.floor(Math.log(s)/Math.log(10)),o=function(){var t,e;for(e=[],u=t=i;s>0?t<=r:t>=r;u=t+=s)e.push(parseFloat(u.toFixed(1-a)));return e}()):o=function(){var t,e;for(e=[],u=t=i;s>0?t<=r:t>=r;u=t+=s)e.push(u);return e}(),o},r.prototype._calc=function(){var t,e,n,r,i,o,a;if(r=this.el.width(),e=this.el.height(),(this.elementWidth!==r||this.elementHeight!==e||this.dirty)&&(this.elementWidth=r,this.elementHeight=e,this.dirty=!1,this.left=this.options.padding,this.right=this.elementWidth-this.options.padding,this.top=this.options.padding,this.bottom=this.elementHeight-this.options.padding,!0!==(o=this.options.axes)&&"both"!==o&&"y"!==o||(i=(function(){var t,e,n,r;for(r=[],t=0,e=(n=this.grid).length;t<e;t++)r.push(this.measureText(this.yAxisFormat(n[t])).width);return r}).call(this),this.left+=Math.max.apply(Math,i)),!0!==(a=this.options.axes)&&"both"!==a&&"x"!==a||(t=(function(){var t,e,r;for(r=[],n=t=0,e=this.data.length;0<=e?t<e:t>e;n=0<=e?++t:--t)r.push(this.measureText(this.data[n].text,-this.options.xLabelAngle).height);return r}).call(this),this.bottom-=Math.max.apply(Math,t)),this.width=Math.max(1,this.right-this.left),this.height=Math.max(1,this.bottom-this.top),this.dx=this.width/(this.xmax-this.xmin),this.dy=this.height/(this.ymax-this.ymin),this.calc))return this.calc()},r.prototype.transY=function(t){return this.bottom-(t-this.ymin)*this.dy},r.prototype.transX=function(t){return 1===this.data.length?(this.left+this.right)/2:this.left+(t-this.xmin)*this.dx},r.prototype.redraw=function(){if(this.raphael.clear(),this._calc(),this.drawGrid(),this.drawGoals(),this.drawEvents(),this.draw)return this.draw()},r.prototype.measureText=function(t,e){var n,r;return null==e&&(e=0),n=(r=this.raphael.text(100,100,t).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).rotate(e)).getBBox(),r.remove(),n},r.prototype.yAxisFormat=function(t){return this.yLabelFormat(t)},r.prototype.yLabelFormat=function(t){return"function"==typeof this.options.yLabelFormat?this.options.yLabelFormat(t):""+this.options.preUnits+e.commas(t)+this.options.postUnits},r.prototype.drawGrid=function(){var t,e,n,r,i,o,a,s;if(!1!==this.options.grid||!0===(i=this.options.axes)||"both"===i||"y"===i){for(s=[],n=0,r=(o=this.grid).length;n<r;n++)e=this.transY(t=o[n]),!0!==(a=this.options.axes)&&"both"!==a&&"y"!==a||this.drawYAxisLabel(this.left-this.options.padding/2,e,this.yAxisFormat(t)),s.push(this.options.grid?this.drawGridLine("M"+this.left+","+e+"H"+(this.left+this.width)):void 0);return s}},r.prototype.drawGoals=function(){var t,e,n,r,i;for(i=[],t=e=0,n=(r=this.options.goals).length;e<n;t=++e)i.push(this.drawGoal(r[t],this.options.goalLineColors[t%this.options.goalLineColors.length]));return i},r.prototype.drawEvents=function(){var t,e,n,r,i;for(i=[],t=e=0,n=(r=this.events).length;e<n;t=++e)i.push(this.drawEvent(r[t],this.options.eventLineColors[t%this.options.eventLineColors.length]));return i},r.prototype.drawGoal=function(t,e){return this.raphael.path("M"+this.left+","+this.transY(t)+"H"+this.right).attr("stroke",e).attr("stroke-width",this.options.goalStrokeWidth)},r.prototype.drawEvent=function(t,e){return this.raphael.path("M"+this.transX(t)+","+this.bottom+"V"+this.top).attr("stroke",e).attr("stroke-width",this.options.eventStrokeWidth)},r.prototype.drawYAxisLabel=function(t,e,n){return this.raphael.text(t,e,n).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).attr("fill",this.options.gridTextColor).attr("text-anchor","end")},r.prototype.drawGridLine=function(t){return this.raphael.path(t).attr("stroke",this.options.gridLineColor).attr("stroke-width",this.options.gridStrokeWidth)},r.prototype.startRange=function(t){return this.hover.hide(),this.selectFrom=t,this.selectionRect.attr({x:t,width:0}).show()},r.prototype.endRange=function(t){var e,n;if(this.selectFrom)return n=Math.min(this.selectFrom,t),e=Math.max(this.selectFrom,t),this.options.rangeSelect.call(this.el,{start:this.data[this.hitTest(n)].x,end:this.data[this.hitTest(e)].x}),this.selectFrom=null},r.prototype.resizeHandler=function(){return this.timeoutId=null,this.raphael.setSize(this.el.width(),this.el.height()),this.redraw()},r}(e.EventEmitter),e.parseDate=function(t){var e,n,r,i,o,a,s,l,u,c,h;return"number"==typeof t?t:(n=t.match(/^(\d+) Q(\d)$/),i=t.match(/^(\d+)-(\d+)$/),o=t.match(/^(\d+)-(\d+)-(\d+)$/),s=t.match(/^(\d+) W(\d+)$/),l=t.match(/^(\d+)-(\d+)-(\d+)[ T](\d+):(\d+)(Z|([+-])(\d\d):?(\d\d))?$/),u=t.match(/^(\d+)-(\d+)-(\d+)[ T](\d+):(\d+):(\d+(\.\d+)?)(Z|([+-])(\d\d):?(\d\d))?$/),n?new Date(parseInt(n[1],10),3*parseInt(n[2],10)-1,1).getTime():i?new Date(parseInt(i[1],10),parseInt(i[2],10)-1,1).getTime():o?new Date(parseInt(o[1],10),parseInt(o[2],10)-1,parseInt(o[3],10)).getTime():s?(4!==(c=new Date(parseInt(s[1],10),0,1)).getDay()&&c.setMonth(0,1+(4-c.getDay()+7)%7),c.getTime()+6048e5*parseInt(s[2],10)):l?l[6]?(a=0,"Z"!==l[6]&&(a=60*parseInt(l[8],10)+parseInt(l[9],10),"+"===l[7]&&(a=0-a)),Date.UTC(parseInt(l[1],10),parseInt(l[2],10)-1,parseInt(l[3],10),parseInt(l[4],10),parseInt(l[5],10)+a)):new Date(parseInt(l[1],10),parseInt(l[2],10)-1,parseInt(l[3],10),parseInt(l[4],10),parseInt(l[5],10)).getTime():u?(h=parseFloat(u[6]),e=Math.floor(h),r=Math.round(1e3*(h-e)),u[8]?(a=0,"Z"!==u[8]&&(a=60*parseInt(u[10],10)+parseInt(u[11],10),"+"===u[9]&&(a=0-a)),Date.UTC(parseInt(u[1],10),parseInt(u[2],10)-1,parseInt(u[3],10),parseInt(u[4],10),parseInt(u[5],10)+a,e,r)):new Date(parseInt(u[1],10),parseInt(u[2],10)-1,parseInt(u[3],10),parseInt(u[4],10),parseInt(u[5],10),e,r).getTime()):new Date(parseInt(t,10),0,1).getTime())},e.Hover=function(){function n(n){null==n&&(n={}),this.options=t.extend({},e.Hover.defaults,n),this.el=t("<div class='"+this.options.class+"'></div>"),this.el.hide(),this.options.parent.append(this.el)}return n.defaults={class:"morris-hover morris-default-style"},n.prototype.update=function(t,e,n){return t?(this.html(t),this.show(),this.moveTo(e,n)):this.hide()},n.prototype.html=function(t){return this.el.html(t)},n.prototype.moveTo=function(t,e){var n,r,i,o,a,s;return a=this.options.parent.innerWidth(),o=this.options.parent.innerHeight(),r=this.el.outerWidth(),n=this.el.outerHeight(),i=Math.min(Math.max(0,t-r/2),a-r),null!=e?(s=e-n-10)<0&&(s=e+10)+n>o&&(s=o/2-n/2):s=o/2-n/2,this.el.css({left:i+"px",top:parseInt(s)+"px"})},n.prototype.show=function(){return this.el.show()},n.prototype.hide=function(){return this.el.hide()},n}(),e.Line=function(t){function n(t){if(this.hilight=o(this.hilight,this),this.onHoverOut=o(this.onHoverOut,this),this.onHoverMove=o(this.onHoverMove,this),this.onGridClick=o(this.onGridClick,this),!(this instanceof e.Line))return new e.Line(t);n.__super__.constructor.call(this,t)}return s(n,t),n.prototype.init=function(){if("always"!==this.options.hideHover)return this.hover=new e.Hover({parent:this.el}),this.on("hovermove",this.onHoverMove),this.on("hoverout",this.onHoverOut),this.on("gridclick",this.onGridClick)},n.prototype.defaults={lineWidth:3,pointSize:4,lineColors:["#0b62a4","#7A92A3","#4da74d","#afd8f8","#edc240","#cb4b4b","#9440ed"],pointStrokeWidths:[1],pointStrokeColors:["#ffffff"],pointFillColors:[],smooth:!0,xLabels:"auto",xLabelFormat:null,xLabelMargin:24,hideHover:!1},n.prototype.calc=function(){return this.calcPoints(),this.generatePaths()},n.prototype.calcPoints=function(){var t,e,n,r,i,o;for(o=[],n=0,r=(i=this.data).length;n<r;n++)(t=i[n])._x=this.transX(t.x),t._y=(function(){var n,r,i,o;for(o=[],n=0,r=(i=t.y).length;n<r;n++)o.push(null!=(e=i[n])?this.transY(e):e);return o}).call(this),o.push(t._ymax=Math.min.apply(Math,[this.bottom].concat(function(){var n,r,i,o;for(o=[],n=0,r=(i=t._y).length;n<r;n++)null!=(e=i[n])&&o.push(e);return o}())));return o},n.prototype.hitTest=function(t){var e,n,r,i;if(0===this.data.length)return null;for(e=n=0,r=(i=this.data.slice(1)).length;n<r&&!(t<(i[e]._x+this.data[e]._x)/2);e=++n);return e},n.prototype.onGridClick=function(t,e){var n;return n=this.hitTest(t),this.fire("click",n,this.data[n].src,t,e)},n.prototype.onHoverMove=function(t,e){var n;return n=this.hitTest(t),this.displayHoverForRow(n)},n.prototype.onHoverOut=function(){if(!1!==this.options.hideHover)return this.displayHoverForRow(null)},n.prototype.displayHoverForRow=function(t){var e;return null!=t?((e=this.hover).update.apply(e,this.hoverContentForRow(t)),this.hilight(t)):(this.hover.hide(),this.hilight())},n.prototype.hoverContentForRow=function(t){var e,n,r,i,o,a,s;for(e="<div class='morris-hover-row-label'>"+(r=this.data[t]).label+"</div>",n=o=0,a=(s=r.y).length;o<a;n=++o)i=s[n],e+="<div class='morris-hover-point' style='color: "+this.colorFor(r,n,"label")+"'>\n  "+this.options.labels[n]+":\n  "+this.yLabelFormat(i)+"\n</div>";return"function"==typeof this.options.hoverCallback&&(e=this.options.hoverCallback(t,this.options,e,r.src)),[e,r._x,r._ymax]},n.prototype.generatePaths=function(){var t,n,r,i;return this.paths=(function(){var o,a,s;for(s=[],n=o=0,a=this.options.ykeys.length;0<=a?o<a:o>a;n=0<=a?++o:--o)i="boolean"==typeof this.options.smooth?this.options.smooth:l.call(this.options.smooth,this.options.ykeys[n])>=0,t=(function(){var t,e,i,o;for(o=[],t=0,e=(i=this.data).length;t<e;t++)void 0!==(r=i[t])._y[n]&&o.push({x:r._x,y:r._y[n]});return o}).call(this),s.push(t.length>1?e.Line.createPath(t,i,this.bottom):null);return s}).call(this)},n.prototype.draw=function(){var t;if(!0!==(t=this.options.axes)&&"both"!==t&&"x"!==t||this.drawXAxis(),this.drawSeries(),!1===this.options.hideHover)return this.displayHoverForRow(this.data.length-1)},n.prototype.drawXAxis=function(){var t,n,r,i,o,a,s,l,u,c,h=this;for(s=this.bottom+this.options.padding/2,o=null,i=null,t=function(t,e){var n,r,a,l,u;return u=(n=h.drawXAxisLabel(h.transX(e),s,t)).getBBox(),n.transform("r"+-h.options.xLabelAngle),r=n.getBBox(),n.transform("t0,"+r.height/2+"..."),0!==h.options.xLabelAngle&&(l=-.5*u.width*Math.cos(h.options.xLabelAngle*Math.PI/180),n.transform("t"+l+",0...")),r=n.getBBox(),(null==o||o>=r.x+r.width||null!=i&&i>=r.x)&&r.x>=0&&r.x+r.width<h.el.width()?(0!==h.options.xLabelAngle&&(a=1.25*h.options.gridTextSize/Math.sin(h.options.xLabelAngle*Math.PI/180),i=r.x-a),o=r.x-h.options.xLabelMargin):n.remove()},(r=this.options.parseTime?1===this.data.length&&"auto"===this.options.xLabels?[[this.data[0].label,this.data[0].x]]:e.labelSeries(this.xmin,this.xmax,this.width,this.options.xLabels,this.options.xLabelFormat):(function(){var t,e,n,r;for(r=[],t=0,e=(n=this.data).length;t<e;t++)r.push([(a=n[t]).label,a.x]);return r}).call(this)).reverse(),c=[],l=0,u=r.length;l<u;l++)c.push(t((n=r[l])[0],n[1]));return c},n.prototype.drawSeries=function(){var t,e,n,r,i,o;for(this.seriesPoints=[],t=e=r=this.options.ykeys.length-1;r<=0?e<=0:e>=0;t=r<=0?++e:--e)this._drawLineFor(t);for(o=[],t=n=i=this.options.ykeys.length-1;i<=0?n<=0:n>=0;t=i<=0?++n:--n)o.push(this._drawPointFor(t));return o},n.prototype._drawPointFor=function(t){var e,n,r,i,o,a;for(this.seriesPoints[t]=[],a=[],r=0,i=(o=this.data).length;r<i;r++)e=null,null!=(n=o[r])._y[t]&&(e=this.drawLinePoint(n._x,n._y[t],this.colorFor(n,t,"point"),t)),a.push(this.seriesPoints[t].push(e));return a},n.prototype._drawLineFor=function(t){var e;if(null!==(e=this.paths[t]))return this.drawLinePath(e,this.colorFor(null,t,"line"),t)},n.createPath=function(t,n,r){var i,o,a,s,l,u,c,h,f;for(u="",n&&(a=e.Line.gradients(t)),c={y:null},s=h=0,f=t.length;h<f;s=++h)null!=(i=t[s]).y&&(null!=c.y?n?(o=a[s],u+="C"+(c.x+(l=(i.x-c.x)/4))+","+Math.min(r,c.y+l*a[s-1])+","+(i.x-l)+","+Math.min(r,i.y-l*o)+","+i.x+","+i.y):u+="L"+i.x+","+i.y:n&&null==a[s]||(u+="M"+i.x+","+i.y)),c=i;return u},n.gradients=function(t){var e,n,r,i,o,a,s,l;for(n=function(t,e){return(t.y-e.y)/(t.x-e.x)},l=[],r=a=0,s=t.length;a<s;r=++a)null!=(e=t[r]).y?(i=t[r+1]||{y:null},l.push(null!=(o=t[r-1]||{y:null}).y&&null!=i.y?n(o,i):null!=o.y?n(o,e):null!=i.y?n(e,i):null)):l.push(null);return l},n.prototype.hilight=function(t){var e,n,r,i,o;if(null!==this.prevHilight&&this.prevHilight!==t)for(e=n=0,i=this.seriesPoints.length-1;0<=i?n<=i:n>=i;e=0<=i?++n:--n)this.seriesPoints[e][this.prevHilight]&&this.seriesPoints[e][this.prevHilight].animate(this.pointShrinkSeries(e));if(null!==t&&this.prevHilight!==t)for(e=r=0,o=this.seriesPoints.length-1;0<=o?r<=o:r>=o;e=0<=o?++r:--r)this.seriesPoints[e][t]&&this.seriesPoints[e][t].animate(this.pointGrowSeries(e));return this.prevHilight=t},n.prototype.colorFor=function(t,e,n){return"function"==typeof this.options.lineColors?this.options.lineColors.call(this,t,e,n):"point"===n&&this.options.pointFillColors[e%this.options.pointFillColors.length]||this.options.lineColors[e%this.options.lineColors.length]},n.prototype.drawXAxisLabel=function(t,e,n){return this.raphael.text(t,e,n).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).attr("fill",this.options.gridTextColor)},n.prototype.drawLinePath=function(t,e,n){return this.raphael.path(t).attr("stroke",e).attr("stroke-width",this.lineWidthForSeries(n))},n.prototype.drawLinePoint=function(t,e,n,r){return this.raphael.circle(t,e,this.pointSizeForSeries(r)).attr("fill",n).attr("stroke-width",this.pointStrokeWidthForSeries(r)).attr("stroke",this.pointStrokeColorForSeries(r))},n.prototype.pointStrokeWidthForSeries=function(t){return this.options.pointStrokeWidths[t%this.options.pointStrokeWidths.length]},n.prototype.pointStrokeColorForSeries=function(t){return this.options.pointStrokeColors[t%this.options.pointStrokeColors.length]},n.prototype.lineWidthForSeries=function(t){return this.options.lineWidth instanceof Array?this.options.lineWidth[t%this.options.lineWidth.length]:this.options.lineWidth},n.prototype.pointSizeForSeries=function(t){return this.options.pointSize instanceof Array?this.options.pointSize[t%this.options.pointSize.length]:this.options.pointSize},n.prototype.pointGrowSeries=function(t){return Raphael.animation({r:this.pointSizeForSeries(t)+3},25,"linear")},n.prototype.pointShrinkSeries=function(t){return Raphael.animation({r:this.pointSizeForSeries(t)},25,"linear")},n}(e.Grid),e.labelSeries=function(n,r,i,o,a){var s,l,u,c,h,f,d,p,g,m;if(u=200*(r-n)/i,l=new Date(n),void 0===(f=e.LABEL_SPECS[o]))for(p=0,g=(m=e.AUTO_LABEL_ORDER).length;p<g;p++)if(u>=(h=e.LABEL_SPECS[m[p]]).span){f=h;break}for(void 0===f&&(f=e.LABEL_SPECS.second),a&&(f=t.extend({},f,{fmt:a})),s=f.start(l),c=[];(d=s.getTime())<=r;)d>=n&&c.push([f.fmt(s),d]),f.incr(s);return c},n=function(t){return{span:60*t*1e3,start:function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours())},fmt:function(t){return e.pad2(t.getHours())+":"+e.pad2(t.getMinutes())},incr:function(e){return e.setUTCMinutes(e.getUTCMinutes()+t)}}},r=function(t){return{span:1e3*t,start:function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes())},fmt:function(t){return e.pad2(t.getHours())+":"+e.pad2(t.getMinutes())+":"+e.pad2(t.getSeconds())},incr:function(e){return e.setUTCSeconds(e.getUTCSeconds()+t)}}},e.LABEL_SPECS={decade:{span:1728e8,start:function(t){return new Date(t.getFullYear()-t.getFullYear()%10,0,1)},fmt:function(t){return""+t.getFullYear()},incr:function(t){return t.setFullYear(t.getFullYear()+10)}},year:{span:1728e7,start:function(t){return new Date(t.getFullYear(),0,1)},fmt:function(t){return""+t.getFullYear()},incr:function(t){return t.setFullYear(t.getFullYear()+1)}},month:{span:24192e5,start:function(t){return new Date(t.getFullYear(),t.getMonth(),1)},fmt:function(t){return t.getFullYear()+"-"+e.pad2(t.getMonth()+1)},incr:function(t){return t.setMonth(t.getMonth()+1)}},week:{span:6048e5,start:function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate())},fmt:function(t){return t.getFullYear()+"-"+e.pad2(t.getMonth()+1)+"-"+e.pad2(t.getDate())},incr:function(t){return t.setDate(t.getDate()+7)}},day:{span:864e5,start:function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate())},fmt:function(t){return t.getFullYear()+"-"+e.pad2(t.getMonth()+1)+"-"+e.pad2(t.getDate())},incr:function(t){return t.setDate(t.getDate()+1)}},hour:n(60),"30min":n(30),"15min":n(15),"10min":n(10),"5min":n(5),minute:n(1),"30sec":r(30),"15sec":r(15),"10sec":r(10),"5sec":r(5),second:r(1)},e.AUTO_LABEL_ORDER=["decade","year","month","week","day","hour","30min","15min","10min","5min","minute","30sec","15sec","10sec","5sec","second"],e.Area=function(n){var r;function i(n){var o;if(!(this instanceof e.Area))return new e.Area(n);o=t.extend({},r,n),this.cumulative=!o.behaveLikeLine,"auto"===o.fillOpacity&&(o.fillOpacity=o.behaveLikeLine?.8:1),i.__super__.constructor.call(this,o)}return s(i,n),r={fillOpacity:"auto",behaveLikeLine:!1},i.prototype.calcPoints=function(){var t,e,n,r,i,o,a;for(a=[],r=0,i=(o=this.data).length;r<i;r++)(t=o[r])._x=this.transX(t.x),e=0,t._y=(function(){var r,i,o,a;for(a=[],r=0,i=(o=t.y).length;r<i;r++)n=o[r],a.push(this.transY(this.options.behaveLikeLine?n:e+=n||0));return a}).call(this),a.push(t._ymax=Math.max.apply(Math,t._y));return a},i.prototype.drawSeries=function(){var t,e,n,r,i,o,a,s;for(this.seriesPoints=[],s=[],n=0,r=(e=this.options.behaveLikeLine?(function(){o=[];for(var t=0,e=this.options.ykeys.length-1;0<=e?t<=e:t>=e;0<=e?t++:t--)o.push(t);return o}).apply(this):(function(){a=[];for(var t=i=this.options.ykeys.length-1;i<=0?t<=0:t>=0;i<=0?t++:t--)a.push(t);return a}).apply(this)).length;n<r;n++)this._drawFillFor(t=e[n]),this._drawLineFor(t),s.push(this._drawPointFor(t));return s},i.prototype._drawFillFor=function(t){var e;if(null!==(e=this.paths[t]))return e=e+"L"+this.transX(this.xmax)+","+this.bottom+"L"+this.transX(this.xmin)+","+this.bottom+"Z",this.drawFilledPath(e,this.fillForSeries(t))},i.prototype.fillForSeries=function(t){var e;return e=Raphael.rgb2hsl(this.colorFor(this.data[t],t,"line")),Raphael.hsl(e.h,this.options.behaveLikeLine?.9*e.s:.75*e.s,Math.min(.98,this.options.behaveLikeLine?1.2*e.l:1.25*e.l))},i.prototype.drawFilledPath=function(t,e){return this.raphael.path(t).attr("fill",e).attr("fill-opacity",this.options.fillOpacity).attr("stroke","none")},i}(e.Line),e.Bar=function(n){function r(n){if(this.onHoverOut=o(this.onHoverOut,this),this.onHoverMove=o(this.onHoverMove,this),this.onGridClick=o(this.onGridClick,this),!(this instanceof e.Bar))return new e.Bar(n);r.__super__.constructor.call(this,t.extend({},n,{parseTime:!1}))}return s(r,n),r.prototype.init=function(){if(this.cumulative=this.options.stacked,"always"!==this.options.hideHover)return this.hover=new e.Hover({parent:this.el}),this.on("hovermove",this.onHoverMove),this.on("hoverout",this.onHoverOut),this.on("gridclick",this.onGridClick)},r.prototype.defaults={barSizeRatio:.75,barGap:3,barColors:["#0b62a4","#7a92a3","#4da74d","#afd8f8","#edc240","#cb4b4b","#9440ed"],barOpacity:1,barRadius:[0,0,0,0],xLabelMargin:50},r.prototype.calc=function(){var t;if(this.calcBars(),!1===this.options.hideHover)return(t=this.hover).update.apply(t,this.hoverContentForRow(this.data.length-1))},r.prototype.calcBars=function(){var t,e,n,r,i,o,a;for(a=[],t=r=0,i=(o=this.data).length;r<i;t=++r)(e=o[t])._x=this.left+this.width*(t+.5)/this.data.length,a.push(e._y=(function(){var t,r,i,o;for(o=[],t=0,r=(i=e.y).length;t<r;t++)o.push(null!=(n=i[t])?this.transY(n):null);return o}).call(this));return a},r.prototype.draw=function(){var t;return!0!==(t=this.options.axes)&&"both"!==t&&"x"!==t||this.drawXAxis(),this.drawSeries()},r.prototype.drawXAxis=function(){var t,e,n,r,i,o,a,s,l,u,c,h,f;for(u=this.bottom+(this.options.xAxisLabelTopPadding||this.options.padding/2),a=null,o=null,f=[],t=c=0,h=this.data.length;0<=h?c<h:c>h;t=0<=h?++c:--c)l=(e=this.drawXAxisLabel((s=this.data[this.data.length-1-t])._x,u,s.label)).getBBox(),e.transform("r"+-this.options.xLabelAngle),n=e.getBBox(),e.transform("t0,"+n.height/2+"..."),0!==this.options.xLabelAngle&&(i=-.5*l.width*Math.cos(this.options.xLabelAngle*Math.PI/180),e.transform("t"+i+",0...")),(null==a||a>=n.x+n.width||null!=o&&o>=n.x)&&n.x>=0&&n.x+n.width<this.el.width()?(0!==this.options.xLabelAngle&&(r=1.25*this.options.gridTextSize/Math.sin(this.options.xLabelAngle*Math.PI/180),o=n.x-r),f.push(a=n.x-this.options.xLabelMargin)):f.push(e.remove());return f},r.prototype.drawSeries=function(){var t,e,n,r,i,o,a,s,l,u,c,h,f,d;return t=((n=this.width/this.options.data.length)*this.options.barSizeRatio-this.options.barGap*((s=this.options.stacked?1:this.options.ykeys.length)-1))/s,this.options.barSize&&(t=Math.min(t,this.options.barSize)),a=(n-t*s-this.options.barGap*(s-1))/2,d=this.ymin<=0&&this.ymax>=0?this.transY(0):null,this.bars=(function(){var s,p,g,m;for(m=[],r=s=0,p=(g=this.data).length;s<p;r=++s)l=g[r],i=0,m.push((function(){var s,p,g,m;for(m=[],u=s=0,p=(g=l._y).length;s<p;u=++s)null!==(f=g[u])?(d?(h=Math.min(f,d),e=Math.max(f,d)):(h=f,e=this.bottom),o=this.left+r*n+a,this.options.stacked||(o+=u*(t+this.options.barGap)),c=e-h,this.options.verticalGridCondition&&this.options.verticalGridCondition(l.x)&&this.drawBar(this.left+r*n,this.top,n,Math.abs(this.top-this.bottom),this.options.verticalGridColor,this.options.verticalGridOpacity,this.options.barRadius),this.options.stacked&&(h-=i),this.drawBar(o,h,t,c,this.colorFor(l,u,"bar"),this.options.barOpacity,this.options.barRadius),m.push(i+=c)):m.push(null);return m}).call(this));return m}).call(this)},r.prototype.colorFor=function(t,e,n){return"function"==typeof this.options.barColors?this.options.barColors.call(this,{x:t.x,y:t.y[e],label:t.label},{index:e,key:this.options.ykeys[e],label:this.options.labels[e]},n):this.options.barColors[e%this.options.barColors.length]},r.prototype.hitTest=function(t){return 0===this.data.length?null:(t=Math.max(Math.min(t,this.right),this.left),Math.min(this.data.length-1,Math.floor((t-this.left)/(this.width/this.data.length))))},r.prototype.onGridClick=function(t,e){var n;return n=this.hitTest(t),this.fire("click",n,this.data[n].src,t,e)},r.prototype.onHoverMove=function(t,e){var n,r;return n=this.hitTest(t),(r=this.hover).update.apply(r,this.hoverContentForRow(n))},r.prototype.onHoverOut=function(){if(!1!==this.options.hideHover)return this.hover.hide()},r.prototype.hoverContentForRow=function(t){var e,n,r,i,o,a,s;for(e="<div class='morris-hover-row-label'>"+(r=this.data[t]).label+"</div>",n=o=0,a=(s=r.y).length;o<a;n=++o)i=s[n],e+="<div class='morris-hover-point' style='color: "+this.colorFor(r,n,"label")+"'>\n  "+this.options.labels[n]+":\n  "+this.yLabelFormat(i)+"\n</div>";return"function"==typeof this.options.hoverCallback&&(e=this.options.hoverCallback(t,this.options,e,r.src)),[e,this.left+(t+.5)*this.width/this.data.length]},r.prototype.drawXAxisLabel=function(t,e,n){return this.raphael.text(t,e,n).attr("font-size",this.options.gridTextSize).attr("font-family",this.options.gridTextFamily).attr("font-weight",this.options.gridTextWeight).attr("fill",this.options.gridTextColor)},r.prototype.drawBar=function(t,e,n,r,i,o,a){var s;return(0===(s=Math.max.apply(Math,a))||s>r?this.raphael.rect(t,e,n,r):this.raphael.path(this.roundedRect(t,e,n,r,a))).attr("fill",i).attr("fill-opacity",o).attr("stroke","none")},r.prototype.roundedRect=function(t,e,n,r,i){return null==i&&(i=[0,0,0,0]),["M",t,i[0]+e,"Q",t,e,t+i[0],e,"L",t+n-i[1],e,"Q",t+n,e,t+n,e+i[1],"L",t+n,e+r-i[2],"Q",t+n,e+r,t+n-i[2],e+r,"L",t+i[3],e+r,"Q",t,e+r,t,e+r-i[3],"Z"]},r}(e.Grid),e.Donut=function(n){function r(n){this.resizeHandler=o(this.resizeHandler,this),this.select=o(this.select,this),this.click=o(this.click,this);var r=this;if(!(this instanceof e.Donut))return new e.Donut(n);if(this.options=t.extend({},this.defaults,n),this.el=t("string"==typeof n.element?document.getElementById(n.element):n.element),null===this.el||0===this.el.length)throw new Error("Graph placeholder not found.");void 0!==n.data&&0!==n.data.length&&(this.raphael=new Raphael(this.el[0]),this.options.resize&&t(window).bind("resize",function(t){return null!=r.timeoutId&&window.clearTimeout(r.timeoutId),r.timeoutId=window.setTimeout(r.resizeHandler,100)}),this.setData(n.data))}return s(r,n),r.prototype.defaults={colors:["#0B62A4","#3980B5","#679DC6","#95BBD7","#B0CCE1","#095791","#095085","#083E67","#052C48","#042135"],backgroundColor:"#FFFFFF",labelColor:"#000000",formatter:e.commas,resize:!1},r.prototype.redraw=function(){var t,n,r,i,o,a,s,l,u,c,h,f,d,p,g,m,v,y,b,x,_,w;for(this.raphael.clear(),n=this.el.width()/2,r=this.el.height()/2,f=(Math.min(n,r)-10)/3,h=0,d=0,m=(b=this.values).length;d<m;d++)h+=b[d];for(l=5/(2*f),t=1.9999*Math.PI-l*this.data.length,a=0,o=0,this.segments=[],i=p=0,v=(x=this.values).length;p<v;i=++p)(c=new e.DonutSegment(n,r,2*f,f,a,u=a+l+t*(x[i]/h),this.data[i].color||this.options.colors[o%this.options.colors.length],this.options.backgroundColor,o,this.raphael)).render(),this.segments.push(c),c.on("hover",this.select),c.on("click",this.click),a=u,o+=1;for(this.text1=this.drawEmptyDonutLabel(n,r-10,this.options.labelColor,15,800),this.text2=this.drawEmptyDonutLabel(n,r+10,this.options.labelColor,14),s=Math.max.apply(Math,this.values),o=0,w=[],g=0,y=(_=this.values).length;g<y;g++){if(_[g]===s){this.select(o);break}w.push(o+=1)}return w},r.prototype.setData=function(t){return this.data=t,this.values=(function(){var t,e,n,r;for(r=[],t=0,e=(n=this.data).length;t<e;t++)r.push(parseFloat(n[t].value));return r}).call(this),this.redraw()},r.prototype.click=function(t){return this.fire("click",t,this.data[t])},r.prototype.select=function(t){var e,n,r,i;for(n=0,r=(i=this.segments).length;n<r;n++)i[n].deselect();return this.segments[t].select(),this.setLabels((e=this.data[t]).label,this.options.formatter(e.value,e))},r.prototype.setLabels=function(t,e){var n,r,i,o,a,s,l,u;return o=1.8*(n=2*(Math.min(this.el.width()/2,this.el.height()/2)-10)/3),i=n/2,r=n/3,this.text1.attr({text:t,transform:""}),a=this.text1.getBBox(),s=Math.min(o/a.width,i/a.height),this.text1.attr({transform:"S"+s+","+s+","+(a.x+a.width/2)+","+(a.y+a.height)}),this.text2.attr({text:e,transform:""}),l=this.text2.getBBox(),u=Math.min(o/l.width,r/l.height),this.text2.attr({transform:"S"+u+","+u+","+(l.x+l.width/2)+","+l.y})},r.prototype.drawEmptyDonutLabel=function(t,e,n,r,i){var o;return o=this.raphael.text(t,e,"").attr("font-size",r).attr("fill",n),null!=i&&o.attr("font-weight",i),o},r.prototype.resizeHandler=function(){return this.timeoutId=null,this.raphael.setSize(this.el.width(),this.el.height()),this.redraw()},r}(e.EventEmitter),e.DonutSegment=function(t){function e(t,e,n,r,i,a,s,l,u,c){this.cx=t,this.cy=e,this.inner=n,this.outer=r,this.color=s,this.backgroundColor=l,this.index=u,this.raphael=c,this.deselect=o(this.deselect,this),this.select=o(this.select,this),this.sin_p0=Math.sin(i),this.cos_p0=Math.cos(i),this.sin_p1=Math.sin(a),this.cos_p1=Math.cos(a),this.is_long=a-i>Math.PI?1:0,this.path=this.calcSegment(this.inner+3,this.inner+this.outer-5),this.selectedPath=this.calcSegment(this.inner+3,this.inner+this.outer),this.hilight=this.calcArc(this.inner)}return s(e,t),e.prototype.calcArcPoints=function(t){return[this.cx+t*this.sin_p0,this.cy+t*this.cos_p0,this.cx+t*this.sin_p1,this.cy+t*this.cos_p1]},e.prototype.calcSegment=function(t,e){var n,r,i,o,a,s;return n=(a=this.calcArcPoints(t))[0],i=a[1],r=a[2],o=a[3],s=this.calcArcPoints(e),"M"+n+","+i+"A"+t+","+t+",0,"+this.is_long+",0,"+r+","+o+"L"+s[2]+","+s[3]+"A"+e+","+e+",0,"+this.is_long+",1,"+s[0]+","+s[1]+"Z"},e.prototype.calcArc=function(t){var e;return"M"+(e=this.calcArcPoints(t))[0]+","+e[1]+"A"+t+","+t+",0,"+this.is_long+",0,"+e[2]+","+e[3]},e.prototype.render=function(){var t=this;return this.arc=this.drawDonutArc(this.hilight,this.color),this.seg=this.drawDonutSegment(this.path,this.color,this.backgroundColor,function(){return t.fire("hover",t.index)},function(){return t.fire("click",t.index)})},e.prototype.drawDonutArc=function(t,e){return this.raphael.path(t).attr({stroke:e,"stroke-width":2,opacity:0})},e.prototype.drawDonutSegment=function(t,e,n,r,i){return this.raphael.path(t).attr({fill:e,stroke:n,"stroke-width":3}).hover(r).click(i)},e.prototype.select=function(){if(!this.selected)return this.seg.animate({path:this.selectedPath},150,"<>"),this.arc.animate({opacity:1},150,"<>"),this.selected=!0},e.prototype.deselect=function(){if(this.selected)return this.seg.animate({path:this.path},150,"<>"),this.arc.animate({opacity:0},150,"<>"),this.selected=!1},e}(e.EventEmitter)}).call(this),function(t){"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return t(e,window,document)}):"object"==typeof exports?module.exports=function(e,n){return e||(e=window),n&&n.fn.dataTable||(n=require("datatables.net")(e,n).$),t(n,0,e.document)}:t(jQuery,window,document)}(function(t,e,n,r){"use strict";var i=t.fn.dataTable;return t.extend(!0,i.defaults,{dom:"<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",renderer:"bootstrap"}),t.extend(i.ext.classes,{sWrapper:"dataTables_wrapper dt-bootstrap4",sFilterInput:"form-control form-control-sm",sLengthSelect:"custom-select custom-select-sm form-control form-control-sm",sProcessing:"dataTables_processing card",sPageButton:"paginate_button page-item"}),i.ext.renderer.pageButton.bootstrap=function(e,o,a,s,l,u){var c,h,f,d=new i.Api(e),p=e.oClasses,g=e.oLanguage.oPaginate,m=e.oLanguage.oAria.paginate||{},v=0,y=function(n,r){var i,o,s,f,b=function(e){e.preventDefault(),t(e.currentTarget).hasClass("disabled")||d.page()==e.data.action||d.page(e.data.action).draw("page")};for(i=0,o=r.length;i<o;i++)if(t.isArray(f=r[i]))y(n,f);else{switch(c="",h="",f){case"ellipsis":c="&#x2026;",h="disabled";break;case"first":c=g.sFirst,h=f+(l>0?"":" disabled");break;case"previous":c=g.sPrevious,h=f+(l>0?"":" disabled");break;case"next":c=g.sNext,h=f+(l<u-1?"":" disabled");break;case"last":c=g.sLast,h=f+(l<u-1?"":" disabled");break;default:c=f+1,h=l===f?"active":""}c&&(s=t("<li>",{class:p.sPageButton+" "+h,id:0===a&&"string"==typeof f?e.sTableId+"_"+f:null}).append(t("<a>",{href:"#","aria-controls":e.sTableId,"aria-label":m[f],"data-dt-idx":v,tabindex:e.iTabIndex,class:"page-link"}).html(c)).appendTo(n),e.oApi._fnBindAction(s,{action:f},b),v++)}};try{f=t(o).find(n.activeElement).data("dt-idx")}catch(b){}y(t(o).empty().html('<ul class="pagination"/>').children("ul"),s),f!==r&&t(o).find("[data-dt-idx="+f+"]").trigger("focus")},i});