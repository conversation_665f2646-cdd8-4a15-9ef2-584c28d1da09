{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"smarthr": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "allowedCommonJsDependencies": ["angular-calendar", "date-fns", "lodash", "pdfjs-dist"], "assets": ["src/favicon.ico", "src/assets", "src/WEB-INF"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "node_modules/font-awesome/css/font-awesome.css", "node_modules/line-awesome/dist/line-awesome/css/line-awesome.css", "node_modules/filepond/dist/filepond.min.css", "node_modules/angular-calendar/css/angular-calendar.css", "src/styles.css", "src/_custom.css", "node_modules/morris.js/morris.css", "node_modules/datatables.net-bs4/css/dataTables.bootstrap4.css", "node_modules/primeng/resources/themes/saga-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css", "node_modules/ng-drag-drop/style.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/popper.js/dist/umd/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js", "node_modules/datatables.net/js/jquery.dataTables.js", "node_modules/raphael/raphael.js", "node_modules/morris.js/morris.js", "node_modules/datatables.net-bs4/js/dataTables.bootstrap4.js", "src/assets/balkanapp/orgchart.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "smarthr:build"}, "configurations": {"production": {"browserTarget": "smarthr:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "smarthr:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "smarthr:serve"}, "configurations": {"production": {"devServerTarget": "smarthr:serve:production"}}}}}}, "defaultProject": "smarthr", "schematics": {"@schematics/angular": {"component": {"spec": false}}}, "cli": {"analytics": "1f6fd494-fa37-47a2-be44-a4ec96f0c95e"}}