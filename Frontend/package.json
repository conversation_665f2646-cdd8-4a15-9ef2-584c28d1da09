{"name": "smarthr", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "ngcc --properties es2015 browser module main --first-only --create-ivy-entry-points"}, "private": true, "dependencies": {"@angular/animations": "^11.2.6", "@angular/cdk": "^8.2.3", "@angular/common": "~11.2.6", "@angular/compiler": "~11.2.6", "@angular/core": "^11.2.6", "@angular/forms": "~11.2.6", "@angular/localize": "^11.2.6", "@angular/material": "^8.2.3", "@angular/platform-browser": "~11.2.6", "@angular/platform-browser-dynamic": "~11.2.6", "@angular/router": "~11.2.6", "@ckeditor/ckeditor5-angular": "^2.0.2", "@ckeditor/ckeditor5-build-classic": "^32.0.0", "@ckeditor/ckeditor5-font": "^34.1.0", "@ng-bootstrap/ng-bootstrap": "^5.1.2", "@ng-select/ng-select": "^7.3.0", "@rxweb/reactive-form-validators": "^2.1.4", "angular-calendar": "^0.27.20", "angular-datatables": "^8.0.0", "angular-google-charts": "^2.2.2", "angular-in-memory-web-api": "^0.9.0", "angular-morris-js": "^1.1.0", "angularx-flatpickr": "^6.1.1", "bootstrap": "^4.5.2", "calendar-utils": "0.0.59", "chart.js": "^3.6.2", "ckeditor4-angular": "^3.1.0", "core-js": "^3.3.3", "datatables.net": "^1.12.1", "datatables.net-bs4": "^1.12.1", "datatables.net-dt": "^1.12.1", "date-fns": "^1.30.1", "file-saver": "^2.0.5", "filepond": "^4.7.3", "flatpickr": "^4.6.3", "font-awesome": "^4.7.0", "jquery": "^3.5.1", "jquery-dropdown": "^1.0.0", "line-awesome": "^1.0.2", "lodash-es": "^4.17.21", "moment": "^2.29.3", "morris.js": "^0.5.0", "ng-drag-drop": "^5.0.0", "ng-select2": "^1.2.2", "ng2-charts": "^3.0.2", "ng2-ckeditor": "^1.3.6", "ng2-pdf-viewer": "^7.0.2", "ng2-search-filter": "^0.5.1", "ngx-bootstrap": "^5.6.2", "ngx-filepond": "^5.0.1", "ngx-mask": "^10.0.1", "ngx-pagination": "^5.1.1", "ngx-perfect-scrollbar": "^8.0.0", "ngx-print": "^1.2.1", "ngx-spinner": "^11.0.2", "ngx-summernote": "^0.8.8", "ngx-toastr": "^11.3.0", "popper.js": "^1.16.0", "positioning": "^1.3.1", "primeicons": "^4.0.0", "primeng": "^10.0.0", "raphael": "^2.3.0", "rxjs": "^6.5.4", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "tslib": "^2.4.0", "xlsx": "^0.18.5", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1102.19", "@angular/cli": "~11.2.5", "@angular/compiler-cli": "~11.2.6", "@angular/language-service": "~11.2.6", "@types/datatables.net": "^1.10.18", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.5.1", "@types/node": "~8.9.4", "@types/sockjs-client": "^1.5.4", "@types/stompjs": "^2.3.4", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.3.20", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.3", "typescript": "~4.0.3"}, "description": "This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 8.0.0.", "main": "karma.conf.js", "directories": {"doc": "docs"}, "author": "", "license": "ISC"}