# PowerShell script to add chat route to routing module
$filePath = "D:\Projects\HRMS\Frontend\src\app\all-modules\all-modules-routing.module.ts"
$content = Get-Content $filePath -Raw

# Find the transport-mgt route and add chat route after it
$oldPattern = "path:'transport-mgt',\s*loadChildren: \(\) => import\('\.\/transport-mgt\/transport-mgt\.module'\)\.then\(m => m\.TransportMgtModule\)\s*},"
$newPattern = @"
path:'transport-mgt',
        loadChildren: () => import('./transport-mgt/transport-mgt.module').then(m => m.TransportMgtModule)
      },
      {
        path:'chat',
        loadChildren: () => import('./chat/chat.module').then(m => m.ChatModule)
      },
"@

$newContent = $content -replace $oldPattern, $newPattern
Set-Content $filePath $newContent -Encoding UTF8

Write-Host "Chat route added successfully!"
