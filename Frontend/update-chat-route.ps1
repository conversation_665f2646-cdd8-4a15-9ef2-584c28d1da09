# PowerShell script to update chat route to use simple module
$filePath = "D:\Projects\HRMS\Frontend\src\app\all-modules\all-modules-routing.module.ts"
$content = Get-Content $filePath -Raw

# Update the chat route to use the simple module
$oldPattern = "path:'chat',\s*loadChildren: \(\) => import\('\.\/chat\/chat\.module'\)\.then\(m => m\.ChatModule\)"
$newPattern = "path:'chat', loadChildren: () => import('./chat/chat-simple.module').then(m => m.ChatModule)"

$newContent = $content -replace $oldPattern, $newPattern
Set-Content $filePath $newContent -Encoding UTF8

Write-Host "Chat route updated to use simple module!"
