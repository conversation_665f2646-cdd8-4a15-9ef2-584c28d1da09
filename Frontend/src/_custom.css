@import "~ngx-toastr/toastr.css";
@import "~@ng-select/ng-select/themes/default.theme.css";

.toast-custom {
    top: 62px;
    right: 0px;
}

#toast-container > div {
    max-width: 700px;
    width: 1200px; /* width: 100% */
    width: 100%;
    min-width: 400px;
}


/* ----------------- Pagination CSS Start ------------------------------ */
.pgn-pageSliceCt ul.ngx-pagination {
	padding-left: 5px !important;
}

.pgn-pageSizeOption {
    height: 30px;
    border: 1px solid #dee2e6;
}

.ngx-pagination li {
    margin-right: 0px !important;
    position: relative;
    color: #0d6efd;
    background-color: #fff;
    border: 1px solid #dee2e6;
}
.ngx-pagination li a {
    color: #0d6efd !important;
}
/* ----------------- Pagination CSS End ------------------------------ */
