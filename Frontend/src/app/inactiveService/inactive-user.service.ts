import { EventEmitter, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LoginService } from '../login/services/login.service';
import { AnyObject } from 'chart.js/types/basic';

@Injectable({
  providedIn: 'root'
})
export class InactiveUserService {

  private timeoutId: any;
  public onTimeout: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private login: LoginService,
    private router: Router,

  ) {
   // this.addActivityListeners();

  }

  // startIdleTimer(minutes: number, onTimeout: () => void) {
  //   this.clearIdleTimer();
  //   this.timeoutId = setTimeout(() => {
  //     onTimeout();
  //   }, minutes * 60 * 1000);

  // }

  // clearIdleTimer() {
  //   if (this.timeoutId) {
  //     clearTimeout(this.timeoutId);
  //     this.timeoutId = null;
  //   }
  // }

  // private addActivityListeners() {

  //  console.log("====== MOUSE / KEYBORAD MOVEMEMT FOUND =========");
  //   // Listen for mousemove and keydown events to reset the timer
  //   window.addEventListener('mousemove', this.resetIdleTimer.bind(this));
  //   window.addEventListener('keydown', this.resetIdleTimer.bind(this));
    
  // }

  // private resetIdleTimer() {

  //   console.log("====== Time RESET =========");
  //   // Reset the idle timer when activity is detected
  //   this.clearIdleTimer();
   
  //   // Start Timer After Reset Time
  //   this.startIdleTimer(60, () => {

  //     console.log("====== LOGOUT - USER WAS IDLE =========");
  //     // Logout If remains Idle
  //     this.logOutUser();
  //   });
  // }

  // logOutUser() {

  //   this.login.logout();
  //   this.router.navigate(["/login"]);

  // }
}

