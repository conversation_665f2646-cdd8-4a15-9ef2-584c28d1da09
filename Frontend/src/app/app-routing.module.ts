import { NgModule } from '@angular/core';
import { Routes, RouterModule, PreloadAllModules } from '@angular/router';
import { UserGuard } from './guard/user.guard';
import { RouterExtService } from './utils/_services/prev-router.service';
import { SelectivePreloadingStrategyService } from './all-modules/selectivePreloadingStrategyService';

const routes: Routes = [
  { path: 'login', loadChildren: () => import(`./login/login.module`).then(m => m.LoginModule) },
  { path: 'error', loadChildren: () => import(`./errorpages/errorpages.module`).then(m => m.ErrorpagesModule) },
  { path: '', loadChildren: () => import(`./all-modules/all-modules.module`).then(m => m.AllModulesModule),canActivate:[UserGuard] },
  { path: '**', loadChildren: () => import(`./login/login.module`).then(m => m.LoginModule) },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: SelectivePreloadingStrategyService
    })
  ],
  exports: [RouterModule],
  providers: [RouterExtService]
})
export class AppRoutingModule { }
