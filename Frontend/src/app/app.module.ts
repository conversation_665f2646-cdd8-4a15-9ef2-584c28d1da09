import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

// Bootstrap DataTable
import { DataTablesModule } from 'angular-datatables';
import { ToastrModule } from 'ngx-toastr';
import { HttpClientModule } from '@angular/common/http';
import { authInterceptorProviders } from './guard/auth.interceptor';
import { DatePipe } from '@angular/common';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { RouterExtService } from './utils/_services/prev-router.service';
import { FormsModule } from '@angular/forms';


@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    FormsModule ,
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    DataTablesModule,
    HttpClientModule,
    PdfViewerModule,

    ToastrModule.forRoot(
      {
        timeOut: 10000, // 10 seconds
        positionClass: 'toast-bottom-right',
        preventDuplicates: true,
        closeButton: true,
        progressBar: true,
      }
    ),
  ],
  providers: [
    authInterceptorProviders,
    DatePipe,
  ],

  bootstrap: [AppComponent]
})
export class AppModule {
  constructor(private routerExtService: RouterExtService){

  }
 }
