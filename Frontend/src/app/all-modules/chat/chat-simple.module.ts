import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { ChatMainComponent } from './chat-main.component';

const routes = [
  {
    path: '',
    component: ChatMainComponent
  }
];

@NgModule({
  declarations: [
    ChatMainComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(routes)
  ]
})
export class ChatModule { }
