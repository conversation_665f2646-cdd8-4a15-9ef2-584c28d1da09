// Chat Module Data Models - Aligned with Backend DTOs

export interface ChatMessage {
  id?: number;
  content: string;
  type?: 'TEXT' | 'FILE' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'CHAT';
  senderUsername: string;
  senderName: string;
  senderProfileImage?: string;
  senderId: number;
  receiverUsername?: string;
  receiverName?: string;
  receiverProfileImage?: string;
  receiverId?: number;
  roomId?: string;
  isRead?: boolean;
  status?: 'SENT' | 'DELIVERED' | 'READ';
  sentAt: Date | string;
  readAt?: Date | string;
  
  // Additional UI fields
  isTyping?: boolean;
  avatar?: string;
  timestamp?: string;
  
  // File-related fields
  fileName?: string;
  fileUrl?: string;
  fileType?: string;
  fileSize?: number;
  
  // Legacy compatibility
  messageType?: 'TEXT' | 'IMAGE' | 'FILE';
}

export interface UserStatus {
  userId: number;
  username: string;
  displayName: string;
  status: 'ONLINE' | 'OFFLINE' | 'AWAY' | 'BUSY';
  lastSeen?: Date | string;
  isOnline?: boolean;
  sessionId?: string;
  deviceInfo?: string;
  ipAddress?: string;
}

export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'CHAT_MESSAGE' | 'SYSTEM_ANNOUNCEMENT' | 'USER_STATUS' | 'FILE_UPLOAD' | 'MENTION' | 'GENERAL';
  senderUsername?: string;
  senderName?: string;
  receiverUsername: string;
  isRead: boolean;
  actionUrl?: string;
  icon?: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  createdAt: Date | string;
  updatedAt?: Date | string;
  
  // Legacy compatibility
  senderId?: number;
}

export interface ChatRoom {
  id?: number;
  roomId: string;
  roomName?: string;
  roomType: 'PRIVATE' | 'GROUP' | 'PUBLIC';
  createdBy?: number;
  isActive?: boolean;
  maxParticipants?: number;
  participants?: ChatRoomParticipant[];
  lastMessage?: string;
  lastMessageTime?: Date | string;
  unreadCount?: number;
}

export interface ChatRoomParticipant {
  id?: number;
  roomId: number;
  userId: number;
  username?: string;
  displayName?: string;
  joinedAt: Date | string;
  leftAt?: Date | string;
  isActive: boolean;
  role: 'ADMIN' | 'MODERATOR' | 'MEMBER';
}

export interface ChatFile {
  id?: number;
  messageId?: number;
  originalName: string;
  storedName: string;
  filePath: string;
  fileSize: number;
  mimeType?: string;
  uploadedBy: number;
  uploadDate: Date | string;
  isDeleted?: boolean;
}

export interface TypingIndicator {
  username: string;
  roomId: string;
  isTyping: boolean;
  timestamp: Date | string;
}

export interface MessageStatus {
  messageId: number;
  status: 'SENT' | 'DELIVERED' | 'READ';
  timestamp: Date | string;
  userId?: number;
}

export interface OnlineUser {
  userId: number;
  username: string;
  displayName: string;
  status: 'ONLINE' | 'AWAY' | 'BUSY';
  lastSeen: Date | string;
  avatar?: string;
}

export interface ChatConversation {
  participantUsername: string;
  participantName: string;
  participantAvatar?: string;
  lastMessage: string;
  lastMessageTime: Date | string;
  unreadCount: number;
  isOnline: boolean;
  roomId: string;
}

export interface SystemAnnouncement {
  id: number;
  title: string;
  message: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  createdAt: Date | string;
  isActive: boolean;
  targetUsers?: string[];
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'CHAT_MESSAGE' | 'USER_STATUS' | 'TYPING' | 'NOTIFICATION' | 'SYSTEM_ANNOUNCEMENT';
  payload: any;
  timestamp: Date | string;
  sender?: string;
}

export interface ConnectionStatus {
  isConnected: boolean;
  lastConnected?: Date | string;
  reconnectAttempts: number;
  error?: string;
}

// Utility Types
export type MessageType = 'TEXT' | 'FILE' | 'IMAGE' | 'VIDEO' | 'AUDIO' | 'CHAT';
export type UserStatusType = 'ONLINE' | 'OFFLINE' | 'AWAY' | 'BUSY';
export type NotificationType = 'CHAT_MESSAGE' | 'SYSTEM_ANNOUNCEMENT' | 'USER_STATUS' | 'FILE_UPLOAD' | 'MENTION' | 'GENERAL';
export type PriorityType = 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
export type RoomType = 'PRIVATE' | 'GROUP' | 'PUBLIC';
export type ParticipantRole = 'ADMIN' | 'MODERATOR' | 'MEMBER';
export type MessageStatusType = 'SENT' | 'DELIVERED' | 'READ';

// Data Transformation Utilities
export class ChatModelMapper {
  static mapBackendChatMessage(backendMessage: any): ChatMessage {
    return {
      id: backendMessage.id,
      content: backendMessage.content,
      type: backendMessage.type,
      senderUsername: backendMessage.senderUsername,
      senderName: backendMessage.senderName,
      senderProfileImage: backendMessage.senderProfileImage,
      senderId: backendMessage.senderId,
      receiverUsername: backendMessage.receiverUsername,
      receiverName: backendMessage.receiverName,
      receiverProfileImage: backendMessage.receiverProfileImage,
      receiverId: backendMessage.receiverId,
      roomId: backendMessage.roomId,
      isRead: backendMessage.isRead,
      status: backendMessage.status,
      sentAt: backendMessage.sentAt,
      readAt: backendMessage.readAt,
      fileName: backendMessage.fileName,
      fileUrl: backendMessage.fileUrl,
      fileType: backendMessage.fileType,
      fileSize: backendMessage.fileSize,
      // Legacy compatibility
      messageType: backendMessage.type,
      timestamp: backendMessage.sentAt
    };
  }

  static mapBackendUserStatus(backendStatus: any): UserStatus {
    return {
      userId: backendStatus.userId,
      username: backendStatus.username,
      displayName: backendStatus.displayName,
      status: backendStatus.status,
      lastSeen: backendStatus.lastSeen,
      isOnline: backendStatus.isOnline,
      sessionId: backendStatus.sessionId,
      deviceInfo: backendStatus.deviceInfo,
      ipAddress: backendStatus.ipAddress
    };
  }

  static mapBackendNotification(backendNotification: any): Notification {
    return {
      id: backendNotification.id,
      title: backendNotification.title,
      message: backendNotification.message,
      type: backendNotification.type,
      senderUsername: backendNotification.senderUsername,
      senderName: backendNotification.senderName,
      receiverUsername: backendNotification.receiverUsername,
      isRead: backendNotification.isRead,
      actionUrl: backendNotification.actionUrl,
      icon: backendNotification.icon,
      priority: backendNotification.priority,
      createdAt: backendNotification.createdAt,
      updatedAt: backendNotification.updatedAt
    };
  }
}
