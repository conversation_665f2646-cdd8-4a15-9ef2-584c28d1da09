import { ComponentFixture, TestBed } from '@angular/core/testing';

import { OrgSearchModalMultipleParamComponent } from './org-search-modal-multiple-param.component';

describe('OrgSearchModalMultipleParamComponent', () => {
  let component: OrgSearchModalMultipleParamComponent;
  let fixture: ComponentFixture<OrgSearchModalMultipleParamComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ OrgSearchModalMultipleParamComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OrgSearchModalMultipleParamComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
