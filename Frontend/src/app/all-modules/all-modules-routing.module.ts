import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { UserGuard } from '../guard/user.guard';
import { AllModulesComponent } from './all-modules.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: '',
    component: AllModulesComponent,
    children: [
      {
        path: 'dashboard',
        loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule),
        data: { preload: true }
      },
      {
        path: 'employees',
        loadChildren: () => import('./employees/employees.module').then(m => m.EmployeesModule),
        canActivate:[UserGuard],
      },
      {
        path: 'payroll',
        loadChildren: () => import('./payroll/payroll.module').then(m => m.PayrollModule)
      },
      {
        path: 'reports',
        loadChildren: () => import('./reports/reports.module').then(m => m.ReportsModule)
      },
      {
        path: 'users',
        loadChildren: () => import('./users/users.module').then(m => m.UsersModule)
      },
      {
        path: 'settings',
        loadChildren: () => import('./settings/settings.module').then(m => m.SettingsModule)
      },
      {
        path: 'shift',
        loadChildren: () => import('./shift/shift.module').then(m => m.ShiftModule)
      },
      {
        path: 'employeereports',
        loadChildren: () => import('./employeereports/employeereports.module').then(m => m.EmployeereportsModule)
      },
      {
        path: 'attendancereports',
        loadChildren: () => import('./attendancereports/attendancereports.module').then(m => m.AttendancereportsModule)
      },
       {
        path: 'leavereports',
        loadChildren: () => import('./leavereports/leavereports.module').then(m => m.LeavereportsModule)
      },
      {
        path: 'approval',
        loadChildren: () => import('./approval/approval.module').then(m => m.ApprovalModule)
      },
      {
        path: 'broadcast',
        loadChildren: () => import('./broadcastx/broadcastx.module').then(m => m.BroadcastxModule)
      },
      {
        path: 'sim',
        loadChildren: () => import('./sim/sim.module').then(m => m.SimModule)
      },
      {
        path: 'self-service',
        loadChildren: () => import('./self-service/self-service.module').then(m => m.SelfServiceModule)
      },
      {
        path: 'irecruitment',
        loadChildren: () => import('./i-recruitment/i-recruitment.module').then(m => m.IRecruitmentModule)
      },
      {
        path: 'logs',
        loadChildren: () => import('./visitorlog/vislog.module').then(m => m.VislogModule)
      },
      {
        path: 'taskmanagement',
        loadChildren: () => import('./taskmanagement/taskmanagement.module').then(m => m.TaskmanagementModule)
      },
      {
        path: 'campusambassador',
        loadChildren: () => import('./campus-ambassador/campus-ambassador.module').then(m => m.CampusAmbassadorModule)
      },
      {
        path: 'letter',
        loadChildren: () => import('./letter/letter.module').then(m => m.LetterModule)
      },
      {
        path: 'performance',
        loadChildren: () => import('./performance/performance.module').then(m=> m.PerformanceModule)
      },
      {
        path: 'loan',
        loadChildren: () => import('./loan/loan.module').then(m=> m.LoanModule)
      },

      {
        path: 'tax',
        loadChildren: () => import('./tax/tax.module').then(m=> m.TaxModule)
      },

      {
        path: 'food-management',
        loadChildren: () => import('./food-management/food-management.module').then(m=> m.FoodManagementModule)
      },

      {
        path: 'complainant-statement',
        loadChildren: () => import('./complainant-statement/complainant-statement.module').then(m=> m.ComplainantStatementModule)
      },

      {
        path: 'system',
        loadChildren: () => import('./system-menu/system-menu.module').then(m => m.SystemMenuModule)
      },

      {
        path: 'base',
        loadChildren: () => import('./base/base.module').then(m => m.BaseModule)
      },

      {
        path:'auth',
        loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
      },

      {
        path:'software-support',
        loadChildren: () => import('./software-support/software-support.module').then(m => m.SoftwareSupportModule)
      },

      {
        path:'file-management',
        loadChildren: () => import('./file-management/file-management.module').then(m => m.FileManagementModule)
      },

      {
        path:'training',
        loadChildren: () => import('./training/training.module').then(m => m.TrainingModule)
      },

      {
        path:'about',
        loadChildren: () => import('./about/about.module').then(m => m.AboutModule)
      },
      
      {
        path:'custom-notification',
        loadChildren: () => import('./custom-notification/custom-notification.module').then(m => m.CustomNotificationModule)
      },

      {
        path:'marketing-and-sales',
        loadChildren: () => import('./marketing-and-sales/marketing-and-sales.module').then(m => m.MarketingAndSalesModule)
      },
      {
        path:'transport-mgt',
        loadChildren: () => import('./transport-mgt/transport-mgt.module').then(m => m.TransportMgtModule)
      },
      {
        path:'chat', loadChildren: () => import('./chat/chat-simple.module').then(m => m.ChatModule)
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AllModulesRoutingModule { }


