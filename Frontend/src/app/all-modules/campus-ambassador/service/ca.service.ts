import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { retry } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CaService {


  public baseUrl = environment.baseUrl;

  constructor(private http: HttpClient) { }

  public sendGetRequest(apiURL, queryParams) {
    console.log('@sendGetRequest');
    return this.http.get<any>(apiURL, { params: queryParams });
  }

  public sendPostRequest(apiURL, formData) {
    console.log('@sendPostRequest');
    return this.http.post(apiURL, formData);
  }


  //upload Document Info
  public uploadDocuments(id, formData) {
    return this.http.post<any>(`${this.baseUrl}/api/applicantDocumentsInfo/uploadPhoto/${id}`, formData);

  }
  public uploaEmpdDocuments(id, formData) {
    return this.http.post<any>(`${this.baseUrl}/emp/infoUpdate/uploadDoc/${id}`, formData);

  }


  public sendPutRequest(apiURL, formData) {
    console.log('@sendPutRequest');
    return this.http.put(apiURL, formData);
  }

  public sendDeleteRequest(apiURL, formData) {
    console.log('@sendDeleteRequest');
    return this.http.delete(apiURL, formData);

  }

  //upload profile image file
  public uploadProfileImage(id, formData) {
    return this.http.post<any>(`${this.baseUrl}/ca/candidateInfo/uploadPhoto/${id}`, formData);

  }

  public topSheetReport(rptParams) {
    const httpOptions = {
      responseType: 'arraybuffer' as 'json'
    };

    let param: any = rptParams;
    return this.http.get(`${this.baseUrl}/reports/campusAmbassadorTopsheet?rptFileName=${param.rptFileName}&P_ID=${param.id}&outputFileName=${param.rptFileName}`, httpOptions);


  }

  public idCardReport(rptParams) {
    const httpOptions = {
      responseType: 'arraybuffer' as 'json'
    };

    let param: any = rptParams;
    return this.http.get(`${this.baseUrl}/reports/empDailyPunch?rptFileName=${param.rptFileName}&code=${param.id}&outputFileName=${param.rptFileName}`, httpOptions);


  }

}
