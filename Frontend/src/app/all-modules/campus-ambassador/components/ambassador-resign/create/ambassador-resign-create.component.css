.content {
    padding: 15px;
}

.profile-view{
    margin-bottom: 10%;
}

/* ================= Default Template Color ================== */
xx-.form-control {
    border-color: #e3e3e3;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #ccc;
    box-shadow: none;
    outline: 0 none;
}



/* ================= Purpale Color =========================== */
input.form-control,
select.form-control,
textarea.form-control {
	border-color: #4BA1D9;
	border-left: 3px solid #4BA1D9;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
    border-color: #705CBA;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================= Green Color ============================= */
xx-input.form-control {
    border-color: #d4cdcd;
    border-left: 3px solid green;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #37a000;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================== Blue Color ============================== */
xx-input.form-control {
    border-color: #66afe9;
    border-left: 3px solid #66afe9;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}


input.ng-invalid{
    border:1px solid red;
}
select.ng-invalid{
    border:1px solid red;
}
textarea.ng-invalid{
    border:1px solid red;
}

input.ng-valid {
    border-left: 2px solid green
}

select.ng-valid {
    border-left: 2px solid green;
}

textarea.ng-valid {
    border-left: 2px solid green;
}
