import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormArray, Validators, FormControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { ALKP } from 'src/app/all-modules/settings/common/models/alkp';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';
import { CaService } from '../../../service/ca.service';
declare const $: any;

@Component({
  selector: 'app-ambassador-resign-create',
  templateUrl: './ambassador-resign-create.component.html',
  styleUrls: ['./ambassador-resign-create.component.css']
})
export class AmbassadorResignCreateComponent implements OnInit {
  public baseUrl = environment.baseUrl;
  public resignTypeData: any = [];
  imgSrc: string | ArrayBuffer;
  imageSrc: any;
  signatureSource: any;
  empLoginCode: any;
  listData: any = [];
  id: any;
  public basicInfoForm: FormGroup;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private employeeService: EmployeeService,
    private toastr: ToastrService,
    private router: Router,
    private caService: CaService,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService

  ) { }

  ngOnInit(): void {
    this.initBasicInfoForm();
    this.getresignType();
  }


  initBasicInfoForm() {

    this.basicInfoForm = this.formBuilder.group({
      id: [""],
      resignType: ["", [Validators.required]],
      resignDate: ["", [Validators.required]],
      resignRemarks: ["", [Validators.required]],
    });

  }


  // --------------------------- Get Resign Type ----------------------


  getresignType() {
    let keyword = "RESIGN_TYPE";
    let apiURL = this.baseUrl + "/alkp/searchByParent/" + keyword;

    this.caService.sendGetRequest(apiURL, {}).subscribe(
      (resData: any) => {

        this.resignTypeData = resData;

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // ---------------- Set Employee Login Code ----------------------

  setEmployeeId(loginCode) {
    this.empLoginCode = loginCode;
  }


  // ------------------------ Get Employee Info By Login Code ---------------------------

  getEmployeeInfo() {
    this.listData = [];
    let apiURL = this.baseUrl + "/ca/candidateInfo/getLists?pageNum=1&pageSize=10&refCode=" + this.empLoginCode + "&rEntityName=CandidateInfo&rReqType=getListData";
    let queryParams: any = {};
    this.spinnerService.show();
    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response?.objectList[0];
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning("Ambassador not found with this ID");
        this.spinnerService.hide();
      }
    );
  }



  // =================== Update Status ===================

  updateStatus() {

    if (!this.listData?.id) {
      this.toastr.warning("Please Search a Candidate First")
      return;
    }
    const apiURL = this.baseUrl + '/ca/candidateInfo/statusUpdate/' + this.listData?.id;
    const formData: any = {};
    formData.enable = false;
    formData.status = this.basicInfoForm?.controls?.resignType.value;
    //process Date
    formData.resignDate = (this.basicInfoForm?.controls?.resignDate.value) ? this.commonService.format_Date_Y_M_D(this.basicInfoForm?.controls?.resignDate.value) : null;
    formData.resignRemarks = this.basicInfoForm?.controls?.resignRemarks.value;
    this.spinnerService.show();
    this.caService.sendPutRequest(apiURL, formData).subscribe((res: any) => {
      if (res.status === true) {
        this.spinnerService.hide();
        this.toastr.success("Updated Successfully");
        this.router.navigate(["/campusambassador/candidate/resign"], { relativeTo: this.route });
      } else {
        this.spinnerService.hide();
        this.toastr.warning(res.message);
      }
    })

  }

}
