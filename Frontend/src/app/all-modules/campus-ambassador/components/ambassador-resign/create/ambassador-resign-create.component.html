<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Campus Ambassador</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Ambassador Resign</b></span></li>
                    <li class="breadcrumb-item active">Create</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">

                <a class="btn add-btn" routerLink="/campusambassador/candidate/resign"><i class="fa fa-share"> </i> Back
                    To
                    List </a>

            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Search Filter -->
    <div class="card mb-2" style="background-color:transparent;">
        <div class="card-body p-3">

            <div class="row">

                <div class="col-sm-6 col-md-4">
                    <div class="form-group">
                        <input type="text" class="form-control floating" placeholder="CA ID"
                            (input)="setEmployeeId($event.target.value)" (keyup.enter)="getEmployeeInfo()">
                    </div>
                </div>


                <div class="col-sm-6 col-md-2">
                    <button class="btn btn-success btn-ripple" (click)="getEmployeeInfo()"> <i class="fa fa-search"></i>
                        Search</button>
                </div>
            </div>

        </div>
    </div>
    <!-- /Search Filter -->

    <div class="tab-content">

        <!-- Profile Info Tab -->
        <div id="hrCrEmp" class="pro-overview tab-pane fade show active">
            <div class="card customCard">
                <div class="card-body">


                    <h3 class="card-title"> Basic Information</h3>
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="profile-view ">
                                <div class="profile-img-wrap">
                                    <div class="profile-img">
                                        <a *ngIf="listData?.profilePicPath" [routerLink]="" class="avatar"><img
                                                src="{{ baseUrl + listData?.profilePicPath }}" alt="" />
                                        </a>

                                        <a *ngIf="listData?.profilePicPath == null || 
                                        listData?.profilePicPath =='' || 
                                        listData?.profilePicPath?.length<1" [routerLink]="" class="avatar"
                                            data-target="#profile_Image" data-toggle="modal"><img id="companyLogo"
                                                data-flag="1" src="assets/img/user-icon/u-sq-pic.jpg" alt="" /></a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <form (ngSubmit)="updateStatus()" [formGroup]="basicInfoForm">
                        <div class="row ">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Name</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.name}}" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Designation</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.corporateInfo?.officialDesignation | titlecase}}" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Institute</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.institutionalInfo?.campusName | titlecase}}" readonly>

                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Email</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.email}}" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Phone</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.phone}}" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Joining Date</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.corporateInfo?.joiningDate|date:'mediumDate'}}" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Expire Date</label>
                                    <input class="form-control" class="form-control" type="text"
                                        value="{{listData?.corporateInfo?.expiryDate|date:'mediumDate'}}" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Resign Type</label>
                                    <select id="status" class="select form-control" formControlName="resignType">
                                        <option value="">Select Any</option>
                                        <option *ngFor="let item of resignTypeData" [value]='item.title'>
                                            {{item.title}}
                                        </option>
                                    </select>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Resign Date</label>

                                    <div class="cal-icon">
                                        <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker
                                            type="text" placeholder="DD-MM-YYYY"
                                            [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                                            formControlName="resignDate">
                                    </div>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Remarks</label>

                                    <textarea class="form-control" formControlName="resignRemarks" rows="1"></textarea>

                                </div>
                            </div>

                        </div>

                        <div class="submit-section">
                            <button class="btn btn-primary submit-btn" type="submit" [disabled]="basicInfoForm.invalid">

                                Update
                            </button>
                        </div>

                    </form>
                </div>
            </div>
        </div>

    </div>

</div>

<!-- /Page Content -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>