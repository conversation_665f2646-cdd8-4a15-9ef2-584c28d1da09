<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Campus Ambassador</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Ambassador</b></span></li>
        </ul>
      </div>

    </div>
  </div>
  <!-- /Page Header -->

  <!-- Search Filter -->
  <div class="card mb-2" style="background-color:transparent;">
    <div class="card-body p-3">

      <div class="row">

        <div class="col-md-2">
          <div class="form-group">
            <input type="text" class="form-control" id="caId" (input)="searchByCaId($event.target.value)"
              placeholder="CA ID" (keyUp.enter)="_getListData()">

          </div>
        </div>

        <div class="col-md-2">
          <div class="form-group">
            <select id="status" class="select form-control" (change)="searchByStatus($event.target.value)">
              <option value="">:: All Status ::</option>
              <option value="active" selected>Active</option>
              <option *ngFor="let item of resignTypeData" [value]='item.title'>
                {{item.title}}
              </option>
            </select>

          </div>
        </div>


        <div class="col-md-4">
          <button class="btn btn-success btn-ripple" (click)="searchBySearchButton()"> <i class="fa fa-search"></i>
            Search</button> &nbsp;&nbsp;&nbsp;

          <button type="button" class="btn btn-danger btn-ripple" (click)="clearFilter()"> <i class="fa fa-eraser">
              Clear</i>
          </button>
        </div>
      </div>

    </div>
  </div>
  <!-- /Search Filter -->

  <div class="row">
    <div class="col-md-12">

      <div class="card customCard">

        <div class="card-header">
          <div class="card-tools">
            <a routerLink="/campusambassador/candidate/create" class="btn btn-outline-primary"><i
                class="fa fa-plus"></i> Add New Ambassador
              &nbsp;&nbsp;&nbsp;</a>
          </div>
        </div>

        <div class="card-body">

          <div class="table-responsive">

            <div class="d-flex justify-content-start pb-1">
              <div class="pgn-displayDataInfo">
                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (1) )
                  }} to {{configPgn.pngDiplayLastSeq}} of {{configPgn.totalItem}} ) entries</span>
              </div>
            </div>

            <table id="genListTable" class="table table-bordered table-striped custom-table table-hover ">
              <thead>
                <tr>
                  <th class="text-center">SL</th>

                  <th class="text-center">ID</th>
                  <th class="text-center">Photo</th>

                  <th class="text-center">Ambassador</th>
                  <th class="text-center">Phone <br> Email</th>
                  <th class="text-center">Rocket Number </th>
                  <th class="text-center">Blood Group <br> Date of Birth </th>
                  <th class="text-center">Joining Date <br> Expire Date</th>
                  <th class="text-center">Status</th>
                  <th class="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let thisObj of listData | paginate : configPgn; let i = index"
                  [class.active]="i == currentIndex">

                  <td class="text-center">{{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) ) }}</td>
                  <td class="text-center">{{thisObj.caIdCode}}</td>
                  <td class="text-center" routerLink="/campusambassador/candidate/profiles/{{thisObj.id}}">

                    <span class="table-avatar">

                      <img class="img-avatar text-center" alt="" style="height: 70px; width: 70px;"
                        src="{{ baseUrl + thisObj.profilePicPath}}">
                    </span>


                  </td>


                  <td>
                    <h2><a style="color: #125260;"
                        routerLink="/campusambassador/candidate/profiles/{{thisObj.id}}">{{thisObj.name}}</a> </h2><br>
                    <span> {{thisObj.officialDesignation | titlecase }}</span> <br>
                    <span> {{thisObj.universityName | titlecase }}</span>


                  </td>

                  <td class="text-center">

                    <span style="color:#25B6B2;">
                      {{thisObj.officialSimNo ? thisObj.officialSimNo : ' -- ' | titlecase
                      }}

                    </span> (Official)<br>
                    {{thisObj.personalPhone}} (Personal) <br>

                    <span style="color: #4B68E7;"> {{thisObj.email}} </span>

                  </td>

                  <td class="text-center">

                    {{thisObj.rocketNumber}}

                  </td>

                  <td class="text-center">

                    <span style="color: red;"> <i class="fa fa-tint" aria-hidden="true"> </i>
                      {{thisObj?.bloodGroup}}
                    </span>

                    <br>

                    {{thisObj.dateOfBirth|date:'mediumDate' }}


                  </td>

                  <td class="text-center"><span class="text-success">{{thisObj?.joiningDate|date:'mediumDate' }}</span>
                    <br>
                    <span class="text-danger">{{ getDateAfterOneYear(thisObj?.joiningDate) | date: 'mediumDate'
                      }}</span>
                  </td>

                  <td class="text-center">

                    {{thisObj?.status | titlecase}}
                    <!-- <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="customSwitch{{thisObj?.id}}"
                        [checked]="thisObj?.user?.enabled" (change)="updateEnabled(thisObj?.id , $event.target.checked)"
                        [disabled]="!thisObj?.user?.enabled">
                      <label class="custom-control-label" for="customSwitch{{thisObj?.id}}"></label>
                    </div> -->
                  </td>

                  <td>
                    <a class="btn btn-sm btn-primary" title="View"
                      routerLink="/campusambassador/candidate/profiles/{{thisObj.id}}"><i class="fa fa-eye"></i></a>
                    &nbsp;
                    <a class="btn btn-sm btn-info" title="Edit"
                      routerLink="/campusambassador/candidate/edit/{{thisObj?.id}}"><i
                        class="fa fa-pencil"></i></a>&nbsp;
                    <!-- <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#delete_entity" title="Delete"
                      (click)="tempId = thisObj.id">
                      <i class="fa fa-trash-o"></i>
                    </a> &nbsp; -->

                    <a class="btn btn-sm btn-warning" title="ID Card View"
                      routerLink="/campusambassador/candidate/id-card/{{thisObj?.caIdCode}}" target="_blank"><i
                        class="fa fa-id-card-o"></i></a>

                    &nbsp;

                    <a *ngIf="thisObj.document" class="btn btn-sm btn-info" title="View Top Sheet"
                      (click)=redirectToImage(thisObj?.document)><i class="fa fa-file"></i></a>


                  </td>
                </tr>

                <tr *ngIf="listData?.length === 0">
                  <td colspan="11">
                    <h5 style="text-align: center;">No data found</h5>
                  </td>
                </tr>
              </tbody>
            </table>


            <div class="d-flex justify-content-end ">

              <div class="" [formGroup]="myFromGroup">
                Items per Page
                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption" formControlName="pageSize">
                  <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                    {{ size }}
                  </option>
                </select>
              </div>

              <div class="pgn-pageSliceCt">
                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                  (pageChange)="handlePageChange($event)">
                </pagination-controls>
              </div>

            </div>

          </div>

        </div>
      </div>

    </div>
  </div>

</div>



<!-- Delete Modal -->
<div class="modal custom-modal fade" id="delete_entity" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="form-header">
          <h3>Delete Item</h3>
          <p>Are you sure want to delete?</p>
        </div>
        <div class="modal-btn delete-action">
          <div class="row">
            <div class="col-6">
              <a class="btn btn-primary continue-btn" (click)="deleteEntityData(tempId)">Delete</a>
            </div>
            <div class="col-6">
              <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /Delete Modal -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>