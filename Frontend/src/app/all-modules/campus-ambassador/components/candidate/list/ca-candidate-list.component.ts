import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';
import { CaService } from '../../../service/ca.service';
declare const $: any;
@Component({
  selector: 'app-ca-candidate-list',
  templateUrl: './ca-candidate-list.component.html',
  styleUrls: ['./ca-candidate-list.component.css']
})
export class CaCandidateListComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  private polling: any;

  public pipe = new DatePipe('en-US');
  public myFromGroup: FormGroup;
  public resignTypeData: any = [];
  public configPgn: any;
  public listData: any = [];
  public editId: any;
  public tempId: any;

  // search fields for
  private status: string;
  private caIdCode: string;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,
    private caService: CaService,
    private loginService: LoginService
  ) {
    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };
  }

  ngOnInit(): void {

    // set init params
    this.myFromGroup = new FormGroup({
      pageSize: new FormControl()
    });
    this.myFromGroup.get('pageSize').setValue(this.configPgn.pageSize);

    this.status = "active";

    this._getListData();
    this.getresignType();
  }



  // --------------------------- Get Resign Type ----------------------


  getresignType() {
    let keyword = "RESIGN_TYPE";
    let apiURL = this.baseUrl + "/alkp/searchByParent/" + keyword;

    this.caService.sendGetRequest(apiURL, {}).subscribe(
      (resData: any) => {

        this.resignTypeData = resData;

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }


  // ================= GET ALL LIST DATA =========================

  _getListData() {
    const apiURL = this.baseUrl + '/ca/candidateInfo/getLists';

    let queryParams: any = {};
    const params = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);
    queryParams = params;
    queryParams.rEntityName = 'CandidateInfo';
    queryParams.rReqType = 'getListData';

    this.spinnerService.show();
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response?.objectList;
        this.configPgn.totalItem = response?.totalItems;
        this.configPgn.totalItems = response?.totalItems;
        this.setDisplayLastSequence();
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );

  }


  getDateAfterOneYear(joiningDate: Date): Date {
    const date = new Date(joiningDate);
    date.setFullYear(date.getFullYear() + 1);
    return date;
  }



  // =================== ACTIVE INACTIVE CA ===================

  updateEnabled(id: any, status) {
    const apiURL = this.baseUrl + '/ca/candidateInfo/statusUpdate/' + id;
    const formData: any = {};
    formData.enable = status;
    if (status == true) {
      formData.status = 'active'
    }
    else {
      formData.status = 'inactive'
    }
    this.spinnerService.show();
    this.caService.sendPutRequest(apiURL, formData).subscribe((res: any) => {
      if (res.status === true) {
        this.spinnerService.hide();
        this.toastr.success(res.message, 'Success');
        this._getListData();
      } else {
        this.spinnerService.hide();
        this.toastr.warning(res.message, 'Info');
      }
    })

  }

  searchByCaId(id: any) {
    this.caIdCode = id;
  }
  searchByStatus(status: any) {
    this.status = status;
  }

  searchBySearchButton() {
    this._getListData();
  }

  clearFilter() {
    $('#caId').val("");
    $('#status').val("");
    this.status = '';
    this.caIdCode = '';
    this._getListData();
  }

  public _getSearchData() {
    this._getListData();

  }


  private getUserQueryParams(page: number, pageSize: number): any {

    const params: any = {};

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    // push other attributes
    if (this.status) {
      params[`status`] = this.status;
    }

    if (this.caIdCode) {
      params[`refCode`] = this.caIdCode;
    }

    return params;

  }


  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }


  // ========================= DELETE CA DATA ========================

  deleteEntityData(id: any) {
    const apiURL = this.baseUrl + '/ca/candidateInfo/delete/' + id;

    const formData: any = {};
    formData.rEntityName = 'CandidateInfo';
    formData.rActiveOperation = 'delete';
    formData.reqId = id;
    this.spinnerService.show();
    this.caService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {
        if (response.status === true) {
          this.toastr.success(response.message, 'Success');
          //hide modal
          $('#delete_entity').modal('hide');
          this._getListData();
        } else {
          //hide modal
          $('#delete_entity').modal('hide');
          this.toastr.warning(response.message, 'Error');
        }
      },
      (error) => {
        $('#delete_entity').modal('hide');
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }



  // pagination handling methods start -----------------------------------------------------------------------
  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {
    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this._getListData();
  }
  handlePageSizeChange(event: any): void {
    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this._getListData();
  }
  // pagination handling methods end -------------------------------------------------------------------------

}
