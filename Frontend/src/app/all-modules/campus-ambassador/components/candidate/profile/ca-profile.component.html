<!-- Page Content -->
<div class="content container-fluid">


  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Campus Ambassador</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Ambassador</b></span></li>
          <li class="breadcrumb-item active">Profile of <span
              style="color:#25B6B2;"><b>{{personalInfo?.name}}</b></span> </li>
        </ul>
      </div>
      <div class="col-auto float-right ml-auto">

        <a class="btn add-btn" routerLink="/campusambassador/candidate/list"><i class="fa fa-share"> </i> Back To
          List </a>

      </div>
    </div>
  </div>
  <!-- /Page Header -->

  <div class="card mb-0 customCard">
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <div class="profile-view">
            <div class="profile-img-wrap">
              <div class="profile-img">
                <a *ngIf="personalInfo.profilePicPath" [routerLink]="" class="avatar" data-target="#profile_Image"
                  data-toggle="modal"><img src="{{ baseUrl + personalInfo.profilePicPath }}" alt="" />
                </a>

                <a *ngIf="!personalInfo.profilePicPath" [routerLink]="" class="avatar" data-target="#profile_Image"
                  data-toggle="modal"><img id="companyLogo" data-flag="1" src="assets/img/user-icon/u-sq-pic.jpg"
                    alt="" /></a>

              </div>
            </div>
            <div class="profile-basic">
              <div class="row">
                <div class="col-md-5">
                  <div class="profile-info-left">
                    <br>
                    <h3 class="user-name m-t-0 mb-0">{{personalInfo?.name}}</h3>

                    <div class="staff-id">ID : {{personalInfo?.caIdCode}}</div>
                    <div class="staff-id">Official Designation : {{personalInfo?.corporateInfo?.officialDesignation}}
                    </div>
                    <h6>
                      Status : <span class="text-success staff-id profilefront"
                        *ngIf="personalInfo.status == 'Active' || personalInfo.status == 'active' "> {{
                        personalInfo.status | titlecase }}</span>

                      <span class="text-danger staff-id profilefront"
                        *ngIf="personalInfo.status != 'Active' && personalInfo.status != 'active'  "
                        title="{{thisObj?.resignRemarks}}" style="cursor: pointer;"> {{
                        personalInfo.status | titlecase }}</span>
                    </h6>
                    <div class="small doj text-muted">Joining date : {{personalInfo.corporateInfo?.joiningDate ?
                      (personalInfo.corporateInfo?.joiningDate |date:'mediumDate'): 'N/A'}}
                      (<span>{{personalInfo.corporateInfo?.remainingDays}} days remaining</span>)
                    </div>
                  </div>
                </div>
                <div class="col-md-7">
                  <ul class="personal-info">
                    <li>
                      <div class="title">Email:</div>
                      <div class="text"><a href="">{{personalInfo.email ? personalInfo?.email: 'N/A' }}</a></div>
                    </li>

                    <li>
                      <div class="title">Address:</div>
                      <div class="text">{{personalInfo.permanentAddress ? personalInfo?.permanentAddress: 'N/A' }}</div>
                    </li>

                    <li>
                      <div class="title">Blood Group:</div>
                      <div class="text"><span style="color: red;"> <i class="fa fa-tint" aria-hidden="true"> </i>
                          {{personalInfo.bloodGroup ? personalInfo.bloodGroup: 'N/A' }}
                        </span></div>
                    </li>

                    <li>
                      <div class="title">National Id:</div>
                      <div class="text">{{personalInfo.nationalId ? personalInfo?.nationalId: 'N/A' }}</div>
                    </li>

                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card tab-box customCard">
    <div class="row user-tabs">
      <div class="col-lg-12 col-md-12 col-sm-12 line-tabs">
        <ul class="nav nav-tabs nav-tabs-bottom">
          <li class="nav-item"><a href="#ca_personal" data-toggle="tab" class="nav-link active">Personal Info </a>
          </li>
          <li class="nav-item"><a (click)="getInstitutelInfoByCandidateId(personalInfo.id)" href="#ca_institute"
              data-toggle="tab" class="nav-link">Institutional Info </a></li>

          <li class="nav-item"><a (click)="getCorporateInfoByCandidateId(personalInfo.id)" href="#ca_corporate"
              data-toggle="tab" class="nav-link">Corporate Info</a></li>

          <li class="nav-item"><a (click)="getAssetInfoByCandidateId(personalInfo.id)" href="#ca_asset"
              data-toggle="tab" class="nav-link">Asset Info</a></li>

          <li class="nav-item"><a (click)="getScholarshipInfoByCandidateId(personalInfo.id)" href="#ca_scholarship"
              data-toggle="tab" class="nav-link">Scholarship Info</a></li>

          <li class="nav-item"><a (click)="_getKpiByPersonalInfoId(personalInfo.id)" href="#ca_kpi" data-toggle="tab"
              class="nav-link">Key Performance Indicator (KPI)</a></li>

        </ul>
      </div>
    </div>
  </div>

  <div class="tab-content">

    <!-- Profile Info Tab -->
    <div id="ca_personal" class="pro-overview tab-pane fade show active">
      <div class="row">
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Basic Information</h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Full Name</div>
                  <div class="text">{{personalInfo.name?personalInfo.name:'N/A'}}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Phone</div>
                  <div class="text">{{personalInfo.phone ?personalInfo.phone :'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Alternate Email</div>
                  <div class="text"><a href="">{{personalInfo.alternateEmail? personalInfo.alternateEmail:'N/A'}}</a>
                  </div>
                </li>
                <hr>
                <li>
                  <div class="title">Alternate Email</div>
                  <div class="text">{{personalInfo.alternateEmail ?personalInfo.alternateEmail: 'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Date of Birth</div>
                  <div class="text">{{personalInfo.dateOfBirth ? (personalInfo.dateOfBirth |date:'mediumDate'): 'N/A'}}
                  </div>
                </li>
                <hr>
                <li>
                  <div class="title">Birth Certificate No</div>
                  <div class="text">{{personalInfo.birthCertificateNo ? personalInfo.birthCertificateNo: 'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Physical Illness</div>
                  <div class="text">{{personalInfo.physicalIllness ? personalInfo.physicalIllness: 'No' }}</div>
                </li>


              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Family Information<a></a></h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Father name </div>
                  <div class="text">{{personalInfo.fatherName ? personalInfo.fatherName:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Father Profession</div>
                  <div class="text">{{personalInfo.fatherProfession ? personalInfo.fatherProfession:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Father Phone </div>
                  <div class="text">{{personalInfo.fatherPhone ? personalInfo.fatherPhone:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Mother name </div>
                  <div class="text">{{personalInfo.motherName ? personalInfo.motherName:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Mother Profession</div>
                  <div class="text">{{personalInfo.motherProfession ? personalInfo.motherProfession:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Mother Phone </div>
                  <div class="text">{{personalInfo.motherPhone ? personalInfo.motherPhone:'N/A' }}</div>
                </li>

              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 d-flex ">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Address Information <a></a>
              </h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Present Address</div>
                  <div class="text">{{personalInfo.presentAddress ? personalInfo.presentAddress:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Permanent Address</div>
                  <div class="text">{{personalInfo.permanentAddress ? personalInfo.permanentAddress:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Local Guardian</div>
                  <div class="text">{{personalInfo.localGuardianPhone ? personalInfo.localGuardianPhone:'N/A' }}</div>
                </li>


              </ul>
            </div>
          </div>
        </div>
        <!-- <div class="col-md-6 d-flex">
                  <div class="card profile-box flex-fill">
                      <div class="card-body">
                          <h3 class="card-title">Experience <a class="edit-icon" data-toggle="modal"
                                  data-target="#experience_info"><i class="fa fa-pencil"></i></a></h3>
                          <div class="experience-box">
                              <ul class="experience-list">
                                  <li>
                                      <div class="experience-user">
                                          <div class="before-circle"></div>
                                      </div>
                                      <div class="experience-content">
                                          <div class="timeline-content">
                                              <a href="#/" class="name">Web Designer at Zen Corporation</a>
                                              <span class="time">Jan 2013 - Present (5 years 2 months)</span>
                                          </div>
                                      </div>
                                  </li>
                                  <li>
                                      <div class="experience-user">
                                          <div class="before-circle"></div>
                                      </div>
                                      <div class="experience-content">
                                          <div class="timeline-content">
                                              <a href="#/" class="name">Web Designer at Ron-tech</a>
                                              <span class="time">Jan 2013 - Present (5 years 2 months)</span>
                                          </div>
                                      </div>
                                  </li>
                                  <li>
                                      <div class="experience-user">
                                          <div class="before-circle"></div>
                                      </div>
                                      <div class="experience-content">
                                          <div class="timeline-content">
                                              <a href="#/" class="name">Web Designer at Dalt Technology</a>
                                              <span class="time">Jan 2013 - Present (5 years 2 months)</span>
                                          </div>
                                      </div>
                                  </li>
                              </ul>
                          </div>
                      </div>
                  </div>
              </div> -->
      </div>
    </div>
    <!-- /Profile Info Tab -->

    <!-- Projects Tab -->
    <div class="tab-pane fade" id="ca_institute">
      <div class="row">
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Instiute Information</h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Campus Name</div>
                  <div class="text">{{institutionalInfo.campusName?institutionalInfo.campusName:'N/A'}}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Class Name</div>
                  <div class="text">{{institutionalInfo.className ?institutionalInfo.className :'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Shift Name</div>
                  <div class="text"><a href="">{{institutionalInfo.shiftName? institutionalInfo.shiftName:'N/A'}}</a>
                  </div>
                </li>
                <hr>
                <li>
                  <div class="title">Institute ID</div>
                  <div class="text">{{institutionalInfo.idNo ?institutionalInfo.idNo: 'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Coordinator Name</div>
                  <div class="text">{{ institutionalInfo.campusCoordinatorName ? institutionalInfo.campusCoordinatorName
                    : 'N/A'}}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Coordinator Desg.</div>
                  <div class="text">{{institutionalInfo.campusCoordinatorDesignation ?
                    institutionalInfo.campusCoordinatorDesignation: 'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Coordinator Dept.</div>
                  <div class="text">{{institutionalInfo.campusCoordinatorDepartment ?
                    institutionalInfo.campusCoordinatorDepartment: 'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Coordinator Phone</div>
                  <div class="text">{{institutionalInfo.campusCoordinatorPhone ?
                    institutionalInfo.campusCoordinatorPhone: 'N/A' }}</div>
                </li>




              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">SSC Information<a></a></h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Title</div>
                  <div class="text">{{institutionalInfo.sscTitle ? institutionalInfo.sscTitle:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Institute</div>
                  <div class="text">{{institutionalInfo.sscInstitute ? institutionalInfo.sscInstitute:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Group</div>
                  <div class="text">{{institutionalInfo.sscGroup ? institutionalInfo.sscGroup:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Passing Year</div>
                  <div class="text">{{institutionalInfo.sscPassingYear ? institutionalInfo.sscPassingYear:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Result</div>
                  <div class="text">{{institutionalInfo.sscResult ? institutionalInfo.sscResult:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">GPA-(Out of 5)</div>
                  <div class="text">{{institutionalInfo.sscGpaOutOf5 ? institutionalInfo.sscGpaOutOf5:'N/A' }}</div>
                </li>
                <hr>

                <!-- <li>
                  <div class="title">GPA(Out of 4)</div>
                  <div class="text">{{institutionalInfo.sscGpaOutOf4 ? institutionalInfo.sscGpaOutOf4:'N/A' }}</div>
                </li> -->


              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">HSC Information<a></a></h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Title</div>
                  <div class="text">{{institutionalInfo.hscTitle ? institutionalInfo.hscTitle:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Institute</div>
                  <div class="text">{{institutionalInfo.hscInstitute ? institutionalInfo.hscInstitute:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Group</div>
                  <div class="text">{{institutionalInfo.hscGroup ? institutionalInfo.hscGroup:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Passing Year</div>
                  <div class="text">{{institutionalInfo.hscPassingYear ? institutionalInfo.hscPassingYear:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Result</div>
                  <div class="text">{{institutionalInfo.hscResult ? institutionalInfo.hscResult:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">GPA-(Out of 5)</div>
                  <div class="text">{{institutionalInfo.hscGpaOutOf5 ? institutionalInfo.hscGpaOutOf5:'N/A' }}</div>
                </li>
                <hr>

                <!-- <li>
                  <div class="title">GPA-(Out of 4)</div>
                  <div class="text">{{institutionalInfo.hscGpaOutOf4 ? institutionalInfo.hscGpaOutOf4:'N/A' }}</div>
                </li> -->


              </ul>
            </div>
          </div>
        </div>
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Graduation Information<a></a></h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Institute</div>
                  <div class="text">{{institutionalInfo.campusName ? institutionalInfo.campusName:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Title</div>
                  <div class="text">{{institutionalInfo.bscTitle ? institutionalInfo.bscTitle:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Degree</div>
                  <div class="text">{{institutionalInfo.bscSubjectOrDegree ? institutionalInfo.bscSubjectOrDegree:'N/A'
                    }}
                  </div>
                </li>
                <hr>

                <li>
                  <div class="title">Passing Year</div>
                  <div class="text">{{institutionalInfo.bscPassingYear ? institutionalInfo.bscPassingYear:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Result</div>
                  <div class="text">{{institutionalInfo.bscResult ? institutionalInfo.bscResult:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">GPA-(Out of 4)</div>
                  <div class="text">{{institutionalInfo.bscGpaOutOf4 ? institutionalInfo.bscGpaOutOf4:'N/A' }}</div>
                </li>
                <hr>



              </ul>
            </div>
          </div>
        </div>

        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Post Graduation Information<a></a></h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Institute</div>
                  <div class="text">{{institutionalInfo.campusName ? institutionalInfo.campusName:'N/A' }}</div>
                </li>
                <hr>
                <li>
                  <div class="title">Title</div>
                  <div class="text">{{institutionalInfo.mscTitle ? institutionalInfo.mscTitle:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Degree</div>
                  <div class="text">{{institutionalInfo.mscSubjectOrDegree ? institutionalInfo.mscSubjectOrDegree:'N/A'
                    }}
                  </div>
                </li>
                <hr>

                <li>
                  <div class="title">Passing Year</div>
                  <div class="text">{{institutionalInfo.mscPassingYear ? institutionalInfo.mscPassingYear:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Result</div>
                  <div class="text">{{institutionalInfo.mscResult ? institutionalInfo.mscResult:'N/A' }}</div>
                </li>
                <hr>

                <li>
                  <div class="title">GPA-(Out of 4)</div>
                  <div class="text">{{institutionalInfo.mscGpaOutOf4 ? institutionalInfo.mscGpaOutOf4:'N/A' }}</div>
                </li>
                <hr>



              </ul>
            </div>
          </div>
        </div>
      </div>

    </div>
    <!-- /Projects Tab -->

    <!-- Bank Statutory Tab -->
    <div class="tab-pane fade" id="ca_corporate">

      <div class="row">
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Corporate Information</h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Designation</div>
                  <div class="text">{{corporateInfo.officialDesignation?corporateInfo.officialDesignation:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Department</div>
                  <div class="text">{{corporateInfo.department?corporateInfo.department:'N/A'}}</div>
                </li>
                <hr>


                <li>
                  <div class="title">Section</div>
                  <div class="text">{{corporateInfo.section?corporateInfo.section:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Sim No.</div>
                  <div class="text">{{corporateInfo.officialSimNo?corporateInfo.officialSimNo:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Sim Balance</div>
                  <div class="text">{{corporateInfo.allocatedSimBal?corporateInfo.allocatedSimBal:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Official Mail Address</div>
                  <div class="text">{{corporateInfo.officialMailAddress?corporateInfo.officialMailAddress:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Rocket Number</div>
                  <div class="text">{{corporateInfo.officialSimNo?corporateInfo.rocketNumber:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Campus mail</div>
                  <div class="text">{{corporateInfo.campusMail?corporateInfo.campusMail:'N/A'}}</div>
                </li>
                <hr>



              </ul>
            </div>
          </div>
        </div>

        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Ambassador List </h3>

              <table id="genListTable" class="table table-striped custom-table">
                <thead>
                  <tr>
                    <th>SL</th>
                    <th>Name</th>
                    <th>Phone</th>
                    <th>Email</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let thisObj of myAmbassador let i = index">


                    <td>{{i+1}}</td>
                    <td>{{thisObj.name}}</td>
                    <td>{{thisObj.phone}}</td>
                    <td>{{thisObj.email}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>


        </div>

      </div>

    </div>
    <!-- /Bank Statutory Tab -->

    <!-- Asset Tab -->

    <div class="pro-overview tab-pane fade show" id="ca_asset">
      <div class="card customCard">
        <div class="card-body">
          <h3>Asset List</h3>

          <table id="eduListTable" class="table table-striped custom-table">
            <thead>
              <tr>
                <th>Asset Name</th>
                <th>Asset Type</th>
                <th>Asset Description</th>
                <th>Asset Quantity</th>
                <th>Asset Price</th>
                <th>Asset Total Price</th>
                <th>Remarks</th>

              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let thisObj of assetInfo">
                <td>{{ thisObj.assetName }} </td>
                <td>{{ thisObj.assetType }}</td>
                <td>{{ thisObj.assetDescription }}</td>
                <td>{{ thisObj.assetQuantity}}</td>
                <td>{{thisObj.assetPrice}}</td>
                <td>{{thisObj.assetTotalPrice}}</td>
                <td>{{ thisObj.assetRemarks }}</td>
              </tr>
            </tbody>
          </table>


        </div>
      </div>
    </div>
    <!-- /Asset Tab -->

    <!-- Scholarship Tab -->

    <div class="pro-overview tab-pane fade show" id="ca_scholarship">
      <div class="card customCard">
        <div class="card-body">
          <h3>Scholarship List</h3>

          <table id="eduListTable" class="table table-striped custom-table">
            <thead>
              <tr>
                <th class="d-none">TB_ROW_ID</th>
                <th>ID</th>
                <th>Amount</th>
                <th>Provide Date</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let thisObj of scholarshipInfo let i = index">
                <td>{{ i+1 }}</td>
                <td class="d-none">{{thisObj.id}}</td>
                <td>{{ thisObj.amount }} </td>
                <td>{{ thisObj.provideDate }}</td>
              </tr>
            </tbody>
          </table>


        </div>
      </div>
    </div>

    <!-- /Kpi Tab -->

    <div class="tab-pane fade" id="ca_kpi">

      <div class="row">
        <div class="col-md-6 d-flex">
          <div class="card profile-box flex-fill customCard">
            <div class="card-body">
              <h3 class="card-title">Key Performance Indicator</h3>
              <ul class="personal-info">
                <li>
                  <div class="title">Event Perticipated</div>
                  <div class="text">{{kpiInfo.eventPerticipated?kpiInfo.eventPerticipated:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Seminar Perticipeted</div>
                  <div class="text">{{kpiInfo.seminarPerticipated?kpiInfo.seminarPerticipated:'N/A'}}</div>
                </li>
                <hr>


                <li>
                  <div class="title">Product Showcasing</div>
                  <div class="text">{{kpiInfo.productShowcasing?kpiInfo.productShowcasing:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Product Communication</div>
                  <div class="text">{{kpiInfo.productCommunication?kpiInfo.productCommunication:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Corporate Events Attended</div>
                  <div class="text">{{kpiInfo.corporateEventsAttended?kpiInfo.corporateEventsAttended:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Market Survay Conducted</div>
                  <div class="text">{{kpiInfo.marketSurveyConducted?kpiInfo.marketSurveyConducted:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Industry Collabration</div>
                  <div class="text">{{kpiInfo.industryCollaboration?kpiInfo.industryCollaboration:'N/A'}}</div>
                </li>
                <hr>

                <li>
                  <div class="title">Over All KPI</div>
                  <div class="text">{{kpiInfo.overAllKpi?kpiInfo.overAllKpi:'N/A'}} % </div>
                </li>
                <hr>

              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Kpi Tab -->

  </div>
</div>
<!-- /Page Content -->


<!-- Image upload modal -->
<div id="profile_Image" class="modal custom-modal fade" role="dialog">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Upload Profile Image</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="uploadForm" (ngSubmit)="UploadProfilePhoto(personalInfo.id)">
          <div class="row">
            <div class="col-md-12">
              <div class="profile-img-wrap edit-img" *ngIf="personalInfo">
                <img class="inline-block" [src]="imageSrc || baseUrl + personalInfo.profilePic" />

                <div class="fileupload btn">
                  <span class="btn-text">Change</span>
                  <input class="upload" type="file" name="profile" accept="image/x-png,image/jpeg,image/jpg"
                    (change)="onFileSelect($event)" />
                </div>
              </div>
            </div>
          </div>

          <div class="submit-section">
            <button class="btn btn-primary submit-btn">Upload</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- /Image upload modal -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>