import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgxSpinnerService } from "ngx-spinner";
import { ToastrService } from "ngx-toastr";
import { environment } from "src/environments/environment";
import { CaService } from "../../../service/ca.service";

declare const $: any;
@Component({
  selector: "app-ca-profile",
  templateUrl: "./ca-profile.component.html",
  styleUrls: ["./ca-profile.component.css"],
})
export class CaProfileComponent implements OnInit {
  public baseUrl = environment.baseUrl;
  public personalInfo: any = [];
  public institutionalInfo: any = [];
  public corporateInfo: any = [];
  public assetInfo: any = [];
  public scholarshipInfo: any = [];
  public myAmbassador: any = [];
  public kpiInfo: any = [];

  public uploadForm: FormGroup;
  public imageSrc: any;
  public profileImageUrl!: any;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,
    private caService: CaService
  ) { }

  ngOnInit(): void {

    if (this.personalInfo.length == 0) {
      this.getPersonalInfoIdById(this.route.snapshot.params.id);
    }

    this.uploadForm = this.formBuilder.group({
      profile: [""],
    });
  }

  // ------------------------- get Personal Info Id By Id ---------------------------

  getPersonalInfoIdById(id) {
    const apiURL = this.baseUrl + "/ca/candidateInfo/get/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {
        this.spinnerService.hide();

        if (res.status == true) {
          this.personalInfo = res.data;
        } else {
          this.spinnerService.hide();
          this.toastr.warning(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // ---------------------------- get Institute  InfoBy Candidate Id --------------------------------

  getInstitutelInfoByCandidateId(id) {
    if (this.institutionalInfo.length == 0) {
      const apiURL =
        this.baseUrl + "/ca/instituteInfo/getByCandidateInfoId/" + id;
      this.spinnerService.show();

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          if (res.status == true) {
            this.institutionalInfo = res.data;
            this.spinnerService.hide();
          } else {
            this.spinnerService.hide();
            this.toastr.warning(res.message);
          }
        },
        (error) => { }
      );
    }
  }


  //--------------------------- get Corporate Info By Candidate Id ---------------------------


  getCorporateInfoByCandidateId(id) {
    if (this.corporateInfo.length == 0) {
      this.spinnerService.show();
      const apiURL =
        this.baseUrl + "/ca/corporateInfo/getByCandidateInfoId/" + id;

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          if (res.status == true) {
            this.corporateInfo = res.data;
            this.spinnerService.hide();
            this.getMyAmbassador(id);
          } else {
            this.spinnerService.hide();
            this.toastr.warning(res.message);
          }
        },
        (error) => { }
      );
    }
  }


  // ------------------------ get Asset Info By Candidate Id -----------------------

  getAssetInfoByCandidateId(id) {
    if (this.assetInfo.length == 0) {
      this.spinnerService.show();
      const apiURL = this.baseUrl + "/ca/assets/getAsset/" + id;

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          if (res.status == true) {
            this.spinnerService.hide();
            this.assetInfo = res.data;
          } else {
            this.spinnerService.hide();
            this.toastr.warning(res.message);
          }
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        }
      );
    }
  }

  // ---------------- Get Scholarship Info By Candidate ID -----------------------

  getScholarshipInfoByCandidateId(id) {
    if (this.scholarshipInfo.length == 0) {
      this.spinnerService.show();
      const apiURL = this.baseUrl + "/ca/scholarship/getScholarship/" + id;

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          if (res.status == true) {
            this.spinnerService.hide();
            this.scholarshipInfo = res.data;
          } else {
            this.spinnerService.hide();
            this.toastr.warning(res.message);
          }
        },
        (error) => { }
      );
    }
  }

  // ------------------- Get My Ambassador ------------------

  getMyAmbassador(id) {
    this.spinnerService.show();

    const apiURL = this.baseUrl + "/ca/corporateInfo/getMyAmbassador/" + id;

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res: any) => {
        if (res.status == true) {
          this.spinnerService.hide();
          this.myAmbassador = res.data;
          // this.toastr.success(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // <-----Kpi data ---->

  _getKpiByPersonalInfoId(id) {
    if (this.kpiInfo === null || this.kpiInfo.length <= 0) {
      const apiURL = this.baseUrl + "/ca/kpi/getByCandidateInfoId/" + id;
      this.spinnerService.show();

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          this.kpiInfo = res.data;
          this.spinnerService.hide();
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        }
      );
    }
  }

  // <-----Kpi data ---->

  UploadProfilePhoto(id: any) {
    const formData = new FormData();
    formData.append("file", this.uploadForm.get("profile").value);
    formData.append("type", "profile");
    this.caService.uploadProfileImage(id, formData).subscribe(
      (data) => {
        $("#profile_Image").modal("hide");
        this.getPersonalInfoIdById(id);
        this.toastr.success("Successfully uploaded image");
      },
      (error) => {
        this.toastr.warning("Error" + error.message);
      }
    );
  }

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imageSrc = reader.result);
      this.uploadForm.get("profile").setValue(file);
    }
  }
}
