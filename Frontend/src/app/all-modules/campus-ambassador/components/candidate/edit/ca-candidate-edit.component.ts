import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgxSpinnerService } from "ngx-spinner";
import { ToastrService } from "ngx-toastr";
import { environment } from "src/environments/environment";
import { CaService } from "../../../service/ca.service";
import { CommonService } from "src/app/all-modules/settings/common/services/common.service";

declare const $: any;
@Component({
  selector: "app-ca-candidate-edit",
  templateUrl: "./ca-candidate-edit.component.html",
  styleUrls: ["./ca-candidate-edit.component.css"],
})
export class CaCandidateEditComponent implements OnInit {
  public baseUrl = environment.baseUrl;
  uploadForm: FormGroup;
  uploadForm2: FormGroup;
  public personalInfoForm: FormGroup;
  public instituteForm: FormGroup;
  public assetInfoForm: FormGroup;
  public scholarshipForm: FormGroup;
  public corporateInfoForm: FormGroup;
  public extraCurrForm: FormGroup;
  public kpiInfoForm: FormGroup;
  public instituteList = [];
  public championList = [];
  public personalInfoId: number = null;

  public tempId: any;
  public campusId: any = null;

  public paramId: number;

  public caPersonalInfo: any = [];

  public personalInfoData: any = [];
  public instituteInfoData: any = [];
  public coporateInfoData: any = [];
  public assetInfo: any = [];
  public scholarshipInfo: any = [];
  public kpiInfoData: any = [];

  public eventPerticipatedValue: any;
  public seminarPerticipatedValue: any;
  public productCommunicationValue: any;
  public productShowcasingValue: any;
  public corporateEventsAttendedValue: any;
  public marketSurveyConductedValue: any;
  public industryCollaborationValue: any;

  configDDL: any;

  docSeleted: boolean = false;

  imgSrc: string | ArrayBuffer;
  imageSrc: any;

  docSeleted2: boolean = false;

  imgSrc2: string | ArrayBuffer;
  imageSrc2: any;

  // Text area field character limitation

  maxNumberOfCharacters = 100;
  maxNumForPsyIllness = 300;

  maxNumForExCurAct = 300;

  numOfCharPrsntAdrs = 0; // For Present Addres Field
  numOfCharPrmntAdrs = 0; // For Permanent Addres Field
  pillnessCharNum = 0; // For psysical Illness Field
  exCurActCharNum = 0; //Extra Curriculam Act Field

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,
    private caService: CaService,
    private commonService: CommonService,
  ) {
    this._initConfigDDL();
    this._customInitLoadData();
  }

  ngOnInit(): void {
    this.paramId = this.route.snapshot.params.id;
    this.personalInfoId = this.paramId;

    this.uploadForm = this.formBuilder.group({
      document: ["", [Validators.required]],
    });


    this.uploadForm2 = this.formBuilder.group({
      document2: ["", [Validators.required]],
    });

    this._initPersonalForm();
    this._initInstituteForm();
    this._initCorporateForm();
    this._initAssetInfoForm();
    this._initScholarshipForm();
    this.getAllInstitutes();
    this._initKpiForm();
    this._getPersonalInfoIdById(this.paramId);

    this._initextraCurrForm();

    this.extraCurrForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()]),
    });

  }

  _initPersonalForm() {
    this.personalInfoForm = this.formBuilder.group({
      id: [""],
      name: ["", [Validators.required]],
      phone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
      email: [
        "",
        [
          Validators.required,
          Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ],
      ],
      alternateEmail: [
        "",
        [
          Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ],
      ],
      localGuardianPhone: [
        "",
        [Validators.required, Validators.pattern("[0-9 ]{11}")],
      ],
      presentAddress: ["", [Validators.required]],
      permanentAddress: ["", [Validators.required]],
      dateOfBirth: ["", [Validators.required]],
      nationalId: ["", [Validators.pattern("^[0-9]*$")]],
      birthCertificateNo: ["", [Validators.pattern("^[0-9]*$")]],
      bloodGroup: ["", [Validators.required]],
      noOfFamilyMembers: [""],
      positionAmongSiblings: [""],
      physicalIllness: [""],
      physicalIllnessHistory: [""],

      fatherName: ["", [Validators.required]],
      fatherProfession: ["", [Validators.required]],
      fatherPhone: [
        "",
        [Validators.required, Validators.pattern("[0-9 ]{11}")],
      ],
      motherName: ["", [Validators.required]],
      motherProfession: ["", [Validators.required]],
      motherPhone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
    });
  }

  _initInstituteForm() {
    this.instituteForm = this.formBuilder.group({
      id: [""],
      campusId: ["", [Validators.required]],
      className: [""],
      shiftName: [""],
      idNo: [""],
      campusCoordinatorName: [""],
      campusCoordinatorDesignation: [""],
      campusCoordinatorDepartment: [""],
      campusCoordinatorPhone: ["", [Validators.pattern("[0-9 ]{11}")]],

      sscTitle: [""],
      sscInstitute: [""],
      sscGroup: [""],
      sscRollNo: ["", [Validators.pattern("^[0-9]*$")]],
      sscBoard: [""],
      sscPassingYear: [""],
      sscResult: [""],
      sscGpaOutOf4: [""],
      sscGpaOutOf5: [""],

      hscTitle: [""],
      hscInstitute: [""],
      hscGroup: [""],
      hscRollNo: ["", [Validators.pattern("^[0-9]*$")]],
      hscBoard: [""],
      hscPassingYear: [""],
      hscResult: [""],
      hscGpaOutOf4: [""],
      hscGpaOutOf5: [""],

      bscTitle: [""],
      bscInstitute: [""],
      bscSubjectOrDegree: [""],
      bscPassingYear: [""],
      bscResult: [""],
      bscGpaOutOf4: [""],
      bscGpaOutOf5: [""],
      bscCourseDuration: [""],

      mscTitle: [""],
      mscInstitute: [""],
      mscSubjectOrDegree: [""],
      mscPassingYear: [""],
      mscResult: [""],
      mscGpaOutOf4: [""],
      mscGpaOutOf5: [""],
      mscCourseDuration: [""],

      extraCurricularInfos: [""],

      candidateInfoId: [""],
    });
  }

  _initCorporateForm() {
    this.corporateInfoForm = this.formBuilder.group({
      id: [""],
      officialDesignation: ["", [Validators.required]],
      legalEntity: ["", [Validators.required]],
      department: ["", [Validators.required]],
      section: ["", [Validators.required]],
      subSection: ["", [Validators.required]],
      team: ["", [Validators.required]],

      officialSimNo: ["", [Validators.pattern("[0-9 ]{11}")]],
      rocketNumber: ["", [Validators.required]],
      allocatedSimBal: ["", [Validators.pattern("^[0-9]*$")]],
      joiningDate: ["", [Validators.required]],
      officialMailAddress: [
        "",
        [Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$")],
      ],
      campusMail: [
        "",
        [Validators.required, Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$")],
        //         [Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$")],
      ],
      candidateInfoId: [""],
      campusChampionId: [""],
      jobDescription: ["", [Validators.required]],
      inCharge: ["", [Validators.required]],
      concernHR: ["", [Validators.required]],
      document: [""],
      caSignature: [""],
    });
  }

  _initKpiForm() {
    this.kpiInfoForm = this.formBuilder.group({
      id: [""],
      eventPerticipated: ["", [Validators.required, Validators.max(15)]],
      seminarPerticipated: ["", [Validators.required, Validators.max(15)]],
      productShowcasing: ["", [Validators.required, Validators.max(14)]],
      productCommunication: ["", [Validators.required, Validators.max(14)]],
      corporateEventsAttended: ["", [Validators.required, Validators.max(144)]],
      marketSurveyConducted: ["", [Validators.required, Validators.max(14)]],
      industryCollaboration: ["", [Validators.required, Validators.max(14)]],
      overAllKpi: ["", Validators.max(100)],
      candidateInfoId: [""],
      campusChampionId: [""],
    });
  }

  _initAssetInfoForm() {
    this.assetInfoForm = this.formBuilder.group({
      id: [""],
      assetType: [""],
      assetName: [""],
      assetDescription: [""],
      assetQuantity: [""],
      assetPrice: [""],
      assetTotalPrice: [""],
      assetLocation: [""],
      assetRemarks: [""],
      candidateInfoId: [""],
    });
  }

  _initScholarshipForm() {
    this.scholarshipForm = this.formBuilder.group({
      id: [""],
      amount: ["", [Validators.required]],
      provideDate: ["", [Validators.required]],
      candidateInfoId: [""],
    });
  }

  _initextraCurrForm() {
    this.extraCurrForm = this.formBuilder.group({
      id: [""],
      priceFrom: ["", [Validators.required]],
      priceTo: ["", [Validators.required]],
      employeePayPercentage: ["", [Validators.required]],
      orgPayPercentage: ["", [Validators.required]],
      remarks: [""],
    });
  }

  // --------------- Extra Curriculam Form Array ---------

  get extraCurrActFromArr() {
    return this.extraCurrForm.get("Rows") as FormArray;
  }

  initDocRows() {
    return this.formBuilder.group({
      extraCurrAct: [""],
      extraCurrActExp: [""],
      extraCurrActDesc: [""],
      extraCurrActAward: [""],
    });
  }

  addDocRow() {
    this.extraCurrActFromArr.push(this.initDocRows());
  }

  deleteDocRow(index: number) {
    this.extraCurrActFromArr.removeAt(index);
  }


  // ---------------- Get All Institution ----------------

  getAllInstitutes() {
    const url = this.baseUrl + "/ca/campus/findAll";
    this.spinnerService.show();
    this.caService.sendGetRequest(url, null).subscribe(
      (res) => {
        if (res.status == true) {
          this.instituteList = res.data;
        } else {
          this.toastr.warning(res.message);

        }
      },
      (err) => {
        this.spinnerService.hide();
        this.toastr.warning(err.error.message);
      }
    );
  }

  // --------------------- Get champion List --------------------

  getChampion(campusId: any) {
    const url =
      this.baseUrl + "/ca/corporateInfo/getCampusChampion/" + campusId;
    this.spinnerService.show();
    this.caService.sendGetRequest(url, null).subscribe(
      (res) => {
        this.spinnerService.hide();
        if (res.status == true) {
          this.championList = res.data;
        } else {
          this.spinnerService.hide();
          this.toastr.warning(res.message);
        }
      },
      (err) => {
        this.spinnerService.hide();
        this.toastr.warning(err.error.message);
      }
    );
  }

  // ---------------- Get Personal Info  ----------------------

  _getPersonalInfoIdById(id) {
    if (this.personalInfoData === null || this.personalInfoData.length <= 0) {
      const apiURL = this.baseUrl + "/ca/candidateInfo/get/" + id;
      this.spinnerService.show();

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe((res) => {

        this.caPersonalInfo = res?.data;

        this.personalInfoForm.patchValue(res?.data);
        this.personalInfoForm.patchValue({
          dateOfBirth: this.datePipe.transform(
            res?.data?.dateOfBirth,
            "dd-MM-yyyy"
          ),

        });

        this.spinnerService.hide();
      });
    }
  }

  // Text area field character limitation

  prsntAdrsCharCount(event: any): void {
    this.numOfCharPrsntAdrs = event.target.value.length;

    if (this.numOfCharPrsntAdrs > this.maxNumberOfCharacters) {
      event.target.value = event.target.value.slice(
        0,
        this.maxNumberOfCharacters
      );
      this.numOfCharPrsntAdrs = this.maxNumberOfCharacters;
    }
  }

  prmntAdrsCharCount(event: any): void {
    this.numOfCharPrmntAdrs = event.target.value.length;

    if (this.numOfCharPrmntAdrs > this.maxNumberOfCharacters) {
      event.target.value = event.target.value.slice(
        0,
        this.maxNumberOfCharacters
      );
      this.numOfCharPrmntAdrs = this.maxNumberOfCharacters;
    }
  }

  psyIllCharCount(event: any): void {
    this.pillnessCharNum = event.target.value.length;

    if (this.pillnessCharNum > this.maxNumForPsyIllness) {
      event.target.value = event.target.value.slice(
        0,
        this.maxNumForPsyIllness
      );
      this.pillnessCharNum = this.maxNumForPsyIllness;
    }
  }

  exCurActCharCount(event: any): void {
    this.exCurActCharNum = event.target.value.length;

    if (this.exCurActCharNum > this.maxNumForExCurAct) {
      event.target.value = event.target.value.slice(0, this.maxNumForExCurAct);
      this.exCurActCharNum = this.maxNumForExCurAct;
    }
  }

  // ------------------------- Get Personal Info Submit ---------------------

  public personalInfoUpdateSubmit() {
    const apiURL = this.baseUrl + "/ca/candidateInfo/update";

    let pInfoFormData: any = {};
    pInfoFormData = this.personalInfoForm.value;
    // process date
    pInfoFormData.dateOfBirth = (pInfoFormData.dateOfBirth) ? this.commonService.format_Date_Y_M_D(pInfoFormData.dateOfBirth) : null;

    this.spinnerService.show();

    this.caService
      .sendPutRequest(apiURL, pInfoFormData)
      .subscribe((res: any) => {
        this.spinnerService.hide();
        this.personalInfoForm.reset();
        this.toastr.success("Personal Info Updated", "Success");
        this.personalInfoForm.patchValue(res.data);
        this.personalInfoForm.patchValue({
          dateOfBirth: this.datePipe.transform(
            res?.data?.dateOfBirth,
            "dd-MM-yyyy"
          ),
        });
      });
  }

  // ------------------------- Institutional Info Submit -----------------------

  _getInstituteInfoByPersonalInfoId(id) {
    if (this.instituteInfoData === null || this.instituteInfoData.length <= 0) {
      this.spinnerService.show();
      const apiURL =
        this.baseUrl + "/ca/instituteInfo/getByCandidateInfoId/" + id;
      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          this.campusId = res.data?.campusId;

          this.instituteInfoData = res.data;
          this.instituteInfoData.bscInstitute = res?.data?.bscInstitute?.id;
          this.instituteInfoData.mscInstitute = res?.data?.mscInstitute?.id;
          this.instituteForm.patchValue(this.instituteInfoData);

          if (res?.data?.extraCurricularInfos) {
            this.addRowExtraCurrAct(res?.data?.extraCurricularInfos);
          }

          this.spinnerService.hide();
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        }
      );
    }
  }

  // -------------------- Patch value of Extra Curr. Act Array ---------------------

  addRowExtraCurrAct(data) {
    this.extraCurrActFromArr.clear();
    for (let da of data) {
      this.extraCurrActFromArr.push(this.mapWithPrevExpTable(da));
    }

    this.spinnerService.hide();
  }

  mapWithPrevExpTable(data) {
    return this.formBuilder.group({
      id: data.id,
      extraCurrAct: data.extraCurrAct,
      extraCurrActExp: data.extraCurrActExp,
      extraCurrActDesc: data.extraCurrActDesc,
      extraCurrActAward: data.extraCurrActAward,
    });
  }

  // --------------------- Institutional Info Update ------------------------

  public instituteInfoUpdateSubmit() {
    const apiURL = this.baseUrl + "/ca/instituteInfo/update";

    let FormData: any = {};
    FormData = Object.assign(this.instituteForm.value, {
      bscInstitute: this.instituteForm.controls.bscInstitute.value ? { id: this.instituteForm.controls.bscInstitute.value } : null,
      mscInstitute: this.instituteForm.controls.mscInstitute.value ? { id: this.instituteForm.controls.mscInstitute.value } : null,

    });

    FormData.candidateInfoId = this.paramId;

    for (let i = 0; i < this.extraCurrForm.value.Rows.length; i++) {
      this.extraCurrForm.value.Rows[i] = this.extraCurrForm.value.Rows[i];
    }

    FormData.extraCurricularInfos = this.extraCurrForm.value.Rows;

    this.spinnerService.show();

    this.caService.sendPutRequest(apiURL, FormData).subscribe(
      (res: any) => {
        this.instituteForm.reset();
        this.toastr.success("InstitutionalInfo updated successfully");
        this.instituteInfoData = [];
        this._getInstituteInfoByPersonalInfoId(res?.data?.candidateInfo?.id);
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // -------------------- Get Corporate Info --------------------

  _getCorporateInfoByPersonalInfoId(id) {
    if (this.coporateInfoData === null || this.coporateInfoData.length <= 0) {
      const apiURL =
        this.baseUrl + "/ca/corporateInfo/getByCandidateInfoId/" + id;
      this.spinnerService.show();

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {

          // field 1
          let inCharge = [
            {
              ddlCode: res?.data?.inCharge?.id,
              ddlDescription: res?.data?.inCharge?.loginCode + "-" + res?.data?.inCharge?.displayName,
            },
          ];

          this.configDDL.listData = inCharge;
          res.data.inCharge = res?.data?.inCharge?.id;

          // field 2
          let concernHR = [
            {
              ddlCode: res?.data?.concernHR?.id,
              ddlDescription: res?.data?.concernHR?.loginCode + "-" + res?.data?.concernHR?.displayName,
            },
          ];

          this.configDDL.listData2 = concernHR;
          res.data.concernHR = res?.data?.concernHR?.id;

          this.corporateInfoForm.patchValue(res.data);
          this.corporateInfoForm.patchValue({
            joiningDate: this.datePipe.transform(
              res?.data?.joiningDate,
              "dd-MM-yyyy"
            ),
          });
          this.coporateInfoData = res.data;
          this.spinnerService.hide();
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        }
      );
    }
  }

  // ---------------------- Get KPI Info ----------------------

  _getKpiByPersonalInfoId(id) {
    if (this.kpiInfoData === null || this.kpiInfoData.length <= 0) {
      const apiURL = this.baseUrl + "/ca/kpi/getByCandidateInfoId/" + id;
      this.spinnerService.show();

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {

          this.kpiInfoForm.patchValue(res.data);
          this.kpiInfoData = res.data;
          this.spinnerService.hide();
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        }
      );
    }
  }

  // --------------------- Corporate Info Update -----------------------

  public corporateInfoUpdateSubmit() {
    const apiURL = this.baseUrl + "/ca/corporateInfo/update";

    let corporateData: any = {};
    corporateData = this.corporateInfoForm.value;
    corporateData.candidateInfoId = this.route.snapshot.params.id;
    corporateData.inCharge = { id: this.corporateInfoForm.controls.inCharge.value };
    corporateData.concernHR = { id: this.corporateInfoForm.controls.concernHR.value };
    // process date
    corporateData.joiningDate = (corporateData.joiningDate) ? this.commonService.format_Date_Y_M_D(corporateData.joiningDate) : null;
    this.spinnerService.show();

    this.caService.sendPutRequest(apiURL, corporateData).subscribe(
      (res: any) => {

        this.uploaddocument(res?.data.id);
        this.uploaddocument2(res?.data.id);
        this.corporateInfoForm.reset();
        this.toastr.success("Corporate Info Updated", "Success");
        this.corporateInfoForm.patchValue(res.data);
        this.corporateInfoForm.patchValue({
          joiningDate: this.datePipe.transform(
            res?.data?.joiningDate,
            "dd-MM-yyyy"
          ),
        });

        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // ---------------------------- KPI Info Update --------------------------

  public kpiInfoUpdateSubmit() {
    const apiURL = this.baseUrl + "/ca/kpi/update";

    let kpiData: any = {};
    kpiData = this.kpiInfoForm.value;
    kpiData.candidateInfoId = this.route.snapshot.params.id;
    this.spinnerService.show();

    this.caService.sendPutRequest(apiURL, kpiData).subscribe(
      (res: any) => {
        this.kpiInfoForm.reset();
        this.toastr.success("Kpi Info Updated", "Success");
        this.kpiInfoForm.patchValue(res.data);
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  //  ---------------------- Asset Info List (ALL) -------------------------

  _getAssetInfoByPersonalInfoId(id) {
    const apiURL = this.baseUrl + "/ca/assets/getAsset/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {

        this.assetInfoForm.patchValue(res.data);
        this.assetInfo = res.data;
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  //  --------------------- Asset Info Form Modal (edit) ------------------

  editAssetInfo(id) {
    const apiURL = this.baseUrl + "/ca/assets/get/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {

        this.assetInfoForm.patchValue(res.data);
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // ------------------------- Asset info Update ---------------------

  public updateAssetInfo() {
    const apiURL = this.baseUrl + "/ca/assets/update";

    let assetFormData: any = {};
    assetFormData = this.assetInfoForm.value;

    this.spinnerService.show();
    this.caService.sendPutRequest(apiURL, assetFormData).subscribe(
      (res: any) => {
        this.assetInfoForm.reset();
        this.toastr.success("Asset Info Updated", "Success");
        $("#add_assetinfo_modal").modal("hide");
        this.assetInfoForm.patchValue(res.data);
        this.assetInfo = res.data;
        this._getAssetInfoByPersonalInfoId(this.route.snapshot.params.id);
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  //  ----------------------- Asset Info Delete  --------------------

  deleteAssetInfo(id) {
    const apiURL = this.baseUrl + "/ca/assets/delete/" + id;
    const formData: any = {};

    this.spinnerService.show();
    this.caService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {
        if (response.status === true) {
          this.spinnerService.hide();
          this.toastr.success("Asset Item Deleted", "Success");

          $("#delete_asset").modal("hide");
          this._getAssetInfoByPersonalInfoId(this.route.snapshot.params.id);
        } else {
          this.spinnerService.hide();
          this.toastr.warning(response.message, "Error");
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  //  ------------------------- Scholarship List (All) ------------------------

  _getScholarshipInfoByPersonalInfoId(id) {
    const apiURL = this.baseUrl + "/ca/scholarship/getScholarship/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {
        this.spinnerService.hide();

        this.scholarshipForm.patchValue(res?.data);
        this.scholarshipForm.patchValue({
          provideDate: this.datePipe.transform(
            res?.data?.provideDate,
            "dd-MM-yyyy"
          ),
        });
        this.scholarshipInfo = res?.data;
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  //  ------------------- Scholarship Info Form Modal(Edit) ---------------------

  editscholarshipInfo(id) {
    const apiURL = this.baseUrl + "/ca/scholarship/get/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {
        this.spinnerService.hide();

        this.scholarshipForm.patchValue(res.data);
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // ---------------------- Scholarship Info Update ---------------------

  public updateScholarshipInfo() {
    const apiURL = this.baseUrl + "/ca/scholarship/update";

    let siFormData: any = {};
    siFormData = this.scholarshipForm.value;
    // process date
    siFormData.provideDate = (siFormData.provideDate) ? this.commonService.format_Date_Y_M_D(siFormData.provideDate) : null;

    this.spinnerService.show();
    this.caService.sendPutRequest(apiURL, siFormData).subscribe(
      (res: any) => {
        this.spinnerService.hide();
        this.scholarshipForm.reset();
        this.toastr.success("Scholarship Info Updated", "Success");
        $("#add_scholarshipinfo_modal").modal("hide");
        this.scholarshipForm.patchValue(res.data);
        this.scholarshipInfo = res.data;
        this._getScholarshipInfoByPersonalInfoId(this.route.snapshot.params.id);
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  //  ------------------- Scholarship Info Delete  ----------------------

  deleteScholarshipInfo(id) {
    const apiURL = this.baseUrl + "/ca/scholarship/delete/" + id;
    const formData: any = {};
    this.spinnerService.show();
    this.caService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {
        if (response.status === true) {
          this.spinnerService.hide();
          this.toastr.success("Scholarship Info Deleted", "Success");
          $("#delete_scholarship").modal("hide");
          this._getScholarshipInfoByPersonalInfoId(
            this.route.snapshot.params.id
          );
        } else {
          this.spinnerService.hide();
          this.toastr.warning(response.message, "Error");
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }



  // ---------------------- Asset Info Submit --------------------

  assetInfoSubmit() {
    console.warn(this.assetInfoForm.value);
    if (this.assetInfoForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/assets/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = this.assetInfoForm.value;
    formData.candidateInfoId = this.personalInfoId;
    formData.assetTotalPrice = formData.assetQuantity * formData.assetPrice;

    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this.assetInfoForm.reset();
          //  this.getAssetInfoById(this.assetInfoId);
          this._getAssetInfoByPersonalInfoId(this.route.snapshot.params.id);
          //hide add_asset modal
          $("#add_asset").modal("hide");
        } else {
          this.spinnerService.hide();
          this.toastr.warning(res.message);
          $("#add_asset").modal("hide");
          this._getAssetInfoByPersonalInfoId(this.route.snapshot.params.id);
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        $("#add_asset").modal("hide");
      }
    );
  }


  // ------------------ Scholarship Info Submit --------------------------

  scholarshipInfoSubmit() {
    console.warn(this.scholarshipForm.value);

    if (this.scholarshipForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/scholarship/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = this.scholarshipForm.value;
    formData.candidateInfoId = this.personalInfoId;

    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this._getScholarshipInfoByPersonalInfoId(
            this.route.snapshot.params.id
          );
          this.scholarshipForm.reset();
          // hide modal
          $("#add_scholarship").modal("hide");
        } else {
          this.spinnerService.hide();
          this.toastr.info(res.message);
          this._getScholarshipInfoByPersonalInfoId(
            this.route.snapshot.params.id
          );
          $("#add_scholarship").modal("hide");
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this._getScholarshipInfoByPersonalInfoId(this.route.snapshot.params.id);
        $("#add_scholarship").modal("hide");
      }
    );
  }

  // ---------------------- KPI Calculation ------------------
  eventPerticipated(value) {
    this.eventPerticipatedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.eventPerticipatedValue) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  seminarPerticipated(value) {
    this.seminarPerticipatedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.seminarPerticipatedValue) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  productCommunication(value) {
    this.productCommunicationValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.productCommunicationValue) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  productShowcasing(value) {
    this.productShowcasingValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.productShowcasingValue) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  corporateEventsAttended(value) {
    this.corporateEventsAttendedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.corporateEventsAttendedValue) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  marketSurveyConducted(value) {
    this.marketSurveyConductedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.marketSurveyConductedValue) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  industryCollaboration(value) {
    this.seminarPerticipatedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.seminarPerticipatedValue)
    );
  }

  piFrmCntrl() {
    return this.personalInfoForm.controls;
  }

  insFrmCntrl() {
    return this.instituteForm.controls;
  }

  corporateFrmCntrl() {
    return this.corporateInfoForm.controls;
  }

  kpiFrmCntrl() {
    return this.kpiInfoForm.controls;
  }

  piResetFormValues() {
    this.personalInfoForm.reset();
  }

  assetResetFormValues() {
    this.assetInfoForm.reset();
  }

  instResetFormValues() {
    this.instituteForm.reset();
  }

  cfResetFormValues() {
    this.corporateInfoForm.reset();
  }

  kpiResetFormValues() {
    this.kpiInfoForm.reset();
  }

  siResetFormValues() {
    this.scholarshipForm.reset();
  }



  // ------------------- Change document Picture ------------------

  onFileSelectDoc(event) {
    if (event.target.files.length > 0) {
      this.docSeleted = true;
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imageSrc = reader.result);
      this.uploadForm.get("document").setValue(file);
      $("#document_Image").modal("hide");
    }
  }


  onFileSelectDoc2(event) {
    if (event.target.files.length > 0) {
      this.docSeleted2 = true;
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imageSrc2 = reader.result);
      this.uploadForm2.get("document2").setValue(file);
    }
  }

  uploaddocument(id) {

    if (this.docSeleted) {

      let apiURL = this.baseUrl + "/ca/corporateInfo/document/" + id;

      const formData = new FormData();
      formData.append("file", this.uploadForm.get("document").value);
      formData.append("type", "file");

      this.caService.sendPostRequest(apiURL, formData).subscribe(
        (data) => {

          this.toastr.success("Top Sheet Document Uploaded Successfully");
          this.spinnerService.hide();

        },
        (error) => {
          this.toastr.warning("Top Sheet Document Not Uploaded");
          this.spinnerService.hide();
        }
      );
    }

  }

  uploaddocument2(id) {

    if (this.docSeleted2) {

      let apiURL = this.baseUrl + "/ca/corporateInfo/uploadSignature/" + id;

      const formData = new FormData();
      formData.append("file", this.uploadForm2.get("document2").value);
      formData.append("type", "file");

      this.caService.sendPostRequest(apiURL, formData).subscribe(
        (data) => {

          this.toastr.success("Signature Uploaded Successfully");
          this.spinnerService.hide();
        },
        (error) => {
          this.toastr.warning("Signature File Not Uploaded");
          this.spinnerService.hide();
        }
      );
    }

  }


  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
          this.configDDL.listData2 = this.configDDL.listData2.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
          this.configDDL.listData2 = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      listData2: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL,

  ) {

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;

    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------

}
