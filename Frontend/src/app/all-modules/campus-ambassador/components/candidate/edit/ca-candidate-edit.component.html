<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Campus Ambassador</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Ambassador</b></span></li>
          <li class="breadcrumb-item active">Edit </li>
        </ul>
      </div>
      <div class="col-auto float-right ml-auto">

        <a class="btn add-btn" routerLink="/campusambassador/candidate/list"><i class="fa fa-share"> </i> Back To
          List </a>

      </div>
    </div>
  </div>
  <!-- /Page Header -->

  <div class="card mb-3 customCard">
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <div class="profile-view">
            <div class="profile-img-wrap">
              <div class="profile-img">
                <a *ngIf="caPersonalInfo.profilePicPath" [routerLink]="" class="avatar" data-target="#profile_Image"
                  data-toggle="modal"><img src="{{ baseUrl + caPersonalInfo.profilePicPath }}" alt="" />
                </a>

                <a *ngIf="!caPersonalInfo.profilePicPath" [routerLink]="" class="avatar" data-target="#profile_Image"
                  data-toggle="modal"><img id="companyLogo" data-flag="1" src="assets/img/user-icon/u-sq-pic.jpg"
                    alt="" /></a>

              </div>
            </div>
            <div class="profile-basic">
              <div class="row">
                <div class="col-md-5">
                  <div class="profile-info-left">
                    <br>
                    <h3 class="user-name m-t-0 mb-0">{{caPersonalInfo?.name}}</h3>
                    
                    <div class="staff-id">ID : {{caPersonalInfo?.caIdCode}}</div>
                    <div class="staff-id">Official Designation : {{caPersonalInfo?.corporateInfo?.officialDesignation}}</div>
                    <h6 class="text-success" *ngIf="caPersonalInfo.status == 'active' ">
                      Status : Active
                    </h6>
                  </div>
                </div>
                <div class="col-md-7">
                  <ul class="personal-info">
                    <li>
                      <div class="staff-id">University Name: {{caPersonalInfo?.institutionalInfo?.campusName}}</div>
                    </li>

                    <li>
                      <div class="staff-id">Email: <a href="">{{caPersonalInfo.email ? caPersonalInfo?.email: 'N/A' }}</a></div>
                    </li>

                    <li>
                      <div class="staff-id">Official Phone: {{caPersonalInfo.officialSimNo ? caPersonalInfo?.officialSimNo: 'N/A' }}</div>
                    </li>
                    <li>
                      <div class="staff-id">Joining date : {{caPersonalInfo.corporateInfo?.joiningDate ?
                        (caPersonalInfo.corporateInfo?.joiningDate |date:'mediumDate'): 'N/A'}}
                        (<span>{{caPersonalInfo.corporateInfo?.remainingDays}} days remaining</span>)
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="card tab-box customCard">
    <div class="row user-tabs">
      <div class="col-lg-12 col-md-12 col-sm-12 line-tabs">
        <ul class="nav nav-tabs nav-tabs-bottom">

          <li class="nav-item"><a href="#personal_info" data-toggle="tab" class="nav-link active">Personal Info </a>
          </li>
          <li class="nav-item"><a (click)="_getInstituteInfoByPersonalInfoId(personalInfoId)" href="#institute_info"
              data-toggle="tab" class="nav-link"> Institutional Info </a></li>

          <li class="nav-item"><a (click)="_getCorporateInfoByPersonalInfoId(personalInfoId)" href="#corporate_info"
              data-toggle="tab" class="nav-link">Corporate Info</a></li>

          <li class="nav-item"><a (click)="_getAssetInfoByPersonalInfoId(personalInfoId)" href="#asset_info"
              data-toggle="tab" class="nav-link">Asset Info</a></li>

          <li class="nav-item"><a (click)="_getScholarshipInfoByPersonalInfoId(personalInfoId)" href="#scholarship_info"
              data-toggle="tab" class="nav-link">Scholarship Info</a></li>

          <li class="nav-item"><a (click)="_getKpiByPersonalInfoId(personalInfoId)" href="#kpi_info" data-toggle="tab"
              class="nav-link">KPI Info</a></li>


          <!-- <li *ngIf="personalInfoId" class="nav-item"><a href="#assignRole_info" data-toggle="tab" class="nav-link">Assign Role
              <small class="text-danger"></small></a>
          </li> -->

        </ul>
      </div>
    </div>
  </div>



  <div class="tab-content">

    <!-- Personal Info Tab -->
    <div class="pro-overview tab-pane fade show active" id="personal_info">
      <div class="card customCard">
        <div class="card-body">
          <form (ngSubmit)="personalInfoUpdateSubmit()" [formGroup]="personalInfoForm">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>Candidate Full Name<span class="text-danger"> *</span></label>
                  <!-- input candidate name -->
                  <input type="text" class="form-control" formControlName="name">
                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('name').errors?.required && personalInfoForm.get  ('name').touched  && personalInfoForm.get('name').invalid"
                    class="text-danger">Candidate name is required
                  </span>

                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>Candidate Phone<span class="text-danger"> *</span></label>
                  <!-- input candidate name -->
                  <input type="text" class="form-control" formControlName="phone">
                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('phone').errors?.required && personalInfoForm.get  ('phone').touched"
                    class="text-danger">Phone Number is required
                  </span>
                  <span *ngIf="personalInfoForm.get('phone').invalid && personalInfoForm.get('phone').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>Candidate Email<span class="text-danger"> *</span></label>
                  <!-- input candidate name -->
                  <input type="email" class="form-control" formControlName="email">
                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('email').errors?.required && personalInfoForm.get  ('email').touched"
                    class="text-danger">Candidate email is required.
                  </span>
                  <span *ngIf="personalInfoForm.get('email').invalid && personalInfoForm.get('email').errors.pattern"
                    class="text-danger">Invalid Email
                  </span>

                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Alternate Email<span class="text-danger"> *</span></label>
                  <!-- input candidate name -->
                  <input type="text" class="form-control" formControlName="alternateEmail">
                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('alternateEmail').errors?.required && personalInfoForm.get  ('alternateEmail').touched"
                    class="text-danger">Alternate email is required.
                  </span>
                  <span
                    *ngIf="personalInfoForm.get('alternateEmail').invalid && personalInfoForm.get('alternateEmail').errors.pattern"
                    class="text-danger">Invalid Email
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Date Of Birth</label>

                  <div class="cal-icon">
                    <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker type="text"
                      placeholder="DD-MM-YYYY"
                      [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                      formControlName="dateOfBirth">
                  </div>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>National ID</label>
                  <!-- input candidate name -->
                  <input type="text" class="form-control" formControlName="nationalId">
                  <span
                    *ngIf="personalInfoForm.get('nationalId').invalid && personalInfoForm.get('nationalId').errors.pattern"
                    class="text-danger">Number Only
                  </span>
                </div>
              </div>
            </div>

            <hr>
            <br> <br>

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>Birth Certificate No</label>
                  <!-- input candidate name -->
                  <input type="text" class="form-control" formControlName="birthCertificateNo">
                  <span
                    *ngIf="personalInfoForm.get('birthCertificateNo').invalid && personalInfoForm.get('birthCertificateNo').errors.pattern"
                    class="text-danger">Number Only
                  </span>
                </div>
              </div>

              <!-- Blood Group -->
              <div class="col-md-3">
                <div class="form-group">
                  <label>Blood Group <span class="text-danger"> *</span></label>
                  <select class="form-control" formControlName="bloodGroup">
                    <option value="">Select Blood Group</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                </div>
              </div>

              <!-- No of mamily member -->
              <div class="col-md-3">
                <div class="form-group">
                  <label>No of Family Member</label>
                  <!-- input candidate name -->
                  <input type="number" class="form-control" formControlName="noOfFamilyMembers">
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Position Among Siblings</label>
                  <!-- input candidate name -->
                  <input type="number" class="form-control" formControlName="positionAmongSiblings">
                </div>
              </div>

              <!-- father name -->
              <div class="col-md-3">
                <div class="form-group">
                  <label>Father Name <span class="text-danger"> *</span></label>
                  <input type="text" class="form-control" formControlName="fatherName">
                  <span
                    *ngIf="personalInfoForm.get('fatherName').errors?.required && personalInfoForm.get  ('fatherName').touched  && personalInfoForm.get('fatherName').invalid"
                    class="text-danger">Father name is required
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Father Profession <span class="text-danger"> *</span></label>
                  <input type="text" class="form-control" formControlName="fatherProfession">
                  <span
                    *ngIf="personalInfoForm.get('fatherProfession').errors?.required && personalInfoForm.get  ('fatherProfession').touched  && personalInfoForm.get('fatherProfession').invalid"
                    class="text-danger">Father profession is required
                  </span>
                </div>
              </div>


            </div>

            <hr>

            <br> <br>

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>Father Phone <span class="text-danger"> *</span></label>
                  <input type="text" class="form-control" formControlName="fatherPhone">
                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('fatherPhone').errors?.required && personalInfoForm.get  ('fatherPhone').touched"
                    class="text-danger">Father Phone is required
                  </span>
                  <span
                    *ngIf="personalInfoForm.get('fatherPhone').invalid && personalInfoForm.get('fatherPhone').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Mother name <span class="text-danger"> *</span></label>
                  <input type="text" class="form-control" formControlName="motherName">
                  <span
                    *ngIf="personalInfoForm.get('motherName').errors?.required && personalInfoForm.get  ('motherName').touched  && personalInfoForm.get('motherName').invalid"
                    class="text-danger">Mother name is required
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Mother Profession</label>
                  <input type="text" class="form-control" formControlName="motherProfession">
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Mother Phone <span class="text-danger"> *</span></label>
                  <input type="text" class="form-control" formControlName="motherPhone">
                  <span
                    *ngIf="personalInfoForm.get('motherPhone').invalid && personalInfoForm.get('motherPhone').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>

              <!-- physical illness check box -->

              <div class="col-md-3 custom-checkbox ">
                <div class="form-group">

                  <label class="toggle">
                    <input class="toggle-checkbox" formControlName="physicalIllness" type="checkbox" value="1">
                    <div class="toggle-switch"></div>
                    <span class="toggle-label">Physical illness </span>
                  </label>

                </div>
              </div>
              <!-- <div class="col-md-3">
                <div class="form-group">
                  <label>Physical illness <span class="text-danger"> *</span></label>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="1" formControlName="physicalIllness">
                    <label class="form-check-label">
                      Yes
                    </label>
                  </div>
                </div>
              </div> -->

              <div class="col-md-6">
                <div class="form-group">
                  <label>Physical illness history<span class="text-danger"> *</span></label>
                  <span class="float-right">
                    {{ pillnessCharNum }} / {{maxNumForPsyIllness}}
                  </span>
                  <textarea class="form-control" formControlName="physicalIllnessHistory" placeholder="Illness History"
                    (keyup)="psyIllCharCount($event)" rows="1"></textarea>


                </div>
              </div>



            </div>
            <hr>
            <br> <br>

            <div class="row">

              <div class="col-md-3">
                <div class="form-group">
                  <label>Local Guardian Phone<span class="text-danger"> *</span></label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="localGuardianPhone">
                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('localGuardianPhone').errors?.required && personalInfoForm.get  ('localGuardianPhone').touched"
                    class="text-danger">Local Guardian Phone is required
                  </span>
                  <span
                    *ngIf="personalInfoForm.get('localGuardianPhone').invalid && personalInfoForm.get('localGuardianPhone').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>Present Address <span class="text-danger"> *</span></label>
                  <span class="float-right">
                    {{ numOfCharPrsntAdrs }} / {{maxNumberOfCharacters}}
                  </span>
                  <!-- text area candidate name -->
                  <textarea class="form-control" formControlName="presentAddress"
                    placeholder="village/area, post office, police station, district"
                    (keyup)="prsntAdrsCharCount($event)" rows="1"></textarea>

                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('presentAddress').errors?.required && personalInfoForm.get  ('presentAddress').touched  && personalInfoForm.get('presentAddress').invalid"
                    class="text-danger">Present Address is required
                  </span>

                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Permanent Address <span class="text-danger"> *</span></label>
                  <span class="float-right">
                    {{ numOfCharPrmntAdrs }} / {{maxNumberOfCharacters}}
                  </span>
                  <!-- text area candidate name -->
                  <textarea class="form-control" formControlName="permanentAddress"
                    placeholder="village/area, post office, police station, district"
                    (keyup)="prmntAdrsCharCount($event)" rows="1"></textarea>

                  <!-- error if invalid span -->
                  <span
                    *ngIf="personalInfoForm.get('permanentAddress').errors?.required && personalInfoForm.get  ('permanentAddress').touched  && personalInfoForm.get('permanentAddress').invalid"
                    class="text-danger">Permanent Address is required
                  </span>

                </div>
              </div>

            </div>

            <br> <br>

            <div class="text-right">
              <a class="btn btn-warning btn-ripple" routerLink="/ticket/bbs/list"><i class="fa fa-share"></i>
                Cancel</a>
              &nbsp; &nbsp;
              <button type="button" class="btn btn-secondary btn-ripple" (click)="piResetFormValues()">
                <i class="fa fa-undo" aria-hidden="true"></i> Reset
              </button>
              &nbsp; &nbsp;
              <button *ngIf="personalInfoId" type="submit" class="btn btn-primary btn-ripple"
                [disabled]="!personalInfoForm.valid">
                <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
              </button>
            </div>



          </form>
        </div>
      </div>
    </div>



    <!-- Instutional Info Tab -->
    <div class="tab-pane fade" id="institute_info">
      <div class="card customCard">
        <div class="card-body">
          <form (ngSubmit)="instituteInfoUpdateSubmit()" [formGroup]="instituteForm">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Institute Name </label>
                  <!-- input  select loop data -->
                  <select class="form-control" formControlName="campusId">
                    <option value="">Select Institute</option>
                    <option *ngFor="let campus of instituteList" [ngValue]='campus.id'>{{campus.campusName}}</option>
                  </select>

                  <span
                    *ngIf="instituteForm.get('campusId').errors?.required && instituteForm.get('campusId').touched  && instituteForm.get('campusId').invalid"></span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Year/Class Name </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="className">
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Shift Name </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="shiftName">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Studebt Id </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="idNo">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Campus Coordinator Name </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="campusCoordinatorName">
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Campus Coordinator Designation </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="campusCoordinatorDesignation">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Campus Coordinator Department </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="campusCoordinatorDepartment">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Campus Coordinator Phone </label>
                  <!-- input  name -->
                  <input type="text" class="form-control" formControlName="campusCoordinatorPhone">
                  <span
                    *ngIf="instituteForm.get('campusCoordinatorPhone').invalid && instituteForm.get('campusCoordinatorPhone').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>
            </div>
            <hr>



            <div id="accordion">
              <div class="card customCard">
                <div class="card-header" id="headingSSC">
                  <h5 class="mb-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseSSC"
                      aria-expanded="false" aria-controls="collapseSSC">
                      SSC or Equivalent Level
                    </a>
                  </h5>
                </div>
                <div id="collapseSSC" class="collapse" aria-labelledby="headingSSC" data-parent="#accordion">
                  <div class="card-body">
                    <div class="row">

                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Exam Name </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="sscTitle">
                            <option value="">Select</option>
                            <option value="SSC">SSC</option>
                            <option value="Dakhil">Dakhil</option>
                            <option value="SSC Vocational">SSC Vocational</option>
                            <option value="O Level/Cambridge">O Level/Cambridge</option>
                            <option value="SSC Equivalent">SSC Equivalent</option>
                            <option value="Dakhil Vocational">Dakhil Vocational</option>
                          </select>

                        </div>
                      </div>

                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Institute </label>
                          <!-- input  name -->
                          <input type="text" class="form-control" formControlName="sscInstitute">
                          <span
                            *ngIf="instituteForm.get('sscInstitute').invalid && instituteForm.get('sscInstitute').errors.pattern"
                            class="text-danger">Invalid Roll
                          </span>
                        </div>
                      </div>

                      <!-- ssc roll -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Roll No </label>
                          <!-- input  name -->
                          <input type="text" class="form-control" formControlName="sscRollNo">
                          <span
                            *ngIf="instituteForm.get('sscRollNo').invalid && instituteForm.get('sscRollNo').errors.pattern"
                            class="text-danger">Invalid Roll
                          </span>
                        </div>
                      </div>

                      <!--Group or Subject-->
                      <div class="col-md-3">
                        <label>Group/Subject </label>
                        <!-- input  select -->
                        <select class="form-control" formControlName="sscGroup">
                          <option value=""> Select </option>
                          <option value="Science">Science</option>
                          <option value="Humanities">Humanities</option>
                          <option value="Business Studies">Business Studies</option>
                          <option value="Agriculture Technology">Agriculture Technology</option>
                          <option value="Architecture and Interior Design Technology">Architecture and Interior Design
                            Technology</option>
                          <option value="Automobile Technology">Automobile Technology</option>
                          <option value="Civil Technology">Civil Technology</option>
                          <option value="Computer Science &amp; Technology">Computer Science &amp; Technology</option>
                          <option value="Chemical Technology">Chemical Technology</option>
                          <option value="Electrical Technology">Electrical Technology</option>
                          <option value="Data Telecommunication and Network Technology">Data Telecommunication and
                            Network
                            Technology</option>
                          <option value="Electrical and Electronics Technology">Electrical and Electronics Technology
                          </option>
                          <option value="Environmental Technology">Environmental Technology</option>
                          <option value="Instrumentation &amp; Process Control Technology">Instrumentation &amp; Process
                            Control
                            Technology
                          </option>
                          <option value="Mechanical Technology">Mechanical Technology</option>
                          <option value="Mechatronics Technology">Mechatronics Technology</option>
                          <option value="Power Technology">Power Technology</option>
                          <option value="Refregeration &amp; Air Conditioning Technology">Refregeration &amp; Air
                            Conditioning
                            Technology
                          </option>
                          <option value="Telecommunication Technology">Telecommunication Technology</option>
                          <option value="Electronics Technology">Electronics Technology</option>
                          <option value="Library Science">Library Science</option>
                          <option value="Survey">Survey</option>
                          <option value="General Mechanics">General Mechanics</option>
                          <option value="Firm Machinery">Firm Machinery</option>
                          <option value="Textile Technology">Textile Technology</option>
                          <option value="Food">Food</option>
                          <option value="Glass and Ceramic">Glass and Ceramic</option>
                          <option value="Agro-Based Food">Agro-Based Food</option>
                          <option value="General Electronics">General Electronics</option>
                          <option value="Automotive">Automotive</option>
                          <option value="Building Maintenance">Building Maintenance</option>
                          <option value="Wood Working">Wood Working</option>
                          <option value="Ceramic">Ceramic</option>
                          <option value="Civil Construction">Civil Construction</option>
                          <option value="Computer and Information Technology">Computer and Information Technology
                          </option>
                          <option value="Civil Drafting with CAD">Civil Drafting with CAD</option>
                          <option value="Mechanical Drafting with CAD">Mechanical Drafting with CAD</option>
                          <option value="Dress Making">Dress Making</option>
                          <option value="Dyeing, Printing and Finishing">Dyeing, Printing and Finishing</option>
                          <option value="Electrical Maintenance Works">Electrical Maintenance Works</option>
                          <option value="Farm Machinery">Farm Machinery</option>
                          <option value="Fish Culture and Breeding">Fish Culture and Breeding</option>
                          <option value="Food Processing and Preservation">Food Processing and Preservation</option>
                          <option value="Livestock Rearing and Farming">Livestock Rearing and Farming</option>
                          <option value="Machine Tools Operation">Machine Tools Operation</option>
                          <option value="Poultry Rearing and Farming">Poultry Rearing and Farming</option>
                          <option value="Patient Care">Patient Care</option>
                          <option value="General Electrical Works">General Electrical Works</option>
                          <option value="Plumbing and Pipe Fittings">Plumbing and Pipe Fittings</option>
                          <option value="Refrigeration and Air Conditioning">Refrigeration and Air Conditioning</option>
                          <option value="Glass">Glass</option>
                          <option value="Flower, Fruit and Vegetable Cultivation">Flower, Fruit and Vegetable
                            Cultivation
                          </option>
                          <option value="Weaving">Weaving</option>
                          <option value="Welding and Fabrication">Welding and Fabrication</option>
                          <option value="Architectural Drafting with CAD">Architectural Drafting with CAD</option>
                          <option value="Knitting">Knitting</option>
                          <option value="Shrimp Culture and Breeding">Shrimp Culture and Breeding</option>
                          <option value="Others">Others</option>
                        </select>
                      </div>




                      <!-- board -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Board </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="sscBoard">
                            <option value=""> Select </option>
                            <option value="Dhaka">Dhaka</option>
                            <option value="Cumilla">Cumilla</option>
                            <option value="Rajshahi">Rajshahi</option>
                            <option value="Jashore">Jashore</option>
                            <option value="Chittagong">Chittagong</option>
                            <option value="Barishal">Barishal</option>
                            <option value="Sylhet">Sylhet</option>
                            <option value="Dinajpur">Dinajpur</option>
                            <option value="Madrasah">Madrasah</option>
                            <option value="Mymensingh">Mymensingh</option>
                            <option value="Pharmacy Council of Bangladesh">Pharmacy Council of Bangladesh</option>
                            <option value="Cambridge International - IGCE">Cambridge International - IGCE</option>
                            <option value="Edexcel International">Edexcel International</option>
                            <option value="Bangladesh Technical Education Board (BTEB)">Bangladesh Technical Education
                              Board
                              (BTEB)</option>
                            <option value="Open University">Open University</option>
                            <option value="Others">Others</option>
                          </select>
                        </div>
                      </div>

                      <!-- result -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Result </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="sscResult">
                            <option value=""> Select </option>
                            <option value="1st Division">1st Division</option>
                            <option value="2nd Division">2nd Division</option>
                            <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                            <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                          </select>
                          <!-- if select Gpa(out of 5) then open extra input field -->
                          <div *ngIf="instituteForm.get('sscResult').value == 'GPA(Out of 5)'">
                            <input type="number" class="form-control" formControlName="sscGpaOutOf5"
                              placeholder="Type here your GPA">
                          </div>
                          <!-- if select Gpa(out of 4) then open extra input field -->
                          <div *ngIf="instituteForm.get('sscResult').value == 'GPA(Out of 4)'">
                            <input type="number" class="form-control" formControlName="sscGpaOutOf4"
                              placeholder="Type here your GPA">
                          </div>

                        </div>
                      </div>

                      <!-- Passing year -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Passing Year </label>
                          <input type="text" class="form-control" formControlName="sscPassingYear">
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>


              <div class="card customCard">
                <div class="card-header" id="headingHSC">
                  <h5 class="mb-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseHSC"
                      aria-expanded="false" aria-controls="collapseHSC">
                      HSC or Equivalent Level
                    </a>
                  </h5>
                </div>
                <div id="collapseHSC" class="collapse" aria-labelledby="headingHSC" data-parent="#accordion">
                  <div class="card-body">

                    <div class="row">

                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Exam Name </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="hscTitle">
                            <option value=""> Select </option>
                            <option value="H.S.C">H.S.C</option>
                            <option value="Diploma-in-Engineering (4 Years)">Diploma-in-Engineering (4 Years)</option>
                            <option value="A Level/Sr. Cambridge">A Level/Sr. Cambridge</option>
                          </select>

                        </div>
                      </div>

                      <!-- hscinstitute -->

                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Institute </label>
                          <!-- input  name -->
                          <input type="text" class="form-control" formControlName="hscInstitute">
                          <span
                            *ngIf="instituteForm.get('sscInstitute').invalid && instituteForm.get('hscInstitute').errors.pattern"
                            class="text-danger">Invalid Roll
                          </span>
                        </div>
                      </div>

                      <!-- hsc roll -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Roll No </label>
                          <!-- input  name -->
                          <input type="text" class="form-control" formControlName="hscRollNo">
                          <span
                            *ngIf="instituteForm.get('hscRollNo').invalid && instituteForm.get('hscRollNo').errors.pattern"
                            class="text-danger">Invalid Roll
                          </span>
                        </div>
                      </div>

                      <!--Group or Subject-->
                      <div class="col-md-3">
                        <label>Group/Subject </label>
                        <!-- input  select -->
                        <select class="form-control" formControlName="hscGroup">
                          <option value=""> Select </option>
                          <option value="Science">Science</option>
                          <option value="Humanities">Humanities</option>
                          <option value="Business Studies">Business Studies</option>
                          <option value="Agriculture Technology">Agriculture Technology</option>
                          <option value="Architecture and Interior Design Technology">Architecture and Interior Design
                            Technology</option>
                          <option value="Automobile Technology">Automobile Technology</option>
                          <option value="Civil Technology">Civil Technology</option>
                          <option value="Computer Science &amp; Technology">Computer Science &amp; Technology</option>
                          <option value="Chemical Technology">Chemical Technology</option>
                          <option value="Electrical Technology">Electrical Technology</option>
                          <option value="Data Telecommunication and Network Technology">Data Telecommunication and
                            Network
                            Technology</option>
                          <option value="Electrical and Electronics Technology">Electrical and Electronics Technology
                          </option>
                          <option value="Environmental Technology">Environmental Technology</option>
                          <option value="Instrumentation &amp; Process Control Technology">Instrumentation &amp; Process
                            Control
                            Technology
                          </option>
                          <option value="Mechanical Technology">Mechanical Technology</option>
                          <option value="Mechatronics Technology">Mechatronics Technology</option>
                          <option value="Power Technology">Power Technology</option>
                          <option value="Refregeration &amp; Air Conditioning Technology">Refregeration &amp; Air
                            Conditioning
                            Technology
                          </option>
                          <option value="Telecommunication Technology">Telecommunication Technology</option>
                          <option value="Electronics Technology">Electronics Technology</option>
                          <option value="Library Science">Library Science</option>
                          <option value="Survey">Survey</option>
                          <option value="General Mechanics">General Mechanics</option>
                          <option value="Firm Machinery">Firm Machinery</option>
                          <option value="Textile Technology">Textile Technology</option>
                          <option value="Food">Food</option>
                          <option value="Glass and Ceramic">Glass and Ceramic</option>
                          <option value="Agro-Based Food">Agro-Based Food</option>
                          <option value="General Electronics">General Electronics</option>
                          <option value="Automotive">Automotive</option>
                          <option value="Building Maintenance">Building Maintenance</option>
                          <option value="Wood Working">Wood Working</option>
                          <option value="Ceramic">Ceramic</option>
                          <option value="Civil Construction">Civil Construction</option>
                          <option value="Computer and Information Technology">Computer and Information Technology
                          </option>
                          <option value="Civil Drafting with CAD">Civil Drafting with CAD</option>
                          <option value="Mechanical Drafting with CAD">Mechanical Drafting with CAD</option>
                          <option value="Dress Making">Dress Making</option>
                          <option value="Dyeing, Printing and Finishing">Dyeing, Printing and Finishing</option>
                          <option value="Electrical Maintenance Works">Electrical Maintenance Works</option>
                          <option value="Farm Machinery">Farm Machinery</option>
                          <option value="Fish Culture and Breeding">Fish Culture and Breeding</option>
                          <option value="Food Processing and Preservation">Food Processing and Preservation</option>
                          <option value="Livestock Rearing and Farming">Livestock Rearing and Farming</option>
                          <option value="Machine Tools Operation">Machine Tools Operation</option>
                          <option value="Poultry Rearing and Farming">Poultry Rearing and Farming</option>
                          <option value="Patient Care">Patient Care</option>
                          <option value="General Electrical Works">General Electrical Works</option>
                          <option value="Plumbing and Pipe Fittings">Plumbing and Pipe Fittings</option>
                          <option value="Refrigeration and Air Conditioning">Refrigeration and Air Conditioning</option>
                          <option value="Glass">Glass</option>
                          <option value="Flower, Fruit and Vegetable Cultivation">Flower, Fruit and Vegetable
                            Cultivation
                          </option>
                          <option value="Weaving">Weaving</option>
                          <option value="Welding and Fabrication">Welding and Fabrication</option>
                          <option value="Architectural Drafting with CAD">Architectural Drafting with CAD</option>
                          <option value="Knitting">Knitting</option>
                          <option value="Shrimp Culture and Breeding">Shrimp Culture and Breeding</option>
                          <option value="Others">Others</option>
                        </select>
                      </div>




                      <!-- board -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Board </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="hscBoard">
                            <option value=""> Select </option>
                            <option value="Dhaka">Dhaka</option>
                            <option value="Cumilla">Cumilla</option>
                            <option value="Rajshahi">Rajshahi</option>
                            <option value="Jashore">Jashore</option>
                            <option value="Chittagong">Chittagong</option>
                            <option value="Barishal">Barishal</option>
                            <option value="Sylhet">Sylhet</option>
                            <option value="Dinajpur">Dinajpur</option>
                            <option value="Madrasah">Madrasah</option>
                            <option value="Mymensingh">Mymensingh</option>
                            <option value="Pharmacy Council of Bangladesh">Pharmacy Council of Bangladesh</option>
                            <option value="Cambridge International - IGCE">Cambridge International - IGCE</option>
                            <option value="Edexcel International">Edexcel International</option>
                            <option value="Bangladesh Technical Education Board (BTEB)">Bangladesh Technical Education
                              Board
                              (BTEB)</option>
                            <option value="Open University">Open University</option>
                            <option value="Others">Others</option>
                          </select>
                        </div>
                      </div>

                      <!-- result -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Result </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="hscResult">
                            <option value=""> Select </option>
                            <option value="1st Division">1st Division</option>
                            <option value="2nd Division">2nd Division</option>
                            <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                            <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                          </select>
                          <!-- if select Gpa(out of 5) then open extra input field -->
                          <div *ngIf="instituteForm.get('hscResult').value == 'GPA(Out of 5)'">
                            <input type="number" class="form-control" formControlName="hscGpaOutOf5"
                              placeholder="Type here your GPA">
                          </div>
                          <!-- if select Gpa(out of 4) then open extra input field -->
                          <div *ngIf="instituteForm.get('hscResult').value == 'GPA(Out of 4)'">
                            <input type="number" class="form-control" formControlName="hscGpaOutOf4"
                              placeholder="Type here your GPA">
                          </div>

                        </div>
                      </div>

                      <!-- Passing year -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Passing Year </label>
                          <input type="text" class="form-control" formControlName="hscPassingYear">

                        </div>
                      </div>

                    </div>

                  </div>
                </div>
              </div>


              <div class="card customCard">
                <div class="card-header" id="headingBsc">
                  <h5 class="mb-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseBsc"
                      aria-expanded="false" aria-controls="collapseBsc">
                      Graduation/Bachelor/Equivalent Level
                    </a>
                  </h5>
                </div>
                <div id="collapseBsc" class="collapse" aria-labelledby="headingBsc" data-parent="#accordion">
                  <div class="card-body">
                    <div class="row">
                      <!-- board -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Examination</label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="bscTitle">
                            <option value="" selected="selected">Select One</option>
                            <option value="B.Sc Engineering">B.Sc Engineering</option>
                            <option value="B.Sc in Agricultural Science">B.Sc in Agricultural Science</option>
                            <option value="M.B.B.S./B.D.S">M.B.B.S./B.D.S</option>
                            <option value="Honors">Honors</option>
                            <option value="Pass Course">Pass Course</option>
                            <option value="Fazil">Fazil</option>
                            <option value="B.B.A">B.B.A</option>
                            <option value="Graduation Equivalent">Graduation Equivalent</option>
                          </select>
                        </div>
                      </div>

                      <!-- Subject/Degree select -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Subject/Degree</label>
                          <input type="text" class="form-control" formControlName="bscSubjectOrDegree"
                            placeholder="Computer Science Engineering">
                        </div>
                      </div>

                      <!-- Institute -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>University</label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="bscInstitute">
                            <option value="">Select Institute</option>
                            <option *ngFor="let institute of instituteList" [value]="institute.id">
                              {{institute.campusName}}</option>
                          </select>
                        </div>
                      </div>

                      <!-- result -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Result </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="bscResult">
                            <option value=""> Select </option>
                            <option value="1st Division">1st Division</option>
                            <option value="2nd Division">2nd Division</option>
                            <option value="Passed">Passed</option>
                            <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                            <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                          </select>
                          <!-- if select Gpa(out of 5) then open extra input field -->
                          <div *ngIf="instituteForm.get('bscResult').value == 'GPA(Out of 5)'">
                            <input type="number" class="form-control" formControlName="bscGpaOutOf5"
                              placeholder="Type here your GPA">
                          </div>
                          <!-- if select Gpa(out of 4) then open extra input field -->
                          <div *ngIf="instituteForm.get('bscResult').value == 'GPA(Out of 4)'">
                            <input type="number" class="form-control" formControlName="bscGpaOutOf4"
                              placeholder="Type here your GPA">
                          </div>

                        </div>
                      </div>

                      <!-- Passing year -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Passing Year </label>
                          <input type="text" class="form-control" formControlName="bscPassingYear">
                        </div>
                      </div>

                      <!-- course duration select -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Course Duration </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="bscCourseDuration">
                            <option value=""> Select </option>
                            <option value="4 Years">4 Years</option>
                            <option value="5 Years">5 Years</option>
                            <option value="3 Years">3 Years</option>
                            <option value="2 Years">2 Years</option>
                          </select>
                        </div>
                      </div>


                    </div>
                  </div>
                </div>
              </div>


              <div class="card customCard">
                <div class="card-header" id="headingMsc">
                  <h5 class="mb-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseMsc"
                      aria-expanded="false" aria-controls="collapseMsc">
                      Masters or Equivalent Level
                    </a>
                  </h5>
                </div>
                <div id="collapseMsc" class="collapse" aria-labelledby="headingMsc" data-parent="#accordion">
                  <div class="card-body">
                    <div class="row">
                      <!-- board -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Examination</label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="mscTitle">
                            <option value="" selected="selected">Select One</option>
                            <option value="L.L.M">L.L.M</option>
                            <option value="M.A">M.A</option>
                            <option value="M.B.A">M.B.A</option>
                            <option value="M.Com">M.Com</option>
                            <option value="M.S.S">M.S.S</option>
                            <option value="M.Sc">M.Sc</option>
                            <option value="Masters Equivalent">Masters Equivalent</option>
                          </select>
                        </div>
                      </div>

                      <!-- Subject/Degree select -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Subject/Degree</label>
                          <input type="text" class="form-control" formControlName="mscSubjectOrDegree"
                            placeholder="Computer Science Engineering">
                        </div>
                      </div>

                      <!-- Institute -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>University</label>
                          <!-- input  select loop instituteList -->
                          <select class="form-control" formControlName="mscInstitute">
                            <option value="">Select Institute</option>
                            <option *ngFor="let institute of instituteList" [value]="institute.id">
                              {{institute.campusName}}</option>
                          </select>


                        </div>
                      </div>

                      <!-- result -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Result </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="mscResult">
                            <option value=""> Select </option>
                            <option value="1st Division">1st Division</option>
                            <option value="2nd Division">2nd Division</option>
                            <option value="Passed">Passed</option>
                            <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                            <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                          </select>
                          <!-- if select Gpa(out of 5) then open extra input field -->
                          <div *ngIf="instituteForm.get('mscResult').value == 'GPA(Out of 5)'">
                            <input type="number" class="form-control" formControlName="mscGpaOutOf5"
                              placeholder="Type here your GPA">
                          </div>
                          <!-- if select Gpa(out of 4) then open extra input field -->
                          <div *ngIf="instituteForm.get('mscResult').value == 'GPA(Out of 4)'">
                            <input type="number" class="form-control" formControlName="mscGpaOutOf4"
                              placeholder="Type here your GPA">
                          </div>

                        </div>
                      </div>

                      <!-- Passing year -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Passing Year </label>
                          <input type="text" class="form-control" formControlName="mscPassingYear">
                        </div>
                      </div>

                      <!-- course duration select -->
                      <div class="col-md-3">
                        <div class="form-group">
                          <label>Course Duration </label>
                          <!-- input  select -->
                          <select class="form-control" formControlName="mscCourseDuration">
                            <option value=""> Select </option>
                            <option value="4 Years">4 Years</option>
                            <option value="5 Years">5 Years</option>
                            <option value="3 Years">3 Years</option>
                            <option value="2 Years">2 Years</option>
                          </select>
                        </div>
                      </div>


                    </div>
                  </div>
                </div>
              </div>


              <div class="card customCard">
                <div class="card-header" id="headingExtCur">
                  <h5 class="mb-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapseExtCur"
                      aria-expanded="false" aria-controls="collapseHSC">
                      Extra Curricular Activities
                    </a>
                  </h5>
                </div>
                <div id="collapseExtCur" class="collapse" aria-labelledby="headingExtCur" data-parent="#accordion">
                  <div class="card-body">

                    <div class="table-responsive col-md-12">
                      <form [formGroup]="extraCurrForm">
                        <table class="table table-striped custom-table">
                          <thead>
                            <th>SL</th>
                            <th>Title</th>
                            <th>Experience/Duration</th>
                            <th>Description</th>
                            <th>Award</th>
                            <th>Action</th>

                          </thead>
                          <tbody formArrayName="Rows">
                            <tr *ngFor="let itemrow of extraCurrActFromArr.controls; let i=index;let l=last"
                              [formGroupName]="i">
                              <td>{{i+1}}</td>

                              <td>
                                <input class="form-control" formControlName="extraCurrAct" class="form-control"
                                  type="text">
                              </td>

                              <td>
                                <input class="form-control" formControlName="extraCurrActExp" class="form-control"
                                  type="text">
                              </td>


                              <td>
                                <input class="form-control" formControlName="extraCurrActDesc" class="form-control"
                                  type="text">

                              </td>


                              <td>
                                <input class="form-control" formControlName="extraCurrActAward" class="form-control"
                                  type="text">
                              </td>


                              <td>
                                <button *ngIf="extraCurrForm.controls.Rows.controls.length > 0"
                                  (click)="deleteDocRow(i)" class="btn btn-danger">Delete</button>
                              </td>
                            </tr>
                          </tbody>
                        </table>

                        <br>
                        <button type="button" (click)="addDocRow()" class="btn btn-primary">Add More</button>

                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="text-right">
              <a class="btn btn-warning btn-ripple" routerLink="/ticket/bbs/list"><i class="fa fa-share"></i>
                Cancel</a>
              &nbsp; &nbsp;
              <button type="button" class="btn btn-secondary btn-ripple" (click)="instResetFormValues()">
                <i class="fa fa-undo" aria-hidden="true"></i> Reset
              </button>
              &nbsp; &nbsp;
              <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!instituteForm.valid">
                <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
              </button>
            </div>

          </form>
        </div>
      </div>
    </div>

    <!-- Corporate Info -->
    <div class="pro-overview tab-pane fade show" id="corporate_info">
      <div class="card customCard">
        <div class="card-body">
          <form (ngSubmit)="corporateInfoUpdateSubmit()" [formGroup]="corporateInfoForm">

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>Official Designation<span class="text-danger"> *</span></label>
                  <!-- input cselect-->
                  <select class="form-control" formControlName="officialDesignation">
                    <option value=""> Select </option>
                    <option value="Campus Ambassador">Campus Ammbasador</option>
                    <option value="Campus Champion">Campus Champion</option>
                  </select>
                  <!-- error if invalid span -->
                  <span
                    *ngIf="corporateInfoForm.get('officialDesignation').errors?.required && corporateInfoForm.get ('officialDesignation').touched  && corporateInfoForm.get('officialDesignation').invalid"
                    class="text-danger">Official Designation is required
                  </span>

                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Legal Entity</label>


                  <select class="form-control" formControlName="legalEntity">
                    <option value=""> Select </option>
                    <option value="WDTIL">Walton Digi-Tech Industries Limited</option>
                  </select>
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Department<span class="text-danger"> *</span></label>

                  <select class="form-control" formControlName="department">
                    <option value=""> Select </option>
                    <option value="Marketing & Sales">Marketing & Sales</option>
                  </select>
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Section<span class="text-danger"> *</span></label>
                  <select class="form-control" formControlName="section">
                    <option value=""> Select </option>
                    <option value="Marketing">Marketing</option>
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Sub Section<span class="text-danger"> *</span></label>
                  <select class="form-control" formControlName="subSection">
                    <option value=""> Select </option>
                    <option value="Branding">Branding</option>
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Team<span class="text-danger"> *</span></label>
                  <!-- input cselect-->
                  <select class="form-control" formControlName="team">
                    <option value=""> Select </option>
                    <option value="Campus Ambassador">Campus Ambassador</option>
                  </select>
                </div>
              </div>



              <div class="col-md-3">
                <div class="form-group">
                  <label class=" val-required">Incharge </label>

                  <ng-select [items]="configDDL.listData" formControlName="inCharge" placeholder="Select employee"
                    bindLabel="ddlDescription" bindValue="ddlCode" [searchable]="true" [clearable]="true"
                    [virtualScroll]="true" [clearOnBackspace]="true" (search)="searchDDL($event)"
                    (scrollToEnd)="scrollToEndDDL()" (clear)="clearDDL()"
                    (click)="initSysParamsDDL($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes')"
                    ddlActiveFieldName="ddlDescription" class="custom-ng-select">
                  </ng-select>
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label class=" val-required">Concern HR </label>

                  <ng-select [items]="configDDL.listData2" formControlName="concernHR" placeholder="Select employee"
                    bindLabel="ddlDescription" bindValue="ddlCode" [searchable]="true" [clearable]="true"
                    [virtualScroll]="true" [clearOnBackspace]="true" (search)="searchDDL($event)"
                    (scrollToEnd)="scrollToEndDDL()" (clear)="clearDDL()"
                    (click)="initSysParamsDDL($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes')"
                    ddlActiveFieldName="ddlDescription" class="custom-ng-select">
                  </ng-select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Rocket Number</label>

                  <input type="text" class="form-control" formControlName="rocketNumber">
                  <span
                    *ngIf="corporateInfoForm.get('rocketNumber').invalid && corporateInfoForm.get('rocketNumber').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Official Sim No.</label>
                  <!-- input name-->
                  <input type="text" class="form-control" formControlName="officialSimNo">
                  <span
                    *ngIf="corporateInfoForm.get('officialSimNo').invalid && corporateInfoForm.get('officialSimNo').errors.pattern"
                    class="text-danger">Invalid Number
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Allocated Sim Balance</label>
                  <!-- input name-->
                  <input type="text" class="form-control" formControlName="allocatedSimBal">
                  <span
                    *ngIf="corporateInfoForm.get('allocatedSimBal').invalid && corporateInfoForm.get('allocatedSimBal').errors.pattern"
                    class="text-danger">Number Only
                  </span>
                </div>
              </div>

              <!-- joining date -->
              <div class="col-md-3">
                <div class="form-group">
                  <label>Joining Date<span class="text-danger"> *</span></label>

                  <div class="cal-icon">
                    <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker type="text"
                      placeholder="DD-MM-YYYY"
                      [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                      formControlName="joiningDate">
                  </div>
                </div>
              </div>


              <div class="col-md-3">
                <div class="form-group">
                  <label>Official Mail Address</label>
                  <!-- input name-->
                  <input type="text" class="form-control" formControlName="officialMailAddress">
                  <span
                    *ngIf="corporateInfoForm.get('officialMailAddress').invalid && corporateInfoForm.get('officialMailAddress').errors.pattern"
                    class="text-danger">Invalid Email
                  </span>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Campus Mail <span class="text-danger"> *</span></label>
                  <!-- input name-->
                  <input type="text" class="form-control" formControlName="campusMail">
                  <span
                    *ngIf="corporateInfoForm.get('campusMail').invalid && corporateInfoForm.get('campusMail').errors.pattern"
                    class="text-danger">Invalid Email
                  </span>
                </div>
              </div>

              <!-- Campus Champion  show only ooficial designation is CAMPUS_AMMBASADOR -->


              <div class="col-md-3" *ngIf="corporateInfoForm.get('officialDesignation').value == 'CAMPUS_AMMBASADOR'">
                <div class="form-group">
                  <label>Campus Champion</label>
                  <!-- input cselect-->
                  <select class="form-control" (click)="getChampion(campusId)" formControlName="campusChampionId">
                    <option value=""> Select </option>
                    <option *ngFor="let champion of championList" [ngValue]='champion.id'>{{champion.name}}</option>

                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Job Description </label>

                  <textarea class="form-control" formControlName="jobDescription" rows="1"></textarea>

                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <span class="text-muted">Top Sheet File type should be PDF, PNG,
                    JPG, DOC</span>
                  <input class="form-control" type="file" name="document" (change)="onFileSelectDoc($event)">


                </div>

              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <span class="text-muted">Signature File type should be PDF, PNG,
                    JPG, DOC</span>
                  <input class="form-control" type="file" name="document2" (change)="onFileSelectDoc2($event)">
                </div>

              </div>


            </div>

            <div class="text-right">
              <a class="btn btn-warning btn-ripple" routerLink="/ticket/bbs/list"><i class="fa fa-share"></i>
                Cancel</a>
              &nbsp; &nbsp;
              <button type="button" class="btn btn-secondary btn-ripple" (click)="cfResetFormValues()">
                <i class="fa fa-undo" aria-hidden="true"></i> Reset
              </button>
              &nbsp; &nbsp;
              <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!corporateInfoForm.valid">
                <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
              </button>
            </div>

          </form>
        </div>
      </div>

    </div>

    <!-- Kpi Info -->
    <div class="pro-overview tab-pane fade show" id="kpi_info">
      <div class="card customCard">
        <div class="card-body">
          <form (ngSubmit)="kpiInfoUpdateSubmit()" [formGroup]="kpiInfoForm">
            <div class="row">

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Event Perticipated</label><span class="text-muted"> Out of 15</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="eventPerticipated"
                    (input)="eventPerticipated($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Seminar Perticipated</label><span class="text-muted"> Out of 15</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="seminarPerticipated"
                    (input)="seminarPerticipated($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Product Showcasing</label><span class="text-muted"> Out of 14</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="productShowcasing"
                    (input)="productShowcasing($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Product Communication</label><span class="text-muted"> Out of 14</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="productCommunication"
                    (input)="productCommunication($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Corporate Events Attended</label><span class="text-muted"> Out of
                    14</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="corporateEventsAttended"
                    (input)="corporateEventsAttended($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Market Survey Conducted</label><span class="text-muted"> Out of 14</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="marketSurveyConducted"
                    (input)="marketSurveyConducted($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label class="val-required">Industry Collaboration</label><span class="text-muted"> Out of 14</span>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="industryCollaboration"
                    (input)="industryCollaboration($event.target.value)">
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>Over All Kpi<span class="text-muted"> Out of 100</span></label>
                  <!-- input name-->
                  <input type="number" class="form-control" formControlName="overAllKpi" readonly>
                </div>
              </div>
            </div>

            <div class="text-right">
              <a class="btn btn-warning btn-ripple" routerLink="/ticket/bbs/list"><i class="fa fa-share"></i>
                Cancel</a>
              &nbsp; &nbsp;
              <button type="button" class="btn btn-secondary btn-ripple" (click)="cfResetFormValues()">
                <i class="fa fa-undo" aria-hidden="true"></i> Reset
              </button>
              &nbsp; &nbsp;
              <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!kpiInfoForm.valid">
                <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
              </button>
            </div>

          </form>
        </div>
      </div>

    </div>

    <!-- add asset -->
    <div id="add_asset" class="modal custom-modal fade" role="dialog">
      <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Add Asset</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="card">
              <div class="card-body">
                <form novalidate (ngSubmit)="assetInfoSubmit()" [formGroup]="assetInfoForm">

                  <div class="row">
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Name</label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetName">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Type</label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetType">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Description</label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetDescription">
                        <!-- error if invalid span -->
                      </div>
                    </div>



                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Quantity</label>
                        <!-- input-->
                        <input type="number" class="form-control" formControlName="assetQuantity">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Price </label>
                        <!-- input-->
                        <input type="number" class="form-control" formControlName="assetPrice">
                        <!-- error if invalid span -->
                      </div>
                    </div>




                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Remarks </label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetRemarks">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3 mt-4">
                      <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!assetInfoForm.valid">
                        <i class="fa fa-check" aria-hidden="true"></i> Submit &nbsp;&nbsp;&nbsp;
                      </button>
                    </div>

                  </div>




                </form>
              </div>
            </div>



          </div>
        </div>
      </div>
    </div>




    <!-- Asset Info -->
    <div class="pro-overview tab-pane fade show" id="asset_info">
      <div class="card customCard">

        <div class="card-header">
          <div class="card-tools">
            <a class="btn btn-outline-primary" data-toggle="modal" data-target="#add_asset"><i class="fa fa-plus"></i>
              Add Asset &nbsp;&nbsp;&nbsp;</a>


          </div>
        </div>

        <div class="card-body">
          <div class="pro-overview tab-pane fade show" id="ca_asset">
            <!-- Show Asset Info Table -->

            <div class="card">
              <table id="assetListTable" class="table table-striped custom-table">
                <thead>
                  <tr>
                    <th>Asset Name</th>
                    <th>Asset Type</th>
                    <th>Asset Description</th>
                    <th>Asset Quantity</th>
                    <th>Asset Price</th>
                    <th>Asset Total Price</th>
                    <th>Remarks</th>
                    <th>Action</th>

                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let thisObj of assetInfo">
                    <td>{{ thisObj.assetName }} </td>
                    <td>{{ thisObj.assetType }}</td>
                    <td>{{ thisObj.assetDescription }}</td>
                    <td>{{ thisObj.assetQuantity}}</td>
                    <td>{{thisObj.assetPrice}}</td>
                    <td>{{thisObj.assetTotalPrice}}</td>
                    <td>{{ thisObj.assetRemarks }}</td>
                    <td>
                      <a class="btn btn-sm btn-info" data-toggle="modal" data-target="#add_assetinfo_modal"
                        (click)="editAssetInfo(thisObj.id)"><i class="fa fa-pencil m-r-5"></i></a>&nbsp;&nbsp;
                      <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#delete_asset"
                        (click)="tempId = thisObj.id">
                        <i class="fa fa-trash-o m-r-5"></i>
                      </a> &nbsp;
                    </td>
                  </tr>
                </tbody>
              </table>

            </div>
          </div>
        </div>

        <!-- Asset update Info Modal -->

        <div id="add_assetinfo_modal" class="modal custom-modal fade" role="dialog">
          <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Edit Asset Info </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <form (ngSubmit)="updateAssetInfo()" [formGroup]="assetInfoForm">

                  <div class="row">
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Name</label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetName">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Type</label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetType">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Description</label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetDescription">
                        <!-- error if invalid span -->
                      </div>
                    </div>



                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Quantity</label>
                        <!-- input-->
                        <input type="number" class="form-control" formControlName="assetQuantity">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Asset Price </label>
                        <!-- input-->
                        <input type="number" class="form-control" formControlName="assetPrice">
                        <!-- error if invalid span -->
                      </div>
                    </div>




                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Remarks <span class="text-danger"> *</span></label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="assetRemarks">
                        <!-- error if invalid span -->
                      </div>
                    </div>

                  </div>



                  <div class="text-right">
                    <button type="button" class="btn btn-secondary btn-ripple" (click)="assetResetFormValues()">
                      <i class="fa fa-undo" aria-hidden="true"></i> Reset
                    </button>
                    &nbsp; &nbsp;
                    <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!assetInfoForm.valid">
                      <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
                    </button>
                  </div>

                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- End Asset Info Modal -->

        <!-- Asset Delete Modal -->
        <div class="modal custom-modal fade" id="delete_asset" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-body">
                <div class="form-header">
                  <h3>Delete Item</h3>
                  <p>Are you sure want to delete?</p>
                </div>
                <div class="modal-btn delete-action">
                  <div class="row">
                    <div class="col-6">
                      <a class="btn btn-primary continue-btn" (click)="deleteAssetInfo(tempId)">Delete</a>
                    </div>
                    <div class="col-6">
                      <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- End Asset Delete Modal -->

      </div>

    </div>

    <!-- add scholarship -->
    <div id="add_scholarship" class="modal custom-modal fade" role="dialog">
      <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Add Scholarship</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="card customCard">
              <div class="card-body">
                <form novalidate (ngSubmit)="scholarshipInfoSubmit()" [formGroup]="scholarshipForm">

                  <div class="form-group">
                    <label>Amount<span class="text-danger"> *</span></label>
                    <!-- input-->
                    <input type="number" class="form-control" formControlName="amount">
                    <!-- error if invalid span -->
                    <span class="text-danger"
                      *ngIf="scholarshipForm.get('amount').errors?.required &&  scholarshipForm.get('amount').touched  && scholarshipForm.get('amount').invalid">Amount
                      is required</span>
                  </div>

                  <div class="form-group">
                    <label class="val-required">Provide Date</label>

                    <div class="cal-icon">
                      <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker type="text"
                        placeholder="DD-MM-YYYY"
                        [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                        formControlName="provideDate">
                    </div>
                  </div>


                  <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!scholarshipForm.valid">
                    <i class="fa fa-check" aria-hidden="true"></i> Submit &nbsp;&nbsp;&nbsp;
                  </button>


                </form>
              </div>
            </div>



          </div>
        </div>
      </div>
    </div>


    <!-- Scholarship Info -->
    <div class="pro-overview tab-pane fade show" id="scholarship_info">
      <div class="card customCard">

        <div class="card-header">
          <div class="card-tools">
            <a class="btn btn-outline-primary" data-toggle="modal" data-target="#add_scholarship"><i class="fa fa-plus"></i>
              Add Scholarship &nbsp;&nbsp;&nbsp;</a>


          </div>
        </div>

        <div class="card-body">

          <div class="pro-overview" id="ca_scholarship">

            <div class="card">
              <table id="scholarshipListTable" class="table table-striped custom-table">
                <thead>
                  <tr>
                    <th class="d-none">TB_ROW_ID</th>
                    <th>SL</th>
                    <th>Amount</th>
                    <th>Provide Date</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let thisObj of scholarshipInfo let i = index">
                    <td>{{ i+1 }}</td>
                    <td class="d-none">{{thisObj.id}}</td>
                    <td>{{ thisObj.amount }} </td>
                    <td>{{ thisObj.provideDate | date}}</td>
                    <td>
                      <a class="btn btn-sm btn-info" data-toggle="modal" data-target="#add_scholarshipinfo_modal"
                        (click)="editscholarshipInfo(thisObj.id)"><i class="fa fa-pencil m-r-5"></i></a>&nbsp;&nbsp;
                      <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#delete_scholarship"
                        (click)="tempId = thisObj.id">
                        <i class="fa fa-trash-o m-r-5"></i>
                      </a> &nbsp;
                    </td>
                  </tr>
                </tbody>
              </table>

            </div>

          </div>
        </div>

        <!-- Scholarship Info Modal -->

        <div id="add_scholarshipinfo_modal" class="modal custom-modal fade" role="dialog">
          <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Edit Scholarship Info </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <form (ngSubmit)="updateScholarshipInfo()" [formGroup]="scholarshipForm">

                  <div class="row">
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Amount<span class="text-danger"> *</span></label>
                        <!-- input-->
                        <input type="text" class="form-control" formControlName="amount">
                        <!-- error if invalid span -->
                        <span class="text-danger"
                          *ngIf="scholarshipForm.get('amount').errors?.required &&  scholarshipForm.get('amount').touched  && scholarshipForm.get('amount').invalid">Amount
                          is required</span>
                      </div>
                    </div>

                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Provide Date</label>
                        <input type="date" class="form-control" formControlName="provideDate">

                        <span
                          *ngIf="scholarshipForm.get('provideDate').errors?.required &&  scholarshipForm.get  ('provideDate').touched  && scholarshipForm.get('provideDate').invalid"
                          class="text-danger">Provide Date is required
                        </span>

                      </div>
                    </div>

                  </div>

                  <div class="text-right">
                    <button type="button" class="btn btn-secondary btn-ripple" (click)="siResetFormValues()">
                      <i class="fa fa-undo" aria-hidden="true"></i> Reset
                    </button>
                    &nbsp; &nbsp;
                    <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!scholarshipForm.valid">
                      <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
                    </button>
                  </div>

                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- End Scholarship Info Modal -->

        <!-- Scholarship Delete Modal -->
        <div class="modal custom-modal fade" id="delete_scholarship" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-body">
                <div class="form-header">
                  <h3>Delete Item</h3>
                  <p>Are you sure want to delete?</p>
                </div>
                <div class="modal-btn delete-action">
                  <div class="row">
                    <div class="col-6">
                      <a class="btn btn-primary continue-btn" (click)="deleteScholarshipInfo(tempId)">Delete</a>
                    </div>
                    <div class="col-6">
                      <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- END Scholarship Delete Modal -->

      </div>

    </div>

    <!-- END Scholarship Info -->

  </div>

</div>

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>