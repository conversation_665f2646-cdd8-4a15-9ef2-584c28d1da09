.content {
  padding: 15px;
}


/* ================= Default Template Color ================== */
xx-.form-control {
  border-color: #e3e3e3;
  box-shadow: none;
}

xx-.form-control:focus {
  border-color: #ccc;
  box-shadow: none;
  outline: 0 none;
}




/* ================= Purpale Color =========================== */
input.form-control,
select.form-control,
textarea.form-control {
	border-color: #4BA1D9;
	border-left: 3px solid #4BA1D9;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
  border-color: #705CBA;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
  box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================= Green Color ============================= */
xx-input.form-control {
  border-color: #d4cdcd;
  border-left: 3px solid green;
  box-shadow: none;
}

xx-.form-control:focus {
  border-color: #37a000;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
  box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================== Blue Color ============================== */
xx-input.form-control {
  border-color: #66afe9;
  border-left: 3px solid #66afe9;
  box-shadow: none;
}

xx-.form-control:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
  box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



input.ng-invalid {
  border: 1px solid red;
}

select.ng-invalid {
  border: 1px solid red;
}

textarea.ng-invalid {
  border: 1px solid red;
}


.custom-checkbox {
  margin-top: 35px;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

.toggle {
  cursor: pointer;
  display: inline-block;
}

.toggle-switch {
  display: inline-block;
  background: #ccc;
  border-radius: 16px;
  width: 38px;
  height: 22px;
  position: relative;
  vertical-align: middle;
  transition: background 0.25s;
}

.toggle-switch:before,
.toggle-switch:after {
  content: "";
}

.toggle-switch:before {
  display: block;
  background: linear-gradient(to bottom, #fff 0%, #eee 100%);
  border-radius: 50%;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.25);
  width: 17px;
  height: 17px;
  position: absolute;
  top: 2px;
  left: 3px;
  transition: left 0.25s;
}

.toggle:hover .toggle-switch:before {
  background: linear-gradient(to bottom, #fff 0%, #fff 100%);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}

.toggle-checkbox:checked+.toggle-switch {
  background: #4BA1D9;
}

.toggle-checkbox:checked+.toggle-switch:before {
  left: 20px;
}

.toggle-checkbox {
  position: absolute;
  visibility: hidden;
}

.toggle-label {
  margin-left: 10px;
  position: relative;
  top: 2px;
  font-size: 16px;
}