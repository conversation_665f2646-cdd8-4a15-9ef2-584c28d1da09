import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { AnyObject } from "chart.js/types/basic";
import { NgxSpinnerService } from "ngx-spinner";
import { ToastrService } from "ngx-toastr";
import { environment } from "src/environments/environment";
import { CaService } from "../../../service/ca.service";
import { CommonService } from "src/app/all-modules/settings/common/services/common.service";
declare const $: any;
@Component({
  selector: "app-ca-candidate-create",
  templateUrl: "./ca-candidate-create.component.html",
  styleUrls: ["./ca-candidate-create.component.css"],
})
export class CaCandidateCreateComponent implements OnInit {
  uploadForm: FormGroup;
  uploadForm2: FormGroup;
  public baseUrl = environment.baseUrl;
  public personalInfoForm: FormGroup;
  public topSheetInfo: any = [];
  public instituteForm: FormGroup;
  public extraCurrForm: FormGroup;
  public corporateInfoForm: FormGroup;
  public assetInfoForm: FormGroup;
  public scholarshipForm: FormGroup;
  public kpiInfoForm: FormGroup;

  public instituteList = [];
  public championList = [];
  public personalInfoId: AnyObject = null;
  public instituteId: any = null;
  public campusId: any = null;
  public corporateInfoId: any = null;
  public assetInfoId: any = null;
  public assetInfo: any = [];
  public scholarshipInfo: any = [];
  public kpiInfoData: any = [];

  public eventPerticipatedValue: any;
  public seminarPerticipatedValue: any;
  public productCommunicationValue: any;
  public productShowcasingValue: any;
  public corporateEventsAttendedValue: any;
  public marketSurveyConductedValue: any;
  public industryCollaborationValue: any;

  configDDL: any;


  docSeleted: boolean = false;

  imgSrc: string | ArrayBuffer;
  imageSrc: any;

  docSeleted2: boolean = false;

  imgSrc2: string | ArrayBuffer;
  imageSrc2: any;

  // Text area field character limitation

  maxNumberOfCharacters = 100;
  maxNumForPsyIllness = 300;

  maxNumForExCurAct = 300;

  numOfCharPrsntAdrs = 0; // For Present Addres Field
  numOfCharPrmntAdrs = 0; // For Permanent Addres Field
  pillnessCharNum = 0; // For psysical Illness Field
  exCurActCharNum = 0; //Extra Curriculam Act Field

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,
    private caService: CaService
  ) {
    this._initConfigDDL();
    this._customInitLoadData();
  }

  ngOnInit(): void {



    this._initPersonalForm();
    this._initInstituteForm();
    this._initCorporateForm();
    this._initAssetInfoForm();
    this._initScholarshipForm();
    this._initKpiForm();

    //  this.getAllInstitutes();
    this._initextraCurrForm();

    this.uploadForm = this.formBuilder.group({
      document: ["", [Validators.required]],
    });

    this.uploadForm2 = this.formBuilder.group({
      document2: ["", [Validators.required]],
    });

    this.extraCurrForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()]),
    });


  }

  _initPersonalForm() {
    this.personalInfoForm = this.formBuilder.group({
      id: [""],
      refCode: ["", [Validators.required]],
      name: ["", [Validators.required]],
      phone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
      email: [
        "",
        [
          Validators.required,
          Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ],
      ],
      alternateEmail: [
        "",
        [Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ],
      ],
      localGuardianPhone: [
        "",
        [Validators.required, Validators.pattern("[0-9 ]{11}")],
      ],
      presentAddress: ["", [Validators.required]],
      permanentAddress: ["", [Validators.required]],
      dateOfBirth: ["", [Validators.required]],
      nationalId: ["", [Validators.pattern("^[0-9]*$")]],
      birthCertificateNo: ["", [Validators.pattern("^[0-9]*$")]],
      bloodGroup: ["", [Validators.required]],
      noOfFamilyMembers: [""],
      positionAmongSiblings: [""],
      physicalIllness: [""],
      physicalIllnessHistory: [""],
      profilePicPath: [""],
      fatherName: ["", [Validators.required]],
      fatherProfession: ["", [Validators.required]],
      fatherPhone: [
        "",
        [Validators.required, Validators.pattern("[0-9 ]{11}")],
      ],
      motherName: ["", [Validators.required]],
      motherProfession: ["", [Validators.required]],
      motherPhone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
    });
  }

  _initInstituteForm() {
    this.instituteForm = this.formBuilder.group({
      id: [""],
      campusId: ["", [Validators.required]],
      className: [""],
      shiftName: [""],
      idNo: [""],
      campusCoordinatorName: [""],
      campusCoordinatorDesignation: [""],
      campusCoordinatorDepartment: [""],
      campusCoordinatorPhone: ["", [Validators.pattern("[0-9 ]{11}")]],

      sscTitle: [""],
      sscInstitute: [""],
      sscGroup: [""],
      sscRollNo: ["", [Validators.pattern("^[0-9]*$")]],
      sscBoard: [""],
      sscPassingYear: [""],
      sscResult: [""],
      sscGpaOutOf4: [""],
      sscGpaOutOf5: [""],

      hscTitle: [""],
      hscInstitute: [""],
      hscGroup: [""],
      hscRollNo: ["", [Validators.pattern("^[0-9]*$")]],
      hscBoard: [""],
      hscPassingYear: [""],
      hscResult: [""],
      hscGpaOutOf4: [""],
      hscGpaOutOf5: [""],

      bscTitle: [""],
      bscInstitute: [""],
      bscSubjectOrDegree: [""],
      bscPassingYear: [""],
      bscResult: [""],
      bscGpaOutOf4: [""],
      bscGpaOutOf5: [""],
      bscCourseDuration: [""],

      mscTitle: [""],
      mscInstitute: [""],
      mscSubjectOrDegree: [""],
      mscPassingYear: [""],
      mscResult: [""],
      mscGpaOutOf4: [""],
      mscGpaOutOf5: [""],
      mscCourseDuration: [""],

      candidateInfoId: [""],

      extraCurricularInfos: [""],
    });
  }

  _initCorporateForm() {
    this.corporateInfoForm = this.formBuilder.group({
      id: [""],
      officialDesignation: ["", [Validators.required]],
      department: ["", [Validators.required]],
      legalEntity: ["", [Validators.required]],
      section: ["", [Validators.required]],
      subSection: ["", [Validators.required]],
      team: ["", [Validators.required]],
      officialSimNo: ["", [Validators.pattern("[0-9 ]{11}")]],
      rocketNumber: ["", [Validators.required]],
      allocatedSimBal: ["", [Validators.pattern("^[0-9]*$")]],
      joiningDate: ["", [Validators.required]],
      officialMailAddress: [
        "",
        [Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$")],
      ],
      campusMail: [
        "",
        [Validators.required, Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$")],
      ],
      candidateInfoId: [""],

      campusChampionId: [""],
      jobDescription: ["", [Validators.required]],
      inCharge: ["", [Validators.required]],
      concernHR: ["", [Validators.required]],
      document: [""],
      caSignature: [""],
    });
  }

  _initKpiForm() {
    this.kpiInfoForm = this.formBuilder.group({
      id: [""],
      eventPerticipated: ["", [Validators.required, Validators.max(15)]],
      seminarPerticipated: ["", [Validators.required, Validators.max(15)]],
      productShowcasing: ["", [Validators.required, Validators.max(14)]],
      productCommunication: ["", [Validators.required, Validators.max(14)]],
      corporateEventsAttended: ["", [Validators.required, Validators.max(144)]],
      marketSurveyConducted: ["", [Validators.required, Validators.max(14)]],
      industryCollaboration: ["", [Validators.required, Validators.max(14)]],
      overAllKpi: ["", Validators.max(100)],
      candidateInfoId: [""],
      campusChampionId: [""],
    });
  }

  _initAssetInfoForm() {
    this.assetInfoForm = this.formBuilder.group({
      id: [""],
      assetType: [""],
      assetName: [""],
      assetDescription: [""],
      assetQuantity: [""],
      assetPrice: [""],
      assetTotalPrice: [""],
      assetLocation: [""],
      assetRemarks: [""],
      candidateInfoId: [""],
    });
  }

  _initScholarshipForm() {
    this.scholarshipForm = this.formBuilder.group({
      id: [""],
      amount: ["", [Validators.required]],
      provideDate: ["", [Validators.required]],
      candidateInfoId: [""],
    });
  }

  _initextraCurrForm() {
    this.extraCurrForm = this.formBuilder.group({
      id: [""],
      priceFrom: ["", [Validators.required]],
      priceTo: ["", [Validators.required]],
      employeePayPercentage: ["", [Validators.required]],
      orgPayPercentage: ["", [Validators.required]],
      remarks: [""],
    });
  }

  // --------------- Extra Curriculam Form Array ---------

  get extraCurrActFromArr() {
    return this.extraCurrForm.get("Rows") as FormArray;
  }

  initDocRows() {
    return this.formBuilder.group({
      extraCurrAct: [""],
      extraCurrActExp: [""],
      extraCurrActDesc: [""],
      extraCurrActAward: [""],
    });
  }

  addDocRow() {
    this.extraCurrActFromArr.push(this.initDocRows());
  }

  deleteDocRow(index: number) {
    this.extraCurrActFromArr.removeAt(index);
  }


  // patch value of Extra Curr. Act Array

  addRowExtraCurrAct(data) {
    this.extraCurrActFromArr.clear();
    for (let da of data) {
      this.extraCurrActFromArr.push(this.mapWithPrevExpTable(da));
    }

    this.spinnerService.hide();
  }

  mapWithPrevExpTable(data) {
    return this.formBuilder.group({
      extraCurrAct: data.extraCurrAct,
      extraCurrActExp: data.extraCurrActExp,
      extraCurrActDesc: data.extraCurrActDesc,
      extraCurrActAward: data.extraCurrActAward,
    });
  }

  // ----------------  Get Applicant Info by  Ref Coode -------------------------


  getAplicantInfo(refCode) {


    let apiURL = this.baseUrl + "/ambassadorTopSheet/getAmbassadorByRefCode?code=" + refCode;

    let queryParams: any = {};
    this.spinnerService.show();
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.topSheetInfo = response?.data;
        this.topSheetInfo.profilePicPath = response?.data?.profilePic;
        this.topSheetInfo.campusId = response?.data?.universityName;
        this.personalInfoForm.patchValue(this.topSheetInfo);
        this.instituteForm.patchValue(this.topSheetInfo);
        this.corporateInfoForm.patchValue(this.topSheetInfo);
        if (response?.data?.topSheetExtraCurricular) {
          this.addRowExtraCurrAct(response?.data?.topSheetExtraCurricular);
        }

        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.")
      }
    );


  }


  // ---------------- Personal Info Submit --------------------

  personalInfoSubmit() {

    const apiURL = this.baseUrl + "/ca/candidateInfo/create";

    let formData: any = {};
    formData = this.personalInfoForm.value;

    // process date
    formData.dateOfBirth = (formData.dateOfBirth) ? this.commonService.format_Date_Y_M_D(formData.dateOfBirth) : null;

    this.spinnerService.show();
    this.caService
      .sendPostRequest(apiURL, formData)
      .subscribe(
        (data: any) => {
          if (data.status === true) {
            this.spinnerService.hide();
            this.toastr.success(data.message);
            this.personalInfoId = data.data.id;

          } else {
            this.spinnerService.hide();
            this.toastr.error(data.message);
          }
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.error(error.message);
        }
      );
  }

  // ---------------- Get Personal Info By Id --------------------

  getPersonalInfoIdById(id) {
    const apiURL = this.baseUrl + "/ca/candidateInfo/get/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe((res) => {
      this.spinnerService.hide();
      this.personalInfoForm.patchValue(res.data);
      this.personalInfoForm.patchValue({
        dateOfBirth: this.datePipe.transform(
          res.data.dateOfBirth,
          "yyyy-MM-dd"
        ),
      });
    });
  }

  // ---------------- Institute Info Submit --------------------

  instituteInfoSubmit() {
    if (this.instituteForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/instituteInfo/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = Object.assign(this.instituteForm.value, {
      bscInstitute: this.instituteForm.controls.bscInstitute.value ? { id: this.instituteForm.controls.bscInstitute.value } : null,
      mscInstitute: this.instituteForm.controls.mscInstitute.value ? { id: this.instituteForm.controls.mscInstitute.value } : null,

    });
    for (let i = 0; i < this.extraCurrForm.value.Rows.length; i++) {
      this.extraCurrForm.value.Rows[i] = this.extraCurrForm.value.Rows[i];
    }

    formData.extraCurricularInfos = this.extraCurrForm.value.Rows;
    formData.candidateInfoId = this.personalInfoId;

    //send post request
    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.instituteId = res.data.id;
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this.instituteForm.reset();
          this.getAllInstitutes();
          this.getInstitutelInfoById(this.instituteId);
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.error(error.message);
      }
    );
  }

  // ---------------- Get Institute Info By ID --------------------

  getInstitutelInfoById(id) {
    const apiURL = this.baseUrl + "/ca/instituteInfo/get/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe((res) => {
      this.campusId = res.data.campusId;
      this.spinnerService.hide();
      this.instituteForm.patchValue(res.data);
      this.instituteForm.controls.bscInstitute.setValue(res?.data?.bscInstitute?.id);
      this.instituteForm.controls.mscInstitute.setValue(res?.data?.mscInstitute?.id);
    });
  }

  // ---------------- Corporate Info Submit --------------------

  corporateInfoSubmit() {
    if (this.corporateInfoForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/corporateInfo/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = this.corporateInfoForm.value;
    formData.candidateInfoId = this.personalInfoId;
    formData.inCharge = { id: this.corporateInfoForm.controls.inCharge.value };
    formData.concernHR = { id: this.corporateInfoForm.controls.concernHR.value };
    //process Date
    formData.joiningDate = (formData.joiningDate) ? this.commonService.format_Date_Y_M_D(formData.joiningDate) : null;

    //send post request
    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.corporateInfoId = res.data.id;
          this.uploaddocument(res?.data?.id);
          this.uploaddocument2(res?.data?.id);
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this.corporateInfoForm.reset();
          this.getCorporateInfoById(this.corporateInfoId);
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
      }
    );
  }

  // ---------------- Get Corporate Info By ID --------------------

  getCorporateInfoById(id: any) {
    const apiURL = this.baseUrl + "/ca/corporateInfo/get/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe((res) => {
      this.spinnerService.hide();
      this.corporateInfoForm.patchValue(res.data);
    });
  }


  // ---------------- KPI Info Submit --------------------

  kpiInfoSubmit() {
    if (this.kpiInfoForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/kpi/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = this.kpiInfoForm.value;
    formData.candidateInfoId = this.personalInfoId;

    //send post request
    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.corporateInfoId = res.data.id;
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this.corporateInfoForm.reset();
          this.getCorporateInfoById(this.corporateInfoId);
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
      }
    );
  }

  // ---------------- Get KPI Info By ID --------------------

  _getKpiByPersonalInfoId(id) {
    if (this.kpiInfoData === null || this.kpiInfoData.length <= 0) {
      const apiURL =
        this.baseUrl + "/ca/kpi/getByCandidateInfoId/" + id;
      this.spinnerService.show();

      let queryParams: any = {};
      this.caService.sendGetRequest(apiURL, queryParams).subscribe(
        (res) => {
          this.kpiInfoForm.patchValue(res.data);
          this.kpiInfoForm.patchValue({
            joiningDate: this.datePipe.transform(
              res.data.joiningDate,
              "yyyy-MM-dd"
            ),
          });
          this.kpiInfoData = res.data;
          this.spinnerService.hide();
        },
        (error) => {
          this.spinnerService.hide();
          this.toastr.error(error.message);
        }
      );
    }
  }

  // ---------------- Asset Info Submit --------------------

  assetInfoSubmit() {
    if (this.assetInfoForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/assets/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = this.assetInfoForm.value;
    formData.candidateInfoId = this.personalInfoId;
    formData.assetTotalPrice = formData.assetQuantity * formData.assetPrice;

    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.assetInfoId = res.data.id;
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this.assetInfoForm.reset();
          this.getAssetInfoById(this.personalInfoId);
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.error(error.message);
      }
    );
  }


  // ---------------- Get Asset Info By ID --------------------

  getAssetInfoById(id) {
    const apiURL = this.baseUrl + "/ca/assets/getAsset/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {
        this.assetInfo = res.data;
        $("#add_asset").modal("hide");
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.error(error.message);
      }
    );
  }


  // ---------------- Scholarship Info Submit --------------------

  scholarshipInfoSubmit() {
    if (this.scholarshipForm.invalid) {
      return;
    }
    const apiURL = this.baseUrl + "/ca/scholarship/create";
    this.spinnerService.show();

    let formData: any = {};
    formData = this.scholarshipForm.value;
    formData.candidateInfoId = this.personalInfoId;
    //process Date
    formData.provideDate = (formData.provideDate) ? this.commonService.format_Date_Y_M_D(formData.provideDate) : null;

    this.caService.sendPostRequest(apiURL, formData).subscribe(
      (res: any) => {
        if (res.status === true) {
          this.spinnerService.hide();
          this.toastr.success(res.message);
          this.getScolarshipInfoById(this.personalInfoId);
          this.scholarshipForm.reset();
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.error(error.message);
      }
    );
  }


  // ---------------- Get Scholarship By ID --------------------

  getScolarshipInfoById(id) {
    const apiURL = this.baseUrl + "/ca/scholarship/getScholarship/" + id;
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (res) => {
        this.scholarshipInfo = res.data;
        $("#add_scholarship").modal("hide");
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.error(error.message);
      }
    );
  }

  // Text area field character limitation

  prsntAdrsCharCount(event: any): void {
    this.numOfCharPrsntAdrs = event.target.value.length;

    if (this.numOfCharPrsntAdrs > this.maxNumberOfCharacters) {
      event.target.value = event.target.value.slice(
        0,
        this.maxNumberOfCharacters
      );
      this.numOfCharPrsntAdrs = this.maxNumberOfCharacters;
    }
  }

  prmntAdrsCharCount(event: any): void {
    this.numOfCharPrmntAdrs = event.target.value.length;

    if (this.numOfCharPrmntAdrs > this.maxNumberOfCharacters) {
      event.target.value = event.target.value.slice(
        0,
        this.maxNumberOfCharacters
      );
      this.numOfCharPrmntAdrs = this.maxNumberOfCharacters;
    }
  }

  psyIllCharCount(event: any): void {
    this.pillnessCharNum = event.target.value.length;

    if (this.pillnessCharNum > this.maxNumForPsyIllness) {
      event.target.value = event.target.value.slice(
        0,
        this.maxNumForPsyIllness
      );
      this.pillnessCharNum = this.maxNumForPsyIllness;
    }
  }

  exCurActCharCount(event: any): void {
    this.exCurActCharNum = event.target.value.length;

    if (this.exCurActCharNum > this.maxNumForExCurAct) {
      event.target.value = event.target.value.slice(0, this.maxNumForExCurAct);
      this.exCurActCharNum = this.maxNumForExCurAct;
    }
  }

  // KPI Calculation
  eventPerticipated(value) {
    this.eventPerticipatedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.eventPerticipatedValue) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  seminarPerticipated(value) {
    this.seminarPerticipatedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.seminarPerticipatedValue) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  productCommunication(value) {
    this.productCommunicationValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.productCommunicationValue) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  productShowcasing(value) {
    this.productShowcasingValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.productShowcasingValue) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  corporateEventsAttended(value) {
    this.corporateEventsAttendedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.corporateEventsAttendedValue) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  marketSurveyConducted(value) {
    this.marketSurveyConductedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.marketSurveyConductedValue) +
      parseFloat(this.kpiInfoForm.controls.industryCollaboration.value)
    );
  }

  industryCollaboration(value) {
    this.seminarPerticipatedValue = value;
    this.kpiInfoForm.controls.overAllKpi.setValue(
      parseFloat(this.kpiInfoForm.controls.eventPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.seminarPerticipated.value) +
      parseFloat(this.kpiInfoForm.controls.productCommunication.value) +
      parseFloat(this.kpiInfoForm.controls.productShowcasing.value) +
      parseFloat(this.kpiInfoForm.controls.corporateEventsAttended.value) +
      parseFloat(this.kpiInfoForm.controls.marketSurveyConducted.value) +
      parseFloat(this.seminarPerticipatedValue)
    );
  }

  piFrmCntrl() {
    return this.personalInfoForm.controls;
  }

  insFrmCntrl() {
    return this.instituteForm.controls;
  }

  corporateFrmCntrl() {
    return this.corporateInfoForm.controls;
  }

  kpiFrmCntrl() {
    return this.kpiInfoForm.controls;
  }

  pfResetFormValues() {
    this.personalInfoForm.reset();
  }

  instResetFormValues() {
    this.instituteForm.reset();
  }
  cfResetFormValues() {
    this.corporateInfoForm.reset();
  }

  kpiResetFormValues() {
    this.kpiInfoForm.reset();
  }

  // ---------------- Get All Institute --------------------


  getAllInstitutes() {
    const url = this.baseUrl + "/ca/campus/findAll";
    this.spinnerService.show();
    this.caService.sendGetRequest(url, null).subscribe(
      (res) => {
        this.spinnerService.hide();
        if (res.status == true) {
          this.instituteList = res.data;
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (err) => {
        this.spinnerService.hide();
        this.toastr.error(err.message);
      }
    );
  }

  // ---------------- Get  All Champions --------------------

  getChampion(campusId: any) {
    const url =
      this.baseUrl + "/ca/corporateInfo/getCampusChampion/" + campusId;
    this.spinnerService.show();
    this.caService.sendGetRequest(url, null).subscribe(
      (res) => {
        this.spinnerService.hide();
        if (res.status == true) {
          this.championList = res.data;
        } else {
          this.spinnerService.hide();
          this.toastr.error(res.message);
        }
      },
      (err) => {
        this.spinnerService.hide();
        this.toastr.error(err.message);
      }
    );
  }
  get f() {
    return this.personalInfoForm.controls;
  }




  // ------------------- Change document Picture ------------------

  onFileSelectDoc(event) {
    if (event.target.files.length > 0) {
      this.docSeleted = true;
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imageSrc = reader.result);
      this.uploadForm.get("document").setValue(file);
    }
  }

  onFileSelectDoc2(event) {
    if (event.target.files.length > 0) {
      this.docSeleted2 = true;
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imageSrc2 = reader.result);
      this.uploadForm2.get("document2").setValue(file);
    }
  }

  uploaddocument(id) {

    if (this.docSeleted) {

      let apiURL = this.baseUrl + "/ca/corporateInfo/document/" + id;

      const formData = new FormData();
      formData.append("file", this.uploadForm.get("document").value);
      formData.append("type", "file");

      this.caService.sendPostRequest(apiURL, formData).subscribe(
        (data) => {

          this.toastr.success("Top Sheet Document Uploaded Successfully");
          this.spinnerService.hide();

        },
        (error) => {
          this.toastr.warning("Top Sheet Document Not Uploaded");
          this.spinnerService.hide();
        }
      );
    }

    else {
      this.toastr.warning("Top Sheet Document Not Uploaded");
      this.spinnerService.hide();
    }

  }

  uploaddocument2(id) {

    if (this.docSeleted2) {

      let apiURL = this.baseUrl + "/ca/corporateInfo/uploadSignature/" + id;

      const formData = new FormData();
      formData.append("file", this.uploadForm2.get("document2").value);
      formData.append("type", "file");

      this.caService.sendPostRequest(apiURL, formData).subscribe(
        (data) => {

          this.toastr.success("Signature Uploaded Successfully");
          this.spinnerService.hide();
        },
        (error) => {
          this.toastr.warning("Signature File Not Uploaded");
          this.spinnerService.hide();
        }
      );
    }

    else {
      this.toastr.warning("Signature File Not Uploaded");
      this.spinnerService.hide();
    }

  }




  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
          this.configDDL.listData2 = this.configDDL.listData2.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
          this.configDDL.listData2 = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      listData2: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL,

  ) {

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;

    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------

}
