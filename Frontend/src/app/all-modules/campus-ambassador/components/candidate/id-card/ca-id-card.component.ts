import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { ReportsService } from 'src/app/all-modules/reports/service/reports.service';
import { CaService } from '../../../service/ca.service';

@Component({
  selector: 'app-ca-id-card',
  templateUrl: './ca-id-card.component.html',
  styleUrls: ['./ca-id-card.component.css']
})
export class CaIdCardComponent implements OnInit {


  dataLocalUrl: any;
  dataLocalUrl2: any;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private spinnerService: NgxSpinnerService,
    private reportsService: ReportsService,
    private domSanitizer: DomSanitizer,
    private caService: CaService
  ) { }


  ngOnInit(): void {

    this.loadIdCardPdfReport();
  }

  loadIdCardPdfReport() {

    let id = this.route.snapshot.params.id;

    let reportParams = {};
    reportParams['rptFileName'] = "campus_ambassador_id";
    reportParams['id'] = id;
    this.spinnerService.show();
    this.caService.idCardReport(reportParams).subscribe((response: any) => {

      const file = new Blob([response], { type: 'application/pdf' });
      const fileURL = URL.createObjectURL(file);
      this.dataLocalUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(fileURL);

      $('body').css('height', '100%');
      $('.main-wrapper').css('height', '100%');
      $('.main-wrapper .page-wrapper').css('height', '100%');
      $([document.documentElement, document.body]).animate({
        scrollTop: $("#idReportDisplay").offset().top
      }, 2000);

      this.spinnerService.hide();

    },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }


}
