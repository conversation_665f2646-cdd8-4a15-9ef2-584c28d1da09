<!-- Page Content -->
<div class="content container-fluid" style="height: 100%; padding-bottom: 5px;">


    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Campus Ambassador</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Ambassador</b></span></li>
                    <li class="breadcrumb-item active">ID Card</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/campusambassador/candidate/list"><i class="fa fa-share"></i>
                    Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->


    <!-- Report Display -->
    <div id="idReportDisplay" class="row" style="height: 100%;">

        <div class="col-sm-12" style="height: 100%;">
            <div *ngIf="dataLocalUrl != undefined" style="height: 100%;">
                <div class="card" style="height: 100%;">
                    <div class="card-body" style="height: 100%;">

                        <!--<h5>Report Display</h5>-->
                        <iframe [attr.src]="dataLocalUrl" type="application/pdf"
                            style="width: 100%; height: 100%; min-width: 700px; min-height: 550px;"></iframe>
                        <!-- <iframe width="100%" height="100%" frameBorder="0" [src]="urlSafe"></iframe> -->

                    </div>
                </div>
            </div>
        </div>

    </div>

</div>
<!-- /Page Content -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>