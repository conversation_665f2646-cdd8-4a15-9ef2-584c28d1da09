import { Component, OnInit } from '@angular/core';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';
import { CaService } from '../../../service/ca.service';

@Component({
  selector: 'app-ca-admin-dashboard',
  templateUrl: './ca-admin-dashboard.component.html',
  styleUrls: ['./ca-admin-dashboard.component.css']
})
export class CaAdminDashboardComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public user:any;
  public totalCandidate:any;
  public totalCampus:any;
  public totalCampusAmbassador:any;
  public totalCampusChampion:any;
  public totalScholarship:number;

  constructor( private loginService: LoginService,private caService: CaService)
  {

  }


  ngOnInit(): void {
    this.user = this.loginService.getUser();
    this.getTotalCanditates();
    this.getTotalCampus();
    this.getTotalCampusAmbassador();
    this.getCampusChampion();
    this.getTotalScholarship();
  }

  getTotalCanditates(){
    const apiUrl = this.baseUrl + '/ca/dashboard/totalCandidate';
    let queryParams: any = {};
    this.caService.sendGetRequest(apiUrl, queryParams).subscribe((resp:any)=>{
      this.totalCandidate = resp;

    });
  }

  getTotalCampus(){
    const apiUrl = this.baseUrl + '/ca/dashboard/totalCampus';
    let queryParams: any = {};
    this.caService.sendGetRequest(apiUrl, queryParams).subscribe((resp:any)=>{
      this.totalCampus = resp;

    });
  }

  getTotalCampusAmbassador(){
    const apiUrl = this.baseUrl + '/ca/dashboard/totalCA';
    let queryParams: any = {};
    this.caService.sendGetRequest(apiUrl, queryParams).subscribe((resp:any)=>{
      this.totalCampusAmbassador = resp;

    });
  }

  getCampusChampion(){
    const apiUrl = this.baseUrl + '/ca/dashboard/totalCampusChampion';
    let queryParams: any = {};
    this.caService.sendGetRequest(apiUrl, queryParams).subscribe((resp:any)=>{
      this.totalCampusChampion = resp;

    });
  }

  getTotalScholarship(){
    const apiUrl = this.baseUrl + '/ca/dashboard/totalScholarship';
    let queryParams: any = {};
    this.caService.sendGetRequest(apiUrl, queryParams).subscribe((resp:any)=>{
      this.totalScholarship = resp;

    });
  }

}
