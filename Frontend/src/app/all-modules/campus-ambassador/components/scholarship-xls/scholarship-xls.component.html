<!-- Page Content -->
<div class="content container-fluid">

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
               
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Campus Ambassador</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Scholarship Upload</b></span></li>
                   
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/sim/billUpload/list"><i class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">
                <div class="card-body">
                    <div class="col-sm">
                        <form [formGroup]="form" (ngSubmit)="onSubmit()">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div>
                                        <h6 class="head-title margin-top-8"><span>UPLOAD</span></h6>
                                    </div>
                                </div>

                                <div class="col-sm-6 text-center">

                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="customFile" name="myfile"
                                            (change)="onFileSelect($event)" #UploadFileInput>
                                        <label class="custom-file-label" for="customFile">{{fileInputLabel || 'Choose
                                            File'}}</label>
                                    </div>

                                </div>
                                <div class="col-sm-6 text-center">
                                    <button class="btn btn-primary" type="submit"><i class="fa fa-upload"
                                            aria-hidden="true"></i> Upload</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- XLS List -->

    <div class="pro-overview tab-pane fade show" id="scholarship_xls">
        <div class="card customCard">
            <div class="card-body">
                <h3>Scholarship XLS List</h3>

                <table id="scholarshipXlsTable" class="table table-striped custom-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Candidate Id</th>
                            <th>Provide Date</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let thisObj of xlsList let i = index" >
                            <td>{{ i+1 }}</td>
                            <td>{{thisObj.candidateInfoId}}</td>
                            <td>{{ thisObj.provideDate|date:'mediumDate' }}</td>
                            <td>{{ thisObj.amount }} </td>
                            
                        </tr>
                    </tbody>
                </table>


            </div>
        </div>
    </div>

    <!-- /XLS List -->

</div>
<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
    </ngx-spinner>