import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { SimService } from 'src/app/all-modules/sim/services/sim.service';
import { environment } from 'src/environments/environment';
import { CaService } from '../../service/ca.service';

@Component({
  selector: 'app-scholarship-xls',
  templateUrl: './scholarship-xls.component.html',
  styleUrls: ['./scholarship-xls.component.css']
})
export class ScholarshipXlsComponent implements OnInit {

  @ViewChild('UploadFileInput', { static: false }) uploadFileInput: ElementRef;

  baseUrl = environment.baseUrl;

  form: FormGroup;
  fileInputLabel: string;
  public xlsList: any = [];

  constructor(
    private route: ActivatedRoute,
    private currRouter: Router,
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private simService: SimService,
    private commonService: CommonService,
    private spinner: NgxSpinnerService,
    private spinnerService: NgxSpinnerService,
    private caService: CaService,
  ) { }

  ngOnInit(): void {

    $('body').addClass('mini-sidebar'); // sidebar always collapse

    this.getScholarshipXls();

    this.form = this.formBuilder.group({
      myfile: [""],
    });
  }


  // ======================== SUBMIT EXCEL FILE =====================


  onSubmit() {

    if (!this.form.get('myfile').value) {
      alert('Please fill valid details!');
      return false;
    }
    const apiURL = this.baseUrl + "/ca/scholarship/uploadXls";
    const formData = new FormData();
    formData.append('file', this.form.get('myfile').value);
    this.spinner.show();
    this.simService.sendPostRequestOfBillUploadFile(apiURL, formData).subscribe((data: any) => {
      // 1 sec delay
      setTimeout(() => {
        this.form.reset();
        this.toastr.success('Scholarship Uploaded Successfully');
        this.getScholarshipXls();
      }, 3000);

      //hide spinner
      this.spinner.hide();

    }, (error) => {
      this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      this.spinnerService.hide();
    });

  }

  // ====================== SELECT FILE ====================

  onFileSelect(event) {
    let af = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      let fileName1 = file.type;
      if (fileName1 !== 'application/vnd.ms-excel' && fileName1 !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        this.toastr.warning("Only EXCEL files are allowed!");
        return;
      } else {
        this.fileInputLabel = file.name;
        this.form.get('myfile').setValue(file);
      }
    }
  }

  // ======================== GET ALL LIST =======================

  getScholarshipXls() {

    const apiURL = this.baseUrl + "/ca/scholarship/getLists";
    this.spinnerService.show();

    let queryParams: any = {};
    this.caService.sendGetRequest(apiURL, queryParams).subscribe((res) => {

      if (res.status == true) {
        this.spinnerService.hide();
        this.xlsList = res.data;
      } else {
        this.spinnerService.hide();
        this.toastr.warning(res.message);
      }

    }, (error) => {
      this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      this.spinnerService.hide();
    });
  }

}
