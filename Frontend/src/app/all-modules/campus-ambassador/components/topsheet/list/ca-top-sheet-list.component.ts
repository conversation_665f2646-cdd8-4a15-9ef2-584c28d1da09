import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { environment } from 'src/environments/environment';
declare const $: any;

@Component({
  selector: 'app-ca-top-sheet-list',
  templateUrl: './ca-top-sheet-list.component.html',
  styleUrls: ['./ca-top-sheet-list.component.css']
})
export class CaTopSheetListComponent implements OnInit {

  public listData: any = [];
  baseUrl = environment.baseUrl;
  myFromGroup: FormGroup;
  tempId: any;


  configPgn: any;
  private joiningDate: string;
  private phone: string;
  private code: string;
  private email: string;

  showDivv = {
    cLocation: false,
    Organization: false,
  }

  constructor(
    private spinnerService: NgxSpinnerService,
    private employeeService: EmployeeService,
    private datePipe: DatePipe,
    private toastr: ToastrService

  ) {

    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };

  }

  ngOnInit(): void {

    this.code = "";
    this.joiningDate = "";
    this.phone = "";
    this.email = "";
    // set init params
    this.myFromGroup = new FormGroup({
      pageSize: new FormControl()
    });
    this.myFromGroup.get('pageSize').setValue(this.configPgn.pageSize);

    this.getAllData();

  }

  clearEverything() {
    this.code = "";
    this.joiningDate = "";
    this.phone = "";
    this.email = "";
    $('#phoneId').val('');
    $('#joiningDateId').val('');
    $('#codeId').val('');
    $('#emailId').val('');

    this.getAllData();

  }



  setjoiningDate(val) {
    this.joiningDate = this.datePipe.transform(val, "yyyy-MM-dd");
  }
  setphone(val) {
    this.phone = val;
  }
  setCode(val) {
    this.code = val;
  }
  setemail(val) {
    this.email = val;

  }


  searchFunction() {

  }

  // ------------------pagination handling methods start -----------------------------

  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {
    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this.getAllData();
  }
  handlePageSizeChange(event: any): void {
    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this.getAllData();
  }

  // ------------------------------pagination handling methods end -------------------------------------------

  getAllData() {

    let apiURL = this.baseUrl + "/ambassadorTopSheet/getList";

    let queryParams = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);


    this.spinnerService.show();

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(

      (response: any) => {

        this.listData = response.objectList;
        this.configPgn.totalItem = response.totalItems;
        this.configPgn.totalItems = response.totalItems;
        this.setDisplayLastSequence();
        this.spinnerService.hide();

      },


      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }



  deleteEnityData(Id) {

    let apiURL = this.baseUrl + "/ambassadorTopSheet/delete/" + Id;
    let formData: any = {};
    this.spinnerService.show();
    this.employeeService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {
        $("#delete_entity").modal("hide");
        this.toastr.success("Successfully item is deleted", "Success");
        this.getAllData();
        this.spinnerService.hide();
      },
      (error) => {
        $("#delete_entity").modal("hide");
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }

    );
  }

  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    if (this.code) {
      params["refCode"] = this.code;
    }

    if (this.joiningDate) {
      params["joiningDate"] = this.joiningDate;
    }

    if (this.phone) {
      params["phone"] = this.phone;
    }

    if (this.email) {
      params["email"] = this.email;
    }

    return params;

  }

}
