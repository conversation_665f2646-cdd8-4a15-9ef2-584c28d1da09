<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Campus Ambassador</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Top Sheet</b></span></li>
                    <li class="breadcrumb-item active">List </li>
                </ul>

            </div>
        </div>
    </div>
    <!-- /Page Header -->


    <!-----  Search  Parameter start  -->

    <div class="card mb-2" style="background-color:transparent;">
        <div class="card-body p-3">

            <div class="row ">


                <div class="col-sm-6 col-md-2 col-lg-2 col-xl-2 col-xxl-2 col-12">
                    <div class="form-group">
                        <label>Ref. Code</label>
                        <input type="text" class="form-control" id="codeId" (input)="setCode($event.target.value)"
                            (keyup.enter)="searchFunction()">

                    </div>
                </div>

                <div class="col-sm-6 col-md-2 col-lg-2 col-xl-2 col-xxl-2 col-12">
                    <div class="form-group">
                        <label>Phone</label>
                        <input type="text" class="form-control" id="phoneId" (input)="setphone($event.target.value)">

                    </div>
                </div>

                <div class="col-sm-6 col-md-2 col-lg-2 col-xl-2 col-xxl-2 col-12">
                    <div class="form-group">
                        <label>Email</label>
                        <input type="text" class="form-control" id="emailId" (input)="setemail($event.target.value)">

                    </div>
                </div>

                <!-- 
                <div class="col-sm-6 col-md-2 col-lg-2 col-xl-2 col-xxl-2 col-12">
                    <div class="form-group">
                        <label>Approval Status</label>
                        <select id="setaprvlstatusId" class="select form-control"
                            (input)="setaprvlStatus($event.target.value)">
                            <option value="">Select Status</option>
                            <option value="Submitted">Submitted</option>
                            <option value="Recommended">Recommended</option>
                            <option value="Rejected">Rejected</option>
                        </select>

                    </div>
                </div> -->

                <div class="col-sm-6 col-md-2 col-lg-2 col-xl-2 col-xxl-2 col-12">
                    <div class="form-group">
                        <label>Joining Date</label>

                        <div class="cal-icon">
                            <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker type="text"
                                placeholder="DD-MM-YYYY"
                                [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                                (bsValueChange)="setjoiningDate($event)">
                        </div>

                    </div>
                </div>



                <div class="col-md-4 mt-4">
                    <a class="btn btn-success btn-ripple" (click)="getAllData()"> <i class="fa fa-search"></i>
                        Search</a> &nbsp;

                    <button type="button" class="btn btn-danger btn-ripple" (click)="clearEverything()">
                        <i class="fa fa-eraser"></i> Clear
                    </button>

                </div>



            </div>

        </div>
    </div>

    <!-----  Search  Parameter end-->

    <!-- list view start -->
    <div class="row">
        <div class="col-md-12">
            <div class="card customCard">
                <div class="card-header">
                    <div class="card-tools">
                        <a class="btn btn-outline-primary" routerLink="/campusambassador/candidate/top-sheet/create"><i
                                class="fa fa-plus"></i> New &nbsp;&nbsp;&nbsp;</a>


                    </div>
                </div>
                <div class="card-body ">
                    <div class="table-responsive">
                        <div class="d-flex justify-content-start pb-1">
                            <div class="pgn-displayDataInfo">
                                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                                    configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                                    {{configPgn.totalItem}} ) entries</span>
                            </div>
                        </div>
                        <table class="table table-striped custom-table datatable">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Ref. Code</th>
                                    <th>Photo</th>
                                    <th>Name</th>
                                    <th>Campus Code</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Created By</th>
                                    <th>Joining Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr *ngFor="let row of listData| paginate : configPgn; let i = index"
                                    [class.active]="i == currentIndex">
                                    <td>
                                        {{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) ) }}
                                    </td>

                                    <td>
                                        {{row.code}}
                                    </td>
                                    <td>
                                        <span class="table-avatar">

                                            <img class="img-avatar text-center" alt=""
                                                style="height: 60px; width: 55px;" src="{{ baseUrl + row.profilePic}}">
                                        </span>
                                    </td>

                                    <td>

                                        {{row.name}}
                                    </td>
                                    <td>{{row?.universityCode}}</td>
                                    <td>{{row?.phone}}</td>
                                    <td>{{row?.email}}</td>
                                    <td>{{row?.createdBy?.loginCode}} - {{row?.createdBy?.displayName}}</td>
                                    <td>{{row?.joiningDate | date}}</td>



                                    <td>
                                        <a class="btn btn-sm btn-primary"
                                            routerLink="/campusambassador/candidate/top-sheet/view/{{row.id}}"><i
                                                class="fa fa-eye m-r-5"></i></a> &nbsp;
                                        <a class="btn btn-sm btn-info"
                                            routerLink="/campusambassador/candidate/top-sheet/edit/{{row.id}}"><i
                                                class="fa fa-pencil m-r-5"></i></a>&nbsp;&nbsp;

                                        <a class="btn btn-sm btn-danger" data-toggle="modal"
                                            data-target="#delete_entity" (click)="tempId = row.id">
                                            <i class="fa fa-trash-o m-r-5"></i>
                                        </a>
                                    </td>


                                </tr>
                                <tr *ngIf="listData?.length === 0">
                                    <td colspan="10">
                                        <h5 style="text-align: center;">No data found</h5>
                                    </td>

                                </tr>

                            </tbody>

                        </table>
                        <div class="d-flex justify-content-end ">

                            <div class="" [formGroup]="myFromGroup">
                                Items per Page
                                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption"
                                    formControlName="pageSize">
                                    <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                                        {{ size }}
                                    </option>
                                </select>
                            </div>

                            <div class="pgn-pageSliceCt">
                                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                                    (pageChange)="handlePageChange($event)">
                                </pagination-controls>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- list view end -->


    <!-- Delete Modal -->
    <div class="modal custom-modal fade" id="delete_entity" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="form-header">
                        <h3>Delete Item</h3>
                        <p>Are you sure want to delete?</p>
                    </div>
                    <div class="modal-btn delete-action">
                        <div class="row">
                            <div class="col-6">
                                <a class="btn btn-primary continue-btn" (click)="deleteEnityData(tempId)">Delete</a>
                            </div>
                            <div class="col-6">
                                <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Delete Modal -->


</div>




<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>