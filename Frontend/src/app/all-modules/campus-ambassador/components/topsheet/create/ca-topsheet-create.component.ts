import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { TaskService } from 'src/app/all-modules/taskmanagement/task/service/task.service';
import { environment } from 'src/environments/environment';
declare const $: any;

@Component({
  selector: 'app-ca-topsheet-create',
  templateUrl: './ca-topsheet-create.component.html',
  styleUrls: ['./ca-topsheet-create.component.css']
})
export class CaTopsheetCreateComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  public isSubmitted = false;
  public extraCurrForm: FormGroup;
  public interviewForm: FormGroup;
  uploadForm: FormGroup;
  instituteList: any = [];
  docSeleted: boolean = false;

  imgSrc: string | ArrayBuffer;
  imageSrc: any;
  imageSrc2 = "assets/img/user-icon/u-sq-pic.jpg";

  // for multi select
  public configDDL: any;
  public configPgn: any;
  public user: any;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService,
  ) {
    this.configPgn = {
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      totalItem: 50,
      pngDiplayLastSeq: 10,
      entityName: "",
    };

  }

  ngOnInit(): void {
    this._initForm();
    this.getAllInstitutes();

    this.uploadForm = this.formBuilder.group({
      profilePic: ["", [Validators.required]],
    });

    this.extraCurrForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()]),
    });

    this.interviewForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows2()]),
    });

  }

  _initForm() {
    this.myForm = this.formBuilder.group({
      name: ["", [Validators.required]],
      dateOfBirth: ["", [Validators.required]],
      fatherName: ["", [Validators.required]],
      motherName: ["", [Validators.required]],
      presentAddress: ["", [Validators.required]],
      permanentAddress: ["", [Validators.required]],
      bloodGroup: ["", [Validators.required]],
      nationalId: ["", [Validators.required]],
      email: [
        "",
        [
          Validators.required,
          Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ],
      ],
      phone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
      maritalStatus: ["", [Validators.required]],
      height: [""],
      weight: [""],


      belowSscInstitute: [""],
      belowSscGroup: [""],
      belowSscBoard: [""],
      educationYear: [""],

      sscTitle: [""],
      sscInstitute: [""],
      sscGroup: [""],
      sscBoard: [""],
      sscPassingYear: [""],
      sscResult: [""],
      sscGpaOutOf4: null,
      sscGpaOutOf5: null,

      hscTitle: [""],
      hscInstitute: [""],
      hscGroup: [""],
      hscBoard: [""],
      hscPassingYear: [""],
      hscResult: [""],
      hscGpaOutOf4: null,
      hscGpaOutOf5: null,

      bscTitle: [""],
      bscInstituteId: [""],
      bscSubjectOrDegree: [""],
      bscPassingYear: [""],
      bscResult: [""],
      bscGpaOutOf4: null,
      bscGpaOutOf5: null,
      bscCourseDuration: [""],

      mscTitle: [""],
      mscInstituteId: [""],
      mscSubjectOrDegree: [""],
      mscPassingYear: [""],
      mscResult: [""],
      mscGpaOutOf4: null,
      mscGpaOutOf5: null,
      mscCourseDuration: [""],


      topSheetExtraCurricular: [""],
      topSheetInterview: [""],

      campusCoordinatorName: ["", [Validators.required]],
      campusCoordinatorPhone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
      referenceName: ["", [Validators.required]],
      referenceDesignation: ["", [Validators.required]],
      referenceIdNo: [""],
      referenceDepartment: ["", [Validators.required]],
      referencePhone: ["", [Validators.required, Validators.pattern("[0-9 ]{11}")]],
      relation: ["", [Validators.required]],
      institutionEIIN: [""],
      scholarShip: ["", [Validators.required]],
      department: ["", [Validators.required]],
      legalEntity: ["", [Validators.required]],
      section: ["", [Validators.required]],
      subSection: ["", [Validators.required]],
      team: ["", [Validators.required]],
      officialDesignation: ["", [Validators.required]],
      universityName: ["", [Validators.required]],
      duration: ["", [Validators.required]],
      joiningDate: [""],
      hrComment: [""],

    });
  }


  // ------------------- Change profilePic Picture ------------------

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      this.docSeleted = true;
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imageSrc = reader.result);
      this.uploadForm.get("profilePic").setValue(file);
      $("#profilePic_Image").modal("hide");
    }
  }

  uploadProfilePicture(id) {

    if (this.docSeleted) {

      let apiURL = this.baseUrl + "/ambassadorTopSheet/profilePic/" + id;
      let queryParams: any = {};

      this.commonService.sendGetRequest(apiURL, queryParams).subscribe

      const formData = new FormData();
      formData.append("file", this.uploadForm.get("profilePic").value);
      formData.append("type", "file");

      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (data) => {

          this.toastr.success("Profile Picture Uploaded Successfully");
          this.router.navigate(['/campusambassador/candidate/top-sheet']);
        },
        (error) => {
          this.router.navigate(['/campusambassador/candidate/top-sheet']);
        }
      );
    }

    else {
      this.router.navigate(['/campusambassador/candidate/top-sheet']);
    }

  }


  // ------------------------- Get All Institute -------------------------


  getAllInstitutes() {
    const url = this.baseUrl + "/ca/campus/findAll";
    this.spinnerService.show();
    this.commonService.sendGetRequest(url, {}).subscribe(
      (res: any) => {
        this.spinnerService.hide();

        this.instituteList = res.data;

      },
      (err) => {
        this.spinnerService.hide();
        this.toastr.error(err.message);
      }
    );
  }


  // --------------- Extra Curriculam Form Array ---------

  get extraCurrActFromArr() {
    return this.extraCurrForm.get("Rows") as FormArray;
  }

  initDocRows() {
    return this.formBuilder.group({
      extraCurrAct: [""],
      extraCurrActExp: [""],
      extraCurrActDesc: [""],
      extraCurrActAward: [""],
    });
  }


  addDocRow() {
    this.extraCurrActFromArr.push(this.initDocRows());
  }

  deleteDocRow(index: number) {
    this.extraCurrActFromArr.removeAt(index);
  }

  // ---------------Interview Form Array ---------

  get interviewFormArr() {
    return this.interviewForm.get("Rows") as FormArray;
  }



  initDocRows2() {
    return this.formBuilder.group({
      juryMemberId: null,
      juryMemberCode: ["", Validators.required],
      juryMemberName: ["", Validators.required],
      writtenMark: [""],
      vivaMark: [""],
      presentationMark: [""],
      totalMark: [""],
    });
  }



  addDocRow2() {
    this.interviewFormArr.push(this.initDocRows2());
  }


  deleteDocRow2(index: number) {
    this.interviewFormArr.removeAt(index);
  }


  // ----------------- Get Employee Info ---------------

  getEmployeeInfo(i) {

    this.spinnerService.show();
    let empId = this.interviewForm.value.Rows[i].juryMemberCode;
    let apiURL = this.baseUrl + "/hrCrEmp/getEmpDataByLoginCode/" + empId;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        let formData = response;
        formData.juryMemberName = response.employeeName;
        formData.juryMemberId = response.id;

        this.setFormValues(formData, i);
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.error(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    )
  }

  setFormValues(response, i) {
    this.interviewFormArr.controls[i].patchValue(response);
  }


  // ---------------------------- Form Submit ----------------------


  myFormSubmit() {



    const apiURL = this.baseUrl + '/ambassadorTopSheet/create';

    this.isSubmitted = true;
    if (this.myForm.invalid) {
      return;
    }

    let formData: any = {};
    formData = this.myForm.value;

    for (let i = 0; i < this.extraCurrForm.value.Rows.length; i++) {
      this.extraCurrForm.value.Rows[i] = this.extraCurrForm.value.Rows[i];
    }

    for (let i = 0; i < this.interviewForm.value.Rows.length; i++) {
      this.interviewForm.value.Rows[i] = this.interviewForm.value.Rows[i];
    }

    formData.topSheetExtraCurricular = this.extraCurrForm.value.Rows;
    formData.topSheetInterview = this.interviewForm.value.Rows;

    // process date
    formData.dateOfBirth = (formData.dateOfBirth) ? this.commonService.format_Date_Y_M_D(formData.dateOfBirth) : null;
    formData.joiningDate = (formData.joiningDate) ? this.commonService.format_Date_Y_M_D(formData.joiningDate) : null;

    this.spinnerService.show();

    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        if (response.status === true) {
          this.toastr.success("Ambassador Created SUccessfully");

          this.uploadProfilePicture(response?.data.id);
        } else {
          this.spinnerService.hide();
          this.toastr.warning(response.message, 'Error');
        }

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );



  }

  get f() { return this.myForm.controls; }

  resetFormValues() {
    this.myForm.reset();
  }

}
