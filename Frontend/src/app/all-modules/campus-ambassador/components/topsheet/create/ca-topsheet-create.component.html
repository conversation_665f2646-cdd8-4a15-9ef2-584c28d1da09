<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Campus Ambassador</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Top Sheet</b></span></li>
                    <li class="breadcrumb-item active">Create </li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/campusambassador/candidate/top-sheet"><i class="fa fa-share"></i>
                    Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body">

                    <div class="row">
                        <div class="col-md-12">
                            <div class="profile-view ">
                                <div class="profile-img-wrap">
                                    <div class="profile-img">
                                        <a *ngIf="imageSrc" [routerLink]="" class="avatar"
                                            data-target="#profilePic_Image" data-toggle="modal"><img id="companyLogo"
                                                data-flag="1" [src]="imageSrc" alt="" /></a>

                                        <a *ngIf="!imageSrc" [routerLink]="" class="avatar"
                                            data-target="#profilePic_Image" data-toggle="modal"><img id="companyLogo"
                                                data-flag="1" [src]="imageSrc2" alt="" /></a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <form novalidate (ngSubmit)="myFormSubmit()" [formGroup]="myForm">


                        <h3 class="card-title mt-2">Personal Information</h3>
                        <hr />

                        <div class="row">


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Full Name</label>

                                    <input type="text" class="form-control" formControlName="name">

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Date of Birth</label>

                                    <div class="cal-icon">
                                        <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker
                                            type="text" placeholder="DD-MM-YYYY"
                                            [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                                            formControlName="dateOfBirth">
                                    </div>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Father's Name </label>
                                    <input type="text" class="form-control" formControlName="fatherName">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Mother's Name</label>
                                    <input type="text" class="form-control" formControlName="motherName">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Candidate Phone</label>

                                    <input type="text" class="form-control" formControlName="phone">

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Candidate Email</label>

                                    <input type="email" class="form-control" formControlName="email">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">NID/Birth Certificate No </label>

                                    <input type="text" class="form-control" formControlName="nationalId">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Blood Group </label>
                                    <select class="form-control" formControlName="bloodGroup">
                                        <option value="">Select Blood Group</option>
                                        <option value="A+">A+</option>
                                        <option value="A-">A-</option>
                                        <option value="B+">B+</option>
                                        <option value="B-">B-</option>
                                        <option value="AB+">AB+</option>
                                        <option value="AB-">AB-</option>
                                        <option value="O+">O+</option>
                                        <option value="O-">O-</option>
                                    </select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Marital Status </label>
                                    <select class="form-control" formControlName="maritalStatus">
                                        <option value="">Select Marital Status</option>
                                        <option value="Single">Single</option>
                                        <option value="Married">Married</option>
                                        <option value="Divorced">Divorced</option>
                                        <option value="Widow">Widow</option>

                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Height</label>

                                    <input type="text" class="form-control" formControlName="height"
                                        placeholder="Example: 5 ft 11 inch">

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Weight </label>

                                    <input type="text" class="form-control" formControlName="weight"
                                        placeholder="Example: 85 kg">

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Present Address</label>

                                    <textarea class="form-control" formControlName="presentAddress"
                                        placeholder="Village / area, post office, police station, district"
                                        rows="1"></textarea>


                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Permanent Address</label>

                                    <textarea class="form-control" formControlName="permanentAddress"
                                        placeholder="Village / area, post office, police station, district"
                                        rows="1"></textarea>


                                </div>
                            </div>


                        </div>


                        <h3 class="card-title mt-2">Education Information</h3>

                        <h3 class="card-title mt-2">Below SSC</h3>
                        <hr />

                        <div class="row">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Institute Name </label>
                                    <input type="text" class="form-control" formControlName="belowSscInstitute"
                                        placeholder="School Name">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Group / Subject / Class</label>
                                    <input type="text" class="form-control" formControlName="belowSscGroup"
                                        placeholder="Class 8">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Board </label>
                                    <select class="form-control" formControlName="belowSscBoard">
                                        <option value=""> Select </option>
                                        <option value="Dhaka">Dhaka</option>
                                        <option value="Cumilla">Cumilla</option>
                                        <option value="Rajshahi">Rajshahi</option>
                                        <option value="Jashore">Jashore</option>
                                        <option value="Chittagong">Chittagong</option>
                                        <option value="Barishal">Barishal</option>
                                        <option value="Sylhet">Sylhet</option>
                                        <option value="Dinajpur">Dinajpur</option>
                                        <option value="Madrasah">Madrasah</option>
                                        <option value="Mymensingh">Mymensingh</option>
                                        <option value="Pharmacy Council of Bangladesh">Pharmacy Council of Bangladesh
                                        </option>
                                        <option value="Cambridge International - IGCE">Cambridge International - IGCE
                                        </option>
                                        <option value="Edexcel International">Edexcel International</option>
                                        <option value="Bangladesh Technical Education Board (BTEB)">Bangladesh Technical
                                            Education
                                            Board
                                            (BTEB)</option>
                                        <option value="Open University">Open University</option>
                                        <option value="Others">Others</option>
                                    </select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Education Year</label>
                                    <input type="text" class="form-control" formControlName="educationYear">

                                </div>
                            </div>

                        </div>


                        <h3 class="card-title mt-2">SSC / O Levels</h3>
                        <hr />


                        <div class="row">


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Exam Name </label>
                                    <select class="form-control" formControlName="sscTitle">
                                        <option value="">Select</option>
                                        <option value="SSC">SSC</option>
                                        <option value="Dakhil">Dakhil</option>
                                        <option value="SSC Vocational">SSC Vocational</option>
                                        <option value="O Level/Cambridge">O Level/Cambridge</option>
                                        <option value="SSC Equivalent">SSC Equivalent</option>
                                        <option value="Dakhil Vocational">Dakhil Vocational</option>
                                    </select>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Institute </label>
                                    <input type="text" class="form-control" formControlName="sscInstitute"
                                        placeholder="SSC Institute Name">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <label>Group / Subject </label>

                                <select class="form-control" formControlName="sscGroup">
                                    <option value=""> Select </option>
                                    <option value="Science">Science</option>
                                    <option value="Humanities">Humanities</option>
                                    <option value="Business Studies">Business Studies</option>
                                    <option value="Agriculture Technology">Agriculture Technology</option>
                                    <option value="Architecture and Interior Design Technology">Architecture and
                                        Interior Design
                                        Technology</option>
                                    <option value="Automobile Technology">Automobile Technology</option>
                                    <option value="Civil Technology">Civil Technology</option>
                                    <option value="Computer Science &amp; Technology">Computer Science &amp; Technology
                                    </option>
                                    <option value="Chemical Technology">Chemical Technology</option>
                                    <option value="Electrical Technology">Electrical Technology</option>
                                    <option value="Data Telecommunication and Network Technology">Data Telecommunication
                                        and
                                        Network
                                        Technology</option>
                                    <option value="Electrical and Electronics Technology">Electrical and Electronics
                                        Technology
                                    </option>
                                    <option value="Environmental Technology">Environmental Technology</option>
                                    <option value="Instrumentation &amp; Process Control Technology">Instrumentation
                                        &amp; Process
                                        Control
                                        Technology
                                    </option>
                                    <option value="Mechanical Technology">Mechanical Technology</option>
                                    <option value="Mechatronics Technology">Mechatronics Technology</option>
                                    <option value="Power Technology">Power Technology</option>
                                    <option value="Refregeration &amp; Air Conditioning Technology">Refregeration &amp;
                                        Air
                                        Conditioning
                                        Technology
                                    </option>
                                    <option value="Telecommunication Technology">Telecommunication Technology</option>
                                    <option value="Electronics Technology">Electronics Technology</option>
                                    <option value="Library Science">Library Science</option>
                                    <option value="Survey">Survey</option>
                                    <option value="General Mechanics">General Mechanics</option>
                                    <option value="Firm Machinery">Firm Machinery</option>
                                    <option value="Textile Technology">Textile Technology</option>
                                    <option value="Food">Food</option>
                                    <option value="Glass and Ceramic">Glass and Ceramic</option>
                                    <option value="Agro-Based Food">Agro-Based Food</option>
                                    <option value="General Electronics">General Electronics</option>
                                    <option value="Automotive">Automotive</option>
                                    <option value="Building Maintenance">Building Maintenance</option>
                                    <option value="Wood Working">Wood Working</option>
                                    <option value="Ceramic">Ceramic</option>
                                    <option value="Civil Construction">Civil Construction</option>
                                    <option value="Computer and Information Technology">Computer and Information
                                        Technology
                                    </option>
                                    <option value="Civil Drafting with CAD">Civil Drafting with CAD</option>
                                    <option value="Mechanical Drafting with CAD">Mechanical Drafting with CAD</option>
                                    <option value="Dress Making">Dress Making</option>
                                    <option value="Dyeing, Printing and Finishing">Dyeing, Printing and Finishing
                                    </option>
                                    <option value="Electrical Maintenance Works">Electrical Maintenance Works</option>
                                    <option value="Farm Machinery">Farm Machinery</option>
                                    <option value="Fish Culture and Breeding">Fish Culture and Breeding</option>
                                    <option value="Food Processing and Preservation">Food Processing and Preservation
                                    </option>
                                    <option value="Livestock Rearing and Farming">Livestock Rearing and Farming</option>
                                    <option value="Machine Tools Operation">Machine Tools Operation</option>
                                    <option value="Poultry Rearing and Farming">Poultry Rearing and Farming</option>
                                    <option value="Patient Care">Patient Care</option>
                                    <option value="General Electrical Works">General Electrical Works</option>
                                    <option value="Plumbing and Pipe Fittings">Plumbing and Pipe Fittings</option>
                                    <option value="Refrigeration and Air Conditioning">Refrigeration and Air
                                        Conditioning</option>
                                    <option value="Glass">Glass</option>
                                    <option value="Flower, Fruit and Vegetable Cultivation">Flower, Fruit and Vegetable
                                        Cultivation
                                    </option>
                                    <option value="Weaving">Weaving</option>
                                    <option value="Welding and Fabrication">Welding and Fabrication</option>
                                    <option value="Architectural Drafting with CAD">Architectural Drafting with CAD
                                    </option>
                                    <option value="Knitting">Knitting</option>
                                    <option value="Shrimp Culture and Breeding">Shrimp Culture and Breeding</option>
                                    <option value="Others">Others</option>
                                </select>
                            </div>




                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Board </label>
                                    <select class="form-control" formControlName="sscBoard">
                                        <option value=""> Select </option>
                                        <option value="Dhaka">Dhaka</option>
                                        <option value="Cumilla">Cumilla</option>
                                        <option value="Rajshahi">Rajshahi</option>
                                        <option value="Jashore">Jashore</option>
                                        <option value="Chittagong">Chittagong</option>
                                        <option value="Barishal">Barishal</option>
                                        <option value="Sylhet">Sylhet</option>
                                        <option value="Dinajpur">Dinajpur</option>
                                        <option value="Madrasah">Madrasah</option>
                                        <option value="Mymensingh">Mymensingh</option>
                                        <option value="Pharmacy Council of Bangladesh">Pharmacy Council of Bangladesh
                                        </option>
                                        <option value="Cambridge International - IGCE">Cambridge International - IGCE
                                        </option>
                                        <option value="Edexcel International">Edexcel International</option>
                                        <option value="Bangladesh Technical Education Board (BTEB)">Bangladesh Technical
                                            Education
                                            Board
                                            (BTEB)</option>
                                        <option value="Open University">Open University</option>
                                        <option value="Others">Others</option>
                                    </select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Result </label>
                                    <select class="form-control" formControlName="sscResult">
                                        <option value=""> Select </option>
                                        <option value="1st Division">1st Division</option>
                                        <option value="2nd Division">2nd Division</option>
                                        <option value="Running">Running</option>
                                        <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                                        <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                                    </select>
                                    <!-- if select Gpa(out of 5) then open extra input field -->
                                    <div *ngIf="myForm.get('sscResult').value == 'GPA(Out of 5)'">
                                        <input type="number" class="form-control" formControlName="sscGpaOutOf5"
                                            placeholder="Type here your GPA">
                                    </div>
                                    <!-- if select Gpa(out of 4) then open extra input field -->
                                    <div *ngIf="myForm.get('sscResult').value == 'GPA(Out of 4)'">
                                        <input type="number" class="form-control" formControlName="sscGpaOutOf4"
                                            placeholder="Type here your GPA">
                                    </div>

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Passing Year </label>
                                    <input type="text" class="form-control" formControlName="sscPassingYear">

                                </div>
                            </div>

                        </div>



                        <h3 class="card-title mt-2">HSC / A Levels</h3>
                        <hr />


                        <div class="row">


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Exam Name </label>
                                    <select class="form-control" formControlName="hscTitle">
                                        <option value=""> Select </option>
                                        <option value="H.S.C">H.S.C</option>
                                        <option value="Diploma-in-Engineering (4 Years)">Diploma-in-Engineering (4
                                            Years)</option>
                                        <option value="A Level/Sr. Cambridge">A Level/Sr. Cambridge</option>
                                    </select>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Institute </label>
                                    <input type="text" class="form-control" formControlName="hscInstitute"
                                        placeholder="HSC Institute Name">
                                    <span
                                        *ngIf="myForm.get('sscInstitute').invalid && myForm.get('hscInstitute').errors.pattern"
                                        class="text-danger">Invalid Roll
                                    </span>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <label>Group / Subject </label>
                                <select class="form-control" formControlName="hscGroup">
                                    <option value=""> Select </option>
                                    <option value="Science">Science</option>
                                    <option value="Humanities">Humanities</option>
                                    <option value="Business Studies">Business Studies</option>
                                    <option value="Agriculture Technology">Agriculture Technology</option>
                                    <option value="Architecture and Interior Design Technology">Architecture and
                                        Interior Design
                                        Technology</option>
                                    <option value="Automobile Technology">Automobile Technology</option>
                                    <option value="Civil Technology">Civil Technology</option>
                                    <option value="Computer Science &amp; Technology">Computer Science &amp; Technology
                                    </option>
                                    <option value="Chemical Technology">Chemical Technology</option>
                                    <option value="Electrical Technology">Electrical Technology</option>
                                    <option value="Data Telecommunication and Network Technology">Data Telecommunication
                                        and
                                        Network
                                        Technology</option>
                                    <option value="Electrical and Electronics Technology">Electrical and Electronics
                                        Technology
                                    </option>
                                    <option value="Environmental Technology">Environmental Technology</option>
                                    <option value="Instrumentation &amp; Process Control Technology">Instrumentation
                                        &amp; Process
                                        Control
                                        Technology
                                    </option>
                                    <option value="Mechanical Technology">Mechanical Technology</option>
                                    <option value="Mechatronics Technology">Mechatronics Technology</option>
                                    <option value="Power Technology">Power Technology</option>
                                    <option value="Refregeration &amp; Air Conditioning Technology">Refregeration &amp;
                                        Air
                                        Conditioning
                                        Technology
                                    </option>
                                    <option value="Telecommunication Technology">Telecommunication Technology</option>
                                    <option value="Electronics Technology">Electronics Technology</option>
                                    <option value="Library Science">Library Science</option>
                                    <option value="Survey">Survey</option>
                                    <option value="General Mechanics">General Mechanics</option>
                                    <option value="Firm Machinery">Firm Machinery</option>
                                    <option value="Textile Technology">Textile Technology</option>
                                    <option value="Food">Food</option>
                                    <option value="Glass and Ceramic">Glass and Ceramic</option>
                                    <option value="Agro-Based Food">Agro-Based Food</option>
                                    <option value="General Electronics">General Electronics</option>
                                    <option value="Automotive">Automotive</option>
                                    <option value="Building Maintenance">Building Maintenance</option>
                                    <option value="Wood Working">Wood Working</option>
                                    <option value="Ceramic">Ceramic</option>
                                    <option value="Civil Construction">Civil Construction</option>
                                    <option value="Computer and Information Technology">Computer and Information
                                        Technology
                                    </option>
                                    <option value="Civil Drafting with CAD">Civil Drafting with CAD</option>
                                    <option value="Mechanical Drafting with CAD">Mechanical Drafting with CAD</option>
                                    <option value="Dress Making">Dress Making</option>
                                    <option value="Dyeing, Printing and Finishing">Dyeing, Printing and Finishing
                                    </option>
                                    <option value="Electrical Maintenance Works">Electrical Maintenance Works</option>
                                    <option value="Farm Machinery">Farm Machinery</option>
                                    <option value="Fish Culture and Breeding">Fish Culture and Breeding</option>
                                    <option value="Food Processing and Preservation">Food Processing and Preservation
                                    </option>
                                    <option value="Livestock Rearing and Farming">Livestock Rearing and Farming</option>
                                    <option value="Machine Tools Operation">Machine Tools Operation</option>
                                    <option value="Poultry Rearing and Farming">Poultry Rearing and Farming</option>
                                    <option value="Patient Care">Patient Care</option>
                                    <option value="General Electrical Works">General Electrical Works</option>
                                    <option value="Plumbing and Pipe Fittings">Plumbing and Pipe Fittings</option>
                                    <option value="Refrigeration and Air Conditioning">Refrigeration and Air
                                        Conditioning</option>
                                    <option value="Glass">Glass</option>
                                    <option value="Flower, Fruit and Vegetable Cultivation">Flower, Fruit and Vegetable
                                        Cultivation
                                    </option>
                                    <option value="Weaving">Weaving</option>
                                    <option value="Welding and Fabrication">Welding and Fabrication</option>
                                    <option value="Architectural Drafting with CAD">Architectural Drafting with CAD
                                    </option>
                                    <option value="Knitting">Knitting</option>
                                    <option value="Shrimp Culture and Breeding">Shrimp Culture and Breeding</option>
                                    <option value="Others">Others</option>
                                </select>
                            </div>




                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Board </label>
                                    <select class="form-control" formControlName="hscBoard">
                                        <option value=""> Select </option>
                                        <option value="Dhaka">Dhaka</option>
                                        <option value="Cumilla">Cumilla</option>
                                        <option value="Rajshahi">Rajshahi</option>
                                        <option value="Jashore">Jashore</option>
                                        <option value="Chittagong">Chittagong</option>
                                        <option value="Barishal">Barishal</option>
                                        <option value="Sylhet">Sylhet</option>
                                        <option value="Dinajpur">Dinajpur</option>
                                        <option value="Madrasah">Madrasah</option>
                                        <option value="Mymensingh">Mymensingh</option>
                                        <option value="Pharmacy Council of Bangladesh">Pharmacy Council of Bangladesh
                                        </option>
                                        <option value="Cambridge International - IGCE">Cambridge International - IGCE
                                        </option>
                                        <option value="Edexcel International">Edexcel International</option>
                                        <option value="Bangladesh Technical Education Board (BTEB)">Bangladesh Technical
                                            Education
                                            Board
                                            (BTEB)</option>
                                        <option value="Open University">Open University</option>
                                        <option value="Others">Others</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Result </label>
                                    <select class="form-control" formControlName="hscResult">
                                        <option value=""> Select </option>
                                        <option value="1st Division">1st Division</option>
                                        <option value="2nd Division">2nd Division</option>
                                        <option value="Running">Running</option>
                                        <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                                        <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                                    </select>
                                    <!-- if select Gpa(out of 5) then open extra input field -->
                                    <div *ngIf="myForm.get('hscResult').value == 'GPA(Out of 5)'">
                                        <input type="number" class="form-control" formControlName="hscGpaOutOf5"
                                            placeholder="Type here your GPA">
                                    </div>
                                    <!-- if select Gpa(out of 4) then open extra input field -->
                                    <div *ngIf="myForm.get('hscResult').value == 'GPA(Out of 4)'">
                                        <input type="number" class="form-control" formControlName="hscGpaOutOf4"
                                            placeholder="Type here your GPA">
                                    </div>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Passing Year </label>
                                    <input type="text" class="form-control" formControlName="hscPassingYear">
                                </div>
                            </div>



                        </div>


                        <h3 class="card-title mt-2">Graduation / Bachelor / Equivalent</h3>
                        <hr />


                        <div class="row">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Examination</label>
                                    <select class="form-control" formControlName="bscTitle">
                                        <option value="" selected="selected">Select One</option>
                                        <option value="B.Sc Engineering">B.Sc Engineering</option>
                                        <option value="B.Sc in Agricultural Science">B.Sc in Agricultural Science
                                        </option>
                                        <option value="M.B.B.S./B.D.S">M.B.B.S./B.D.S</option>
                                        <option value="Honors">Honors</option>
                                        <option value="Pass Course">Pass Course</option>
                                        <option value="Fazil">Fazil</option>
                                        <option value="B.B.A">B.B.A</option>
                                        <option value="Graduation Equivalent">Graduation Equivalent</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Subject / Degree</label>
                                    <input type="text" class="form-control" formControlName="bscSubjectOrDegree"
                                        placeholder="Computer Science Engineering">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>University</label>
                                    <select class="form-control" formControlName="bscInstituteId">
                                        <option value="">Select Institute</option>
                                        <option *ngFor="let institute of instituteList" [value]="institute.id">
                                            {{institute.campusName}}</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Result </label>
                                    <select class="form-control" formControlName="bscResult">
                                        <option value=""> Select </option>
                                        <option value="1st Division">1st Division</option>
                                        <option value="2nd Division">2nd Division</option>
                                        <option value="Running">Running</option>
                                        <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                                        <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                                    </select>
                                    <!-- if select Gpa(out of 5) then open extra input field -->
                                    <div *ngIf="myForm.get('bscResult').value == 'GPA(Out of 5)'">
                                        <input type="number" class="form-control" formControlName="bscGpaOutOf5"
                                            placeholder="Type here your GPA">
                                    </div>
                                    <!-- if select Gpa(out of 4) then open extra input field -->
                                    <div
                                        *ngIf="myForm.get('bscResult').value == 'GPA(Out of 4)' || myForm.get('bscResult').value == 'Running'">
                                        <input type="number" class="form-control" formControlName="bscGpaOutOf4"
                                            placeholder="Type here your GPA">
                                    </div>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Passing Year </label>
                                    <input type="text" class="form-control" formControlName="bscPassingYear">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Course Duration </label>
                                    <select class="form-control" formControlName="bscCourseDuration">
                                        <option value=""> Select </option>
                                        <option value="4 Years">4 Years</option>
                                        <option value="5 Years">5 Years</option>
                                        <option value="3 Years">3 Years</option>
                                        <option value="2 Years">2 Years</option>
                                    </select>
                                </div>
                            </div>

                        </div>




                        <h3 class="card-title mt-2"> Masters or Equivalent</h3>
                        <hr />



                        <div class="row">
                            <!-- board -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Examination</label>
                                    <!-- input  select -->
                                    <select class="form-control" formControlName="mscTitle">
                                        <option value="" selected="selected">Select One</option>
                                        <option value="L.L.M">L.L.M</option>
                                        <option value="M.A">M.A</option>
                                        <option value="M.B.A">M.B.A</option>
                                        <option value="M.Com">M.Com</option>
                                        <option value="M.S.S">M.S.S</option>
                                        <option value="M.Sc">M.Sc</option>
                                        <option value="Masters Equivalent">Masters Equivalent</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Subject / Degree select -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Subject / Degree</label>
                                    <input type="text" class="form-control" formControlName="mscSubjectOrDegree"
                                        placeholder="Computer Science Engineering">
                                </div>
                            </div>

                            <!-- Institute -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>University</label>
                                    <!-- input  select loop instituteList -->
                                    <select class="form-control" formControlName="mscInstituteId">
                                        <option value="">Select Institute</option>
                                        <option *ngFor=" let institute of instituteList" [value]="institute.id">
                                            {{institute.campusName}}</option>
                                    </select>


                                </div>
                            </div>

                            <!-- result -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Result </label>
                                    <!-- input  select -->
                                    <select class="form-control" formControlName="mscResult">
                                        <option value=""> Select </option>
                                        <option value="1st Division">1st Division</option>
                                        <option value="2nd Division">2nd Division</option>
                                        <option value="Running">Running</option>
                                        <option value="GPA(Out of 4)">GPA(Out of 4)</option>
                                        <option value="GPA(Out of 5)">GPA(Out of 5)</option>
                                    </select>
                                    <!-- if select Gpa(out of 5) then open extra input field -->
                                    <div *ngIf="myForm.get('mscResult').value == 'GPA(Out of 5)'">
                                        <input type="number" class="form-control" formControlName="mscGpaOutOf5"
                                            placeholder="Type here your GPA">
                                    </div>
                                    <!-- if select Gpa(out of 4) then open extra input field -->
                                    <div
                                        *ngIf="myForm.get('mscResult').value == 'GPA(Out of 4)' || myForm.get('mscResult').value == 'Running'">
                                        <input type="number" class="form-control" formControlName="mscGpaOutOf4"
                                            placeholder="Type here your GPA">
                                    </div>

                                </div>
                            </div>

                            <!-- Passing year -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Passing Year </label>
                                    <input type="text" class="form-control" formControlName="mscPassingYear">

                                </div>
                            </div>

                            <!-- course duration select -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Course Duration </label>
                                    <!-- input  select -->
                                    <select class="form-control" formControlName="mscCourseDuration">
                                        <option value=""> Select </option>
                                        <option value="4 Years">4 Years</option>
                                        <option value="5 Years">5 Years</option>
                                        <option value="3 Years">3 Years</option>
                                        <option value="2 Years">2 Years</option>
                                    </select>
                                </div>
                            </div>


                        </div>


                        <h3 class="card-title mt-2">Extra Curricular Activities</h3>
                        <hr />

                        <div class="table-responsive col-md-12">
                            <form [formGroup]="extraCurrForm">
                                <table class="table table-striped custom-table">
                                    <thead>
                                        <th>SL</th>
                                        <th>Title</th>
                                        <th>Experience/Duration</th>
                                        <th>Description</th>
                                        <th>Award</th>
                                        <th>Action</th>

                                    </thead>
                                    <tbody formArrayName="Rows">
                                        <tr *ngFor="let itemrow of extraCurrActFromArr.controls; let i=index;let l=last"
                                            [formGroupName]="i">
                                            <td>{{i+1}}</td>

                                            <td>
                                                <input class="form-control" formControlName="extraCurrAct"
                                                    class="form-control" type="text">
                                            </td>

                                            <td>
                                                <input class="form-control" formControlName="extraCurrActExp"
                                                    class="form-control" type="text">
                                            </td>


                                            <td>

                                                <textarea class="form-control" formControlName="extraCurrActDesc"
                                                    rows="1"></textarea>


                                            </td>


                                            <td>
                                                <input class="form-control" formControlName="extraCurrActAward"
                                                    class="form-control" type="text">
                                            </td>


                                            <td>
                                                <button *ngIf="extraCurrForm.controls.Rows.controls.length > 0"
                                                    (click)="deleteDocRow(i)" class="btn btn-danger"><i
                                                        class="fa fa-trash-o"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <br>
                                <button type="button" (click)="addDocRow()" class="btn btn-info"><i
                                        class="fa fa-plus"></i></button>

                            </form>
                        </div>


                        <h3 class="card-title mt-4">Interview Marks</h3>
                        <hr />


                        <div class="table-responsive col-md-12">
                            <form [formGroup]="interviewForm">
                                <table class="table table-striped custom-table">
                                    <thead>
                                        <th>SL</th>
                                        <th>Jury Member ID</th>
                                        <th>Jury Member Name</th>
                                        <th>Written Marks</th>
                                        <th>Viva Marks</th>
                                        <th>Presentation Marks</th>
                                        <!-- <th>Total Marks</th> -->
                                        <th>Action</th>

                                    </thead>
                                    <tbody formArrayName="Rows">
                                        <tr *ngFor="let itemrow of interviewFormArr.controls; let i=index;let l=last"
                                            [formGroupName]="i">
                                            <td>{{i+1}}</td>

                                            <td>


                                                <input class="form-control" formControlName="juryMemberCode"
                                                    class="form-control" type="text" (blur)="getEmployeeInfo(i)">
                                            </td>



                                            <td>


                                                <input class="form-control" formControlName="juryMemberName"
                                                    class="form-control" type="text" readonly>
                                            </td>


                                            <td>
                                                <input class="form-control" formControlName="writtenMark"
                                                    class="form-control" type="number">
                                            </td>


                                            <td>
                                                <input class="form-control" formControlName="vivaMark"
                                                    class="form-control" type="number">

                                            </td>


                                            <td>
                                                <input class="form-control" formControlName="presentationMark"
                                                    class="form-control" type="number">
                                            </td>

                                            <!-- <td>
                                                <input class="form-control" formControlName="totalMark"
                                                    class="form-control" type="number">
                                            </td> -->


                                            <td>
                                                <button *ngIf="interviewForm.controls.Rows.controls.length > 0"
                                                    (click)="deleteDocRow2(i)" class="btn btn-danger"><i
                                                        class="fa fa-trash-o"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <br>
                                <button type="button" (click)="addDocRow2()" class="btn btn-info"><i
                                        class="fa fa-plus"></i></button>

                            </form>
                        </div>

                        <div class="row mt-4">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">University</label>
                                    <select class="form-control" formControlName="universityName">
                                        <option value="">Select Institute</option>
                                        <option *ngFor="let institute of instituteList" [value]="institute.id">
                                            {{institute.campusName}}</option>
                                    </select>
                                </div>
                            </div>



                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Institution EIIN </label>
                                    <input type="text" class="form-control" formControlName="institutionEIIN">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Campus Coordinator Name </label>
                                    <input type="text" class="form-control" formControlName="campusCoordinatorName">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Campus Coordinator Phone </label>
                                    <input type="text" class="form-control" formControlName="campusCoordinatorPhone">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Reference Name </label>
                                    <input type="text" class="form-control" formControlName="referenceName">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Reference Designation </label>
                                    <input type="text" class="form-control" formControlName="referenceDesignation">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Reference ID No</label>
                                    <input type="text" class="form-control" formControlName="referenceIdNo">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Reference Department</label>
                                    <input type="text" class="form-control" formControlName="referenceDepartment">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Reference Mobile No</label>
                                    <input type="text" class="form-control" formControlName="referencePhone">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Relation with Reference</label>
                                    <input type="text" class="form-control" formControlName="relation">

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Legal Entity</label>

                                    <select class="form-control" formControlName="legalEntity">
                                        <option value=""> Select </option>
                                        <option value="WDTIL">Walton Digi-Tech Industries Limited</option>
                                    </select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Department<span class="text-danger"> *</span></label>

                                    <select class="form-control" formControlName="department">
                                        <option value=""> Select </option>
                                        <option value="Marketing & Sales">Marketing & Sales</option>
                                    </select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Section<span class="text-danger"> *</span></label>
                                    <!-- input cselect-->
                                    <select class="form-control" formControlName="section">
                                        <option value=""> Select </option>
                                        <option value="Marketing">Marketing</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Sub Section<span class="text-danger"> *</span></label>
                                    <!-- input cselect-->
                                    <select class="form-control" formControlName="subSection">
                                        <option value=""> Select </option>
                                        <option value="Branding">Branding</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Team<span class="text-danger"> *</span></label>
                                    <!-- input cselect-->
                                    <select class="form-control" formControlName="team">
                                        <option value=""> Select </option>
                                        <option value="Campus Ambassador">Campus Ambassador</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Designation<span class="text-danger"> *</span></label>
                                    <select class="form-control" formControlName="officialDesignation">
                                        <option value=""> Select </option>
                                        <option value="Campus Ambassador">Campus Ambassador</option>
                                    </select>
                                </div>
                            </div>



                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Duration </label>
                                    <input type="text" class="form-control" formControlName="duration">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Scholarship </label>
                                    <input type="number" class="form-control" formControlName="scholarShip">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Joining Date </label>

                                    <div class="cal-icon">
                                        <input id="joiningDateId" class="form-control datetimepicker" bsDatepicker
                                            type="text" placeholder="DD-MM-YYYY"
                                            [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                                            formControlName="joiningDate">
                                    </div>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>HR Comments </label>
                                    <textarea class="form-control" formControlName="hrComment" rows="1"></textarea>

                                </div>
                            </div>


                        </div>

                        <div class="text-right">

                            <button type="button" class="btn btn-secondary btn-ripple" (click)="resetFormValues()">
                                <i class="fa fa-undo" aria-hidden="true"></i> Reset
                            </button>
                            &nbsp; &nbsp;
                            <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!myForm.valid">
                                <i class="fa fa-check" aria-hidden="true"></i> Submit &nbsp;&nbsp;&nbsp;
                            </button>
                        </div>


                    </form>
                </div>
            </div>
        </div>
    </div>


    <!-- Image upload modal -->

    <div id="profilePic_Image" class="modal custom-modal fade" role="dialog">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Upload Profile Image</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form [formGroup]="uploadForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="profile-img-wrap edit-img">
                                    <img class="inline-block" [src]="imageSrc || imageSrc2" />

                                    <div class="fileupload btn">
                                        <span class="btn-text">Change</span>
                                        <input class="upload" type="file" name="profilePic"
                                            accept="image/x-png,image/jpeg,image/jpg" (change)="onFileSelect($event)" />
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- /Image upload modal -->


</div>
<!-- /Page Content -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>