import { Validators } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';
import { CaService } from '../../service/ca.service';

declare const $: any;
@Component({
  selector: 'app-ca-setup',
  templateUrl: './ca-setup.component.html',
  styleUrls: ['./ca-setup.component.css']
})
export class CaSetupComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public instituteForm: FormGroup;


  public pipe = new DatePipe('en-US');
  public myFromGroup: FormGroup;

  public configPgn: any;
  public listData: any = [];
  public editId: any;
  public tempId: any;

  // search fields for
  private campusCode: string;

  constructor(private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private caService: CaService,
    private loginService: LoginService) {
    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [3, 5, 10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };
  }

  ngOnInit(): void {

    this.instituteForm = this.formBuilder.group({
      id: [''],
      campusCode: ['', [Validators.required]],
      campusName: ['', [Validators.required]],
      campusAddress: [''],
      campusPhone: [""],
      campusFax: [''],
      campusEmail: ["", [Validators.pattern("^[&a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-zA-Z]{2,4}$")]],
      campusWebsite: [''],
      campusLogo: [''],
      campusLatitude: [''],
      campusLongitude: [''],
      campusResPerson: [''],
      campusResPersonPhone: ["", [Validators.pattern("[0-9 ]{11}")]],
    });




    // set init params
    this.myFromGroup = new FormGroup({
      pageSize: new FormControl()
    });
    this.myFromGroup.get('pageSize').setValue(this.configPgn.pageSize);

    // bind event & action
    this._bindFromFloatingLabel();
    //  this.pollData();
    this._getListData();

  }



  fillFormInstitute(id: any) {
    this.editId = id;
    const apiURL = this.baseUrl + '/ca/campus/get/' + id;

    let queryParams: any = {};
    queryParams.rEntityName = 'Campus';
    queryParams.rReqType = 'getById';
    queryParams.rId = this.editId;

    this.spinnerService.show();
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (response.status === true) {
          this.instituteForm.patchValue(response.data);
          this.spinnerService.hide();
        } else {
          this.spinnerService.hide();
          this.toastr.info(response.message, 'Info');
        }

      },
      (error) => {
        console.log(error)
      }
    );
  }

  _bindFromFloatingLabel() {

    const self = this;
    // for floating label
    if ($('.floating').length > 0) {
      $('.floating')
        .on('focus blur', function (e) {
          $(this)
            .parents('.form-focus')
            .toggleClass('focused', e.type === 'focus' || this.value.length > 0);
        })
        .trigger('blur');
    }

    // tslint:disable-next-line:only-arrow-functions
    $('.filter-row').find('input, select, textarea').keyup(function (e) {

      console.log(e.keyCode)
      if (e.keyCode === 13) {
        self._getSearchData();
      }

    });



  }

  public _getSearchData() {
    this._getListData();

  }

  _getListData() {
    const apiURL = this.baseUrl + '/ca/campus/getLists';

    let queryParams: any = {};
    // this.creationUser = this.loginService.getUser().username;
    const params = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);
    queryParams = params;

    queryParams.rEntityName = 'Campus';
    queryParams.rReqType = 'getListData';


    this.spinnerService.show();
    this.caService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (response.status === true) {
          this.listData = response.data;
          this.configPgn.totalItem = response.totalItems;
          this.configPgn.totalItems = response.totalItems;
          this.setDisplayLastSequence();
          this.spinnerService.hide();
        } else {
          this.spinnerService.hide();
          this.toastr.info(response.message, 'Info');
        }

      },
      (error) => {
        console.log(error)
      }
    );

  }


  private getUserQueryParams(page: number, pageSize: number): any {

    const params: any = {};

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    // push other attributes
    if (this.campusCode) {
      params[`campusCode`] = this.campusCode;
    }
    return params;

  }

  searchByCode(event) {

  }
  searchBySearchButton() {

  }

  //reset institute form
  resetInstituteForm() {
    this.editId = null;
    this.instituteForm.reset();
  }




  deleteEntityData(id: any) {
    const apiURL = this.baseUrl + '/ca/campus/delete/' + id;
    console.log(apiURL);

    const formData: any = {};
    formData.rEntityName = 'Campus';
    formData.rActiveOperation = 'delete';

    this.spinnerService.show();
    this.caService.sendDeleteRequest(apiURL, formData).subscribe((response: any) => {
      if (response.status === true) {
        this.spinnerService.hide();
        this.toastr.success(response.message, 'Success');
        this._getListData();
        //close modal
        $('#delete_entity').modal('hide');
      } else if (response.status === false) {
        this.spinnerService.hide();
        this.toastr.error(response.message, 'Error');
      }
    }, (error) => {
      $('#delete_entity').modal('hide');
      console.log(error)
    });

  }

  submitInstitute() {
    //if form is invalid, return
    if (this.instituteForm.invalid) {
      return;
    }
    if (this.editId) {
      this._updateInstitute();
    } else {
      this._saveInstitute();
    }

  }

  // save institute
  _saveInstitute() {
    const apiURL = this.baseUrl + '/ca/campus/create';
    let formData: any;
    formData = this.instituteForm.value;

    this.spinnerService.show();
    this.caService.sendPostRequest(apiURL, formData).subscribe((response: any) => {
      if (response.status === true) {
        this.spinnerService.hide();
        this.toastr.success(response.message, 'Success');
        this.instituteForm.reset();
        //close Modal
        $('#add_institute_modal').modal('hide');
        this._getListData();
      } else if (response.status === false) {
        this.spinnerService.hide();
        this.toastr.error(response.message, 'Error');
        $('#add_institute_modal').modal('hide');
        //rest form
        this.instituteForm.reset();
      }
    }, (error) => {
      this.spinnerService.hide();
      console.log(error);
      $('#add_institute_modal').modal('hide');
      this.instituteForm.reset();
    });


    $('#add_institute_modal').modal('hide');
  }

  // update institute
  public _updateInstitute() {
    const apiURL = this.baseUrl + '/ca/campus/update';
    let formData: any;
    formData = this.instituteForm.value;

    console.log("@Updating........");
    console.log(formData);

    this.spinnerService.show();
    this.caService.sendPutRequest(apiURL, formData).subscribe((response: any) => {
      if (response.status === true) {
        this.spinnerService.hide();
        this.toastr.success(response.message, 'Success');
        this.instituteForm.reset();
        //close Modal
        $('#add_institute_modal').modal('hide');
        this._getListData();
      } else if (response.status === false) {
        this.spinnerService.hide();
        this.toastr.error(response.message, 'Error');
        $('#add_institute_modal').modal('hide');
        //rest form
        this.instituteForm.reset();
      }
    }, (error) => {
      this.spinnerService.hide();
      console.log(error);
      $('#add_institute_modal').modal('hide');
      this.instituteForm.reset();
    }
    );

  }

  // pagination handling methods start -----------------------------------------------------------------------
  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {
    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this._getListData();
  }
  handlePageSizeChange(event: any): void {
    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this._getListData();
  }
  // pagination handling methods end -------------------------------------------------------------------------


}
