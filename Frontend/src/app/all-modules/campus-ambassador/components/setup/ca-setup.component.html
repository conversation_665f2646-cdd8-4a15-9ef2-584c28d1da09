<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Campus Ambassador</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Campus</b></span></li>
        </ul>
      </div>
    </div>
  </div>
  <!-- /Page Header -->

  <div class="row">
    <div class="col-md-12">
      <div class="card customCard">

        <div class="card-header">
          <div class="card-tools">
            <a (click)="resetInstituteForm() " class="btn btn-outline-primary" data-toggle="modal"
              data-target="#add_institute_modal"><i class="fa fa-plus"></i> Add New
              &nbsp;&nbsp;&nbsp;</a>
          </div>
        </div>

        <div class="card-body">

          <div class="table-responsive">

            <div class="d-flex justify-content-start pb-1">
              <div class="pgn-displayDataInfo">
                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (1) )
                  }} to {{configPgn.pngDiplayLastSeq}} of {{configPgn.totalItem}} ) entries</span>
              </div>
            </div>

            <table id="genListTable" class="table table-striped custom-table">
              <thead>
                <tr>
                  <th>SL</th>
                  <th class="d-none">TB_ROW_ID</th>
                  <th>Campus Code</th>
                  <th>Campus Name </th>
                  <!-- <th>Campus Address</th>
                  <th>Campus Phone </th> -->
                  <th>Campus Website</th>
                  <th>Campus Res. Person</th>
                  <th>Campus Res. Phone</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let thisObj of listData | paginate : configPgn; let i = index"
                  [class.active]="i == currentIndex">

                  <td>{{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) ) }}</td>
                  <td class="d-none">{{thisObj.id}}</td>
                  <td>{{thisObj.campusCode}}</td>
                  <td>{{thisObj.campusName}}</td>
                  <!-- <td>{{thisObj.campusAddress}}</td>
                  <td>{{thisObj.campusPhone}}</td> -->
                  <td>
                    {{thisObj.campusWebsite}}
                  </td>
                  <td>{{thisObj.campusResPerson}}</td>
                  <td>{{thisObj.campusResPersonPhone}}</td>

                  <td>
                    <a class="btn btn-sm btn-info" data-toggle="modal" data-target="#add_institute_modal"
                      (click)="fillFormInstitute(thisObj.id)"><i class="fa fa-pencil m-r-5"></i></a>&nbsp;&nbsp;
                    <!-- <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#delete_entity"
                      (click)="tempId = thisObj.id">
                      <i class="fa fa-trash-o m-r-5"></i>
                    </a> &nbsp; -->
                  </td>
                </tr>

                <tr *ngIf="listData.length === 0">
                  <td colspan="10">
                    <h5 style="text-align: center;">No data found</h5>
                  </td>
                </tr>
              </tbody>
            </table>


            <div class="d-flex justify-content-end ">

              <div class="" [formGroup]="myFromGroup">
                Items per Page
                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption" formControlName="pageSize">
                  <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                    {{ size }}
                  </option>
                </select>
              </div>

              <div class="pgn-pageSliceCt">
                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                  (pageChange)="handlePageChange($event)">
                </pagination-controls>
              </div>

            </div>

          </div>

        </div>
      </div>

    </div>
  </div>



</div>

<!-- Add institute Modal -->
<div id="add_institute_modal" class="modal custom-modal fade" role="dialog">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" *ngIf="!editId">Add New Campus </h5>
        <h5 class="modal-title" *ngIf="editId">Update Campus </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form (ngSubmit)="submitInstitute()" [formGroup]="instituteForm">
          <div class="row">

            <div class="col-md-6" *ngIf="editId" hidden>
              <div class="form-group">
                <label>Id</label>
                <input type="text" readonly formControlName="id" class="form-control">
              </div>
            </div>


            <div class="col-md-6">
              <div class="form-group">
                <label class="val-required">Campus Code</label>
                <input type="text" formControlName="campusCode" class="form-control" placeholder="Ex: BUET, AIUB , SEU">
                <!-- span is error -->
                <span
                  *ngIf="instituteForm.get('campusCode').touched && instituteForm.get('campusCode').hasError('required')"
                  class="text-danger">Campus Code is required</span>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label class="val-required">Name</label>
                <input class="form-control" formControlName="campusName" type="text"
                  placeholder="Institution Full Name">
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label>Phone</label>
                <input class="form-control" formControlName="campusPhone" type="text">

              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label>Email</label>
                <input class="form-control" formControlName="campusEmail" type="email">
                <span
                  *ngIf="instituteForm.get('campusEmail').invalid && instituteForm.get('campusEmail').errors.pattern"
                  class="text-danger">Invalid Email
                </span>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label>Website</label>
                <input class="form-control" formControlName="campusWebsite" type="text">
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label>Responsible Person</label>
                <input class="form-control" formControlName="campusResPerson" type="text">
              </div>
            </div>


            <div class="col-md-6">
              <div class="form-group">
                <label>Responsible Person Phone</label>
                <input class="form-control" formControlName="campusResPersonPhone" type="text">
                <span
                  *ngIf="instituteForm.get('campusResPersonPhone').invalid && instituteForm.get('campusResPersonPhone').errors.pattern"
                  class="text-danger">Invalid Number
                </span>
              </div>
            </div>



            <div class="col-md-6">
              <div class="form-group">
                <label>Campus Address</label>
                <!-- text area -->
                <textarea class="form-control" formControlName="campusAddress" rows="1"></textarea>
              </div>
            </div>

          </div>
          <div class="submit-section">
            <button *ngIf="!editId" class="btn btn-primary submit-btn" [disabled]="!instituteForm.valid">Submit</button>
            <button *ngIf="editId" class="btn btn-primary submit-btn" [disabled]="!instituteForm.valid">Update</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- /Add Institue Info Modal -->



<!-- Delete Modal -->
<div class="modal custom-modal fade" id="delete_entity" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="form-header">
          <h3>Delete Item</h3>
          <p>Are you sure want to delete?</p>
        </div>
        <div class="modal-btn delete-action">
          <div class="row">
            <div class="col-6">
              <a class="btn btn-primary continue-btn" (click)="deleteEntityData(tempId)">Delete</a>
            </div>
            <div class="col-6">
              <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /Delete Modal -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>