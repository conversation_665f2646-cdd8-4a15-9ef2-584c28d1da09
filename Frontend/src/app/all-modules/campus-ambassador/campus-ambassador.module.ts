import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CampusAmbassadorRoutingModule } from './campus-ambassador-routing.module';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { NgSelectModule } from '@ng-select/ng-select';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxPrintModule } from 'ngx-print';
import { NgxSpinnerModule } from 'ngx-spinner';
import { PickListModule } from 'primeng/picklist';
import { SharingModule } from 'src/app/sharing/sharing.module';
import { CampusAmbassadorComponent } from './campus-ambassador.component';
import { CaCandidateCreateComponent } from './components/candidate/create/ca-candidate-create.component';
import { CaCandidateListComponent } from './components/candidate/list/ca-candidate-list.component';
import { CaSetupComponent } from './components/setup/ca-setup.component';
import { CaProfileComponent } from './components/candidate/profile/ca-profile.component';
import { CaAdminDashboardComponent } from './components/dashboard/admin/ca-admin-dashboard.component';
import { CaCandidateEditComponent } from './components/candidate/edit/ca-candidate-edit.component';
import { ScholarshipXlsComponent } from './components/scholarship-xls/scholarship-xls.component';
import { CaTopsheetCreateComponent } from './components/topsheet/create/ca-topsheet-create.component';
import { CaTopSheetEditComponent } from './components/topsheet/edit/ca-top-sheet-edit.component';
import { CaTopSheetListComponent } from './components/topsheet/list/ca-top-sheet-list.component';
import { CaTopSheetViewComponent } from './components/topsheet/view/ca-top-sheet-view.component';
import { CaIdCardComponent } from './components/candidate/id-card/ca-id-card.component';
import { AmbassadorResignComponent } from './components/ambassador-resign/list/ambassador-resign.component';
import { AmbassadorResignCreateComponent } from './components/ambassador-resign/create/ambassador-resign-create.component';




@NgModule({
  declarations: [CampusAmbassadorComponent, CaCandidateCreateComponent, CaCandidateListComponent, CaSetupComponent, CaProfileComponent, CaAdminDashboardComponent, CaCandidateEditComponent, ScholarshipXlsComponent, CaTopsheetCreateComponent, CaTopSheetEditComponent, CaTopSheetListComponent, CaTopSheetViewComponent, CaIdCardComponent, AmbassadorResignComponent, AmbassadorResignCreateComponent],
  imports: [
    CommonModule,
    CampusAmbassadorRoutingModule,
    HttpClientModule,
    FormsModule,
    SharingModule,
    CKEditorModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgxSpinnerModule,
    NgSelectModule,
    PickListModule,
    NgxPrintModule,
    BsDatepickerModule.forRoot(),
  ]
})
export class CampusAmbassadorModule { }
