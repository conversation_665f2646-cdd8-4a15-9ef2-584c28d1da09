import { CaCandidateListComponent } from './components/candidate/list/ca-candidate-list.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CampusAmbassadorComponent } from './campus-ambassador.component';
import { CaCandidateCreateComponent } from './components/candidate/create/ca-candidate-create.component';
import { CaSetupComponent } from './components/setup/ca-setup.component';
import { CaProfileComponent } from './components/candidate/profile/ca-profile.component';
import { CaAdminDashboardComponent } from './components/dashboard/admin/ca-admin-dashboard.component';
import { CaCandidateEditComponent } from './components/candidate/edit/ca-candidate-edit.component';
import { ScholarshipXlsComponent } from './components/scholarship-xls/scholarship-xls.component';
import { CaTopSheetListComponent } from './components/topsheet/list/ca-top-sheet-list.component';
import { CaTopSheetViewComponent } from './components/topsheet/view/ca-top-sheet-view.component';
import { CaTopSheetEditComponent } from './components/topsheet/edit/ca-top-sheet-edit.component';
import { CaTopsheetCreateComponent } from './components/topsheet/create/ca-topsheet-create.component';
import { CaIdCardComponent } from './components/candidate/id-card/ca-id-card.component';
import { AmbassadorResignComponent } from './components/ambassador-resign/list/ambassador-resign.component';
import { AmbassadorResignCreateComponent } from './components/ambassador-resign/create/ambassador-resign-create.component';

const routes: Routes = [
  {
    path: '',
    component: CampusAmbassadorComponent,
    children: [
      {
        path: 'candidate/create',
        component: CaCandidateCreateComponent,
        data: { title: 'CA - Create Ambassador' }
      },
      {
        path: 'candidate/list',
        component: CaCandidateListComponent,
        data: { title: 'CA - Ambassador List' }
      },
      {
        path: 'candidate/profiles/:id',
        component: CaProfileComponent
        ,
        data: { title: 'CA - Ambassador Profile' }
      },
      {
        path: 'candidate/edit/:id',
        component: CaCandidateEditComponent,
        data: { title: 'CA - Edit Ambassador' }
      },
      {
        path: 'candidate/id-card/:id',
        component: CaIdCardComponent
        ,
        data: { title: 'CA - Ambassador ID Card' }
      },
      {
        path: 'setup/create',
        component: CaSetupComponent,
        data: { title: 'CA - Add Campus' }
      },
      {
        path: 'dashboard/admin',
        component: CaAdminDashboardComponent,
        data: { title: 'CA - Admin Dashboard' }
      },
      {
        path: 'candidate/scholarship-xls',
        component: ScholarshipXlsComponent,
        data: { title: 'CA - Scholarship Upload' }
      },

      {
        path: 'candidate/top-sheet',
        component: CaTopSheetListComponent
        ,
        data: { title: 'CA - Top Sheet' }
      }
      ,

      {
        path: 'candidate/top-sheet/create',
        component: CaTopsheetCreateComponent
        ,
        data: { title: 'CA - Create Top Sheet' }
      }
      ,

      {
        path: 'candidate/top-sheet/edit/:id',
        component: CaTopSheetEditComponent,
        data: { title: 'CA - Edit Top Sheet' }
      }
      ,

      {
        path: 'candidate/top-sheet/view/:id',
        component: CaTopSheetViewComponent,
        data: { title: 'CA - View Top Sheet' }
      },
      {
        path: 'candidate/resign',
        component: AmbassadorResignComponent
        ,
        data: { title: 'CA - Resign' }
      }
      ,
      {
        path: 'candidate/resign/create',
        component: AmbassadorResignCreateComponent
        ,
        data: { title: 'CA - Create Resign' }
      }
      ,
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CampusAmbassadorRoutingModule { }
