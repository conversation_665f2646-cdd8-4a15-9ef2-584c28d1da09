import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgxSpinnerService } from "ngx-spinner";
import { environment } from "src/environments/environment";
import { ReportBetweenDateDTO } from "../model/ReportBetweenDateDTO";
import { AttendanceReportService } from "../services/attendance-report.service";
import { ToastrService } from "ngx-toastr";
import { LoginService } from "src/app/login/services/login.service";
import { HrCrEmp } from "../../employees/model/HrCrEmp";
import { HrTlAttnDaily } from "../../employees/model/HrTlAttnDaily";
import { EmployeeService } from "../../employees/services/employee.service";
import { ShortLeaveService } from "../../self-service/service/short-leave.service";
import { CommonService } from "../../settings/common/services/common.service";
import { ReportsService } from "../../reports/service/reports.service";
import { DomSanitizer } from "@angular/platform-browser";



declare const $: any;

@Component({
  selector: 'app-attendance-waiver-onoffstatus',
  templateUrl: './attendance-waiver-onoffstatus.component.html',
  styleUrls: ['./attendance-waiver-onoffstatus.component.css']
})
export class AttendanceWaiverOnoffstatusComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public reportBetweenDateForm: FormGroup;

  contextPth = environment.contextPath;

  public pipe = new DatePipe("en-US");
  public configPgn: any;
  public listData: any = [];
  user!: HrCrEmp;
  tempId: any;

  myForm: FormGroup;
  public responseData: any = [];
  public dataLocalUrl: any;


  constructor(
    private formBuilder: FormBuilder,
    private login: LoginService,
    private router: Router,
    private toastr: ToastrService,
    private spinnerService: NgxSpinnerService,
    private shortLeaveService: ShortLeaveService,
    private empService: EmployeeService,
    private commonService: CommonService,
    private _reportService: ReportsService,
    private domSanitizer: DomSanitizer,
  ) {
    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };
  }

  ngOnInit(): void {

    this.myForm = this.formBuilder.group({
      startDate: [""],
      endDate: [""],
      refCode: [""],
      empCodes: [""],
      oneHourFlex: ["0"],
      remainDays: [""]
    });

    //this.getAllList();

  }


  searchByButton() {
    this.handlePageChange(1);
  }

  resetform() {
    this.myForm.reset();
    this.handlePageChange(1);
  }



  // --------------- Get All List --------------



  renderJasperReport(format: any) {


    // data processing
    let params: any = {};

    if (
      this.myForm.get("empCodes").value
    ) {
      params.empCode = this.myForm.get("empCodes").value;
    } else {
      params.empCode = null;
    }

    if (this.myForm.get("startDate").value) {
      params["startDate"] = this.commonService.format_Date_Y_M_D(this.myForm.get("startDate").value);
    } else {
      params["startDate"] = null;
    }
    if (this.myForm.get("endDate").value) {
      params["endDate"] = this.commonService.format_Date_Y_M_D(this.myForm.get("endDate").value);
    } else {
      params["endDate"] = null;
    }

    if (this.myForm.get("remainDays").value) {
      params["remainDays"] = this.myForm.get("remainDays").value;
    } else {
      params["remainDays"] = 0;
    }

    if (this.myForm.get("oneHourFlex").value) {
      params["oneHourFlex"] = this.myForm.get("oneHourFlex").value;
    } else {
      params["oneHourFlex"] = null;
    }

    params["reportFormat"] = format;

    params["rptFileName"] = 'Attendance_With_Waiver_On_Off_Status';
    params["outputFileName"] = 'Attendance_With_Waiver_On_Off_Status';

    let httpOptions: any = {};


    if (format == 'pdf') {
      httpOptions = {
        responseType: 'arraybuffer' as 'json'
      };
    }
    else {
      httpOptions = {
        observe: 'response',
        responseType: 'blob'
      };
    }

    this.loadReport(params, httpOptions, format);

  }


  loadReport(params: any, httpOptions: any, format: any) {

    this.spinnerService.show();

    if (format == 'pdf') {

      this._reportService.waiverPdf(params, httpOptions).subscribe((response: any) => {
        const file = new Blob([response], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        this.dataLocalUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(fileURL);
        // this.urlSafe= this.domSanitizer.bypassSecurityTrustResourceUrl('https://angular.io/api/router/RouterLink');

        $('body').css('height', '100%');
        $('.main-wrapper').css('height', '100%');
        $('.main-wrapper .page-wrapper').css('height', '100%');
        this.spinnerService.hide();

      }, (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      })

    } else {

      this._reportService.waiverPdf(params, httpOptions)
        .subscribe((response: any) => { // explicitly typing response as any
          this.spinnerService.hide();
          let fileName = params["rptFileName"];
          let blob: Blob = response.body as Blob;
          let a = document.createElement('a');
          a.download = fileName + "." + "xlsx";
          a.href = window.URL.createObjectURL(blob);
          a.click();
          console.log(fileName);
        });

    }


  }


  // ------------------ Query Param ---------------------------

  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    if (
      this.myForm.get("refCode").value
    ) {
      params.refCode = this.myForm.get("refCode").value;
    }

    if (
      this.myForm.get("empCodes").value
    ) {
      params.empCodes = this.myForm.get("empCodes").value;
    }

    if (this.myForm.get("startDate").value) {
      params["startDate"] = this.commonService.format_Date_Y_M_D(this.myForm.get("startDate").value);
    }
    if (this.myForm.get("endDate").value) {
      params["endDate"] = this.commonService.format_Date_Y_M_D(this.myForm.get("endDate").value);
    }
    return params;
  }


  // ------------------------------------------- pagination handling methods start ----------------------------
  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {

    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;

  }
  handlePageSizeChange(event: any): void {

    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;

  }
  //--------------------------------------- pagination handling methods end ----------------------------------

}
