import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { DomSanitizer } from "@angular/platform-browser";
import { Router } from "@angular/router";
import { NgxSpinnerService } from "ngx-spinner";
import { environment } from "src/environments/environment";
import { ReportsService } from "../../reports/service/reports.service";
import { ReportBetweenDateDTO } from "../model/ReportBetweenDateDTO";
import { AttendanceReportService } from "../services/attendance-report.service";
import { Excelreportservice } from "../services/excel-service.service";
import { ToastrService } from "ngx-toastr";
import { CommonService } from "../../settings/common/services/common.service";

declare const $: any;
@Component({
  selector: "app-attendancereports-list",
  templateUrl: "./attendancereports-list.component.html",
  styleUrls: ["./attendancereports-list.component.css"],
})
export class AttendancereportsHrAdminListComponent implements OnInit {
  public reportBetweenDateForm: FormGroup;
  public baseUrl = environment.baseUrl;
  contextPth = environment.contextPath;
  dataLocalUrl: any;

  public myGroup: FormGroup;

  //pagination config
  pageNum = 1;
  pageSize = 10;
  pageSizes = [10, 25, 50, 100, 200, 500, 1000];
  totalItem = 50;
  pngDiplayLastSeq = 10;
  pngConfig: any;
  tempId: any;

  public departments2 = [];
  public sections2 = [];
  public subSections2 = [];
  public teams2 = [];
  public subTeams2 = [];
  public products2 = [];
  public operating_units2 = [];
  public locations = [];
  public searchByOrg = false;



  constructor(
    private formBuilder: FormBuilder,
    private attnReport: AttendanceReportService,
    private datePipe: DatePipe,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private reportsService: ReportsService,
    private domSanitizer: DomSanitizer,
    private excelsrvc: Excelreportservice,
    private toStr: ToastrService,
    private commonService: CommonService
  ) {

  }

  AttnReport = [];
  startdate;
  todate;
  attntype;


  ngOnInit() {


    this.loadAllLocations();
    this.loadAllOperatingUnits();
    this.initializeForm();
    this.loadAttnDada(); //lode defort data for this month


  }


  byOrganization() {
    this.searchByOrg = !this.searchByOrg;
    console.log(this.searchByOrg);
  }


  initializeForm() {

    this.reportBetweenDateForm = this.formBuilder.group({
      fromDate: ["", [Validators.required]],
      toDate: ["", [Validators.required]],
      empCode: ["", [Validators.required]],
      attntype: [""],
      operatingUnit: [""],
      product: [""],
      department: [""],
      Section: [""],
      subSection: [""],
      team: [""],
      subTeam: [""],
      designationId: [""],
      catId: [""],
      statusId: [""],
      location: [""],

    });

  }

  loadAllLocations() {

    let orgType = "JOB_LOCATION";
    let apiURL = this.baseUrl + "/alkp/search/" + orgType;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        this.locations = response.subALKP;

      },
      (error) => {
        console.log(error)

      }
    );
  }

  loadAllOperatingUnits() {
    let orgType = "OPERATING_UNIT";
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType;
    let queryParams: any = {};


    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.operating_units2 = response;

      },
      (error) => {
        console.log(error)
      }
    );
  }


  formSubmit() {

    const params = this.getUserQueryParams(this.pageNum, this.pageSize);
    let queryParams: any = {};
    queryParams = params;
    let apiURL = this.baseUrl + "/attnReport/reportInfobetweenDate";



    if (this.reportBetweenDateForm.get("operatingUnit").value) {
      queryParams[`operatingUnit`] = this.reportBetweenDateForm.get("operatingUnit").value;
    }

    if (this.reportBetweenDateForm.get("product").value) {
      queryParams[`product`] = this.reportBetweenDateForm.get("product").value;
    }

    if (this.reportBetweenDateForm.get("department").value) {
      queryParams[`department`] = this.reportBetweenDateForm.get("department").value;
    }

    if (this.reportBetweenDateForm.get("Section").value) {
      queryParams[`section`] = this.reportBetweenDateForm.get("Section").value;
    }

    if (this.reportBetweenDateForm.get("subSection").value) {
      queryParams[`subSection`] = this.reportBetweenDateForm.get("subSection").value;
    }

    if (this.reportBetweenDateForm.get("team").value) {
      queryParams[`team`] = this.reportBetweenDateForm.get("team").value;
    }

    if (this.reportBetweenDateForm.get("location").value) {
      queryParams[`location`] = this.reportBetweenDateForm.get("location").value;
    }


    this.spinnerService.show();

    this.attnReport.getReportBetweenDateAllEmp2(apiURL, queryParams).subscribe((data: any) => {
      this.AttnReport = data.objectList;
      this.totalItem = data.totalItems;
      this.spinnerService.hide();

    }, (error) => {

      this.toStr.warning(error?.error?.message || "Something went wrong. Please try again.");
      this.spinnerService.hide();

    });



  }

  clearFilter() {
    this.ngOnInit();
  }


  loadAttnDada() {

    let queryParams: any = {};


    // var today  = new Date();
    // console.log(today.toLocaleDateString("en-US")); // 9/17/2016


    let apiURL = this.baseUrl + "/attnReport/reportInfobetweenDate";

    const params = this.getUserQueryParams(this.pageNum, this.pageSize);
    queryParams = params;

    this.spinnerService.show();
    this.attnReport.getReportBetweenDateAllEmp2(apiURL, queryParams).subscribe((data: any) => {
      this.AttnReport = data.objectList;
      this.totalItem = data.totalItems;
      this.setDisplayLastSequence();
      this.spinnerService.hide();
    }, (error) => {
      console.log(error)
    });
  }


  searchByOperatingUnit(val) {


    let orgType = "PRODUCT";
    let operatingUnitIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + operatingUnitIdForAPI;
    let queryParams: any = {};

    this.products2 = [];
    this.departments2 = [];
    this.sections2 = [];
    this.subSections2 = [];
    this.teams2 = [];
    this.subTeams2 = [];

    this.reportBetweenDateForm.controls["product"].setValue("");
    this.reportBetweenDateForm.controls["department"].setValue("");
    this.reportBetweenDateForm.controls["Section"].setValue("");
    this.reportBetweenDateForm.controls["subSection"].setValue("");
    this.reportBetweenDateForm.controls["team"].setValue("");
    this.reportBetweenDateForm.controls["subTeam"].setValue("");




    if (operatingUnitIdForAPI.length > 0) {
      this.spinnerService.show();
      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          this.products2 = response;
          this.spinnerService.hide();
        },
        (error) => {
          console.log(error)
        }
      );
    }


  }

  searchByProduct(val) {

    let orgType = "DEPARTMENT";
    let productIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + productIdForAPI;
    let queryParams: any = {};

    this.departments2 = [];
    this.sections2 = [];
    this.subSections2 = [];
    this.teams2 = [];
    this.subTeams2 = [];

    this.reportBetweenDateForm.controls["department"].setValue("");
    this.reportBetweenDateForm.controls["Section"].setValue("");
    this.reportBetweenDateForm.controls["subSection"].setValue("");
    this.reportBetweenDateForm.controls["team"].setValue("");
    this.reportBetweenDateForm.controls["subTeam"].setValue("");


    if (productIdForAPI.length > 0) {
      this.spinnerService.show();
      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          this.departments2 = response;
          this.spinnerService.hide();
        },
        (error) => {
          console.log(error)
        }
      );
    }




  }

  searchByDepartment(val) {

    let orgType = "SECTION";
    let depIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + depIdForAPI;
    let queryParams: any = {};

    this.sections2 = [];
    this.subSections2 = [];
    this.teams2 = [];
    this.subTeams2 = [];

    this.reportBetweenDateForm.controls["Section"].setValue("");
    this.reportBetweenDateForm.controls["subSection"].setValue("");
    this.reportBetweenDateForm.controls["team"].setValue("");
    this.reportBetweenDateForm.controls["subTeam"].setValue("");

    if (depIdForAPI.length > 0) {
      this.spinnerService.show();
      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          this.sections2 = response;
          this.spinnerService.hide();
        },
        (error) => {
          console.log(error)
        }
      );
    }

  }



  searchBySection(val) {

    let orgType = "SUB_SECTION";
    let secIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + secIdForAPI;
    let queryParams: any = {};

    this.subSections2 = [];
    this.teams2 = [];
    this.subTeams2 = [];

    this.reportBetweenDateForm.controls["subSection"].setValue("");
    this.reportBetweenDateForm.controls["team"].setValue("");
    this.reportBetweenDateForm.controls["subTeam"].setValue("");



    if (secIdForAPI.length > 0) {
      this.spinnerService.show();
      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          this.subSections2 = response;
          this.spinnerService.hide();
        },
        (error) => {
          console.log(error)
        }
      );
    }

  }



  searchBySubSection(val) {
    let orgType = "TEAM";
    let subSecIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + subSecIdForAPI;
    let queryParams: any = {};

    this.teams2 = [];
    this.subTeams2 = [];
    this.reportBetweenDateForm.controls["team"].setValue("");
    this.reportBetweenDateForm.controls["subTeam"].setValue("");

    if (subSecIdForAPI.length > 0) {
      this.spinnerService.show();
      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          this.teams2 = response;
          this.spinnerService.hide();
        },
        (error) => {
          console.log(error)
        }
      );
    }


  }


  searchBySubTeam(val) {
    let orgType = "SUB_TEAM";
    let teamIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + teamIdForAPI;
    let queryParams: any = {};

    this.subTeams2 = [];
    this.reportBetweenDateForm.controls["subTeam"].setValue("");
    if (teamIdForAPI.length > 0) {
      this.spinnerService.show();
      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          this.subTeams2 = response;
          this.spinnerService.hide();
        },
        (error) => {
          console.log(error)
        }
      );
    }


  }



  loadReportAllAttendance() {




  }

  renderJasperReportToday() {

    const url = this.router.serializeUrl(
      this.router.createUrlTree(
        [this.contextPth + "reports/emp-attn-single-date"],
      )

    );

    window.open(url, "_blank");
  }

  renderJasperReport() {


    // data processing
    let formData = this.reportBetweenDateForm.value;
    let fromDate_ = formData.fromDate;
    let toDate_ = formData.toDate;
    let fromDate = this.datePipe.transform(fromDate_, "yyyy-MM-dd").toString().slice(0, 10);
    let toDate = this.datePipe.transform(toDate_, "yyyy-MM-dd").toString().slice(0, 10);
    let empCode = formData.empCode;

    let queryParams = {
      startDate: fromDate,
      endDate: toDate,
      empCode: empCode,
      operatingUnit: '',
      product: '',
      department: '',
      section: '',
      subSection: '',
      team: '',
      location: '',
      filter: 'operating_unit'
    };


    if (this.reportBetweenDateForm.get("operatingUnit").value) {
      queryParams[`operatingUnit`] = this.reportBetweenDateForm.get("operatingUnit").value;
    }

    if (this.reportBetweenDateForm.get("product").value) {
      queryParams[`product`] = this.reportBetweenDateForm.get("product").value;
    }

    if (this.reportBetweenDateForm.get("department").value) {
      queryParams[`department`] = this.reportBetweenDateForm.get("department").value;
    }

    if (this.reportBetweenDateForm.get("Section").value) {
      queryParams[`section`] = this.reportBetweenDateForm.get("Section").value;
    }

    if (this.reportBetweenDateForm.get("subSection").value) {
      queryParams[`subSection`] = this.reportBetweenDateForm.get("subSection").value;
    }

    if (this.reportBetweenDateForm.get("team").value) {
      queryParams[`team`] = this.reportBetweenDateForm.get("team").value;
    }

    if (this.reportBetweenDateForm.get("location").value) {
      queryParams[`location`] = this.reportBetweenDateForm.get("location").value;
    }


    // self
    // this.router.navigateByUrl('/reports/attendance-jsr-rpt');

    // new tab
    const url = this.router.serializeUrl(
      this.router.createUrlTree(
        [this.contextPth + "reports/all-emp-att"],
        { queryParams: queryParams }
      )
      // this.router.createUrlTree([`/reports/attendance-jsr-rpt`],{queryParams:queryParams})
    );

    window.open(url, "_blank");



  }

  public todaysexcel() {
    this.excelsrvc.downloadFile()
      .subscribe(response => {
        let fileName = 'Todays All Employee Attendence Report.xlsx'
        let blob: Blob = response.body as Blob;
        let a = document.createElement('a');
        a.download = fileName;
        a.href = window.URL.createObjectURL(blob);
        a.click();
        console.log(fileName);

      })

  }
  setDisplayLastSequence() {
    this.pngDiplayLastSeq = (((this.pageNum - 1) * this.pageSize) + this.pageSize);
    if (this.AttnReport.length < this.pageSize) {
      this.pngDiplayLastSeq = (((this.pageNum - 1) * this.pageSize) + this.pageSize);
    }
    if (this.totalItem < this.pngDiplayLastSeq) {
      this.pngDiplayLastSeq = this.totalItem;
    }
  }

  handlePageChange(event: number) {
    this.pageNum = event;
    this.loadAttnDada();
  }


  handlePageSizeChange(event: any): void {
    this.pageSize = event.target.value;
    this.pageNum = 1;
    this.loadAttnDada();
  }


  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};
    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    if (this.reportBetweenDateForm.controls.fromDate.value) {
      params[`fromDate`] = this.reportBetweenDateForm.controls.fromDate.value;
    }

    if (this.reportBetweenDateForm.controls.toDate.value) {
      params[`toDate`] = this.reportBetweenDateForm.controls.toDate.value;
    }
    if (this.reportBetweenDateForm.controls.empCode.value) {
      params[`loginCode`] = this.reportBetweenDateForm.controls.empCode.value;
    }

    return params;
  }

}
