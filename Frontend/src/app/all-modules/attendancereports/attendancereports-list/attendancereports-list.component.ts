import { DatePipe } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgxSpinnerService } from "ngx-spinner";
import { environment } from "src/environments/environment";
import { ReportBetweenDateDTO } from "../model/ReportBetweenDateDTO";
import { AttendanceReportService } from "../services/attendance-report.service";
import { ToastrService } from "ngx-toastr";
import { ReportsService } from "../../reports/service/reports.service";
import { DomSanitizer } from "@angular/platform-browser";
import { ComplainantStatementModule } from "../../complainant-statement/complainant-statement.module";
import { HrCrEmp } from "../../employees/model/HrCrEmp";
import { LoginService } from "src/app/login/services/login.service";

declare const $: any;
@Component({
  selector: "app-attendancereports-list",
  templateUrl: "./attendancereports-list.component.html",
  styleUrls: ["./attendancereports-list.component.css"],
})
export class AttendancereportsListComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public reportBetweenDateForm: FormGroup;

  contextPth = environment.contextPath;

  myFromGroup: FormGroup;

  public configPgn: any;

  AttnReport = [];
  startdate;
  todate;

  reportDisplay = false;
  dataLocalUrl: any;

  excel = [];

  user!: HrCrEmp;

  constructor(
    private formBuilder: FormBuilder,
    private attnReport: AttendanceReportService,
    private datePipe: DatePipe,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,
    private reportsService: ReportsService,
    private domSanitizer: DomSanitizer,
    private login: LoginService,
  ) {

    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 31,
      totalItem: 50,
      pageSizes: [31, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 31,
      // ngx plugin props
      itemsPerPage: 31,
      currentPage: 1,
      totalItems: 50
    };
  }


  ngOnInit() {
    this.initializeForm();
    // set init params
    this.myFromGroup = new FormGroup({
      pageSize: new FormControl()
    });

    this.myFromGroup.get('pageSize').setValue(this.configPgn.pageSize);
    this.loadAttnData(); // On Default Loading Current Month Attendance 
    this.selInitialDefaultValues();

  }

  initializeForm() {
    this.reportBetweenDateForm = this.formBuilder.group({
      fromDate: ["", [Validators.required]],
      toDate: ["", [Validators.required]],
    });
  }


  // -------------------- Get current login user ----------------------------

  loginUser() {
    this.user = this.login.getUser();
  }


  selInitialDefaultValues() {

    this.reportBetweenDateForm.controls.toDate.setValue(new Date()); // Set endDate to today's date

    // Set startDate to the first day of the current month
    const currentDate = new Date();
    const firstDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );

    this.reportBetweenDateForm.controls.fromDate.setValue(firstDayOfMonth);

  }

  formSubmit() {
    this.reportDisplay = false;
    this.spinnerService.show();
    let apiUrl = this.baseUrl + "/attnReport/reportBdateGet";
    let reportParams = {};
    reportParams['start_date'] = this.datePipe.transform((this.reportBetweenDateForm.get('fromDate').value), 'yyyy-MM-dd');
    reportParams['end_date'] = this.datePipe.transform((this.reportBetweenDateForm.get('toDate').value), 'yyyy-MM-dd');
    reportParams['pageNum'] = this.configPgn.pageNum;
    reportParams['pageSize'] = this.configPgn.pageSize;

    this.attnReport.sendGetRequest(apiUrl, reportParams).subscribe((response: any) => {
      this.AttnReport = response.objectList;
      this.spinnerService.hide();

    },
      (error) => {
        this.toastr.error(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      });


  }

  clearFilter() {
    this.ngOnInit();
    this.reportDisplay = false;
  }

  // ================== GET LAST 30 DAYS ATTENDANCE ===============

  loadAttnData() {
    this.spinnerService.show();
    let apiUrl = this.baseUrl + "/attnReport/reportBdateGet";

    let reportParams = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);
    reportParams['start_date'] = "empty";
    reportParams['end_date'] = "empty";

    this.attnReport.sendGetRequest(apiUrl, reportParams).subscribe((response: any) => {
      this.AttnReport = response.objectList;
      this.configPgn.totalItem = response.totalItems;
      this.configPgn.totalItems = response.totalItems;
      this.setDisplayLastSequence();
      this.spinnerService.hide();

    },
      (error) => {
        this.toastr.error(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      });
  }


  // ==================== DOWNLOAD PDF REPORT ================

  downLoadPdfReport() {
    this.reportDisplay = false;

    this.spinnerService.show();

    let reportParams = {};

    let reportFileName = "employee_wise_daily_punch_status";
    reportParams['outputFileName'] = "employee_wise_daily_punch_status";
    let startDate = this.datePipe.transform(this.reportBetweenDateForm.get('fromDate').value, "yyyy-MM-dd").toString().slice(0, 10);
    let endDate = this.datePipe.transform(this.reportBetweenDateForm.get('toDate').value, "yyyy-MM-dd").toString().slice(0, 10);;;
    let userLoginCode = this.user?.loginCode;

    this.reportsService.employeeAttendanceRpt(reportFileName, userLoginCode, startDate, endDate).subscribe((response: any) => {

      const file = new Blob([response], { type: 'application/pdf' });
      const fileURL = URL.createObjectURL(file);
      this.dataLocalUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(fileURL);
      $('body').css('height', '100%');
      $('.main-wrapper').css('height', '100%');
      $('.main-wrapper .page-wrapper').css('height', '100%');

      // Wait for a brief moment for the DOM to update after *ngIf
      setTimeout(() => {
        $([document.documentElement, document.body]).animate({
          scrollTop: $("#idReportDisplay").offset()?.top
        }, 2000);
      }, 100); // Adjust the timeout duration as needed

      this.spinnerService.hide();
      this.reportDisplay = true;
    }, (error) => {
      this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      this.spinnerService.hide();
    });
  }

  // ------------------Query Param -----------------------------


  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    return params;

  }



  // ------------------pagination handling methods start -----------------------------

  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.AttnReport.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {

    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this.loadAttnData();
  }
  handlePageSizeChange(event: any): void {

    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this.loadAttnData();
  }


  // ------------------------------pagination handling methods end -------------------------------------------


}
