import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AttendancereportsComponent } from './attendancereports.component';
import { AttendancereportsListComponent } from './attendancereports-list/attendancereports-list.component';
import { AttendancereportsHrAdminListComponent } from './attendancereport-hr-admin-list/attendancereports-list.component';
import { AttendanceWaiverOnoffstatusComponent } from './attendance-waiver-onoffstatus/attendance-waiver-onoffstatus.component';

const routes: Routes = [
  {
    path:"",
    component:AttendancereportsComponent,
    children:[
     {
      path:"attendance-reports",
      component:AttendancereportsListComponent
     },
     {
      path:"attnRptsHradmin",
      component:AttendancereportsHrAdminListComponent
     },
     {
      path:"waiver-status",
      component:AttendanceWaiverOnoffstatusComponent
     },

    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AttendancereportsRoutingModule { }
