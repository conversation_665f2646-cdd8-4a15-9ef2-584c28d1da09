import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-ticket-support-view',
  templateUrl: './ticket-support-view.component.html',
  styleUrls: ['./ticket-support-view.component.css']
})
export class TicketSupportViewComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;

  public myFormData: any = {};

  // for multi select
  public configDDL: any;
  public configPgn: any;
  public user: any;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService
  ) {
    this.configPgn = {
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      totalItem: 50,
      pngDiplayLastSeq: 10,
      entityName: "",
    };

  }

  ngOnInit(): void {
    this.getApplicationData();
  }


  getApplicationData() {
    const id = this.route.snapshot.params.id;
    const apiURL = this.baseUrl + '/software-support/get/' + id;

    const queryParams: any = {};
    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        this.myFormData = response.data;
        this.spinnerService.hide();

      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }


  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }



}
