<div class="modal custom-modal" id="organizationSearchModal" role="dialog">

    <div class="modal-content">
        <h3 class="text-center mb-2"><span style="color:#25B6B2;">Employee Details</span></h3>


        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="close()">
            <span aria-hidden="true">&times;</span>
        </button>

        <div class="row">

            <div class="col-md-12 d-flex justify-content-center">

                <div class="card-client">
                    <div class="user-picture">
                        <a style="border: 0;margin: 0;padding: 0;"><img src="{{ baseUrl  + empData?.photo}}"
                                onError="this.src='assets/img/user-icon/maleEmp.png'"
                                style="height: 9rem; width: 9rem;" />
                        </a>
                    </div>
                    <p class="name-client"> <b style="color: #003566;">{{empData?.name}}
                            ({{empData?.employeeId}})</b>
                        <span style="color: #28B4B4;">{{empData?.role}}
                        </span>
                    </p>
                    <div class="social-media">
                        <h4 style="color: #003566;">{{empData?.designation}}</h4>
                    </div>
                </div> &nbsp; &nbsp;

                <div class="card-client2">
                    <table class="summaryTable col-md-12">


                        <tr>
                            <td><b>Operating Unit</b></td>
                            <td>{{empData.operatingUnit?empData.operatingUnit:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Product</b></td>
                            <td>{{empData.product?empData.product:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Department</b></td>
                            <td>{{empData.department?empData.department:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Section</b></td>
                            <td>{{empData.section?empData.section:'-'}}</td>

                        </tr>
                        
                        <tr>
                            <td><b>Sub Section</b></td>
                            <td>{{empData.subSection?empData.subSection:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Team</b></td>
                            <td>{{empData.team?empData.team:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Sub Team</b></td>
                            <td>{{empData.subTeam?empData.subTeam:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Job Location</b></td>
                            <td>{{empData.jobLocation?empData.jobLocation:'-'}}</td>

                        </tr>

                        <tr>
                            <td><b>Joining Date</b></td>
                            <td>{{empData?.joiningDate |date}} ({{empData?.jobDuration}})</td>

                        </tr>

                        <tr *ngIf="empData?.gross">
                            <td><b>Gross Salary</b></td>
                            <td>{{empData?.gross}}</td>

                        </tr>


                    </table>
                </div>


            </div>

        </div>
    </div>
</div>