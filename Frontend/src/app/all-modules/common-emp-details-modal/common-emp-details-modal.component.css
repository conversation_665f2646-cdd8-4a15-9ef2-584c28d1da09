/* Style the modal here as needed */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    width: 70vw;
}

/* Add additional styling as needed */


.summaryTable tr td {
	border: 1px solid #dddddd;
	padding: 5px;
}

.content {
    padding: 15px;
}

.content div {
    text-align: center;
}

.associateDiv {
    border: 1px solid #28B4B4;
    padding: 0.5em 0.75em;
    text-decoration: none;
    display: inline-block;
    border-radius: 5px;
    position: relative;
    top: 1px;
    margin-bottom: 2.5%;
}



fieldset.fieldsetBorder {
    border: 1px solid;
    border-color: rgba(31, 31, 31, 0.25);
    /*1f1f1f*/
    border-radius: 5px;
    margin: 1px;
    margin-bottom: 7px;
    padding-left: 5px;
    padding-bottom: 1%;
}

fieldset.fieldsetWithoutBorder {
    margin-bottom: 7px;
}

fieldset legend {
    /* border: 1px solid; */
    /* border-color: rgba(31, 31, 31, 0.25); */
    width: auto;
    border-radius: 5px;
    font-size: 15px;
    padding-left: 5px;
    padding-right: 5px;
    background-color: #00A7B3;
    color: white;
}

.logBox .form-group {
    float: left;
}

.logBox .form-group label {
    width: 295px;
    margin-right: 5px;
    margin-left: 5px;
}

.logBox .form-group div {
    border: 1px solid;
    border-radius: 3px;
    border-color: rgba(31, 31, 31, 0.25);
    padding-left: 3px;
    padding-right: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    margin-right: 5px;
    margin-left: 5px;
    min-height: 25px;
}

.logBox {
    font-size: 13px;
    background: #25b6b10c;
}



/*
 style for tab
*/

.tabset>input[type="radio"] {
    position: absolute;
    left: -200vw;
}

.tabset .tab-panel {
    display: none;
}

.tabset>input:first-child:checked~.tab-panels>.tab-panel:first-child,
.tabset>input:nth-child(3):checked~.tab-panels>.tab-panel:nth-child(2),
.tabset>input:nth-child(5):checked~.tab-panels>.tab-panel:nth-child(3),
.tabset>input:nth-child(7):checked~.tab-panels>.tab-panel:nth-child(4),
.tabset>input:nth-child(9):checked~.tab-panels>.tab-panel:nth-child(5),
.tabset>input:nth-child(11):checked~.tab-panels>.tab-panel:nth-child(6),
.tabset>input:nth-child(13):checked~.tab-panels>.tab-panel:nth-child(7) {
    display: block;
}



.tabset>label {
    position: relative;
    display: inline-block;
    padding: 10px 10px 10px;
    border: 1px solid transparent;
    border-bottom: 0;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: -1.5rem;
    transition: transform .2s;
    /* Animation */
}

/* .tabset > label::after {
	content: "";
	position: absolute;
	left: 15px;
	bottom: 10px;
	width: 22px;
	height: 4px;
	background: #8d8d8d;
  } */

.tabset>label:hover,
.tabset>input:focus+label {
    color: #00A7B3;
    transform: scale(1.2);
}


.tabset>label:hover::after,
.tabset>input:focus+label::after,
.tabset>input:checked+label::after {
    background: #00A7B3;
}

.tabset>input:checked+label {
    border-color: #ccc;
    border-bottom: 1px solid #EBF2FC;
    margin-bottom: -1px;
    color: #00A7B3;
    transform: scale(1);
}

.tab-panel {
    padding: 30px 0;
    border-top: 1px solid #ccc;
}


*,
*:before,
*:after {
    box-sizing: border-box;
}



@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

fieldset {
    animation: fadeIn 1s ease-in-out;
    opacity: 0;
    animation-fill-mode: forwards;
}


.card-client {
    background: white;
    width: 20rem;
    padding-top: 25px;
    padding-bottom: 25px;
    padding-left: 20px;
    padding-right: 20px;
    border: 2px solid #1FBAAB;
    box-shadow: 0 6px 10px rgba(207, 212, 222, 1);
    border-radius: 10px;
    text-align: center;
    color: #fff;
    font-family: "Poppins", sans-serif;
    transition: all 0.3s ease;
}

.card-client2 {
    background: white;
    width: 35rem;
    padding-top: 25px;
    padding-bottom: 25px;
    padding-left: 20px;
    padding-right: 20px;
    border: 2px solid #1FBAAB;
    box-shadow: 0 6px 10px rgba(207, 212, 222, 1);
    border-radius: 10px;
    font-family: "Poppins", sans-serif;
}

.card-client:hover {
    transform: translateY(-10px);
}

.user-picture {
    overflow: hidden;
    object-fit: cover;
    width: 9rem;
    height: 9rem;
    border: 4px solid #1FBAAB;
    border-radius: 999px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
}

.name-client {
    margin: 0;
    margin-top: 20px;
    font-weight: 600;
    font-size: 15px;
}

.name-client span {
    display: block;
    font-weight: 200;
    font-size: 16px;
}

.social-media:before {
    content: " ";
    display: block;
    width: 100%;
    height: 2px;
    margin: 20px 0;
    background: #28B4B4;
}



/* TESTIMONIALS */
.leftPicDiv {
    margin-left: 23%;
}

.rightPicDiv {
    margin-left: -1%;
}

.user-picture2 {
    overflow: hidden;
    object-fit: cover;
    width: 7rem;
    height: 7rem;
    border: 4px solid #1FBAAB;
    border-radius: 999px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
}

.name-client2 {
    margin: 0;
    margin-top: 20px;
    font-weight: 600;
    font-size: 16px;
}

.name-client2 span {
    display: block;
    font-weight: 200;
    font-size: 16px;
}

.testimonialOdd {
    padding: 2%;
    padding-top: 1%;
    margin-left: -3%;
}

.testimonialEven {
    padding: 2%;
    padding-top: 1%;
    margin-left: 10%;
}

.odd {
    animation: slideright 2s ease;
}

@keyframes slideright {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

.even {
    animation: slideleft 2s forwards;
}

@keyframes slideleft {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}