import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { environment } from 'src/environments/environment';
import { CommonService } from '../settings/common/services/common.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-common-emp-details-modal',
  templateUrl: './common-emp-details-modal.component.html',
  styleUrls: ['./common-emp-details-modal.component.css']
})
export class CommonEmpDetailsModalComponent implements OnInit {

  @Input() emploginCode: any;
  @Output() closeModal: EventEmitter<any> = new EventEmitter<any>();

  public baseUrl = environment.baseUrl;
  public empData: any = {};

  constructor(
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,

  ) { }

  ngOnInit(): void {
    this.getEmployeeDetails();
  }

  getEmployeeDetails() {

    this.spinnerService.show();
    let apiURL = this.baseUrl + "/emp/viewDetails?code=" + this.emploginCode;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.empData = response;
        this.getJobDurationCountDown(response?.joiningDate);
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    )
  }


  close() {
    this.emploginCode = null;
    this.empData = {};
    this.closeModal.emit();
  }

  getJobDurationCountDown(date) {

    let today = new Date();
    let joinDate = new Date(date);

    let years: any;
    let months: any;
    let days: any;

    years = today.getFullYear() - joinDate.getFullYear();
    months = today.getMonth() - joinDate.getMonth();
    days = today.getDate() - joinDate.getDate();

    if (days < 0) {
      months--;
      days += new Date(today.getFullYear(), today.getMonth(), 0).getDate();
    }
    if (months < 0) {
      years--;
      months += 12;
    }

    this.empData.jobDuration = (`${years}Y ${months}M ${days}D`);

  }


}

