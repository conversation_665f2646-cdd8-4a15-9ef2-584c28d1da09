import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MarketingAndSalesComponent } from './marketing-and-sales.component';
import { ScTshirtCreateComponent } from './t-shirt-management/sales-consultant/create/sc-tshirt-create.component';
import { ScTshirtListComponent } from './t-shirt-management/sales-consultant/list/sc-tshirt-list.component';
import { ScTshirtViewComponent } from './t-shirt-management/sales-consultant/view/sc-tshirt-view.component';
import { VisitPlanListComponent } from './visit-plan/list/visit-plan-list.component';
import { VisitPlanCreateComponent } from './visit-plan/create/visit-plan-create.component';
import { VisitPlanViewComponent } from './visit-plan/view/visit-plan-view.component';
import { VisitPlanEditComponent } from './visit-plan/edit/visit-plan-edit.component';

const routes: Routes = [
  {
    path: '',
    component: MarketingAndSalesComponent,
    children: [
      {
        path: "Tshirt/sales-consultant",
        component: ScTshirtListComponent,
        data: { title: 'Tshirt - Sales Consultant' }
      },
      {
        path: "Tshirt/sales-consultant/create",
        component: ScTshirtCreateComponent,
        data: { title: 'Tshirt - Sales Consultant Create' }
      },
      {
        path: "Tshirt/sales-consultant/view/:id",
        component: ScTshirtViewComponent,
        data: { title: 'Tshirt - Sales Consultant View' }
      },
      {
        path: "visit-plan",
        component: VisitPlanListComponent,
        data: { title: 'Marketing & Sales - Visit Plan' }
      },
      {
        path: "visit-plan/create",
        component: VisitPlanCreateComponent,
        data: { title: 'Marketing & Sales - Visit Plan Create' }
      },
      {
        path: "visit-plan/view/:id",
        component: VisitPlanViewComponent,
        data: { title: 'Marketing & Sales -  Visit Plan View' }
      },
      {
        path: "visit-plan/edit/:id",
        component: VisitPlanEditComponent,
        data: { title: 'Marketing & Sales -  Visit Plan Edit' }
      },

    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MarketingAndSalesRoutingModule { }
