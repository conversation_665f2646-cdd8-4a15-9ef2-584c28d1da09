<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Sales Consultant
                                T-Shirt</b></span></li>
                    <li class="breadcrumb-item active">Create</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/marketing-and-sales/Tshirt/sales-consultant"><i
                        class="fa fa-share"></i> Back To
                    List</a> &nbsp;

                <div class="col-auto float-right ml-auto">
                    <a class="btn add-btn"
                        (click)="downloadFile('/assets/file/Excel/Sales_Consultant_T-Shirt.xlsx')"><i
                            class="fa fa-download"></i> Excel Format</a>
                </div>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">
                <div class="card-body">
                    <form novalidate (ngSubmit)="myFormSubmit()" [formGroup]="myForm">

                        <div class="row">


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class=" val-required">Proposed By </label>

                                    <ng-select [items]="configDDL.listData" formControlName="proposedBy"
                                        bindLabel="ddlDescription" placeholder="Select Employee" bindValue="ddlCode"
                                        [searchable]="true" [clearable]="true" [virtualScroll]="true"
                                        [clearOnBackspace]="true" (search)="searchDDL($event)"
                                        (scrollToEnd)="scrollToEndDDL()" (clear)="clearDDL()"
                                        (click)="initSysParamsDDL($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes')"
                                        ddlActiveFieldName="ddlDescription" class="custom-ng-select">
                                    </ng-select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class=" val-required">Approx. Distribution</label>
                                    <div class="cal-icon">
                                        <input class="form-control datetimepicker" formControlName="tShirtFor"
                                            bsDatepicker type="text" placeholder="DD-MM-YYYY"
                                            [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class=" val-required">Description</label>
                                    <span class="float-right">
                                        {{ numberOfCharacters }} / {{maxNumberOfCharacters}}
                                    </span>

                                    <textarea type="text" class="form-control" formControlName="description"
                                        (keyup)="addrsDuringLeaveCount($event)" rows="1"></textarea>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">T-Shirt Excel File</label>
                                    <input class="form-control" type="file" name="file" id="uploadXl"
                                        (change)="onFileSelectDoc($event)">
                                    <span class="text-danger" *ngIf="formDataDoc.controls.file.invalid">Excel file
                                        is
                                        required</span>

                                </div>

                            </div>

                        </div>


                        <div class="text-right">
                            <button type="button" class="btn btn-secondary btn-ripple" (click)="resetFormValues()">
                                <i class="fa fa-undo" aria-hidden="true"></i> Reset
                            </button>
                            &nbsp; &nbsp;
                            <button type="submit" class="btn btn-primary btn-ripple"
                                [disabled]="myForm.invalid || formDataDoc.invalid">
                                <i class="fa fa-check" aria-hidden="true"></i> Submit &nbsp;&nbsp;&nbsp;
                            </button>

                        </div>


                    </form>


                </div>
            </div>
        </div>
    </div>

</div>
<!-- /Page Content -->




<!-- Employee excelErrorList History Modal -->

<div id="excelErrorList_modal" class="modal custom-modal fade " role="dialog">
    <div class="modal-dialog modal-dialog-centered excelErrorList_modal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Excel Upload Error List</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">


                <div class="row">
                    <div class="col-md-12">
                        <div class="card customCard">
                            <div class="card-body">
                                <div class="table-wrapper-scroll-y my-custom-scrollbar table-responsive">
                                    <table class="table table-striped custom-table datatable">
                                        <thead>
                                            <tr>
                                                <th>SL</th>
                                                <th>Employee</th>
                                                <th>Reason</th>

                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let val of excelErrorData ;let i = index"
                                                [class.active]="i == currentIndex">
                                                <td>{{i+1}}</td>
                                                <td>{{val.empCode}}</td>
                                                <td>{{val.reason}}</td>

                                            </tr>
                                            <tr *ngIf="excelErrorData?.length === 0">
                                                <td colspan="10">
                                                    <h5 style="text-align: center;">No Error found</h5>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- excelErrorList Modal End -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>