import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { HrCrEmp } from 'src/app/all-modules/employees/model/HrCrEmp';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { LeaveTrx } from 'src/app/all-modules/self-service/models/LeaveTrx';
import { LeaveService } from 'src/app/all-modules/self-service/service/leave.service';
import { ALKP } from 'src/app/all-modules/settings/common/models/alkp';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LeaveConfigService } from 'src/app/all-modules/settings/leave/services/leave-config.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';
declare const $: any;

@Component({
  selector: 'app-sc-tshirt-create',
  templateUrl: './sc-tshirt-create.component.html',
  styleUrls: ['./sc-tshirt-create.component.css']
})
export class ScTshirtCreateComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  public formDataDoc: FormGroup;

  configDDL: any;

  user!: HrCrEmp;


  excelErrorData: any = [];

  docSeleted = false;
  imgSrc: any;


  // Text area field character Count

  maxNumberOfCharacters = 100;
  numberOfCharacters = 0;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private login: LoginService,
    private leaveService: LeaveService,
    private toastr: ToastrService,
    private spinnerSerive: NgxSpinnerService,
    private commonService: CommonService,
    private employeeService: EmployeeService,
  ) {
    this._initConfigDDL();
    this._customInitLoadData();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  empName(event) {

  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      proposedBy: [null, Validators.required],
      tShirtFor: ["", [Validators.required]],
      description: ["", [Validators.required]],
      // excelUpload: [""]

    });

    this.formDataDoc = this.formBuilder.group({
      file: ["", [Validators.required]],
    });
  }

  get getfile() {
    return this.formDataDoc.get("file");
  }



  // -------------- Download File -------------------

  downloadFile(fileName) {

    window.open(fileName, '_blank');
  }


  // ------------------------ Select DOC --------------------------


  onFileSelectDoc(event) {

    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      this.docSeleted = true;
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imgSrc = reader.result);
      this.formDataDoc.get("file").setValue(file);
    }
  }

  // ------------------------ Final Submit Form --------------------------

  myFormSubmit() {

    let apiURL = this.baseUrl + "/tShirtMgt/save";
    let formData: any = {};
    formData = Object.assign(this.myForm.value, {
      proposedBy: this.getEmployee.value ? { id: this.getEmployee.value } : null,

    });

    // process date
    formData.tShirtFor = (formData.tShirtFor) ? this.commonService.format_Date_Y_M_D(formData.tShirtFor) : null;

    this.spinnerSerive.show();
    this.leaveService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        if (this.docSeleted) {

          this.uploadFile(response?.data?.id);
        }
        else {
          this.toastr.success("Tshirt Requisition Created Successfully");
          this.spinnerSerive.hide();
          this.router.navigate(['/marketing-and-sales/Tshirt/sales-consultant']);
        }
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerSerive.hide();
      }
    );
  }


  // ------------------------ Upload DOC --------------------------



  uploadFile(id) {

    let apiURL = this.baseUrl + "/tShirtMgt/upload/" + id;

    if (this.docSeleted) {

      const formData = new FormData();
      formData.append("file", this.formDataDoc.get("file").value);
      formData.append("type", "file");
      this.leaveService.sendPostRequest(apiURL, formData).subscribe(
        (data) => {

          this.excelErrorData = data;
          this.formDataDoc.reset();
          this.formDataDoc.controls.file.setValue("");
          $("#uploadXl").val('');
          this.spinnerSerive.hide();

          if (!this.excelErrorData) {
            this.toastr.success("Tshirt Requisition Created Successfully");
            this.toastr.success('File Uploaded Successfully');
            this.router.navigate(['/marketing-and-sales/Tshirt/sales-consultant']);
            $("#upload_modal").modal("hide");
          }

          if (this.excelErrorData) {
            this.toastr.warning('Some Data Not Updated');
            $("#excelErrorList_modal").modal("show");
          }

        },
        (error) => {
          this.toastr.warning("Error" + error.message);
          this.router.navigate(['/marketing-and-sales/Tshirt/sales-consultant']);
        }
      );
    }

    else {
      this.router.navigate(['/marketing-and-sales/Tshirt/sales-consultant']);
    }

  }


  // Textarea field character Count

  addrsDuringLeaveCount(event: any): void {
    this.numberOfCharacters = event.target.value.length;

    if (this.numberOfCharacters > this.maxNumberOfCharacters) {
      event.target.value = event.target.value.slice(0, this.maxNumberOfCharacters);
      this.numberOfCharacters = this.maxNumberOfCharacters;
    }
  }


  resetFormValues() {
    this.myForm.reset();
    this.formDataDoc.reset();

  }


  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmpSpec";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------


  //-----------Get Relational Object Id ------------------

  get getEmployee() {
    return this.myForm.get("proposedBy");
  }
  get getAlkpLeaveId() {
    return this.myForm.get("alkpLeaveType");
  }

  get getAlkpLeaveCreateType() {
    return this.myForm.get("leaveCreateType");
  }

}
