.content {
	padding: 15px;
	font-size: 15px;
}

.excelErrorList_modal {
    min-width: 70vw;
}


.leave_entity{
	min-width: 50vw;
}

.progress {
	background-color: #cae3e3;
}



/* Apply the animation to the progress bar */
.progress-bar {
	animation: progressBarAnimation 2s linear forwards;
	/* Use linear animation for 2 seconds */
  }
  
  
  /* Define the animation */
  @keyframes progressBarAnimation {
	0% {
	  width: 0%;
	}
  
	/* Start with 0% width */
	100% {
	  width: calc(100% * var(--progress) / var(--progressPct));
	}
  
	/* End with calculated width */
  }
  
  

.bg-primary,
.badge-primary {
	background-color: #25B6B2 !important;
}


.leavecustomDesign {
	border-radius: 10px;
	background: #ffffff;
	box-shadow: 10px 10px 10px #d9d9d9,
		-0px -0px 0px #ffffff;
}

/* ================= Default Template Color ================== */
xx-.form-control {
	border-color: #e3e3e3;
	box-shadow: none;
}

xx-.form-control:focus {
	border-color: #ccc;
	box-shadow: none;
	outline: 0 none;
}



/* ================= Purpale Color =========================== */
input.form-control,
select.form-control,
textarea.form-control {
	border-color: #4BA1D9;
	border-left: 3px solid #4BA1D9;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}


.form-control:focus {
	border-color: #705CBA;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
	box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================= Green Color ============================= */
xx-input.form-control {
	border-color: #d4cdcd;
	border-left: 3px solid green;
	box-shadow: none;
}

xx-.form-control:focus {
	border-color: #37a000;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
	box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================== Blue Color ============================== */
xx-input.form-control {
	border-color: #66afe9;
	border-left: 3px solid #66afe9;
	box-shadow: none;
}

xx-.form-control:focus {
	border-color: #66afe9;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
	box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}


input.ng-invalid {
	border: 1px solid red;
}

select.ng-invalid {
	border: 1px solid red;
}

textarea.ng-invalid {
	border: 1px solid red;
}

input.ng-valid {
	border-left: 2px solid green
}

select.ng-valid {
	border-left: 2px solid green;
}

textarea.ng-valid {
	border-left: 2px solid green;
}


fieldset.fieldsetBorder {
	border: 1px solid;
	border-color: rgba(31, 31, 31, 0.25);
	/*1f1f1f*/
	border-radius: 5px;
	margin: 1px;
	margin-bottom: 7px;
	padding-left: 5px;
}

fieldset.fieldsetWithoutBorder {
	margin-bottom: 7px;
}

fieldset legend {
	/* border: 1px solid; */
	/* border-color: rgba(31, 31, 31, 0.25); */
	width: auto;
	border-radius: 5px;
	font-size: 15px;
	padding-left: 5px;
	padding-right: 5px;
	margin-left: 7px;
}

.logBox .form-group {
	float: left;
}

.logBox .form-group label {
	width: 295px;
	margin-right: 5px;
	margin-left: 5px;
}

.logBox .form-group div {
	border: 1px solid;
	border-radius: 3px;
	border-color: rgba(31, 31, 31, 0.25);
	padding-left: 3px;
	padding-right: 3px;
	padding-top: 1px;
	padding-bottom: 1px;
	margin-right: 5px;
	margin-left: 5px;
	min-height: 25px;
}

.logBox {
	font-size: 13px;
}