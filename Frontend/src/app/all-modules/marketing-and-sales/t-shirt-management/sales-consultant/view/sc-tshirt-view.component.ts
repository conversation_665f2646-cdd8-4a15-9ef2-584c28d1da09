import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { ApprovalService } from 'src/app/all-modules/approval/service/approval.service';
import { HrCrEmp } from 'src/app/all-modules/employees/model/HrCrEmp';
import { OnTourService } from 'src/app/all-modules/self-service/service/on-tour.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-sc-tshirt-view',
  templateUrl: './sc-tshirt-view.component.html',
  styleUrls: ['./sc-tshirt-view.component.css']
})
export class ScTshirtViewComponent implements OnInit {


  public baseUrl = environment.baseUrl;
  public myData: any = {};
  appliedHrCrEmpId: any;

  listData: any = [];
  listData2: any = [];

  user!: HrCrEmp;

  public myForm: FormGroup;
  public tourForm: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private spinnerService: NgxSpinnerService,
    private onTourService: OnTourService,
    private approvalService: ApprovalService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private router: Router,
    private datePipe: DatePipe,
  ) { }


  ngOnInit(): void {
    this.initializeForm();
    this.getFormData();


  }
  initializeForm() {

    this.myForm = this.formBuilder.group({
      id: [""],
      approvalStepAction: ["", Validators.required],
      remarks: ["", Validators.required],

    });

  }

  //----------------------- Get Application Data -----------------

  getFormData() {

    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/tShirtMgt/get/" + id;

    let queryParams: any = {};


    this.spinnerService.show();
    this.onTourService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.myData = response;
        this.appliedHrCrEmpId = this.myData?.proposedById;

        this.spinnerService.hide();
        this.getSelfListData();
        this.getApprovalStepAction();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }

  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }


  // --------------------Approval Details ---------------------

  getSelfListData() {

    let id = this.route.snapshot.params.id;

    this.spinnerService.show();
    let apiURL = this.baseUrl + "/approvalProcTnxHtry/getSelfApprovalProcTnxList/" + id;

    let queryParams: any = {};
    const params = this.getUserQueryParams();
    queryParams = params;


    this.approvalService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response;
        this.spinnerService.hide();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }



  getApprovalStepAction() {
    let id = this.route.snapshot.params.id;


    let apiURL = this.baseUrl + "/approvalStepAction/getApprovalStepAction/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};

    queryParams = params;


    this.approvalService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData2 = response;
        this.spinnerService.hide();

      },
      (error) => {
        this.spinnerService.hide();

      }
    );
  }

  tackAction() {
    if (this.myForm.invalid) {
      return;
    }
    let id = this.route.snapshot.params.id;
    let obj = Object.assign(this.myForm.value, {
      referenceId: id,
      referenceEntity: this.myData?.approvalProcess?.code + "/" + this.myData?.approvalStep?.thisApprovalNode + "/" + this.myData?.approvalStep?.nextApprovalNode + "/" + this.myData?.proposedById,
      approvalStepAction: this.get.value ? { id: this.get.value } : null,
    });

    this.spinnerService.show();
    let apiURL = this.baseUrl + "/approvalProcTnxHtry/edit";
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;

    let formData: any = {};
    formData = obj

    this.approvalService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.listData = [];
        this.listData2 = [];
        this.resetFormValues();

        this.toastr.success("Action Taken Successfully");
        this.getFormData();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.")

      }
    );



  }


  resetFormValues() {

    this.myForm.reset();
  }

  private getUserQueryParams(): any {

    let params: any = {};
    // push other attributes

    params[`approvalProcess`] = this.myData?.approvalProcess?.code;

    params[`nextApprovalNode`] = this.myData?.approvalStep?.nextApprovalNode;

    params[`thisApprovalNode`] = this.myData?.approvalStep?.thisApprovalNode;

    params[`appliedHrCrEmpId`] = this.appliedHrCrEmpId;

    return params;
  }


  get get() {
    return this.myForm.get("approvalStepAction");
  }


}
