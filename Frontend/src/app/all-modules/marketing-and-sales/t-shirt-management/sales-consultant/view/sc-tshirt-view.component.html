<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Sales Consultant
                                T-Shirt</b></span></li>
                    <li class="breadcrumb-item active">Show</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/marketing-and-sales/Tshirt/sales-consultant"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">



                    <div class="row">
                        <div class="col-md-12">


                            <table class="summaryTable col-md-12">
                                <tr>
                                    <td><b>Reference Code</b></td>
                                    <td>{{myData?.code}}</td>
                                    <td><b>Proposed By</b></td>
                                    <td>
                                        {{myData?.proposedByName}} ({{myData?.proposedByCode}})
                                    </td>

                                    <td><b>Created</b></td>
                                    <td> {{myData?.createDate | date}} <br> {{myData?.createdByCode}} -
                                        {{myData?.createdByName}}</td>
                                </tr>

                                <tr>
                                    <td><b>Approval Status</b></td>
                                    <td> <span
                                            *ngIf="myData?.approvalStatus === 'Submitted' || myData?.approvalStatus === 'SUBMITTED'">
                                            <span class="text-info"> <b> {{myData?.approvalStatus}}
                                                </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Recommended' || myData?.approvalStatus === 'RECOMMENDED'">
                                            <span class="text-warning">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Rejected' || myData?.approvalStatus === 'REJECTED'">
                                            <span class="text-danger">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Approved' || myData?.approvalStatus === 'APPROVED'">
                                            <span class="text-success">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                    </td>

                                    <td><b>Approx. Distribution</b></td>
                                    <td>{{myData?.tShirtFor | date}}</td>
                                    <td><b>Description</b></td>
                                    <td>{{myData?.description}}</td>

                                </tr>


                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row" *ngIf="myData?.tshirtMgtDetailsDTOS?.length > 0">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">

                    <div class="row">
                        <div class="col-12">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">T-Shirt Details</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>SL</th>
                                            <th>Employee</th>
                                            <th>Size</th>
                                            <th>Team</th>
                                            <th>Sub Team</th>
                                            <th>Date Of Joining</th>
                                            <th>Last Receive Date</th>
                                            <th>Next Receive Date</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of myData.tshirtMgtDetailsDTOS;let i = index">
                                            <td>
                                                {{ 1 + i}}
                                            </td>
                                            <td>{{thisObj?.empCode}} -
                                                {{thisObj?.empName}}</td>
                                            <td>{{thisObj?.tshirtSize}}</td>
                                            <td>{{thisObj?.waltonPlazaDivision}}</td>
                                            <td>{{thisObj?.waltonPlazaName}}</td>
                                            <td>{{thisObj.dateOfJoining | date}}</td>
                                            <td>{{thisObj.lastTShirtReceivingDate | date}}</td>
                                            <td>{{thisObj.nextShirtReceivingDate | date}}</td>
                                            <td>{{thisObj?.remarks}}</td>



                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>




    <div class="row">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">

                    <div class="row">
                        <div [ngClass]="{'col-md-12': listData2?.length === 0, 'col-md-8': listData2?.length !== 0 }">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">Approval Status</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>S/L</th>
                                            <th>Approval Step</th>
                                            <th>Sign By</th>
                                            <th>Designation</th>
                                            <th>Action</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of listData;let i = index">
                                            <td>
                                                {{ 1 + i}}
                                            </td>
                                            <td>{{thisObj.approvalStep.approvalGroupName?thisObj.approvalStep.approvalGroupName:'-'}}
                                            </td>
                                            <td>
                                                {{ thisObj?.approvalStepApproverEmp?.loginCode}} - {{
                                                thisObj?.approvalStepApproverEmp?.displayName}}
                                            </td>
                                            <td>{{thisObj?.designation}}
                                            </td>
                                            <td>{{thisObj.actionStatus?thisObj.actionStatus:'-'}}
                                            </td>
                                            <td>
                                                {{thisObj.actionStatus?thisObj.updateDateTime.substr(0,10) :'-'}}
                                            </td>
                                            <td>
                                                {{thisObj.actionStatus?thisObj.updateDateTime.substr(11) :'-'}}</td>

                                            <td>{{thisObj.remarks?thisObj.remarks:'-'}}</td>

                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>


                        <div class="col-4" *ngIf="listData2?.length > 0">
                            <form novalidate (ngSubmit)="tackAction()" [formGroup]="myForm">
                                <fieldset class="row fieldsetBorder logBox ">
                                    <legend class="bg-warning">Take Action</legend>


                                    <label class="col-form-label col-md-3">Status</label>
                                    <div class="col-md-8">
                                        <select class="select form-control" formControlName="approvalStepAction">
                                            <option value="">Select Action</option>
                                            <option *ngFor="let data of listData2" [ngValue]='data.id'>
                                                {{data.activityStatusTitle}}
                                            </option>
                                        </select>

                                    </div>
                                    <br><br>

                                    <label class="col-form-label col-md-3">Remarks</label>
                                    <div class="col-md-8">
                                        <textarea formControlName="remarks" class="form-control mb-3"></textarea>

                                    </div>


                                    <div class="col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary btn-ripple mb-2 ">
                                            &nbsp; Save &nbsp;
                                        </button>
                                    </div>

                                </fieldset>
                            </form>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>


</div>
<!-- /Page Content -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>