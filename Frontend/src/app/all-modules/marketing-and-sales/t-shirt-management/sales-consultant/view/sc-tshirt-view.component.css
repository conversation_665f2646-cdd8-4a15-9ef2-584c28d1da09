.content {
	padding: 15px;
}

.summaryTable tr td {
	border: 1px solid #dddddd;
	padding: 5px;
}

 fieldset.fieldsetBorder {
	border: 1px solid;
	border-color: rgba(31, 31, 31, 0.25); /*1f1f1f*/
	border-radius: 5px;
	margin: 1px;
	margin-bottom: 7px;
	padding-left: 5px;
}
 fieldset.fieldsetWithoutBorder {
	margin-bottom: 7px;
}

 fieldset legend {
	/* border: 1px solid; */
	/* border-color: rgba(31, 31, 31, 0.25); */
	width: auto;
	border-radius: 5px;
    font-size: 15px;
    padding-left: 5px;
    padding-right: 5px;
	margin-left: 7px;
}
.logBox .form-group {
	float: left;
}

.logBox .form-group label {
	width: 295px;
	margin-right: 5px;
	margin-left: 5px;
}

.card{
	border-radius: 10px;
	background: #ffffff;
	box-shadow: 10px 10px 10px #d9d9d9,
    -0px -0px 0px #ffffff;
}

.logBox .form-group div {
	border: 1px solid;
	border-radius: 3px;
	border-color: rgba(31, 31, 31, 0.25);
	padding-left: 3px;
	padding-right: 3px;
	padding-top: 1px;
	padding-bottom: 1px;
	margin-right: 5px;
	margin-left: 5px;
	min-height: 25px;
}

.logBox {
	font-size: 13px;
}