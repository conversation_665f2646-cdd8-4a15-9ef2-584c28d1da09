import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { HrCrEmp } from 'src/app/all-modules/employees/model/HrCrEmp';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { OnTourService } from 'src/app/all-modules/self-service/service/on-tour.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';
declare const $: any;

@Component({
  selector: 'app-sc-tshirt-list',
  templateUrl: './sc-tshirt-list.component.html',
  styleUrls: ['./sc-tshirt-list.component.css']
})
export class ScTshirtListComponent implements OnInit {

  // cores
  public baseUrl = environment.baseUrl;

  public pipe = new DatePipe("en-US");
  public myFromGroup: FormGroup;


  showdiv: boolean[] = [];

  public editId: any;
  public tempId: any;
  // list
  public listData: any = [];
  public configPgn: any;

  user!: HrCrEmp;

  modalParam: any = {};

  organizationFilter: boolean = false;

  myForm: FormGroup;

  id: any[] = [];
  distributionDate: any[] = [];

  constructor(
    private onTourService: OnTourService,
    private formBuilder: FormBuilder,
    private login: LoginService,
    private spinnerService: NgxSpinnerService,
    private datePipe: DatePipe,
    private toastr: ToastrService,
    private employeeService: EmployeeService,
  ) {
    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };
  }

  ngOnInit(): void {
    this.initializeForm();
    this.getAllData();
  }


  initializeForm() {

    this.myForm = this.formBuilder.group({
      startDate: [""],
      endDate: [""],
      refCode: [""],
      empCode: [""],

    });
  }


  orgFilter() {
    this.organizationFilter = true;
  }

  handleModalData(param: any) {
    this.modalParam = param;
    this.organizationFilter = false;
    this.configPgn.pageNum = 1;
    this.getAllData();
  }

  //---------------- Get All List --------------------

  getAllData() {

    let apiURL = this.baseUrl + "/tShirtMgt/getList";

    let queryParams: any = {};
    const params = this.getSearchParams();
    queryParams = params;

    this.spinnerService.show();

    this.onTourService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response.objectList;
        this.configPgn.totalItem = response.totalItems;
        this.configPgn.totalItems = response.totalItems;
        this.setDisplayLastSequence();

        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }

  // -------------------- Delete Data --------------------

  deleteEnityData(dataId) {
    let apiURL = this.baseUrl + "/tShirtMgt/delete/" + dataId;

    let formData: any = {};


    this.spinnerService.show();
    this.onTourService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {

        if (response.status == true) {
          $("#delete_entity").modal("hide");
          this.toastr.success("Successfully item is deleted", "Success");
          this.getAllData();
          this.spinnerService.hide();

        }

      },
      (error) => {
        $("#delete_entity").modal("hide");
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  // =================== UPDATE DISTRIBUTION STATUS =====================

  clickToggole(index, event) {
    this.showdiv[index] = event;
  }

  setDistributionDate(index, Date: any) {

    this.distributionDate[index] = this.pipe.transform(Date, "yyyy-MM-dd");
  }

  updateDistribution(index, id) {

    console.log(index, id)

    const apiURL = this.baseUrl + '/tShirtMgt/distributionStatus/' + id;

    this.spinnerService.show();

    let formData: any = {
      distributionDate: this.distributionDate[index],
      isDistributed: true,
    };

    this.employeeService.sendPutRequest(apiURL, formData).subscribe((res: any) => {
      this.toastr.success("Distribution Status Updated Successfully");
      this.spinnerService.hide();
      this.showdiv[index] = false;
      this.getAllData();

    },
      (error) => {
        this.spinnerService.hide();
        this.toastr.error(error?.error?.message || "Something went wrong. Please try again.");
      }
    );

  }


  resetform() {
    this.configPgn.pageNum = 1
    this.myForm.reset();
    this.modalParam = {};
    this.getAllData();
  }


  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }


  getSearchParams(): any {

    let params: any = {};


    params = this.modalParam

    if (this.configPgn.pageNum) {
      params[`pageNum`] = this.configPgn.pageNum - 0;
    }
    if (this.configPgn.pageSize) {
      params[`pageSize`] = this.configPgn.pageSize;
    }


    if (this.myForm.get("empCode").value) {
      params["empCodes"] = this.myForm.get("empCode").value;
    }

    if (this.myForm.get("refCode").value) {
      params["refCode"] = this.myForm.get("refCode").value;
    }

    if (this.myForm.get("startDate").value) {
      params["startDate"] = this.datePipe.transform(this.myForm.get("startDate").value, "yyyy-MM-dd");
    }

    if (this.myForm.get("endDate").value) {
      params["endDate"] = this.datePipe.transform(this.myForm.get("endDate").value, "yyyy-MM-dd");
    }


    return params;

  }

  // pagination handling methods start -----------------------------------------------------------------------
  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }

  handlePageChange(event: number) {

    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this.getAllData();
  }
  handlePageSizeChange(event: any): void {

    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this.getAllData();
  }
  // pagination handling methods end -------------------------------------------------------------------------
}
