<!-- Page Content -->
<div class="content container-fluid">

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Sales Consultant
                                T-Shirt</b></span></li>
                </ul>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Search Filter -->

    <div class="card mb-2" style="background-color:transparent;">
        <div class="card-body p-3">

            <form [formGroup]="myForm">
                <div class="row">

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Employee ID(s)</label>
                            <input class="form-control" formControlName="empCode" type="text"
                                placeholder="Example: 51567,54546">

                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Ref. Code(s)</label>
                            <input class="form-control" formControlName="refCode" type="text">

                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>T-Shirt For From Date</label>

                            <div class="cal-icon">
                                <input class="form-control datetimepicker" formControlName="startDate" bsDatepicker
                                    type="text" placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2">
                        <div class="form-group">
                            <label>T-Shirt For To Date</label>
                            <div class="cal-icon">
                                <input class="form-control datetimepicker" formControlName="endDate" bsDatepicker
                                    type="text" placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }">
                            </div>

                        </div>
                    </div>



                    <div class="col-md-4 mt-4">
                        <button class="btn btn-success btn-ripple" type="submit" (click)="getAllData()">
                            <i class="fa fa-search" aria-hidden="true"></i> Search
                        </button> &nbsp;
                        <button class="btn btn-danger btn-ripple" (click)="resetform()">
                            <i class="fa fa-eraser" aria-hidden="true"></i> Clear

                        </button> &nbsp;

                        <a class="btn btn-primary btn-ripple" (click)="orgFilter()"><i class="fa fa-filter"></i>
                            Organization</a>
                    </div>

                </div>
            </form>

        </div>
    </div>
    <!-- /Search Filter -->


    <!-- /Page Content -->
    <div class="row">
        <div class="col-md-12">

            <div class="card customCard">

                <div class="card-header">
                    <div class="card-tools">
                        <a routerLink="/marketing-and-sales/Tshirt/sales-consultant/create"
                            class="btn btn-outline-primary"><i class="fa fa-plus"></i> New &nbsp;&nbsp;&nbsp;</a>
                    </div>
                </div>

                <div class="card-body">

                    <div class="table-responsive">

                        <div class="d-flex justify-content-start pb-1">
                            <div class="pgn-displayDataInfo">
                                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                                    configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                                    {{configPgn.totalItem}} ) entries</span>
                            </div>
                        </div>

                        <table id="genListTable" class="table table-striped custom-table">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Ref Code</th>
                                    <th>Employee</th>
                                    <th>Approx. Distribution</th>
                                    <th>Description</th>
                                    <th>Create Date</th>
                                    <th>Is Distributed</th>
                                    <th>Distributed</th>
                                    <th>Approval Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let thisObj of listData | paginate : configPgn; let i = index"
                                    [class.active]="i == currentIndex">

                                    <td>{{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) )
                                        }}</td>
                                    <td>{{thisObj?.code}}</td>
                                    <td> {{thisObj?.proposedByName}} ({{thisObj?.proposedByCode}})</td>
                                    <td>{{thisObj?.tShirtFor | date}}</td>

                                    <td>{{thisObj?.description}}</td>
                                    <td>{{thisObj?.createDate | date}}</td>
                                    <td>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input"
                                                id="customSwitch{{thisObj.id}}" [checked]="thisObj?.isDistributed"
                                                (change)="clickToggole(i , $event.target.checked)"
                                                [disabled]="thisObj?.distributionDate || thisObj?.approvalStatus !== 'Approved'">
                                            <label class="custom-control-label"
                                                for="customSwitch{{thisObj.id}}"></label>
                                        </div>
                                    </td>
                                    <td>
                                        <span *ngIf="thisObj?.distributionDate">
                                            {{thisObj?.distributionDate | date}}
                                        </span>

                                        <span *ngIf="showdiv[i] && !thisObj?.distributionDate">

                                            <input id="td" class="form-control datetimepicker" bsDatepicker type="text"
                                                placeholder="DD-MM-YYYY"
                                                [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }"
                                                (bsValueChange)="setDistributionDate(i,$event)">

                                        </span>
                                    </td>
                                    <td>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Submitted' || thisObj?.approvalStatus === 'SUBMITTED'">
                                            <span class="badge badge-info"> <b> {{thisObj?.approvalStatus}}
                                                </b></span>
                                        </span>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Recommended' || thisObj?.approvalStatus === 'RECOMMENDED'">
                                            <span class="badge badge-warning">
                                                <b> {{thisObj?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Rejected' || thisObj?.approvalStatus === 'REJECTED'">
                                            <span class="badge badge-danger">
                                                <b> {{thisObj?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Approved' || thisObj?.approvalStatus === 'APPROVED'">
                                            <span class="badge badge-success">
                                                <b> {{thisObj?.approvalStatus}} </b></span>
                                        </span>
                                    </td>


                                    <td>
                                        <a class="btn btn-sm btn-primary"
                                            routerLink="/marketing-and-sales/Tshirt/sales-consultant/view/{{thisObj.id}}"
                                            target="_blank"><i class="fa fa-eye m-r-5" title="View"></i></a>
                                        &nbsp;
                                        <a [ngClass]="{'disabled': (thisObj?.approvalStatus === 'Approved' || thisObj?.approvalStatus === 'Rejected' || thisObj?.approvalStatus === 'Recommended')}"
                                            class="btn btn-sm btn-info" routerLink="./edit/{{thisObj.id}}"
                                            target="_blank"><i class="fa fa-pencil m-r-5"></i></a>&nbsp;&nbsp;
                                        <!-- <a class="btn btn-sm btn-danger" (click)="deleteEnityData(thisObj.id)"><i class="fa fa-trash-o m-r-5"></i></a>&nbsp;&nbsp; -->
                                        <a [ngClass]="{'disabled': (thisObj?.approvalStatus === 'Approved' || thisObj?.approvalStatus === 'Rejected' || thisObj?.approvalStatus === 'Recommended')}"
                                            class="btn btn-sm btn-danger" data-toggle="modal"
                                            data-target="#delete_entity" (click)="tempId = thisObj.id">
                                            <i class="fa fa-trash-o m-r-5"></i>
                                        </a>&nbsp;&nbsp;

                                        <a *ngIf="thisObj.fileUrl" class="btn btn-sm btn-primary" title="View Document"
                                            (click)="redirectToImage(thisObj.fileUrl)">
                                            <i class="fa fa-file-text"></i></a> &nbsp;&nbsp;

                                        <button *ngIf="showdiv[i] && !thisObj?.isDistributed"
                                            class="btn btn-primary btn-sm" (click)="updateDistribution(i , thisObj?.id)"
                                            [disabled]="!distributionDate[i]">
                                            Distributed</button>
                                    </td>

                                </tr>

                                <tr *ngIf="listData.length === 0">
                                    <td colspan="13">
                                        <h5 style="text-align: center;">No data found</h5>
                                    </td>
                                </tr>
                            </tbody>
                        </table>


                        <div class="d-flex justify-content-end ">

                            <div class="">
                                Items per Page
                                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption">
                                    <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                                        {{ size }}
                                    </option>
                                </select>
                            </div>

                            <div class="pgn-pageSliceCt">
                                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                                    (pageChange)="handlePageChange($event)">
                                </pagination-controls>
                            </div>

                        </div>

                    </div>

                </div>
            </div>

        </div>
    </div>


    <app-org-search-modal-param-based *ngIf="organizationFilter"
        (closeModal)="handleModalData($event)"></app-org-search-modal-param-based>

</div>


<!-- /Page Content -->


<!-- Delete Modal -->
<div class="modal custom-modal fade" id="delete_entity" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="form-header">
                    <h3>Delete Item</h3>
                    <p>Are you sure want to delete?</p>
                </div>
                <div class="modal-btn delete-action">
                    <div class="row">
                        <div class="col-6">
                            <a class="btn btn-primary continue-btn" (click)="deleteEnityData(tempId)">Delete</a>
                        </div>
                        <div class="col-6">
                            <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- /Delete Modal -->



<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>