import { Component, HostListener, NgZone, OnInit } from '@angular/core';

@Component({
  selector: 'app-marketing-and-sales',
  templateUrl: './marketing-and-sales.component.html',
  styleUrls: ['./marketing-and-sales.component.css']
})

@HostListener('window: resize', ['$event'])

export class MarketingAndSalesComponent implements OnInit {

  public innerHeight: any;
  getScreenHeight() {
    this.innerHeight = window.innerHeight + 'px';
  }

  constructor(private ngZone: NgZone) {
    window.onresize = (e) => {
      this.ngZone.run(() => {
        this.innerHeight = window.innerHeight + 'px';
      });
    };
    this.getScreenHeight();
  }

  ngOnInit() {
  }

  onResize(event) {
    this.innerHeight = event.target.innerHeight + 'px';
  }

}
