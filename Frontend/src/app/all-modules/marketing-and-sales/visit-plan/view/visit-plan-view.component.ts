import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { ApprovalService } from 'src/app/all-modules/approval/service/approval.service';
import { HrCrEmp } from 'src/app/all-modules/employees/model/HrCrEmp';
import { OnTourService } from 'src/app/all-modules/self-service/service/on-tour.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-visit-plan-view',
  templateUrl: './visit-plan-view.component.html',
  styleUrls: ['./visit-plan-view.component.css']
})
export class VisitPlanViewComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public mobileBaseUrl = "https://smart-hrms.waltonbd.com:8443/smart_hrms_api";
  public webBaseUrl = "https://smart-hrms.waltonbd.com/smart_hrms_api";
  public myData: any = {};
  appliedHrCrEmpId: any;

  listData: any = [];
  listData2: any = [];

  user!: HrCrEmp;

  public myForm: FormGroup;
  public tourForm: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private spinnerService: NgxSpinnerService,
    private onTourService: OnTourService,
    private approvalService: ApprovalService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private router: Router,
    private datePipe: DatePipe,
  ) { }


  ngOnInit(): void {
    this.initializeForm();
    this.getFormData();


  }
  initializeForm() {

    this.myForm = this.formBuilder.group({
      id: [""],
      approvalStepAction: ["", Validators.required],
      remarks: [""],

    });

  }

  //----------------------- Get Application Data -----------------

  getFormData() {

    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/SalesVisitPlan/get/" + id;

    let queryParams: any = {};


    this.spinnerService.show();
    this.onTourService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.myData = response;
        this.appliedHrCrEmpId = this.myData?.empId;

        this.spinnerService.hide();

        if (this.myData?.isFinalSubmit) {
          if (response.approvalProcess != null) {
            this.getSelfListData();
            this.getApprovalStepAction();
          }

        }

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }

  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }


  // --------------------Approval Details ---------------------

  getSelfListData() {

    let id = this.route.snapshot.params.id;

    this.spinnerService.show();
    let apiURL = this.baseUrl + "/approvalProcTnxHtry/getSelfApprovalProcTnxList/" + id;

    let queryParams: any = {};
    const params = this.getUserQueryParams();
    queryParams = params;


    this.approvalService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response;
        this.spinnerService.hide();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }

  //======================= CORPORATE SALES EVENT APPROVAL ========================

  corpEventAprv(id) {

    let apiURL = this.baseUrl + "/SalesVisitPlan/updateCorporateApproval/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;

    let formData: any = {};
    formData.approvalStatus = 'Approved';
    this.spinnerService.show();
    this.approvalService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Event Status Updated Successfully");
        this.getFormData();
        this.spinnerService.hide();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }

  corpEventRej(id) {
    let apiURL = this.baseUrl + "/SalesVisitPlan/updateCorporateApproval/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;

    let formData: any = {};
    formData.approvalStatus = 'Rejected';
    this.spinnerService.show();
    this.approvalService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Event Status Updated Successfully");
        this.getFormData();
        this.spinnerService.hide();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }



  //======================= PLAZA SALES EVENT APPROVAL ========================

  plazaEventAprv(id) {

    let apiURL = this.baseUrl + "/SalesVisitPlan/updatePlazaApproval/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;

    let formData: any = {};
    formData.approvalStatus = 'Approved';
    this.spinnerService.show();
    this.approvalService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Event Status Updated Successfully");
        this.getFormData();
        this.spinnerService.hide();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }

  plazaEventRej(id) {
    let apiURL = this.baseUrl + "/SalesVisitPlan/updatePlazaApproval/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;

    let formData: any = {};
    formData.approvalStatus = 'Rejected';
    this.spinnerService.show();
    this.approvalService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Event Status Updated Successfully");
        this.getFormData();
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  //=========================== GET APPROVAL STEP ACTION =====================

  getApprovalStepAction() {
    let id = this.route.snapshot.params.id;


    let apiURL = this.baseUrl + "/approvalStepAction/getApprovalStepAction/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};

    queryParams = params;


    this.approvalService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData2 = response;
        this.spinnerService.hide();

      },
      (error) => {
        this.spinnerService.hide();

      }
    );
  }


  handleImageError(event: Event, file) {
    const target = event.target as HTMLImageElement;
    // Set a default image or handle the error as needed
    target.src = this.webBaseUrl + file; // Provide the path to your default image
  }


  // ======================= TAKE APPROVAL ACTION ====================

  tackAction() {
    if (this.myForm.invalid) {
      return;
    }
    let id = this.route.snapshot.params.id;
    let obj = Object.assign(this.myForm.value, {
      referenceId: id,
      referenceEntity: this.myData?.approvalProcess?.code + "/" + this.myData?.approvalStep?.thisApprovalNode + "/" + this.myData?.approvalStep?.nextApprovalNode + "/" + this.myData?.empId,
      approvalStepAction: this.get.value ? { id: this.get.value } : null,
    });

    this.spinnerService.show();
    let apiURL = this.baseUrl + "/approvalProcTnxHtry/edit";
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;

    let formData: any = {};
    formData = obj

    this.approvalService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.listData = [];
        this.listData2 = [];
        this.resetActionForm();
        this.spinnerService.hide();
        this.toastr.success("Action Taken Successfully");
        this.getFormData();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  resetActionForm() {

    this.myForm.reset();
  }


  redirectToMap(lat, lng, event, time) {

    if (!lat || !lng) {
      this.toastr.warning("No Location Found for " + event + " Event at " + time)
      return;
    }

    window.open("https://www.google.com/maps?q=" + lat + "," + lng, '_blank');
  }


  private getUserQueryParams(): any {

    let params: any = {};
    // push other attributes

    params[`approvalProcess`] = this.myData?.approvalProcess?.code;

    params[`nextApprovalNode`] = this.myData?.approvalStep?.nextApprovalNode;

    params[`thisApprovalNode`] = this.myData?.approvalStep?.thisApprovalNode;

    params[`appliedHrCrEmpId`] = this.appliedHrCrEmpId;

    return params;
  }


  get get() {
    return this.myForm.get("approvalStepAction");
  }

}
