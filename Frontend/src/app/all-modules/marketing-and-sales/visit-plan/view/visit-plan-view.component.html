<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Visit Plan</b></span></li>
                    <li class="breadcrumb-item active">Show</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/marketing-and-sales/visit-plan"><i class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">

                    <div class="row">
                        <div class="col-md-12">


                            <table class="summaryTable col-md-12">
                                <tr>
                                    <td><b>Reference Code</b></td>
                                    <td>{{myData?.code}}</td>
                                    <td><b>Employee</b></td>
                                    <td>{{myData?.empName}} ({{myData?.empCode}})
                                    </td>

                                    <td><b>Team Type</b></td>
                                    <td> {{myData?.teamType}}</td>
                                </tr>

                                <tr>
                                    <td><b>Approval Status</b></td>
                                    <td> <span
                                            *ngIf="myData?.approvalStatus === 'Submitted' || myData?.approvalStatus === 'SUBMITTED'">
                                            <span class="text-info"> <b> {{myData?.approvalStatus}}
                                                </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Recommended' || myData?.approvalStatus === 'RECOMMENDED'">
                                            <span class="text-warning">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Rejected' || myData?.approvalStatus === 'REJECTED'">
                                            <span class="text-danger">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Approved' || myData?.approvalStatus === 'APPROVED'">
                                            <span class="text-success">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>

                                        <span *ngIf="myData?.approvalStatus == 'Not Submitted'"
                                            class="text-secondary">Not
                                            Submitted</span>

                                    </td>

                                    <td><b>Visit Date</b></td>
                                    <td>
                                        {{myData?.applyDate | date}}
                                    </td>
                                    <td><b>Remarks</b></td>
                                    <td style="max-width: 300px;">
                                        {{myData?.remarks}}
                                    </td>

                                </tr>

                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row" *ngIf="myData?.teamType == 'PLAZA_DISTRIBUTION'">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">

                    <div class="row">
                        <div class="col-12">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">Visit Details</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>SL</th>
                                            <th>Customer Type</th>
                                            <th>Customer Details</th>
                                            <th>Event Category</th>
                                            <th>Start Time</th>
                                            <th>Check In Time</th>
                                            <th>Description</th>
                                            <th>Event Status</th>
                                            <th
                                                *ngIf="myData?.teamType == 'PLAZA_DISTRIBUTION' && listData2?.length > 0 && (myData?.approvalStep?.thisApprovalNode == 2 || myData?.approvalStep?.nextApprovalNode == 9)">
                                                Event Action</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of myData?.plazaVisitPlanDTOS;let i = index">
                                            <td>
                                                {{ 1 + i}}
                                            </td>
                                            <td>{{thisObj.customerType}}
                                            </td>
                                            <td>{{thisObj.customerNameAddress}}
                                            </td>
                                            <td>{{thisObj.eventCategory}}
                                            </td>
                                            <td>{{thisObj.startTimeTwl}}
                                            </td>
                                            <td>{{thisObj.checkInTime}}
                                            </td>
                                            <td style="max-width: 200px;">
                                                {{thisObj.description?thisObj.description:'-'}}
                                            </td>

                                            <td>
                                                <span *ngIf="thisObj.eventApprovalStatus == 'Approved'"
                                                    class="text-success">
                                                    <b>{{thisObj.eventApprovalStatus}}</b>
                                                </span>

                                                <span *ngIf="thisObj.eventApprovalStatus == 'Rejected'"
                                                    class="text-danger">
                                                    <b>{{thisObj.eventApprovalStatus}}</b>
                                                </span> <span *ngIf="thisObj.eventApprovalStatus"> by
                                                    {{thisObj.eventActionTakerCode}}-{{thisObj.eventActionTakerName}}</span>
                                            </td>

                                            <td
                                                *ngIf="myData?.teamType == 'PLAZA_DISTRIBUTION' && listData2?.length > 0 && (myData?.approvalStep?.thisApprovalNode == 2 || myData?.approvalStep?.nextApprovalNode == 9)">

                                                <!-- EVENT APPROVAL FOR RSM -->

                                                <span>
                                                    <a [ngClass]="{'disabled': (thisObj.eventApprovalStatus == 'Approved')}"
                                                        class="btn btn-sm btn-success" title="Approved"
                                                        (click)="plazaEventAprv(thisObj?.id)"><i
                                                            class="fa fa-check mr-1"></i>
                                                    </a> &nbsp;
                                                    <a [ngClass]="{'disabled': (thisObj.eventApprovalStatus == 'Rejected')}"
                                                        class="btn btn-sm btn-danger" title="Rejected"
                                                        (click)="plazaEventRej(thisObj?.id)"><i
                                                            class="fa fa-times mr-1"></i>
                                                    </a>
                                                </span>

                                            </td>
                                            <td>

                                                <a class="btn btn-sm btn-info"
                                                    (click)="redirectToMap(thisObj?.lat , thisObj?.lng , thisObj.eventCategory , thisObj.startTimeTwl)"
                                                    title="View Location on Google Map"><i
                                                        class="fa fa-map-marker mr-1"></i>
                                                </a> &nbsp;

                                                <span style="cursor: pointer;">
                                                    <img class="img-avatar text-center" alt=""
                                                        style="height: 30px; width: 30px;border: 1px solid #25B6B2;"
                                                        src="{{ mobileBaseUrl + thisObj.selfieImage}}"
                                                        (error)="handleImageError($event , thisObj.selfieImage)">
                                                </span>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="row" *ngIf="myData?.teamType == 'CORPORATE_SALES'">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">

                    <div class="row">
                        <div class="col-12">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">Visit Details</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>SL</th>
                                            <th>Customer Type</th>
                                            <th>Customer Details</th>
                                            <th>Contact Person Details</th>
                                            <th>Start Time</th>
                                            <th>Check In Time</th>
                                            <th>Description</th>
                                            <th>Event Status</th>
                                            <th
                                                *ngIf="myData?.teamType == 'CORPORATE_SALES' && listData2?.length > 0 && (myData?.approvalStep?.thisApprovalNode == 5 ||  myData?.approvalStep?.thisApprovalNode == 15)">
                                                Event Action</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of myData?.corporateVisitPlanDTOS;let i = index">
                                            <td>
                                                {{ 1 + i}}
                                            </td>
                                            <td>{{thisObj.customerType}}
                                            </td>
                                            <td>{{thisObj.customerNameAddress}}
                                            </td>
                                            <td>{{thisObj.contractPerson}}
                                            </td>
                                            <td>{{thisObj.startTimeTwl}}
                                            </td>
                                            <td>{{thisObj.checkInTime}}
                                            </td>
                                            <td style="max-width: 200px;">
                                                {{thisObj.description?thisObj.description:'-'}}
                                            </td>


                                            <td>
                                                <span *ngIf="thisObj.eventApprovalStatus == 'Approved'"
                                                    class="text-success">
                                                    <b>{{thisObj.eventApprovalStatus}}</b>
                                                </span>

                                                <span *ngIf="thisObj.eventApprovalStatus == 'Rejected'"
                                                    class="text-danger">
                                                    <b>{{thisObj.eventApprovalStatus}}</b>
                                                </span> <span *ngIf="thisObj.eventApprovalStatus"> by
                                                    {{thisObj.eventActionTakerCode}}-{{thisObj.eventActionTakerName}}</span>
                                            </td>

                                            <td
                                                *ngIf="myData?.teamType == 'CORPORATE_SALES' && listData2?.length > 0 && (myData?.approvalStep?.thisApprovalNode == 5 ||  myData?.approvalStep?.thisApprovalNode == 15)">
                                                <!-- EVENT APPROVAL FOR COORDINATOR -->

                                                <span>
                                                    <a [ngClass]="{'disabled': (thisObj.eventApprovalStatus == 'Approved')}"
                                                        class="btn btn-sm btn-success" title="Approved"
                                                        (click)="corpEventAprv(thisObj?.id)"><i
                                                            class="fa fa-check mr-1"></i>
                                                    </a> &nbsp;
                                                    <a [ngClass]="{'disabled': (thisObj.eventApprovalStatus == 'Rejected')}"
                                                        class="btn btn-sm btn-danger" title="Rejected"
                                                        (click)="corpEventRej(thisObj?.id)"><i
                                                            class="fa fa-times mr-1"></i>
                                                    </a>
                                                </span>

                                            </td>

                                            <td>

                                                <a class="btn btn-sm btn-info"
                                                    (click)="redirectToMap(thisObj?.lat , thisObj?.lng , thisObj.eventCategory , thisObj.startTimeTwl)"
                                                    title="View Location on Google Map"><i
                                                        class="fa fa-map-marker mr-1"></i>
                                                </a> &nbsp;

                                                <span style="cursor: pointer;">
                                                    <img class="img-avatar text-center" alt=""
                                                        style="height: 30px; width: 30px;border: 1px solid #25B6B2;"
                                                        src="{{ mobileBaseUrl + thisObj.selfieImage}}"
                                                        (error)="handleImageError($event , thisObj.selfieImage)">
                                                </span>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <div class="row">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-body">

                    <div class="row">
                        <div [ngClass]="{'col-md-12': listData2?.length === 0, 'col-md-8': listData2?.length !== 0 }">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">Approval Status</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>S/L</th>
                                            <th>Approval Step</th>
                                            <th>Approver</th>
                                            <th>Sign By</th>
                                            <th>Designation</th>
                                            <th>Action</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of listData;let i = index">
                                            <td>
                                                {{ 1 + i}}
                                            </td>
                                            <td>{{thisObj.approvalStep.approvalGroupName?thisObj.approvalStep.approvalGroupName:'-'}}
                                            </td>
                                            <td>
                                                {{ thisObj?.approvalStepApproverEmp?.loginCode}} - {{
                                                thisObj?.approvalStepApproverEmp?.displayName}}
                                            </td>
                                            <td>
                                                {{thisObj.actionTaker?thisObj.actionTaker:'-'}}
                                            </td>
                                            <td>{{thisObj?.designation}}
                                            </td>
                                            <td>{{thisObj.actionStatus?thisObj.actionStatus:'-'}}
                                            </td>
                                            <td>
                                                {{thisObj.actionStatus?thisObj.updateDateTime.substr(0,10) :'-'}}
                                            </td>
                                            <td>
                                                {{thisObj.actionStatus?thisObj.updateDateTime.substr(11) :'-'}}</td>

                                            <td>{{thisObj.remarks?thisObj.remarks:'-'}}</td>

                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>


                        <div class="col-4" *ngIf="listData2?.length > 0">
                            <form novalidate (ngSubmit)="tackAction()" [formGroup]="myForm">
                                <fieldset class="row fieldsetBorder logBox ">
                                    <legend class="bg-warning">Take Action</legend>


                                    <label class="col-form-label col-md-3">Status</label>
                                    <div class="col-md-8">
                                        <select class="select form-control" formControlName="approvalStepAction">
                                            <option value="">Select Action</option>
                                            <option *ngFor="let data of listData2" [ngValue]='data.id'>
                                                {{data.activityStatusTitle}}
                                            </option>
                                        </select>

                                    </div>
                                    <br><br>

                                    <label class="col-form-label col-md-3">Remarks</label>
                                    <div class="col-md-8">
                                        <textarea formControlName="remarks" class="form-control mb-3"></textarea>

                                    </div>


                                    <div class="col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary btn-ripple mb-2 ">
                                            &nbsp; Save &nbsp;
                                        </button>
                                    </div>

                                </fieldset>
                            </form>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>


</div>
<!-- /Page Content -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>