import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-visit-plan-create',
  templateUrl: './visit-plan-create.component.html',
  styleUrls: ['./visit-plan-create.component.css']
})
export class VisitPlanCreateComponent implements OnInit {


  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  public formDataDoc: FormGroup;
  public isSubmitted = false;

  // for multi select
  public configDDL: any;
  public configPgn: any;
  public user: any;

  documentsSave = false;
  imgSrc: any;

  maxNumOfCharRmrks = 1000;
  numberOfCharRmrks = 0;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService,
    private employeeService: EmployeeService,
  ) {
    this.configPgn = {
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      totalItem: 50,
      pngDiplayLastSeq: 10,
      entityName: "",
    };

    this._initConfigDDL();
    this._customInitLoadData();
  }

  ngOnInit(): void {
    this._initForm();
  }

  _initForm() {
    this.myForm = this.formBuilder.group({
      emp: [null, [Validators.required]],
      applyDate: ["", [Validators.required]],
      teamType: ["", [Validators.required]],
      remarks: [""],

    });
  }

  // Textarea field character Count

  remarksCharCount(event: any): void {
    this.numberOfCharRmrks = event.target.value.length;

    if (this.numberOfCharRmrks > this.maxNumOfCharRmrks) {
      event.target.value = event.target.value.slice(0, this.maxNumOfCharRmrks);
      this.numberOfCharRmrks = this.maxNumOfCharRmrks;
    }
  }


  // --------------------- Submit Form ----------------------

  myFormSubmit() {

    const apiURL = this.baseUrl + '/SalesVisitPlan/save';

    if (this.myForm.invalid) {
      return;
    }

    let formData = new FormData();

    formData.append("applyDate", this.commonService.format_Date_Y_M_D(this.myForm.get('applyDate').value));
    formData.append("teamType", this.myForm.get("teamType").value);
    formData.append("remarks", this.myForm.get("remarks").value);
    formData.append("emp", this.myForm.get("emp").value);

    this.spinnerService.show();
    this.employeeService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Visit Plan Created Successfully");
        this.spinnerService.hide();
        this.router.navigate(['/marketing-and-sales/visit-plan']);
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }

  get f() { return this.myForm.controls; }

  resetFormValues() {
    this.myForm.reset();
  }



  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmpSpec";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
          this.configDDL.listData2 = this.configDDL.listData2.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
          this.configDDL.listData2 = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      listData2: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {


    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------


}
