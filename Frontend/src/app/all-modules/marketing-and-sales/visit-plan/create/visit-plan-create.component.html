<!-- Page Content -->
<div class="content container-fluid">

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Visit Plan</b></span></li>
                    <li class="breadcrumb-item active">Create</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/marketing-and-sales/visit-plan"><i class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">
                <!-- <div class="card-header"> -->
                <!-- <h4 class="card-title mb-0">Payroll Item Value</h4> -->
                <!-- </div>  -->
                <div class="card-body">
                    <form novalidate (ngSubmit)="myFormSubmit()" [formGroup]="myForm">


                        <div class="row">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Employee</label>

                                    <ng-select [items]="configDDL.listData" formControlName="emp"
                                        placeholder="Select employee" bindLabel="ddlDescription" bindValue="ddlCode"
                                        [searchable]="true" [clearable]="true" [virtualScroll]="true"
                                        [clearOnBackspace]="true" (search)="searchDDL($event)"
                                        (scrollToEnd)="scrollToEndDDL()" (clear)="clearDDL()"
                                        (click)="initSysParamsDDL($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes')"
                                        ddlActiveFieldName="ddlDescription" class="custom-ng-select">
                                    </ng-select>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Team Type</label>
                                    <select class="form-control" formControlName="teamType">
                                        <option value="">Select Team</option>
                                        <option value="plaza_distribution">Plaza & Distribution Channel (Computer)
                                        </option>
                                        <option value="corporate_sales">Corporate Sales Team (Computer)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Visit Date</label>

                                    <div class="cal-icon">
                                        <input class="form-control datetimepicker" bsDatepicker type="text"
                                            placeholder="DD-MM-YYYY" formControlName="applyDate"
                                            [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } ">
                                    </div>



                                </div>
                            </div>


                            <div class="col-md-3">

                                <div class="form-group">
                                    <label>Remarks</label>
                                    <span class="float-right">
                                        {{ numberOfCharRmrks }} / {{maxNumOfCharRmrks}}
                                    </span>
                                    <textarea type="text" class="form-control" rows="1" formControlName="remarks"
                                        (keyup)="remarksCharCount($event)"></textarea>

                                </div>

                            </div>

                        </div>


                        <div class="text-right">

                            <button type="button" class="btn btn-secondary btn-ripple" (click)="resetFormValues()">
                                <i class="fa fa-undo" aria-hidden="true"></i> Reset
                            </button>
                            &nbsp; &nbsp;
                            <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!myForm.valid">
                                <i class="fa fa-check" aria-hidden="true"></i> Submit &nbsp;&nbsp;&nbsp;
                            </button>
                        </div>


                    </form>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /Page Content -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>