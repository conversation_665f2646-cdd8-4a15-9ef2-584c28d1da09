import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormArray, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { ApprovalService } from 'src/app/all-modules/approval/service/approval.service';
import { HrCrEmp } from 'src/app/all-modules/employees/model/HrCrEmp';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';
declare const $: any;


@Component({
  selector: 'app-visit-plan-edit',
  templateUrl: './visit-plan-edit.component.html',
  styleUrls: ['./visit-plan-edit.component.css']
})
export class VisitPlanEditComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public mobileBaseUrl = "https://smart-hrms.waltonbd.com:8443/smart_hrms_api";
  public myData: any = {};
  appliedHrCrEmpId: any;
  public tempId: any;
  public tempIndx: any;
  public plazaVisitPlanFrom: FormGroup;
  public corporateVisitPlanForm: FormGroup;

  listData: any = [];
  listData2: any = [];
  oldCustomerList: any = [];
  oldCustomerListPlaza: any = [];
  imgSrc: any;
  user!: HrCrEmp;

  public myForm: FormGroup;
  public tourForm: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private approvalService: ApprovalService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private router: Router,
    private datePipe: DatePipe,
  ) { }


  ngOnInit(): void {
    this.getFormData();

    this._initplazaVisitPlanFrom();

    this.plazaVisitPlanFrom = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initPlazaRows()])
    });

    this.corporateVisitPlanForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initCorporateRows()])
    });


  }


  // ========================== INIT PLAZA FORM ========================

  _initplazaVisitPlanFrom() {
    this.plazaVisitPlanFrom = this.formBuilder.group({
      id: [""],
      slNo: [""],
      customerNameAddress: [""],
      customerType: [""],
      eventCategory: [""],
      startTime: [""],
      lat: [""],
      lng: [""],
      image: [""],
      salesVisitId: [""],
      description: [""],

    });

  }

  //=================== INIT CORPORATE FORM ==========================

  _initcorporateVisitPlanForm() {
    this.corporateVisitPlanForm = this.formBuilder.group({
      id: [""],
      slNo: [""],
      customerNameAddress: [""],
      customerType: [""],
      contractPerson: [""],
      startTime: [""],
      lat: [""],
      lng: [""],
      image: [""],
      salesVisitId: [""],
      description: [""],

    });

  }


  //----------------------- Get Old Customer List Data -----------------

  getOldCustomerList(empCode) {

    let apiURL = "https://ossapi.waltonbd.com:8008/api/v1/dms/user-kpi-calculation/" + empCode + "/";

    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.oldCustomerList = response;
        this.oldCustomerListPlaza = [...response];
        this.oldCustomerListPlaza.unshift({
          witpCode: '',
          name: 'Walton Plaza',
          sourceType: '',
          createdAt: '',
          username: ''
        });

        console.log(this.oldCustomerList.length + ' oldCustomerList');
        console.log(this.oldCustomerListPlaza.length + ' oldCustomerListPlaza')

        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }

  //----------------------- Get Application Data -----------------

  getFormData() {

    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/SalesVisitPlan/get/" + id;

    let queryParams: any = {};


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.myData = response;

        if (response?.plazaVisitPlanDTOS?.length > 0) {
          this.patchPlazaRow(response?.plazaVisitPlanDTOS);
        }

        if (response?.corporateVisitPlanDTOS?.length > 0) {
          this.patchCorporateRow(response?.corporateVisitPlanDTOS);
        }
        this.getOldCustomerList(this.myData?.empCode);
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  // ========================== PLAZA VISIT DETAILS ===========================

  get plazaVisitPlanFromArr() {
    return this.plazaVisitPlanFrom.get("Rows") as FormArray;

  }

  initPlazaRows() {
    return this.formBuilder.group({
      slNo: [""],
      customerNameAddress: [null, Validators.required],
      customerType: ["", Validators.required],
      eventCategory: ["", Validators.required],
      startTime: ["", Validators.required],
      lat: ["", Validators.required],
      lng: ["", Validators.required],
      image: ["", Validators.required],
      salesVisitId: [""],
      description: [""],


    });
  }

  patchPlazaRow(data) {
    this.plazaVisitPlanFromArr.clear();
    this.plazaVisitPlanFromArr.removeAt(0);
    for (let item of data) {
      this.plazaVisitPlanFromArr.push(this.patchPlazaData(item))
    }

  }

  patchPlazaData(data) {
    return this.formBuilder.group({
      id: data.id,
      slNo: data.slNo,
      description: data.description,
      eventCategory: data.eventCategory,
      customerNameAddress: data.customerNameAddress,
      customerType: data.customerType,
      lat: data.lat,
      lng: data.lng,
      startTime: this.getFirstPartOfTimeString(data.startTime[0] + ":" + data.startTime[1]) + ":" + this.getSecondPartOfTimeString(data.startTime[0] + ":" + data.startTime[1]),
      salesVisitId: data.salesVisitId,

    });
  }


  addPlazaRow() {
    this.plazaVisitPlanFromArr.push(this.initPlazaRows());
  }

  deletePlazaRow(rowId, index: number) {

    this.tempId = rowId;
    this.tempIndx = index;

    if (!rowId) {
      this.plazaVisitPlanFromArr.removeAt(index);
      $("#delete_entity").modal("hide");
      return;
    }
    else {
      $("#delete_entity").modal("show");
    }

  }



  plazaSelfie(event, i) {

    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      let fileName1 = file.type;
      if ((fileName1 === 'image/jpeg') || (fileName1 === 'image/png')) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onload = (e) => (this.imgSrc = reader.result);
        this.plazaVisitPlanFromArr?.controls[i].get("image").setValue(file);
      }
      else {
        this.toastr.warning("Please choose correct format. (only PNG/JPG is applicable)");
      }
    }
  }

  savePlazaRow(index) {

    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/SalesVisitPlan/addVisitPlanDetail";

    let formData = new FormData();

    formData.append("salesVisitId", id);
    formData.append("eventCategory", this.plazaVisitPlanFromArr?.controls[index].value.eventCategory);
    formData.append("customerNameAddress", this.plazaVisitPlanFromArr?.controls[index].value.customerNameAddress);
    formData.append("customerType", this.plazaVisitPlanFromArr?.controls[index].value.customerType);
    formData.append("description", this.plazaVisitPlanFromArr?.controls[index].value.description);
    if (this.plazaVisitPlanFromArr?.controls[index].value.lat) {
      formData.append("lat", this.plazaVisitPlanFromArr?.controls[index].value.lat);
    }
    if (this.plazaVisitPlanFromArr?.controls[index].value.lng) {
      formData.append("lng", this.plazaVisitPlanFromArr?.controls[index].value.lng);
    }
    if (this.plazaVisitPlanFromArr?.controls[index].value.image) {
      formData.append("image", this.plazaVisitPlanFromArr?.controls[index].value.image);
    }
    formData.append("startTime", this.plazaVisitPlanFromArr?.controls[index].value.startTime);

    this.spinnerService.show();
    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Plan Saved Successfully")
        this.getFormData();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }


  DeletePlazaRowData(rowId, index) {
    let apiURL = this.baseUrl + "/SalesVisitPlan/deleteVisitPlanDetail/" + rowId;

    let formData: any = {};


    this.spinnerService.show();
    this.commonService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {

        if (response.status == true) {
          $("#delete_entity").modal("hide");
          this.toastr.success("Successfully item is deleted", "Success");
          this.getFormData();
          this.spinnerService.hide();

        }

      },
      (error) => {
        $("#delete_entity").modal("hide");
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  // ========================= CORPORATE VISIT PLAN DETAILS ==============================

  get corporateVisitFormArr() {
    return this.corporateVisitPlanForm.get("Rows") as FormArray;

  }


  initCorporateRows() {
    return this.formBuilder.group({
      slNo: [""],
      customerNameAddress: [null, Validators.required],
      customerType: ["", Validators.required],
      contractPerson: ["", Validators.required],
      startTime: ["", Validators.required],
      lat: ["", Validators.required],
      lng: ["", Validators.required],
      image: ["", Validators.required],
      salesVisitId: [""],
      description: [""],


    });
  }



  patchCorporateRow(data) {
    this.corporateVisitFormArr.clear();
    this.corporateVisitFormArr.removeAt(0);
    for (let item of data) {
      this.corporateVisitFormArr.push(this.patchCorporateData(item))
    }

  }

  patchCorporateData(data) {
    return this.formBuilder.group({
      id: data.id,
      slNo: data.slNo,
      description: data.description,
      customerNameAddress: data.customerNameAddress,
      customerType: data.customerType,
      contractPerson: data.contractPerson,
      lat: data.lat,
      lng: data.lng,
      startTime: this.getFirstPartOfTimeString(data.startTime[0] + ":" + data.startTime[1]) + ":" + this.getSecondPartOfTimeString(data.startTime[0] + ":" + data.startTime[1]),
      salesVisitId: data.salesVisitId,

    });
  }

  addCorporateRow() {
    this.corporateVisitFormArr.push(this.initCorporateRows());
  }

  deleteCorporateRow(rowId, index: number) {

    this.tempId = rowId;
    this.tempIndx = index;

    if (!rowId) {
      this.corporateVisitFormArr.removeAt(index);
      $("#delete_entity2").modal("hide");
      return;
    }
    else {
      $("#delete_entity2").modal("show");
    }

  }

  corporateSelfie(event, i) {

    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      let fileName1 = file.type;
      if ((fileName1 === 'image/jpeg') || (fileName1 === 'image/png')) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onload = (e) => (this.imgSrc = reader.result);
        this.corporateVisitFormArr?.controls[i].get("image").setValue(file);
      }
      else {
        this.toastr.warning("Please choose correct format. (only PNG/JPG is applicable)");
      }
    }
  }

  saveCorporateRow(index) {

    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/SalesVisitPlan/addVisitPlanCorporate";

    let formData = new FormData();

    formData.append("salesVisitId", id);
    formData.append("customerNameAddress", this.corporateVisitFormArr?.controls[index].value.customerNameAddress);
    formData.append("customerType", this.corporateVisitFormArr?.controls[index].value.customerType);
    formData.append("contractPerson", this.corporateVisitFormArr?.controls[index].value.contractPerson);
    formData.append("description", this.corporateVisitFormArr?.controls[index].value.description);
    if (this.corporateVisitFormArr?.controls[index].value.lat) {
      formData.append("lat", this.corporateVisitFormArr?.controls[index].value.lat);
    }
    if (this.corporateVisitFormArr?.controls[index].value.lng) {
      formData.append("lng", this.corporateVisitFormArr?.controls[index].value.lng);
    }
    if (this.corporateVisitFormArr?.controls[index].value.image) {
      formData.append("image", this.corporateVisitFormArr?.controls[index].value.image);
    }

    formData.append("startTime", this.corporateVisitFormArr?.controls[index].value.startTime);

    this.spinnerService.show();
    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success("Plan Saved Successfully")
        this.getFormData();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }


  DeleteCorporateRowData(rowId, index) {
    let apiURL = this.baseUrl + "/SalesVisitPlan/deleteVisitPlanCorporate/" + rowId;

    let formData: any = {};

    this.spinnerService.show();
    this.commonService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {

        if (response.status == true) {
          $("#delete_entity2").modal("hide");
          this.toastr.success("Successfully item is deleted", "Success");
          this.getFormData();
          this.spinnerService.hide();

        }

      },
      (error) => {
        $("#delete_entity").modal("hide");
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  // ----------------- First Part of Time ---------------------

  getFirstPartOfTimeString(myString: any) {

    const indexOfColon = myString.indexOf(':');

    if (indexOfColon !== -1) {
      const substring = myString.substring(0, indexOfColon);
      const size = substring.length;
      if (size !== 2) {

        const finalString = "0" + substring;
        return finalString;
      }

      return substring;

    }

  }

  // ----------------- Second Part of Time ---------------------

  getSecondPartOfTimeString(myString: any) {

    const substring = myString.split(":")[1];

    const size = substring.length;
    if (size !== 2) {

      const finalString = "0" + substring;
      return finalString;
    }
    return substring;

  }

}
