<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Visit Plan</b></span></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/marketing-and-sales/visit-plan"><i class="fa fa-share"></i> Back To
                    List</a> &nbsp;
                <a class="btn add-btn" routerLink="/marketing-and-sales/visit-plan/view/{{myData?.id}}"
                    target="_blank"><i class="fa fa-eye"></i> View</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body ">

                    <div class="row">
                        <div class="col-md-12">


                            <table class="summaryTable col-md-12">
                                <tr>
                                    <td><b>Reference Code</b></td>
                                    <td>{{myData?.code}}</td>
                                    <td><b>Employee</b></td>
                                    <td>{{myData?.empName}} ({{myData?.empCode}})
                                    </td>

                                    <td><b>Team Type</b></td>
                                    <td> {{myData?.teamType}}</td>
                                </tr>

                                <tr>
                                    <td><b>Approval Status</b></td>
                                    <td> <span
                                            *ngIf="myData?.approvalStatus === 'Submitted' || myData?.approvalStatus === 'SUBMITTED'">
                                            <span class="text-info"> <b> {{myData?.approvalStatus}}
                                                </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Recommended' || myData?.approvalStatus === 'RECOMMENDED'">
                                            <span class="text-warning">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Rejected' || myData?.approvalStatus === 'REJECTED'">
                                            <span class="text-danger">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="myData?.approvalStatus === 'Approved' || myData?.approvalStatus === 'APPROVED'">
                                            <span class="text-success">
                                                <b> {{myData?.approvalStatus}} </b></span>
                                        </span>

                                        <span *ngIf="myData?.approvalStatus == null" class="text-secondary">Not
                                            Submitted</span>

                                    </td>

                                    <td><b>Visit Date</b></td>
                                    <td>
                                        {{myData?.applyDate | date}}
                                    </td>
                                    <td><b>Remarks</b></td>
                                    <td>
                                        {{myData?.remarks}}
                                    </td>

                                </tr>

                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row" *ngIf="myData?.teamType == 'PLAZA_DISTRIBUTION'">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body">

                    <div class="row">
                        <div class="col-12">
                            <h3 class="card-title mt-2">Visit Plan Details</h3>
                            <hr />
                            <div class="table-responsive col-md-12">
                                <form [formGroup]="plazaVisitPlanFrom">
                                    <table class="table table-striped custom-table datatable">
                                        <thead>
                                            <tr>
                                                <th>Customer Type</th>
                                                <th>Customer Details</th>
                                                <th>Event Category</th>
                                                <th>Start Time</th>
                                                <th>Latitude</th>
                                                <th>Longitude</th>
                                                <th>Selfie</th>
                                                <th>Description</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody formArrayName="Rows">
                                            <tr *ngFor="let itemrow of plazaVisitPlanFromArr.controls; let i=index;let l=last"
                                                [formGroupName]="i">

                                                <td>

                                                    <select class="form-control" formControlName="customerType"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id">
                                                        <option value="">Select Type</option>
                                                        <option value="New">New</option>
                                                        <option value="Old">Old</option>

                                                    </select>

                                                </td>


                                                <td>
                                                    <div
                                                        *ngIf="plazaVisitPlanFromArr?.controls[i].value.customerType == 'New' ">
                                                        <textarea class="form-control"
                                                            formControlName="customerNameAddress" rows="1"
                                                            placeholder="Customer Details"
                                                            [readonly]="plazaVisitPlanFromArr?.controls[i].value.id"></textarea>
                                                    </div>

                                                    <div
                                                        *ngIf="plazaVisitPlanFromArr?.controls[i].value.customerType != 'New' ">
                                                        <ng-select [items]="oldCustomerListPlaza"
                                                            placeholder="Select Customer" bindLabel="name"
                                                            bindValue="name" [searchable]="true"
                                                            [readonly]="plazaVisitPlanFromArr?.controls[i].value.id"
                                                            formControlName="customerNameAddress" [appendTo]="'body'"
                                                            class="custom-ng-select">
                                                        </ng-select>
                                                    </div>

                                                </td>

                                                <td>

                                                    <select class="form-control" formControlName="eventCategory"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id">
                                                        <option value="">Select Category</option>
                                                        <option value="Retail sales Development">Retail sales
                                                            Development</option>
                                                        <option value="Corporate Sales Development">Corporate Sales
                                                            Development</option>
                                                        <option value="IT Fair">IT Fair</option>
                                                        <option value="Campaign/ Sales Offer Development">Campaign/
                                                            Sales Offer Development</option>
                                                        <option value="Dealer Sales Development">Dealer Sales
                                                            Development</option>
                                                    </select>

                                                </td>



                                                <td>
                                                    <input class="form-control" formControlName="startTime"
                                                        class="form-control" type="time"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <input class="form-control" formControlName="lat"
                                                        class="form-control" type="number"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <input class="form-control" formControlName="lng"
                                                        class="form-control" type="number"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <input class="form-control" type="file" name="image"
                                                        (change)="plazaSelfie($event , i)"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <textarea class="form-control" formControlName="description"
                                                        rows="1"
                                                        [readonly]="plazaVisitPlanFromArr?.controls[i].value.id"></textarea>

                                                </td>

                                                <td>
                                                    <!-- <button *ngIf="plazaVisitPlanFrom.controls.Rows.controls.length > 0" title="Delete Row" [disabled]="plazaVisitPlanFromArr?.controls[i].value.id"
                                                        (click)="deletePlazaRow(i)" class="btn btn-info"><i
                                                            class="fa fa-minus"></i></button> &nbsp; -->

                                                    <button *ngIf="plazaVisitPlanFrom.controls.Rows.controls.length > 0"
                                                        [disabled]="plazaVisitPlanFromArr?.controls[i].value.id"
                                                        (click)="savePlazaRow(i)" class="btn btn-primary"><i
                                                            class="fa fa-check" title="Save"></i> Save</button> &nbsp;

                                                    <button *ngIf="plazaVisitPlanFrom.controls.Rows.controls.length > 0"
                                                        class="btn btn-danger"
                                                        (click)="deletePlazaRow(plazaVisitPlanFromArr?.controls[i].value.id,i)"><i
                                                            class="fa fa-trash-o" title="Delete"></i> Delete</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </form>
                                <br>
                                <button type="button" (click)="addPlazaRow()" class="btn btn-info ml-2"><i
                                        class="fa fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="row" *ngIf="myData?.teamType == 'CORPORATE_SALES'">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body">

                    <div class="row">
                        <div class="col-12">
                            <h3 class="card-title mt-2">Visit Plan Details</h3>
                            <hr />
                            <div class="table-responsive col-md-12">
                                <form [formGroup]="corporateVisitPlanForm">
                                    <table class="table table-striped custom-table datatable">
                                        <thead>
                                            <tr>
                                                <th>Customer Type</th>
                                                <th>Customer Details</th>
                                                <th>Contact Person Details</th>
                                                <th>Start Time</th>
                                                <th>Latitude</th>
                                                <th>Longitude</th>
                                                <th>Selfie</th>
                                                <th>Description</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody formArrayName="Rows">
                                            <tr *ngFor="let itemrow of corporateVisitFormArr.controls; let i=index;let l=last"
                                                [formGroupName]="i">

                                                <td>

                                                    <select class="form-control" formControlName="customerType"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id">
                                                        <option value="">Select Type</option>
                                                        <option value="New">New</option>
                                                        <option value="Old">Old</option>

                                                    </select>

                                                </td>


                                                <td>
                                                    <div
                                                        *ngIf="corporateVisitFormArr?.controls[i].value.customerType == 'New' ">
                                                        <textarea class="form-control"
                                                            formControlName="customerNameAddress" rows="1"
                                                            placeholder="Customer Details"
                                                            [readonly]="corporateVisitFormArr?.controls[i].value.id"></textarea>
                                                    </div>

                                                    <div
                                                        *ngIf="corporateVisitFormArr?.controls[i].value.customerType != 'New' ">
                                                        <ng-select [items]="oldCustomerList"
                                                            placeholder="Select Customer" bindLabel="name"
                                                            bindValue="name" [searchable]="true"
                                                            [readonly]="corporateVisitFormArr?.controls[i].value.id"
                                                            formControlName="customerNameAddress" [appendTo]="'body'"
                                                            class="custom-ng-select">
                                                        </ng-select>
                                                    </div>

                                                </td>



                                                <td>
                                                    <textarea class="form-control" formControlName="contractPerson"
                                                        rows="1" placeholder="Minimum Two Person"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id"></textarea>

                                                </td>

                                                <td>
                                                    <input class="form-control" formControlName="startTime"
                                                        class="form-control" type="time"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <input class="form-control" formControlName="lat"
                                                        class="form-control" type="number"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <input class="form-control" formControlName="lng"
                                                        class="form-control" type="number"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <input class="form-control" type="file" name="image"
                                                        (change)="corporateSelfie($event , i)"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id">
                                                </td>

                                                <td>
                                                    <textarea class="form-control" formControlName="description"
                                                        rows="1"
                                                        [readonly]="corporateVisitFormArr?.controls[i].value.id"></textarea>

                                                </td>

                                                <td>

                                                    <button *ngIf="plazaVisitPlanFrom.controls.Rows.controls.length > 0"
                                                        [disabled]="corporateVisitFormArr?.controls[i].value.id"
                                                        (click)="saveCorporateRow(i)" class="btn btn-primary"><i
                                                            class="fa fa-check" title="Save"></i> Save</button> &nbsp;

                                                    <button *ngIf="plazaVisitPlanFrom.controls.Rows.controls.length > 0"
                                                        class="btn btn-danger"
                                                        (click)="deleteCorporateRow(corporateVisitFormArr?.controls[i].value.id,i)"><i
                                                            class="fa fa-trash-o" title="Delete"></i> Delete</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </form>
                                <br>
                                <button type="button" (click)="addCorporateRow()" class="btn btn-info ml-2"><i
                                        class="fa fa-plus"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Delete Modal -->
    <div class="modal custom-modal fade" id="delete_entity" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="form-header">
                        <h3>Delete Item</h3>
                        <p>Are you sure want to delete?</p>
                    </div>
                    <div class="modal-btn delete-action">
                        <div class="row">
                            <div class="col-6">
                                <a class="btn btn-primary continue-btn"
                                    (click)="DeletePlazaRowData(tempId,tempIndx)">Delete</a>
                            </div>
                            <div class="col-6">
                                <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- /Delete Modal -->


    <!-- Delete Modal -->
    <div class="modal custom-modal fade" id="delete_entity2" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="form-header">
                        <h3>Delete Item</h3>
                        <p>Are you sure want to delete?</p>
                    </div>
                    <div class="modal-btn delete-action">
                        <div class="row">
                            <div class="col-6">
                                <a class="btn btn-primary continue-btn"
                                    (click)="DeleteCorporateRowData(tempId,tempIndx)">Delete</a>
                            </div>
                            <div class="col-6">
                                <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- /Delete Modal -->

</div>
<!-- /Page Content -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>