<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Marketing & Sales</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Visit Plan</b></span></li>
                </ul>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Search Filter -->

    <div class="card mb-2" style="background-color:transparent;">
        <div class="card-body p-3">

            <form [formGroup]="myForm">
                <div class="row">

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Employee ID(s)</label>
                            <input class="form-control" formControlName="empCode" type="text"
                                placeholder="Example: 51567,54546">

                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Ref. Code(s)</label>
                            <input class="form-control" formControlName="refCode" type="text">

                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Team Type</label>
                            <select class="form-control" formControlName="teamType">
                                <option value="All">:: All ::</option>
                                <option value="plaza_distribution">Plaza & Distribution Channel (Computer)</option>
                                <option value="corporate_sales">Corporate Sales Team (Computer)</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Visit From Date</label>

                            <div class="cal-icon">
                                <input class="form-control datetimepicker" formControlName="startDate" bsDatepicker
                                    type="text" placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Visit To Date</label>
                            <div class="cal-icon">
                                <input class="form-control datetimepicker" formControlName="endDate" bsDatepicker
                                    type="text" placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }">
                            </div>

                        </div>
                    </div>


                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Approval Status</label>
                            <select formControlName="approvalStatus" class="select form-control">
                                <option value="All">All Status</option>
                                <option value="Approved">Approved</option>
                                <option value="Recommended">Recommended</option>
                                <option value="Rejected">Rejected</option>
                                <option value="Submitted">Submitted</option>
                                <option value="Not Submitted">Not Submitted</option>
                            </select>

                        </div>
                    </div>


                    <div class="col-md-12 mt-4 text-center">
                        <button class="btn btn-success btn-ripple" type="submit" (click)="searchData()">
                            <i class="fa fa-search" aria-hidden="true"></i> Search
                        </button> &nbsp;
                        <button class="btn btn-danger btn-ripple" (click)="resetform()">
                            <i class="fa fa-eraser" aria-hidden="true"></i> Clear

                        </button> &nbsp;

                        <a class="btn btn-primary btn-ripple" (click)="orgFilter()"><i class="fa fa-filter"></i>
                            Organization</a>
                    </div>

                </div>
            </form>

        </div>
    </div>
    <!-- /Search Filter -->


    <!-- /Page Content -->
    <div class="row">
        <div class="col-md-12">

            <div class="card customCard">

                <div class="card-header">
                    <div class="card-tools">
                        <a routerLink="/marketing-and-sales/visit-plan/create" class="btn btn-outline-primary"><i
                                class="fa fa-plus"></i> New &nbsp;&nbsp;&nbsp;</a>
                    </div>
                </div>

                <div class="card-body">

                    <div class="table-responsive">

                        <div class="d-flex justify-content-start pb-1">
                            <div class="pgn-displayDataInfo">
                                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                                    configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                                    {{configPgn.totalItem}} ) entries</span>
                            </div>
                        </div>

                        <table id="genListTable" class="table table-striped custom-table">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Ref Code</th>
                                    <th>Employee</th>
                                    <th>Team Type</th>
                                    <th>Visit Date</th>
                                    <th>Create Date</th>
                                    <th>Remarks</th>
                                    <th>Approval Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let thisObj of listData | paginate : configPgn; let i = index"
                                    [class.active]="i == currentIndex">

                                    <td>{{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) )
                                        }}</td>
                                    <td>{{thisObj?.code}}</td>
                                    <td>{{thisObj?.empName}} ({{thisObj?.empCode}}) </td>
                                    <td>{{thisObj?.teamType}}</td>
                                    <td>{{thisObj?.applyDate | date}}</td>
                                    <td>{{thisObj?.createDate | date}}</td>

                                    <td title="{{thisObj.remarks}}">
                                        <a style="cursor: pointer;">{{(thisObj.remarks | slice:0:30)+'...' }}</a>
                                    </td>
                                    <td>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Submitted' || thisObj?.approvalStatus === 'SUBMITTED'">
                                            <span class="badge badge-info"> <b> {{thisObj?.approvalStatus}}
                                                </b></span>
                                        </span>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Recommended' || thisObj?.approvalStatus === 'RECOMMENDED'">
                                            <span class="badge badge-warning">
                                                <b> {{thisObj?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Rejected' || thisObj?.approvalStatus === 'REJECTED'">
                                            <span class="badge badge-danger">
                                                <b> {{thisObj?.approvalStatus}} </b></span>
                                        </span>
                                        <span
                                            *ngIf="thisObj?.approvalStatus === 'Approved' || thisObj?.approvalStatus === 'APPROVED'">
                                            <span class="badge badge-success">
                                                <b> {{thisObj?.approvalStatus}} </b></span>
                                        </span>
                                        <span *ngIf="thisObj?.approvalStatus == 'Not Submitted'"
                                            class="badge badge-secondary">Not
                                            Submitted</span>
                                    </td>


                                    <td>

                                        <a class="btn btn-sm btn-info" title="Edit"
                                            routerLink="/marketing-and-sales/visit-plan/edit/{{thisObj.id}}"
                                            target="_blank"><i class="fa fa-pencil "></i></a>
                                        &nbsp;

                                        <a class="btn btn-sm btn-primary" title="View"
                                            routerLink="/marketing-and-sales/visit-plan/view/{{thisObj.id}}"
                                            target="_blank"><i class="fa fa-eye"></i></a>
                                        &nbsp;

                                        <a *ngIf="thisObj?.applyDate < today && thisObj?.approvalStatus == 'Not Submitted'"
                                            class="btn btn-sm btn-warning" (click)="finalSubmit(thisObj.id)"
                                            title="Final Submit"><i class="fa fa-check"></i></a>
                                        &nbsp;



                                        <!-- <a [ngClass]="{'disabled': (thisObj?.approvalStatus === 'Approved' || thisObj?.approvalStatus === 'Rejected' || thisObj?.approvalStatus === 'Recommended' || thisObj?.approvalStatus === 'APPROVED' || thisObj?.approvalStatus === 'PARTIAL' || thisObj?.approvalStatus === 'DENIED')}"
                                            class="btn btn-sm btn-danger" data-toggle="modal"
                                            data-target="#delete_entity" (click)="tempId = thisObj.id">
                                            <i class="fa fa-trash-o m-r-5"></i>
                                        </a>&nbsp;&nbsp; -->
                                    </td>

                                </tr>

                                <tr *ngIf="listData.length === 0">
                                    <td colspan="13">
                                        <h5 style="text-align: center;">No data found</h5>
                                    </td>
                                </tr>
                            </tbody>
                        </table>


                        <div class="d-flex justify-content-end ">

                            <div class="">
                                Items per Page
                                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption">
                                    <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                                        {{ size }}
                                    </option>
                                </select>
                            </div>

                            <div class="pgn-pageSliceCt">
                                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                                    (pageChange)="handlePageChange($event)">
                                </pagination-controls>
                            </div>

                        </div>

                    </div>

                </div>
            </div>

        </div>
    </div>


    <app-org-search-modal-param-based *ngIf="organizationFilter"
        (closeModal)="handleModalData($event)"></app-org-search-modal-param-based>

</div>


<!-- /Page Content -->


<!-- Delete Modal -->
<div class="modal custom-modal fade" id="delete_entity" role="dialog">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="form-header">
                    <h3>Delete Item</h3>
                    <p>Are you sure want to delete?</p>
                </div>
                <div class="modal-btn delete-action">
                    <div class="row">
                        <div class="col-6">
                            <a class="btn btn-primary continue-btn" (click)="deleteEnityData(tempId)">Delete</a>
                        </div>
                        <div class="col-6">
                            <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- /Delete Modal -->


<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>