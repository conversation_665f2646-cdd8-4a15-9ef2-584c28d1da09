import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MarketingAndSalesRoutingModule } from './marketing-and-sales-routing.module';
import { MarketingAndSalesComponent } from './marketing-and-sales.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxSpinnerModule } from 'ngx-spinner';
import { PickListModule } from 'primeng/picklist';
import { SharingModule } from 'src/app/sharing/sharing.module';
import { ScTshirtCreateComponent } from './t-shirt-management/sales-consultant/create/sc-tshirt-create.component';
import { ScTshirtListComponent } from './t-shirt-management/sales-consultant/list/sc-tshirt-list.component';
import { ScTshirtViewComponent } from './t-shirt-management/sales-consultant/view/sc-tshirt-view.component';
import { VisitPlanListComponent } from './visit-plan/list/visit-plan-list.component';
import { VisitPlanCreateComponent } from './visit-plan/create/visit-plan-create.component';
import { VisitPlanViewComponent } from './visit-plan/view/visit-plan-view.component';
import { VisitPlanEditComponent } from './visit-plan/edit/visit-plan-edit.component';


@NgModule({
  declarations: [
    MarketingAndSalesComponent,
    ScTshirtCreateComponent,
    ScTshirtListComponent,
    ScTshirtViewComponent,
    VisitPlanListComponent,
    VisitPlanCreateComponent,
    VisitPlanViewComponent,
    VisitPlanEditComponent
  ],
  imports: [
    CommonModule,
    MarketingAndSalesRoutingModule,
    BsDatepickerModule.forRoot(),
    FormsModule,
    SharingModule,
    ReactiveFormsModule,
    PickListModule,
    NgxPaginationModule,
    NgxSpinnerModule,
    NgSelectModule,
    PdfViewerModule,

  ]
})
export class MarketingAndSalesModule { }
