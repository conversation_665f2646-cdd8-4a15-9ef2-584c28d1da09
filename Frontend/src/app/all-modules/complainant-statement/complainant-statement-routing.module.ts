import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ComplainantStatementComponent } from './complainant-statement.component';
import { AgainstDeptSecListComponent } from './components/against-dept-sec/list/against-dept-sec-list.component';
import { AgainstIndividualListComponent } from './components/against-individual/list/against-individual-list.component';
import { AwardProposalListComponent } from './components/award-proposal/list/award-proposal-list.component';
import { AgainstDeptSecCreateComponent } from './components/against-dept-sec/create/against-dept-sec-create.component';
import { AgainstDeptSecEditComponent } from './components/against-dept-sec/edit/against-dept-sec-edit.component';
import { AgainstDeptSecViewComponent } from './components/against-dept-sec/view/against-dept-sec-view.component';
import { AgainstIndividualCreateComponent } from './components/against-individual/create/against-individual-create.component';
import { AgainstIndividualEditComponent } from './components/against-individual/edit/against-individual-edit.component';
import { AgainstIndividualViewComponent } from './components/against-individual/view/against-individual-view.component';
import { AwardProposalCreateComponent } from './components/award-proposal/create/award-proposal-create.component';
import { AwardProposalEditComponent } from './components/award-proposal/edit/award-proposal-edit.component';
import { AwardProposalViewComponent } from './components/award-proposal/view/award-proposal-view.component';
import { AssignInvestigatorComponent } from './components/assign-investigator/assign-investigator.component';
import { ForwardedCasesComponent } from './components/forwarded-cases/forwarded-cases.component';
import { AssignInvestigatorEditComponent } from './components/assign-investigator/edit/assign-investigator-edit.component';
import { ForwardedCasesEditComponent } from './components/forwarded-cases/edit/forwarded-cases-edit.component';
import { InvestigationViewComponent } from './components/investigation-view/investigation-view.component';
import { CompletedInvestigationComponent } from './components/completed-investigation/list/completed-investigation.component';
import { CompletedInvestigationViewComponent } from './components/completed-investigation/view/completed-investigation-view.component';
import { AgainstIndividualApproveComponent } from './components/against-individual/approve/against-individual-approve.component';
import { ForwardedCasesViewComponent } from './components/forwarded-cases/view/forwarded-cases-view.component';
import { AssignInvestigatorViewComponent } from './components/assign-investigator/show/assign-investigator-view.component';

const routes: Routes = [

  {
    path: "",
    component: ComplainantStatementComponent,
    children: [
      {
        path: "against-dept-sec/list",
        component: AgainstDeptSecListComponent
      },
      {
        path: "against-dept-sec/create",
        component: AgainstDeptSecCreateComponent
      },
      {
        path: "against-dept-sec/edit/:id",
        component: AgainstDeptSecEditComponent
      },
      {
        path: "against-dept-sec/view/:id",
        component: AgainstDeptSecViewComponent
      },
      {
        path: "against-individual/list",
        component: AgainstIndividualListComponent
      },
      {
        path: "against-individual/create",
        component: AgainstIndividualCreateComponent
      },
      {
        path: "against-individual/edit/:id",
        component: AgainstIndividualEditComponent
      },
      {
        path: "against-individual/view/:id",
        component: AgainstIndividualViewComponent
      },

      {
        path: "against-individual/approve",
        component: AgainstIndividualApproveComponent
      },

      {
        path: "against-individual/approve/:id",
        component: AgainstIndividualApproveComponent
      },

      {
        path: "against-individual/view2/:code",
        component: AgainstIndividualViewComponent
      },
      {
        path: "award-proposal/list",
        component: AwardProposalListComponent
      },
      {
        path: "award-proposal/create",
        component: AwardProposalCreateComponent
      },
      {
        path: "award-proposal/edit/:id",
        component: AwardProposalEditComponent
      },
      {
        path: "award-proposal/view/:id",
        component: AwardProposalViewComponent
      },

      {
        path: "assign-investigator",
        component: AssignInvestigatorComponent
      },

      {
        path: "assign-investigator/edit/:id",
        component: AssignInvestigatorEditComponent
      },
      {
        path: "assign-investigator/view/:id",
        component: AssignInvestigatorViewComponent
      },

      {
        path: "forwarded-cases",
        component: ForwardedCasesComponent
      },
      {
        path: "investigation/view",
        component: InvestigationViewComponent
      },
      {
        path: "investigation/view/:id",
        component: InvestigationViewComponent
      },

      {
        path: "forwarded-cases/edit/:id",
        component: ForwardedCasesEditComponent
      },

      {
        path: "forwarded-cases/view/:id",
        component: ForwardedCasesViewComponent
      },
      {
        path: "completed-investigation",
        component: CompletedInvestigationComponent
      },
      {
        path: "completed-investigation/view/:id",
        component: CompletedInvestigationViewComponent
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ComplainantStatementRoutingModule { }
