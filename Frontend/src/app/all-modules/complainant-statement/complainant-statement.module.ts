import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ComplainantStatementRoutingModule } from './complainant-statement-routing.module';
import { ComplainantStatementComponent } from './complainant-statement.component';
import { AwardProposalListComponent } from './components/award-proposal/list/award-proposal-list.component';
import { AgainstIndividualListComponent } from './components/against-individual/list/against-individual-list.component';
import { AgainstDeptSecListComponent } from './components/against-dept-sec/list/against-dept-sec-list.component';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { CKEditorModule } from 'ckeditor4-angular';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxPrintModule } from 'ngx-print';
import { NgxSpinnerModule } from 'ngx-spinner';
import { PickListModule } from 'primeng/picklist';
import { SharingModule } from 'src/app/sharing/sharing.module';
import { IRecruitmentRoutingModule } from '../i-recruitment/i-recruitment-routing.module';
import { AgainstDeptSecCreateComponent } from './components/against-dept-sec/create/against-dept-sec-create.component';
import { AgainstDeptSecEditComponent } from './components/against-dept-sec/edit/against-dept-sec-edit.component';
import { AgainstDeptSecViewComponent } from './components/against-dept-sec/view/against-dept-sec-view.component';
import { AgainstIndividualCreateComponent } from './components/against-individual/create/against-individual-create.component';
import { AgainstIndividualEditComponent } from './components/against-individual/edit/against-individual-edit.component';
import { AgainstIndividualViewComponent } from './components/against-individual/view/against-individual-view.component';
import { AwardProposalCreateComponent } from './components/award-proposal/create/award-proposal-create.component';
import { AwardProposalEditComponent } from './components/award-proposal/edit/award-proposal-edit.component';
import { AwardProposalViewComponent } from './components/award-proposal/view/award-proposal-view.component';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { AssignInvestigatorComponent } from './components/assign-investigator/assign-investigator.component';
import { ForwardedCasesComponent } from './components/forwarded-cases/forwarded-cases.component';
import { AssignInvestigatorEditComponent } from './components/assign-investigator/edit/assign-investigator-edit.component';
import { ForwardedCasesEditComponent } from './components/forwarded-cases/edit/forwarded-cases-edit.component';
import { InvestigationViewComponent } from './components/investigation-view/investigation-view.component';
import { CompletedInvestigationComponent } from './components/completed-investigation/list/completed-investigation.component';
import { CompletedInvestigationViewComponent } from './components/completed-investigation/view/completed-investigation-view.component';
import { AgainstIndividualApproveComponent } from './components/against-individual/approve/against-individual-approve.component';
import { AssignInvestigatorViewComponent } from './components/assign-investigator/show/assign-investigator-view.component';
import { ForwardedCasesViewComponent } from './components/forwarded-cases/view/forwarded-cases-view.component';


@NgModule({
  declarations: [
    ComplainantStatementComponent,
    AwardProposalListComponent,
    AgainstIndividualListComponent,
    AgainstDeptSecListComponent,
    AgainstDeptSecCreateComponent,
    AgainstDeptSecEditComponent,
    AgainstDeptSecViewComponent,
    AgainstIndividualCreateComponent,
    AgainstIndividualEditComponent,
    AgainstIndividualViewComponent,
    AwardProposalCreateComponent,
    AwardProposalEditComponent,
    AwardProposalViewComponent,
    AssignInvestigatorComponent,
    ForwardedCasesComponent,
    AssignInvestigatorEditComponent,
    ForwardedCasesEditComponent,
    InvestigationViewComponent,
    CompletedInvestigationComponent,
    CompletedInvestigationViewComponent,
    AgainstIndividualApproveComponent,
    AssignInvestigatorViewComponent,
    ForwardedCasesViewComponent
  ],
  imports: [
    CommonModule,
    IRecruitmentRoutingModule,
    HttpClientModule,
    FormsModule,
    SharingModule,
    CKEditorModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgxSpinnerModule,
    NgSelectModule,
    PickListModule,
    NgxPrintModule,
    ComplainantStatementRoutingModule,
    BsDatepickerModule.forRoot(),
  ]
})
export class ComplainantStatementModule { }
