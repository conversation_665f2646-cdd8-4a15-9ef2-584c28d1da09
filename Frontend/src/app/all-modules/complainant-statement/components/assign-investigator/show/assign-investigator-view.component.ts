import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-assign-investigator-view',
  templateUrl: './assign-investigator-view.component.html',
  styleUrls: ['./assign-investigator-view.component.css']
})
export class AssignInvestigatorViewComponent implements OnInit {


  public baseUrl = environment.baseUrl;
  public myData: any = {};
  public actionList: any[] = [];
  public actionList2: any = {};
  public stepActionId: any = {};
  attachmentUrl: any = [];
  public parallelOrAutoActionList: any = {};
  public user: any;
  public formOfAction: FormGroup;
  public forDefendentView: boolean = false;
  public fromFordCasePage: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService,
  ) {

  }

  ngOnInit(): void {
    this.getApplicationData();
  }

  // --------------------- Get Application Data ----------------------

  getApplicationData() {

    const id = this.route.snapshot.params.id;
    const apiURL = this.baseUrl + '/investigation/get/' + id;

    const queryParams: any = {};
    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        this.myData = response?.data;
        this.spinnerService.hide();

      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  redirectToImage(fileName) {
    window.open(this.baseUrl + fileName, "_blank");
  }


  getAttachmentUrl(files) {
    this.attachmentUrl = files.split(',');
  }



}
