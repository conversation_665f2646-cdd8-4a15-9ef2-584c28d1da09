<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Assign Investigator</b></span>
                    </li>
                    <li class="breadcrumb-item active">View</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/assign-investigator"><i
                        class="fa fa-share"></i>
                    Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">


                            <table class="summaryTable col-md-12">
                                <tr>
                                    <td><b>Reference Code</b></td>
                                    <td>{{myData?.code}}</td>
                                    <td><b>Occurrence Date</b></td>
                                    <td>{{myData.occurrenceDate | date}}
                                    </td>
                                    <td><b>Submission Date</b></td>
                                    <td>{{myData.submissionDate | date}}</td>

                                </tr>

                                <tr>
                                    <td><b>Complain Ref Code</b></td>
                                    <td>{{ myData.complainantRefCode?myData.complainantRefCode:'-' }}
                                    </td>

                                    <td><b>Occurrence Time</b></td>
                                    <td>{{myData.occurrenceTime?myData.occurrenceTime:'-'}}
                                    </td>
                                    <td><b>Submission Time</b></td>
                                    <td>{{myData.submissionTime?myData.submissionTime:'-'}}</td>
                                </tr>

                                <tr>
                                    <td><b>Complainant</b></td>
                                    <td>{{ myData.complainantDisplayName}} ({{ myData.complainantLoginCode}})
                                    </td>

                                    <td><b>Investigator</b></td>
                                    <td>{{ myData.assignToName}} ({{ myData.assignToCode}})
                                    </td>

                                    <td><b>Case Category</b></td>
                                    <td>{{myData?.caseCategory}}</td>
                                </tr>



                                <tr>
                                    <td><b>Complain Type</b></td>
                                    <td>{{myData?.complainantType}}</td>

                                    <td><b>Investigator's Assistants</b></td>
                                    <td>{{myData.assignToAssistant?myData.assignToAssistant:'-'}}</td>



                                    <td><b>Case Value</b></td>
                                    <td>{{myData?.caseValue}}</td>



                                </tr>

                                <tr>
                                    <td><b>Case Status</b></td>
                                    <td>
                                        <span *ngIf="myData?.investigationStatus === 'Assigned'">
                                            <span class="text-info"> <b> {{myData?.investigationStatus}}
                                                </b></span>
                                        </span>
                                        <span *ngIf="myData?.investigationStatus === 'Processing'">
                                            <span class="text-warning">
                                                <b> Report Submitted </b></span>
                                        </span>
                                        <span *ngIf="myData?.investigationStatus === 'Completed'">
                                            <span class="text-success">
                                                <b> {{myData?.investigationStatus}} </b></span>
                                        </span>

                                        <span *ngIf="myData?.investigationStatus === 'Committee'">
                                            <span class="text-warning">
                                                <b> {{myData?.investigationStatus}} </b></span>
                                        </span>
                                    </td>

                                    <td><b>Report Headline</b></td>
                                    <td>{{myData?.reportHeadline}}</td>

                                    <td><b>Remarks</b></td>
                                    <td>{{myData.remarks?myData.remarks:'-'}}</td>
                                </tr>


                                <tr>
                                    <td><b>Description</b></td>
                                    <td colspan="6">{{myData?.description}}</td>

                                </tr>

                                <tr>
                                    <td><b>Abstract</b></td>
                                    <td colspan="6">{{myData?.abstractOptional}}</td>

                                </tr>

                                <tr>

                                    <td><b>Investigation HOD Attachment</b></td>
                                    <td><span data-toggle="modal" data-target="#doc_modal"
                                            (click)="getAttachmentUrl(myData.attachmentHod)"
                                            *ngIf="myData?.attachmentHod" class="text-primary"
                                            style="cursor: pointer;">Click
                                            Here</span></td>

                                    <td><b>Investigation Report Attachment</b></td>
                                    <td><span data-toggle="modal" data-target="#doc_modal"
                                            (click)="getAttachmentUrl(myData.investigationFileUpload)"
                                            *ngIf="myData?.investigationFileUpload" class="text-primary"
                                            style="cursor: pointer;">Click
                                            Here</span></td>

                                    <td><b>Enquiry Committee Attachment</b></td>
                                    <td><span data-toggle="modal" data-target="#doc_modal"
                                            (click)="getAttachmentUrl(myData.enquiryCommitteeFile)"
                                            *ngIf="myData?.enquiryCommitteeFile" class="text-primary"
                                            style="cursor: pointer;">Click
                                            Here</span></td>



                                </tr>


                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>





    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body customCard">
                    <div class="row">
                        <div class="col-md-12">

                            <div id="sectionPrint" class="col-md-12">
                                <fieldset class="row fieldsetBorder logBox ">
                                    <legend class="bg-warning">Defendant
                                    </legend>
                                    <div class="table-responsive col-md-12">
                                        <table class="table table-striped custom-table">
                                            <thead>
                                                <tr>
                                                    <th>SL</th>
                                                    <th>Employee</th>
                                                    <th>Misconduct Type</th>
                                                    <th>Sub Misconduct Type</th>
                                                    <th>Irregular Category</th>
                                                    <th>Irregularity</th>
                                                    <th>Proposed Action Category</th>
                                                    <th>Proposed Action Type</th>
                                                    <th>Proposal Deduction Score</th>
                                                    <th>Deduction Type</th>
                                                    <th>Deduction Amount</th>
                                                    <th>Investigation Recommendation</th>
                                                    <th>Status</th>
                                                    <th>APDC Opinion</th>
                                                    <th>Others Recommendation</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr *ngFor="let row of myData?.investigatorDetailsSet; let i = index">
                                                    <td>{{i+1}}</td>
                                                    <td>{{row.defendantName}} ({{row.defendantCode}})</td>
                                                    <td>{{row.misconductTitle?row.misconductTitle:'-'}}</td>
                                                    <td>{{row.subMisconduct?row.subMisconduct:'-'}}</td>
                                                    <td>{{row.irregularCategoryTitle?row.irregularCategoryTitle:'-'}}
                                                    </td>
                                                    <td>{{row.irregularityTitle?row.irregularityTitle:'-'}}</td>
                                                    <td>{{row.proposedActionCategory?row.proposedActionCategory:'-'}}
                                                    </td>
                                                    <td>{{row.proposedActionTypeTitle?row.proposedActionTypeTitle:'-'}}
                                                    </td>
                                                     <td>{{row.deductionScore?row.deductionScore:'-'}}</td>
                                                    <td>{{row.deductionType?row.deductionType:'-'}}</td>
                                                    <td>{{row.deductionAmount?row.deductionAmount:'-'}}</td>
                                                    <td>{{row.recommendation?row.recommendation:'-'}}</td>
                                                    <td>{{row.status?row.status:'-'}}</td>
                                                    <td>{{row.opinion?row.opinion:'-'}}</td>
                                                    <td>{{row.otherRecommendation?row.otherRecommendation:'-'}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </fieldset>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /Page Content -->


<!-- Documents Modal -->

<div id="doc_modal" class="modal custom-modal fade " role="dialog">
    <div class="modal-dialog modal-dialog-centered docModal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Evidence Attachment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card customCard">
                            <div class="card-body">
                                <div class="table-wrapper-scroll-y attn-modal-scrollbar">
                                    <table class="table table-striped custom-table  table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="text-center">SL</th>
                                                <th class="text-center">Attachment</th>


                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let val of attachmentUrl ;let i = index"
                                                [class.active]="i == currentIndex">


                                                <td class="text-center">{{i+1}}</td>

                                                <td class="text-center" style="cursor: pointer;"
                                                    (click)="redirectToImage(val)">{{val}}<br>
                                                </td>


                                        </tbody>
                                    </table>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--  Modal End -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>