import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AgainstIndividualViewComponent } from './against-individual-view.component';

describe('AgainstIndividualViewComponent', () => {
  let component: AgainstIndividualViewComponent;
  let fixture: ComponentFixture<AgainstIndividualViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AgainstIndividualViewComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AgainstIndividualViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
