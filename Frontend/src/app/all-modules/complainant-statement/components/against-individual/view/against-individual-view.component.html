<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Against Individual</b></span>
                    </li>
                    <li class="breadcrumb-item active">View</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto" *ngIf="fromFordCasePage==false">
                <a class="btn add-btn" routerLink="/complainant-statement/against-individual/list"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
            <div class="col-auto float-right ml-auto" *ngIf="fromFordCasePage==true">
                <a class="btn add-btn" routerLink="/complainant-statement/forwarded-cases"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->
    <div class="card customCard">
        <div class="card-body">
            <form [formGroup]="myForm">
                <div class="row">

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Complainant</label>
                            <input class="form-control" formControlName="complaintEmpName" class="form-control"
                                type="text" readonly>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Submission Date</label>

                            <div>
                                <input class="form-control" formControlName="submissionDate" class="form-control"
                                    type="date">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Occurrence Date</label>

                            <div>
                                <input class="form-control" formControlName="occuranceDate" class="form-control"
                                    type="date">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Occurrence Time</label>

                            <div>
                                <input class="form-control" formControlName="occuranceTime2" class="form-control"
                                    type="time">
                            </div>
                        </div>
                    </div>


                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Description (Bangla/English)</label>
                            <span class="float-right">
                                {{ numberOfChar2 }} / {{maxNumOfChar2}}
                            </span>
                            <textarea type="text" class="form-control" formControlName="description"
                                (keyup)="CharCount2($event)" rows="1"></textarea>

                        </div>
                    </div>

                    <div class="col-md-12 mt-3">
                        <h3>Defendant Details</h3>
                        <hr />
                        <form [formGroup]="defendantDetailsForm">

                            <div class="table-responsive col-md-12">
                                <table class="table table-striped custom-table">
                                    <thead>
                                        <th>SL</th>
                                        <th>Employee ID</th>
                                        <th style="min-width: 200px !important">Employee Name</th>
                                        <th>Irregular Category</th>
                                        <th>Irregularity</th>
                                        <th>Proposed Action Category</th>
                                        <th>Proposed Action Type</th>
                                        <th>Proposal Deduction Score</th>
                                        <th>Deduction Type</th>
                                        <th>Deduction Amount</th>
                                        <th>Present Score</th>
                                        <th style="min-width: 220px !important">Designation</th>
                                        <th>Employment Type</th>
                                        <th style="min-width: 200px !important">Operating Unit</th>
                                        <th>Job Location</th>
                                        <th style="min-width: 200px !important">Product</th>
                                        <th>Product Status</th>
                                        <th>Department</th>
                                        <th style="min-width: 200px !important">Section</th>
                                        <th style="min-width: 150px !important">SubSection</th>
                                        <th style="min-width: 200px !important">Team</th>
                                        <th style="min-width: 200px !important">
                                            Concern Incharge
                                        </th>
                                        <!-- <th>Concern HOD</th> -->
                                        <th style="min-width: 200px !important">Concern HR</th>
                                        <th>Previous Disiplinary Action</th>

                                    </thead>
                                    <tbody formArrayName="Rows">
                                        <tr *ngFor="let itemrow of defendantFormArr.controls; let i=index;let l=last"
                                            [formGroupName]="i">
                                            <td>{{i+1}}</td>

                                            <td>
                                                <input class="form-control" formControlName="employeeId"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="employeeName" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="irregularCategory"
                                                    type="text" readonly />

                                            </td>

                                            <td style="min-width: 200px;">

                                                <input class="form-control" formControlName="irregularity" type="text" title="{{againstIndivData?.complainantIndividualDetails[i]?.irregularity?.title}}"
                                                    readonly style="cursor: pointer;" />


                                            </td>

                                            <td style="min-width: 200px;">

                                                <input class="form-control" formControlName="proposedActionCategory"
                                                    type="text" readonly />

                                            </td>

                                            <td style="min-width: 200px;">

                                                <input class="form-control" formControlName="proposedActionType"
                                                    type="text" title="{{againstIndivData?.complainantIndividualDetails[i]?.proposedActionType?.title}}"
                                                    readonly style="cursor: pointer;"  />


                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="deductionScore"
                                                    type="number" readonly />
                                            </td>

                                            <td style="min-width: 200px;">

                                                <input class="form-control" formControlName="deductionType" type="text"
                                                    readonly />

                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="deductionAmount"
                                                    type="number" readonly />
                                            </td>



                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="presentScore"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="designnation"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="employmentType"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="operatingUnit"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="jobLocation"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="product"
                                                    class="form-control" type="text" readonly>
                                            </td>


                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="prductStatus"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="department"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="section"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="subSection"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="team" class="form-control"
                                                    type="text" readonly>
                                            </td>


                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="concernIncharge"
                                                    class="form-control" type="text" readonly>
                                            </td>


                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="concernHr"
                                                    class="form-control" type="text" readonly>
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="previousDisiplinaryAction"
                                                    class="form-control" type="text" readonly>
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <br>
                        </form>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>