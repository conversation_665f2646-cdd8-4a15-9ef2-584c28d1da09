import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-against-individual-view',
  templateUrl: './against-individual-view.component.html',
  styleUrls: ['./against-individual-view.component.css']
})
export class AgainstIndividualViewComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  private uploadForm: FormGroup;
  public defendantDetailsForm: FormGroup;
  againstIndivData: any;

  configDDL: any;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 500;
  numberOfChar = 0;

  maxNumOfChar2 = 500;
  numberOfChar2 = 0;
  listData4: any = [];
  listData3: any = [];

  public fromFordCasePage: boolean = false;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private loginService: LoginService,

  ) {
    this._initConfigDDL();
    this._customInitLoadData();

  }

  ngOnInit(): void {
    this.initializeForm();
    this.defendantDetailsForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()])
    });

    this.loadComplaintData();
  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      complaintEmpName: [""],
      submissionDate: ["", [Validators.required]],
      occuranceDate: ["", [Validators.required]],
      occuranceTime2: ["", [Validators.required]],
      descriptionBngl: ["", [Validators.required]],
      description: ["", [Validators.required]],

    });

    this.myForm.disable();

    this.uploadForm = this.formBuilder.group({
      fileUrl: [""],
    });

  }


  // --------------- Documents Info ---------

  get defendantFormArr() {
    return this.defendantDetailsForm.get("Rows") as FormArray;

  }

  initDocRows() {
    return this.formBuilder.group({
      employeeId: [""],
      employeeName: [""],
      irregularCategory: [""],
      irregularity: [""],
      proposedActionCategory: [""],
      awardPunishmentProposal: [""],
      proposedActionType: [""],
      deductionScore: [""],
      deductionType: [""],
      deductionAmount: [""],

      presentScore: [""],
      designnation: [""],
      employmentType: [""],
      jobLocation: [""],
      operatingUnit: [""],
      prductStatus: [""],
      product: [""],
      department: [""],
      section: [""],
      subSection: [""],
      team: [""],
      concernIncharge: [""],
      concernHr: [""],
      previousDisiplinaryAction: [""],
    });
  }



  // Textarea field character Count

  CharCount(event: any): void {
    this.numberOfChar = event.target.value.length;

    if (this.numberOfChar > this.maxNumOfChar) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar);
      this.numberOfChar = this.maxNumOfChar;
    }
  }

  CharCount2(event: any): void {
    this.numberOfChar2 = event.target.value.length;

    if (this.numberOfChar2 > this.maxNumOfChar2) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar2);
      this.numberOfChar2 = this.maxNumOfChar2;
    }
  }

  //-----------Get Relational Object Id ------------------
  get getHrCrEmp() {
    return this.myForm.get("hrCrEmp");
  }

  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {


    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------


  onInputBlur(i) {
    let empId = this.defendantDetailsForm.value.Rows[i].employeeId;
    let apiURL = this.baseUrl + "/hrCrEmp/getEmpDataByLoginCode/" + empId;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        console.log(response);
        this.setFormValues(response, i);
      }
    )
  }

  setFormValues(response, i) {
    this.defendantFormArr.controls[i].patchValue(response);
  }

  loadComplaintData() {

    let queryParams: any = {};
    let apiURL = this.baseUrl + "/complainIndividual/get";

    this.route.paramMap.subscribe(params => {
      if (params.has('code')) {
        this.fromFordCasePage = true;
        queryParams["code"] = this.route.snapshot.params.code;
        this.spinnerService.show();
        this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
          (response: any) => {
            this.againstIndivData = response;
            this.patchWithFormValues(response);
            this.spinnerService.hide();
          }
        )

      } else {
        queryParams["id"] = this.route.snapshot.params.id;
        this.spinnerService.show();
        this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
          (response: any) => {
            this.againstIndivData = response;
            this.patchWithFormValues(response);
            this.spinnerService.hide();
          }
        )
      }
    });



  }

  patchWithFormValues(response) {
    this.myForm.patchValue(response);
    this.myForm.controls['complaintEmpName'].setValue(response.hrCrEmp.displayName);
    const date = this.convertTimeFromJsonToAnuglar(response.occuranceTime2);
    this.myForm.controls['occuranceTime2'].setValue(this.datePipe.transform(date, "HH:mm").toString());
    this.setDefendentDetails(response.complainantIndividualDetails);
  }


  convertTimeFromJsonToAnuglar(time) {
    const dateString = time;
    const date = new Date();
    date.setHours(parseInt(dateString.substring(0, 2)));
    date.setMinutes(parseInt(dateString.substring(3, 5)));

    return date;
  }

  setDefendentDetails(response) {
    this.defendantFormArr.clear();
    for (let data of response) {
      this.defendantFormArr.push(this.returnInItDocRows(data));
    }
  }

  returnInItDocRows(data) {
    return this.formBuilder.group({
      id: data.id,
      employeeId: data.employeeId,
      employeeName: data.employeeName,
      irregularCategory: data?.irregularCategory?.title,
      irregularity: data?.irregularity?.title,
      proposedActionCategory: data.proposedActionCategory,
      proposedActionType: data.proposedActionType?.title,
      deductionScore: data.deductionScore,
      deductionType: data.deductionType,
      deductionAmount: data.deductionAmount,
      presentScore: data.presentScore,
      designnation: data.designnation,
      employmentType: data.employmentType,
      jobLocation: data.jobLocation,
      operatingUnit: data.operatingUnit,
      prductStatus: data.prductStatus,
      product: data.product,
      department: data.department,
      section: data.section,
      subSection: data.subSection,
      team: data.team,
      concernIncharge: data.concernIncharge,
      concernHr: data.concernHr,
      previousDisiplinaryAction: data.previousDisiplinaryAction,
    });
  }




}
