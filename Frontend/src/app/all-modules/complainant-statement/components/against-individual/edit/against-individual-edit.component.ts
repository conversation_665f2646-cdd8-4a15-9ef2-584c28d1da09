import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-against-individual-edit',
  templateUrl: './against-individual-edit.component.html',
  styleUrls: ['./against-individual-edit.component.css']
})
export class AgainstIndividualEditComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  private uploadForm: FormGroup;
  public defendantDetailsForm: FormGroup;
  againstIndivData: any;
  irregularCategories: any = [];
  irregularities: any[] = [];
  proposedActionTypes: any = [];

  configDDL: any;

  isDocSelect = false;
  imgSrc: any;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 2000;
  numberOfChar = 0;

  maxNumOfChar2 = 2000;
  numberOfChar2 = 0;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private loginService: LoginService
  ) {
    this._initConfigDDL();
    this._customInitLoadData();

  }

  ngOnInit(): void {
    this.initializeForm();
    this.defendantDetailsForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()])
    });
    this.loadAllIregularCategory();
    this.getProposedActionType();
    this.loadComplaintData();

  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      id: [""],
      complaintEmpName: [""],
      investInchIds: [""],
      occuranceDate: [""],
      occuranceTime: [""],
      description: ["", [Validators.required]],

    });

    let userName = this.loginService.getUser().displayName;
    this.myForm.controls['complaintEmpName'].setValue(userName);

    this.uploadForm = this.formBuilder.group({
      fileUrl: [""],
    });

  }


  // --------------- Documents Info ---------

  get defendantFormArr() {
    return this.defendantDetailsForm.get("Rows") as FormArray;

  }

  initDocRows() {
    return this.formBuilder.group({
      id: [""],
      employeeId: ["", [Validators.required]],
      employeeName: ["", [Validators.required]],
      irregularCategory: [""],
      irregularity: ["", [Validators.required]],
      proposedActionCategory: [""],
      awardPunishmentProposal: [""],
      proposedActionType: ["", [Validators.required]],
      deductionScore: [""],
      deductionType: [""],
      deductionAmount: [""],

      presentScore: [""],
      designnation: [""],
      employmentType: [""],
      jobLocation: [""],
      operatingUnit: [""],
      prductStatus: [""],
      product: [""],
      department: [""],
      section: [""],
      subSection: [""],
      team: [""],
      concernIncharge: [""],
      concernHr: [""],
      approvalStatus: [""],
      approvalStep: [""],
      approvalProcess: [""],
      previousDisiplinaryAction: [""],
    });
  }



  addRow() {
    this.defendantFormArr.push(this.initDocRows());
  }

  deleteRow(itemRow, index: number) {
    this.defendantFormArr.removeAt(index);
    if (itemRow.value.id) {
      this.deleteAgainstIndivList(itemRow.value.id);
    }
  }

  deleteAgainstIndivList(id) {
    let apiURL = this.baseUrl + "/complainIndividual/deleteComplainantIndividualDetails/" + id;

    let queryParams: any = {};
    this.spinnerService.show();

    this.commonService.sendDeleteRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.spinnerService.hide();
        this.loadComplaintData();
      }
    )
  }



  // Textarea field character Count

  CharCount(event: any): void {
    this.numberOfChar = event.target.value.length;

    if (this.numberOfChar > this.maxNumOfChar) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar);
      this.numberOfChar = this.maxNumOfChar;
    }
  }

  CharCount2(event: any): void {
    this.numberOfChar2 = event.target.value.length;

    if (this.numberOfChar2 > this.maxNumOfChar2) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar2);
      this.numberOfChar2 = this.maxNumOfChar2;
    }
  }



  myFormSubmit() {

    if (this.myForm.invalid) {

      this.toastr.info("Please insert valid data")
      return;
    }

    const hrCrEmpData = Object.assign(this.myForm.value, {
    });

    let apiURL = this.baseUrl + "/complainIndividual/edit";

    let formData: any = {};
    formData = hrCrEmpData;

    // process date
    formData.occuranceDate = (formData.occuranceDate) ? this.commonService.format_Date_Y_M_D(formData.occuranceDate) : null;

    for (let i = 0; i < this.defendantDetailsForm.value.Rows.length; i++) {
      this.defendantDetailsForm.value.Rows[i] = Object.assign(this.defendantDetailsForm.value.Rows[i], {
        irregularCategory: this.defendantDetailsForm.value.Rows[i].irregularCategory ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].irregularCategory) } : null,
        irregularity: this.defendantDetailsForm.value.Rows[i].irregularity ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].irregularity) } : null,
        proposedActionType: this.defendantDetailsForm.value.Rows[i].proposedActionType ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].proposedActionType) } : null,
        approvalStep: this.defendantDetailsForm.value.Rows[i].approvalStep ?
          { id: parseInt(this.defendantDetailsForm.value.Rows[i].approvalStep) } : null,
        approvalProcess: this.defendantDetailsForm.value.Rows[i].approvalProcess ?
          { id: parseInt(this.defendantDetailsForm.value.Rows[i].approvalProcess) } : null,
      })
    }

    formData.complainantIndividualDetails = this.defendantDetailsForm.value.Rows;

    this.spinnerService.show();

    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.uploadIndivImage(response.data.id);
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
        this.ngOnInit();
      }
    );


  }

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      this.isDocSelect = true;
      let fileList = event.target.files;

      for (let i = 0; i < fileList.length; i++) {
        const reader = new FileReader();
        reader.readAsDataURL(fileList[i]);
        reader.onload = (e) => (this.imgSrc = reader.result);
      }

      this.uploadForm.get("fileUrl").setValue(fileList);
    }
    else {
      this.isDocSelect = false;
    }
  }



  // ---------------- Get Proposed Action Type ----------------

  getProposedActionType() {
    let apiURL = this.baseUrl + "/punishmentType/getAll";
    let queryParams: any = {};
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.proposedActionTypes = response;
      },
      (error) => {
        console.log(error);
      }
    );
  }


  // ---------------- Get Irregular Category ----------------

  loadAllIregularCategory() {
    let alkpType = "PUNISHMENT_CATEGORY";
    let apiURL = this.baseUrl + "/alkp/search/" + alkpType;
    let queryParams: any = {};
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.irregularCategories = response?.subALKP;
      },
      (error) => {
        console.log(error);
      }
    );
  }



  // ---------------- Get Irregularity ----------------

  getIrregularityByCategory(categoryId, i) {
    this.spinnerService.show();
    this.irregularCategories.forEach((element) => {

      if (element.id == categoryId) {

        let apiURL = this.baseUrl + "/hrcrDiscipline/byCategory/" + element.title;
        let queryParams: any = {};
        this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
          (response: any) => {
            this.irregularities[i] = response;
            this.spinnerService.hide();
          },
          (error) => {
            console.log(error);
            this.spinnerService.hide();
          }
        );
      }

    });
  }


  // ---------------- Get Deduction Score --------------------

  getDeductionScore(actionId, i) {
    if (!actionId || actionId == undefined || actionId == null || actionId === '') {
      this.defendantFormArr.controls[i].get('deductionScore').patchValue(0);
    } else {
      this.proposedActionTypes.forEach((element) => {

        if (element.id == actionId) {

          this.defendantFormArr.controls[i].get('deductionScore').patchValue(element.deductScore);
        }

      });
    }
  }


  uploadIndivImage(id) {

    const formData = new FormData();
    if (this.uploadForm.get("fileUrl")) {
      formData.append("file", this.uploadForm.get("fileUrl").value);
      formData.append("type", "fileUrl");

      let apiURL = this.baseUrl + "/complainIndividual/uploadPhoto/" + id;

      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (response: any) => {
          this.spinnerService.hide();
          this.router.navigate(["/complainant-statement/against-individual/list"], { relativeTo: this.route });
        },
        (error) => {
          this.router.navigate(["/complainant-statement/against-individual/list"], { relativeTo: this.route });
          this.spinnerService.hide();
        }
      )
    }
    else {
      this.spinnerService.hide();
      this.router.navigate(["/complainant-statement/against-individual/list"], { relativeTo: this.route });
    }
  }

  //-----------Get Relational Object Id ------------------
  get getHrCrEmp() {
    return this.myForm.get("hrCrEmp");
  }



  getEmployeeInfo(i) {
    let empId = this.defendantDetailsForm.value.Rows[i].employeeId;
    let apiURL = this.baseUrl + "/hrCrEmp/getEmpDataByLoginCode?loginCode=" + empId;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        console.log(response);
        let empDetails = response;
        empDetails.id = this.defendantDetailsForm.value.Rows[i].id ? this.defendantDetailsForm.value.Rows[i].id : null;
        this.setFormValues(empDetails, i);
      }
    )
  }

  // ----------------- First Part of Time ---------------------

  getFirstPartOfTimeString(myString: any) {

    const indexOfColon = myString.indexOf(':');

    if (indexOfColon !== -1) {
      const substring = myString.substring(0, indexOfColon);
      const size = substring.length;
      if (size !== 2) {

        const finalString = "0" + substring;
        return finalString;
      }

      return substring;

    }

  }

  // ----------------- Second Part of Time ---------------------

  getSecondPartOfTimeString(myString: any) {

    const substring = myString.split(":")[1];

    const size = substring.length;
    if (size !== 2) {

      const finalString = "0" + substring;
      return finalString;
    }
    return substring;

  }

  setFormValues(response, i) {
    this.defendantFormArr.controls[i].patchValue(response);
  }

  loadComplaintData() {

    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/complainIndividual/get?id=" + id;

    let queryParams: any = {};

    this.spinnerService.show();

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.againstIndivData = response;

        //set date
        this.againstIndivData.occuranceDate = this.datePipe.transform(response?.occuranceDate, "dd-MM-yyyy").toString().slice(0, 10);

        this.patchWithFormValues(this.againstIndivData);
      }
    )

  }

  patchWithFormValues(response) {
    this.myForm.patchValue(response);
    this.myForm.controls['complaintEmpName'].setValue(this.againstIndivData?.hrCrEmp?.displayName);


    let strinTypeDateTime: any;
    let time: any;
    let finalStartTime: any;
    this.myForm.controls['occuranceTime'].setValue(finalStartTime);

    if (response.occuranceTime && response.occuranceTime != null) {
      strinTypeDateTime = response.occuranceTime;
      time = strinTypeDateTime[0] + ":" + strinTypeDateTime[1];
      finalStartTime = this.getFirstPartOfTimeString(time) + ":" + this.getSecondPartOfTimeString(time);
      this.myForm.controls['occuranceTime'].setValue(finalStartTime);
    }


    // Get Irregularities by Irregular Category

    for (let i = 0; i < response.complainantIndividualDetails.length; i++) {

      this.getIrregularityByCategory(response.complainantIndividualDetails[i]?.irregularCategory?.id, i);


    }


    this.setDefendentDetails(response.complainantIndividualDetails);
    this.spinnerService.hide();
  }


  setDefendentDetails(response) {
    this.defendantFormArr.clear();
    for (let data of response) {
      this.defendantFormArr.push(this.returnInItDocRows(data));
    }
  }

  returnInItDocRows(data) {
    return this.formBuilder.group({
      id: data.id,
      employeeId: data.employeeId,
      irregularCategory: data?.irregularCategory?.id,
      irregularity: data?.irregularity?.id,
      proposedActionCategory: data?.proposedActionCategory,
      proposedActionType: data?.proposedActionType?.id,
      deductionType: data?.deductionType,
      deductionScore: data?.deductionScore,
      deductionAmount: data?.deductionAmount,
      employeeName: data.employeeName,
      presentScore: data.presentScore,
      designnation: data.designnation,
      employmentType: data.employmentType,
      jobLocation: data.jobLocation,
      operatingUnit: data.operatingUnit,
      prductStatus: data.prductStatus,
      product: data.product,
      department: data.department,
      section: data.section,
      subSection: data.subSection,
      team: data.team,
      concernIncharge: data.concernIncharge,
      concernHr: data.concernHr,
      approvalStatus: data.approvalStatus,
      approvalStep: data?.approvalStep?.id,
      approvalProcess: data?.approvalProcess?.id,
      previousDisiplinaryAction: data.previousDisiplinaryAction,
    });
  }


  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {
    console.log("...");
    console.log("ddlActiveFieldName:" + activeFieldNameDDL);
    console.log("dataGetApiPathDDL:" + dataGetApiPathDDL);
    console.log(event.target);

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------


}
