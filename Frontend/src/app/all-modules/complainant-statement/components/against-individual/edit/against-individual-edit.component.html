<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active">
                        <span style="color: #25b6b2"><b>Against Individual</b></span>
                    </li>
                    <li class="breadcrumb-item active">Edit</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/against-individual/list"><i
                        class="fa fa-share"></i>
                    Back To List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <div class="col-md-12 mb-2">
        <div class="d-flex justify-content-between align-items-center breaking-news bg-white">
            <div
                class="d-flex flex-row flex-grow-1 flex-fill justify-content-center bg-danger py-2 text-white px-1 news">
                <span class="d-flex align-items-center">&nbsp;N.B.</span>
            </div>
            <marquee class="news-scroll" behavior="scroll" direction="left" onmouseover="this.stop();"
                onmouseout="this.start();">
                <a style="color: red; font-size: 18px" href="#">If it is proven that any employee has made false
                    complaint then
                    that
                    employee needs to face trials and punishments along with fines if
                    required.
                </a>
                <span class="dot"></span>
                <a style="color: red" href="#">
                    যদি এটি প্রমাণিত হয় যে কোনও কর্মচারী মিথ্যা অভিযোগ করেছেন তবে সেই
                    কর্মচারীকে প্রয়োজনে জরিমানা সহ বিচার এবং শাস্তির মুখোমুখি হতে হবে।</a>
            </marquee>
        </div>
    </div>

    <div class="card customCard">
        <div class="card-body">
            <form novalidate (ngSubmit)="myFormSubmit()" [formGroup]="myForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="val-required">Complainant</label>
                            <input class="form-control" formControlName="complaintEmpName" class="form-control"
                                type="text" readonly />
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Occurrence Date</label>
                            <div class="cal-icon">
                                <input class="form-control floating datetimepicker" formControlName="occuranceDate"
                                    bsDatepicker type="text" placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Occurrence Time</label>

                            <div>
                                <input class="form-control" formControlName="occuranceTime" class="form-control"
                                    type="time" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="val-required">Description (Bangla/English)</label>
                            <span class="float-right">
                                {{ numberOfChar2 }} / {{ maxNumOfChar2 }}
                            </span>
                            <textarea type="text" class="form-control" formControlName="description"
                                (keyup)="CharCount2($event)" rows="1"></textarea>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="val-required">Invest./Section Incharge</label>
                            <textarea type="text" placeholder="Ex: 54546,54802,54840" class="form-control"
                                formControlName="investInchIds" rows="1"></textarea>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="formFileMultiple" class="form-label">Evidence Attachment</label>
                            <input class="form-control" type="file" name="fileUrl" id="formFileMultiple"
                                (change)="onFileSelect($event)" multiple />
                        </div>
                    </div>

                    <div class="col-md-12 mt-3">
                        <h3>Defendant Details</h3>
                        <hr />
                        <form [formGroup]="defendantDetailsForm">
                            <div class="table-responsive col-md-12">
                                <table class="table table-striped custom-table">
                                    <thead>
                                        <tr>
                                            <th>SL</th>
                                            <th>Employee ID</th>
                                            <th style="min-width: 200px !important">Employee Name</th>
                                            <!-- <th>Irregular Category</th> -->
                                            <!-- <th>Irregularity</th> -->
                                            <!-- <th>Proposed Action Category</th> -->
                                            <!-- <th>Proposed Action Type</th> -->
                                            <!-- <th>Proposal Deduction Score</th> -->
                                            <!-- <th>Deduction Type</th> -->
                                            <!-- <th>Deduction Amount</th> -->
                                            <!-- <th>Present Score</th> -->
                                            <th style="min-width: 220px !important">Designation</th>
                                            <th>Employment Type</th>
                                            <th style="min-width: 200px !important">Operating Unit</th>
                                            <th>Job Location</th>
                                            <th style="min-width: 200px !important">Product</th>
                                            <th>Product Status</th>
                                            <th>Department</th>
                                            <th style="min-width: 200px !important">Section</th>
                                            <th style="min-width: 150px !important">SubSection</th>
                                            <th style="min-width: 200px !important">Team</th>
                                            <th style="min-width: 200px !important">
                                                Concern Incharge
                                            </th>
                                            <!-- <th>Concern HOD</th> -->
                                            <th style="min-width: 200px !important">Concern HR</th>
                                            <th>Previous Disiplinary Action</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody formArrayName="Rows">
                                        <tr *ngFor="let itemrow of defendantFormArr.controls;let i = index;let l = last"
                                            [formGroupName]="i">
                                            <td>{{ i + 1 }}</td>

                                            <td>
                                                <input class="form-control" formControlName="employeeId" type="text"
                                                    (blur)="getEmployeeInfo(i)" />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="employeeName" type="text"
                                                    readonly />
                                            </td>

                                            <!-- drop down values from Alkp table with keyword='PUNISHMENT_CATEGORY' -->
                                            <!-- <td style="min-width: 200px;">
                        <select class="select form-control" formControlName="irregularCategory"
                          (change)="getIrregularityByCategory($event.target.value , i)">
                          <option value="">Select Any</option>
                          <option *ngFor="let data of irregularCategories" [value]="data.id">
                            {{ data.title }}
                          </option>
                        </select>
                      </td> -->

                                            <!-- <td style="min-width: 200px;">
                        <select class="select form-control" formControlName="irregularity">
                          <option value="">Select Any</option>
                          <option *ngFor="let data of irregularities[i]" [value]="data.id">
                            {{ data.title }}
                          </option>
                        </select>
                      </td> -->

                                            <!-- <td style="min-width: 200px;">
                        <select class="select form-control" formControlName="proposedActionCategory">
                          <option value="">Select Any</option>
                          <option value="1">1</option>
                          <option value="2">2</option>
                          <option value="3">3</option>
                          <option value="4">4</option>
                          <option value="5">5</option>
                        </select>
                      </td> -->

                                            <!-- <td style="min-width: 200px;">
                        <select class="select form-control" formControlName="proposedActionType"
                          (change)="getDeductionScore($event.target.value, i)">
                          <option value="">Select Any</option>
                          <option *ngFor="let data of proposedActionTypes" [value]="data.id">
                            {{ data.title }}
                          </option>
                        </select>
                      </td> -->

                                            <!-- <td style="min-width: 200px;">
                        <input class="form-control" formControlName="deductionScore" type="number" readonly />
                      </td> -->
                                            <!-- 
                      <td style="min-width: 200px;">
                        <select class="select form-control" formControlName="deductionType">
                          <option selected value="">Select Type</option>
                          <option value="Fine">Fine</option>
                          <option value="Custom">Custom</option>
                        </select>
                      </td> -->

                                            <!-- <td style="min-width: 200px;">
                        <input class="form-control" formControlName="deductionAmount" type="number" />
                      </td> -->


                                            <!-- <td style="min-width: 200px;">
                        <input class="form-control" formControlName="presentScore" type="text" readonly />
                      </td> -->

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="designation" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="employmentType" type="text"
                                                    readonly />
                                            </td>

                                            <td>
                                                <input class="form-control" formControlName="operatingUnit" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="jobLocation" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="product" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="productStatus" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="department" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="section" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="subSection" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input formControlName="team" class="form-control" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="concernIncharge"
                                                    type="text" readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="concernHr" type="text"
                                                    readonly />
                                            </td>

                                            <td style="min-width: 200px;">
                                                <input class="form-control" formControlName="previousDisiplinaryAction"
                                                    type="text" readonly />
                                            </td>

                                            <td>
                                                <button *ngIf="
                            defendantDetailsForm.controls.Rows.controls.length >
                            0
                          " (click)="deleteRow(i)" class="btn btn-danger">
                                                    Delete
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <br />
                            <button type="button" (click)="addRow()" class="btn btn-info">
                                Add Row
                            </button>
                        </form>
                    </div>
                </div>

                <div class="text-right mt-2">
                    <button type="button" class="btn btn-warning btn-ripple"
                        routerLink="/employees/gift-requisition/list">
                        <i class="fa fa-share"></i> Cancel
                    </button>
                    &nbsp; &nbsp;
                    <button type="button" id="reset" class="btn btn-secondary btn-ripple">
                        <i class="fa fa-undo" aria-hidden="true"></i> Reset
                    </button>
                    &nbsp; &nbsp; &nbsp;
                    <button type="submit" id="submit" [disabled]="!myForm.valid" class="btn btn-primary btn-ripple">
                        <i class="fa fa-check" aria-hidden="true"></i> Update
                        &nbsp;&nbsp;&nbsp;
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>