import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AgainstIndividualListComponent } from './against-individual-list.component';

describe('AgainstIndividualListComponent', () => {
  let component: AgainstIndividualListComponent;
  let fixture: ComponentFixture<AgainstIndividualListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AgainstIndividualListComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AgainstIndividualListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
