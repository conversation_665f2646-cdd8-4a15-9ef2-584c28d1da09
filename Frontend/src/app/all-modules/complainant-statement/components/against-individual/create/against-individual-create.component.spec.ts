import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AgainstIndividualCreateComponent } from './against-individual-create.component';

describe('AgainstIndividualCreateComponent', () => {
  let component: AgainstIndividualCreateComponent;
  let fixture: ComponentFixture<AgainstIndividualCreateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AgainstIndividualCreateComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AgainstIndividualCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
