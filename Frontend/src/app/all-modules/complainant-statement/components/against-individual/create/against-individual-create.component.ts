import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { event } from 'jquery';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-against-individual-create',
  templateUrl: './against-individual-create.component.html',
  styleUrls: ['./against-individual-create.component.css']
})
export class AgainstIndividualCreateComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  private uploadForm: FormGroup;
  public defendantDetailsForm: FormGroup;

  irregularCategories: any = [];
  irregularities: any[] = [];
  proposedActionTypes: any = [];

  configDDL: any;

  isDocSelect = false;
  imgSrc: any;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 2000;
  numberOfChar = 0;

  maxNumOfChar2 = 2000;
  numberOfChar2 = 0;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private loginService: LoginService
  ) {
    this._initConfigDDL();
    this._customInitLoadData();

  }

  ngOnInit(): void {
    this.initializeForm();
    this.defendantDetailsForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()])
    });
    this.loadAllIregularCategory();
    this.getProposedActionType();

  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      complaintEmpName: [""],
      occuranceDate: [""],
      occuranceTime: [""],
      investInchIds: [""],
      // descriptionBngl: [""],
      description: ["", [Validators.required]],

    });

    let userName = this.loginService.getUser().displayName;
    this.myForm.controls['complaintEmpName'].setValue(userName);

    this.uploadForm = this.formBuilder.group({
      fileUrl: [""],
    });

  }


  // --------------- Documents Info ---------

  get defendantFormArr() {
    return this.defendantDetailsForm.get("Rows") as FormArray;

  }

  initDocRows() {
    return this.formBuilder.group({
      employeeId: ["", [Validators.required]],
      employeeName: ["", [Validators.required]],
      irregularCategory: [""],
      irregularity: [""],
      proposedActionCategory: [""],
      awardPunishmentProposal: [""],
      proposedActionType: [""],
      deductionScore: [""],
      deductionType: [""],
      deductionAmount: [""],

      presentScore: [""],
      designation: [""],
      employmentType: [""],
      jobLocation: [""],
      operatingUnit: [""],
      productStatus: [""],
      product: [""],
      department: [""],
      section: [""],
      subSection: [""],
      team: [""],
      concernIncharge: [""],
      concernHr: [""],
      previousDisiplinaryAction: [""],
    });
  }



  addRow() {
    this.defendantFormArr.push(this.initDocRows());
  }

  deleteRow(index: number) {
    this.defendantFormArr.removeAt(index);
  }



  // Textarea field character Count

  CharCount(event: any): void {
    this.numberOfChar = event.target.value.length;

    if (this.numberOfChar > this.maxNumOfChar) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar);
      this.numberOfChar = this.maxNumOfChar;
    }
  }

  CharCount2(event: any): void {
    this.numberOfChar2 = event.target.value.length;

    if (this.numberOfChar2 > this.maxNumOfChar2) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar2);
      this.numberOfChar2 = this.maxNumOfChar2;
    }
  }

  // ------------- Get Employee Info -----------------


  getEmployeeInfo(i) {
    let empId = this.defendantDetailsForm.value.Rows[i].employeeId;
    let apiURL = this.baseUrl + "/hrCrEmp/getEmpDataByLoginCode?loginCode=" + empId;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        console.log(response);
        this.setFormValues(response, i);
      }
    )
  }

  setFormValues(response, i) {
    this.defendantFormArr.controls[i].patchValue(response);
  }



  // ---------------- Get Proposed Action Type ----------------

  getProposedActionType() {
    let apiURL = this.baseUrl + "/punishmentType/getAll";
    let queryParams: any = {};
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.proposedActionTypes = response;
      },
      (error) => {
        console.log(error);
      }
    );
  }


  // ---------------- Get Irregular Category ----------------

  loadAllIregularCategory() {
    let alkpType = "PUNISHMENT_CATEGORY";
    let apiURL = this.baseUrl + "/alkp/search/" + alkpType;
    let queryParams: any = {};
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.irregularCategories = response?.subALKP;
      },
      (error) => {
        console.log(error);
      }
    );
  }



  // ---------------- Get Irregularity ----------------

  getIrregularityByCategory(categoryId, i) {
    this.spinnerService.show();
    this.irregularCategories.forEach((element) => {

      if (element.id == categoryId) {

        let apiURL = this.baseUrl + "/hrcrDiscipline/byCategory/" + element.title;
        let queryParams: any = {};
        this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
          (response: any) => {
            this.irregularities[i] = response;
            this.spinnerService.hide();
          },
          (error) => {
            console.log(error);
            this.spinnerService.hide();
          }
        );
      }

    });
  }


  // ---------------- Get Deduction Score --------------------

  getDeductionScore(actionId, i) {
    if (!actionId || actionId == undefined || actionId == null || actionId === '') {
      this.defendantFormArr.controls[i].get('deductionScore').patchValue(0);
    } else {
      this.proposedActionTypes.forEach((element) => {

        if (element.id == actionId) {

          this.defendantFormArr.controls[i].get('deductionScore').patchValue(element.deductScore);
        }

      });
    }
  }


  myFormSubmit() {

    if (this.myForm.invalid) {

      this.toastr.info("Please insert valid data")
      return;
    }

    const hrCrEmpData = Object.assign(this.myForm.value, {
    });

    let apiURL = this.baseUrl + "/complainIndividual/save";

    let formData: any = {};
    formData = hrCrEmpData;

    // process date
    formData.occuranceDate = (formData.occuranceDate) ? this.commonService.format_Date_Y_M_D(formData.occuranceDate) : null;


    for (let i = 0; i < this.defendantDetailsForm.value.Rows.length; i++) {
      this.defendantDetailsForm.value.Rows[i] = Object.assign(this.defendantDetailsForm.value.Rows[i], {
        irregularCategory: this.defendantDetailsForm.value.Rows[i].irregularCategory ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].irregularCategory) } : null,
        irregularity: this.defendantDetailsForm.value.Rows[i].irregularity ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].irregularity) } : null,
        proposedActionType: this.defendantDetailsForm.value.Rows[i].proposedActionType ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].proposedActionType) } : null,
      })
    }

    formData.complainantIndividualDetails = this.defendantDetailsForm.value.Rows;

    this.spinnerService.show();

    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.uploadIndivImage(response.data.id);
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );


  }

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      this.isDocSelect = true;
      let fileList = event.target.files;

      for (let i = 0; i < fileList.length; i++) {
        const reader = new FileReader();
        reader.readAsDataURL(fileList[i]);
        reader.onload = (e) => (this.imgSrc = reader.result);
      }

      this.uploadForm.get("fileUrl").setValue(fileList);
    }
    else {
      this.isDocSelect = false;
    }
  }


  uploadIndivImage(id) {

    const formData = new FormData();
    if (this.uploadForm.get("fileUrl")) {

      for (let i = 0; i < this.uploadForm.get("fileUrl").value.length; i++) {
        formData.append("files", this.uploadForm.get("fileUrl").value[i]);
      }

      let apiURL = this.baseUrl + "/complainIndividual/uploadPhoto/" + id;


      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (response: any) => {
          this.spinnerService.hide();
          this.router.navigate(["/complainant-statement/against-individual/list"], { relativeTo: this.route });
        },
        (error) => {
          this.router.navigate(["/complainant-statement/against-individual/list"], { relativeTo: this.route });
          this.spinnerService.hide();
        }
      )
    }
    else {
      this.spinnerService.hide();
      this.router.navigate(["/complainant-statement/against-individual/list"], { relativeTo: this.route });
    }
  }

  //-----------Get Relational Object Id ------------------
  get getHrCrEmp() {
    return this.myForm.get("hrCrEmp");
  }

  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {
    console.log("...");
    console.log("ddlActiveFieldName:" + activeFieldNameDDL);
    console.log("dataGetApiPathDDL:" + dataGetApiPathDDL);
    console.log(event.target);

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------



}
