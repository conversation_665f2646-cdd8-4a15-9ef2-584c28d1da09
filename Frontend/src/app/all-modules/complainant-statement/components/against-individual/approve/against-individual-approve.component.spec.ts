import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AgainstIndividualApproveComponent } from './against-individual-approve.component';

describe('AgainstIndividualApproveComponent', () => {
  let component: AgainstIndividualApproveComponent;
  let fixture: ComponentFixture<AgainstIndividualApproveComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AgainstIndividualApproveComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AgainstIndividualApproveComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
