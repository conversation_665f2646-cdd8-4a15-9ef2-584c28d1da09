<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Complainant</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Against Individual</b></span>
          </li>
          <li class="breadcrumb-item active">Approve</li>
        </ul>
      </div>
      <div class="col-auto float-right ml-auto" *ngIf="fromFordCasePage==false">
        <a class="btn add-btn" routerLink="/complainant-statement/against-individual/list"><i class="fa fa-share"></i>
          Back To
          List</a>
      </div>
      <div class="col-auto float-right ml-auto" *ngIf="fromFordCasePage==true">
        <a class="btn add-btn" routerLink="/complainant-statement/forwarded-cases"><i class="fa fa-share"></i> Back To
          List</a>
      </div>
    </div>
  </div>
  <!-- /Page Header -->





  <div class="row">
    <div class="col-lg-12">
      <div class="card customCard">

        <div class="card-body">
          <div class="row">
            <div class="col-md-12">
              <table class="summaryTable col-md-12">
                <tr>
                  <td><b>Reference Code</b></td>
                  <td>{{myData?.code}}</td>
                  <td><b>Occurrence Date</b></td>
                  <td>{{myData.occurrenceDate?myData.occurrenceDate:'-'}}
                  </td>
                  <td><b>Submission Date</b></td>
                  <td>{{myData.submissionDate?myData.submissionDate:'-'}}</td>

                </tr>

                <tr>
                  <td><b>Complainant</b></td>
                  <td>{{ myData.complainant?myData.complainant:'-' }}
                  </td>

                  <td><b>Occurrence Time</b></td>
                  <td>{{myData.occurrenceTime?myData.occurrenceTime:'-'}}
                  </td>
                  <td><b>Submission Time</b></td>
                  <td>{{myData.submissionTime?myData.submissionTime:'-'}}</td>
                </tr>

                <tr>
                  <td><b>Invest./Section Incharge</b></td>
                  <td>{{myData?.investInchIds}}</td>

                  <td><b>Attachment</b></td>
                  <td colspan="4"><span data-toggle="modal" data-target="#doc_modal"
                      (click)="getAttachmentUrl(myData.attachmentUrl)" *ngIf="myData?.attachmentUrl"
                      class="text-primary" style="cursor: pointer;">Click
                      Here</span></td>

                </tr>

                <tr>

                  <td><b>Description</b></td>
                  <td colspan="6">{{myData?.description}}</td>

                </tr>

              </table>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>



  <div *ngFor="let df of  myData.defendents;let sl = index">

    <h4 style="color:#25B6B2;"> {{sl+1}}. <b>Defendant:</b> {{df.empName}} ({{df.empCode}})</h4>

    <hr>

    <div class="row">
      <div class="col-lg-12">
        <div class="card customCard">
          <div class="card-body customCard">
            <div class="row">
              <div class="col-md-12">
                <table class="summaryTable col-md-12">
                  <thead>
                    <tr>
                      <th><b>Designation</b></th>
                      <th>{{df.dgOrder}}-{{df.designation}}</th>
                      <th><b>In-Charge</b></th>
                      <th> {{df.incharge}}</th>
                      <th><b>Concern HR</b></th>
                      <th>{{df.concernHr}}</th>
                    </tr>
                  </thead>

                  <tr>
                    <td><b>Position</b></td>
                    <td colspan="6">
                      {{df.operatingUnit}}.{{df.product}}.{{df.department}}.{{df.section}}.{{df.subSection}}.{{df.team}}
                    </td>
                  </tr>

                  <tr>
                    <td><b>Approval Status</b></td>
                    <td> <span *ngIf="df.approvalStatus=='Submitted'" class="text-primary">Submitted</span>
                      <span *ngIf="df.approvalStatus=='Approved'" class="text-success">Approved</span>
                      <span *ngIf="df.approvalStatus=='Recommended'" class="text-warning">Recommended</span>
                      <span *ngIf="df.approvalStatus=='Rejected'" class="text-danger">Rejected</span>
                    </td>
                    <td><b>Job Location</b></td>
                    <td>{{df.jobLocation}}</td>

                    <td><b>Product Status</b></td>
                    <td> {{df.productStatus}}</td>

                  </tr>

                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-lg-12">
        <div class="card customCard">

          <div class="card-body customCard">
            <div class="row">
              <div class="col-md-12">
                <div id="sectionPrint"
                  [ngClass]="{'col-md-12': actionList2[df.id]?.length === 0, 'col-md-8': actionList2[df.id]?.length>0 }">
                  <fieldset class="row fieldsetBorder logBox ">
                    <legend class="bg-warning">Approval Status of {{df.empName}} ({{df.empCode}})</legend>
                    <table class="table table-striped custom-table datatable">
                      <thead>
                        <tr>
                          <th>SL</th>
                          <th>Step Name</th>
                          <th>Approver</th>
                          <th>Sign By</th>
                          <th>Designation</th>
                          <th>Action Status</th>
                          <th>Update Date</th>
                          <th>Remarks</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let dtl of df.history; let i = index">
                          <td>{{i+1}}</td>
                          <td>{{dtl.approvalStep?.approvalGroupName}}</td>
                          <td> {{ dtl?.approvalStepApproverEmp?.loginCode}} - {{
                            dtl?.approvalStepApproverEmp?.displayName}}</td>
                          <td>{{dtl.actionTaker?dtl.actionTaker:'-'}}</td>
                          <td>{{dtl.designation?dtl.designation:'-'}}</td>
                          <td>{{dtl.actionStatus?dtl.actionStatus:'-'}}</td>
                          <td>{{dtl.updateDateTime}}</td>
                          <td>{{dtl.remarks?dtl.remarks:'-'}}</td>
                        </tr>
                      </tbody>
                    </table>

                  </fieldset>
                </div>

                <div class="col-4" *ngIf="df?.inMyPath==true  && actionList2[df.id]?.length>0">

                  <form [formGroup]="formOfAction">
                    <fieldset class="row fieldsetBorder logBox ">
                      <legend class="bg-warning">Take Action</legend>
                      <label class="col-form-label col-md-4 val-required">Status</label>
                      <div class="col-md-8">
                        <select class="select form-control" (change)="setApprovalStepActionId(
                      df.id,
                      $event.target.value
                      )" formControlName="approvalStepAction">
                          <option value="">Select Action</option>
                          <option *ngFor="let data of actionList2[df.id]" [value]="data.id">
                            {{data.activityStatusTitle}}</option>
                        </select>
                      </div>
                      <div class="col-md-12 text-right p-2">
                        <button class="btn btn-sm btn-primary"
                          (click)="savehTRY(df.id,df.hrCrEmpId,df.thisNode,df.nextNode,df.approvalStatus)">Save</button>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
<!-- /Page Content -->


<!-- Documents Modal -->

<div id="doc_modal" class="modal custom-modal fade " role="dialog">
  <div class="modal-dialog modal-dialog-centered docModal" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Evidence Attachment</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>

      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-12">
            <div class="card customCard">
              <div class="card-body">
                <div class="table-wrapper-scroll-y attn-modal-scrollbar">
                  <table class="table table-striped custom-table  table-bordered">
                    <thead>
                      <tr>
                        <th class="text-center">SL</th>
                        <th class="text-center">Attachment</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let val of attachmentUrl ;let i = index" [class.active]="i == currentIndex">
                        <td class="text-center">{{i+1}}</td>
                        <td class="text-center" style="cursor: pointer;" (click)="redirectToImage(val)">{{val}}<br>
                        </td>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!--  Modal End -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>