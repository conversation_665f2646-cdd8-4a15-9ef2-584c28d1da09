import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-against-dept-sec-create',
  templateUrl: './against-dept-sec-create.component.html',
  styleUrls: ['./against-dept-sec-create.component.css']
})
export class AgainstDeptSecCreateComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  private uploadForm: FormGroup;
  public operating_units = [];
  public product = [];
  public department = [];
  public sections = [];
  public subSections = [];
  public teams = [];
  public subTeams = [];

  isDocSelect = false;
  configDDL: any;

  imgSrc: any;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 2000;
  numberOfChar = 0;

  maxNumOfChar2 = 2000;
  numberOfChar2 = 0;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private loginService: LoginService
  ) {
    this._initConfigDDL();
    this._customInitLoadData();

  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadAllOperatingUnits();
  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      // submissionDate: ["", [Validators.required]],
      // submissionTime: ["", [Validators.required]],
      occuranceDate: [""],
      occuranceTime: [""],
      // descriptionBangla: ["", [Validators.required]],
      description: ["", [Validators.required]],
      allOrgMst: [""],
      complaintEmpName: ["", [Validators.required]],
      allOrgMstOperatinUnit: ["", [Validators.required]],
      allOrgMstProduct: ["", [Validators.required]],
      allOrgMstDepartment: [""],
      allOrgMstSection: [""],
      allOrgMstSubSection: [""],
      allOrgMstTeam: [""],
      allOrgMstSubTeam: [""],
      deductAmount: [""]
    });


    let userName = this.loginService.getUser().displayName;
    this.myForm.controls['complaintEmpName'].setValue(userName);

    this.uploadForm = this.formBuilder.group({
      fileUrl: [""],
    });

  }


  // Textarea field character Count

  CharCount(event: any): void {
    this.numberOfChar = event.target.value.length;

    if (this.numberOfChar > this.maxNumOfChar) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar);
      this.numberOfChar = this.maxNumOfChar;
    }
  }

  CharCount2(event: any): void {
    this.numberOfChar2 = event.target.value.length;

    if (this.numberOfChar2 > this.maxNumOfChar2) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar2);
      this.numberOfChar2 = this.maxNumOfChar2;
    }
  }



  myFormSubmit() {

    if (this.myForm.invalid) {
      this.toastr.info("Please insert valid data")
      return;
    }

    // if(this.myForm.controls["allOrgMstDepartment"].value == "" && this.myForm.controls["allOrgMst"].value == "") {
    //   this.toastr.info("please insert department or section value");
    //   return;
    // }

    const hrCrEmp = Object.assign(this.myForm.value, {
      allOrgMst: this.getAllOrgMst.value ? { id: this.getAllOrgMst.value } : null,
      allOrgMstOperatinUnit: this.getAllOrgMstOperatinUnit.value ? { id: this.getAllOrgMstOperatinUnit.value } : null,
      allOrgMstProduct: this.getAllOrgMstProduct.value ? { id: this.getAllOrgMstProduct.value } : null,
      allOrgMstDepartment: this.getAllOrgMstDepartment.value ? { id: this.getAllOrgMstDepartment.value } : null,
      allOrgMstSection: this.getAllOrgMstSection.value ? { id: this.getAllOrgMstSection.value } : null,
      allOrgMstSubSection: this.getAllOrgMstSubSection.value ? { id: this.getAllOrgMstSubSection.value } : null,
      allOrgMstTeam: this.getAllOrgMstTeam.value ? { id: this.getAllOrgMstTeam.value } : null,
      allOrgMstSubTeam: this.getAllOrgMstSubTeam.value ? { id: this.getAllOrgMstSubTeam.value } : null
    });

    let apiURL = this.baseUrl + "/complainDepartment/save";

    let formData: any = {};
    formData = hrCrEmp;


    // process date
    formData.occuranceDate = (formData.occuranceDate) ? this.commonService.format_Date_Y_M_D(formData.occuranceDate) : null;


    this.spinnerService.show();

    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.toastr.success(response.message);
        this.uploadFiles(response?.data?.id);
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();

      }
    );

  }

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      this.isDocSelect = true;
      let fileList = event.target.files;

      for (let i = 0; i < fileList.length; i++) {
        const reader = new FileReader();
        reader.readAsDataURL(fileList[i]);
        reader.onload = (e) => (this.imgSrc = reader.result);
      }

      this.uploadForm.get("fileUrl").setValue(fileList);
    }
    else {
      this.isDocSelect = false;
    }
  }

  // uploadFiles(id) {

  //   if (this.isDocSelect) {

  //     let apiURL = this.baseUrl + "/complainDepartment/uploadFile/" + id;

  //     let formData = new FormData();

  //     for (let i = 0; i < this.uploadForm.get("fileUrl").value.length; i++) {
  //       formData.append("files", this.uploadForm.get("fileUrl").value[i]);
  //     }
  //     this.commonService.sendPostRequest(apiURL, formData).subscribe(
  //       (response: any) => {
  //         this.toastr.success("Files Uploaded Successfully");
  //         this.router.navigate(['/complainant-statement/against-dept-sec/list']);
  //         this.spinnerService.hide();
  //       },
  //       (error) => {
  //         this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
  //         this.spinnerService.hide();
  //       }
  //     );


  //   }
  //   else {
  //     this.spinnerService.hide();
  //     this.router.navigate(['/complainant-statement/against-dept-sec/list']);
  //   }


  // }


  uploadFiles(id) {

    if (this.isDocSelect) {

      let formData = new FormData();

      for (let i = 0; i < this.uploadForm.get("fileUrl").value.length; i++) {
        formData.append("files", this.uploadForm.get("fileUrl").value[i]);
      }
      let apiURL = this.baseUrl + "/complainDepartment/uploadFile/" + id;

      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (response: any) => {
          this.spinnerService.hide();
          this.toastr.success("Document Uploaded Successfully");
          this.router.navigate(["/complainant-statement/against-dept-sec/list"], { relativeTo: this.route });
        },
        (error) => {
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
          this.router.navigate(["/complainant-statement/against-dept-sec/list"], { relativeTo: this.route });
          this.spinnerService.hide();
        }
      )
    }
    else {
      this.spinnerService.hide();
      this.router.navigate(["/complainant-statement/against-dept-sec/list"], { relativeTo: this.route });
    }
  }

  //-----------Get Relational Object Id ------------------

  get getAllOrgMst() {
    return this.myForm.get("allOrgMst");
  }

  get getAllOrgMstOperatinUnit() {
    return this.myForm.get("allOrgMstOperatinUnit");
  }

  get getAllOrgMstProduct() {
    return this.myForm.get("allOrgMstProduct");
  }

  get getAllOrgMstDepartment() {
    return this.myForm.get("allOrgMstDepartment");
  }

  get getAllOrgMstSubSection() {
    return this.myForm.get("allOrgMstSubSection");
  }

  get getAllOrgMstSection() {
    return this.myForm.get("allOrgMstSection");
  }

  get getAllOrgMstTeam() {
    return this.myForm.get("allOrgMstTeam");
  }

  get getAllOrgMstSubTeam() {
    return this.myForm.get("allOrgMstSubTeam");
  }

  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {
    console.log("...");
    console.log("ddlActiveFieldName:" + activeFieldNameDDL);
    console.log("dataGetApiPathDDL:" + dataGetApiPathDDL);
    console.log(event.target);

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------



  loadAllOperatingUnits() {
    let orgType = "OPERATING_UNIT";
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType;
    let queryParams: any = {};
    this.product = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.operating_units = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllProducts(event) {
    let orgType = "PRODUCT";
    let operatingUnitIdForAPI = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + operatingUnitIdForAPI;
    let queryParams: any = {};
    this.department = [];


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.product = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }


  loadAllDepartments(event) {
    let orgType = "DEPARTMENT";
    let productUnitId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + productUnitId;
    let queryParams: any = {};
    this.sections = [];


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.department = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }



  loadAllSections(event) {
    let orgType = "SECTION";
    let departmentId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + departmentId;
    let queryParams: any = {};
    this.subSections = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.sections = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllSubSections(event) {
    let orgType = "SUB_SECTION";
    let sectionId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + sectionId;
    let queryParams: any = {};
    this.teams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subSections = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllTeams(event) {
    let orgType = "TEAM";
    let subSectionId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + subSectionId;
    let queryParams: any = {};
    this.subTeams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.teams = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllSubTeams(event) {
    let orgType = "SUB_TEAM";
    let teamId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + teamId;
    let queryParams: any = {};

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subTeams = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }




}
