import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-against-dept-sec-edit',
  templateUrl: './against-dept-sec-edit.component.html',
  styleUrls: ['./against-dept-sec-edit.component.css']
})
export class AgainstDeptSecEditComponent implements OnInit {
  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  private uploadForm: FormGroup;
  public operating_units = [];
  public product = [];
  public department = [];
  public sections = [];
  public subSections = [];
  public teams = [];
  public subTeams = [];
  public againtsDptSecData: any;


  configDDL: any;
  imgSrc: any;
  isDocSelect = false;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 2000;
  numberOfChar = 0;

  maxNumOfChar2 = 2000;
  numberOfChar2 = 0;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private loginService: LoginService
  ) {
    this._initConfigDDL();
    this._customInitLoadData();

  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadAllOperatingUnits();
    this.loadAgainstDptData();
  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      id: [""],
      submissionDate: [""],
      submissionTime: [""],
      occuranceDate: [""],
      occuranceTime: [""],
      // descriptionBangla: ["", [Validators.required]],
      description: ["", [Validators.required]],
      allOrgMst: [""],
      complaintEmpName: ["", [Validators.required]],
      allOrgMstOperatinUnit: ["", [Validators.required]],
      allOrgMstProduct: ["", [Validators.required]],
      allOrgMstDepartment: [""],
      allOrgMstSubSection: [""],
      allOrgMstTeam: [""],
      allOrgMstSubTeam: [""],
      deductAmount: [""]

    });

    let userName = this.loginService.getUser().displayName;
    this.myForm.controls['complaintEmpName'].setValue(userName);

    this.uploadForm = this.formBuilder.group({
      fileUrl: [""],
    });

  }


  // Textarea field character Count

  CharCount(event: any): void {
    this.numberOfChar = event.target.value.length;

    if (this.numberOfChar > this.maxNumOfChar) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar);
      this.numberOfChar = this.maxNumOfChar;
    }
  }

  CharCount2(event: any): void {
    this.numberOfChar2 = event.target.value.length;

    if (this.numberOfChar2 > this.maxNumOfChar2) {
      event.target.value = event.target.value.slice(0, this.maxNumOfChar2);
      this.numberOfChar2 = this.maxNumOfChar2;
    }
  }



  myFormSubmit() {

    if (this.myForm.invalid) {
      this.toastr.info("Please insert valid data")
      return;
    }

    const hrCrEmp = Object.assign(this.myForm.value, {
      allOrgMst: this.getAllOrgMst.value ? { id: this.getAllOrgMst.value } : null,
      allOrgMstOperatinUnit: this.getAllOrgMstOperatinUnit.value ? { id: this.getAllOrgMstOperatinUnit.value } : null,
      allOrgMstProduct: this.getAllOrgMstProduct.value ? { id: this.getAllOrgMstProduct.value } : null,
      allOrgMstDepartment: this.getAllOrgMstDepartment.value ? { id: this.getAllOrgMstDepartment.value } : null,
      allOrgMstSubSection: this.getAllOrgMstSubSection.value ? { id: this.getAllOrgMstSubSection.value } : null,
      allOrgMstTeam: this.getAllOrgMstTeam.value ? { id: this.getAllOrgMstTeam.value } : null,
      allOrgMstSubTeam: this.getAllOrgMstSubTeam.value ? { id: this.getAllOrgMstSubTeam.value } : null
    });

    let apiURL = this.baseUrl + "/complainDepartment/update";

    let formData: any = {};
    formData = hrCrEmp;

    // process date
    formData.occuranceDate = (formData.occuranceDate) ? this.commonService.format_Date_Y_M_D(formData.occuranceDate) : null;


    this.spinnerService.show();

    this.commonService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        console.log(response);

        this.toastr.success(response.message);
        this.uploadDeptImage(response?.data?.id);
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");

      }
    );

  }

  onFileSelect(event) {
    if (event.target.files.length > 0) {
      this.isDocSelect = true;
      let fileList = event.target.files;

      for (let i = 0; i < fileList.length; i++) {
        const reader = new FileReader();
        reader.readAsDataURL(fileList[i]);
        reader.onload = (e) => (this.imgSrc = reader.result);
      }

      this.uploadForm.get("fileUrl").setValue(fileList);
    }
    else {
      this.isDocSelect = false;
    }
  }


  uploadDeptImage(id) {

    const formData = new FormData();
    if (this.isDocSelect) {
      formData.append("file", this.uploadForm.get("fileUrl").value);
      formData.append("type", "file");

      let apiURL = this.baseUrl + "/complainDepartment/uploadFile/" + id;

      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (response: any) => {
          this.spinnerService.hide();
          this.router.navigate(["/complainant-statement/against-dept-sec/list"], { relativeTo: this.route });
        },
        (error) => {
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
          this.router.navigate(["/complainant-statement/against-dept-sec/list"], { relativeTo: this.route });
          this.spinnerService.hide();
        }
      )
    }
    else {
      this.spinnerService.hide();
      this.router.navigate(["/complainant-statement/against-dept-sec/list"], { relativeTo: this.route });
    }
  }

  //-----------Get Relational Object Id ------------------

  get getAllOrgMst() {
    return this.myForm.get("allOrgMst");
  }

  get getAllOrgMstOperatinUnit() {
    return this.myForm.get("allOrgMstOperatinUnit");
  }

  get getAllOrgMstProduct() {
    return this.myForm.get("allOrgMstProduct");
  }

  get getAllOrgMstDepartment() {
    return this.myForm.get("allOrgMstDepartment");
  }

  get getAllOrgMstSubSection() {
    return this.myForm.get("allOrgMstSubSection");
  }

  get getAllOrgMstTeam() {
    return this.myForm.get("allOrgMstTeam");
  }

  get getAllOrgMstSubTeam() {
    return this.myForm.get("allOrgMstSubTeam");
  }

  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {
    console.log("...");
    console.log("ddlActiveFieldName:" + activeFieldNameDDL);
    console.log("dataGetApiPathDDL:" + dataGetApiPathDDL);
    console.log(event.target);

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------



  loadAllOperatingUnits() {
    let orgType = "OPERATING_UNIT";
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType;
    let queryParams: any = {};
    this.product = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.operating_units = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllProducts(event) {
    let orgType = "PRODUCT";
    let operatingUnitIdForAPI = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + operatingUnitIdForAPI;
    let queryParams: any = {};
    this.department = [];


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.product = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }


  loadAllDepartments(event) {
    let orgType = "DEPARTMENT";
    let productUnitId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + productUnitId;
    let queryParams: any = {};
    this.sections = [];


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.department = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }



  loadAllSections(event) {
    let orgType = "SECTION";
    let subSectionId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + subSectionId;
    let queryParams: any = {};
    this.subSections = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.sections = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllSubSections(event) {
    let orgType = "SUB_SECTION";
    let teamId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + teamId;
    let queryParams: any = {};
    this.teams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subSections = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllTeams(event) {
    let orgType = "TEAM";
    let subSectionId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + subSectionId;
    let queryParams: any = {};
    this.subTeams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.teams = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  loadAllSubTeams(event) {
    let orgType = "SUB_TEAM";
    let teamId = event.target.value;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + teamId;
    let queryParams: any = {};

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subTeams = response;
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }


  getFirstPartOfTimeString(myString: any) {

    const indexOfColon = myString.indexOf(':');

    if (indexOfColon !== -1) {
      const substring = myString.substring(0, indexOfColon);
      const size = substring.length;
      if (size !== 2) {

        const finalString = "0" + substring;
        return finalString;
      }

      return substring;

    }

  }

  getSecondPartOfTimeString(myString: any) {

    const substring = myString.split(":")[1];

    const size = substring.length;
    if (size !== 2) {

      const finalString = "0" + substring;
      return finalString;
    }
    return substring;

  }


  loadAgainstDptData() {
    let id = this.route.snapshot.params.id;

    let apiURL = this.baseUrl + "/complainDepartment/get/" + id;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.againtsDptSecData = response.data;

        if (response.data.submissionTime != null) {
          let arrstring: any[] = response.data.submissionTime;
          let strinTypesubmissionTime = arrstring[0] + ":" + arrstring[1];
          let finalsubmissionTime = this.getFirstPartOfTimeString(strinTypesubmissionTime) + ":" + this.getSecondPartOfTimeString(strinTypesubmissionTime);
          this.againtsDptSecData.submissionTime = finalsubmissionTime;
        }

        if (response.data.occuranceTime != null) {
          let arrstring: any[] = response.data.occuranceTime;
          let strinTypeoccuranceTime = arrstring[0] + ":" + arrstring[1];
          let finaloccuranceTime = this.getFirstPartOfTimeString(strinTypeoccuranceTime) + ":" + this.getSecondPartOfTimeString(strinTypeoccuranceTime);
          this.againtsDptSecData.occuranceTime = finaloccuranceTime;
        }

        //set date
        this.againtsDptSecData.occuranceDate = this.datePipe.transform(response?.data?.occuranceDate, "dd-MM-yyyy").toString().slice(0, 10);


        this.patchWithFormValues(this.againtsDptSecData);
      }
    )
  }

  patchWithFormValues(data) {
    this.myForm.patchValue(data);
    this.myForm.controls["allOrgMstOperatinUnit"].setValue(data.allOrgMstOperatinUnit.id);
    this.patchWithProductValues(data.allOrgMstOperatinUnit.id, data);
  }

  patchWithProductValues(operatingUnitId, data) {
    let orgType = "PRODUCT";
    let operatingUnitIdForAPI = operatingUnitId;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + operatingUnitIdForAPI;
    let queryParams: any = {};
    this.department = [];


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.product = response;
        this.myForm.controls["allOrgMstProduct"].setValue(data?.allOrgMstProduct?.id);
        this.patchWithDepartmentValues(data?.allOrgMstProduct?.id, data);
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }

  patchWithDepartmentValues(productId, data) {
    let orgType = "DEPARTMENT";
    let productUnitId = productId;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + productUnitId;
    let queryParams: any = {};
    this.sections = [];


    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.department = response;
        this.myForm.controls["allOrgMstDepartment"].setValue(data?.allOrgMstDepartment?.id);
        this.patchWitSectionValues(data?.allOrgMstDepartment?.id, data);
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );
  }


  patchWitSectionValues(department, data) {

    let orgType = "SECTION";
    let departmentId = department;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + departmentId;
    let queryParams: any = {};
    this.subSections = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.sections = response;
        this.myForm.controls["allOrgMst"].setValue(data?.allOrgMst?.id);
        this.patchWithSubSectionValues(data?.allOrgMst?.id, data);
        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );

  }

  patchWithSubSectionValues(section, data) {

    let orgType = "SUB_SECTION";
    let sectionId = section;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + sectionId;
    let queryParams: any = {};
    this.teams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subSections = response;

        // if (data.allOrgMstSubSection != null) {
        this.myForm.controls["allOrgMstSubSection"].setValue(data?.allOrgMstSubSection?.id);
        this.patchWithTeamValues(data?.allOrgMstSubSection?.id, data);

        // }

        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );

  }

  patchWithTeamValues(subSection, data) {

    let orgType = "TEAM";
    let subSectionId = subSection;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + subSectionId;
    let queryParams: any = {};
    this.subTeams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.teams = response;

        if (data.allOrgMstTeam != null) {
          this.myForm.controls["allOrgMstTeam"].setValue(data?.allOrgMstTeam?.id);
          this.patchWithSubTeamValues(data?.allOrgMstTeam?.id, data)
        }

        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );

  }

  patchWithSubTeamValues(team, data) {

    let orgType = "SUB_TEAM";
    let teamId = team;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + teamId;
    let queryParams: any = {};
    // this.subTeams = [];

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subTeams = response;

        if (data.allOrgMstSubTeam != null) {
          this.myForm.controls["allOrgMstSubTeam"].setValue(data?.allOrgMstSubTeam?.id);
        }

        this.spinnerService.hide();
      },
      (error) => {
        console.log(error)
      }
    );

  }

}
