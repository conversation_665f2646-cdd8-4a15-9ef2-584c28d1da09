<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Against Dept-Sec</b></span></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/against-dept-sec/list"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->
    <div class="card customCard">
        <div class="card-body">
            <form novalidate (ngSubmit)="myFormSubmit()" [formGroup]="myForm">
                <div class="row">

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Complainant</label>
                            <input class="form-control" formControlName="complaintEmpName" class="form-control"
                                type="text" readonly>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class=" val-required">Operating Unit</label>

                            <select id="operatingUnitSearch" formControlName="allOrgMstOperatinUnit"
                                class="select form-control" (change)="loadAllProducts($event)">
                                <option value="">Select Operating Unit</option>
                                <option *ngFor="let data of operating_units" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label class=" val-required">Product</label>

                            <select id="productSearch" class="select form-control" formControlName="allOrgMstProduct"
                                (change)="loadAllDepartments($event)">
                                <option value="">Select Product</option>
                                <option *ngFor="let data of product" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label>Department</label>

                            <select id="productSearch" formControlName="allOrgMstDepartment" class="select form-control"
                                (change)="loadAllSections($event)">
                                <option value="">Select Department</option>
                                <option *ngFor="let data of department" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label>Section</label>

                            <select id="productSearch" class="select form-control" formControlName="allOrgMst"
                                (change)="loadAllSubSections($event)">
                                <option value="">Select Section</option>
                                <option *ngFor="let data of sections" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label>Sub Section</label>

                            <select id="productSearch" class="select form-control" formControlName="allOrgMstSubSection"
                                (change)="loadAllTeams($event)">
                                <option value="">Select Sub Section</option>
                                <option *ngFor="let data of subSections" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label>Team</label>

                            <select id="productSearch" class="select form-control" formControlName="allOrgMstTeam"
                                (change)="loadAllSubTeams($event)">
                                <option value="">Select Team</option>
                                <option *ngFor="let data of teams" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group ">
                            <label>Sub Team</label>
                            <select id="productSearch" class="select form-control" formControlName="allOrgMstSubTeam">
                                <option value="">Select Sub Team</option>
                                <option *ngFor="let data of subTeams" [value]='data.id'>{{data.title}}
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="val-required">Submission Date</label>

                            <div>
                                <input class="form-control" formControlName="submissionDate" class="form-control"
                                    type="date" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Submission Time</label>

                            <div>
                                <input class="form-control" formControlName="submissionTime" class="form-control"
                                    type="time" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Occurrence Date</label>
                            <div class="cal-icon">
                                <input class="form-control floating datetimepicker" formControlName="occuranceDate"
                                    bsDatepicker type="text" placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true }">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Occurrence Time</label>

                            <div>
                                <input class="form-control" formControlName="occuranceTime" class="form-control"
                                    type="time">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Deduct Amount (BDT)</label>
                            <div>
                                <input class="form-control" formControlName="deductAmount" class="form-control"
                                    type="number">
                            </div>

                        </div>
                    </div>

                    <!-- <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Description (Bangla)</label>
                            <span class="float-right">
                                {{ numberOfChar }} / {{maxNumOfChar}}
                            </span>
                            <textarea type="text" class="form-control" formControlName="descriptionBangla"
                                (keyup)="CharCount($event)" rows="1"></textarea>

                        </div>
                    </div> -->

                    <div class="col-md-3">
                        <div class="form-group">
                            <label class=" val-required">Description (Bangla/English)</label>
                            <span class="float-right">
                                {{ numberOfChar2 }} / {{maxNumOfChar2}}
                            </span>
                            <textarea type="text" class="form-control" formControlName="description"
                                (keyup)="CharCount2($event)" rows="1"></textarea>

                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="formFileMultiple" class="form-label">Multiple Evidence Attachment</label>
                            <input class="form-control" type="file" name="fileUrl" id="formFileMultiple"
                                (change)="onFileSelect($event)" />
                        </div>
                    </div>

                </div>


                <div class="text-right mt-2">
                    <button type="button" id="reset" class="btn btn-secondary btn-ripple">
                        <i class="fa fa-undo" aria-hidden="true"></i> Reset
                    </button>
                    &nbsp; &nbsp; &nbsp;
                    <button type="submit" id="submit" class="btn btn-primary btn-ripple" [disabled]="!myForm.valid">
                        <i class="fa fa-check" aria-hidden="true"></i> Submit
                        &nbsp;&nbsp;&nbsp;
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>