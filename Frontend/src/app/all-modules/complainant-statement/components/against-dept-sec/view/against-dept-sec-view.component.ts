import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-against-dept-sec-view',
  templateUrl: './against-dept-sec-view.component.html',
  styleUrls: ['./against-dept-sec-view.component.css']
})
export class AgainstDeptSecViewComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  public operating_units = [];
  public product = [];
  public department = [];
  public sections = [];
  public subSections = [];
  public teams = [];
  public subTeams = [];
  public myData: any;
  listData4: any = [];
  listData3: any = [];
  public actionForm: FormGroup;


  configDDL: any;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 1000;
  numberOfChar = 0;

  maxNumOfChar2 = 1000;
  numberOfChar2 = 0;


  empDetailsModal: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private loginService: LoginService
  ) {
    this._initConfigDDL();
    this._customInitLoadData();

  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadAgainstDptData();
  }

  initializeForm() {
    this.actionForm = this.formBuilder.group({
      id: [""],
      approvalStepAction: ["", Validators.required],
      remarks: [""],
    });
  }


  //================== HANDLE DETAILS MODAL =================

  empDetails(param: any) {
    this.empDetailsModal = true;
  }

  handleModalData(param: any) {
    this.empDetailsModal = false;
  }

  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }

  // =========================== LOAD ALL DATA =====================
  loadAgainstDptData() {
    let id = this.route.snapshot.params.id;

    let apiURL = this.baseUrl + "/complainDepartment/get/" + id;
    let queryParams: any = {};

    this.spinnerService.show();


    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.spinnerService.hide();
        this.myData = response.data;
        this.getApprovalStepAction();
        this.getSelfListData();
      }
    )
  }

  getApprovalStepAction() {
    let id = this.route.snapshot.params.id;

    let apiURL = this.baseUrl + "/approvalStepAction/getApprovalStepAction/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};

    queryParams = params;
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData3 = response;
      },
      (error) => {
        console.log(error)

      }
    );
  }

  private getUserQueryParams(): any {

    let params: any = {};

    params[`approvalProcess`] = "COMPLAINT_DEPTORSECTION";

    params[`nextApprovalNode`] = this.myData.approvalStep?.nextApprovalNode;

    params[`thisApprovalNode`] = this.myData.approvalStep?.thisApprovalNode;
    params[`appliedHrCrEmpId`] = this.myData.hrCrEmp.id;

    return params;
  }

  getSelfListData() {
    let id = this.route.snapshot.params.id;
    let apiURL = this.baseUrl + "/approvalProcTnxHtry/getSelfApprovalProcTnxList/" + id;

    let queryParams: any = {};
    const params = this.getUserQueryParams();
    queryParams = params;

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData4 = response;
      },
      (error) => {
        console.log(error)
      }
    );

  }

  tackAction() {

    if (this.actionForm.invalid) {
      return;
    }
    let id = this.route.snapshot.params.id;

    let obj: any;

    if (this.listData3[0].isParallel && this.listData3[0].isParallel == true) {

      obj = Object.assign(this.actionForm.value, {
        referenceId: id,
        referenceEntity: "COMPLAINT_DEPTORSECTION" + "/" + this.listData3[0].thisApprovalNode + "/" + this.listData3[0].nextApprovalNode,
        approvalStepAction: this.get.value ? { id: this.get.value } : null,
        approvalStepApproverEmp: null,
        approvalStepApprover: null,
        approvalStep: { id: this.listData3[0].stepId },
        approvalProcess: { id: this.listData3[0].processId },
        isParallelApprove: true,
      });

    } else {

      obj = Object.assign(this.actionForm.value, {
        referenceId: id,
        referenceEntity: "COMPLAINT_DEPTORSECTION" + "/" + this.myData.approvalStep.thisApprovalNode + "/" + this.myData.approvalStep.nextApprovalNode,
        approvalStepAction: this.get.value ? { id: this.get.value } : null,
      });

    }


    let apiURL = this.baseUrl + "/approvalProcTnxHtry/edit";
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;
    let formData: any = {};
    formData = obj;

    this.spinnerService.show();

    this.commonService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.spinnerService.hide();
        this.listData4 = [];
        this.listData3 = [];
        this.loadAgainstDptData();

        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.info(error?.error?.message || "Something went wrong. Please try again.")

      }
    );
  }

  resetFormValues() {
    this.actionForm.reset();
  }

  get get() {
    return this.actionForm.get("approvalStepAction");
  }



  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(
    event,
    activeFieldNameDDL,
    dataGetApiPathDDL,
    apiQueryFieldNameDDL
  ) {
    console.log("...");
    console.log("ddlActiveFieldName:" + activeFieldNameDDL);
    console.log("dataGetApiPathDDL:" + dataGetApiPathDDL);
    console.log(event.target);

    if (
      this.configDDL.activeFieldName &&
      this.configDDL.activeFieldName != activeFieldNameDDL
    ) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------

}
