.content {
    padding: 15px;
    font-size: 15px;
}

.summaryTable tr td {
	border: 1px solid #dddddd;
	padding: 5px;
}

/* ================= Default Template Color ================== */
xx-.form-control {
    border-color: #e3e3e3;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #ccc;
    box-shadow: none;
    outline: 0 none;
}



/* ================= Purpale Color =========================== */
input.form-control,
select.form-control,
textarea.form-control {
	border-color: #4BA1D9;
	border-left: 3px solid #4BA1D9;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
    border-color: #705CBA;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================= Green Color ============================= */
xx-input.form-control {
    border-color: #d4cdcd;
    border-left: 3px solid green;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #37a000;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================== Blue Color ============================== */
xx-input.form-control {
    border-color: #66afe9;
    border-left: 3px solid #66afe9;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}


input.ng-invalid {
    border-left: 1.5px solid red;
}

select.ng-invalid {
    border-left: 1.5px solid red;
}

textarea.ng-invalid {
    border-left: 1.5px solid red;
}

input.ng-valid {
    border-left: 2px solid green
}

select.ng-valid {
    border-left: 2px solid green;
}

textarea.ng-valid {
    border-left: 2px solid green;
}

.content {
    padding: 15px;
    font-size: 15px;
}



/* ================= Default Template Color ================== */
xx-.form-control {
    border-color: #e3e3e3;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #ccc;
    box-shadow: none;
    outline: 0 none;
}



/* ================= Purpale Color =========================== */
input.form-control,
select.form-control,
textarea.form-control {
	border-color: #4BA1D9;
	border-left: 3px solid #4BA1D9;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
    border-color: #705CBA;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================= Green Color ============================= */
xx-input.form-control {
    border-color: #d4cdcd;
    border-left: 3px solid green;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #37a000;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}



/* ================== Blue Color ============================== */
xx-input.form-control {
    border-color: #66afe9;
    border-left: 3px solid #66afe9;
    box-shadow: none;
}

xx-.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
    box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}


input.ng-invalid {
    border-left: 1.5px solid red;
}

select.ng-invalid {
    border-left: 1.5px solid red;
}

textarea.ng-invalid {
    border-left: 1.5px solid red;
}

input.ng-valid {
    border-left: 2px solid green
}

select.ng-valid {
    border-left: 2px solid green;
}

textarea.ng-valid {
    border-left: 2px solid green;
}


.content {
    padding: 15px;
}

fieldset.fieldsetBorder {
    border: 1px solid;
    border-color: rgba(31, 31, 31, 0.25);
    /*1f1f1f*/
    border-radius: 5px;
    margin: 1px;
    margin-bottom: 7px;
    padding-left: 5px;
}

fieldset.fieldsetWithoutBorder {
    margin-bottom: 7px;
}

fieldset legend {
    /* border: 1px solid; */
    /* border-color: rgba(31, 31, 31, 0.25); */
    width: auto;
    border-radius: 5px;
    font-size: 15px;
    padding-left: 5px;
    padding-right: 5px;
    margin-left: 7px;
}

.logBox .form-group {
    float: left;
}

.logBox .form-group label {
    min-width: 170px;
    margin-right: 5px;
    margin-left: 5px;
}

.logBox .form-group div {
    border: 1px solid;
    border-radius: 3px;
    border-color: rgba(31, 31, 31, 0.25);
    padding-left: 3px;
    padding-right: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    margin-right: 5px;
    margin-left: 5px;
    min-height: 25px;
}

.logBox {
    font-size: 13px;
}

tr {
    border-bottom: 1pt solid ghostwhite;
    line-height: 2.0;

}

.titleHeader {
    background-color: #82c1d4;
}