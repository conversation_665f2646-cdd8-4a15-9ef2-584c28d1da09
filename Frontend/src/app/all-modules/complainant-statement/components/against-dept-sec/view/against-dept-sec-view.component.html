<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Against Dept-Sec</b></span></li>
                    <li class="breadcrumb-item active">View</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/against-dept-sec/list"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>

    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">


                            <table class="summaryTable col-md-12">
                                <tr>
                                    <td><b>Reference Code</b></td>
                                    <td>{{myData?.code}}</td>
                                    <td><b>Operating Unit</b></td>
                                    <td>{{myData?.allOrgMstOperatinUnit?.title}}
                                    </td>
                                    <td><b>Sub Section</b></td>
                                    <td>{{myData?.allOrgMstSubSection?.title}}</td>

                                </tr>

                                <tr>
                                    <td><b>Employee</b></td>
                                    <td><span (click)="empDetails($event)" title="Click for Employee Details"
                                            style="cursor: pointer;">{{myData?.hrCrEmp?.displayName}}
                                            ({{myData?.hrCrEmp?.loginCode}})</span>

                                    </td>

                                    <td><b>Product</b></td>
                                    <td>{{myData?.allOrgMstProduct?.title}}
                                    </td>

                                    <td><b>Team</b></td>
                                    <td>{{myData?.allOrgMstTeam?.title}}</td>

                                </tr>

                                <tr>
                                    <td><b>Occurrence Date</b></td>
                                    <td>{{myData?.occuranceDate | date}} {{myData?.occuranceTime}}</td>

                                    <td><b>Department</b></td>
                                    <td>{{myData?.allOrgMstDepartment?.title}}</td>

                                    <td><b>Sub Team</b></td>
                                    <td>{{myData?.allOrgMstSubTeam?.title}}</td>

                                </tr>

                                <tr>
                                    <td><b>Submission Date</b></td>
                                    <td>{{myData?.submissionDate | date}} {{myData?.submissionTime}}</td>

                                    <td><b>Section</b></td>
                                    <td>{{myData?.allOrgMstSection?.title}}</td>

                                    <td></td>
                                    <td></td>

                                </tr>

                                <tr>
                                    <td><b>Approval Status</b></td>

                                    <td>


                                        <b *ngIf="myData?.approvalStatus === 'SUBMITTED' || myData?.approvalStatus === 'Submitted'"
                                            class="text-info">{{myData?.approvalStatus}}</b>

                                        <b *ngIf="myData?.approvalStatus === 'PARTIAL' || myData?.approvalStatus === 'Recommended'"
                                            class="text-warning">{{myData?.approvalStatus}}</b>

                                        <b *ngIf="myData?.approvalStatus === 'DENIED' || myData?.approvalStatus === 'Rejected'"
                                            class="text-danger">{{myData?.approvalStatus}}</b>

                                        <b *ngIf="myData?.approvalStatus === 'APPROVED' || myData?.approvalStatus === 'Approved'"
                                            class="text-success">{{myData?.approvalStatus}}</b>


                                    </td>

                                    <td><b>Deduct Amount</b></td>
                                    <td>{{myData?.deductAmount}}</td>

                                    <td><b>Attachment</b></td>
                                    <td><span (click)="redirectToImage(myData.fileUrl)" *ngIf="myData?.fileUrl"
                                            class="text-primary" style="cursor: pointer;">Click
                                            Here</span></td>

                                </tr>

                                <tr>
                                    <td><b>Description</b></td>
                                    <td colspan="5">{{myData?.description}}</td>
                                </tr>

                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>




    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">

                <div class="card-body customCard">

                    <div class="row">

                        <div [ngClass]="{'col-md-12': listData3?.length === 0, 'col-md-8': listData3?.length !== 0 }">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">Approval Status</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>SL</th>
                                            <th>Step Name</th>
                                            <th>Approver</th>
                                            <th>Sign By</th>
                                            <th>Designation</th>
                                            <th>Action Status</th>
                                            <th>Update Date</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of listData4;let i = index">
                                            <td>{{i+1}}</td>
                                            <td>{{thisObj.approvalStep?.approvalGroupName}}</td>
                                            <td> {{ thisObj?.approvalStepApproverEmp?.loginCode}} - {{
                                                thisObj?.approvalStepApproverEmp?.displayName}}
                                            </td>
                                            <td>{{thisObj.actionTaker?thisObj.actionTaker:'-'}}</td>
                                            <td>{{thisObj.designation?thisObj.designation:'-'}}</td>
                                            <td>{{thisObj.actionStatus?thisObj.actionStatus:'-'}}</td>
                                            <td>{{thisObj.updateDateTime}}</td>
                                            <td>{{thisObj.remarks?thisObj.remarks:'-'}}</td>

                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>
                        <div class="col-4" *ngIf="listData3?.length > 0">
                            <form novalidate (ngSubmit)="tackAction()" [formGroup]="actionForm">
                                <fieldset class="row fieldsetBorder logBox ">
                                    <legend class="bg-warning">Take Action</legend>


                                    <label class="col-form-label col-md-3">Status</label>
                                    <div class="col-md-8">
                                        <select class="select form-control" formControlName="approvalStepAction">
                                            <option value="">Select Action</option>
                                            <option *ngFor="let data of listData3" [ngValue]='data.id'>
                                                {{data.activityStatusTitle}}
                                            </option>
                                        </select>

                                    </div>
                                    <br><br>

                                    <label class="col-form-label col-md-3">Remarks</label>
                                    <div class="col-md-8">
                                        <textarea formControlName="remarks" class="form-control mb-3"></textarea>

                                    </div>


                                    <div class="col-md-9"></div>
                                    <div class="col-md-3">
                                        <button type="submit" class="btn btn-primary btn-ripple btn-sm mb-2 ">
                                            Submit
                                        </button>
                                    </div>


                                </fieldset>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <app-common-emp-details-modal *ngIf="empDetailsModal" [emploginCode]="myData?.hrCrEmp?.loginCode"
        (closeModal)="handleModalData($event)"></app-common-emp-details-modal>

</div>

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>