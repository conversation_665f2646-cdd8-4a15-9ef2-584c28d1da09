import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';
declare const $: any;



@Component({
  selector: 'app-against-dept-sec-list',
  templateUrl: './against-dept-sec-list.component.html',
  styleUrls: ['./against-dept-sec-list.component.css']
})
export class AgainstDeptSecListComponent implements OnInit {
  public listData: any = [];
  baseUrl = environment.baseUrl;
  myForm: FormGroup;
  modalParam: any = {};
  attachmentUrl: any = [];

  organizationFilter: boolean = false;

  //pagination config
  pageNum = 1;
  pageSize = 10;
  pageSizes = [10, 25, 50, 100, 200, 500, 1000];
  totalItem = 50;
  pngDiplayLastSeq = 10;
  pngConfig: any;
  tempId: any;

  constructor(
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private employeeService: EmployeeService,
    private datePipe: DatePipe,
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.getAllList();
  }

  initializeForm() {

    this.myForm = this.formBuilder.group({
      startDate: [""],
      endDate: [""],
      refCode: [""],
      empCodes: [""]
    });
  }


  // --------------- Handle Organization Search ModaL -------------

  orgFilter() {
    this.organizationFilter = true;
  }

  handleModalData(param: any) {
    this.modalParam = param;
    this.organizationFilter = false;
    this.handlePageChange(1);
  }


  searchByButton() {
    this.handlePageChange(1);
  }

  resetform() {
    this.myForm.reset();
    this.modalParam = {};
    this.handlePageChange(1);
  }


  // ---------------- Set Last 30 Days By default ----------------

  setDateLast30Days() {
    this.myForm.controls.endDate.setValue(new Date());
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    this.myForm.controls.startDate.setValue(
      thirtyDaysAgo.setDate(today.getDate() - 30)
    );

    let x = this.datePipe.transform(this.myForm.get("startDate").value, "dd-MM-yyyy");

    this.myForm.controls.startDate.setValue(x);
  }




  // ----------------------- GET ALL LIST ----------------------- 

  getAllList() {
    let apiURL = this.baseUrl + "/complainDepartment/getList";
    let queryParams: any = {};

    const params = this.getUserQueryParams(this.pageNum, this.pageSize);
    queryParams = params;

    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response.objectList;
        this.totalItem = response.totalItems;
        this.setDisplayLastSequence();
        this.spinnerService.hide();
      }
    );
  }



  // --------------- Delete Data ------------------

  deleteEnityData(dataId) {
    let apiURL = this.baseUrl + "/complainDepartment/delete/" + dataId;

    let formData: any = {};

    this.spinnerService.show();
    this.commonService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {

        this.spinnerService.hide();
        $("#delete_entity").modal("hide");
        this.toastr.success("Successfully item is deleted", "Success");
        this.getAllList();
      },
      (error) => {
        $("#delete_entity").modal("hide");
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }

  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    params = this.modalParam

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    if (
      this.myForm.get("refCode").value
    ) {
      params.refCode = this.myForm.get("refCode").value;
    }

    if (
      this.myForm.get("empCodes").value
    ) {
      params.empCodes = this.myForm.get("empCodes").value;
    }

    if (this.myForm.get("startDate").value) {
      params["startDate"] = this.commonService.format_Date_Y_M_D(this.myForm.get("startDate").value);
    }
    if (this.myForm.get("endDate").value) {
      params["endDate"] = this.commonService.format_Date_Y_M_D(this.myForm.get("endDate").value);
    }
    return params;
  }

  redirectToImage(fileName) {
    window.open(this.baseUrl + fileName, "_blank");
  }



  setDisplayLastSequence() {
    this.pngDiplayLastSeq = (this.pageNum - 1) * this.pageSize + this.pageSize;
    if (this.listData.length < this.pageSize) {
      this.pngDiplayLastSeq =
        (this.pageNum - 1) * this.pageSize + this.pageSize;
    }
    if (this.totalItem < this.pngDiplayLastSeq) {
      this.pngDiplayLastSeq = this.totalItem;
    }
  }

  handlePageChange(event: number) {
    this.pageNum = event;
    this.getAllList();
  }

  handlePageSizeChange(event: any): void {
    this.pageSize = event.target.value;
    this.pageNum = 1;
    this.getAllList();
  }

  getAttachmentUrl(files) {
    this.attachmentUrl = files.split(',');
  }

}
