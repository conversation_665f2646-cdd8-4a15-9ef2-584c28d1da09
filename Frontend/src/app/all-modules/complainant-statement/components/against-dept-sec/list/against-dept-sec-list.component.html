<!-- Page Content -->
<div class="content container-fluid">
  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row">
      <div class="col-sm-12">
        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Complainant</li>
          <li class="breadcrumb-item active">
            <span style="color: #25b6b2"><b>Against Dept-Sec</b></span>
          </li>
          <li class="breadcrumb-item active">Create</li>
        </ul>
      </div>
    </div>
  </div>
  <!-- /Page Header -->

  <!-- Search Filter -->

  <div class="card mb-2" style="background-color: transparent">
    <div class="card-body p-3">
      <form [formGroup]="myForm">
        <div class="row">

          <div class="col-md-2">
            <div class="form-group">
              <label>Complainant's ID</label>
              <input class="form-control" formControlName="empCodes" type="text" placeholder="Example: 51567,54546" />
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label>Ref. Code</label>
              <input class="form-control" formControlName="refCode" type="text" placeholder="Ref Code(s)" />
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label>Occurrence From Date</label>
              <div class="cal-icon">
                <input id="td" class="form-control datetimepicker" bsDatepicker type="text" placeholder="DD-MM-YYYY"
                  [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                  formControlName="startDate">
              </div>

            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label>Occurrence To Date</label>
              <div class="cal-icon">
                <input id="td" class="form-control datetimepicker" bsDatepicker type="text" placeholder="DD-MM-YYYY"
                  [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } " formControlName="endDate">
              </div>

            </div>
          </div>

          <div class="col-md-4 mt-4">
            <button class="btn btn-success btn-ripple" type="submit" (click)="searchByButton()">
              <i class="fa fa-search" aria-hidden="true"></i> Search
            </button>
            &nbsp;

            <button class="btn btn-danger btn-ripple" (click)="resetform()">
              <i class="fa fa-eraser" aria-hidden="true"></i> Clear
            </button>
            &nbsp;
            <a class="btn btn-primary btn-ripple" (click)="orgFilter()"><i class="fa fa-filter"></i>
              Organization</a>
          </div>
        </div>
      </form>
    </div>
  </div>
  <!-- /Search Filter -->

  <!-- list view start -->
  <div class="row">
    <div class="col-md-12">
      <div class="card customCard">
        <div class="card-header">
          <div class="card-tools">
            <a class="btn btn-outline-primary" routerLink="/complainant-statement/against-dept-sec/create"><i
                class="fa fa-plus"></i> New &nbsp;&nbsp;&nbsp;</a>
          </div>
        </div>
        <div class="card-body ">
          <div class="table-responsive">
            <div class="d-flex justify-content-start pb-1">
              <div class="pgn-displayDataInfo">
                <span class="page-item disabled">Displaying ( {{ (pageNum - 1) * pageSize + 1 }} to
                  {{ pngDiplayLastSeq }} of {{ totalItem }} ) entries</span>
              </div>
            </div>
            <table class="table table-striped custom-table datatable">
              <thead>
                <tr>
                  <th>SL</th>
                  <th>Ref Code</th>
                  <th>Complainant</th>
                  <th>Occurrence Date</th>
                  <th>Occurrence Time</th>
                  <th>Created</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="
                    let row of listData
                      | paginate
                        : {
                            itemsPerPage: pageSize,
                            currentPage: pageNum,
                            totalItems: totalItem
                          };
                    let i = index
                  " [class.active]="i == currentIndex">
                  <td>
                    {{ (pageNum - 1) * pageSize + (i + 1) }}
                  </td>

                  <td>{{ row?.code }}</td>
                  <td>{{ row?.complainantName }} ({{ row?.complainantCode }})</td>

                  <td>{{ row?.occuranceDate | date }}</td>
                  <td>{{ row?.occuranceTime }}</td>
                  <td>{{ row?.submissionDate | date }}</td>
                  <td>
                    <span *ngIf="row.approvalStatus === 'Submitted'" class="badge badge-info">{{ row.approvalStatus
                      }}</span>
                    <span *ngIf="row.approvalStatus === 'Approved'" class="badge badge-success">{{ row.approvalStatus
                      }}</span>
                    <span *ngIf="row.approvalStatus === 'Rejected'" class="badge badge-danger">{{ row.approvalStatus
                      }}</span>
                    <span *ngIf="row.approvalStatus === 'Recommended'" class="badge badge-warning">{{ row.approvalStatus
                      }}</span>
                  </td>
                  <td>

                    <a class="btn btn-sm btn-primary"
                      routerLink="/complainant-statement/against-dept-sec/view/{{row.id}}"><i
                        class="fa fa-eye"></i></a>&nbsp;&nbsp;

                    <a [ngClass]="{'disabled': (row?.approvalStatus != 'Submitted')}" class="btn btn-sm btn-info"
                      routerLink="/complainant-statement/against-dept-sec/edit/{{row.id}}"><i
                        class="fa fa-pencil"></i></a>&nbsp;&nbsp;

                    <a [ngClass]="{'disabled': (row?.approvalStatus != 'Submitted')}" class="btn btn-sm btn-danger"
                      data-toggle="modal" data-target="#delete_entity" (click)="tempId = row.id">
                      <i class="fa fa-trash-o"></i>
                    </a>&nbsp;&nbsp;

                    <a *ngIf="row?.attachmentUrl" class="btn btn-sm btn-success" data-toggle="modal"
                      data-target="#doc_modal" title="HOD Attachments" (click)="getAttachmentUrl(row.attachmentUrl)"><i
                        class="fa fa-file"></i></a>

                  </td>
                </tr>
                <tr *ngIf="listData.length === 0">
                  <td colspan="10">
                    <h5 style="text-align: center">No data found</h5>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="d-flex justify-content-end">
              <div>
                Items per Page
                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption">
                  <option *ngFor="let size of pageSizes" [value]="size">
                    {{ size }}
                  </option>
                </select>
              </div>

              <div class="pgn-pageSliceCt">
                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                  (pageChange)="handlePageChange($event)">
                </pagination-controls>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- list view end -->


  <app-org-search-modal-param-based *ngIf="organizationFilter"
    (closeModal)="handleModalData($event)"></app-org-search-modal-param-based>

</div>

<!-- Documents Modal -->

<div id="doc_modal" class="modal custom-modal fade " role="dialog">
  <div class="modal-dialog modal-dialog-centered docModal" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Evidence Attachment</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>

      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-md-12">
            <div class="card customCard">
              <div class="card-body">
                <div class="table-wrapper-scroll-y attn-modal-scrollbar">
                  <table class="table table-striped custom-table  table-bordered">
                    <thead>
                      <tr>
                        <th class="text-center">SL</th>
                        <th class="text-center">Attachment</th>


                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let val of attachmentUrl ;let i = index" [class.active]="i == currentIndex">


                        <td class="text-center">{{i+1}}</td>

                        <td class="text-center" style="cursor: pointer;" (click)="redirectToImage(val)">{{val}}<br>
                        </td>


                    </tbody>
                  </table>


                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!--  Modal End -->


<!-- Delete Modal -->
<div class="modal custom-modal fade" id="delete_entity" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="form-header">
          <h3>Delete Item</h3>
          <p>Are you sure want to delete?</p>
        </div>
        <div class="modal-btn delete-action">
          <div class="row">
            <div class="col-6">
              <a class="btn btn-primary continue-btn" (click)="deleteEnityData(tempId)">Delete</a>
            </div>
            <div class="col-6">
              <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /Delete Modal -->

<!-- /Page Content -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>