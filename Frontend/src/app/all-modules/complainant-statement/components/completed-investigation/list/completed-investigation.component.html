<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Completed
                                Investigation</b></span>
                    </li>
                    <li class="breadcrumb-item active">List</li>
                </ul>

            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Search Filter -->

    <div class="card mb-2" style="background-color:transparent;">
        <div class="card-body p-3">

            <form [formGroup]="myForm">
                <div class="row">

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Ref. Code(s)</label>
                            <input class="form-control" formControlName="refCode" type="text" placeholder="Ref. Code(s)"
                                (keyup.enter)="getAllData()">
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Investigator Employee ID(s)</label>
                            <input class="form-control" formControlName="empCodes" type="text"
                                placeholder="12345,51567,54546" (keyup.enter)="getAllData()">
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Approval Status</label>
                            <select formControlName="approvalStatus" class="select form-control">
                                <option value="All">All Status</option>
                                <option value="Assigned">Assigned</option>
                                <option value="Processing">Processing</option>
                                <option value="Completed">Completed</option>
                            </select>

                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Submission Date From</label>
                            <div class="cal-icon">
                                <input id="td" class="form-control floating datetimepicker" bsDatepicker type="text"
                                    placeholder="DD-MM-YYYY"
                                    [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                                    formControlName="startDate">
                            </div>


                        </div>
                    </div>


                    <div class="col-md-2">
                        <label>Submission Date To</label>
                        <div class="cal-icon">
                            <input id="td" class="form-control floating datetimepicker" bsDatepicker type="text"
                                placeholder="DD-MM-YYYY"
                                [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                                formControlName="endDate">
                        </div>

                    </div>


                    <div class="col-md-12 mt-4 text-center">

                        <button class="btn btn-success btn-ripple" type="submit" (click)="searchByButton()">
                            <i class="fa fa-search" aria-hidden="true"></i> Search
                        </button>&nbsp;&nbsp;&nbsp;

                        <button class="btn btn-danger btn-ripple" (click)="resetform()">
                            <i class="fa fa-eraser" aria-hidden="true"></i> Clear
                        </button>&nbsp;&nbsp;&nbsp;

                        <a class="btn btn-primary btn-ripple" (click)="orgFilter()"><i class="fa fa-filter"></i>
                            Organization</a>

                    </div>


                </div>
            </form>

        </div>
    </div>
    <!-- /Search Filter -->

    <!-- list view start -->
    <div class="row">
        <div class="col-md-12">
            <div class="card customCard">
                <div class="card-body">
                    <div class="table-responsive">
                        <div class="d-flex justify-content-start pb-1">
                            <div class="pgn-displayDataInfo">
                                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                                    configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                                    {{configPgn.totalItem}} ) entries</span>
                            </div>
                        </div>
                        <table class="table table-striped custom-table datatable">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Ref. Code</th>
                                    <th>Complainant</th>
                                    <th>Complainant Ref. Code</th>
                                    <th>Complainant Type</th>
                                    <!-- <th>Occurrence Date</th>
                                    <th>Occurrence Time</th> -->
                                    <th>Submission Date</th>
                                    <!-- <th>Submission Time</th>
                                    <th>Case Category</th> -->
                                    <th>Investigator</th>
                                    <th>Investigaton Status</th>
                                    <!-- <th>Approval Status</th> -->
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr *ngFor="let row of listData| paginate : configPgn; let i = index"
                                    [class.active]="i == currentIndex">
                                    <td>
                                        {{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) ) }}
                                    </td>

                                    <td>{{row.code}}</td>
                                    <td>{{row.complainantLoginCode}} - {{row.complainantDisplayName}}</td>
                                    <td>{{row.complainantRefCode}}</td>
                                    <td>{{row?.complainantType}}</td>
                                    <!-- <td>{{row?.occurrenceDate | date}}</td>
                                    <td>{{row?.occurrenceTime}}</td> -->
                                    <td>{{row?.submissionDate | date}}</td>
                                    <!-- <td>{{row?.submissionTime}}</td>
                                    <td>{{row?.caseCategory}}</td> -->
                                    <td>{{row?.investigatorLoginCode}} - {{row?.investigatorDisplayName}}</td>

                                    <td>
                                        <span *ngIf="row?.investigationStatus === 'Assigned'">
                                            <span class="badge badge-info"> <b> {{row?.investigationStatus}}
                                                </b></span>
                                        </span>
                                        <span *ngIf="row?.investigationStatus === 'Processing'">
                                            <span class="badge badge-warning">
                                                <b> Report Submitted </b></span>
                                        </span>
                                        <span *ngIf="row?.investigationStatus === 'Completed'">
                                            <span class="badge badge-success">
                                                <b> {{row?.investigationStatus}} </b></span>
                                        </span>
                                    </td>

                                    <td>
                                        <a class="btn btn-sm btn-primary"
                                            routerLink="/complainant-statement/completed-investigation/view/{{row.id}}"
                                            target="_blank"><i class="fa fa-eye"></i></a> &nbsp;



                                        <a *ngIf="row?.attachmentHod" class="btn btn-sm btn-success" data-toggle="modal"
                                            data-target="#doc_modal" title="HOD Attachments"
                                            (click)="getAttachmentHod(row.attachmentHod)"><i class="fa fa-file"></i></a>
                                        &nbsp;


                                        <a *ngIf="row?.investigationFileUpload" class="btn btn-sm btn-primary"
                                            data-toggle="modal" data-target="#doc_modal2" title="Investigation Report"
                                            (click)="getAttachmentHod(row.investigationFileUpload)"><i
                                                class="fa fa-file-o"></i> </a> &nbsp;



                                        <a class="btn btn-sm btn-warning" style="cursor: pointer;"
                                            (click)="redirectToImage(row?.enquiryCommitteeFile)"
                                            title="Committee Signed Report" *ngIf="row?.enquiryCommitteeFile"> <i
                                                class="text-center" class="fa fa-file"></i></a>


                                    </td>


                                </tr>
                                <tr *ngIf="listData?.length === 0">
                                    <td colspan="10">
                                        <h5 style="text-align: center;">No data found</h5>
                                    </td>

                                </tr>

                            </tbody>

                        </table>
                        <div class="d-flex justify-content-end ">

                            <div class="" [formGroup]="myFromGroup">
                                Items per Page
                                <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption"
                                    formControlName="pageSize">
                                    <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                                        {{ size }}
                                    </option>
                                </select>
                            </div>

                            <div class="pgn-pageSliceCt">
                                <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                                    (pageChange)="handlePageChange($event)">
                                </pagination-controls>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- list view end -->



    <app-org-search-modal-param-based *ngIf="organizationFilter"
        (closeModal)="handleModalData($event)"></app-org-search-modal-param-based>

</div>


<!-- Documents Modal -->

<div id="doc_modal" class="modal custom-modal fade " role="dialog">
    <div class="modal-dialog modal-dialog-centered docModal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Evidence Attachment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">


                <div class="row">
                    <div class="col-md-12">
                        <div class="card customCard">
                            <div class="card-body">
                                <div class="table-wrapper-scroll-y attn-modal-scrollbar">
                                    <table class="table table-striped custom-table  table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="text-center">SL</th>
                                                <th class="text-center">Attachment</th>


                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let val of attachmentHod ;let i = index"
                                                [class.active]="i == currentIndex">


                                                <td class="text-center">{{i+1}}</td>

                                                <td class="text-center" style="cursor: pointer;"
                                                    (click)="redirectToImage(val)">{{val}}<br>
                                                </td>


                                        </tbody>
                                    </table>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--  Modal End -->



<!-- Documents Modal -->

<div id="doc_modal2" class="modal custom-modal fade " role="dialog">
    <div class="modal-dialog modal-dialog-centered docModal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Investigation Report & Other Evidence</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">


                <div class="row">
                    <div class="col-md-12">
                        <div class="card customCard">
                            <div class="card-body">
                                <div class="table-wrapper-scroll-y attn-modal-scrollbar">
                                    <table class="table table-striped custom-table  table-bordered">
                                        <thead>
                                            <tr>
                                                <th class="text-center">SL</th>
                                                <th class="text-center">Attachment</th>


                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let val of attachmentHod ;let i = index"
                                                [class.active]="i == currentIndex">


                                                <td class="text-center">{{i+1}}</td>

                                                <td class="text-center" style="cursor: pointer;"
                                                    (click)="redirectToImage(val)">{{val}}<br>
                                                </td>


                                        </tbody>
                                    </table>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Modal End -->





<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>