<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Complainant</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Completed
                Investigation</b></span>
          </li>
          <li class="breadcrumb-item active">View</li>
        </ul>
      </div>
      <div class="col-auto float-right ml-auto">
        <a class="btn add-btn" routerLink="/complainant-statement/completed-investigation"><i class="fa fa-share"></i>
          Back To
          List</a>
      </div>
    </div>
  </div>
  <!-- /Page Header -->


  <div class="row">
    <div class="col-lg-12">
      <div class="card customCard">

        <div class="card-body">
          <div class="row">
            <div class="col-md-12">


              <table class="summaryTable col-md-12">
                <tr>
                  <td><b>Reference Code</b></td>
                  <td>{{myData?.code}}</td>
                  <td><b>Occurrence Date</b></td>
                  <td>{{myData.occurrenceDate?myData.occurrenceDate:'-'}}
                  </td>
                  <td><b>Submission Date</b></td>
                  <td>{{myData.submissionDate?myData.submissionDate:'-'}}</td>

                </tr>

                <tr>
                  <td><b>Complainant</b></td>
                  <td>{{ myData.complainant?myData.complainant:'-' }}
                  </td>

                  <td><b>Occurrence Time</b></td>
                  <td>{{myData.occurrenceTime?myData.occurrenceTime:'-'}}
                  </td>
                  <td><b>Submission Time</b></td>
                  <td>{{myData.submissionTime?myData.submissionTime:'-'}}</td>
                </tr>

                <tr>
                  <td><b>Complainant Type</b></td>
                  <td>{{ myData.complainantType?myData.complainantType:'-' }}</td>
                  <td><b>Assign By</b></td>
                  <td> {{myData.assignBy?myData.assignBy:'-'}}</td>

                  <td><b>Investigator</b></td>
                  <td> {{myData.assignTo?myData.assignTo:'-'}}</td>

                </tr>
                <tr>
                  <td><b>Case Category </b></td>
                  <td>{{myData.caseCategory?myData.caseCategory:'-'}}</td>
                  <td><b>Case Value</b></td>
                  <td> {{myData.caseValue?myData.caseValue:'-'}}</td>
                  <td><b>Investigator's Assistants</b></td>
                  <td>{{myData.assignToAssistant?myData.assignToAssistant:'-'}}</td>

                </tr>

                <tr>
                  <td><b>Investigation Status </b></td>
                  <td>
                    <span *ngIf="myData?.investigationStatus === 'Assigned'">
                      <span class="text-info"> <b> {{myData?.investigationStatus}}
                        </b></span>
                    </span>
                    <span *ngIf="myData?.investigationStatus === 'Processing'">
                      <span class="text-warning">
                        <b> Report Submitted </b></span>
                    </span>
                    <span *ngIf="myData?.investigationStatus === 'Completed'">
                      <span class="text-success">
                        <b> {{myData?.investigationStatus}} </b></span>
                    </span>
                  </td>
                  <td><b>Remarks</b></td>
                  <td>{{myData.remarks?myData.remarks:'-'}}</td>

                </tr>

              </table>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>


  <div *ngFor="let df of  myData.defendents;let sl = index">
    <div class="card-header mb-3 customCard">
      <h4 style="color:#25B6B2;"> {{sl+1}}. <b>Defendant:</b> {{df.empName}} ({{df.empCode}})</h4>

      <hr>

      <div class="row">
        <div class="col-lg-12">
          <div class="card customCard">

            <div class="card-body customCard">
              <div class="row">
                <div class="col-md-12">


                  <table class="summaryTable col-md-12">


                    <tr>
                      <td><b>Designation</b></td>
                      <td>{{df.dgOrder}}-{{df.designation}}</td>

                      <td><b>Proposed Action Type</b></td>
                      <td>{{df?.actionTypes}}</td>

                      <td><b>Product Status</b></td>
                      <td> {{df.productStatus}}</td>



                    </tr>

                    <tr>
                      <td><b>Approval Status</b></td>
                      <td> <span *ngIf="df.approvalStatus=='Submitted'" class="text-primary">Submitted</span>
                        <span *ngIf="df.approvalStatus=='Approved'" class="text-success">Approved</span>
                        <span *ngIf="df.approvalStatus=='Recommended'" class="text-warning">Recommended</span>
                        <span *ngIf="df.approvalStatus=='Rejected'" class="text-danger">Rejected</span>
                      </td>


                      <td><b>Deduction Score</b></td>
                      <td>{{df.deductionScore}}</td>
                      <td><b>Deduct Amount (BDT)</b></td>
                      <td>
                        {{df.deductAmount}}
                      </td>


                    </tr>

                    <tr>
                      <td><b>Position</b></td>
                      <td colspan="6">
                        {{df.operatingUnit}}.{{df.product}}.{{df.department}}.{{df.section}}.{{df.subSection}}.{{df.team}}
                      </td>
                    </tr>

                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>



        <div id="sectionPrint"
          [ngClass]="{'col-md-12': actionList2[df.id]?.length === 0, 'col-md-8': actionList2[df.id]?.length>0 }">
          <fieldset class="row fieldsetBorder logBox ">
            <legend class="bg-warning">Approval Status</legend>
            <table class="table">
              <thead>
                <tr>
                  <th>SL</th>
                  <th>Step Name</th>
                  <th>Approval Path</th>
                  <th>Sign By</th>
                  <th>Designation</th>
                  <th>Action Status</th>
                  <th>Update Date</th>
                  <th>Remarks</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let dtl of df.history; let i = index">
                  <td>{{i+1}}</td>
                  <td>{{dtl.approvalStep?.approvalGroupName}}</td>
                  <td>{{dtl.approvalStepApproverEmp?dtl.approvalStepApproverEmp?.loginCode:'-'}} -
                    {{dtl.approvalStepApproverEmp?dtl.approvalStepApproverEmp?.displayName:'-'}} </td>
                  <td>{{dtl.actionTaker?dtl.actionTaker:'-'}}</td>
                  <td>{{dtl.designation?dtl.designation:'-'}}</td>
                  <td>{{dtl.actionStatus?dtl.actionStatus:'-'}}</td>
                  <td>{{dtl.updateDateTime}}</td>
                  <td>{{dtl.remarks?dtl.remarks:'-'}}</td>
                </tr>
              </tbody>
            </table>

          </fieldset>
        </div>

        <div class="col-4" *ngIf="df?.inMyPath==true  && actionList2[df.id]?.length>0">

          <form [formGroup]="formOfAction">
            <fieldset class="row fieldsetBorder logBox ">
              <legend class="bg-warning">Take Action</legend>


              <label class="col-form-label col-md-4 val-required">Status</label>
              <div class="col-md-8">
                <select class="select form-control" (change)="setApprovalStepActionId(
                      df.id,
                      $event.target.value
                      )" formControlName="approvalStepAction">
                  <option value="">Select Action</option>
                  <option *ngFor="let data of actionList2[df.id]" [value]="data.id">
                    {{data.activityStatusTitle}}</option>
                </select>

              </div>

              <div class="col-md-12 text-right p-2">
                <button class="btn btn-sm btn-primary"
                  (click)="savehTRY(df.id,df.hrCrEmpId,df.thisNode,df.nextNode,df.approvalStatus)">Save</button>
              </div>


            </fieldset>
          </form>
        </div>

      </div>

    </div>

  </div>


</div>
<!-- /Page Content -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>