<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Development-Award
                                Proposal</b></span></li>
                    <li class="breadcrumb-item active">View</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/award-proposal/list"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->
    <div class="card customCard">
        <div class="card-body">
            <div class="row">

                <div class="col-md-12">
                    <h3>Award Person List</h3>
                    <hr />
                    <table class="table table-striped custom-table datatable">
                        <thead>
                            <tr>
                                <th>SL</th>
                                <th>Employee</th>
                                <th>Proposal Category</th>
                                <th>Proposed Score</th>
                                <th>Award Amount</th>
                                <th>Scope of Development</th>
                                <th>Development Proposal (Bangla/English)</th>
                                <th>Process Date</th>
                                <th>Process Time</th>
                                <th>Saving Cost</th>
                                <th>Others Benefit</th>
                                <th>Document</th>
                            </tr>
                        </thead>
                        <tbody>

                            <tr *ngFor="let row of awardProposalData?.developmentAwardProposalDetailsSet;let i = index"
                                [class.active]="i == currentIndex">
                                <td>
                                    {{ 1 + i}} <span style="color: #784db5;" (click)="getEmployeeInfo(row?.employeeId)"
                                        data-toggle="modal" data-target="#employee_Info"> <i class="fa fa-eye"
                                            title="Employee Summary"> </i> </span>
                                </td>

                                <td>{{row?.employeeId}} - {{row?.employeeName}}</td>
                                <td>{{row?.propCategory?.title}}</td>
                                <td>{{row?.proposedScore}}</td>
                                <td>{{row?.amount}}</td>
                                <td>{{row?.scopeOfDevelopment}}</td>
                                <td>{{row?.developmentProposal}}</td>
                                <td>{{row?.processDate | date}}</td>
                                <td>{{row?.processTime}}</td>
                                <td>{{row?.savingCost}}</td>
                                <td>{{row?.othersBenefit}}</td>
                                <td><a class="btn btn-sm btn-primary" (click)="redirectToImage(row?.fileUrl)"
                                        title="View Document"><i class="fa fa-file"></i></a></td>


                            </tr>
                            <tr *ngIf="awardProposalData?.developmentAwardProposalDetailsSet === 0">
                                <td colspan="10">
                                    <h5 style="text-align: center;">No data found</h5>
                                </td>

                            </tr>

                        </tbody>

                    </table>
                </div>
            </div>



        </div>
    </div>


    <div class="card customCard">
        <div class="card-body">
            <div class="row">

                <div class="col-md-12">


                    <div class="row">

                        <div [ngClass]="{'col-md-12': listData3?.length === 0, 'col-md-8': listData3?.length !== 0 }">
                            <fieldset class="row fieldsetBorder logBox ">
                                <legend class="bg-warning">Approval Status</legend>
                                <table class="table table-striped custom-table datatable">
                                    <thead>
                                        <tr>
                                            <th>S/L</th>
                                            <th>Approval Step</th>
                                            <th>Sign By</th>
                                            <th>Action</th>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Remarks</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let thisObj of listData4;let i = index">
                                            <td>
                                                {{ 1 + i}}
                                            </td>
                                            <td>{{thisObj.approvalStep.approvalGroupName?thisObj.approvalStep.approvalGroupName:'-'}}
                                            </td>
                                            <td>
                                                {{ thisObj?.approvalStepApproverEmp?.loginCode}} -
                                                {{
                                                thisObj?.approvalStepApproverEmp?.displayName}}
                                            </td>
                                            <td>{{thisObj.actionStatus?thisObj.actionStatus:'-'}}
                                            </td>
                                            <td>
                                                {{thisObj.actionStatus?thisObj.updateDateTime.substr(0,10)
                                                :'-'}}
                                            </td>
                                            <td>
                                                {{thisObj.actionStatus?thisObj.updateDateTime.substr(11)
                                                :'-'}}</td>

                                            <td>{{thisObj.remarks?thisObj.remarks:'-'}}</td>

                                        </tr>
                                    </tbody>
                                </table>

                            </fieldset>
                        </div>
                        <div *ngIf="listData3?.length > 0">
                            <form novalidate (ngSubmit)="tackAction()" [formGroup]="actionForm">
                                <fieldset class="row fieldsetBorder logBox ">
                                    <legend class="bg-warning">Take Action</legend>


                                    <label class="col-form-label col-md-3">Status</label>
                                    <div class="col-md-8">
                                        <select class="select form-control" formControlName="approvalStepAction">
                                            <option value="">Select Action</option>
                                            <option *ngFor="let data of listData3" [ngValue]='data.id'>
                                                {{data.activityStatusTitle}}
                                            </option>
                                        </select>

                                    </div>
                                    <br><br>

                                    <label class="col-form-label col-md-3">Remarks</label>
                                    <div class="col-md-8">
                                        <textarea formControlName="remarks" class="form-control mb-3"></textarea>

                                    </div>


                                    <div class="col-md-9"></div>
                                    <div class="col-md-3">
                                        <button type="submit" class="btn btn-primary btn-ripple btn-sm mb-2 ">
                                            Submit
                                        </button>
                                    </div>


                                </fieldset>
                            </form>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


<!-- Employee Info  Modal -->
<div class="modal custom-modal fade" id="employee_Info" role="dialog">
    <div class="modal-dialog modal-dialog-centered empInfoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Employee Information</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card customCard">

                            <div class="card-body">

                                <div class="row">
                                    <div class="col-md-12">


                                        <table class="summaryTable col-md-12">
                                            <tr>
                                                <td><b>Employee</b></td>
                                                <td>{{selectedEmployee?.empCode}}-{{selectedEmployee?.employeeName}}
                                                </td>
                                                <td><b>Section</b></td>
                                                <td>{{selectedEmployee?.section}}</td>
                                                <td><b>Category</b></td>
                                                <td>{{selectedEmployee?.employmentType}}</td>
                                            </tr>

                                            <tr>

                                                <td><b>Designation</b></td>
                                                <td>{{selectedEmployee?.designation}}</td>

                                                <td><b>Sub Section</b></td>
                                                <td>{{selectedEmployee?.subSection}}</td>
                                                <td><b>Concern HR</b></td>
                                                <td>{{selectedEmployee?.concernHr}}</td>

                                            </tr>
                                            <tr>
                                                <td><b>Operating Unit</b></td>
                                                <td>{{selectedEmployee?.operatingUnit}}</td>
                                                <td><b>Team</b></td>
                                                <td>{{selectedEmployee?.team}}</td>
                                                <td><b>Previous Disiplinary Action</b></td>
                                                <td>{{selectedEmployee?.previousDisiplinaryAction}}</td>

                                            </tr>

                                            <tr>
                                                <td><b>Product</b></td>
                                                <td>{{selectedEmployee?.product}}</td>
                                                <td><b>Sub Team</b></td>
                                                <td>{{selectedEmployee?.subTeam}}</td>
                                                <td><b>Present Score</b></td>
                                                <td>{{selectedEmployee?.presentScore}}</td>



                                            </tr>

                                            <tr>
                                                <td><b>Department</b></td>
                                                <td>{{selectedEmployee?.department}}</td>
                                                <td><b>Location</b></td>
                                                <td>{{selectedEmployee?.jobLocation}}</td>
                                                <td><b>Prduct Status</b></td>
                                                <td>{{selectedEmployee?.prductStatus}}</td>

                                            </tr>




                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!-- /Employee Info Modal -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>