import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AwardProposalViewComponent } from './award-proposal-view.component';

describe('AwardProposalViewComponent', () => {
  let component: AwardProposalViewComponent;
  let fixture: ComponentFixture<AwardProposalViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AwardProposalViewComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AwardProposalViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
