import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-award-proposal-view',
  templateUrl: './award-proposal-view.component.html',
  styleUrls: ['./award-proposal-view.component.css']
})
export class AwardProposalViewComponent implements OnInit {


  public baseUrl = environment.baseUrl;
  private uploadForm: FormGroup;
  public awardPersonForm: FormGroup;
  public selectedEmployee: any = [];
  public operating_units = [];
  public product = [];
  public department = [];
  public sections = [];
  public subSections = [];
  public teams = [];
  public awardProposalData;
  public actionForm: FormGroup;

  listData4: any = [];
  listData3: any = [];

  configDDL: any;

  amountDiv = false;
  productDiv = false;
  othersDiv = false;

  // Text area field character Count
  maxNumOfChar = 500;
  numberOfChar = 0;

  maxNumOfChar2 = 500;
  numberOfChar2 = 0;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
  ) {

  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadAwardPoposalData();
  }

  initializeForm() {

    this.actionForm = this.formBuilder.group({
      id: [""],
      approvalStepAction: ["", Validators.required],
      remarks: [""],
    });

  }


  loadAwardPoposalData() {

    let id = this.route.snapshot.params.id;
    this.spinnerService.show();
    let apiURL = this.baseUrl + "/developmentAward/get/" + id;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.awardProposalData = response.data;
        this.getSelfListData();
        this.getApprovalStepAction();
        this.spinnerService.hide();

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    )

  }




  getSelfListData() {

    let id = this.route.snapshot.params.id;


    let apiURL = this.baseUrl + "/approvalProcTnxHtry/getSelfApprovalProcTnxList/" + id;

    let queryParams: any = {};
    const params = this.getUserQueryParams();
    queryParams = params;


    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData4 = response;
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }

  getApprovalStepAction() {
    let id = this.route.snapshot.params.id;

    let apiURL = this.baseUrl + "/approvalStepAction/getApprovalStepAction/" + id;
    const params = this.getUserQueryParams();
    let queryParams: any = {};

    queryParams = params;
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData3 = response;
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();

      }
    );
  }

  private getUserQueryParams(): any {

    let params: any = {};

    params[`approvalProcess`] = "AWARD_PROPOSAL_PROCESS";

    params[`nextApprovalNode`] = this.awardProposalData.approvalStep?.nextApprovalNode;

    params[`thisApprovalNode`] = this.awardProposalData?.approvalStep?.thisApprovalNode;
    params["appliedHrCrEmpId"] = this.awardProposalData.hrCrEmp.id;

    return params;
  }


  tackAction() {

    if (this.actionForm.invalid) {
      return;
    }
    let id = this.route.snapshot.params.id;

    let obj = Object.assign(this.actionForm.value, {
      referenceId: id,
      referenceEntity: "AWARD_PROPOSAL_PROCESS" + "/" + this.awardProposalData.approvalStep.thisApprovalNode + "/" + this.awardProposalData.approvalStep.nextApprovalNode + "/" + this.awardProposalData.hrCrEmp.id,
      approvalStepAction: this.get.value ? { id: this.get.value } : null,
    });

    let apiURL = this.baseUrl + "/approvalProcTnxHtry/edit";
    const params = this.getUserQueryParams();
    let queryParams: any = {};
    queryParams = params;
    let formData: any = {};
    formData = obj;

    this.spinnerService.show();

    this.commonService.sendPutRequest(apiURL, formData).subscribe(
      (response: any) => {
        this.listData3 = [];
        this.listData4 = [];
        this.loadAwardPoposalData();
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.info(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();

      }
    );
  }

  resetFormValues() {
    this.actionForm.reset();
  }

  get get() {
    return this.actionForm.get("approvalStepAction");
  }

  redirectToImage(fileUrl) {
    window.open(this.baseUrl + fileUrl, "_blank");
  }


  // ----------------- Get Employee Info ---------------

  getEmployeeInfo(loginCode) {

    this.spinnerService.show();
    let apiURL = this.baseUrl + "/hrCrEmp/getEmpDataByLoginCode/" + loginCode;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.selectedEmployee = response;
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    )
  }

}
