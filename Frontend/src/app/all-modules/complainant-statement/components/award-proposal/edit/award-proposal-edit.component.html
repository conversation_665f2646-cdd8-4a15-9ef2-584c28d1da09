<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Development-Award
                                Proposal</b></span></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/award-proposal/list"><i
                        class="fa fa-share"></i> Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->
    <div class="card customCard">
        <div class="card-body">
            <form novalidate (ngSubmit)="myFormUpdate()" [formGroup]="myForm">
                <div class="row">



                    <div class="col-md-12">
                        <h4>Employee List</h4>
                        <hr />
                        <form [formGroup]="awardPersonForm">

                            <div class="table-responsive col-md-12">
                                <table class="table table-striped custom-table">
                                    <thead>
                                        <tr>
                                            <th>SL</th>
                                        <th>Employee ID</th>

                                        <th style="min-width: 200px !important;">Employee Name</th>
                                        <th>Proposal Category</th>
                                        <th style="min-width: 200px !important;">Proposed Score</th>
                                        <th>Award Amount</th>
                                        <th style="min-width: 200px !important;">Scope of Development</th>
                                        <th style="min-width: 150px !important;">Development Proposal (Bangla/English)
                                        </th>
                                        <th style="min-width: 150px !important;">Process Date</th>
                                        <th style="min-width: 200px !important;">Process Time</th>
                                        <th style="min-width: 150px !important;">Saving Cost</th>
                                        <th style="min-width: 200px !important;">Others Benefit</th>
                                        <th style="min-width: 200px !important;">Attachment</th>
                                        <th style="min-width: 200px !important;">View Doc</th>


                                        <th>Action</th>
                                        </tr>

                                    </thead>
                                    <tbody formArrayName="Rows">
                                        <tr *ngFor="let itemrow of awardPersonFormArr.controls; let i=index;let l=last"
                                            [formGroupName]="i">
                                            <td>{{i+1}} <span style="color: #784db5;" (click)="getEmployeeInfo(i)"
                                                    data-toggle="modal" data-target="#employee_Info"> <i
                                                        class="fa fa-eye" title="Employee Summary"> </i> </span></td>

                                            <td>
                                                <input formControlName="employeeId" class="form-control" type="text"
                                                    (blur)="getEmployeeInfo(i)">
                                            </td>

                                            <td>
                                                <input formControlName="employeeName" class="form-control" type="text"
                                                    readonly>
                                            </td>

                                            <td>

                                                <ng-select formControlName="propCategory" [items]="awardListData"
                                                    bindLabel="title" bindValue="id" class="custom-ng-select"
                                                    placeholder="Select Any" appendTo="body"
                                                    (change)="getProposedScoreOfAward($event, i)">
                                                </ng-select>


                                            </td>

                                            <td>
                                                <input formControlName="proposedScore" class="form-control" type="text"
                                                    [readonly]="isProposedScore">
                                            </td>

                                            <td>
                                                <input formControlName="amount" class="form-control" type="number">
                                            </td>

                                            <td>
                                                <input formControlName="scopeOfDevelopment" class="form-control"
                                                    type="text">
                                            </td>

                                            <td>
                                                <input formControlName="developmentProposal" class="form-control"
                                                    type="text">
                                            </td>

                                            <td>
                                                <input formControlName="processDate" class="form-control" type="date">
                                            </td>

                                            <td>
                                                <input formControlName="processTime" class="form-control" type="time">
                                            </td>

                                            <td>
                                                <input formControlName="savingCost" class="form-control" type="number">
                                            </td>

                                            <td>
                                                <input formControlName="othersBenefit" class="form-control" type="text">
                                            </td>

                                            <td>
                                                <input class="form-control" type="file" name="fileUrl"
                                                    (change)="onFileSelectDoc($event , i)">
                                            </td>

                                            <td >
                                                <input class="form-control" formControlName="fileUrl" type="text"  (click)="redirectToImage($event)" readonly style="cursor:pointer">
                                            </td>


                                            <td>
                                                <button *ngIf="awardPersonForm.controls.Rows.controls.length > 0"
                                                    (click)="deleteRow(i)" class="btn btn-danger">Delete</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <br>
                            <button type="button" (click)="addRow()" class="btn btn-primary">Add More</button>

                        </form>
                    </div>



                </div>


                <div class="text-right mt-2">
                    <button type="button" id="reset" class="btn btn-secondary btn-ripple">
                        <i class="fa fa-undo" aria-hidden="true"></i> Reset
                    </button>
                    &nbsp; &nbsp; &nbsp;
                    <button type="submit" id="submit" class="btn btn-primary btn-ripple" [disabled]="!myForm.valid">
                        <i class="fa fa-check" aria-hidden="true"></i> Update
                        &nbsp;&nbsp;&nbsp;
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<!-- Employee Info  Modal -->
<div class="modal custom-modal fade" id="employee_Info" role="dialog">
    <div class="modal-dialog modal-dialog-centered empInfoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Employee Information</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <div class="modal-body">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card customCard">

                            <div class="card-body">

                                <div class="row">
                                    <div class="col-md-12">


                                        <table class="summaryTable col-md-12">
                                            <tr>
                                                <td><b>Employee</b></td>
                                                <td>{{selectedEmployee?.empCode}}-{{selectedEmployee?.employeeName}}
                                                </td>
                                                <td><b>Section</b></td>
                                                <td>{{selectedEmployee?.section}}</td>
                                                <td><b>Category</b></td>
                                                <td>{{selectedEmployee?.employmentType}}</td>
                                            </tr>

                                            <tr>

                                                <td><b>Designation</b></td>
                                                <td>{{selectedEmployee?.designation}}</td>

                                                <td><b>Sub Section</b></td>
                                                <td>{{selectedEmployee?.subSection}}</td>
                                                <td><b>Concern HR</b></td>
                                                <td>{{selectedEmployee?.concernHr}}</td>

                                            </tr>
                                            <tr>
                                                <td><b>Operating Unit</b></td>
                                                <td>{{selectedEmployee?.operatingUnit}}</td>
                                                <td><b>Team</b></td>
                                                <td>{{selectedEmployee?.team}}</td>
                                                <td><b>Previous Disiplinary Action</b></td>
                                                <td>{{selectedEmployee?.previousDisiplinaryAction}}</td>

                                            </tr>

                                            <tr>
                                                <td><b>Product</b></td>
                                                <td>{{selectedEmployee?.product}}</td>
                                                <td><b>Sub Team</b></td>
                                                <td>{{selectedEmployee?.subTeam}}</td>
                                                <td><b>Present Score</b></td>
                                                <td>{{selectedEmployee?.presentScore}}</td>



                                            </tr>

                                            <tr>
                                                <td><b>Department</b></td>
                                                <td>{{selectedEmployee?.department}}</td>
                                                <td><b>Location</b></td>
                                                <td>{{selectedEmployee?.jobLocation}}</td>
                                                <td><b>Prduct Status</b></td>
                                                <td>{{selectedEmployee?.prductStatus}}</td>

                                            </tr>




                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!-- /Employee Info Modal -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>