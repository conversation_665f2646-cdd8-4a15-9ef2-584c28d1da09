import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';
declare const $: any;

@Component({
  selector: 'app-award-proposal-list',
  templateUrl: './award-proposal-list.component.html',
  styleUrls: ['./award-proposal-list.component.css']
})
export class AwardProposalListComponent implements OnInit {
  public listData: any = [];
  baseUrl = environment.baseUrl;
  myFromGroup: FormGroup;
  tempId: any;
  configDDL: any;

  public pageNum = 1;
  public pageSize = 10;
  public totalItem: any;



  configPgn: any;

  public myForm: FormGroup;

  modalParam: any = {};

  organizationFilter: boolean = false;
  constructor(
    private spinnerService: NgxSpinnerService,
    private employeeService: EmployeeService,
    private datePipe: DatePipe,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,

  ) {


    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };

  }

  ngOnInit(): void {

    this.myForm = this.formBuilder.group({
      startDate: [""],
      endDate: [""],
      empCodes: [""],
      refCode: [""],
    });

    this.myFromGroup = new FormGroup({
      pageSize: new FormControl()
    });
    this.myFromGroup.get('pageSize').setValue(this.configPgn.pageSize);

    this.getAllData();

  }

  // --------------- Handle Organization Search ModaL -------------

  orgFilter() {
    this.organizationFilter = true;
  }

  handleModalData(param: any) {
    this.modalParam = param;
    this.organizationFilter = false;
    this.handlePageChange(1);
  }

  //-------------- Search By Button -------------

  searchByButton() {
    this.handlePageChange(1);
  }

  // -------------------- Reset Form --------------------

  resetform() {
    this.myForm.reset();
    this.modalParam = {};
    this.handlePageChange(1);
  }

  // ------------------pagination handling methods start -----------------------------

  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {
    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this.getAllData();
  }
  handlePageSizeChange(event: any): void {
    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this.getAllData();
  }

  // ------------------------------pagination handling methods end -------------------------------------------

  getAllData() {

    let apiURL = this.baseUrl + "/developmentAward/getList";

    let queryParams = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);

    this.spinnerService.show();

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(

      (response: any) => {

        this.listData = response.objectList;
        this.configPgn.totalItem = response.totalItems;
        this.configPgn.totalItems = response.totalItems;
        this.pageSize = this.configPgn.pageNum;
        this.pageSize = this.configPgn.pageSize;
        this.totalItem = this.configPgn.totalItem;
        this.setDisplayLastSequence();
        this.spinnerService.hide();
      },

      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }



  deleteEnityData(Id) {

    let apiURL = this.baseUrl + "/developmentAward/delete/" + Id;
    let formData: any = {};
    this.spinnerService.show();
    this.employeeService.sendDeleteRequest(apiURL, formData).subscribe(
      (response: any) => {
        $("#delete_entity").modal("hide");
        this.toastr.success("Successfully item is deleted", "Success");
        this.getAllData();
        this.spinnerService.hide();
      },
      (error) => {
        $("#delete_entity").modal("hide");
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }

    );
  }

  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    params = this.modalParam

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }


    if (this.myForm.get("refCode").value) {
      params.refCode = this.myForm.get("refCode").value;
    }
    if (this.myForm.get("startDate").value) {
      params["startDate"] = this.datePipe.transform(this.myForm.get("startDate").value, "yyyy-MM-dd");
    }

    if (this.myForm.get("endDate").value) {
      params["endDate"] = this.datePipe.transform(this.myForm.get("endDate").value, "yyyy-MM-dd");
    }
    if (this.myForm.get("empCodes").value) {
      params.empCodes = this.myForm.get("empCodes").value;
    }

    return params;
  }


}
