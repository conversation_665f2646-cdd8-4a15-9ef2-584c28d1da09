<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row">
      <div class="col-sm-12">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Complainant</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Award Proposal</b></span></li>
          <li class="breadcrumb-item active">List</li>
        </ul>

      </div>
    </div>
  </div>
  <!-- /Page Header -->

  <!-- Search Filter -->

  <div class="card mb-2" style="background-color:transparent;">
    <div class="card-body p-3">

      <form [formGroup]="myForm">
        <div class="row">

          <div class="col-md-2">
            <div class="form-group">
              <label>Ref. Code(s)</label>
              <input class="form-control" formControlName="refCode" type="text" placeholder="Ref. Code(s)"
                (keyup.enter)="getAllData()">
            </div>
          </div>

          <div class="col-md-2">
            <div class="form-group">
              <label>Employee ID(s)</label>
              <input class="form-control" formControlName="empCodes" type="text" placeholder="12345,51567,54546"
                (keyup.enter)="getAllData()">
            </div>
          </div>


          <div class="col-md-2">
            <div class="form-group">
              <label>From Date</label>
              <div class="cal-icon">
                <input id="td" class="form-control floating datetimepicker" bsDatepicker type="text"
                  placeholder="DD-MM-YYYY" [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                  formControlName="startDate">
              </div>


            </div>
          </div>


          <div class="col-md-2">
            <label>To Date</label>
            <div class="cal-icon">
              <input id="td" class="form-control floating datetimepicker" bsDatepicker type="text"
                placeholder="DD-MM-YYYY" [bsConfig]="{ dateInputFormat: 'DD-MM-YYYY',  returnFocusToInput: true } "
                formControlName="endDate">
            </div>

          </div>


          <div class="col-md-4 mt-4">

            <button class="btn btn-success btn-ripple" type="submit" (click)="searchByButton()">
              <i class="fa fa-search" aria-hidden="true"></i> Search
            </button>&nbsp;&nbsp;&nbsp;

            <button class="btn btn-danger btn-ripple" (click)="resetform()">
              <i class="fa fa-eraser" aria-hidden="true"></i> Clear
            </button>&nbsp;&nbsp;&nbsp;

            <!-- <a class="btn btn-primary btn-ripple" (click)="orgFilter()"><i class="fa fa-filter"></i>
              Organization</a> -->

          </div>


        </div>
      </form>

    </div>
  </div>
  <!-- /Search Filter -->


  <!-- list view start -->
  <div class="row">
    <div class="col-md-12">
      <div class="card customCard">
        <div class="card-header">
          <div class="card-tools">
            <a class="btn btn-outline-primary" routerLink="/complainant-statement/award-proposal/create"><i
                class="fa fa-plus"></i>
              New &nbsp;&nbsp;&nbsp;</a>


          </div>
        </div>
        <div class="card-body customCard table-wrapper-scroll-y my-custom-scrollbar">
          <div class="table custom-table">

            <div class="d-flex justify-content-start pb-1">
              <div class="pgn-displayDataInfo">
                <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                  configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                  {{configPgn.totalItem}} ) entries</span>
              </div>
            </div>

            <table class="table table-striped custom-table datatable">
              <thead>
                <tr>
                  <th>SL</th>
                  <th>Code</th>
                  <th>Created By</th>
                  <th>Created Date</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>

                <tr *ngFor="let row of listData | paginate : {
                                itemsPerPage: pageSize,
                                currentPage: pageNum,
                                totalItems: totalItem
                            };let i = index" [class.active]="i == currentIndex">
                  <td>
                    {{ 1 + i}}
                  </td>

                  <td>{{row?.code}}</td>
                  <td>{{row?.loginCode}} - {{row?.displayName}}</td>
                  <td>
                    <span *ngIf="row?.createdAt">
                      {{row?.createdAt | date}}
                    </span>
                  </td>

                  <td *ngIf="row.approvalStatus === 'Submitted'">
                    <span class="badge badge-info">{{row.approvalStatus}}</span>
                  </td>
                  <td *ngIf="row.approvalStatus === 'Recommended'">
                    <span class="badge badge-warning">{{row.approvalStatus}}</span>
                  </td>
                  <td *ngIf="row.approvalStatus === 'Rejected'">
                    <span class="badge badge-danger">{{row.approvalStatus}}</span>
                  </td>
                  <td *ngIf="row.approvalStatus === 'Approved'">
                    <span class="badge badge-success">{{row.approvalStatus}}</span>
                  </td>

                  <td>

                    <a class="btn btn-sm btn-info" routerLink="/complainant-statement/award-proposal/view/{{row.id}}"><i
                        class="fa fa-eye m-r-5"></i></a>&nbsp;&nbsp;

                    <a class="btn btn-sm btn-info" routerLink="/complainant-statement/award-proposal/edit/{{row.id}}"><i
                        class="fa fa-pencil m-r-5"></i></a>&nbsp;&nbsp;

                    <a class="btn btn-sm btn-danger" data-toggle="modal" data-target="#delete_entity"
                      (click)="tempId = row.id">
                      <i class="fa fa-trash-o m-r-5"></i>
                    </a>

                  </td>


                </tr>
                <tr *ngIf="listData.length === 0">
                  <td colspan="10">
                    <h5 style="text-align: center;">No data found</h5>
                  </td>

                </tr>

              </tbody>

            </table>
            <div class="d-flex justify-content-end ">

              <div class="" [formGroup]="myFromGroup">
                  Items per Page
                  <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption"
                      formControlName="pageSize">
                      <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                          {{ size }}
                      </option>
                  </select>
              </div>

              <div class="pgn-pageSliceCt">
                  <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                      (pageChange)="handlePageChange($event)">
                  </pagination-controls>
              </div>

          </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- list view end -->


</div>

<!-- Delete Modal -->
<div class="modal custom-modal fade" id="delete_entity" role="dialog">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body">
        <div class="form-header">
          <h3>Delete Item</h3>
          <p>Are you sure want to delete?</p>
        </div>
        <div class="modal-btn delete-action">
          <div class="row">
            <div class="col-6">
              <a class="btn btn-primary continue-btn" (click)="deleteEnityData(tempId)">Delete</a>
            </div>
            <div class="col-6">
              <a data-dismiss="modal" class="btn btn-primary cancel-btn">Cancel</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- /Delete Modal -->
<app-org-search-modal-param-based *ngIf="organizationFilter"
  (closeModal)="handleModalData($event)"></app-org-search-modal-param-based>
<!-- /Page Content -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>