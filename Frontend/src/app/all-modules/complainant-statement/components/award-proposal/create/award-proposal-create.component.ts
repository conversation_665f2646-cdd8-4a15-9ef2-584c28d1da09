import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-award-proposal-create',
  templateUrl: './award-proposal-create.component.html',
  styleUrls: ['./award-proposal-create.component.css']
})
export class AwardProposalCreateComponent implements OnInit {


  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  private uploadForm: FormGroup;
  public awardPersonForm: FormGroup;
  public awardListData: any = [];
  public selectedEmployee: any = [];

  public isProposedScore: boolean = false;

  documentsSave = false;
  imgSrc: any;

  configDDL: any;
  configPgn: any;

  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private commonService: CommonService,
    private spinnerService: NgxSpinnerService,
    private employeeService: EmployeeService,
  ) {
    this.configPgn = {
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      totalItem: 50,
      pngDiplayLastSeq: 10,
      entityName: "",
    };

    this._initConfigDDL();
    this._customInitLoadData();
  }

  ngOnInit(): void {
    this.myForm = this.formBuilder.group({});

    this.awardPersonForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initEmpRow()])
    });

    this.uploadForm = this.formBuilder.group({
      fileUrl: [""],
    });

    this.getAward();
  }



  // --------------- Award Person Form Array ---------

  get awardPersonFormArr() {
    return this.awardPersonForm.get("Rows") as FormArray;

  }

  initEmpRow() {
    return this.formBuilder.group({
      employeeId: [""],
      employeeName: [""],
      propCategory: {},
      proposedScore: [""],
      amount: [""],
      scopeOfDevelopment: [""],
      developmentProposal: [""],
      processDate: [""],
      processTime: [""],
      savingCost: [""],
      othersBenefit: [""],
      fileUrl: [""],
    });

  }

  // ----------------- Add Row -------------------

  addRow() {
    this.awardPersonFormArr.push(this.initEmpRow());
  }

  // ----------------- Delete Row -----------------------

  deleteRow(index: number) {
    this.awardPersonFormArr.removeAt(index);
  }


  // --------------- Get  Award ----------------


  getAward() {
    let apiURL = this.baseUrl + "/api/hr-cr-award/get-all";
    let queryParams: any = {};
    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.awardListData = response;
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  // ------------- Submit Form  ----------------


  myFormSubmit() {

    if (this.myForm.invalid) {
      this.toastr.info("Please insert valid data")
      return;
    }

    let apiURL = this.baseUrl + "/developmentAward/save";

    let formData: any = {};

    for (let hb = 0; hb < this.awardPersonForm.value.Rows.length; hb++) {
      this.awardPersonForm.value.Rows[hb] = Object.assign(this.awardPersonForm.value.Rows[hb], {
        propCategory: this.awardPersonForm.value.Rows[hb].propCategory ? { id: this.awardPersonForm.value.Rows[hb].propCategory } : null,
      })
    }

    formData.developmentAwardProposalDetailsSet = this.awardPersonForm.value.Rows;

    this.spinnerService.show();

    this.commonService.sendPostRequest(apiURL, formData).subscribe(
      (response: any) => {
        console.log(response);

        this.toastr.success(response.message);
        this.spinnerService.hide();
        this.router.navigate(["/complainant-statement/award-proposal/list"], { relativeTo: this.route });
        if (this.documentsSave == false) {
          this.uploadLeaveImage(response.data.id, response.data.developmentAwardProposalDetailsSet);
        }

      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();

      }
    );

  }


  // ------------------------ Select DOC --------------------------


  onFileSelectDoc(event, i) {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      let fileName1 = file.type;
      if ((fileName1 === 'application/pdf') || (fileName1 === 'application/msword') || (fileName1 === 'image/jpeg') || (fileName1 === 'image/png') || (fileName1 === 'image/jpg')) {
        this.documentsSave = false;
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onload = (e) => (this.imgSrc = reader.result);
        this.uploadForm.get("fileUrl").setValue(file);
      }
      else {
        this.documentsSave = true;
        this.toastr.warning("Please choose correct format. (only PDF/DOC/PNG/JPG is applicable)");

      }
    }
  }

  // ------------------------ Upload DOC --------------------------

  uploadLeaveImage(id, developmentAwardProposalDetailsSet) {
    if (this.uploadForm.invalid) {
      this.toastr.warning("Please input all fields");
      return;
    }

    const formData = new FormData(); // Create a single FormData object

    const files = developmentAwardProposalDetailsSet.map(item => this.uploadForm.get("fileUrl").value); // Get an array of files

    // Append files and referenceFileIds to the FormData object
    for (let i = 0; i < developmentAwardProposalDetailsSet.length; i++) {
      formData.append("files", files[i], files[i].name);
      formData.append("referenceFileIds", developmentAwardProposalDetailsSet[i].id);
    }

    // Make a single request with the FormData object
    this.employeeService.uploadAwardProposalDoc(id, formData).subscribe(
      (data) => {
        this.spinnerService.hide();
        this.toastr.success("Successfully uploaded documents");
        this.router.navigate(['/complainant-statement/award-proposal/list']);
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }


  // ----------------- Get Employee Info ---------------

  getEmployeeInfo(i) {

    this.spinnerService.show();
    let empId = this.awardPersonForm.value.Rows[i].employeeId;
    let apiURL = this.baseUrl + "/hrCrEmp/getEmpDataByLoginCode/" + empId;
    let queryParams: any = {};

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.selectedEmployee = response;
        this.setFormValues(response, i);
        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    )
  }

  setFormValues(response, i) {
    this.awardPersonFormArr.controls[i].patchValue(response);
  }




  // ------------- Get Proposed Score By Award Category ------------------------

  getProposedScoreOfAward(award, i) {
    if (award.proposedScore) {
      this.awardPersonFormArr.controls[i].get('proposedScore').patchValue(award.proposedScore);
      this.isProposedScore = false;
    }
    else {
      this.awardPersonFormArr.controls[i].get('proposedScore').patchValue(award.proposedScore);
      this.isProposedScore = true;
    }
  }



  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any) {
    let q = event.term;
    console.log("search :: " + q)
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL();
  }

  scrollToEndDDL() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL();
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
          this.configDDL.listData2 = this.configDDL.listData.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
          this.configDDL.listData2 = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      listData2: [],

      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(event, activeFieldNameDDL, dataGetApiPathDDL, apiQueryFieldNameDDL) {

    console.log(event.target);

    if (this.configDDL.activeFieldName && this.configDDL.activeFieldName != activeFieldNameDDL) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL();

  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------


}
