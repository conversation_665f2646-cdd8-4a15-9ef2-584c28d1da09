import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AwardProposalCreateComponent } from './award-proposal-create.component';

describe('AwardProposalCreateComponent', () => {
  let component: AwardProposalCreateComponent;
  let fixture: ComponentFixture<AwardProposalCreateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AwardProposalCreateComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AwardProposalCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
