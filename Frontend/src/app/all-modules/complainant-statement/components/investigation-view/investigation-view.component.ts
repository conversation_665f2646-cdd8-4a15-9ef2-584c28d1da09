import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-investigation-view',
  templateUrl: './investigation-view.component.html',
  styleUrls: ['./investigation-view.component.css']
})
export class InvestigationViewComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myResponse: any = {};
  public actionList: any[] = [];
  public actionList2: any = {};
  public stepActionId: any = {};
  public parallelOrAutoActionList: any = {};
  public user: any;
  public formOfAction: FormGroup;
  public forDefendentView: boolean = false;


  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService,
  ) {

  }

  ngOnInit(): void {
    this.stepActionId = {};
    this.actionList2 = {};
    this.parallelOrAutoActionList = {};
    this.myResponse = {};
    this.forDefendentView = false;
    this.formOfAction = this.formBuilder.group({
      approvalStepAction: ["", Validators.required],

    });
    this.getApplicationData();
  }


  savehTRY(id: any, hrCrEmpId: any, thisNode: any, nextNode: any, approvalStatus: any) {

    let htryObj: any;
    let ParellOrAutoActionData: any = [];
    ParellOrAutoActionData = this.parallelOrAutoActionList[id];


    if (ParellOrAutoActionData[0].isParallel && ParellOrAutoActionData[0].isParallel == true) {

      htryObj = {
        referenceId: id,
        referenceEntity: "INVESTIGATION" + "/" + ParellOrAutoActionData[0].thisApprovalNode + "/" + ParellOrAutoActionData[0].nextApprovalNode + "/" + hrCrEmpId,
        approvalStepAction: { id: this.stepActionId[id] },
        approvalStepApproverEmp: null,
        approvalStepApprover: null,
        approvalStep: { id: ParellOrAutoActionData[0].stepId },
        approvalProcess: { id: ParellOrAutoActionData[0].processId },
        isParallelApprove: true,
        approvalStatus: approvalStatus
      }


    } else {

      htryObj = {
        referenceId: id,
        referenceEntity: "INVESTIGATION" + "/" + thisNode + "/" + nextNode + "/" + hrCrEmpId,
        approvalStepAction: { id: this.stepActionId[id] },
        approvalStatus: approvalStatus
      };

    }

    this.spinnerService.show();

    let apiURL = this.baseUrl + "/approvalProcTnxHtry/edit";
    this.commonService.sendPutRequest(apiURL, htryObj).subscribe(
      (response: any) => {

        let id = this.commonService.getData("id");
        if (!id || id == undefined || id == null) {
          id = this.route.snapshot.params.id;
        }

        this.commonService.setData("id", id);
        this.ngOnInit();
        //         this.getApplicationData();
        //  this.router.navigate(['/complainant-statement/investigation/view/'+id]);
        this.spinnerService.hide();
      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.info(error?.error?.message || "Something went wrong. Please try again.")
      }
    );

  }


  setApprovalStepActionId(index: any, actionId: any) {
    this.parallelOrAutoActionList[index] = this.actionList2[index];
    this.stepActionId[index] = actionId;

  }

  // --------------------- Get Application Data ----------------------

  getApplicationData() {

    this.spinnerService.show();

    let childIds = this.commonService.getData("childIds");
    let id = this.commonService.getData("id");
    this.route.paramMap.subscribe(params => {
      if (params.has('id')) {
        childIds = null;
        id = this.route.snapshot.params.id;

      }
    });

    let obj: any = {};
    obj = {
      id: id,
      childIds: childIds
    }
    const apiURL = this.baseUrl + '/investigation/approval/view';
    this.commonService.sendPostRequest(apiURL, obj).subscribe(
      (response: any) => {

        this.myResponse = response;
        this.getApprovalStepAction2();
        this.spinnerService.hide();

      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );

  }

  getApprovalStepAction2() {

    for (let defendent of this.myResponse.defendents) {
      let apiURL = this.baseUrl + "/approvalStepAction/getApprovalStepAction/" + defendent.id;
      let queryParams: any = {};
      queryParams['thisApprovalNode'] = defendent.thisNode;
      queryParams['nextApprovalNode'] = defendent.nextNode;
      queryParams[`approvalProcess`] = "INVESTIGATION";
      queryParams[`appliedHrCrEmpId`] = defendent.hrCrEmpId;

      this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
        (response: any) => {
          let key: any;
          key = defendent.id;
          this.actionList2[key] = response;

        },
        (error) => {
          let key: any;
          key = defendent.id;
          this.actionList2[key] = [];
        }
      );



    }

  }


}
