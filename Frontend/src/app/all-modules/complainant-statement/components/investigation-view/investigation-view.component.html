<!-- Page Content -->
<div class="content container-fluid">

  <!-- <PERSON> Header -->
  <div class="page-header">
    <div class="row align-items-center">
      <div class="col">

        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
          <li class="breadcrumb-item active">Complainant</li>
          <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Investigation</b></span>
          </li>
          <li class="breadcrumb-item active">View</li>
        </ul>
      </div>
      <div class="col-auto float-right ml-auto">
        <a class="btn add-btn" routerLink="/complainant-statement/forwarded-cases"><i class="fa fa-share"></i>
          Back To
          List</a>
      </div>
    </div>
  </div>
  <!-- /Page Header -->

  <!---- 
       <tr *ngFor="let item of myResponse.defendents">
     -->

  <!-- Results Table -->
  <div>
    <table class="table table-striped">
      <tbody>
        <tr>
          <td> Ref. Code : {{myResponse.code}}</td>
          <td>Case Category : {{myResponse.caseCategory?myResponse.caseCategory:'-'}}</td>
          <td>Complainant Type : {{ myResponse.complainantType?myResponse.complainantType:'-' }}</td>
          <td>Complainant : {{ myResponse.complainant?myResponse.complainant:'-' }}</td>
        </tr>

        <tr>
          <td>Assign To : {{myResponse.assignTo?myResponse.assignTo:'-'}}</td>
          <td>Assign By : {{myResponse.assignBy?myResponse.assignBy:'-'}} </td>
          <td>Assign To Assistant : {{myResponse.assignToAssistant?myResponse.assignToAssistant:'-'}} </td>
          <td>--</td>

        </tr>

        <tr>
          <td> Occurrence Date : {{myResponse.occurrenceDate?myResponse.occurrenceDate:'-'}}</td>
          <td> Occurrence Time : {{myResponse.occurrenceTime?myResponse.occurrenceTime:'-'}}</td>
          <td>Submission Date : {{myResponse.submissionDate?myResponse.submissionDate:'-'}}</td>
          <td>Submission Time : {{myResponse.submissionTime?myResponse.submissionTime:'-'}}</td>
        </tr>

        <tr>
          <td>Investigation Status :</td>
          <td>opinion : {{myResponse.opinion?myResponse.opinion:'-' }}</td>
          <td>Recommendation : {{ myResponse.recommendation?myResponse.recommendation:'-' }}</td>
          <td>Remarks : {{ myResponse.remarks?myResponse.remarks:'-' }}</td>
        </tr>




      </tbody>
    </table>
  </div>



  <div *ngFor="let df of  myResponse.defendents;let sl = index">
    <div class="card-header">
      ( {{sl+1}} ) Approval Path For {{df.empCode}} - {{df.empName}}
      [ Approval Status =
      <span *ngIf="df.approvalStatus=='Submitted'">Submitted</span>
      <span *ngIf="df.approvalStatus=='Approved'" style="color:green">Approved</span>
      <span *ngIf="df.approvalStatus=='Recommended'" style="color:rgb(189, 215, 88)">Recommended</span>
      <span *ngIf="df.approvalStatus=='Rejected'" style="color:red">Rejected</span>
      ]

      <hr>

      <ul style="list-style-type:none;">
        <li>
          Department : {{df.departmentTitle}} , Product Status: {{df.productStatus}}
        </li>
        <li>
          Action Type: {{df.actionTypes}} , Designation Order : {{df.dgOrder}}
        </li>
        <li>
          Designation : {{df.designation}}
        </li>
        <li>
          Deduction Score: {{df?.deductionScore}} , Deduct Amount : {{df?.deductAmount}}
        </li>
      </ul>

    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-8">
          <table>
            <tr>
              <th>SL</th>
              <th>Step Name</th>
              <th>Approval Path</th>
              <th>Sign By</th>
              <th>Designation</th>
              <th>Action Status</th>
              <th>Update Date</th>
              <th>Remarks</th>
            </tr>
            <tr *ngFor="let dtl of df.history; let i = index">
              <td>{{i+1}}</td>
              <td>{{dtl.approvalStep?.approvalGroupName}}</td>
              <td>{{dtl.approvalStepApproverEmp?dtl.approvalStepApproverEmp?.displayName:'-'}}</td>
              <td>{{dtl.actionTaker?dtl.actionTaker:'-'}}</td>
              <td>{{dtl.designation?dtl.designation:'-'}}</td>
              <td>{{dtl.actionStatus?dtl.actionStatus:'-'}}</td>
              <td>{{dtl.updateDateTime}}</td>
              <td>{{dtl.remarks?dtl.remarks:'-'}}</td>
            </tr>

          </table>

        </div>

        <div *ngIf="df.inMyPath==true && actionList2[df.id].length>0" class="col-md-2" style="text-align:left;">

          <form [formGroup]="formOfAction">
            <select class="select form-control" (change)="setApprovalStepActionId(
                      df.id,
                      $event.target.value
                      )" formControlName="approvalStepAction">
              <option value="">Select Action</option>
              <option *ngFor="let data of actionList2[df.id]" [value]="data.id">
                {{data.activityStatusTitle}}</option>
            </select>
            <br />
            <button class="btn btn-sm btn-success"
              (click)="savehTRY(df.id,df.hrCrEmpId,df.thisNode,df.nextNode,df.approvalStatus)">save</button>

          </form>

        </div>



      </div>
    </div>
  </div>










</div>
<!-- /Page Content -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>