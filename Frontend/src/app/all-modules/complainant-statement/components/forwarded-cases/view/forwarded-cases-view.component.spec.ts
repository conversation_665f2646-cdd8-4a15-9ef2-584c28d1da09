import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ForwardedCasesViewComponent } from './forwarded-cases-view.component';

describe('ForwardedCasesViewComponent', () => {
  let component: ForwardedCasesViewComponent;
  let fixture: ComponentFixture<ForwardedCasesViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ForwardedCasesViewComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ForwardedCasesViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
