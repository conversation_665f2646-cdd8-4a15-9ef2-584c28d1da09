import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ForwardedCasesComponent } from './forwarded-cases.component';

describe('ForwardedCasesComponent', () => {
  let component: ForwardedCasesComponent;
  let fixture: ComponentFixture<ForwardedCasesComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ForwardedCasesComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ForwardedCasesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
