import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { HrCrEmp } from 'src/app/all-modules/employees/model/HrCrEmp';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-forwarded-cases',
  templateUrl: './forwarded-cases.component.html',
  styleUrls: ['./forwarded-cases.component.css']
})
export class ForwardedCasesComponent implements OnInit {
  public listData: any = [];
  baseUrl = environment.baseUrl;
  myFromGroup: FormGroup;
  tempId: any;
  configDDL: any;
  user!: HrCrEmp;

  configPgn: any;

  public myForm: FormGroup;

  modalParam: any = {};

  public fileList: any = [];

  organizationFilter: boolean = false;

  constructor(
    private spinnerService: NgxSpinnerService,
    private employeeService: EmployeeService,
    private datePipe: DatePipe,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private login: LoginService,

  ) {


    this.configPgn = {
      // my props
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      // ngx plugin props
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };

  }

  ngOnInit(): void {

    this.myForm = this.formBuilder.group({
      startDate: [""],
      endDate: [""],
      empCodes: [""],
      refCode: [""],
      approvalStatus: [""],
    });

    this.myFromGroup = new FormGroup({
      pageSize: new FormControl()
    });
    this.myFromGroup.get('pageSize').setValue(this.configPgn.pageSize);
    this.loginUser();

    this.getAllData();

  }

  // ------------------------ Login User--------------------------

  loginUser() {
    this.user = this.login.getUser();
    this.myForm.controls.empCodes.setValue(this.user?.loginCode);
  }

  // --------------- Handle Organization Search ModaL -------------

  orgFilter() {
    this.organizationFilter = true;
  }

  handleModalData(param: any) {
    this.modalParam = param;
    this.organizationFilter = false;
    this.handlePageChange(1);
  }

  //-------------- Search By Button -------------

  searchByButton() {
    this.handlePageChange(1);
  }

  // -------------------- Reset Form --------------------

  resetform() {
    this.myForm.reset();
    this.myForm.controls.approvalStatus.setValue("All");
    this.modalParam = {};
    this.handlePageChange(1);
  }



  // ------------------------------pagination handling methods end -------------------------------------------

  getAllData() {

    let apiURL = this.baseUrl + "/investigation/getAll";

    let queryParams = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);

    this.spinnerService.show();

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(

      (response: any) => {

        this.listData = response.objectList;
        this.configPgn.totalItem = response.totalItems;
        this.configPgn.totalItems = response.totalItems;
        this.setDisplayLastSequence();
        this.spinnerService.hide();

      },


      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );

  }


  getAllFiles(files) {
    this.fileList = files.split(',');
  }


  redirectToImage(fileName) {
    window.open(this.baseUrl + fileName, "_blank");
  }

  getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    params = this.modalParam

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }


    if (this.myForm.get("refCode").value) {
      params.refCode = this.myForm.get("refCode").value;
    }
    if (this.myForm.get("startDate").value) {
      params["startDate"] = this.datePipe.transform(this.myForm.get("startDate").value, "yyyy-MM-dd");
    }

    if (this.myForm.get("endDate").value) {
      params["endDate"] = this.datePipe.transform(this.myForm.get("endDate").value, "yyyy-MM-dd");
    }
    if (this.myForm.get("empCodes").value) {
      params.empCodes = this.myForm.get("empCodes").value;
    }
    if (this.myForm.controls.approvalStatus.value) {

      params.approvalStatus = this.myForm.controls.approvalStatus.value;
    }

    return params;
  }

  // ------------------pagination handling methods start -----------------------------

  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }
  handlePageChange(event: number) {
    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this.getAllData();
  }
  handlePageSizeChange(event: any): void {
    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this.getAllData();
  }


}
