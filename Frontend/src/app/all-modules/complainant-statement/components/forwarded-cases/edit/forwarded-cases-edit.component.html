<!-- Page Content -->
<div class="content container-fluid">

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">

                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Complainant</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Forwarded Cases</b></span>
                    </li>
                    <li class="breadcrumb-item active">Edit</li>
                </ul>
            </div>
            <div class="col-auto float-right ml-auto">
                <a class="btn add-btn" routerLink="/complainant-statement/forwarded-cases"><i class="fa fa-share"></i>
                    Back To
                    List</a>
            </div>
        </div>
    </div>
    <!-- /Page Header -->



    <div class="row">
        <div class="col-lg-12">
            <div class="card customCard">
                <div class="card-body">
                    <form novalidate (ngSubmit)="updateForm()" [formGroup]="myForm">


                        <div class="row">

                            <div class="col-md-3">

                                <div class="form-group">
                                    <label>Complainant</label>
                                    <ng-select [items]="configDDL.listData" formControlName="complainantId"
                                        placeholder="Select Employee" bindLabel="ddlDescription" bindValue="ddlCode"
                                        [searchable]="true" [clearable]="true" [virtualScroll]="true"
                                        [clearOnBackspace]="true" (search)="searchDDL2($event)"
                                        (scrollToEnd)="scrollToEndDDL2()" (clear)="clearDDL()"
                                        (click)="initSysParamsDDL2($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes')"
                                        ddlActiveFieldName="ddlDescription" class="custom-ng-select" [appendTo]="'body'"
                                        [readonly]="true">
                                    </ng-select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Complainant Ref Code</label>
                                    <input type="text" class="form-control" formControlName="complainantRefCode"
                                        readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Complain Type</label>
                                    <input type="text" class="form-control" formControlName="complainantType" readonly>

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea type="text" class="form-control" rows="1" formControlName="description"
                                        readonly></textarea>
                                </div>

                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Occurrence Date</label>
                                    <input type="text" class="form-control" formControlName="occurrenceDate" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Occurrence Time</label>
                                    <input type="text" class="form-control" formControlName="occurrenceTime" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Submission Date</label>
                                    <input type="text" class="form-control" formControlName="submissionDate" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Submission Time</label>
                                    <input type="text" class="form-control" formControlName="submissionTime" readonly>

                                </div>
                            </div>


                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Report HeadLine</label>
                                    <input type="text" class="form-control" formControlName="reportHeadLine">

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Case Category</label>
                                    <input type="text" class="form-control" formControlName="caseCategory" readonly>

                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Case Value</label>
                                    <input type="number" class="form-control" formControlName="caseValue" readonly>

                                </div>
                            </div>




                            <div class="col-md-3">

                                <div class="form-group">
                                    <label class="val-required">Investigator</label>
                                    <ng-select [items]="configDDL.listData2" formControlName="assignTo"
                                        placeholder="Select Employee" bindLabel="ddlDescription" bindValue="ddlCode"
                                        [searchable]="true" [clearable]="true" [virtualScroll]="true"
                                        [clearOnBackspace]="true" (search)="searchDDL2($event)"
                                        (scrollToEnd)="scrollToEndDDL2()" (clear)="clearDDL()"
                                        (click)="initSysParamsDDL2($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes')"
                                        ddlActiveFieldName="ddlDescription" class="custom-ng-select" [appendTo]="'body'"
                                        [readonly]="true">
                                    </ng-select>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Investigator's Assistants</label>
                                    <textarea type="text" class="form-control" rows="1"
                                        formControlName="assignToAssistant" placeholder="Example: 54546,54840,54546"
                                        readonly></textarea>
                                </div>

                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="val-required">Case Status</label>
                                    <select class="form-control" formControlName="investigationStatus">
                                        <option value=null>Select Any</option>
                                        <option value="Assigned">Assigned</option>
                                        <option value="Processing">Report Submitted</option>
                                    </select>

                                </div>
                            </div>

                            <div class="col-md-3">

                                <div class="form-group">
                                    <label>Remarks</label>
                                    <span class="float-right">
                                        {{ numberOfCharRmrks2 }} / {{maxNumOfCharRmrks2}}
                                    </span>
                                    <textarea type="text" class="form-control" rows="1" formControlName="remarks"
                                        (keyup)="remarksCharCount2($event)"></textarea>

                                </div>

                            </div>

                            <div class="col-md-3">

                                <div class="form-group">
                                    <label>Abstract</label>
                                    <span class="float-right">
                                        {{ numberOfCharRmrks }} / {{maxNumOfCharRmrks}}
                                    </span>
                                    <textarea type="text" class="form-control" rows="1"
                                        formControlName="abstractOptional"
                                        (keyup)="remarksCharCount($event)"></textarea>

                                </div>

                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Investigation Reports (PDF/DOC/ Image/video)</label>
                                    <input class="form-control" type="file" name="files"
                                        (change)="onFileSelectDoc($event)" multiple />
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Upload Committee Signed Report (PDF)</label>
                                    <input class="form-control" type="file" name="files"
                                        (change)="onFileSelectDoc2($event)" />
                                </div>
                            </div>

                            <div class="col-md-12 mt-3">
                                <h3>Defendant Details</h3>
                                <hr />
                                <form [formGroup]="defendantDetailsForm">
                                    <div class="table-responsive col-md-12">
                                        <table class="table table-striped custom-table">
                                            <thead>
                                                <tr>
                                                    <th>SL</th>
                                                    <th>Employee</th>
                                                    <th>Deduct Score</th>
                                                    <th>Deduct Amount</th>
                                                    <th>Investigation Recommendation</th>
                                                    <th>Status</th>
                                                    <th>APDC Opinion</th>
                                                    <th>Others Recommendation</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody formArrayName="Rows">
                                                <tr *ngFor="let itemrow of defendantFormArr.controls;let i = index;let l = last"
                                                    [formGroupName]="i">
                                                    <td>{{ i + 1 }}</td>

                                                    <td>
                                                        <ng-select [items]="configDDL.listData3[i]"
                                                            formControlName="defendantId" placeholder="Select Employee"
                                                            bindLabel="ddlDescription" bindValue="ddlCode"
                                                            [searchable]="true" [clearable]="true"
                                                            [virtualScroll]="true" [clearOnBackspace]="true"
                                                            (search)="searchDDL($event , i)"
                                                            (scrollToEnd)="scrollToEndDDL(i)" (clear)="clearDDL()"
                                                            (click)="initSysParamsDDL($event, 'ddlDescription', '/api/common/getEmpSpec', 'empCodes', i)"
                                                            ddlActiveFieldName="ddlDescription" class="custom-ng-select"
                                                            [appendTo]="'body'">
                                                        </ng-select>
                                                    </td>

                                                    <td style="min-width: 200px;">
                                                        <input class="form-control" formControlName="deductionScore"
                                                            type="number" disabled />
                                                    </td>
                                                    <td style="min-width: 200px;">
                                                        <input class="form-control" formControlName="deductionAmount"
                                                            type="number" disabled />
                                                    </td>

                                                    <td style="min-width: 200px;">


                                                        <textarea type="text" class="form-control" rows="1"
                                                            formControlName="recommendation"></textarea>

                                                    </td>

                                                    <td style="min-width: 200px;">
                                                        <select class="select form-control" formControlName="status">
                                                            <option value="">Select Any</option>
                                                            <option value="Agree">Agree</option>
                                                            <option value="Not Agree">Not Agree</option>
                                                        </select>
                                                    </td>

                                                    <td style="min-width: 200px;">


                                                        <textarea type="text" class="form-control" rows="1"
                                                            formControlName="opinion"></textarea>

                                                    </td>



                                                    <td style="min-width: 200px;">

                                                        <textarea type="text" class="form-control" rows="1"
                                                            formControlName="otherRecommendation"></textarea>

                                                    </td>



                                                    <td>
                                                        <button *ngIf="
                                                defendantDetailsForm.controls.Rows.controls.length >
                                                0
                                              " (click)="deleteRow(i)" class="btn btn-danger">
                                                            <i class="text-center" class="fa fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <br />
                                    <button type="button" (click)="addRow()" class="btn btn-info">
                                        <i class="text-center" class="fa fa-plus"></i>
                                    </button>
                                </form>
                            </div>

                        </div>


                        <div class="text-right">
                            <button type="button" class="btn btn-secondary btn-ripple" (click)="resetFormValues()">
                                <i class="fa fa-undo" aria-hidden="true"></i> Reset
                            </button>
                            &nbsp; &nbsp;
                            <button type="submit" class="btn btn-primary btn-ripple" [disabled]="!myForm.valid">
                                <i class="fa fa-check" aria-hidden="true"></i> Update &nbsp;&nbsp;&nbsp;
                            </button>
                        </div>


                    </form>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /Page Content -->

<ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
</ngx-spinner>