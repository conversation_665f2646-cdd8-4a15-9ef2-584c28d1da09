import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/all-modules/settings/common/services/common.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-forwarded-cases-edit',
  templateUrl: './forwarded-cases-edit.component.html',
  styleUrls: ['./forwarded-cases-edit.component.css']
})
export class ForwardedCasesEditComponent implements OnInit {

  public baseUrl = environment.baseUrl;
  public myForm: FormGroup;
  public myUploadForm: FormGroup;
  public myUploadForm2: FormGroup;
  public defendantDetailsForm: FormGroup;
  public myFormData: any = {};

  imgSrc: any;
  docSeleted = false;
  docSeleted2 = false;

  // for multi select
  public configDDL: any;
  public configPgn: any;
  public user: any;


  maxNumOfCharRmrks = 4000;
  numberOfCharRmrks = 0;

  maxNumOfCharRmrks2 = 4000;
  numberOfCharRmrks2 = 0;


  constructor(
    private formBuilder: FormBuilder,
    private datePipe: DatePipe,
    private route: ActivatedRoute,
    private router: Router,
    private spinnerService: NgxSpinnerService,
    private commonService: CommonService,
    private toastr: ToastrService
  ) {
    this.configPgn = {
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      totalItem: 50,
      pngDiplayLastSeq: 10,
      entityName: "",
    };

    this._initConfigDDL();
    this._customInitLoadData();
    this._initConfigDDL2();
    this._customInitLoadData2();
  }

  ngOnInit(): void {
    this._initForm();
    this.defendantDetailsForm = this.formBuilder.group({
      Rows: this.formBuilder.array([this.initDocRows()])
    });
    this.getApplicationData();
  }

  // Textarea field character Count

  remarksCharCount(event: any): void {
    this.numberOfCharRmrks = event.target.value.length;

    if (this.numberOfCharRmrks > this.maxNumOfCharRmrks) {
      event.target.value = event.target.value.slice(0, this.maxNumOfCharRmrks);
      this.numberOfCharRmrks = this.maxNumOfCharRmrks;
    }
  }

  remarksCharCount2(event: any): void {
    this.numberOfCharRmrks2 = event.target.value.length;

    if (this.numberOfCharRmrks2 > this.maxNumOfCharRmrks2) {
      event.target.value = event.target.value.slice(0, this.maxNumOfCharRmrks2);
      this.numberOfCharRmrks2 = this.maxNumOfCharRmrks2;
    }
  }


  _initForm() {
    this.myForm = this.formBuilder.group({
      id: [''],
      code: [""],
      complainantId: [""],
      complainantType: [""],
      complainantRefCode: [""],
      description: [""],
      occurrenceDate: [""],
      occurrenceTime: [""],
      submissionDate: [""],
      submissionTime: [""],
      createdAt: [""],

      reportHeadLine: [""],
      caseCategory: [''],
      caseValue: ["", [Validators.required]],
      assignTo: [null, [Validators.required]],
      assignToAssistant: [""],
      approvalStatus: [''],
      remarks: [""],
      abstractOptional: [""],
      investigationStatus: [""],

      investigationFileUpload: [''],
      enquiryCommitteeFile: [''],
      attachmentHod: [''],

    });

    this.myUploadForm = this.formBuilder.group({
      files: [""],
    });

    this.myUploadForm2 = this.formBuilder.group({
      files: [""],
    });
  }

  resetFormValues() {
    this.myForm.reset();

  }


  // --------------- Defendant Details Form ---------

  get defendantFormArr() {
    return this.defendantDetailsForm.get("Rows") as FormArray;

  }

  initDocRows() {
    return this.formBuilder.group({
      id: [""],
      defendant: [null],
      irregularCategory: [""],
      defendantId: [""],
      irregularity: [""],
      proposedActionCategory: [""],
      awardPunishmentProposal: [""],
      deductionType: [""],
      deductionAmount: [""],
      presentScore: [""],
      approvalStatus: [""],
      approvalStep: [""],
      deductionScore: [""],
      approvalProcess: [""],
      proposedActionType: [""],
      misconduct: [""],
      subMisconduct: [""],


      // Used by Forward Case

      recommendation: [""],
      status: [""],
      opinion: [""],
      otherRecommendation: [""],
    });
  }

  addRow() {
    this.defendantFormArr.push(this.initDocRows());
  }

  deleteRow(index: number) {
    this.defendantFormArr.removeAt(index);
  }


  // --------------------- Get Application Data ----------------------

  getApplicationData() {
    const id = this.route.snapshot.params.id;
    const apiURL = this.baseUrl + '/investigation/get/' + id;

    const queryParams: any = {};
    this.spinnerService.show();
    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        this.myFormData = response.data;


        // complainantId field 
        let hrCrEmpVal = [
          {
            ddlCode: response.data?.complainantId,
            ddlDescription: response.data?.complainantLoginCode + "-" + response.data?.complainantDisplayName,
          },
        ];
        this.configDDL.listData = hrCrEmpVal;
        this.myFormData.complainantId = response.data?.complainantId;

        // assignTo field 
        let hrCrEmpVal2 = [
          {
            ddlCode: response.data?.assignToId,
            ddlDescription: response?.data?.assignToCode + "-" + response?.data?.assignToName,
          },
        ];
        this.configDDL.listData2 = hrCrEmpVal2;
        this.myFormData.assignTo = response?.data?.assignToId;

        this.myFormData.reportHeadLine = response?.data?.reportHeadline;
        this.myForm.patchValue(this.myFormData);
        this.setdefendantDetails(response.data?.investigatorDetailsSet);
        this.spinnerService.hide();

      },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
      }
    );
  }

  // ----------------------- Set defendant Details -------------------------

  setdefendantDetails(response) {
    this.defendantFormArr.clear();
    for (let index = 0; index < response.length; index++) {
      this.defendantFormArr.push(this.returnInItDocRows(response[index], index));
    }
  }

  returnInItDocRows(data, index) {

    let hrCrEmpVal = [
      {
        ddlCode: data?.defendantId,
        ddlDescription:
          data?.defendantCode +
          "-" +
          data?.defendantName,
      },
    ];

    this.configDDL.listData3[index] = hrCrEmpVal;

    return this.formBuilder.group({
      id: data.id,
      defendantId: data.defendantId,
      approvalStatus: data.approvalStatus,
      approvalStep: data.approvalStep?.id,
      approvalProcess: data.approvalProcess?.id,
      proposedActionType: data.proposedActionType,
      deductionScore: data.deductionScore,
      deductionAmount: data.deductionAmount,
      irregularCategory: data?.irregularCategory,
      irregularity: data?.irregularity,
      proposedActionCategory: data?.proposedActionCategory,
      deductionType: data?.deductionType,
      employeeName: data.employeeName,
      presentScore: data.presentScore,
      misconduct: data.misconduct,
      subMisconduct: data.subMisconduct,

      recommendation: data.recommendation,
      status: data.status,
      opinion: data.opinion,
      otherRecommendation: data.otherRecommendation,
    });

  }

  // ------------------ Update Form ------------------

  updateForm() {

    const id = this.route.snapshot.params.id;
    const apiURL = this.baseUrl + '/investigation/edit/' + id;
    let formData: any = {};
    formData = Object.assign(this.myForm.value, {
      assignTo: this.getAssignTo.value ? { id: this.getAssignTo.value } : null,
      complainantId: { id: this.myForm.controls.complainantId.value }

    })

    for (let i = 0; i < this.defendantDetailsForm.value.Rows.length; i++) {
      this.defendantDetailsForm.value.Rows[i] = Object.assign(this.defendantDetailsForm.value.Rows[i], {
        defendant: this.defendantDetailsForm.value.Rows[i].defendantId ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].defendantId) } : null,
        irregularCategory: this.defendantDetailsForm.value.Rows[i].irregularCategory ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].irregularCategory) } : null,
        irregularity: this.defendantDetailsForm.value.Rows[i].irregularity ? { id: parseInt(this.defendantDetailsForm.value.Rows[i].irregularity) } : null,
        approvalStep: this.defendantDetailsForm.value.Rows[i].approvalStep ?
          { id: parseInt(this.defendantDetailsForm.value.Rows[i].approvalStep) } : null,
        approvalProcess: this.defendantDetailsForm.value.Rows[i].approvalProcess ?
          { id: parseInt(this.defendantDetailsForm.value.Rows[i].approvalProcess) } : null,
        proposedActionType: this.defendantDetailsForm.value.Rows[i].proposedActionType ?
          { id: parseInt(this.defendantDetailsForm.value.Rows[i].proposedActionType) } : null,
        misconduct: this.defendantDetailsForm.value.Rows[i].misconduct ?
          { id: parseInt(this.defendantDetailsForm.value.Rows[i].misconduct) } : null,

      })
    }


    formData.investigatorDetailsSet = this.defendantDetailsForm.value.Rows;

    // console.log("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");

    // console.log(formData);

    // console.log("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");

    this.spinnerService.show();
    this.commonService.sendPutRequest(apiURL, formData).subscribe((response: any) => {
      this.toastr.success("Updated Successfully");
      this.uploadFiles(response?.id);


    },
      (error) => {
        this.spinnerService.hide();
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.ngOnInit();
      }
    );

  }


  // ------------------------ Select DOC --------------------------

  onFileSelectDoc(event) {
    if (event.target.files.length > 0) {
      this.docSeleted = true;
      let fileList = event.target.files;

      for (let i = 0; i < fileList.length; i++) {
        const reader = new FileReader();
        reader.readAsDataURL(fileList[i]);
        reader.onload = (e) => (this.imgSrc = reader.result);
      }

      this.myUploadForm.get("files").setValue(fileList);
    }
    else {
      this.docSeleted = false;
    }
  }


  // ------------------------ Select DOC --------------------------

  onFileSelectDoc2(event) {
    if (event.target.files.length > 0) {
      this.docSeleted2 = true;
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      reader.onload = (e) => (this.imgSrc = reader.result);
      this.myUploadForm2.get("files").setValue(file);
    }
    else {
      this.docSeleted2 = false;
    }
  }


  // ----------------------- Upload Files -----------------------------

  uploadFiles(id) {

    if (this.docSeleted) {

      let apiURL = this.baseUrl + "/investigation/uploadFileInvestigator/" + id;

      let formData = new FormData();

      for (let i = 0; i < this.myUploadForm.get("files").value.length; i++) {
        formData.append("files", this.myUploadForm.get("files").value[i]);
      }
      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (response: any) => {
          this.toastr.success("Investigator Reports Uploaded Successfully");

          this.router.navigate(['/complainant-statement/forwarded-cases']);
          this.spinnerService.hide();
        },
        (error) => {
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
          this.spinnerService.hide();
        }
      );


    }

    this.uploadFiles2(id);

  }



  uploadFiles2(id) {

    if (this.docSeleted2) {

      let apiURL = this.baseUrl + "/investigation/uploadFileCommittee/" + id;

      const formData = new FormData();
      formData.append("file", this.myUploadForm2.get("files").value);
      formData.append("type", "file");

      this.commonService.sendPostRequest(apiURL, formData).subscribe(
        (response: any) => {
          this.toastr.success("Committee Files Uploaded Successfully");
          this.router.navigate(['/complainant-statement/forwarded-cases']);
          this.spinnerService.hide();
        },
        (error) => {
          this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
          this.spinnerService.hide();
        }
      );


    }
    else {
      this.spinnerService.hide();
      this.router.navigate(['/complainant-statement/forwarded-cases']);
    }


  }

  get getAssignTo() {
    return this.myForm.get("assignTo");
  }

  get getAssignBy() {
    return this.myForm.get("assignBy");
  }


  get f() {
    return this.myForm.controls;
  }

  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL(event: any, i) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL(i);
  }

  scrollToEndDDL(i) {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL(i);
  }

  _customInitLoadData() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL() {
    this.configDDL.q = "";
  }

  private getListDataDDL(i) {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {

          this.configDDL.listData3[i] = this.configDDL.listData2.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData3[i] = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL() {
    this._initConfigDDL();
  }

  _initConfigDDL() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      listData2: [],
      listData3: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL(event, activeFieldNameDDL, dataGetApiPathDDL, apiQueryFieldNameDDL, i) {

    if (this.configDDL.activeFieldName && this.configDDL.activeFieldName != activeFieldNameDDL) {
      this.setDefaultParamsDDL();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL(i);
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------

  // --------------------------- DDL (Dinamic Dropdown List) Methods Start -----------------------------------
  searchDDL2(event: any) {
    let q = event.term;
    this.configDDL.q = q;
    this.configDDL.pageNum = 1;
    this.configDDL.append = false;
    this.getListDataDDL2();
  }

  scrollToEndDDL2() {
    this.configDDL.pageNum++;
    this.configDDL.append = true;
    this.getListDataDDL2();
  }

  _customInitLoadData2() {
    this.configDDL.activeFieldName = "ddlDescription";
    this.configDDL.dataGetApiPath = "/api/common/getEmp";
    this.configDDL.apiQueryFieldName = "empCodes";
    // this.getListDataDDL();
  }

  clearDDL2() {
    this.configDDL.q = "";
  }

  private getListDataDDL2() {
    let apiURL = this.baseUrl + this.configDDL.dataGetApiPath;

    let queryParams: any = {};
    queryParams.pageNum = this.configDDL.pageNum;
    queryParams.pageSize = this.configDDL.pageSize;
    if (this.configDDL.q && this.configDDL.q != null) {
      queryParams[this.configDDL.apiQueryFieldName] = this.configDDL.q;
    }

    this.commonService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        if (this.configDDL.append) {
          this.configDDL.listData = this.configDDL.listData.concat(
            response.objectList
          );
          this.configDDL.listData2 = this.configDDL.listData2.concat(
            response.objectList
          );
        } else {
          this.configDDL.listData = response.objectList;
          this.configDDL.listData2 = response.objectList;
        }
        this.configDDL.totalItem = response.totalItems;
      },
      (error) => {
        console.log(error);
      }
    );
  }

  setDefaultParamsDDL2() {
    this._initConfigDDL2();
  }

  _initConfigDDL2() {
    this.configDDL = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      listData: [],
      listData2: [],
      listData3: [],
      append: false,
      q: "",
      activeFieldName: "xxxFieldName",
      dataGetApiPath: "",
      apiQueryFieldName: "xxxFieldName",
    };
  }

  initSysParamsDDL2(event, activeFieldNameDDL, dataGetApiPathDDL, apiQueryFieldNameDDL) {

    if (this.configDDL.activeFieldName && this.configDDL.activeFieldName != activeFieldNameDDL) {
      this.setDefaultParamsDDL2();
    }

    this.configDDL.activeFieldName = activeFieldNameDDL;
    this.configDDL.dataGetApiPath = dataGetApiPathDDL;
    this.configDDL.apiQueryFieldName = apiQueryFieldNameDDL;
    this.getListDataDDL2();
  }
  // --------------------------- DDL (Dinamic Dropdown List) Methods End -------------------------------------
}
