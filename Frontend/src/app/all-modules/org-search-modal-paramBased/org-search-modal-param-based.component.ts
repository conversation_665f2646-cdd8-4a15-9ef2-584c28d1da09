import { DatePipe } from '@angular/common';
import { Component, ElementRef, EventEmitter, OnInit, Output, Renderer2 } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment';
import { EmployeeService } from '../employees/services/employee.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-org-search-modal-param-based',
  templateUrl: './org-search-modal-param-based.component.html',
  styleUrls: ['./org-search-modal-param-based.component.css']
})
export class OrgSearchModalParamBasedComponent implements OnInit {



  public baseUrl = environment.baseUrl;
  allSearchParams = [];

  payrollLocation = [];
  payrollSubLocation = [];
  payrollSubLocation2 = [];
  public departments = [];
  public departments2 = [];
  public sections = [];
  public sections2 = [];
  public subSections = [];
  public subSections2 = [];
  public teams = [];
  public teams2 = [];
  public subTeams = [];
  public subTeams2 = [];
  public products = [];
  public products2 = [];
  public operating_units = [];
  public operating_units2 = [];

  public status = [];

  public statusTitle: string;
  public allOrgMstDept: string;
  public allOrgMstSections: string;
  public allOrgMstOpunit: string;
  public allOrgMstProduct: string;
  public allOrgMstSubSections: string;
  public allOrgMstTeamTitle: string;
  public allOrgMstSubTeamTitle: string;
  public payrollLocationId: string;
  public payrollSubLocationId: string;


  formData: any = {}; // Define the form data structure here
  @Output() closeModal: EventEmitter<any> = new EventEmitter<any>();


  constructor(
    private toastr: ToastrService,
    private currentRoute : Router,
    private formBuilder: FormBuilder,
    private employeeService: EmployeeService,
    private spinnerService: NgxSpinnerService,
    private datePipe: DatePipe,
    private renderer: Renderer2, private elementRef: ElementRef
  ) { }

  onSubmit() {

    this.getOrgParams();

  }

  close() {

    this.closeModal.emit(this.allSearchParams);
  }


  ngOnInit(): void {
    this.statusTitle = "Active"; // by Default Active
    this.loadAllstatus();
    this.getUserOrgAccess();
    // this.loadAllPayrollLocation();
    // this.loadPayloadSubLocations();
    // this.loadAllOperatingUnits();
    // this.loadAllProducts();
    // this.loadAllDepartments();
    // this.loadAllSections();
    // this.loadAllSubSections();
    // this.loadAllTeams();
    // this.loadAllSubTeams();
  }

  // ================= Get Login User's Organogram Access ===================


  getUserOrgAccess() {

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";

    const params = this.getUserQueryParams3();

    this.employeeService.sendGetRequest(apiURL, params).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) =>
          a?.localeCompare(b)
        );
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) =>
          a?.localeCompare(b)
        );
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );
  }


  // --------------------------- Get All Employee Status ----------------------

  loadAllstatus() {
    let status = "EMP_STS_NEW";
    let apiURL = this.baseUrl + "/alkp/searchByParent/" + status;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.status = response;

      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get Payroll Location ----------------------

  loadAllPayrollLocation() {
    let apiURL = this.baseUrl + "/alkp/alkpPayrollLoc";
    let queryParams: any = {};
    this.payrollLocation.splice(0, this.payrollLocation.length);

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollLocation = response?.map(item => item.title)
          .filter(title => title !== undefined)
          .sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get Payroll Sub Location ----------------------

  loadPayloadSubLocations() {
    let orgType = "JOB_LOCATION";
    let apiURL = this.baseUrl + "/alkp/search/" + orgType;
    let queryParams: any = {};
    this.payrollSubLocation.splice(0, this.payrollSubLocation.length);

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollSubLocation.push(response?.subALKP);
        if (Array.isArray(response?.subALKP)) {
          this.payrollSubLocation2 = response?.subALKP
            .map(item => item.title) // Extract only the 'title' property
            .filter(title => title !== undefined) // Filter out undefined titles (optional)
            .sort((a, b) => a?.localeCompare(b)); // Sort the titles alphabetically
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }



  // --------------------------- Get All Section ----------------------

  loadAllSections() {
    let orgType = "SECTION";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.sections = response?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get All Sub-Section ----------------------

  loadAllSubSections() {
    let orgType = "SUB_SECTION";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subSections = response?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get All Team ----------------------

  loadAllTeams() {
    let orgType = "TEAM";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.teams = response?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get All Sub-Team ----------------------

  loadAllSubTeams() {
    let orgType = "SUB_TEAM";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.subTeams = response?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }


  // --------------------------- Get All Product ----------------------

  loadAllProducts() {
    let orgType = "PRODUCT";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.products = response?.sort((a, b) =>
          a.title?.localeCompare(b.title)
        );
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get All Operating Unit ----------------------

  loadAllOperatingUnits() {
    let orgType = "OPERATING_UNIT";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.operating_units = response?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------------------- Get All Designation ----------------------

  loadAllDepartments() {
    let orgType = "DEPARTMENT";
    let apiURL = this.baseUrl + "/allOrgMst/search2/" + orgType;

    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.departments = response?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------- Change Status --------------------

  searchBystatusTitle(statusTitle) {
    this.statusTitle = statusTitle
  }


  // -------------- Search by Legal Entity Title -----------------

  searchByOperatingUnit(val) {

    $("#empgrpSearch").val("");
    console.log("search by Operating Unit " + val);
    this.allOrgMstOpunit = val;
    this.products = [];
    this.allOrgMstProduct = "";
    this.departments = [];
    this.allOrgMstDept = "";
    this.sections = [];
    this.allOrgMstSections = "";
    this.subSections = [];
    this.allOrgMstSubSections = "";
    this.teams = [];
    this.allOrgMstTeamTitle = "";
    this.subTeams = [];
    this.allOrgMstSubTeamTitle = "";

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";


    const params = this.getUserQueryParams3();

    this.employeeService.sendGetRequest(apiURL, params).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }

  // -------------- Search by Product Title & Load Childs-----------------

  searchByProduct(val) {

    $("#empgrpSearch").val("");
    console.log("search by Product" + val);
    this.allOrgMstProduct = val;
    this.allOrgMstDept = "";
    this.departments = [];
    this.allOrgMstSections = "";
    this.sections = [];
    this.allOrgMstSubSections = "";
    this.subSections = [];
    this.allOrgMstTeamTitle = "";
    this.teams = [];
    this.allOrgMstSubTeamTitle = "";
    this.subTeams = [];

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";


    let queryParams: any = {};

    const params = this.getUserQueryParams3();
    queryParams = params;

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }

  // -------------- Search by Department Title & Load Childs -----------------

  searchByDepartment(val) {

    $("#empgrpSearch").val("");
    console.log("Search by Department " + val);
    this.allOrgMstDept = val;
    this.allOrgMstSections = "";
    this.sections = [];
    this.allOrgMstSubSections = "";
    this.subSections = [];
    this.allOrgMstTeamTitle = "";
    this.teams = [];
    this.allOrgMstSubTeamTitle = "";
    this.subTeams = [];

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";


    let queryParams: any = {};

    const params = this.getUserQueryParams3();
    queryParams = params;

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }

  // -------------- Search by Section Title & Load Childs -----------------

  searchBySection(val) {

    $("#empgrpSearch").val("");
    console.log("search by Section" + val);
    this.allOrgMstSections = val;
    this.allOrgMstSubSections = "";
    this.subSections = [];
    this.allOrgMstTeamTitle = "";
    this.teams = [];
    this.allOrgMstSubTeamTitle = "";
    this.subTeams = [];

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";


    let queryParams: any = {};

    const params = this.getUserQueryParams3();
    queryParams = params;

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }

  // -------------- Search by Sub Section Title & Load Childs -----------------

  searchBySubSection(val) {

    $("#empgrpSearch").val("");
    console.log("search by SubSection" + val);
    this.allOrgMstSubSections = val;
    this.allOrgMstTeamTitle = "";
    this.teams = [];
    this.allOrgMstSubTeamTitle = "";
    this.subTeams = [];

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";


    let queryParams: any = {};

    const params = this.getUserQueryParams3();
    queryParams = params;

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }

  // -------------- Search by Team Title & Load Childs -----------------

  searchByTeam(val) {

    $("#empgrpSearch").val("");
    this.allOrgMstTeamTitle = val;

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";


    let queryParams: any = {};

    const params = this.getUserQueryParams3();
    queryParams = params;

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }

  // -------------- Search by Sub Team Title & Load Childs -----------------

  searchBySubTeam(val) {

    $("#empgrpSearch").val("");
    this.allOrgMstSubTeamTitle = val;
  }

  searchByPayrollLocation(val) {

    $("#empgrpSearch").val("");
    this.payrollLocationId = val;
    const params = this.getUserQueryParams3();

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";
    this.employeeService.sendGetRequest(apiURL, params).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));

      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );
  }

  searchByPayrollSubLocation(val) {

    $("#empgrpSearch").val("");
    this.payrollSubLocationId = val;

    let apiURL = this.baseUrl + "/allOrgMst/searchByTitle";

    const params = this.getUserQueryParams3();

    this.employeeService.sendGetRequest(apiURL, params).subscribe(
      (response: any) => {
        this.payrollLocation = response?.prlLoc?.sort((a, b) => a?.localeCompare(b));
        this.payrollSubLocation2 = response?.prlSubLoc?.sort((a, b) => a?.localeCompare(b));
        this.operating_units = response?.ou?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.products = response?.product?.sort((a, b) => a?.localeCompare(b));
        this.departments = response?.dept?.sort((a, b) => a?.localeCompare(b));
        this.sections = response?.section?.sort((a, b) => a?.localeCompare(b));
        this.subSections = response?.sub_section?.sort((a, b) => a?.localeCompare(b));
        this.teams = response?.team?.sort((a, b) => a?.localeCompare(b));
        this.subTeams = response?.subTeam?.sort((a, b) => a?.localeCompare(b));
      },
      (error) => {
        this.toastr.warning(error.message);
      }
    );

  }


  // --------------------------- Get Search Organization Params ----------------------

  getOrgParams() {


    let queryParams: any = {};
    const params = this.getUserQueryParams();
    queryParams = params;

    this.allSearchParams = queryParams;

    this.close();
  }


  getUserQueryParams(): any {
    let params: any = {};

    if (this.allOrgMstDept) {
      params[`allOrgMstDept`] = this.allOrgMstDept;
    }
    if (this.allOrgMstSections) {
      params[`allOrgMstSections`] = this.allOrgMstSections;
    }
    if (this.allOrgMstSubSections) {
      params[`allOrgMstSubSections`] = this.allOrgMstSubSections;
    }
    if (this.allOrgMstTeamTitle) {
      params[`allOrgMstTeam`] = this.allOrgMstTeamTitle;
    }
    if (this.allOrgMstProduct) {
      params[`allOrgMstProduct`] = this.allOrgMstProduct;
    }
    if (this.payrollLocationId) {
      params[`payrollLocationTitle`] = this.payrollLocationId;
    }

    if (this.payrollSubLocationId) {
      params[`payrollSubLocationTitle`] = this.payrollSubLocationId;
    }
    if (this.allOrgMstSubTeamTitle) {
      params[`allOrgMstSubTeam`] = this.allOrgMstSubTeamTitle;
    }
    if (this.allOrgMstOpunit) {
      params[`allOrgMstOpunit`] = this.allOrgMstOpunit;
    }

    if (this.statusTitle) {
      params[`statusTitle`] = this.statusTitle;
    }


    return params;
  }

  getUserQueryParams3(): any {
    let params: any = {};
    if (this.allOrgMstDept) {
      params[`allOrgMstDept`] = this.allOrgMstDept;
    }
    if (this.allOrgMstSections) {
      params[`allOrgMstSections`] = this.allOrgMstSections;
    }
    if (this.allOrgMstSubSections) {
      params[`allOrgMstSubSections`] = this.allOrgMstSubSections;
    }
    if (this.allOrgMstTeamTitle) {
      params[`allOrgMstTeam`] = this.allOrgMstTeamTitle;
    }
    if (this.allOrgMstProduct) {
      params[`allOrgMstProduct`] = this.allOrgMstProduct;
    }
    if (this.payrollLocationId) {
      params[`payrollLocationTitle`] = this.payrollLocationId;
    }

    if (this.payrollSubLocationId) {
      params[`payrollSubLocationTitle`] = this.payrollSubLocationId;
    }
    if (this.allOrgMstSubTeamTitle) {
      params[`allOrgMstSubTeam`] = this.allOrgMstSubTeamTitle;
    }
    if (this.allOrgMstOpunit) {
      params[`allOrgMstOpunit`] = this.allOrgMstOpunit;
    }

    // orgModalRoute
    if (this.currentRoute) {
      params[`openUrl`] = this.currentRoute.url;
    }
    return params;
  }

  resetSearch() {

    this.allOrgMstOpunit = "";
    this.operating_units = [];
    this.products = [];
    this.allOrgMstProduct = "";
    this.departments = [];
    this.allOrgMstDept = "";
    this.sections = [];
    this.allOrgMstSections = "";
    this.subSections = [];
    this.allOrgMstSubSections = "";
    this.teams = [];
    this.allOrgMstTeamTitle = "";
    this.subTeams = [];
    this.allOrgMstSubTeamTitle = "";
    this.payrollLocation = [];
    this.payrollLocationId = "";
    this.payrollSubLocation = [];
    this.payrollSubLocationId = "";
    this.statusTitle = null;
    this.loadAllOperatingUnits();
    this.loadAllProducts();
    this.loadAllDepartments();
    this.loadAllSections();
    this.loadAllSubSections();
    this.loadAllTeams();
    this.loadAllSubTeams();
    this.loadAllPayrollLocation();
    this.loadPayloadSubLocations();

  }


}
