import { ComponentFixture, TestBed } from '@angular/core/testing';

import { OrgSearchModalParamBasedComponent } from './org-search-modal-param-based.component';

describe('OrgSearchModalParamBasedComponent', () => {
  let component: OrgSearchModalParamBasedComponent;
  let fixture: ComponentFixture<OrgSearchModalParamBasedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ OrgSearchModalParamBasedComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OrgSearchModalParamBasedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
