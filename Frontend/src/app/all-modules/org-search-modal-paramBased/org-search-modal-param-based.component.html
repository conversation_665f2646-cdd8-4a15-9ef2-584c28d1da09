<div class="modal custom-modal" id="organizationSearchModal" role="dialog">

  <div class="modal-content">
    <h3 class="text-center"><span style="color:#25B6B2;">Select Organization</span></h3>


    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="close()">
      <span aria-hidden="true">&times;</span>
    </button>

    <form (ngSubmit)="onSubmit()">
      <div class="row mt-3">




        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Payroll Location</label>
            <ng-select [items]="payrollLocation" placeholder=":: ALL ::" bindLabel="title" bindValue="title"
              [searchable]="true" (change)="searchByPayrollLocation($event)"
              [ngModel]="payrollLocationId? payrollLocationId : null" name="payrollLocationId">

            </ng-select>

          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{payrollLocation?.length}}
          </div>

        </div>


        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Job Location</label>

            <ng-select [items]="payrollSubLocation2" placeholder=":: ALL ::" bindLabel="data" bindValue="data"
              [searchable]="true" (change)="searchByPayrollSubLocation($event)"
              [ngModel]="payrollSubLocationId? payrollSubLocationId : null" name="payrollSubLocationId">

            </ng-select>

          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{payrollSubLocation2?.length}}
          </div>

        </div>

        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Legal Entity</label>

            <ng-select [items]="operating_units" placeholder=":: ALL ::" bindLabel="data" bindValue="data"
              [searchable]="true" (change)="searchByOperatingUnit($event)"
              [ngModel]="allOrgMstOpunit ? allOrgMstOpunit : null" name="allOrgMstOpunit">

            </ng-select>

          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{operating_units?.length}}
          </div>

        </div>


        <div class="col-md-4 d-flex">

          <div class="form-group" style="width: 100%;">
            <label>Product</label>
            <ng-select [items]="products" placeholder=":: ALL ::" bindLabel="data" bindValue="data" [searchable]="true"
              (change)="searchByProduct($event)" [ngModel]="allOrgMstProduct? allOrgMstProduct : null"
              name="allOrgMstProduct">

            </ng-select>
          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{products?.length}}
          </div>

        </div>


        <div class="col-md-4 d-flex">

          <div class="form-group" style="width: 100%;">
            <label>Department</label>
            <ng-select [items]="departments" placeholder=":: ALL ::" bindLabel="data" bindValue="data"
              [searchable]="true" (change)="searchByDepartment($event)" [ngModel]="allOrgMstDept? allOrgMstDept : null"
              name="allOrgMstDept">

            </ng-select>

          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{departments?.length}}
          </div>

        </div>


        <div class="col-md-4 d-flex">

          <div class="form-group" style="width: 100%;">
            <label>Section</label>
            <ng-select [items]="sections" placeholder=":: ALL ::" bindLabel="data" bindValue="data" [searchable]="true"
              (change)="searchBySection($event)" [ngModel]="allOrgMstSections? allOrgMstSections : null"
              name="allOrgMstSections">

            </ng-select>
          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{sections?.length}}
          </div>

        </div>


        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Sub Section</label>
            <ng-select [items]="subSections" placeholder=":: ALL ::" bindLabel="data" bindValue="data"
              [searchable]="true" (change)="searchBySubSection($event)"
              [ngModel]="allOrgMstSubSections? allOrgMstSubSections : null" name="allOrgMstSubSections">

            </ng-select>

          </div>
          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{subSections?.length}}
          </div>

        </div>


        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Team</label>
            <ng-select [items]="teams" placeholder=":: ALL ::" bindLabel="data" bindValue="data" [searchable]="true"
              (change)="searchByTeam($event)" [ngModel]="allOrgMstTeamTitle? allOrgMstTeamTitle : null"
              name="allOrgMstTeamTitle">

            </ng-select>
          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{teams?.length}}
          </div>


        </div>


        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Sub Team</label>
            <ng-select [items]="subTeams" placeholder=":: ALL ::" bindLabel="data" bindValue="data" [searchable]="true"
              (change)="searchBySubTeam($event)" [ngModel]="allOrgMstSubTeamTitle? allOrgMstSubTeamTitle : null"
              name="allOrgMstSubTeamTitle">

            </ng-select>


          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{subTeams?.length}}
          </div>


        </div>

        <div class="col-md-4 d-flex">
          <div class="form-group" style="width: 100%;">
            <label>Employee Status</label>
            <ng-select [items]="status" placeholder=":: ALL ::" bindLabel="title" bindValue="title" [searchable]="true"
              [ngModel]="statusTitle? statusTitle : null" name="statusTitle"
              (change)="searchBystatusTitle($event.title)">

            </ng-select>

          </div>

          <div
            style="background-color: #EFDFFC; height:36px; width:30px;text-align:center; padding-top:10px; margin-top:27px;">
            {{status?.length}}
          </div>

        </div>

        <div class="col-md-4 mt-4">

          <button type="button" id="reset" class="btn btn-secondary btn-ripple" (click)="resetSearch()">
            <i class="fa fa-undo" aria-hidden="true"></i> Reset
          </button>
          &nbsp; &nbsp; &nbsp;
          <button type="submit" id="submit" class="btn btn-success btn-ripple">
            <i class="fa fa-search" aria-hidden="true"></i> Search &nbsp;&nbsp;&nbsp;
          </button>

        </div>

      </div>

    </form>

  </div>
</div>