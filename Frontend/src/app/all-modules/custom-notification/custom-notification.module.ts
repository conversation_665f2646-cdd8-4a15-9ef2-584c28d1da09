import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CustomNotificationRoutingModule } from './custom-notification-routing.module';
import { CustomNotificationComponent } from './custom-notification.component';
import { ManpowerRqsnMonitoringComponent } from './component/manpower-rqsn-monitoring/list/manpower-rqsn-monitoring.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ProbationaryEmployeeComponent } from './component/probationary-employee/probationary-employee.component';
import { PunishmentMonitoringComponent } from './component/punishment/punishment-monitoring.component';


@NgModule({
  declarations: [
    CustomNotificationComponent,
    ManpowerRqsnMonitoringComponent,
    ProbationaryEmployeeComponent,
    PunishmentMonitoringComponent
  ],
  imports: [
    CommonModule,
    CustomNotificationRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxSpinnerModule,
    NgxPaginationModule,
    NgSelectModule,
  ]
})
export class CustomNotificationModule { }
