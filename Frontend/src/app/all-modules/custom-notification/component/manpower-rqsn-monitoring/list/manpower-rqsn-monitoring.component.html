<!-- Page Content -->
<div class="content container-fluid">

    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Manpower Monitoring</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Manpower Requisition
                                Monitoring</b></span></li>
                </ul>
            </div>
        </div>
        <!-- /Page Header -->

        <div class="row mt-3">

            <div class="col-md-6 col-sm-6 col-lg-6 col-xl-3">
                <div class="card dash-widget customCard">
                    <div class="card-body">
                        <span class="dash-widget-icon"><img src="assets/img/mgt-dashboard/newEmp.png"
                            style="width: 50px;height: 50px;"></span>
                        <div class="dash-widget-info">
                            <h3>{{vacancyCount.totalrequisition?vacancyCount.totalrequisition:0}}</h3>
                            <span>Total Requisition</span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-6 col-sm-6 col-lg-6 col-xl-3">
                <div class="card dash-widget customCard">
                    <div class="card-body">
                        <span class="dash-widget-icon"><img src="assets/img/mgt-dashboard/loginEmp.png"
                            style="width: 50px;height: 50px;"></span>
                        <div class="dash-widget-info">
                            <h3>{{vacancyCount.requiredmanpower?vacancyCount.requiredmanpower:0}}</h3>
                            <span>Manpower Required</span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-6 col-sm-6 col-lg-6 col-xl-3">
                <div class="card dash-widget customCard">
                    <div class="card-body">
                        <span class="dash-widget-icon"><img src="assets/img/mgt-dashboard/activeEmp.png"
                                style="width: 50px;height: 50px;"></span>
                        <div class="dash-widget-info">
                            <h3>{{vacancyCount.joinedempcount?vacancyCount.joinedempcount:0}}</h3>
                            <span>Total Joined</span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-md-6 col-sm-6 col-lg-6 col-xl-3">
                <div class="card dash-widget customCard">
                    <div class="card-body">
                        <span class="dash-widget-icon"><img src="assets/img/mgt-dashboard/probEmp.png"
                            style="width: 50px;height: 50px;"></span>
                        <div class="dash-widget-info">
                            <h3>{{vacancyCount.remainingmp?vacancyCount.remainingmp:0}}</h3>
                            <span>Remaining</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>


        <!-- Search Filter -->

        <div class="card mb-2" style="background-color: transparent">
            <div class="card-body p-3">
                <form [formGroup]="myForm">
                    <div class="row">

                        <div class="col-md-2">

                            <div class="form-group ">

                                <label>Job Location</label>

                                <select class="select form-control" formControlName="jobLocationId">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor="let data of locations" [value]='data.id'>{{data.title}}
                                </select>
                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group ">
                                <label>Operating Unit</label>
                                <select class="select form-control" formControlName="operatingUnitId"
                                    (change)="searchByOperatingUnit($event.target.value)">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor=" let data of operating_units" [value]='data.id'>{{data.title}}
                                </select>

                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group">
                                <label>Product</label>
                                <select class="select form-control" formControlName="productId"
                                    (change)="searchByProduct($event.target.value)">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor="let data of products" [value]='data.id'>{{data.title}}
                                </select>

                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group">
                                <label>Department</label>
                                <select id="locationT" class="select form-control" formControlName="departmentId">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor="let data of departments" [value]='data.id'>{{data.title}}
                                </select>

                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group">
                                <label>Approval Status</label>
                                <select id="locationT" class="select form-control" formControlName="approvalStatus">
                                    <option value="">:: ALL ::</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Recommended">Recommended</option>
                                    <option value="Rejected">Rejected</option>
                                    <option value="Submitted">Submitted</option>

                                </select>

                            </div>

                        </div>

                        <div class="col-md-2 mt-4">
                            <button class="btn btn-success btn-ripple" type="submit" (click)="searchByButton()">
                                <i class="fa fa-search" aria-hidden="true"></i> Search
                            </button>
                            &nbsp;

                            <button class="btn btn-danger btn-ripple" (click)="resetform()">
                                <i class="fa fa-eraser" aria-hidden="true"></i> Clear
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- /Search Filter -->

        <!-- /Page Content -->
        <div class="row mt-3">
            <div class="col-md-12">

                <div class="card customCard">

                    <div class="card-body">

                        <div class="">

                            <div class="d-flex justify-content-start pb-1">
                                <div class="pgn-displayDataInfo">
                                    <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                                        configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                                        {{configPgn.totalItem}} ) entries</span>
                                </div>
                            </div>

                            <table id="genListTable" class="table table-striped custom-table">
                                <thead>
                                    <tr>
                                        <th>SL</th>
                                        <th>Ref Code</th>
                                        <th>Title</th>
                                        <th>Job Location</th>
                                        <th class="text-center">Total Vacancy</th>
                                        <th class="text-center">Joined</th>
                                        <th class="text-center">Remaining</th>
                                        <th>Position</th>
                                        <th>Required Within</th>
                                        <th>Circular Date</th>
                                        <th>Requested By</th>

                                        <th>Approval Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let thisObj of listData | paginate : configPgn; let i = index"
                                        [class.active]="i == currentIndex">

                                        <td>{{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) ) }}</td>
                                        <td>{{thisObj?.code}}</td>
                                        <td>{{thisObj.vacancytitle}} </td>
                                        <td>{{thisObj.joblocation}}</td>
                                        <td class="text-center"><b class="text-primary">{{thisObj.requiredmanpower}}</b>
                                        </td>
                                        <td class="text-center"><b class="text-success">{{thisObj.joinedempcount}}</b>
                                        </td>
                                        <td class="text-center"><b class="text-warning">{{thisObj.remainingmp}}</b></td>

                                        <td style="max-width:350px;">
                                            {{thisObj?.positiontitle}}</td>
                                        <td>{{thisObj.requiredwithin|date}}</td>
                                        <td>{{thisObj.crclrdate|date}}</td>
                                        <td>{{thisObj.requestbylogincode}}-{{thisObj.requestbyname}}</td>

                                        <td>
                                            <span
                                                *ngIf="thisObj?.approvalstatus === 'Submitted' || thisObj?.approvalstatus === 'SUBMITTED'">
                                                <span class="badge badge-info"> <b> {{thisObj?.approvalstatus}}
                                                    </b></span>
                                            </span>
                                            <span
                                                *ngIf="thisObj?.approvalstatus === 'Recommended' || thisObj?.approvalstatus === 'RECOMMENDED'">
                                                <span class="badge badge-warning">
                                                    <b> {{thisObj?.approvalstatus}} </b></span>
                                            </span>
                                            <span
                                                *ngIf="thisObj?.approvalstatus === 'Rejected' || thisObj?.approvalstatus === 'REJECTED'">
                                                <span class="badge badge-danger">
                                                    <b> {{thisObj?.approvalstatus}} </b></span>
                                            </span>
                                            <span
                                                *ngIf="thisObj?.approvalstatus === 'Approved' || thisObj?.approvalstatus === 'APPROVED'">
                                                <span class="badge badge-success">
                                                    <b> {{thisObj?.approvalstatus}} </b></span>
                                            </span>
                                        </td>

                                        <td style="min-width:150px;">
                                            <a class="btn btn-sm btn-primary"
                                                routerLink="/irecruitment/vacancy/publish/view/{{thisObj.vacancyid}}"
                                                target="_blank"><i class="fa fa-eye" title="View"></i></a>
                                            &nbsp;

                                        </td>

                                    </tr>

                                    <tr *ngIf="listData.length === 0">
                                        <td colspan="13">
                                            <h5 style="text-align: center;">No data found</h5>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>


                            <div class="d-flex justify-content-end ">

                                <div class="">
                                    Items per Page
                                    <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption">
                                        <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                                            {{ size }}
                                        </option>
                                    </select>
                                </div>

                                <div class="pgn-pageSliceCt">
                                    <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                                        (pageChange)="handlePageChange($event)">
                                    </pagination-controls>
                                </div>

                            </div>

                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
    <!-- /Page Content -->




    <ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
    </ngx-spinner>