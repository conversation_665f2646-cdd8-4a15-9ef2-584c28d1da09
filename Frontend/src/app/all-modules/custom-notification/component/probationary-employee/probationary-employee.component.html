<!-- Page Content -->
<div class="content container-fluid">

    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a routerLink="/dashboard">Home</a></li>
                    <li class="breadcrumb-item active">Manpower Monitoring</li>
                    <li class="breadcrumb-item active"><span style="color:#25B6B2;"><b>Probationary Employee</b></span>
                    </li>
                </ul>
            </div>
        </div>
        <!-- /Page Header -->

        <div class="row mt-3">


            <div class="col-md-6 col-sm-6 col-lg-6 col-xl-3">
                <div class="card dash-widget customCard">
                    <div class="card-body">
                        <span class="dash-widget-icon"><img src="assets/img/mgt-dashboard/probEmp.png"
                                style="width: 50px;height: 50px;"></span>
                        <div class="dash-widget-info">
                            <h3> {{configPgn.totalItem}}</h3>
                            <span>Total Probationary Employee</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>


        <!-- Search Filter -->

        <div class="card mb-2" style="background-color: transparent">
            <div class="card-body p-3">
                <form [formGroup]="myForm">
                    <div class="row">

                        <div class="col-md-2">

                            <div class="form-group ">

                                <label>Job Location</label>

                                <select class="select form-control" formControlName="jobLocationId">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor="let data of locations" [value]='data.id'>{{data.title}}
                                </select>
                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group ">
                                <label>Operating Unit</label>
                                <select class="select form-control" formControlName="operatingUnitId"
                                    (change)="searchByOperatingUnit($event.target.value)">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor=" let data of operating_units" [value]='data.id'>{{data.title}}
                                </select>

                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group">
                                <label>Product</label>
                                <select class="select form-control" formControlName="productId"
                                    (change)="searchByProduct($event.target.value)">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor="let data of products" [value]='data.id'>{{data.title}}
                                </select>

                            </div>

                        </div>

                        <div class="col-sm-2">

                            <div class="form-group">
                                <label>Department</label>
                                <select id="locationT" class="select form-control" formControlName="departmentId">
                                    <option value="">:: ALL ::</option>
                                    <option *ngFor="let data of departments" [value]='data.id'>{{data.title}}
                                </select>

                            </div>

                        </div>

                        <div class="col-md-2 mt-4">
                            <button class="btn btn-success btn-ripple" type="submit" (click)="searchByButton()">
                                <i class="fa fa-search" aria-hidden="true"></i> Search
                            </button>
                            &nbsp;

                            <button class="btn btn-danger btn-ripple" (click)="resetform()">
                                <i class="fa fa-eraser" aria-hidden="true"></i> Clear
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- /Search Filter -->

        <!-- /Page Content -->
        <div class="row mt-3">
            <div class="col-md-12">

                <div class="card customCard">

                    <div class="card-body">

                        <div class="">

                            <div class="d-flex justify-content-start pb-1">
                                <div class="pgn-displayDataInfo">
                                    <span class="page-item disabled">Displaying ( {{ ( ((configPgn.pageNum-1) *
                                        configPgn.pageSize) + (1) ) }} to {{configPgn.pngDiplayLastSeq}} of
                                        {{configPgn.totalItem}} ) entries</span>
                                </div>
                            </div>

                            <table id="genListTable" class="table table-striped custom-table">
                                <thead>
                                    <tr>
                                        <th>SL</th>
                                        <th>Employee</th>
                                        <th>Designation</th>
                                        <th>Joining Date</th>
                                        <th>Job Location</th>
                                        <th>Operating Unit</th>
                                        <th>Product</th>
                                        <th>Department</th>
                                        <th>Section</th>
                                        <th>Employee Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let thisObj of listData | paginate : configPgn; let i = index"
                                        [class.active]="i == currentIndex">

                                        <td>{{ ( ((configPgn.pageNum-1) * configPgn.pageSize) + (i+1) ) }}</td>
                                        <td>{{thisObj?.logincode}} - {{thisObj?.displayname}}</td>
                                        <td>{{thisObj.designation}} </td>
                                        <td>{{thisObj.joining_date | date}}</td>

                                        <td>{{thisObj.locationtitle}} </td>
                                        <td>{{thisObj.operatingunittitle}} </td>

                                        <td> {{thisObj?.producttitle}}</td>
                                        <td>{{thisObj.departmenttitle}}</td>
                                        <td>{{thisObj.sectiontitle}}</td>
                                        <td>{{thisObj.employeestatus}}</td>


                                    </tr>

                                    <tr *ngIf="listData.length === 0">
                                        <td colspan="13">
                                            <h5 style="text-align: center;">No data found</h5>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>


                            <div class="d-flex justify-content-end ">

                                <div class="">
                                    Items per Page
                                    <select (change)="handlePageSizeChange($event)" class="pgn-pageSizeOption">
                                        <option *ngFor="let size of configPgn.pageSizes" [value]="size">
                                            {{ size }}
                                        </option>
                                    </select>
                                </div>

                                <div class="pgn-pageSliceCt">
                                    <pagination-controls responsive="true" previousLabel="Prev" nextLabel="Next"
                                        (pageChange)="handlePageChange($event)">
                                    </pagination-controls>
                                </div>

                            </div>

                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
    <!-- /Page Content -->




    <ngx-spinner bdColor="rgba(255,255,255,0.5)" size="small" template="<img src='assets/img/loader.gif' />">
    </ngx-spinner>