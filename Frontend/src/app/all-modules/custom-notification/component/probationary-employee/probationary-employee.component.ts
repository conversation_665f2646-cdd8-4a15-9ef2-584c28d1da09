import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { EmployeeService } from 'src/app/all-modules/employees/services/employee.service';
import { OnTourService } from 'src/app/all-modules/self-service/service/on-tour.service';
import { LoginService } from 'src/app/login/services/login.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-probationary-employee',
  templateUrl: './probationary-employee.component.html',
  styleUrls: ['./probationary-employee.component.css']
})
export class ProbationaryEmployeeComponent implements OnInit {


  // cores
  public baseUrl = environment.baseUrl;

  public pipe = new DatePipe("en-US");
  public myFromGroup: FormGroup;
  myForm: FormGroup;
  public editId: any;
  public tempId: any;
  // list
  public listData: any = [];
  public locations: any = [];
  public operating_units: any = [];
  public products: any = [];
  public departments: any = [];

  public vacancyCount: any = [];
  public configPgn: any;


  constructor(
    private onTourService: OnTourService,
    private login: LoginService,
    private spinnerService: NgxSpinnerService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private employeeService: EmployeeService,
  ) {
    this.configPgn = {
      pageNum: 1,
      pageSize: 10,
      totalItem: 50,
      pageSizes: [10, 25, 50, 100, 200, 500, 1000],
      pgnDiplayLastSeq: 10,
      itemsPerPage: 10,
      currentPage: 1,
      totalItems: 50
    };
  }

  ngOnInit(): void {

    this.myForm = this.formBuilder.group({
      operatingUnitId: [""],
      productId: [""],
      jobLocationId: [""],
      departmentId: [""],
      approvalStatus: [""],
    });

    this.getList();
    this.loadAllLocations();
    this.loadAllOperatingUnits();
  }

  searchByButton() {
    this.handlePageChange(1);
  }

  resetform() {
    this.myForm.reset();
    this.products = [];
    this.departments = [];
    this.handlePageChange(1);
  }

  // ------------ JOB LOCATION ---------------

  loadAllLocations() {

    let orgType = "JOB_LOCATION";
    let apiURL = this.baseUrl + "/alkp/search/" + orgType;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        this.locations = response.subALKP;

      },
      (error) => {
        console.log(error);

      }
    );
  }

  // ---------------- ALL OPERATING UNIT -----------------

  loadAllOperatingUnits() {
    let orgType = "OPERATING_UNIT";
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType;
    let queryParams: any = {};


    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.operating_units = response;

      },
      (error) => {
        console.log(error);
      }
    );
  }

  // ------------ LOAD PRODUCT by OPERATING UNIT -----------

  searchByOperatingUnit(id) {

    let orgType = "PRODUCT";
    let operatingUnitIdForAPI = id;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + operatingUnitIdForAPI;
    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.products = response;

      },
      (error) => {
        console.log(error);
      }
    );
  }

  // --------------- LOAD Department by Product --------------------

  searchByProduct(val) {

    let orgType = "DEPARTMENT";
    let productIdForAPI = val;
    let apiURL = this.baseUrl + "/allOrgMst/search/" + orgType + "/" + productIdForAPI;

    let queryParams: any = {};

    this.employeeService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {

        this.departments = response;
      },
      (error) => {
        console.log(error);
      }
    );


  }


  // ------------------ Get All List -----------------------

  getList() {

    let apiURL = this.baseUrl + "/hrCrEmpAssgnmnt/LocationAndOrgWiseProvisionalEmployeeList";

    let queryParams: any = {};
    const params = this.getUserQueryParams(this.configPgn.pageNum, this.configPgn.pageSize);
    queryParams = params;

    this.spinnerService.show();

    this.onTourService.sendGetRequest(apiURL, queryParams).subscribe(
      (response: any) => {
        this.listData = response.objectList;
        this.configPgn.totalItem = response.totalItems;
        this.configPgn.totalItems = response.totalItems;
        this.setDisplayLastSequence();

        this.spinnerService.hide();
      },
      (error) => {
        this.toastr.warning(error?.error?.message || "Something went wrong. Please try again.");
        this.spinnerService.hide();
      }
    );
  }

  // ----------------------- Query Param ---------------------

  private getUserQueryParams(page: number, pageSize: number): any {

    let params: any = {};

    if (page) {
      params[`pageNum`] = page - 0;
    }
    if (pageSize) {
      params[`pageSize`] = pageSize;
    }

    if (
      this.myForm.get("jobLocationId").value
    ) {
      params.jobLocationId = this.myForm.get("jobLocationId").value;
    }

    if (
      this.myForm.get("operatingUnitId").value
    ) {

      let selectedOrg = this.operating_units.find(unit => unit.id === Number(this.myForm.get("operatingUnitId").value));
      params.allOrgMstOpunit = selectedOrg?.title;
    }

    if (
      this.myForm.get("productId").value
    ) {

      let selectedOrg = this.products.find(unit => unit.id === Number(this.myForm.get("productId").value));
      params.allOrgMstProduct = selectedOrg?.title;
    }


    if (
      this.myForm.get("departmentId").value
    ) {

      let selectedOrg = this.departments.find(unit => unit.id === Number(this.myForm.get("departmentId").value));
      params.allOrgMstDept = selectedOrg?.title;
    }

    if (
      this.myForm.get("approvalStatus").value
    ) {
      params.approvalStatus = this.myForm.get("approvalStatus").value;
    }

    return params;

  }



  redirectToImage(fileName) {

    window.open(this.baseUrl + fileName, '_blank');
  }


  // ---------------------------------------- pagination handling methods start -------------------------------

  setDisplayLastSequence() {
    this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    if (this.listData.length < this.configPgn.pageSize) {
      this.configPgn.pngDiplayLastSeq = (((this.configPgn.pageNum - 1) * this.configPgn.pageSize) + this.configPgn.pageSize);
    }
    if (this.configPgn.totalItem < this.configPgn.pngDiplayLastSeq) {
      this.configPgn.pngDiplayLastSeq = this.configPgn.totalItem;
    }
  }

  handlePageChange(event: number) {

    this.configPgn.pageNum = event;
    // set for ngx
    this.configPgn.currentPage = this.configPgn.pageNum;
    this.getList();
  }

  handlePageSizeChange(event: any): void {

    this.configPgn.pageSize = event.target.value;
    this.configPgn.pageNum = 1;
    // set for ngx
    this.configPgn.itemsPerPage = this.configPgn.pageSize;
    this.getList();
  }
  // ---------------------------------------- pagination handling methods end ---------------------------------
}
