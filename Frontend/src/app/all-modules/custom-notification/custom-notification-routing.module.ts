import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CustomNotificationComponent } from './custom-notification.component';
import { ManpowerRqsnMonitoringComponent } from './component/manpower-rqsn-monitoring/list/manpower-rqsn-monitoring.component';
import { ProbationaryEmployeeComponent } from './component/probationary-employee/probationary-employee.component';
import { PunishmentMonitoringComponent } from './component/punishment/punishment-monitoring.component';

const routes: Routes = [
  {
    path:'',
    component:CustomNotificationComponent,
    children:[
      {
        path:'manpower-requisition-monitoring',
        component:ManpowerRqsnMonitoringComponent,
      },
      {
        path:'probationary-employee',
        component:ProbationaryEmployeeComponent,
      },
      {
        path:'punishment-list',
        component:PunishmentMonitoringComponent,
      },
     
    ]
  }
];


@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CustomNotificationRoutingModule { }
