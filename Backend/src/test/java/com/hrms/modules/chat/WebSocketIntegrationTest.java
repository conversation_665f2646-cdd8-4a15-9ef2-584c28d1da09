package com.hrms.modules.chat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hrms.modules.chat.dto.ChatMessageDTO;
import com.hrms.modules.chat.dto.UserStatusDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.simp.stomp.*;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.messaging.WebSocketStompClient;

import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class WebSocketIntegrationTest {

    @LocalServerPort
    private int port;

    private WebSocketStompClient stompClient;
    private StompSession stompSession;
    private final BlockingQueue<ChatMessageDTO> messageQueue = new LinkedBlockingQueue<>();
    private final BlockingQueue<UserStatusDTO> statusQueue = new LinkedBlockingQueue<>();

    @BeforeEach
    void setUp() throws Exception {
        stompClient = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());
        
        String url = "ws://localhost:" + port + "/ws";
        stompSession = stompClient.connect(url, new StompSessionHandlerAdapter() {
            @Override
            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
                System.out.println("Connected to WebSocket");
            }

            @Override
            public void handleException(StompSession session, StompCommand command, 
                                      StompHeaders headers, byte[] payload, Throwable exception) {
                exception.printStackTrace();
            }
        }).get(5, TimeUnit.SECONDS);
    }

    @Test
    void testWebSocketConnection() {
        assertTrue(stompSession.isConnected());
    }

    @Test
    void testSendPrivateMessage() throws Exception {
        // Subscribe to private messages
        stompSession.subscribe("/user/queue/messages", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return ChatMessageDTO.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                messageQueue.offer((ChatMessageDTO) payload);
            }
        });

        // Send a test message
        ChatMessageDTO testMessage = new ChatMessageDTO();
        testMessage.setContent("Test WebSocket message");
        testMessage.setSenderUsername("testuser");
        testMessage.setReceiverUsername("receiver");
        testMessage.setSentAt(LocalDateTime.now());

        stompSession.send("/app/chat.sendMessage", testMessage);

        // Wait for message to be received
        ChatMessageDTO receivedMessage = messageQueue.poll(5, TimeUnit.SECONDS);
        assertNotNull(receivedMessage);
        assertEquals("Test WebSocket message", receivedMessage.getContent());
    }

    @Test
    void testGroupMessage() throws Exception {
        // Subscribe to group messages
        stompSession.subscribe("/topic/group", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return ChatMessageDTO.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                messageQueue.offer((ChatMessageDTO) payload);
            }
        });

        // Send a group message
        ChatMessageDTO groupMessage = new ChatMessageDTO();
        groupMessage.setContent("Test group message");
        groupMessage.setSenderUsername("testuser");

        stompSession.send("/app/chat.sendGroupMessage", groupMessage);

        // Wait for message to be received
        ChatMessageDTO receivedMessage = messageQueue.poll(5, TimeUnit.SECONDS);
        assertNotNull(receivedMessage);
        assertEquals("Test group message", receivedMessage.getContent());
    }

    @Test
    void testUserStatusUpdate() throws Exception {
        // Subscribe to user status updates
        stompSession.subscribe("/topic/user-status", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return UserStatusDTO.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                statusQueue.offer((UserStatusDTO) payload);
            }
        });

        // Send status update
        UserStatusDTO statusUpdate = new UserStatusDTO();
        statusUpdate.setUsername("testuser");
        statusUpdate.setStatus(UserStatusDTO.Status.AWAY);

        stompSession.send("/app/user.updateStatus", statusUpdate);

        // Wait for status update to be received
        UserStatusDTO receivedStatus = statusQueue.poll(5, TimeUnit.SECONDS);
        assertNotNull(receivedStatus);
        assertEquals(UserStatusDTO.Status.AWAY, receivedStatus.getStatus());
    }

    @Test
    void testTypingIndicator() throws Exception {
        // Subscribe to typing indicators
        stompSession.subscribe("/user/queue/typing", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                // Handle typing indicator
                System.out.println("Typing indicator received: " + payload);
            }
        });

        // Send typing indicator
        stompSession.send("/app/chat.typing", "testuser is typing...");

        // Give some time for the message to be processed
        Thread.sleep(1000);
    }

    @Test
    void testNotificationDelivery() throws Exception {
        // Subscribe to notifications
        stompSession.subscribe("/user/queue/notifications/1", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                System.out.println("Notification received: " + payload);
            }
        });

        // Send notification
        stompSession.send("/app/notification.send", "Test notification");

        // Give some time for the notification to be processed
        Thread.sleep(1000);
    }

    @Test
    void testSystemAnnouncement() throws Exception {
        // Subscribe to system announcements
        stompSession.subscribe("/topic/announcements", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                System.out.println("System announcement received: " + payload);
            }
        });

        // Send system announcement
        stompSession.send("/app/system.announce", "System maintenance scheduled");

        // Give some time for the announcement to be processed
        Thread.sleep(1000);
    }

    @Test
    void testMultipleConnections() throws Exception {
        // Create second connection
        WebSocketStompClient stompClient2 = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient2.setMessageConverter(new MappingJackson2MessageConverter());
        
        String url = "ws://localhost:" + port + "/ws";
        StompSession stompSession2 = stompClient2.connect(url, new StompSessionHandlerAdapter()).get(5, TimeUnit.SECONDS);

        assertTrue(stompSession.isConnected());
        assertTrue(stompSession2.isConnected());

        // Test communication between sessions
        BlockingQueue<ChatMessageDTO> session2Queue = new LinkedBlockingQueue<>();
        
        stompSession2.subscribe("/user/queue/messages", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return ChatMessageDTO.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                session2Queue.offer((ChatMessageDTO) payload);
            }
        });

        // Send message from session 1
        ChatMessageDTO testMessage = new ChatMessageDTO();
        testMessage.setContent("Message from session 1");
        testMessage.setSenderUsername("user1");
        testMessage.setReceiverUsername("user2");

        stompSession.send("/app/chat.sendMessage", testMessage);

        // Verify message received in session 2
        ChatMessageDTO receivedMessage = session2Queue.poll(5, TimeUnit.SECONDS);
        assertNotNull(receivedMessage);
        assertEquals("Message from session 1", receivedMessage.getContent());

        stompSession2.disconnect();
    }

    @Test
    void testConnectionResilience() throws Exception {
        assertTrue(stompSession.isConnected());

        // Simulate network interruption by disconnecting and reconnecting
        stompSession.disconnect();
        assertFalse(stompSession.isConnected());

        // Reconnect
        String url = "ws://localhost:" + port + "/ws";
        stompSession = stompClient.connect(url, new StompSessionHandlerAdapter()).get(5, TimeUnit.SECONDS);
        
        assertTrue(stompSession.isConnected());
    }

    @Test
    void testMessagePersistence() throws Exception {
        // Send a message
        ChatMessageDTO testMessage = new ChatMessageDTO();
        testMessage.setContent("Persistent test message");
        testMessage.setSenderUsername("testuser");
        testMessage.setReceiverUsername("receiver");

        stompSession.send("/app/chat.sendMessage", testMessage);

        // Give time for message to be saved
        Thread.sleep(1000);

        // Verify message was persisted (this would require database verification in real test)
        assertTrue(true); // Placeholder for actual persistence check
    }
}
