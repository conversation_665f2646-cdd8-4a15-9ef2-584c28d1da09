package com.hrms.modules.chat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hrms.modules.chat.dto.ChatMessageDTO;
import com.hrms.modules.chat.dto.NotificationDTO;
import com.hrms.modules.chat.dto.UserStatusDTO;
import com.hrms.modules.chat.service.ChatService;
import com.hrms.modules.chat.service.RealTimeNotificationService;
import com.hrms.modules.chat.service.UserSessionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.*;

@SpringBootTest
@AutoConfigureTestMvc
public class ChatApiIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ChatService chatService;

    @MockBean
    private UserSessionService userSessionService;

    @MockBean
    private RealTimeNotificationService notificationService;

    private ChatMessageDTO testMessage;
    private UserStatusDTO testUserStatus;
    private NotificationDTO testNotification;

    @BeforeEach
    void setUp() {
        // Setup test data
        testMessage = new ChatMessageDTO();
        testMessage.setId(1L);
        testMessage.setContent("Test message");
        testMessage.setSenderUsername("testuser");
        testMessage.setReceiverUsername("receiver");
        testMessage.setSentAt(LocalDateTime.now());

        testUserStatus = new UserStatusDTO();
        testUserStatus.setUserId(1L);
        testUserStatus.setUsername("testuser");
        testUserStatus.setDisplayName("Test User");
        testUserStatus.setStatus(UserStatusDTO.Status.ONLINE);

        testNotification = new NotificationDTO();
        testNotification.setId(1L);
        testNotification.setTitle("Test Notification");
        testNotification.setMessage("Test message");
        testNotification.setReceiverUsername("testuser");
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetConversation() throws Exception {
        List<ChatMessageDTO> messages = Arrays.asList(testMessage);
        when(chatService.getConversation(anyString(), anyString())).thenReturn(messages);

        mockMvc.perform(get("/api/chat/conversations/receiver")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].content", is("Test message")));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetRecentConversations() throws Exception {
        List<ChatMessageDTO> messages = Arrays.asList(testMessage);
        when(chatService.getRecentConversations(anyString(), anyInt(), anyInt())).thenReturn(messages);

        mockMvc.perform(get("/api/chat/recent")
                .param("page", "0")
                .param("size", "20")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetUnreadCount() throws Exception {
        when(chatService.getUnreadMessageCount(anyString())).thenReturn(5L);

        mockMvc.perform(get("/api/chat/unread-count")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", is(5)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testMarkAsRead() throws Exception {
        mockMvc.perform(post("/api/chat/mark-read")
                .param("senderUsername", "sender")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetChatHistory() throws Exception {
        List<ChatMessageDTO> messages = Arrays.asList(testMessage);
        when(chatService.getChatHistoryByRoomId(anyString(), anyString())).thenReturn(messages);

        mockMvc.perform(get("/api/chat/messages/private_1_2")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testSendMessage() throws Exception {
        when(chatService.saveMessage(any(ChatMessageDTO.class), anyString())).thenReturn(testMessage);

        String messageJson = objectMapper.writeValueAsString(testMessage);

        mockMvc.perform(post("/api/chat/send")
                .contentType(MediaType.APPLICATION_JSON)
                .content(messageJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", is("Test message")));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetOnlineUsers() throws Exception {
        List<UserStatusDTO> users = Arrays.asList(testUserStatus);
        when(userSessionService.getOnlineUsers()).thenReturn(users);

        mockMvc.perform(get("/api/chat/users/online")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].username", is("testuser")));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testUpdateUserStatus() throws Exception {
        when(userSessionService.updateUserStatus(anyString(), anyString())).thenReturn(testUserStatus);

        String statusJson = objectMapper.writeValueAsString(testUserStatus);

        mockMvc.perform(put("/api/chat/users/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(statusJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("ONLINE")));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testCreateChatRoom() throws Exception {
        when(chatService.createChatRoom(anyList(), anyString())).thenReturn("private_1_2");

        mockMvc.perform(post("/api/chat/rooms")
                .contentType(MediaType.APPLICATION_JSON)
                .content("[1, 2]"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetChatRooms() throws Exception {
        when(chatService.getUserChatRooms(anyString())).thenReturn(Collections.emptyList());

        mockMvc.perform(get("/api/chat/rooms")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetNotifications() throws Exception {
        List<NotificationDTO> notifications = Arrays.asList(testNotification);
        when(notificationService.getUserNotifications(anyString())).thenReturn(notifications);

        mockMvc.perform(get("/api/notifications")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].title", is("Test Notification")));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testMarkNotificationAsRead() throws Exception {
        mockMvc.perform(put("/api/notifications/1/read")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testMarkAllNotificationsAsRead() throws Exception {
        mockMvc.perform(put("/api/notifications/read-all")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testDeleteNotification() throws Exception {
        mockMvc.perform(delete("/api/notifications/1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testErrorHandling() throws Exception {
        when(chatService.getConversation(anyString(), anyString())).thenThrow(new RuntimeException("Test error"));

        mockMvc.perform(get("/api/chat/conversations/invalid")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testUnauthorizedAccess() throws Exception {
        mockMvc.perform(get("/api/chat/conversations/test")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser")
    void testFileUpload() throws Exception {
        mockMvc.perform(multipart("/api/chat/upload")
                .file("file", "test content".getBytes())
                .param("roomId", "private_1_2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success", is(true)));
    }

    @Test
    @WithMockUser(username = "testuser")
    void testFileUploadSizeLimit() throws Exception {
        byte[] largeFile = new byte[11 * 1024 * 1024]; // 11MB file

        mockMvc.perform(multipart("/api/chat/upload")
                .file("file", largeFile)
                .param("roomId", "private_1_2"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success", is(false)))
                .andExpect(jsonPath("$.message", containsString("10MB")));
    }
}
