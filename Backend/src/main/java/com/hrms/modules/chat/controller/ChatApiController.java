package com.hrms.modules.chat.controller;

import com.hrms.config.DynamicCrossOrigin;
import com.hrms.modules.chat.dto.NotificationDTO;
import com.hrms.modules.chat.service.RealTimeNotificationService;
import com.hrms.modules.hris.entity.HrCrEmp;
import com.hrms.modules.hris.repository.HrCrEmpRepository;
import com.hrms.dto.response.MessageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;
import java.util.List;
import java.util.Optional;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api")
@DynamicCrossOrigin
@Slf4j
public class ChatApiController {

    @Autowired
    private RealTimeNotificationService notificationService;
    
    @Autowired
    private HrCrEmpRepository employeeRepository;

    // User endpoints
    @GetMapping("/users/{userId}")
    public ResponseEntity<Object> getUserInfo(@PathVariable Long userId) {
        try {
            Optional<HrCrEmp> employee = employeeRepository.findById(userId);
            if (employee.isPresent()) {
                HrCrEmp emp = employee.get();
                // Create a simplified user object
                Object userInfo = new Object() {
                    public final Long id = emp.getId();
                    public final String username = emp.getUsername();
                    public final String displayName = emp.getFirstName() + " " + emp.getLastName();
                    public final String email = emp.getEmail();
                    public final String profileImage = emp.getProfileImage();
                };
                return ResponseEntity.ok(userInfo);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("Error getting user info: ", e);
            return ResponseEntity.badRequest().build();
        }
    }

    // Notification endpoints
    @GetMapping("/notifications")
    public ResponseEntity<List<NotificationDTO>> getNotifications(Principal principal) {
        try {
            List<NotificationDTO> notifications = notificationService.getUserNotifications(principal.getName());
            return ResponseEntity.ok(notifications);
        } catch (Exception e) {
            log.error("Error getting notifications: ", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/notifications/{id}/read")
    public ResponseEntity<MessageResponse> markNotificationAsRead(@PathVariable Long id, Principal principal) {
        try {
            notificationService.markAsRead(id, principal.getName());
            return ResponseEntity.ok(new MessageResponse("Notification marked as read", true));
        } catch (Exception e) {
            log.error("Error marking notification as read: ", e);
            return ResponseEntity.badRequest().body(new MessageResponse("Failed to mark notification as read", false));
        }
    }

    @PutMapping("/notifications/read-all")
    public ResponseEntity<MessageResponse> markAllNotificationsAsRead(Principal principal) {
        try {
            notificationService.markAllAsRead(principal.getName());
            return ResponseEntity.ok(new MessageResponse("All notifications marked as read", true));
        } catch (Exception e) {
            log.error("Error marking all notifications as read: ", e);
            return ResponseEntity.badRequest().body(new MessageResponse("Failed to mark all notifications as read", false));
        }
    }

    @DeleteMapping("/notifications/{id}")
    public ResponseEntity<MessageResponse> deleteNotification(@PathVariable Long id, Principal principal) {
        try {
            notificationService.deleteNotification(id, principal.getName());
            return ResponseEntity.ok(new MessageResponse("Notification deleted", true));
        } catch (Exception e) {
            log.error("Error deleting notification: ", e);
            return ResponseEntity.badRequest().body(new MessageResponse("Failed to delete notification", false));
        }
    }

    // File upload endpoint for chat
    @PostMapping("/chat/upload")
    public ResponseEntity<MessageResponse> uploadChatFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("roomId") String roomId,
            Principal principal) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("Please select a file to upload", false));
            }

            // Check file size (10MB limit)
            if (file.getSize() > 10 * 1024 * 1024) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("File size must be less than 10MB", false));
            }

            // Here you would implement file storage logic
            // For now, return success
            return ResponseEntity.ok(new MessageResponse("File uploaded successfully", true));
            
        } catch (Exception e) {
            log.error("Error uploading file: ", e);
            return ResponseEntity.badRequest()
                .body(new MessageResponse("File upload failed: " + e.getMessage(), false));
        }
    }
}
